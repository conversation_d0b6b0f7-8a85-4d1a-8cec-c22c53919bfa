<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.svg"/>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="ClinicalPad" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

    <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" /> -->
    <script type="text/javascript">
			(function(c,l,a,r,i,t,y){
				c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
				t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
				y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
			})(window, document, "clarity", "script", '%REACT_APP_CLARITY_KEY%');
		</script>
    <script src="https://script.supademo.com/script.js"></script>
    <script src="%PUBLIC_URL%/html2canvas.js" ></script>
    <title>ClinicalPad</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <div id="rodot" data-chatbotId="7b6ae8ef-d922-4c1c-8d33-ab773de0ba63" style="width: 0px; height: 0px; overflow: hidden"></div>
    <script src="https://widget.chatbuddypro.com/embed.js"></script>
    <link rel="stylesheet" href="https://widget.chatbuddypro.com/embed.css"></link>
  </body>
</html>
