stages:
  - build

build_token:
  only:
    - master
  stage: build
  image: node:16-alpine
  environment:
    name: develop
  before_script:
    - node -v
    - npm i -g firebase-tools
  script:
    - npm install
    - mkdir build
    - npm run build
    - firebase use tony-123456
    - firebase deploy --only hosting:dev --token $FIREBASE_TOKEN


build_serivce_account:
  only:
    - uat
  stage: build
  image: node:16-alpine
  environment:
    name: uat
  before_script:
    - node -v
    - npm i -g firebase-tools
    - cat $ENV_FILE > .env
    - export GOOGLE_APPLICATION_CREDENTIALS=$SERVICE_ACCOUNT_TONY
  script:
    - npm install --legacy-peer-deps
    - rm -rf build
    - mkdir build
    - npm run build:uat
    - firebase use tony-123456
    - firebase deploy --only hosting:uat
