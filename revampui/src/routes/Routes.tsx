import React, { Fragment, lazy, Suspense } from 'react';
import { Redirect, Route, Switch } from 'react-router-dom';

// configs
import { PATH_NAME, PERMISSIONS, USER_ROLE } from 'configs';

// types
import { IRoutes } from 'models/IRoutes';

// layouts
import MainLayout from 'layouts/MainLayout';

// containers
import AuthGuard from 'guards/AuthGuard';
import GuestGuard from 'guards/GuestGuard';

import {
  SignUp,
  ClinicInformation,
  IndividualAccountOwner,
  HospitalAccountOwner,
  CreatePassword,
  ActivateAccount,
} from 'features/Signup';
import { EmailSent, ForgotPassword, ResetPassword } from 'features/Login';

// route
import RoleRoute from './RoleRoute';
import Account from '../features/Account';
import HelpAndResources from '../features/HelpAndResources';

// modules

const Error404View = lazy(() => import('features/Error404View'));
const DenyView = lazy(() => import('features/DenyView'));
const Dashboard = lazy(() => import('features/Dashboard'));
const PatientListing = lazy(() => import('features/PatientListing'));
const PatientDetails = lazy(() => import('features/PatientDetails'));
const CreatePatient = lazy(() => import('features/CreatePatient'));
const UserProfile = lazy(() => import('features/UserProfile'));
const Contacts = lazy(() => import('features/Contacts'));
const Permissions = lazy(() => import('features/Permissions'));
const Tasks = lazy(() => import('features/Tasks'));
const ClinicalLetters = lazy(() => import('features/ClinicalLetters'));
const TaskPage = lazy(() => import('features/Tasks/_id'));
const Login = lazy(() => import('features/Login'));
const TemplateList = lazy(() => import('features/Templates/TemplateList'));
const TemplateAdd = lazy(() => import('features/Templates/TemplateAdd'));
const InviteUser = lazy(() => import('features/InviteUser'));
const ClinicalLetter = lazy(() => import('features/ClinicalLetter'));
const ClinicalDocumentsListing = lazy(() => import('features/ClinicalDocumentsListing'));
const PromptList = lazy(() => import('features/Prompts/PromptList'));
const PromptAdd = lazy(() => import('features/Prompts/PromptAdd'));

const routesConfig: IRoutes[] = [
  {
    exact: true,
    path: '/',
    component: () => <Redirect to={PATH_NAME.DASHBOARD} />,
  },
  {
    exact: true,
    path: PATH_NAME.ERROR_404,
    component: Error404View,
  },
  {
    exact: true,
    guard: GuestGuard,
    path: PATH_NAME.LOGIN,
    component: Login,
  },
  {
    exact: true,
    guard: GuestGuard,
    path: PATH_NAME.SIGNUP,
    component: SignUp,
  },
  {
    exact: true,
    guard: GuestGuard,
    path: PATH_NAME.CLINIC_INFORMATION,
    component: ClinicInformation,
  },
  {
    exact: true,
    guard: GuestGuard,
    path: PATH_NAME.INDIVIDUAL_INFORMATION,
    component: IndividualAccountOwner,
  },
  {
    exact: true,
    guard: GuestGuard,
    path: PATH_NAME.HOSPITAL_ACCOUNT_OWNER,
    component: HospitalAccountOwner,
  },
  {
    exact: true,
    guard: GuestGuard,
    path: PATH_NAME.CREATE_PASSWORD,
    component: CreatePassword,
  },
  {
    exact: true,
    guard: GuestGuard,
    path: PATH_NAME.FORGOT_PASSWORD,
    component: ForgotPassword,
  },
  {
    exact: true,
    guard: GuestGuard,
    path: PATH_NAME.RESET_PASSWORD,
    component: ResetPassword,
  },
  {
    exact: true,
    guard: GuestGuard,
    path: PATH_NAME.EMAIL_SENT,
    component: EmailSent,
  },
  {
    exact: true,
    path: PATH_NAME.ERROR_403,
    component: DenyView,
  },
  {
    exact: true,
    path: PATH_NAME.VERIFY_EMAIL,
    component: ActivateAccount,
  },
  {
    exact: true,
    path: PATH_NAME.INVITE_USER,
    component: InviteUser,
  },
  {
    path: '/',
    guard: AuthGuard,
    layout: MainLayout,
    routes: [
      {
        exact: true,
        path: PATH_NAME.DASHBOARD,
        component: Dashboard,
      },
      {
        exact: true,
        path: PATH_NAME.HELPANDRESOURCES,
        component: HelpAndResources,
      },
      {
        exact: true,
        path: PATH_NAME.PATIENT_LISTING,
        component: PatientListing,
        requirePermissions: [PERMISSIONS.PATIENT_VIEW, PERMISSIONS.PATIENT_EDIT],
      },
      {
        exact: true,
        path: PATH_NAME.CONTACTS,
        component: Contacts,
        // requireRoles: [USER_ROLE.ADMIN, USER_ROLE.LEAD],
      },
      {
        exact: true,
        path: PATH_NAME.PATIENT_DETAILS,
        component: PatientDetails,
        // requireRoles: [USER_ROLE.ADMIN, USER_ROLE.LEAD],
      },
      {
        exact: true,
        path: PATH_NAME.USER_PROFILE,
        component: UserProfile,
        // requireRoles: [USER_ROLE.ADMIN, USER_ROLE.LEAD],
      },
      {
        exact: true,
        path: PATH_NAME.CREATE_PATIENT,
        component: CreatePatient,
        requirePermissions: [PERMISSIONS.PATIENT_ADD],
        // requireRoles: [USER_ROLE.ACCOUNT_OWNER, USER_ROLE.CLINICIAN, USER_ROLE.STAFF_MEMBER],
      },
      {
        exact: true,
        path: PATH_NAME.PERMISSION,
        component: Permissions,
        requireRoles: [USER_ROLE.ACCOUNT_OWNER],
      },
      {
        exact: true,
        path: PATH_NAME.TASKS,
        component: Tasks,
        // requireRoles: [USER_ROLE.ACCOUNT_OWNER, USER_ROLE.CLINICIAN, USER_ROLE.STAFF_MEMBER],
        requirePermissions: [PERMISSIONS.TASK_VIEW, PERMISSIONS.TASK_EDIT],
      },
      {
        exact: true,
        path: PATH_NAME.LETTERS,
        component: ClinicalLetters,
        // requireRoles: [USER_ROLE.ADMIN, USER_ROLE.LEAD],
      },
      {
        exact: true,
        path: PATH_NAME.LETTERS_TASK_VIEW,
        component: ClinicalLetters,
        // requireRoles: [USER_ROLE.ADMIN, USER_ROLE.LEAD],
      },
      {
        exact: true,
        path: PATH_NAME.TASKS_VIEW,
        component: TaskPage,
        // requireRoles: [USER_ROLE.ADMIN, USER_ROLE.LEAD],
      },
      {
        exact: true,
        path: PATH_NAME.DESIGN_LIST,
        component: TemplateList,
        // requireRoles: [USER_ROLE.ACCOUNT_OWNER, USER_ROLE.CLINICIAN, USER_ROLE.STAFF_MEMBER],
        requirePermissions: [PERMISSIONS.DESIGN_VIEW, PERMISSIONS.DESIGN_EDIT],
      },
      {
        exact: true,
        path: PATH_NAME.DESIGN_ADD,
        component: TemplateAdd,
        // requireRoles: [USER_ROLE.ACCOUNT_OWNER, USER_ROLE.CLINICIAN, USER_ROLE.STAFF_MEMBER],
        requirePermissions: [PERMISSIONS.DESIGN_VIEW, PERMISSIONS.DESIGN_EDIT, PERMISSIONS.DESIGN_ADD],
      },
      {
        exact: true,
        path: PATH_NAME.CLINICAL_LETTER,
        component: ClinicalLetter,
        // requireRoles: [USER_ROLE.ACCOUNT_OWNER, USER_ROLE.CLINICIAN, USER_ROLE.STAFF_MEMBER],
        requirePermissions: [PERMISSIONS.CLINICAL_NOTE_ADD, PERMISSIONS.CLINICAL_NOTE_VIEW, PERMISSIONS.CLINICAL_NOTE_EDIT],
      },
      {
        exact: true,
        path: PATH_NAME.VIEW_CLINICAL_LETTER,
        component: ClinicalLetter,
        // requireRoles: [USER_ROLE.ACCOUNT_OWNER, USER_ROLE.CLINICIAN, USER_ROLE.STAFF_MEMBER],
        requirePermissions: [PERMISSIONS.CLINICAL_NOTE_VIEW, PERMISSIONS.CLINICAL_NOTE_EDIT],
      },
      {
        exact: true,
        path: PATH_NAME.VIEW_CLINICAL_LETTER_TASK,
        component: ClinicalLetter,
        requirePermissions: [PERMISSIONS.CLINICAL_NOTE_VIEW, PERMISSIONS.CLINICAL_NOTE_EDIT],
        // requireRoles: [USER_ROLE.ACCOUNT_OWNER, USER_ROLE.CLINICIAN, USER_ROLE.STAFF_MEMBER],
      },
      {
        exact: true,
        path: PATH_NAME.CLINICAL_DOCUMENT,
        component: ClinicalDocumentsListing,
        requirePermissions: [PERMISSIONS.CLINICAL_NOTE_VIEW, PERMISSIONS.CLINICAL_NOTE_EDIT],
        // requireRoles: [USER_ROLE.ACCOUNT_OWNER, USER_ROLE.CLINICIAN, USER_ROLE.STAFF_MEMBER],
      },
      {
        exact: true,
        path: PATH_NAME.TEMPLATE_LIST,
        component: PromptList,
        // requireRoles: [USER_ROLE.ACCOUNT_OWNER, USER_ROLE.CLINICIAN, USER_ROLE.STAFF_MEMBER],
        requirePermissions: [PERMISSIONS.TEMPLATE_VIEW, PERMISSIONS.TEMPLATE_EDIT, PERMISSIONS.TEMPLATE_ADD],
      },
      {
        exact: true,
        path: PATH_NAME.TEMPLATE_ADD,
        component: PromptAdd,
        // requireRoles: [USER_ROLE.ACCOUNT_OWNER, USER_ROLE.CLINICIAN, USER_ROLE.STAFF_MEMBER],
        requirePermissions: [PERMISSIONS.TEMPLATE_EDIT, PERMISSIONS.TEMPLATE_ADD],
      },
      {
        exact: true,
        path: PATH_NAME.ACCOUNT,
        component: Account,
      },
      {
        component: () => <Redirect to={PATH_NAME.ERROR_404} />,
      },
    ],
  },
  {
    path: '*',
    routes: [
      {
        exact: true,
        path: '/app',
        component: MainLayout,
      },
      {
        component: () => <Redirect to={PATH_NAME.ERROR_404} />,
      },
    ],
  },
];

const renderRoutes = (routes: IRoutes[]) => {
  return (
    <>
      {routes ? (
        // @ts-ignore
        <Suspense fallback={<div />}>
          <Switch>
            {routes.map((route: IRoutes, idx: number) => {
              const Guard = route.guard || Fragment;
              const Layout = route.layout || Fragment;
              const Component = route.component;
              const requireRoles = route.requireRoles || [];
              const requirePermissions = route.requirePermissions || [];

              return (
                <Route
                  key={`routes-${idx}`}
                  path={route.path}
                  exact={route.exact}
                  render={(props: any) => (
                    // @ts-ignore
                    <Guard>
                      {/* @ts-ignore */}
                      <Layout>
                        {route.routes ? (
                          renderRoutes(route.routes)
                        ) : (
                          <RoleRoute requireRoles={requireRoles} requirePermissions={requirePermissions}>
                            <Component {...props} />
                          </RoleRoute>
                        )}
                      </Layout>
                    </Guard>
                  )}
                />
              );
            })}
          </Switch>
        </Suspense>
      ) : null}
    </>
  );
};

function Routes() {
  return renderRoutes(routesConfig);
}

export default Routes;
