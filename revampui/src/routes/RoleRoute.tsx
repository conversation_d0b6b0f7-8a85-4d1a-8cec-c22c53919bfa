import React, { FC, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { useSelector } from 'react-redux';

// configs
import { PATH_NAME } from 'configs';

// selectors
import { roleSelector } from 'selectors/auth.selector';
import { canAction } from '../helpers';

type IProps = {
  requireRoles: string[] | [];
  requirePermissions: string[] | [];
};

const RoleRoute: FC<IProps> = ({ children, requireRoles = [], requirePermissions = [] }) => {
  const history = useHistory();
  const role = useSelector(roleSelector);

  useEffect(() => {
    if (!role) return;

    // const checkRole = requireRoles.includes(role);
    // if (!checkRole) {
    //   history.replace(PATH_NAME.ERROR_403);
    // }
    console.log({ requirePermissions });
    if (requirePermissions.length > 0) {
      const hasPermission = requirePermissions.some((permission) => {
        return canAction('do', permission);
      });
      if (!hasPermission) {
        history.replace(PATH_NAME.ERROR_403);
      }
    }
  }, [history, role, requireRoles]);

  return <>{children}</>;
};

export default RoleRoute;
