// @ts-nocheck
import React, { useState, useCallback, FC, useEffect, useMemo } from 'react';

// libs
import clsx from 'clsx';

// material core
import CssBaseline from '@material-ui/core/CssBaseline';

// containers
import ErrorBoundary from 'containers/ErrorBoundary';

// components
import { useHistory } from 'react-router-dom';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import _ from 'lodash';
import { useLocation } from 'react-router';
import NavBar from './NavBar';
import TopBar from './TopBar';

// styles
import useStyles from './styles';
import { permissionsSelector, userSelector } from '../../selectors/auth.selector';
import { getUser } from '../../services/users';
import { IAuthActionTypes } from '../../models/IAuthState';
import { IAppActionTypes } from '../../models/IAppState';

const MainLayout: FC = ({ children }) => {
  const dispatch = useDispatch();
  const classes = useStyles();
  const [isDrawer, setIsDrawer] = useState(true);

  // TODO: Find a better way to adjust the styles
  const history = useHistory();
  const { pathname } = history.location;
  const isTemplateAdd = pathname.startsWith('/templates/add');
  const loggedInUser = useSelector(userSelector, shallowEqual);
  const permissions = useSelector(permissionsSelector);
  const hasPermissions = (loggedInUser && loggedInUser?.role_name === 'Account Owner') || !_.isEmpty(permissions);

  const location = useLocation();
  const basePath = useMemo(() => {
    return location.pathname.split('/')[1];
  }, [location.pathname]);

  const _handleToggleDrawer = useCallback(() => {
    setIsDrawer(!isDrawer);
  }, [isDrawer]);

  useEffect(() => {
    if (loggedInUser && loggedInUser?.role_name !== 'Account Owner') {
      dispatch({ type: IAppActionTypes.SET_LOADING, payload: true });
      getUser(loggedInUser.id)
        .then((user) => {
          dispatch({ type: IAppActionTypes.SET_LOADING, payload: false });
          dispatch({ type: IAuthActionTypes.SET_PERMISSIONS, payload: user.permissions });
        })
        .catch((e) => {
          // TODO: handle error
          console.log(e);
          dispatch({ type: IAppActionTypes.SET_LOADING, payload: false });
        });
    }
  }, [loggedInUser, basePath]);

  return (
    <div className={classes.root}>
      {/* <CssBaseline /> */}

      <TopBar isDrawer={isDrawer} handleToogleDrawer={_handleToggleDrawer} />

      <NavBar isDrawer={isDrawer} handleToogleDrawer={_handleToggleDrawer} />

      {/* TODO: add loader here to till we don't get the permissions */}
      {hasPermissions && (
        <main
          className={clsx(classes.content, {
            [classes.contentShift]: isDrawer,
            [classes.templateAdd]: isTemplateAdd,
          })}
        >
          <div className={classes.toolbar} />
          <ErrorBoundary>{children}</ErrorBoundary>
        </main>
      )}
    </div>
  );
};

export default MainLayout;
