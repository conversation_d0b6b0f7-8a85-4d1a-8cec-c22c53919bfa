// @ts-nocheck
import React, { ReactNode } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Box, Typography, Grid, useTheme, useMediaQuery, Stack } from '@mui/material';
import { FOOTER_LINKS } from 'configs/externalLinks';
import { PATH_NAME } from '../../configs';

const OnBoardingLayout = ({ content }: { content: ReactNode }) => {
  const theme = useTheme();
  const location = useLocation();
  const { pathname } = location;
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Grid container sx={{ minHeight: '100vh', backgroundColor: '#FFFFFF' }}>
      {/* LEFT COLUMN - Hidden on mobile */}
      {!isMobile && (
        <Grid
          item
          md={6}
          lg={7}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            padding: '100px',
            paddingBottom: '0',
            backgroundColor: '#FFFFFF',
          }}
        >
          <Typography
            variant="h1"
            sx={{
              fontSize: '40px',
              lineHeight: '1.4',
              fontWeight: 700,
              mb: 4,
            }}
          >
            <Box component="span" sx={{ color: '#FF5A5F' }}>
              AI-Powered
            </Box>{' '}
            <Box component="span" sx={{ color: '#2D2869' }}>
              Clinical
            </Box>
            <br />
            <Box component="span" sx={{ color: '#2D2869' }}>
              Documentation That
            </Box>
            <br />
            <Box component="span" sx={{ color: '#2D2869' }}>
              Saves You Time
            </Box>
          </Typography>

          <Box
            component="img"
            src="/assets/images/doctor.png" // ✅ starts from public/
            alt="Doctor"
            sx={{
              width: '400px',
              height: '400px',
              objectFit: 'contain',
              borderRadius: '8px',
            }}
          />
        </Grid>
      )}

      {/* RIGHT COLUMN - Logo + Content */}
      <Grid
        item
        xs={12}
        sm={12}
        md={6}
        lg={5}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          px: 4,
          py: 6,
        }}
      >
        <Box width="100%" maxWidth="400px">
          {content}
        </Box>
      </Grid>
    </Grid>
  );
};

export default OnBoardingLayout;
