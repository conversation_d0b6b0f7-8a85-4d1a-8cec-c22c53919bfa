import React from 'react';
import { Breadcrumbs, Typography } from '@mui/material';
import { useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';

const BREADCRUMBS_MAP: Record<string, string> = {
  tasks: 'Tasks / Tasks Listing',
  contacts: 'Contacts / Contacts Listing',
  permission: 'Permissions / Role Listing',
  dashboard: 'Dashboard',
  letters: 'Clinical Letters',
  designs: 'Designs / Designs Listing',
  createpatient: 'Patient / Create Patient',
  patient: 'Patients / Patients Listing',
  patientdetails: 'Patient / Patient Details',
  'clinical-letter': 'Patient / Patient Detail / Create Document',
  'clinical-document': 'Document History / Document Listing',
  templates: 'Templates / Templates Listing',
  createtemplate: 'Templates / Create Template',
  account: 'Account',
};

const BreadcrumbComponent: React.FC = () => {
  const location = useLocation();
  const app = useSelector((state: any) => state.app);
  const path = location.pathname.split('/')[1] as keyof typeof BREADCRUMBS_MAP; // Explicitly define type

  let pathNames = BREADCRUMBS_MAP[path] ? BREADCRUMBS_MAP[path].split('/') : [path];

  if (app?.navigation?.breadcrumb) {
    pathNames = app.navigation.breadcrumb.split('/');
  }

  return (
    <Breadcrumbs>
      {pathNames.map((name, index) => {
        const isLast = index === pathNames.length - 1;
        return isLast ? (
          <Typography key={name} color="text.primary" variant="body2">
            {BREADCRUMBS_MAP[name as keyof typeof BREADCRUMBS_MAP] || name} {/* Ensure correct typing */}
          </Typography>
        ) : (
          <Typography key={name} variant="body2" style={{ fontSize: '16px' }}>
            {name}
          </Typography>
        );
      })}
    </Breadcrumbs>
  );
};

export default BreadcrumbComponent;
