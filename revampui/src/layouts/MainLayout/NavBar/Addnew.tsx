import React, { useState } from 'react';
import { But<PERSON>, <PERSON>u, MenuItem, Paper } from '@material-ui/core';
import { useHistory } from 'react-router-dom';
import { PERMISSIONS, PATH_NAME } from 'configs';
import { makeStyles } from '@material-ui/core/styles';
import PatientIcon from 'themes/icons/PatientIcon';
import ClinicalLetterIcon from 'themes/icons/ClinicalLetterIcon';
import { canAction } from '../../../helpers';

const useStyles = makeStyles((theme) => ({
  addNewButton: {
    display: 'flex',
    color: theme.palette.type === 'dark' ? 'rgb(158, 158, 158)' : '#2D2869 !important',
    justifyContent: 'center',
    alignItems: 'center',
    gap: '6px',
    flexShrink: 0,
    lineHeight:'22px',
    alignSelf: 'stretch',
    width: '100%',
    height:'45px',
    backgroundColor: theme.palette.type === 'dark'
      ? 'rgba(51, 94, 247, 0.08) !important'
      : 'rgba(51, 94, 247, 0.08) !important',
    textTransform: 'none',
    padding: '18px 16px',
    borderRadius: '12px',
    fontSize: '14px',

    '&:hover': {
      boxShadow: '0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12)',

      border: theme.custom.GradientButton['&:hover'].border,
    },
  },
  iconWrapper: {
    width: '29px',
    height: '29px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  wrapper: {
    padding: '0 20px',
  },
}));

interface CreateNewButtonProps {
  isDrawer: boolean;
}

const CreateNewButton: React.FC<CreateNewButtonProps> = ({ isDrawer }) => {
  const classes = useStyles();
  const history = useHistory();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleAddNewClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuItemClick = (path: string) => {
    history.push(path);
    setAnchorEl(null);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div className={`flex align-items-center justify-content-center w-full ${classes.wrapper}`}>
      {/* if isDrawer then apply custom styles to button */}
      <Button
        style={{
          fontWeight: '700',
          ...(!isDrawer && {
            minWidth: '55px',
            marginLeft: '10px',
          }),
        }}
        onClick={handleAddNewClick}
        className={classes.addNewButton}
      >
        {isDrawer ? 'Create +' : '+'}
      </Button>

      <Menu
        style={{ position: 'absolute', top: 70 }}
        PaperProps={{
          component: Paper,
          style: {
            borderRadius: '12px',
            padding: '10px 20px',
          },
        }}
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleClose}
        className="create-new-menu"
      >
        {canAction('do', PERMISSIONS.PATIENT_ADD) && (
          <MenuItem
            style={{ borderRadius:'12px', marginBottom: '5px', fontSize: '16px', fontWeight: '600px', display: 'flex', gap: '6px',

            }}
            onClick={() => handleMenuItemClick(PATH_NAME.CREATE_PATIENT)}
          >
            Patient Profile
            <span className={classes.iconWrapper}>
              <PatientIcon />
            </span>
          </MenuItem>
        )}
        {canAction('do', PERMISSIONS.CLINICAL_NOTE_ADD) && (
          <MenuItem
            style={{  borderRadius:'12px', marginBottom: '0px', fontSize: '16px', fontWeight: '600px', display: 'flex', gap: '6px' }}
            onClick={() => handleMenuItemClick(PATH_NAME.CLINICAL_LETTER)}
          >
            Document
            <span className={classes.iconWrapper}>
              <ClinicalLetterIcon />
            </span>
          </MenuItem>
        )}
      </Menu>
    </div>
  );
};

export default CreateNewButton;
