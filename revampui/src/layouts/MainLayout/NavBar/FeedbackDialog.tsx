import React, { useState } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Typography,
  IconButton,
} from '@material-ui/core';
import { <PERSON><PERSON>, <PERSON>ack, TextField } from '@mui/material';
import CloseIcon from '@material-ui/icons/Close';
import Rating from '@mui/material/Rating'; // Use MUI's Rating
import { useTheme } from '@material-ui/core/styles';

interface FeedbackDialogProps {
  open: boolean;
  onClose: () => void; // Function to close the dialog
  onSubmit: (rating: number, feedback: string) => void; // Function to handle feedback submission
}

const FeedbackDialog: React.FC<FeedbackDialogProps> = ({ open, onClose, onSubmit }) => {
  const [rating, setRating] = useState(0);
  const [feedback, setFeedback] = useState('');
  const [error, setError] = useState({ rating: false, feedback: false });

  const theme = useTheme(); // Moved to top to avoid unnecessary calls during renders

  const handleSubmit = () => {
    const isRatingValid = rating > 0;
    const isFeedbackValid = feedback.length > 0;
    if (!isRatingValid || !isFeedbackValid) {
      setError({
        rating: !isRatingValid,
        feedback: !isFeedbackValid,
      });
      return;
    }

    // Call onSubmit with the rating and feedback
    onSubmit(rating, feedback);
    setRating(0);
    setFeedback('');
    setError({ rating: false, feedback: false });
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      style={{
        borderRadius: '16px',
        border: '1px solid #E6E6E6',
      }}
    >
      <div
        style={{
          backgroundColor: '#E6E6E6',
        }}
      >
        <DialogTitle>
          <Typography style={{ fontWeight: '600', fontSize: '16px' }} component="span">
            Send us some Feedback!
          </Typography>
          <IconButton style={{ position: 'absolute', top: '8px', right: '8px' }} onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" style={{ marginBottom: '16px' }}>
            Do you have a suggestion or a comment? Let us know.
          </Typography>
          <Stack flexDirection="row" alignItems="center" gap="15px">
            <Typography variant="body2" style={{ marginBottom: '8px' }}>
              Rate Us:
            </Typography>
            <Rating
              name="rating"
              value={rating}
              onChange={(event, newValue) => {
                const validValue = newValue ?? 0;
                setRating(validValue);
                if (validValue > 0) setError((prev) => ({ ...prev, rating: false }));
              }}
              style={{ marginBottom: '16px' }}
            />
          </Stack>
          {error.rating && (
            <Typography
              variant="caption"
              color="error"
              style={{ marginTop: '-12px', marginBottom: '12px', marginLeft: '10px', display: 'block', color: '#D32F2F' }}
            >
              Please provide a rating.
            </Typography>
          )}
          <TextField
            autoFocus
            hiddenLabel
            id="feedback"
            type="text"
            fullWidth
            variant="outlined"
            multiline
            rows={8}
            value={feedback}
            onChange={(e) => {
              const inputText = e.target.value;
              if (inputText.length <= 1000) {
                setFeedback(inputText);
                if (inputText.length > 0) {
                  setError((prev) => ({ ...prev, feedback: false }));
                }
                if (inputText.length === 1000) {
                  setFeedback(inputText);
                  setError((prev) => ({ ...prev, feedback: true }));
                }
              }
            }}
            error={error.feedback}
            helperText={(() => {
              if (error.feedback && feedback.length === 1000) {
                return 'Max word limit reached (1000)';
              }
              if (feedback.length === 0) {
                return 'Feedback is required.';
              }
              return '';
            })()}
            sx={{
              borderRadius: '15px !important',
            }}
          />
        </DialogContent>
        <DialogActions style={{ justifyContent: 'center', padding: '16px' }}>
          <Button
            onClick={handleSubmit}
            style={{
              padding: '6px 70px',
              borderRadius: '84px',
              fontWeight: '600',
              height: '45px',
            }}
          >
            Submit
          </Button>
        </DialogActions>
      </div>
    </Dialog>
  );
};

export default FeedbackDialog;
