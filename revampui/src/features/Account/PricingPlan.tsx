// @ts-nocheck
import { Box, Button, Typography, useTheme } from '@mui/material';
import DoneIcon from '@mui/icons-material/Done';
import React, { useState } from 'react';
import { getPlanDisplayName } from '../../configs/plan-mapper';

export type ActionType = 'subscribe' | 'upgrade' | 'downgrade' | 'cancel';

interface PlanCoupons {
  monthly?: string;  // coupon code for monthly billing
  yearly?: string;   // coupon code for yearly billing
}

interface PlanDetailsProps {
  details?: string;
}

interface PricingPlanProps {
  plan: {
    id: string;
    title: string;
    price: string;      // monthly price
    yearlyPrice?: string; // yearly price
    billing: string;
    details: string;
    buttonText: string;
    note?: string;
    stripePriceIds?: { 
      month: {
        stripe_price_id: string;
        price: string;
        original_price?: string;  // Add original price field
      };
      year: {
        stripe_price_id: string;
        price: string;
        original_price?: string;  // Add original price field
      };
    };
    coupons?: PlanCoupons;
  };
  isSubscribed: boolean;
  hasSubscription?: boolean; // indicates if the user already has a paid subscription
  onPlanAction: (
    plan: PricingPlanProps['plan'] & { stripePriceId?: string },
    actionType: ActionType
  ) => void;
  planType: string;
}

function planDetails(details: DetailsProps) {
  if (!details) return null;

  // Parse the HTML string into DOM nodes
  const parser = new DOMParser();
  const doc = parser.parseFromString(details, 'text/html');

  // Grab all <p> and <li> elements in document order
  const nodes = Array.from(doc.body.querySelectorAll('p, li'));

  return (
    <>
      {nodes.map((node, idx) => {
        const text = node.textContent?.trim() || '';
        // Treat <p> as header, all <li> as regular items
        const isHeader = node.tagName.toLowerCase() === 'p';

        return (
          <Typography
            key={idx}
            sx={{
              display: 'flex',
              alignItems: 'center',
              fontSize: '13px',
              color: isHeader
                ? theme => theme.palette.text.primary
                : '#747474',
              lineHeight: '24px',
              mb: 1,
            }}
          >
            {!isHeader && (
              <DoneIcon
                sx={{ color: '#7d7d7d', fontSize: '18px', mr: '8px' }}
              />
            )}
            {text}
          </Typography>
        );
      })}
    </>
  );
}

const PriceDisplay = ({ planType, stripePriceIds }) => {
  const priceInfo = stripePriceIds?.[planType];
  console.log({ priceInfo, stripePriceIds, planType });
  if (!priceInfo) return null;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', textAlign: 'center' }}>
      <Box sx={{ display: 'flex', flexDirection: 'row', gap: 1, textAlign: 'center' }}>
        {priceInfo.original_price && (
          <Typography
            variant="h4"
            sx={{
              textDecoration: 'line-through',
              color: 'text.secondary',
              // mt: 1,
            }}
          >
            {priceInfo.original_price}{/* /{priceInfo.billing_interval} */}
          </Typography>
        )}
        <Typography variant="h4" component="div" sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          {priceInfo.price}
          <Typography variant="subtitle1" component="span" sx={{ ml: 1 }}>
            /{priceInfo.billing_interval}
          </Typography>
        </Typography>
      </Box>

      <Typography variant="body2" sx={{ mt: 1 }}>
        {planType === 'year' ? 'For 1 year' : 'For 12 months'}
      </Typography>

      {priceInfo.savings && (
        <Box sx={{ mt: 2 }}>
          {/* <Typography */}
          {/*  variant="body1" */}
          {/*  color="success.main" */}
          {/*  sx={{ fontWeight: 'bold' }} */}
          {/* > */}
          {/*  Save {priceInfo.savings.percentage} */}
          {/* </Typography> */}
          <Typography variant="body2" color="success.main">
            Save {priceInfo.savings.amount} {priceInfo.savings.period}
          </Typography>
        </Box>
      )}

      {/* {planType === 'year' && priceInfo.total_yearly && (
        <Typography variant="body2" sx={{ mt: 1 }}>
          Total {priceInfo.total_yearly}/year
          <br />
          <span style={{ textDecoration: 'line-through' }}>
            {priceInfo.original_yearly}
          </span>
        </Typography>
      )} */}

      <Typography
        variant="body2"
        color="text.secondary"
        sx={{ display: 'block', mt: 1 }}
      >
        {priceInfo.regular_price_notice}
      </Typography>
      <Typography
        variant="caption"
        color="text.secondary"
        sx={{ display: 'block', mt: 1 }}
      >
        {priceInfo.subtitle_regular_price_notice}
      </Typography>
    </Box>
  );
};


const PricingPlan: React.FC<PricingPlanProps> = ({ plan, subscribedPlanType, isSubscribed, onPlanAction,  hasSubscription = false, planType }) => {
  const theme = useTheme();
  const [hover, setHover] = useState(false);

  const isPro = plan.title.toLowerCase() === 'pro';


  // Determine button label based on subscription status:
  // - If the plan is the Starter plan (which is free) and subscribed, we show "Current Plan" (and disable the button).
  // - Otherwise, if subscribed, show "Cancel Subscription".
  // - If not subscribed, use the plan’s buttonText.
  // eslint-disable-next-line no-nested-ternary
  const buttonLabel = isSubscribed
    ? plan.title.toLowerCase() === 'starter'
      ? 'Current Plan'
      : 'Cancel Subscription'
    : plan.buttonText;

  // Disable the button for Starter plan if the user is subscribed, because it cannot be cancelled.
  const buttonDisabled = isSubscribed && plan.title.toLowerCase() === 'starter';

  // Determine the action type and extract monthly stripe price id if applicable.
  const handleClick = () => {
    // debugger;
    let actionType: ActionType;
    // If the user is not subscribed to this plan:
    if (!isSubscribed) {
      // If the user already has a paid subscription, then clicking means an upgrade or downgrade.
      // For the free Starter plan, this is a downgrade.
      if (hasSubscription) {
        actionType =
          plan.title.toLowerCase() === 'starter' ? 'downgrade' : 'upgrade';
      } else {
        // No current subscription so this is a fresh subscription.
        actionType = 'subscribe';
      }
    } else {
      // If already subscribed (and not Starter, since that button is disabled), then cancel.
      actionType = 'cancel';
    }

    // For paid plans, we always use the monthly price for now.
    // (For the free Starter plan, there is no stripe price id.)
    const stripePriceId =
      plan.stripePriceIds && plan.stripePriceIds[planType === 'yearly' ? 'year' : 'month']?.stripe_price_id
        ? plan.stripePriceIds[planType === 'yearly' ? 'year' : 'month']?.stripe_price_id
        : undefined;

    const db_id = plan.stripePriceIds && plan.stripePriceIds.month
      ? plan.db_id
      : undefined;

    // Pass the plan along with the stripePriceId and the determined action type.
    onPlanAction({ ...plan, stripePriceId, db_id }, actionType);
  };

  // Get both current and original prices
  const currentPrice = plan.stripePriceIds && plan.stripePriceIds[planType === 'yearly' ? 'year' : 'month']?.price
    ? plan.stripePriceIds[planType === 'yearly' ? 'year' : 'month']?.price
    : plan.price;

  const originalPrice = plan.stripePriceIds && plan.stripePriceIds[planType === 'yearly' ? 'year' : 'month']?.original_price;

  return (
    <Box
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
      sx={{
        flex: '1 1 260px',
        position: 'relative',
        borderRadius: '16px',
        padding: '24px',
        minWidth: '260px',
        background: theme.palette.mode === 'dark' ? '#1e1e1e' : '#fff',
        border: '1px solid #DCE5E5',
        transition: 'all 0.3s ease',
        '&::before': hover
          ? {
              content: '""',
              position: 'absolute',
              inset: 0,
              padding: '2px',
              borderRadius: '16px',
              background: 'linear-gradient(to right, #ff3d3d, #ff7373)',
              WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
              WebkitMaskComposite: 'xor',
              maskComposite: 'exclude',
              zIndex: 1,
            }
          : {},
      }}
    >
      {/* Most Popular Tag */}
      {isPro && (
        <Box
          sx={{
            position: 'absolute',
            top: '-10px',
            right: '16px',
            background: '#ff3d3d',
            color: '#fff',
            fontSize: '10px',
            fontWeight: 600,
            padding: '4px 8px',
            borderRadius: '999px',
            textTransform: 'uppercase',
            zIndex: 2,
          }}
        >
          Most Popular
        </Box>
      )}

      {/* Header */}
      <Box
        display="flex"
        flexDirection="column"
        gap="4px"
        borderBottom="1px solid #DCE5E5"
        pb={2}
        zIndex={2}
        position="relative"
        sx={{
          backgroundColor: theme.palette.mode === 'dark' ? '#1e1e1e' : '#f9f9f9',
          border: `3px solid ${hover ? '#ff3d3d' : '#DCE5E5'}`,
          borderRadius: '12px',
          padding: '12px 16px',
          transition: 'all 0.3s ease',
          marginBottom: '16px',
          height:'222px',
        }}
      >
        {/* eslint-disable-next-line no-nested-ternary */}
        <Typography sx={{ fontSize: '18px', fontWeight: '700', textTransform: 'uppercase',  color: hover ? '#ff3d3d' : theme.palette.mode === 'dark' ? '#fff' : '#000' }}>{getPlanDisplayName(plan.title)}</Typography>
        <Typography sx={{ color: '#747474', fontSize: '12px', lineHeight: '18px' }}>{plan.billing}</Typography>
        <Box display="flex" alignItems="flex-end" gap={1} justifyContent="center">
          {/* <Typography
            sx={{
              marginTop: '4px',
              fontSize: '32px',
              lineHeight: '40px',
              fontWeight: '700',
              // eslint-disable-next-line no-nested-ternary
              color: hover ? '#ff3d3d' : theme.palette.mode === 'dark' ? '#fff' : '#000',
            }}
          >
            {currentPrice}
          </Typography> */}
          <Typography
            sx={{
              marginBottom: '8px',
              fontSize: '14px',
              color: '#747474',
            }}
          >
            {/* {planType === 'yearly' ? '/year' : '/month'} */}
            {/* {plan.title === 'Pro' ? '/ Per User' : ''} */}
          </Typography>
          {/* {originalPrice && (
            <Typography
              sx={{
                marginLeft: '8px',
                fontSize: '20px',
                lineHeight: '40px',
                fontWeight: '500',
                textDecoration: 'line-through',
                color: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.5)' : 'rgba(0,0,0,0.5)',
              }}
            >
              {originalPrice}
            </Typography>
          )} */}
          <PriceDisplay
            planType={planType === 'yearly' ? 'year' : 'month'}
            stripePriceIds={plan.stripePriceIds}
          />
        </Box>
      </Box>

      {/* <Box> */}
      {/*  <PriceDisplay */}
      {/*    planType={planType === 'yearly' ? 'year' : 'month'} */}
      {/*    stripePriceIds={plan.stripePriceI  ds} */}
      {/*  /> */}
      {/* </Box> */}

      {/* Features */}
      <Box sx={{ py: 2, height: '420px', overflowY: 'auto', zIndex: 2, position: 'relative', paddingRight: '10px' }}>
        {/* {plan.details && ( */}
        {/*  <div dangerouslySetInnerHTML={{ __html: plan.details }} /> */}
        {/* )} */}
        {/* {plan.details.map((detail, i) => ( */}
        {/*  <Typography */}
        {/*    key={i} */}
        {/*    sx={{ */}
        {/*      display: 'flex', */}
        {/*      alignItems: 'center', */}
        {/*      fontSize: '13px', */}
        {/*      color: i === 0 ? (theme.palette.mode === 'dark' ? '#fff' : '#000') : '#747474', */}
        {/*      lineHeight: '24px',
              mb: 1, */}
        {/*    }} */}
        {/*  > */}
        {/*    {i > 0 && <DoneIcon sx={{ color: '#7d7d7d', fontSize: '18px', mr: '8px' }} />} */}
        {/*    {detail} */}
        {/*  </Typography> */}
        {/* ))} */}
        {plan?.details && planDetails(plan?.details)}
        {/*  {plan?.details?.split('</li>').map((detail, i) => { */}
        {/*  // Skip empty items and the last empty element after split */}
        {/*  if (!detail.trim() || i === plan.details.split('</li>').length - 1) return null; */}

        {/*  // Extract text content from HTML */}
        {/*  const text = detail.replace(/<[^>]+>/g, '').trim(); */}

        {/*  // Check if it's the header (contains ":") or a regular list item */}
        {/*  const isHeader = text.includes(':'); */}

        {/*  return ( */}
        {/*    <Typography */}
        {/*      key={i} */}
        {/*      sx={{ */}
        {/*        display: 'flex', */}
        {/*        alignItems: 'center', */}
        {/*        fontSize: '13px', */}
        {/*        // eslint-disable-next-line no-nested-ternary */}
        {/*        color: isHeader ? (theme.palette.mode === 'dark' ? '#fff' : '#000') : '#747474', */}
        {/*        lineHeight: '24px', */}
        {/*        mb: 1, */}
        {/*      }} */}
        {/*    > */}
        {/*      {!isHeader && <DoneIcon sx={{ color: '#7d7d7d', fontSize: '18px', mr: '8px' }} />} */}
        {/*      {text} */}
        {/*    </Typography> */}
        {/*  ); */}
        {/* })} */}
      </Box>

      {/* Button */}
      <Box display="flex" alignItems="center" flexDirection="column" gap="8px" pt={2} zIndex={2} position="relative">
        <Button
          onClick={handleClick}
          disabled={buttonDisabled}
          sx={{
            padding: '14px 28px',
            borderRadius: '999px',
            fontWeight: 600,
            fontSize: '14px',
            // eslint-disable-next-line no-nested-ternary
            background: hover
              ? 'linear-gradient(90deg, #ff3d3d 0%, #ff7373 100%)'
              : theme.palette.mode === 'dark'
              ? '#ffffff'
              : 'linear-gradient(90deg, #2f2367 0%, #4d3cac 100%)',
            // eslint-disable-next-line no-nested-ternary
            color: hover ? '#fff' : theme.palette.mode === 'dark' ? '#2f2367' : '#fff',
            textTransform: 'none',
            border: hover ? '1px solid #ff3d3d' : '1px solid transparent',
            boxShadow: hover ? '0px 4px 16px rgba(255, 61, 61, 0.4)' : 'none',
            transition: 'all 0.3s ease',
          }}
        >
          {buttonLabel}
        </Button>
        {/*
          Optionally, if the plan is paid and the user is switching plans (upgrade/downgrade),
          you might want to show an extra note indicating that any free trial period already in progress will continue.
        */}
        {plan.title.toLowerCase() !== 'starter' && hasSubscription && !isSubscribed && (
          <Typography sx={{ fontSize: '12px', color: '#747474', mt: 1 }}>
            Your current free trial will continue with this change.
          </Typography>
        )}
        {plan.note && (
          <Typography sx={{ fontSize: '12px', color: '#747474', textAlign: 'center' }}>
            <span style={{ color: 'red' }}>*</span> {plan.note}
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default PricingPlan;
