import { Avatar, Box, Stack, Typography, Grid, Chip, useTheme } from '@mui/material';
import React from 'react';

interface UserProfileProps {
  name: string;
  email: string;
  avatar: string;
  dateJoined: string;
  plan: string;
}

// @ts-ignore
const UserProfileCard: React.FC<UserProfileProps> = ({ name, email, avatar, dateJoined, plan }) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  return (
    <Box
      sx={{
        backgroundColor: isDarkMode ? '#222' : 'white',
        padding: '20px',
        borderRadius: '10px',
        boxShadow: isDarkMode ? '0px 4px 8px rgba(255, 255, 255, 0.1)' : '0px 4px 8px rgba(0, 0, 0, 0.1)',
        width: '100%',
        maxWidth: {
          xs: '100%',   // mobile
          sm: '400px',  // tablet and up
        }, // Adjust width as needed
      }}
    >
      {/* First Row - Avatar & Date Joined */}
      <Grid container spacing={2} alignItems="center">
        {/* Left Column (Avatar, Name, Email) */}
        <Grid item xs={7} display="flex" alignItems="center">
          <Avatar src={avatar} sx={{ width: '56px', height: '56px', marginRight: '10px' }} />
          <Box>
            <Typography sx={{ fontSize: '16px', fontWeight: 'bold' }}>{name}</Typography>
            <Typography sx={{ fontSize: '12px', color: '#888' }}>{email}</Typography>
          </Box>
        </Grid>

        {/* Right Column (Date Joined) */}
        <Grid item xs={5} textAlign="right">
          <Typography sx={{ fontSize: '12px', color: '#888' }}>Date Joined</Typography>
          <Typography sx={{ fontSize: '14px', fontWeight: '500' }}>{dateJoined}</Typography>
        </Grid>
      </Grid>

      {/* Second Row - Current Plan */}
      <Box
        sx={{
          marginTop: '16px',
          padding: '10px',
          borderRadius: '8px',
          backgroundColor: isDarkMode ? '#333' : '#F0F5FF', // Light blue in light mode, dark mode support
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Typography sx={{ fontSize: '14px', fontWeight: '500' }}>Current Plan</Typography>
        <Chip
          label={plan}
          sx={{
            backgroundColor: isDarkMode ? '#4256BF' : '#A3B1FF',
            color: 'white',
            fontSize: '12px',
            fontWeight: 'bold',
            borderRadius: '16px',
          }}
        />
      </Box>
    </Box>
  );
};

export default UserProfileCard;
