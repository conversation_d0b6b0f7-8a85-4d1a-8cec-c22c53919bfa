import React from 'react';
import { Card, CardContent, Grid, Typography, useTheme, Box } from '@mui/material';
import MicIcon from '@mui/icons-material/Mic';
import ClinicalLetterIcon2 from 'themes/icons/ClinicalLetterIcon2';
import TemplatesIcon from 'themes/icons/TemplatesIcon';
import PaintIcon from 'themes/icons/PaintIcon';

// Define the type for the stats prop
interface Stats {
  lettersCount?: number;
  audioMinutesUsed?: number;
  totalTemplates?: number;
  totalDesigns?: number;
}

interface AccountCardProps {
  stats: Stats;
}

const AccountCard: React.FC<AccountCardProps> = ({ stats = {} }) => {
  const theme = useTheme();

  const formatAudioTime = (totalSeconds = 0) => {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}m ${seconds}s`;
  };

  const cardsData = [
    {
      id: 'lettersCount',
      label: 'Document Created',
      value: stats.lettersCount ?? '0',
      color: '#F5F7FF',
      icon: <ClinicalLetterIcon2 />,
      iconBg: '#E7EDFF',
    },
    {
      id: 'audioMinutesUsed',
      label: 'Audio Minutes Used',
      value: formatAudioTime(stats.audioMinutesUsed || 0),
      color: '#F4FBF4',
      icon: <MicIcon />,
      iconBg: '#E4F8E5',
    },
    {
      id: 'totalTemplates',
      label: 'Templates',
      value: stats.totalTemplates ?? '0',
      color: '#FFFEF5',
      icon: <TemplatesIcon />,
      iconBg: '#FFF7D9',
    },
    {
      id: 'totalDesigns',
      label: 'Designs',
      value: stats.totalDesigns ?? '0',
      color: '#F5FAFD',
      icon: <PaintIcon />,
      iconBg: '#E4F3F8',
    },
  ];

  return (
    <Grid container spacing={2} sx={{ mb: theme.spacing(3) }}>
      {cardsData.map((item, index) => (
        <Grid item xs={6} sm={6} md={3} key={index}>
          <Card
            sx={{
              borderRadius: '12px',
              backgroundColor: theme.palette.mode === 'dark' ? '#121212' : '#fff',
              border: theme.palette.mode === 'dark' ? '1px solid #333' : '1px solid #eee',
              boxShadow: 'none',
              height: '100%',
              padding: 2,
            }}
          >
            <CardContent sx={{ padding: 0 }}>
              <Box sx={{ mb: 2 }}>
                <Box
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: '12px',
                    backgroundColor: theme.palette.mode === 'dark' ? '#000' : item.iconBg,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  {item.icon}
                </Box>
              </Box>

              <Typography
                sx={{
                  fontSize: '14px',
                  color: theme.palette.mode === 'dark' ? '#fff' : '#5A5A5A',
                  fontWeight: 500,
                  mb: 1,
                }}
              >
                {item.label}
              </Typography>

              <Typography
                sx={{
                  fontSize: '20px',
                  fontWeight: 600,
                  color: theme.palette.mode === 'dark' ? '#fff' : '#111',
                }}
              >
                {item.value}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default AccountCard;
