// @ts-nocheck
import { Box, Typography, useTheme } from '@mui/material';
import React from 'react';

interface CardProps {
  icon: React.ReactNode;
  label: string;
  value: string | number;
  label2?: string;
  value2?: string | number;
  label3?: string;
  variant?: string;
}

const Card = ({ icon, label, value, label2, value2, label3, variant }: CardProps) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  // Define styles based on variant
  const styles = {
    payment: {
      iconBg: isDarkMode ? '#2D3B63' : '#EFF3FF',
      iconColor: '#6495FD',
    },
    frequency: {
      iconBg: isDarkMode ? '#1B4224' : '#F1F9F1',
      iconColor: '#4ADE80',
    },
    documents: {
      iconBg: isDarkMode ? '#4A3B0A' : '#FFF7E1',
      iconColor: '#FBDF6D',
    },
  };

  const currentStyle = styles[variant] || styles.payment;

  return (
    <Box
      className={`card-container ${isDarkMode ? 'dark-mode' : ''} card-${variant}`}
      data-test-id={`card-${variant}`}
      sx={{
        width: '100%',
        maxWidth: '250px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
        gap: '16px',
        padding: '20px',
        borderRadius: '10px',
        backgroundColor: isDarkMode ? '#222' : 'white',
        boxShadow: isDarkMode ? '0px 4px 8px rgba(255, 255, 255, 0.1)' : '0px 4px 8px rgba(0, 0, 0, 0.05)',
      }}
    >
      {/* Icon with background */}
      <Box
        sx={{
          width: '40px',
          height: '40px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '8px',
          backgroundColor: currentStyle.iconBg,
        }}
        className="icon-box"
      >
        {React.cloneElement(icon, {
          sx: { color: currentStyle.iconColor, fontSize: '24px' },
        })}
      </Box>

      {/* Label */}
      <Typography fontSize="14px" fontWeight="500" className="card-label" sx={{ color: currentStyle.iconColor }}>
        {label}
      </Typography>

      {!value2 && (
        <Typography fontSize="20px">
          {value}
          {label2 && (
            <Typography fontSize="14px" color="#747474" component="span">
              {' '}
              / {label2}
            </Typography>
          )}
        </Typography>
      )}

      {value2 && (
        <Box sx={{ display:'flex', flexDirection:'row', gap:'15px' }}>
          <Box>
            <Typography fontSize="20px">
              {value}
            </Typography>
            <Typography mt='5px' fontSize="14px" color="#747474" component="span">
              / {label2}
            </Typography>
          </Box>
          <Box>
            <Typography fontSize="20px">
              {value2}
            </Typography>
            <Typography fontSize="14px" color="#747474" component="span">
              / {label3}
            </Typography>

          </Box>
        </Box>
      )}
    </Box>
  );
};

export default Card;
