// TODO: Fix typescript errors
// @ts-nocheck
import * as React from 'react';
import PropTypes from 'prop-types';
import { alpha } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import DeleteIcon from '@mui/icons-material/Delete';
import FilterListIcon from '@mui/icons-material/FilterList';
import { visuallyHidden } from '@mui/utils';
import { Pagination, useTheme } from '@mui/material';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getRolesList, useRolesList } from '../../services/permissions';
import IRootState from '../../models/IRootState';
import { IAppActionTypes } from '../../models/IAppState';
import { sortRoleList, updateRoleManagementList } from '../../actions/roleManagement.action';
import { roleListSelector } from '../../selectors/roleManagement.selector';

function createData(id, type, name, description) {
  return {
    id,
    type,
    name,
    description,
  };
}

const rows = [
  createData(1, 'Other', 'Secretary', 'This is a description of the role'),
  createData(1, 'Clinician', 'Secretary', 'This is a description of the role'),
  createData(1, 'Staff Member', 'Secretary', 'This is a description of the role'),
];

function descendingComparator(a, b, orderBy) {
  if (b[orderBy] < a[orderBy]) {
    return -1;
  }
  if (b[orderBy] > a[orderBy]) {
    return 1;
  }
  return 0;
}

function getComparator(order, orderBy) {
  return order === 'desc' ? (a, b) => descendingComparator(a, b, orderBy) : (a, b) => -descendingComparator(a, b, orderBy);
}

// Since 2020 all major browsers ensure sort stability with Array.prototype.sort().
// stableSort() brings sort stability to non-modern browsers (notably IE11). If you
// only support modern browsers you can replace stableSort(exampleArray, exampleComparator)
// with exampleArray.slice().sort(exampleComparator)
function stableSort(array, comparator) {
  const stabilizedThis = array.map((el, index) => [el, index]);
  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) {
      return order;
    }
    return a[1] - b[1];
  });
  return stabilizedThis.map((el) => el[0]);
}

const headCells = [
  {
    id: 'type',
    numeric: false,
    // disablePadding: true,
    label: 'Role Type',
  },
  {
    id: 'name',
    numeric: true,
    // disablePadding: false,
    label: 'Role Name',
  },
  {
    id: 'role_descriptions',
    numeric: true,
    // disablePadding: false,
    label: 'Description',
  },
];

function EnhancedTableHead(props) {
  const theme = useTheme();
  const { onSelectAllClick, order, orderBy, numSelected, rowCount, onRequestSort } = props;
  const createSortHandler = (property) => (event) => {
    onRequestSort(event, property);
  };

  return (
    <TableHead>
      <TableRow>
        {/* <TableCell padding="checkbox" variant="head"> */}
        {/*  <Checkbox */}
        {/*    color="primary" */}
        {/*    size="small" */}
        {/*    indeterminate={numSelected > 0 && numSelected < rowCount} */}
        {/*    checked={rowCount > 0 && numSelected === rowCount} */}
        {/*    onChange={onSelectAllClick} */}
        {/*    inputProps={{ */}
        {/*      'aria-label': 'select all desserts', */}
        {/*    }} */}
        {/*  /> */}
        {/* </TableCell> */}
        {headCells.map((headCell, i) => (
          <TableCell
            key={headCell.id}
            // align={headCell.numeric ? 'right' : 'left'}
            // align={i ? 'right' : 'inherit'}
            variant="head"
            sx={(theme) => ({
              color: theme?.customProperties.permissionHeadingColor,
            })}
            // padding={headCell.disablePadding ? 'none' : 'normal'}
            sortDirection={orderBy === headCell.id ? order : false}
          >
            <TableSortLabel
              active={orderBy === headCell.id}
              direction={orderBy === headCell.id ? order : 'asc'}
              onClick={createSortHandler(headCell.id)}
            >
              {headCell.label}
              {orderBy === headCell.id ? (
                <Box component="span" sx={visuallyHidden}>
                  {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                </Box>
              ) : null}
            </TableSortLabel>
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}

EnhancedTableHead.propTypes = {
  numSelected: PropTypes.number.isRequired,
  onRequestSort: PropTypes.func.isRequired,
  onSelectAllClick: PropTypes.func.isRequired,
  order: PropTypes.oneOf(['asc', 'desc']).isRequired,
  orderBy: PropTypes.string.isRequired,
  rowCount: PropTypes.number.isRequired,
};

function EnhancedTableToolbar(props) {
  const { numSelected } = props;

  return (
    <Toolbar
      sx={{
        pl: { sm: 2 },
        pr: { xs: 1, sm: 1 },
        ...(numSelected > 0 && {
          bgcolor: (theme) => alpha(theme.palette.primary.main, theme.palette.action.activatedOpacity),
        }),
      }}
    >
      {numSelected > 0 ? (
        <Typography sx={{ flex: '1 1 100%' }} color="inherit" variant="subtitle1" component="div">
          {numSelected} selected
        </Typography>
      ) : (
        <Typography sx={{ flex: '1 1 100%' }} variant="h6" id="tableTitle" component="div">
          Nutrition
        </Typography>
      )}

      {numSelected > 0 ? (
        <Tooltip title="Delete">
          <IconButton>
            <DeleteIcon />
          </IconButton>
        </Tooltip>
      ) : (
        <Tooltip title="Filter list">
          <IconButton>
            <FilterListIcon />
          </IconButton>
        </Tooltip>
      )}
    </Toolbar>
  );
}

EnhancedTableToolbar.propTypes = {
  numSelected: PropTypes.number.isRequired,
};

interface RoleManagementProps {
  onSelect: (row: any) => void;
}

export default function RoleManagementTable(props: RoleManagementProps) {
  // const [roleList, setRoleList] = useState([]);
  const [order, setOrder] = React.useState('asc');
  const [orderBy, setOrderBy] = React.useState('name');
  const [selected, setSelected] = React.useState([]);
  const [page, setPage] = React.useState(0);
  const [dense, setDense] = React.useState(false);
  const [rowsPerPage, setRowsPerPage] = React.useState(5);

  const auth = useSelector((state: IRootState) => state.auth);
  const roleList: any[] = useSelector(roleListSelector);
  const dispatch = useDispatch();

  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
    dispatch(sortRoleList(roleList, isAsc ? 'desc' : 'asc', property));
  };

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelected = rows.map((n) => n.id);
      setSelected(newSelected);
      return;
    }
    setSelected([]);
  };

  const handleClick = (event, id) => {
    setSelected([id]);
    props.onSelect(roleList.find((r) => r.id === id));
    dispatch({ type: IAppActionTypes.SET_PAGE_EDITABLE, payload: true });
  };

  const isSelected = (id) => selected.indexOf(id) !== -1;

  // Avoid a layout jump when reaching the last page with empty rows.
  const emptyRows = page > 0 ? Math.max(0, (1 + page) * rowsPerPage - rows.length) : 0;

  const visibleRows = React.useMemo(
    () => stableSort(rows, getComparator(order, orderBy)).slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage),
    [order, orderBy, page, rowsPerPage],
  );

  useEffect(() => {
    dispatch(updateRoleManagementList(auth.user.clinic_id));
  }, [auth.user.clinic_id]);

  return (
    <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
      <Paper sx={{ width: '100%', mb: 2, boxShadow: 'none' }}>
        {/* <EnhancedTableToolbar numSelected={selected.length} /> */}
        <TableContainer>
          <Table aria-labelledby="tableTitle" size={dense ? 'small' : 'medium'} className="task-table ">
            <EnhancedTableHead
              numSelected={selected.length}
              order={order}
              orderBy={orderBy}
              onSelectAllClick={handleSelectAllClick}
              onRequestSort={handleRequestSort}
              rowCount={roleList === undefined ? 0 : roleList.length}
            />
            <TableBody>
              {roleList?.map((row, index) => {
                const isItemSelected = isSelected(row.id);
                const labelId = `enhanced-table-checkbox-${index}`;

                return (
                  <TableRow
                    hover
                    onClick={(event) => handleClick(event, row.id)}
                    role="checkbox"
                    aria-checked={isItemSelected}
                    tabIndex={-1}
                    key={row.id}
                    selected={isItemSelected}
                    sx={{
                      cursor: 'pointer',
                      border: 0,
                      borderBottom: 'none',
                      [`& .${tableCellClasses.root}`]: {
                        fontFamily: 'UrbanistMedium',
                        borderBottom: '1px solid var(--black-5, rgba(28, 28, 28, 0.05))',
                      },
                    }}
                  >
                    {/* <TableCell padding="checkbox" className="table-cell"> */}
                    {/*  <Checkbox */}
                    {/*    color="primary" */}
                    {/*    size="small" */}
                    {/*    checked={isItemSelected} */}
                    {/*    inputProps={{ */}
                    {/*      'aria-labelledby': labelId, */}
                    {/*    }} */}
                    {/*  /> */}
                    {/* </TableCell> */}
                    <TableCell component="th" id={labelId} scope="row" className="table-cell">
                      {row.name}
                    </TableCell>
                    <TableCell className="table-cell" align="left">
                      {row.name}
                    </TableCell>
                    <TableCell className="table-cell" align="left">
                      {row.role_descriptions}
                    </TableCell>
                  </TableRow>
                );
              })}
              {emptyRows > 0 && (
                <TableRow
                  style={{
                    height: (dense ? 33 : 53) * emptyRows,
                  }}
                >
                  <TableCell colSpan={6} />
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
      {/* <Pagination */}
      {/*  shape="rounded" */}
      {/*  page={page + 1} */}
      {/*  count={10} */}
      {/*  onChange={handleChangePage} */}
      {/*  sx={{ display: 'flex', flexDirection: 'row-reverse', padding: '1rem' }} */}
      {/* /> */}
    </Box>
  );
}
