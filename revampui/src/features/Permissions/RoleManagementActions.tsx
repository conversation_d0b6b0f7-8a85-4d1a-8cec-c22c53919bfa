import React, { useState } from 'react';
import { Box, Button } from '@mui/material';
import { useSelector } from 'react-redux';
import AddIcon from '@mui/icons-material/Add';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import InviteUserDialog from './InviteuserModal';
import AddRoleModal from './AddRoleModal';
import { hasInviteUserSelector } from '../../selectors/auth.selector';

const RoleManagementActions: React.FC = () => {
  const [inviteUserModalIsOpen, setInviteUserModalIsOpen] = useState(false);
  const [addRoleModalIsOpen, setAddRoleModalIsOpen] = useState(false);
  const allowInviteUser: boolean = useSelector(hasInviteUserSelector!);

  return (
    <Box className="role-management-actions">
      {/* Add New Role Button */}
      <Button onClick={() => setAddRoleModalIsOpen(true)} className="add-role-button">
        Add New Role
        <AddIcon className="button-icon" />
      </Button>

      {/* Invite User Button (Only shown if user has permission) */}
      {allowInviteUser && (
        <Button onClick={() => setInviteUserModalIsOpen(true)} className="invite-user-button">
          Invite New User
          <PersonAddIcon className="button-icon" />
        </Button>
      )}

      {/* Invite User Modal */}
      <InviteUserDialog open={inviteUserModalIsOpen} handleClose={() => setInviteUserModalIsOpen(false)} />

      {/* Add Role Modal */}
      <AddRoleModal open={addRoleModalIsOpen} handleClose={() => setAddRoleModalIsOpen(false)} />
    </Box>
  );
};

export default RoleManagementActions;
