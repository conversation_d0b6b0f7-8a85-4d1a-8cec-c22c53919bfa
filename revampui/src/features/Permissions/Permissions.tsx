// TODO: Fix typescript errors
// @ts-nocheck
import * as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Switch from '@mui/material/Switch';
import { styled, useTheme } from '@mui/material/styles';
import { Box, Snackbar } from '@mui/material';
import Typography from '@mui/material/Typography';
import Button from 'components/atoms/Button';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import _ from 'lodash';
import RoleManagementAppBar from './RoleManagementBar';
import { updateClinicPermissions, updateUser } from '../../services/users';
import IRootState from '../../models/IRootState';
import { pageEditableSelector } from '../../selectors/app.selector';
import { enqueueSnackbarAction } from '../../actions/app.action';
import { updateRoleManagementList } from '../../actions/roleManagement.action';

const DISABLED_COLUMNS = ['Adhoc Letter', 'Letters', 'Settings', 'Dashboard'];

const COLUMNS_MAP: Record<string, string> = {
  'Clinical Note': 'Document History',
  'Patient Management': 'Patients',
};

const Android12Switch = styled(Switch)(({ theme }) => ({
  padding: 8,
  '& .MuiSwitch-switchBase': {
    '&.Mui-checked': {
      color: '#fff',
      '& + .MuiSwitch-track': {
        backgroundColor: '#000000', // theme.palette.mode === 'dark' ? '#2ECA45' : '#65C466',
        opacity: 1,
        border: 0,
      },
    },
  },
  '&.Mui-focusVisible .MuiSwitch-thumb': {
    color: '#000000',
    border: '6px solid #fff',
  },
  '&.Mui-disabled .MuiSwitch-thumb': {
    color: theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600],
  },
  '&.Mui-disabled + .MuiSwitch-track': {
    opacity: theme.palette.mode === 'light' ? 0.7 : 0.3,
  },
  '& .MuiSwitch-track': {
    borderRadius: 22 / 2,
    '&::before, &::after': {
      content: '""',
      position: 'absolute',
      top: '50%',
      transform: 'translateY(-50%)',
      width: 16,
      height: 16,
    },
    '&::before': {
      left: 12,
    },
    '&::after': {
      right: 12,
    },
  },
  '& .MuiSwitch-thumb': {
    boxShadow: 'none',
    width: 16,
    height: 16,
    margin: 2,
  },
}));

// BasicTableProps interface
interface BasicTableProps {
  selectedRow: {
    id: string;
    permissions: IPermission[];
    email?: string;
    clinic_id?: string;
    first_name?: string;
    last_name?: string;
    role_descriptions?: string[];
  };
  isModal: boolean;
  setSelectedPermissions: (permissions: any[]) => void;
  role?: IRole;
  setLastUpdate?: () => void;
}

interface IPermission {
  section: string;
  label: string;
  isSelected: boolean;
  id: string;
  status: string;
}

interface IRole {
  role_id: string;
}

interface IPermissionRow {
  [label: string]: {
    isSelected: boolean;
    id: string;
    status: string;
  };
}

interface IRow {
  [section: string]: IPermissionRow;
}

export default function BasicTable({ selectedRow, isModal, setSelectedPermissions, role, setLastUpdate }: BasicTableProps) {
  const [rows, setRows] = useState<IPermissionRow>([]);
  const [currentPermissions, setCurrentPermissions] = useState<any>([]);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('User Permissions updated');
  const [selectedRole, setSelectedRole] = useState(role);
  const [isFullAccess, setIsFullAccess] = useState<boolean>(false);
  const auth = useSelector((state: IRootState) => state.auth);
  const editableSelector = useSelector(pageEditableSelector);
  const dispatch = useDispatch();
  const theme = useTheme();

  useEffect(() => {
    if (selectedRow?.permissions) {
      const _rows: Record<string, any> = {};
      selectedRow.permissions.forEach((permission: IPermission) => {
        if (!_rows[permission.section]) {
          const { label, isSelected, id, status } = permission;
          _rows[permission.section] = {
            [label]: {
              isSelected,
              id,
              status,
            },
          };
        } else {
          const { label, isSelected, id, status } = permission;
          _rows[permission.section] = {
            ..._rows[permission.section],
            [label]: {
              isSelected,
              id,
              status,
            },
          };
        }
      });
      setRows(_rows);
      const permissions: ((prevState: never[]) => never[]) | any[] = [];
      Object.values(_rows).map((row) => {
        Object.values(row).map((r: any) => {
          if (r.isSelected) {
            permissions.push(r.id);
          }
        });
      });
      setSelectedPermissions(permissions);
      setCurrentPermissions(permissions);
      setIsFullAccess(false);
    }
  }, [selectedRow]);

  const onChange = (e: React.ChangeEvent<HTMLInputElement>, key: string, access: string) => {
    const isChecked = e.target.checked;

    const _rows: IPermissionRow = {
      ...rows,
      [key]: {
        ...rows[key],
        [access]: {
          ...rows[key][access],
          isSelected: isChecked,
        },
      },
    };

    if (access === 'View') {
      // If 'View' is being disabled, disable all other permissions
      if (!isChecked) {
        ['Edit', 'Create', 'Delete'].forEach((permission) => {
          if (_rows[key][permission]) {
            _rows[key][permission].isSelected = false;
          }
        });
      }
    } else if (isChecked && _rows[key].View) {
      // If any other permission is being enabled, ensure 'View' is enabled
      _rows[key].View.isSelected = true;
    }

    setRows(_rows);

    const isAllPermissionsSelected = Object.entries(_rows)
      .filter(([key]) => !['Adhoc Letter', 'Settings'].includes(key)) // Skip 'Adhoc Letter' and 'Settings'
      .map(([, row]) => Object.values(row)) // Map to the values of each section
      .map((permissions) => permissions.every((permission) => permission.isSelected)) // Check if every permission is selected
      .every(Boolean);
    setIsFullAccess(isAllPermissionsSelected);

    // Collect selected permissions
    const permissions: string[] = [];
    Object.values(_rows).forEach((row: IPermission[]) => {
      Object.values(row).forEach((r: IPermission) => {
        if (r.isSelected) {
          permissions.push(r.id);
        }
      });
    });
    setSelectedPermissions(permissions);
    setCurrentPermissions(permissions);
  };

  const onFullAccessChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const _rows = {};
    setIsFullAccess(e.target.checked);
    selectedRow.permissions.forEach((permission) => {
      if (!_rows[permission.section]) {
        const { label, isSelected, id, status } = permission;
        _rows[permission.section] = {
          [label]: {
            isSelected: e.target.checked ? 1 : isSelected,
            id,
            status,
          },
        };
      } else {
        const { label, isSelected, id, status } = permission;
        _rows[permission.section] = {
          ..._rows[permission.section],
          [label]: {
            isSelected: e.target.checked ? 1 : isSelected,
            id,
            status,
          },
        };
      }
    });
    setRows(_rows);
    const permissions = [];
    Object.values(_rows).map((row) => {
      Object.values(row).map((r) => {
        if (r.isSelected) {
          permissions.push(r.id);
        }
      });
    });
    setSelectedPermissions(permissions);
    setCurrentPermissions(permissions);
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  useEffect(() => {
    setSelectedRole(role?.role_id);
  }, [role]);

  const updateUserPermissions = () => {
    if (selectedRow.email) {
      updateUser(selectedRow.id, {
        permissions: currentPermissions,
        first_name: selectedRow.first_name,
        last_name: selectedRow.last_name,
        role_id: selectedRole,
        clinic_id: auth.user.clinic_id,
      })
        .then(() => {
          dispatch(
            enqueueSnackbarAction({
              message: 'User Permissions updated',
              key: new Date().getTime() + Math.random(),
              variant: 'success',
            }),
          );
          setLastUpdate && setLastUpdate(new Date());
        })
        .catch(() => {
          dispatch(
            enqueueSnackbarAction({
              message: 'Error: Please try again',
              key: new Date().getTime() + Math.random(),
              variant: 'error',
            }),
          );
        });
    } else if (selectedRow.clinic_id) {
      updateClinicPermissions(selectedRow.id, {
        role_descriptions: selectedRow.role_descriptions,
        permissions: currentPermissions,
      })
        .then(() => {
          dispatch(
            enqueueSnackbarAction({
              message: 'Permissions updated',
              key: new Date().getTime() + Math.random(),
              variant: 'success',
            }),
          );
          setLastUpdate && setLastUpdate(new Date());
          dispatch(updateRoleManagementList(auth.user?.clinic_id));
        })
        .catch(() => {
          dispatch(
            enqueueSnackbarAction({
              message: 'Error: Please try again',
              key: new Date().getTime() + Math.random(),
              variant: 'error',
            }),
          );
        });
    }
  };

  return (
    <Box
      sx={{
        width: '100%',
        padding: 2,
        borderRadius: 2,
        boxShadow: 1,
        backgroundColor: theme.palette.mode === 'dark' ? '#222' : '#f3f6f6',
        color: theme.palette.mode === 'dark' ? 'white' : 'black',
      }}
    >
      {_.isEmpty(selectedRow) && (
        <Box p={2} sx={{ fontSize: '16px' }}>
          Please select a row
        </Box>
      )}
      {!_.isEmpty(selectedRow) && (
        <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
          <Table aria-label="simple table" size="small" className="permission-table">
            <TableHead sx={{}}>
              <TableRow
                sx={(theme) => ({
                  color: theme?.customProperties.permissionHeadingColor,
                })}
              >
                {isModal ? (
                  <TableCell
                    sx={(theme) => ({
                      color: theme?.customProperties.permissionHeadingColor,
                    })}
                    align="left"
                  >
                    Type
                  </TableCell>
                ) : (
                  <TableCell
                    sx={(theme) => ({
                      color: theme?.customProperties.permissionHeadingColor,
                    })}
                    align="left"
                  >
                    Type
                  </TableCell>
                )}
                <TableCell
                  sx={(theme) => ({
                    color: theme?.customProperties.permissionHeadingColor,
                  })}
                  align="center"
                >
                  Edit
                </TableCell>
                <TableCell
                  sx={(theme) => ({
                    color: theme?.customProperties.permissionHeadingColor,
                  })}
                  align="center"
                >
                  Create
                </TableCell>
                <TableCell
                  sx={(theme) => ({
                    color: theme?.customProperties.permissionHeadingColor,
                  })}
                  align="center"
                >
                  View
                </TableCell>
                <TableCell
                  sx={(theme) => ({
                    color: theme?.customProperties.permissionHeadingColor,
                  })}
                  align="center"
                >
                  Delete
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Object.entries(rows).map(
                ([key, row]) =>
                  !DISABLED_COLUMNS.includes(key) && (
                    <TableRow key={key} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                      {isModal ? (
                        <TableCell component="th" scope="row" sx={{ paddingLeft: '0', fontSize: '12px' }}>
                          {COLUMNS_MAP[key] ? COLUMNS_MAP[key] : key}
                        </TableCell>
                      ) : (
                        <TableCell component="th" scope="row" sx={{ fontSize: '12px' }}>
                          {COLUMNS_MAP[key] ? COLUMNS_MAP[key] : key}
                        </TableCell>
                      )}
                      <TableCell align="center">
                        {row.Edit && (
                          <Android12Switch checked={!!row.Edit.isSelected} onChange={(e) => onChange(e, key, 'Edit')} />
                        )}
                      </TableCell>
                      <TableCell align="center">
                        {row.Create && (
                          <Android12Switch checked={!!row.Create.isSelected} onChange={(e) => onChange(e, key, 'Create')} />
                        )}
                      </TableCell>
                      <TableCell align="center">
                        {row.View && (
                          <Android12Switch checked={!!row.View.isSelected} onChange={(e) => onChange(e, key, 'View')} />
                        )}
                      </TableCell>
                      <TableCell align="center">
                        {row.Delete && (
                          <Android12Switch checked={!!row.Delete.isSelected} onChange={(e) => onChange(e, key, 'Delete')} />
                        )}
                      </TableCell>
                    </TableRow>
                  ),
              )}
            </TableBody>
          </Table>
          <Typography
            variant="h6"
            sx={{ fontSize: '14px', paddingLeft: !isModal ? '1rem' : '0', marginLeft: '1rem' }}
            className="full-acess-swtich"
          >
            Full Access <Android12Switch checked={isFullAccess} onChange={(e) => onFullAccessChange(e)} />
          </Typography>
        </TableContainer>
      )}
      {!isModal && (
        <div className="flex justify-content-space-between p1" style={{ display: 'flex', justifyContent: 'right' }}>
          {/* <div className="flex gap-45">
            <div className="rotate-90">
              <UTurnLeftIcon />
            </div>
            <div className="rotate--90">
              <UTurnRightIcon />
            </div>
          </div> */}

          <Button disabled={!editableSelector} onClick={updateUserPermissions}>
            Apply
          </Button>
        </div>
      )}
    </Box>
  );
}
