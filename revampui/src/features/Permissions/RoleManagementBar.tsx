// TODO: Fix typescript errors

import * as React from 'react';
import { Box, Menu, MenuItem, useTheme } from '@mui/material';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import SvgIcon from '@mui/material/SvgIcon';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { useState } from 'react';
import CustomAppBar from 'features/Tasks/CustomAppBar';
import { useDispatch, useSelector } from 'react-redux';
import InviteUserDialog from './InviteuserModal';
import { IAppActionTypes } from '../../models/IAppState';
import { setBreadcrumbs } from '../../actions/app.action';
import AddRoleModal from './AddRoleModal';
import { hasInviteUserSelector, remainingCustomTemplateSelector } from '../../selectors/auth.selector';

const options = ['Role Management', 'User Management'];

const ITEM_HEIGHT = 48;

// @ts-ignore
export default function RoleManagementAppBar({ setIsRoleManagement }) {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const [selectedOption, setSelectedOption] = useState(options[0]);
  const [inviteUserModalIsOpen, setInviteUserModalIsOpen] = useState(false);
  const [addRoleModalIsOpen, setAddRoleModalIsOpen] = useState(false);
  const dispatch = useDispatch();
  const allowInviteUser: boolean = useSelector(hasInviteUserSelector!);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClicked = (e: any) => {
    setAnchorEl(null);
    setSelectedOption(e);
    setIsRoleManagement(e === options[0]);
    dispatch({ type: IAppActionTypes.SET_PAGE_EDITABLE, payload: false });
    const breadcrumbText = e === 'Role Management' ? 'Permissions / Role Listing' : 'Permissions / User Listing';
    dispatch(setBreadcrumbs(breadcrumbText));
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <CustomAppBar
        leftContent={
          <Toolbar>
            <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, display: { sm: 'block' }, fontSize: '18px' }}>
              {selectedOption}
              <span>
                <IconButton
                  aria-label="more"
                  id="long-button"
                  aria-controls={open ? 'long-menu' : undefined}
                  aria-expanded={open ? 'true' : undefined}
                  aria-haspopup="true"
                  onClick={handleClick}
                >
                  <ExpandMoreIcon />
                </IconButton>
                <Menu
                  id="long-menu"
                  MenuListProps={{
                    'aria-labelledby': 'long-button',
                  }}
                  anchorEl={anchorEl}
                  open={open}
                  onClose={handleClose}
                  PaperProps={{
                    style: {
                      maxHeight: ITEM_HEIGHT * 4.5,
                      width: '20ch',
                    },
                  }}
                >
                  {options.map((option) => (
                    <MenuItem key={option} selected={option === selectedOption} onClick={() => handleMenuItemClicked(option)}>
                      {option}
                    </MenuItem>
                  ))}
                </Menu>
              </span>
            </Typography>
          </Toolbar>
        }
        rightContent={
          <Box>
            {' '}
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                flexWrap: 'nowrap',
                alignContent: 'center',
                alignItems: 'center',
              }}
            >
              <IconButton
                size="large"
                edge="start"
                color="inherit"
                aria-label="open add role modal"
                onClick={() => setAddRoleModalIsOpen(true)}
              >
                <SvgIcon>
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      id="Vector"
                      d="M11.0731 2.46344C11.0731 2.3001 11.138 2.14346 11.2535 2.02796C11.369 1.91247 11.5257 1.84758 11.689 1.84758H12.9207V0.615866C12.9207 0.452531 12.9856 0.295885 13.1011 0.180389C13.2166 0.0648935 13.3732 8.75107e-06 13.5366 8.75107e-06C13.6999 8.75107e-06 13.8565 0.0648935 13.972 0.180389C14.0875 0.295885 14.1524 0.452531 14.1524 0.615866V1.84758H15.3841C15.5475 1.84758 15.7041 1.91247 15.8196 2.02796C15.9351 2.14346 16 2.3001 16 2.46344C16 2.62678 15.9351 2.78342 15.8196 2.89892C15.7041 3.01441 15.5475 3.0793 15.3841 3.0793H14.1524V4.31101C14.1524 4.47435 14.0875 4.63099 13.972 4.74649C13.8565 4.86199 13.6999 4.92687 13.5366 4.92687C13.3732 4.92687 13.2166 4.86199 13.1011 4.74649C12.9856 4.63099 12.9207 4.47435 12.9207 4.31101V3.0793H11.689C11.5257 3.0793 11.369 3.01441 11.2535 2.89892C11.138 2.78342 11.0731 2.62678 11.0731 2.46344ZM15.8891 6.67283C16.1705 8.34531 15.913 10.0639 15.154 11.5806C14.3949 13.0972 13.1735 14.3334 11.666 15.1105C10.1586 15.8877 8.4432 16.1658 6.76747 15.9045C5.09173 15.6432 3.54239 14.8561 2.34315 13.6568C1.14391 12.4576 0.35682 10.9083 0.0955249 9.23253C-0.16577 7.55679 0.112252 5.84138 0.889448 4.33395C1.66664 2.82653 2.9028 1.6051 4.41943 0.846023C5.93606 0.0869484 7.65469 -0.170495 9.32717 0.110863C9.48704 0.139135 9.62928 0.229387 9.72294 0.361987C9.81661 0.494588 9.85413 0.65881 9.82734 0.818932C9.80055 0.979053 9.71162 1.12212 9.5799 1.21701C9.44817 1.3119 9.2843 1.35095 9.12394 1.32564C8.15253 1.16223 7.15717 1.21246 6.20714 1.47284C5.25712 1.73321 4.37524 2.19749 3.62288 2.83334C2.87053 3.46919 2.26577 4.26135 1.85069 5.15469C1.43561 6.04804 1.22019 7.0211 1.21941 8.00616C1.21795 9.6645 1.82745 11.2653 2.93149 12.5027C3.61835 11.5074 4.58416 10.7373 5.70747 10.2895C5.10407 9.8142 4.66377 9.16273 4.44779 8.42563C4.23181 7.68854 4.25089 6.90246 4.50238 6.17671C4.75387 5.45096 5.22526 4.82162 5.85101 4.37621C6.47676 3.93079 7.22576 3.69144 7.99384 3.69144C8.76193 3.69144 9.51092 3.93079 10.1367 4.37621C10.7624 4.82162 11.2338 5.45096 11.4853 6.17671C11.7368 6.90246 11.7559 7.68854 11.5399 8.42563C11.3239 9.16273 10.8836 9.8142 10.2802 10.2895C11.4035 10.7373 12.3693 11.5074 13.0562 12.5027C14.1602 11.2653 14.7697 9.6645 14.7683 8.00616C14.7683 7.62751 14.7369 7.24951 14.6744 6.87606C14.6602 6.79595 14.662 6.71382 14.6798 6.63443C14.6975 6.55503 14.7308 6.47994 14.7778 6.41349C14.8247 6.34704 14.8844 6.29055 14.9533 6.24728C15.0222 6.20401 15.0989 6.17482 15.1792 6.1614C15.2594 6.14797 15.3415 6.15058 15.4208 6.16907C15.5 6.18756 15.5748 6.22156 15.6408 6.26911C15.7068 6.31666 15.7627 6.37682 15.8054 6.44612C15.848 6.51541 15.8765 6.59246 15.8891 6.67283ZM7.99384 9.85373C8.48106 9.85373 8.95734 9.70925 9.36245 9.43857C9.76756 9.16788 10.0833 8.78315 10.2698 8.33301C10.4562 7.88288 10.505 7.38757 10.4099 6.90971C10.3149 6.43185 10.0803 5.99291 9.73575 5.64839C9.39123 5.30387 8.95229 5.06926 8.47443 4.9742C7.99657 4.87915 7.50126 4.92794 7.05113 5.11439C6.60099 5.30084 6.21626 5.61658 5.94557 6.02169C5.67489 6.4268 5.53041 6.90308 5.53041 7.3903C5.53041 8.04364 5.78995 8.67023 6.25193 9.13221C6.71392 9.59419 7.3405 9.85373 7.99384 9.85373ZM7.99384 14.7806C9.49758 14.7821 10.9587 14.2808 12.1447 13.3564C11.6992 12.6596 11.0854 12.0862 10.36 11.689C9.63461 11.2918 8.82088 11.0836 7.99384 11.0836C7.1668 11.0836 6.35307 11.2918 5.62766 11.689C4.90224 12.0862 4.28848 12.6596 3.84296 13.3564C5.02902 14.2808 6.4901 14.7821 7.99384 14.7806Z"
                      fill="black"
                    />
                  </svg>
                </SvgIcon>
              </IconButton>

              <Typography
                noWrap
                component="div"
                sx={{ flexGrow: 1, display: { xs: 'none', sm: 'block' }, fontSize: '14px', marginRight: '10px' }}
              >
                Add New Role
              </Typography>

              {allowInviteUser && (
                <>
                  <IconButton
                    size="large"
                    edge="start"
                    color="inherit"
                    aria-label="open drawer"
                    onClick={() => setInviteUserModalIsOpen(true)}
                    // sx={{ mr: 2 }}
                  >
                    <SvgIcon>
                      <svg width="20" height="14" viewBox="0 0 20 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          id="Vector"
                          d="M19.543 8.27374C19.543 8.44247 19.4759 8.60429 19.3566 8.72359C19.2373 8.84291 19.0755 8.90993 18.9068 8.90993H17.6344V10.1823C17.6344 10.3511 17.5674 10.5129 17.448 10.6322C17.3287 10.7515 17.1669 10.8185 16.9982 10.8185C16.8295 10.8185 16.6676 10.7515 16.5483 10.6322C16.429 10.5129 16.362 10.3511 16.362 10.1823V8.90993H15.0896C14.9209 8.90993 14.759 8.84291 14.6397 8.72359C14.5204 8.60429 14.4534 8.44247 14.4534 8.27374C14.4534 8.10501 14.5204 7.94319 14.6397 7.82388C14.759 7.70457 14.9209 7.63754 15.0896 7.63754H16.362V6.36515C16.362 6.19642 16.429 6.0346 16.5483 5.91529C16.6676 5.79598 16.8295 5.72895 16.9982 5.72895C17.1669 5.72895 17.3287 5.79598 17.448 5.91529C17.5674 6.0346 17.6344 6.19642 17.6344 6.36515V7.63754H18.9068C19.0755 7.63754 19.2373 7.70457 19.3566 7.82388C19.4759 7.94319 19.543 8.10501 19.543 8.27374ZM14.9409 12.9538C15.0495 13.083 15.1023 13.2502 15.0877 13.4184C15.0731 13.5866 14.9922 13.7422 14.8629 13.8508C14.7337 13.9594 14.5665 14.0122 14.3983 13.9976C14.2301 13.983 14.0745 13.9021 13.9659 13.7729C12.3659 11.8675 10.1662 10.8185 7.77333 10.8185C5.38044 10.8185 3.18079 11.8675 1.58075 13.7729C1.47213 13.902 1.31664 13.9828 1.14849 13.9973C0.980337 14.0119 0.813295 13.959 0.684112 13.8504C0.554928 13.7418 0.474184 13.5863 0.459643 13.4181C0.445102 13.25 0.497955 13.0829 0.606575 12.9538C1.79467 11.5398 3.27224 10.5354 4.91442 9.99783C3.91711 9.37667 3.14923 8.44759 2.72697 7.35115C2.3047 6.25472 2.25102 5.05058 2.57405 3.92092C2.89708 2.79126 3.57924 1.79754 4.51733 1.0901C5.45542 0.382666 6.59839 0 7.77333 0C8.94827 0 10.0912 0.382666 11.0293 1.0901C11.9674 1.79754 12.6496 2.79126 12.9726 3.92092C13.2956 5.05058 13.242 6.25472 12.8197 7.35115C12.3974 8.44759 11.6296 9.37667 10.6322 9.99783C12.2744 10.5354 13.752 11.5398 14.9409 12.9538ZM7.77333 9.54613C8.59121 9.54613 9.39072 9.3036 10.0708 8.84921C10.7508 8.39482 11.2808 7.74898 11.5938 6.99335C11.9068 6.23773 11.9887 5.40626 11.8292 4.6041C11.6696 3.80193 11.2757 3.0651 10.6974 2.48677C10.1191 1.90844 9.38225 1.51459 8.58008 1.35503C7.77792 1.19547 6.94645 1.27736 6.19083 1.59035C5.4352 1.90334 4.78936 2.43337 4.33497 3.11341C3.88058 3.79346 3.63805 4.59297 3.63805 5.41085C3.63931 6.50721 4.0754 7.5583 4.85064 8.33354C5.62588 9.10878 6.67697 9.54487 7.77333 9.54613Z"
                          fill={theme.palette.text.primary}
                        />
                      </svg>
                    </SvgIcon>
                  </IconButton>

                  <Typography noWrap component="div" sx={{ flexGrow: 1, display: { xs: 'none', sm: 'block' }, fontSize: '14px' }}>
                    Invite New User
                  </Typography>
                </>
              )}
            </div>
            <InviteUserDialog
              open={inviteUserModalIsOpen}
              handleClose={() => setInviteUserModalIsOpen(false)}
              // roleList={roleList}
            />
            <AddRoleModal open={addRoleModalIsOpen} handleClose={() => setAddRoleModalIsOpen(false)} />
          </Box>
        }
      />
    </Box>
  );
}
