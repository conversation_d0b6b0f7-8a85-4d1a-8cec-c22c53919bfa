// TODO: Fix typescript errors
// @ts-nocheck
import * as React from 'react';
import PropTypes from 'prop-types';
import { alpha } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';

import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';

import FilterListIcon from '@mui/icons-material/FilterList';
import { visuallyHidden } from '@mui/utils';
import { Button, Pagination, useTheme } from '@mui/material';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import DeleteIcon from 'themes/icons/DeleteIcon';
import IRootState from '../../models/IRootState';
import { deleteInvitedUser, getUsers } from '../../services/users';
import NoRecordsRow from '../../components/molecules/NoRecordsMessage/NoRecordsMessage';
import { IAppActionTypes } from '../../models/IAppState';
import AlertDialogSlide from '../../components/molecules/ConfirmationDialog';
import { enqueueSnackbarAction } from '../../actions/app.action';

function createData(id, type, name, description) {
  return {
    id,
    type,
    name,
    description,
  };
}

const rows = [
  createData(1, 'Other', 'Secretary', 'This is a description of the role'),
  createData(1, 'Clinician', 'Secretary', 'This is a description of the role'),
  createData(1, 'Staff Member', 'Secretary', 'This is a description of the role'),
];

function descendingComparator(a, b, orderBy) {
  if (b[orderBy] < a[orderBy]) {
    return -1;
  }
  if (b[orderBy] > a[orderBy]) {
    return 1;
  }
  return 0;
}

function getComparator(order, orderBy) {
  return order === 'desc' ? (a, b) => descendingComparator(a, b, orderBy) : (a, b) => -descendingComparator(a, b, orderBy);
}

// Since 2020 all major browsers ensure sort stability with Array.prototype.sort().
// stableSort() brings sort stability to non-modern browsers (notably IE11). If you
// only support modern browsers you can replace stableSort(exampleArray, exampleComparator)
// with exampleArray.slice().sort(exampleComparator)
function stableSort(array, comparator) {
  const stabilizedThis = array.map((el, index) => [el, index]);
  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) {
      return order;
    }
    return a[1] - b[1];
  });
  return stabilizedThis.map((el) => el[0]);
}

const headCells = [
  {
    id: 'first_name',
    numeric: false,
    //  disablePadding: true,
    label: 'Name',
  },
  {
    id: 'email',
    numeric: false,
    //  disablePadding: false,
    label: 'Email',
  },
  // {
  //   id: 'registration_number',
  //   numeric: true,
  //   disablePadding: false,
  //   label: 'GMC / Registration No.',
  // },
  {
    id: 'role',
    numeric: true,
    //  disablePadding: false,
    label: 'User Role',
  },
  {
    id: 'action',
    numeric: true,
    //  disablePadding: false,
    label: 'Action',
  },
];

function EnhancedTableHead(props) {
  const { onSelectAllClick, order, orderBy, numSelected, rowCount, onRequestSort } = props;
  const createSortHandler = (property) => (event) => {
    onRequestSort(event, property);
  };

  return (
    <TableHead>
      <TableRow sx={{}}>
        {/* <TableCell padding="checkbox" variant="head"> */}
        {/*  <Checkbox */}
        {/*    color="primary" */}
        {/*    size='small' */}
        {/*    indeterminate={numSelected > 0 && numSelected < rowCount} */}
        {/*    checked={rowCount > 0 && numSelected === rowCount} */}
        {/*    onChange={onSelectAllClick} */}
        {/*    inputProps={{ */}
        {/*      'aria-label': 'select all desserts', */}
        {/*    }} */}
        {/*  /> */}
        {/* </TableCell> */}
        {headCells.map((headCell, i) => (
          <TableCell
            key={headCell.id}
            variant="head"
            sx={{}}
            padding={headCell.disablePadding ? 'none' : 'normal'}
            sortDirection={orderBy === headCell.id ? order : false}
          >
            <TableSortLabel
              active={orderBy === headCell.id}
              direction={orderBy === headCell.id ? order : 'asc'}
              onClick={createSortHandler(headCell.id)}
            >
              {headCell.label}
              {orderBy === headCell.id ? (
                <Box component="span" sx={visuallyHidden}>
                  {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                </Box>
              ) : null}
            </TableSortLabel>
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}

EnhancedTableHead.propTypes = {
  numSelected: PropTypes.number.isRequired,
  onRequestSort: PropTypes.func.isRequired,
  onSelectAllClick: PropTypes.func.isRequired,
  order: PropTypes.oneOf(['asc', 'desc']).isRequired,
  orderBy: PropTypes.string.isRequired,
  rowCount: PropTypes.number.isRequired,
};

function EnhancedTableToolbar(props) {
  const { numSelected } = props;

  return (
    <Toolbar
      sx={{
        pl: { sm: 2 },
        pr: { xs: 1, sm: 1 },
        ...(numSelected > 0 && {
          bgcolor: (theme) => alpha(theme.palette.primary.main, theme.palette.action.activatedOpacity),
        }),
      }}
    >
      {numSelected > 0 ? (
        <Typography sx={{ flex: '1 1 100%' }} color="inherit" variant="subtitle1" component="div">
          {numSelected} selected
        </Typography>
      ) : (
        <Typography sx={{ flex: '1 1 100%' }} variant="h6" id="tableTitle" component="div">
          Nutrition
        </Typography>
      )}

      {numSelected > 0 ? (
        <Tooltip title="Delete">
          <IconButton>
            <DeleteIcon />
          </IconButton>
        </Tooltip>
      ) : (
        <Tooltip title="Filter list">
          <IconButton>
            <FilterListIcon />
          </IconButton>
        </Tooltip>
      )}
    </Toolbar>
  );
}

EnhancedTableToolbar.propTypes = {
  numSelected: PropTypes.number.isRequired,
};

interface RoleManagementProps {
  onSelect: (row: any) => void;
  // TODO: remove lastUpdate and instead use redux
  lastUpdate: string;
}

export default function UserManagementTable(props: RoleManagementProps) {
  const [userList, setUserList] = useState([]);
  const [order, setOrder] = React.useState('asc');
  const [orderBy, setOrderBy] = React.useState('first_name');
  const [selected, setSelected] = React.useState([]);
  const [page, setPage] = React.useState(0);
  const [dense, setDense] = React.useState(false);
  const [rowsPerPage, setRowsPerPage] = React.useState(5);
  const [listCount, setListCount] = useState(0);
  const [open, setOpen] = useState(false);
  const auth = useSelector((state: IRootState) => state.auth);
  const dispatch = useDispatch();
  const [selectedRow, setSelectedRow] = useState(null);
  const theme = useTheme();

  const buttonHeadingStyles = (theme) => ({
    background: 'none',
    '&:hover': { border: 'none', boxShadow: 'none', background: 'none' },
    color: theme.customProperties.buttonSecondary,
    textTransform: 'none',
  });

  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelected = rows.map((n) => n.id);
      setSelected(newSelected);
      return;
    }
    setSelected([]);
  };

  const handleClick = (event, id) => {
    setSelected([id]);
    props.onSelect(userList.find((u) => u.id === id));
    dispatch({ type: IAppActionTypes.SET_PAGE_EDITABLE, payload: true });
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage - 1);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleChangeDense = (event) => {
    setDense(event.target.checked);
  };

  const isSelected = (id) => selected.indexOf(id) !== -1;

  // Avoid a layout jump when reaching the last page with empty rows.
  const emptyRows = page > 0 ? Math.max(0, (1 + page) * rowsPerPage - rows.length) : 0;

  const visibleRows = React.useMemo(
    () => stableSort(rows, getComparator(order, orderBy)).slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage),
    [order, orderBy, page, rowsPerPage],
  );

  const initializeUserData = () => {
    getUsers(page + 1, auth.user.clinic_id, '', auth.user?.role_name, orderBy, order).then((r) => {
      setUserList(r.list);
      setListCount(r.totalCount);
    });
  };

  useEffect(() => {
    initializeUserData();
  }, [order, orderBy, props.lastUpdate]);

  const handleDeleteAccount = async () => {
    try {
      await deleteInvitedUser(selectedRow.id, { clinic_id: auth.user.clinic_id });
      initializeUserData();
      dispatch(
        enqueueSnackbarAction({
          message: 'User deleted successfully',
          variant: 'success',
        }),
      );
    } catch (e) {
      dispatch(
        enqueueSnackbarAction({
          message: 'Error: Could not delete user',
          key: new Date().getTime() + Math.random(),
          variant: 'error',
        }),
      );
    } finally {
      setOpen(false);
    }
  };

  return (
    <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
      <AlertDialogSlide
        open={open}
        handleCancel={() => setOpen(false)}
        handleOk={handleDeleteAccount}
        title="Delete Account"
        content="Are you sure you want to delete this user account?"
      />
      <Paper sx={{ width: '100%', mb: 2, boxShadow: 'none' }}>
        {/* <EnhancedTableToolbar numSelected={selected.length} /> */}
        <TableContainer>
          <Table
            // sx={{  [`& .${tableCellClasses.root}`]: {
            //     borderBottom: "none"
            //   } }}
            aria-labelledby="tableTitle"
            size={dense ? 'small' : 'medium'}
            className="task-table"
          >
            <EnhancedTableHead
              numSelected={selected.length}
              order={order}
              orderBy={orderBy}
              onSelectAllClick={handleSelectAllClick}
              onRequestSort={handleRequestSort}
              rowCount={userList.length}
            />
            <TableBody>
              {userList?.length !== 0 ? (
                userList.map((row, index) => {
                  const isItemSelected = isSelected(row.id);
                  const labelId = `enhanced-table-checkbox-${index}`;

                  return (
                    <TableRow
                      hover
                      onClick={(event) => handleClick(event, row.id)}
                      role="checkbox"
                      aria-checked={isItemSelected}
                      tabIndex={-1}
                      key={row.id}
                      selected={isItemSelected}
                      sx={{
                        cursor: 'pointer',
                        border: 0,
                        borderBottom: 'none',
                        [`& .${tableCellClasses.root}`]: {
                          fontFamily: 'UrbanistMedium',
                        },
                      }}
                    >
                      {/* <TableCell padding="checkbox" className='table-cell'> */}
                      {/*  <Checkbox */}
                      {/*    color="primary" */}
                      {/*    size='small' */}
                      {/*    checked={isItemSelected} */}
                      {/*    inputProps={{ */}
                      {/*      'aria-labelledby': labelId, */}
                      {/*    }} */}
                      {/*  /> */}
                      {/* </TableCell> */}
                      <TableCell component="th" id={labelId} scope="row" className="table-cell" sx={{ fontSize: '12px' }}>
                        {`${row.first_name} ${row.last_name}`}
                      </TableCell>
                      <TableCell className="table-cell" align="left" sx={{ fontSize: '12px' }}>
                        {row.email}
                      </TableCell>
                      {/* <TableCell className="table-cell" align="left" sx={{ fontSize: '12px' }}>
                      {row.reg_gmc_no}
                    </TableCell> */}
                      <TableCell className="table-cell" align="left" sx={{ fontSize: '12px' }}>
                        {row?.role?.name}
                      </TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          sx={buttonHeadingStyles}
                          startIcon={<DeleteIcon />}
                          onClick={() => {
                            setOpen(true);
                            setSelectedRow(row);
                          }}
                        />
                      </TableCell>
                    </TableRow>
                  );
                })
              ) : (
                <NoRecordsRow />
              )}
              {/* {userList?.length < 9 && ( */}
              {/*  <TableRow */}
              {/*    style={{ */}
              {/*      height: (dense ? 33 : 53) * (9 - userList?.length), */}
              {/*    }} */}
              {/*  /> */}
              {/* )} */}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
      {listCount > rowsPerPage && (
        <Pagination
          shape="rounded"
          page={page + 1}
          count={Math.ceil(listCount / rowsPerPage, 10) || 0}
          onChange={handleChangePage}
          sx={{ display: 'flex', flexDirection: 'row-reverse', padding: '1rem' }}
        />
      )}
    </Box>
  );
}
