// @ts-nocheck
import * as React from 'react';
import { useEffect, useState } from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { Box, Input, MenuItem, Select, Stack, InputAdornment, TextField } from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import EmailIcon from '@mui/icons-material/Email';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import Typography from '@mui/material/Typography';
import { useDispatch, useSelector } from 'react-redux';
import { useFormik, FieldArray, FormikProvider } from 'formik';
import Grid from '@mui/material/Grid';
import { Delete } from '@mui/icons-material';
import PersonAddAltIcon from '@mui/icons-material/PersonAddAlt';
import { getRolesList } from '../../../services/permissions';
import IRootState from '../../../models/IRootState';
import { sendInvitation } from '../../../services/users';
import { enqueueSnackbarAction } from '../../../actions/app.action';
import signupValidations from '../../../helpers/schema/signupValidations';
import { planPriceSelector, planTypeSelector, userSelector } from '../../../selectors/auth.selector';

interface InviteUserDialogProps {
  open: boolean;
  handleClose: (event?: {}, reason?: 'backdropClick' | 'escapeKeyDown') => void;
}

function getSelectedPermissionIds(permissions: any) {
  return permissions
    .filter((permission) => permission.isSelected === 1) // Filter for selected permissions
    .map((permission) => permission.id); // Map to the id
}

const mapPriceToDiscountByPlan = {
  core: {
    monthly: 9.80,
    yearly: 58.50,
  },
  pro: {
    monthly: 24.50,
    yearly: 234,
  },
};

export default function InviteUserDialog({ open, handleClose }: InviteUserDialogProps) {
  const auth = useSelector((state: IRootState) => state.auth);
  const [roleList, setRoleList] = React.useState([]);
  const planPrice = useSelector(planPriceSelector);
  const loggedInUser = useSelector(userSelector);

  const dispatch = useDispatch();

  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const roles = await getRolesList(auth.user.clinic_id);
        setRoleList(roles);
      } catch (e) {
        console.log(e);
      }
    };

    if (open) {
      fetchRoles();
    } else {
      formik.resetForm(); // Reset the form when the dialog is closed
    }
  }, [open, auth]);

  const formik = useFormik({
    initialValues: {
      users: [{ id: Date.now(), firstName: '', lastName: '', email: '', role: '' }],
    },
    validateOnBlur: true,
    validateOnChange: true, // Avoid excessive validations
    validationSchema: signupValidations.inviteUser,
    onSubmit: (values) => {
      let mappedUsers = [];
      if (values.users) {
        mappedUsers = values.users.map((user) => ({
          first_name: user.firstName,
          last_name: user.lastName,
          email: user.email,
          role_id: user.role,
          permissions: getSelectedPermissionIds(roleList.find((r) => r.role_id === parseInt(user.role, 10))?.permissions),
        }));
      }
      const userCount = mappedUsers.length;
      // const costPerUser = planPrice;
      const costPerUser = mapPriceToDiscountByPlan[loggedInUser?.subscriptionPlan?.name?.toLowerCase()][loggedInUser?.subscriptionPlan?.plan_type?.toLowerCase()];
      const totalCost = userCount > 1 ? userCount * costPerUser : costPerUser;

      const payload = {
        users: mappedUsers,
        client_id: auth.user?.clinic_id,
      };
      sendInvitation(payload)
        .then((e) => {

          const message =
            userCount === 1
              ? `The team member has been successfully added. An additional amount of $${totalCost} will be charged.`
              : `${userCount} team members have been successfully added. An additional amount of $${totalCost} will be charged.`;

          dispatch(
            enqueueSnackbarAction({
              message: message,
              key: new Date().getTime() + Math.random(),
              variant: 'success',
            }),
          );
        })
        .catch((e) => {
          dispatch(
            enqueueSnackbarAction({
              message: `Error: ${e?.response?.data?.msg}`,
              key: new Date().getTime() + Math.random(),
              variant: 'error',
            }),
          );
        });

      handleClose();
    },
  });

  return (
    <Dialog
      sx={(theme) => ({})}
      open={open}
      onClose={(event, reason) => {
        if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
          handleClose(event, reason); // only close if reason is not backdrop click or escape key
        }
      }}
      PaperProps={{
        component: 'form',
        onSubmit: formik.handleSubmit,
      }}
      maxWidth="md"
    >
      {/* Dynamic Rows */}
      <FormikProvider value={formik}>
        <DialogTitle
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            pr: 2,
            pb: 0,
          }}
        >
          <Typography variant="h6" fontWeight="600" fontSize="16px">
            Invite User
          </Typography>
          <IconButton onClick={handleClose} size="small" sx={{ color: 'grey.500' }}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent
          sx={(theme) => ({
            backgroundColor: theme?.customProperties.paginationDiv,
          })}
        >
          <Typography
            variant="body2"
            sx={{
              color: 'text.secondary',
              mb: 2,
            }}
          >
            Create a new user role by selecting the following permissions
          </Typography>
          {/* Headers */}
          <Grid container spacing={2} style={{ marginBottom: '16px', marginTop: '10px' }} />
          <FieldArray name="users">
            {({ push, remove }) => (
              <>
                {formik.values.users.map((row, index) => (
                  <Box
                    key={row.id}
                    sx={{
                      display: 'flex',
                      flexDirection: { xs: 'column', sm: 'row' }, 
                      gap: 2,
                      mb: 2,
                      alignItems: 'center',
                      border: '1px solid #d9d2d4',
                      borderRadius: '10px',
                      padding: 2,
                    }}
                  >
                    {/* First Name with Icon */}
                    <TextField
                      fullWidth
                      variant="outlined"
                      placeholder="First Name"
                      name={`users[${index}].firstName`}
                      value={formik.values.users[index].firstName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon sx={{ color: 'text.secondary' }} />
                          </InputAdornment>
                        ),
                        sx: {
                          backgroundColor: '#f9f9f9',
                          borderRadius: '10px',
                        },
                      }}
                      sx={{
                        flex: 1,
                        minWidth: '160px',
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: 'none',
                        },
                      }}
                    />

                    {/* Last Name */}
                    <TextField
                      fullWidth
                      variant="outlined"
                      placeholder="Last Name"
                      name={`users[${index}].lastName`}
                      value={formik.values.users[index].lastName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      InputProps={{
                        sx: {
                          backgroundColor: '#f9f9f9',
                          borderRadius: '10px',
                        },
                      }}
                      sx={{
                        flex: 1,
                        minWidth: '160px',
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: 'none',
                        },
                      }}
                    />

                    {/* Email with Icon */}
                    <TextField
                      fullWidth
                      variant="outlined"
                      placeholder="Email"
                      name={`users[${index}].email`}
                      value={formik.values.users[index].email}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <EmailIcon sx={{ color: 'text.secondary' }} />
                          </InputAdornment>
                        ),
                        sx: {
                          backgroundColor: '#f9f9f9',
                          borderRadius: '10px',
                        },
                      }}
                      sx={{
                        flex: 1,
                        minWidth: '160px',
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: 'none',
                        },
                      }}
                    />

                    {/* Role Select */}
                    <Select
                      fullWidth
                      displayEmpty
                      variant="outlined"
                      name={`users[${index}].role`}
                      value={formik.values.users[index].role}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      sx={{
                        flex: 1,
                        minWidth: '160px',
                        backgroundColor: '#f9f9f9',
                        borderRadius: '10px',
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: 'none',
                        },
                      }}
                    >
                      <MenuItem value="">
                        <em>Select role</em>
                      </MenuItem>
                      {roleList.map((role) => (
                        <MenuItem key={role.role_id} value={role.role_id}>
                          {role.name}
                        </MenuItem>
                      ))}
                    </Select>

                    {/* Delete Button */}
                    {formik.values.users.length > 1 && (
                      <IconButton
                        onClick={() => remove(index)}
                        sx={{
                          ml: 1,
                          backgroundColor: '#f9f9f9',
                          borderRadius: '10px',
                          padding: '16px',
                          '&:hover': {
                            backgroundColor: '#f0f0f0', // optional subtle hover effect
                          },
                        }}
                      >
                        <Delete sx={{ color: 'text.secondary' }} />
                      </IconButton>
                    )}
                  </Box>
                ))}

                <Button
                  fullWidth
                  variant="outlined"
                  endIcon={<PersonAddAltIcon />} // move icon to the right
                  onClick={() => push({ id: Date.now(), firstName: '', lastName: '', email: '', role: '' })}
                  sx={{
                    mt: 2,
                    border: '1px solid #d9d2d4',
                    borderRadius: '10px',
                    padding: '16px',
                    justifyContent: 'space-between',
                    color: 'text.primary',
                    textTransform: 'none', // keep text casing as is
                    backgroundColor: '#fff',
                    '&:hover': {
                      backgroundColor: '#f5f5f5',
                      border: '1px solid #d9d2d4',
                    },
                  }}
                >
                  Add New
                </Button>
              </>
            )}
          </FieldArray>
        </DialogContent>
        <Stack
          sx={(theme) => ({
            backgroundColor: theme?.customProperties.paginationDiv,
            padding: '24px',
            display: 'flex',
            justifyContent: 'right',
            flexDirection: 'row',
            gap: '5px',
            borderTop: '1px solid #E6E6E6',
          })}
        >
          <Button
            type="submit"
            className="invite-user-button"
            endIcon={<PersonAddAltIcon />}
            disabled={!formik.isValid || formik.values.users.length === 0}
          >
            Send Invites
          </Button>
        </Stack>
      </FormikProvider>
    </Dialog>
  );
}
