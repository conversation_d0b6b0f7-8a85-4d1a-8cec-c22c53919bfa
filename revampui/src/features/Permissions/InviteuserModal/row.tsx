import React, { useState } from 'react';
import { Button, IconButton, Input, MenuItem, Select, Grid, Typography } from '@mui/material';
import { Delete, Add } from '@mui/icons-material';

interface FormRow {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  type: string;
}

interface Props {
  updateInviteList: (rows: FormRow[]) => void;
}

export default function InviteUserRow({ updateInviteList }: Props) {
  const [rows, setRows] = useState<FormRow[]>([{ id: 1, firstName: '', lastName: '', email: '', type: '' }]);

  const addRow = () => {
    const newRow: FormRow = {
      id: Date.now(),
      firstName: '',
      lastName: '',
      email: '',
      type: '',
    };
    setRows([...rows, newRow]);
    updateInviteList([...rows, newRow]);
  };

  const removeRow = (id: number) => {
    setRows(rows.filter((row) => row.id !== id));
    updateInviteList(rows.filter((row) => row.id !== id));
  };

  const updateRow = (id: number, field: keyof FormRow, value: string) => {
    setRows(rows.map((row) => (row.id === id ? { ...row, [field]: value } : row)));
    updateInviteList(rows.map((row) => (row.id === id ? { ...row, [field]: value } : row)));
  };

  return (
    <div style={{ padding: '24px', backgroundColor: 'white', borderRadius: '8px', maxWidth: '800px', margin: 'auto' }}>
      {/* Headers */}
      <Grid container spacing={2} style={{ marginBottom: '16px' }}>
        <Grid item xs={3}>
          <Typography variant="body2">First Name</Typography>
        </Grid>
        <Grid item xs={3}>
          <Typography variant="body2">Last Name</Typography>
        </Grid>
        <Grid item xs={3}>
          <Typography variant="body2">Email</Typography>
        </Grid>
        <Grid item xs={3}>
          <Typography variant="body2">Type</Typography>
        </Grid>
      </Grid>

      {/* Dynamic Rows */}
      {rows.map((row) => (
        <Grid container spacing={2} key={row.id} style={{ marginBottom: '16px' }}>
          <Grid item xs={3}>
            <Input
              fullWidth
              style={{ borderColor: 'white' }}
              value={row.firstName}
              onChange={(e) => updateRow(row.id, 'firstName', e.target.value)}
            />
          </Grid>
          <Grid item xs={3}>
            <Input
              fullWidth
              style={{ borderColor: 'white' }}
              value={row.lastName}
              onChange={(e) => updateRow(row.id, 'lastName', e.target.value)}
            />
          </Grid>
          <Grid item xs={3}>
            <Input
              fullWidth
              style={{ borderColor: 'white' }}
              value={row.email}
              onChange={(e) => updateRow(row.id, 'email', e.target.value)}
            />
          </Grid>
          <Grid item xs={3} style={{ display: 'flex', alignItems: 'center' }}>
            <Select
              fullWidth
              value={row.type}
              onChange={(e) => updateRow(row.id, 'type', e.target.value)}
              displayEmpty
              style={{ borderColor: 'white' }}
            >
              <MenuItem value="">
                <em>Select type</em>
              </MenuItem>
              <MenuItem value="2">Clinician</MenuItem>
              <MenuItem value="3">Staff Member</MenuItem>
            </Select>
            <IconButton aria-label="delete" onClick={() => removeRow(row.id)} style={{ marginLeft: '8px' }}>
              <Delete />
            </IconButton>
          </Grid>
        </Grid>
      ))}

      {/* Add Row Button */}
      <Button variant="outlined" startIcon={<Add />} style={{ borderColor: 'white', marginTop: '16px' }} onClick={addRow}>
        Add Row
      </Button>
    </div>
  );
}
