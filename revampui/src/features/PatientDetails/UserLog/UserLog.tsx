import React, { useEffect, useState, CSSProperties } from 'react';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import {
  Box,
  Typography,
  TextField,
  List,
  ListItem,
  ListItemText,
  Divider,
  Card,
  CardContent,
  useTheme,
  Grid,
  Avatar,
} from '@mui/material';
import { IconButton, InputAdornment } from '@material-ui/core';
import { format } from 'date-fns';
// eslint-disable-next-line import/no-extraneous-dependencies
import { styled } from '@mui/system';
// eslint-disable-next-line import/no-extraneous-dependencies
import DatePicker from 'react-datepicker';
// eslint-disable-next-line import/no-extraneous-dependencies
import 'react-datepicker/dist/react-datepicker.css';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import CardHeader from '@material-ui/core/CardHeader';
import { Visibility, VisibilityOff } from '@material-ui/icons';
import SearchIcon from '@mui/icons-material/Search';
import _ from 'lodash';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import UserLogService from '../../../services/userLogService';
import { clinicIdSelector } from '../../../selectors/auth.selector';
import {
  currentUserLogItemSelector,
  userLogEndDateSelector,
  userLogListSelector,
  userLogSearchStrSelector,
  userLogStartDateSelector,
} from '../../../selectors/userLog.selector';
import {
  setCurrentUserLogItemInStore,
  setUserLogEndDateInStore,
  setUserLogInStore,
  setUserLogSearchStrInStore,
  setUserLogStartDateInStore,
} from '../../../actions/userLog';
import CalenderIcon from '../../../themes/icons/CalenderIcon';
import ClockIcon from '../../../themes/icons/ClockIcon';
import { originalPatientMedicalHistorySelector } from '../../../selectors/clinicalLetter.selector';
import { IUserLogActionTypes } from '../../../models/IUserLogState';
import NoRecordsMessage from '../../../components/molecules/NoRecordsMessage/NoRecordsMessage';

const Container = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'row',
  gap: theme.spacing(2),
  width: '100%',
}));

const LeftPanel = styled(Box)(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.palette.background.paper,
  overflow: 'hidden',
  borderRadius: theme.shape.borderRadius,
  maxHeight: 'calc(100vh - 100px)',
  // boxShadow: theme.shadows[1],
}));

const RightPanel = styled(Box)(({ theme }) => ({
  flex: 2,
  maxHeight: 'calc(100vh - 100px)',
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
  padding: theme.spacing(2),
  paddingTop: 0,
  borderRadius: theme.shape.borderRadius,
  // boxShadow: theme.shadows[1],
  backgroundColor: theme.palette.background.paper,
}));

function mapUserLogEntries(changes: any) {
  const transformedObject: Record<string, any> = {};

  const mappings = [
    { source: 'healthManagement.carePlan', key: 'healthManagement.carePlan' },
    { source: 'healthManagement.recommendations', key: 'healthManagement.recommendations' },
    { source: 'otherInformation', key: 'otherInformation' },
    { source: 'presentingProblem.problemList', key: 'presentingProblem.problemList' },
    { source: 'vitalsAndObservations.vitals', key: 'vitalsAndObservations.vitals' },
    { source: 'vitalsAndObservations.observations', key: 'vitalsAndObservations.observations' },
    { source: 'familyAndSocialHistory.familyHistory', key: 'familyAndSocialHistory.familyHistory' },
    { source: 'familyAndSocialHistory.socialHistory', key: 'familyAndSocialHistory.socialHistory' },
    { source: 'medicationsAndAllergies.allergies', key: 'medicationsAndAllergies.allergies' },
    { source: 'medicationsAndAllergies.pastMedications', key: 'medicationsAndAllergies.pastMedications' },
    { source: 'medicationsAndAllergies.currentMedications', key: 'medicationsAndAllergies.currentMedications' },
    { source: 'pastMedicalAndSurgicalHistory.surgicalHistory', key: 'pastMedicalAndSurgicalHistory.surgicalHistory' },
    { source: 'pastMedicalAndSurgicalHistory.pastMedicalHistory', key: 'pastMedicalAndSurgicalHistory.pastMedicalHistory' },
    { source: 'labResultsAndDiagnosticImaging.labResults', key: 'labResultsAndDiagnosticImaging.labResults' },
    { source: 'labResultsAndDiagnosticImaging.diagnosticImaging', key: 'labResultsAndDiagnosticImaging.diagnosticImaging' },
  ];

  // Transform Object A based on the mappings
  mappings.forEach(({ source, key }) => {
    const value = source.split('.').reduce((acc, curr) => acc?.[curr], changes);
    if (value !== undefined) {
      transformedObject[key] = {
        new: value,
        old: '', // Replace this with actual old data if available
      };
    }
  });

  return transformedObject;
}

const PatientList = () => {
  const userLog = useSelector(userLogListSelector);
  const { list } = userLog;
  const dispatch = useDispatch();
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);

  const handleOnClick = (currentItem: any, index: number) => {
    setSelectedIndex(index);
    dispatch(setCurrentUserLogItemInStore(currentItem));
  };

  useEffect(() => {
    if (list?.length > 0) {
      setSelectedIndex(0);
      dispatch(setCurrentUserLogItemInStore(list[0]));
    }
  }, [list, dispatch]);

  return (
    <List
      sx={{
        height: '710px',
        overflowY: 'scroll',
        padding: '0px 10px 16px 0px',
        margin: '10px 0px 10px 0px',
      }}
    >
      {list?.map((item: any, index: number) => (
        <ListItem
          key={index}
          button
          selected={index === selectedIndex}
          onClick={() => handleOnClick(item, index)}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderRadius: '10px',
            marginBottom: '10px',
            padding: '20px 10px',
            backgroundColor: index === selectedIndex ? '#f9f9f9 !important' : '#ffffff !important',
            color: index === selectedIndex ? '#2D2869' : 'inherit',
            '&:hover': {
              backgroundColor: '#f9f9f9 !important',
            },
          }}
        >
          <Grid container alignItems="center">
            {/* Left Column - Avatar & Name */}
            <Grid item xs={7} display="flex" alignItems="center">
              <Avatar src={item?.users?.profileS3Url} sx={{ marginRight: '10px' }} />
              <Typography
                fontWeight="bold"
                sx={{ fontSize: '14px' }}
              >{`${item.users.first_name} ${item.users.last_name}`}
              </Typography>
            </Grid>

            {/* Right Column - Date & Time */}
            <Grid item xs={5} textAlign="right">
              <Box display="flex" alignItems="center" justifyContent="flex-end" sx={{ marginBottom: '6px' }}>
                <Typography fontSize="14px">{format(new Date(item.created_at), 'dd MMM, yyyy')}</Typography>
                <CalendarTodayIcon sx={{ fontSize: '16px', marginLeft: '10px' }} />
              </Box>
              <Box display="flex" alignItems="center" justifyContent="flex-end">
                <Typography fontSize="14px">{format(new Date(item.created_at), 'hh:mm a')}</Typography>
                <AccessTimeIcon sx={{ fontSize: '16px', marginLeft: '10px' }} />
              </Box>
            </Grid>
          </Grid>
        </ListItem>
      ))}
    </List>
  );
};

function capitalizeKey(key: string) {
  return key
    .split('.') // Split on dots to handle nested keys
    .map(
      (part: string) =>
        part
          .replace(/([A-Z])/g, ' $1') // Add space before capital letters
          .replace(/^./, (str) => str.toUpperCase()), // Capitalize first letter
    )
    .join(' | ');
}

const RightPanelContent = () => {
  const dispatch = useDispatch();
  const currentItem = useSelector(currentUserLogItemSelector);
  const searchStr = useSelector(userLogSearchStrSelector);
  let changes = currentItem?.changes;
  const userLog = useSelector(userLogListSelector);
  const { list } = userLog;

  if (currentItem.operation === 'CREATE') {
    changes = mapUserLogEntries(changes);
  }

  const renderChanges = (changesObj: any = {}, type: string = '') => {
    return Object.keys(changesObj).map((key: any) => {
      const changes = changesObj[key]?.[type];
      if (!changes || changes === '<ul></ul>') return null;
      return (
        <Box
          key={key}
          display="flex"
          flexDirection="column"
          gap={1}
          sx={{
         backgroundColor: theme.palette.mode === 'dark' ? '#333' : '#f9f9f9',
          padding: '16px',
          borderRadius: '10px',
          boxShadow: '0px 2px 4px rgba(0,0,0,0.1)',
          margin: '16px 0',
        }}
          className="ql-box"
        >
          {/* @ts-ignore */}
          <CardHeader
            title={
              <Typography
                variant="h4"
                sx={{
                  color: theme.palette.mode === 'dark' ? '#fff' : '#000',
                  fontWeight: 'bold',
                  marginBottom: '8px',
                  fontSize: '14px',
                }}
              >
                {capitalizeKey(key)}
              </Typography>
            }
            style={{
              paddingBottom: 0,
            }}
          />
          <CardContent sx={{ paddingTop: 0, paddingBottom: '0 !important' }}>{renderHtmlString(changes)}</CardContent>
        </Box>
      );
    });
  };

  const renderHtmlString = (htmlString: string) => {
    const tempElement = document.createElement('div');
    tempElement.innerHTML = htmlString;
    return (
      <>
        <ul>
          {Array.from(tempElement.querySelectorAll('li')).map((el, index) => (
            <li style={{ fontFamily: 'UrbanistMedium', fontSize: '14px' }} key={index}>
              {el.textContent}
            </li>
          ))}
        </ul>
        <>
          {Array.from(tempElement.querySelectorAll('p')).map((el, index) => (
            <p style={{ fontFamily: 'UrbanistMedium', fontSize: '14px', paddingLeft: '20px' }} key={index}>
              {el.textContent}
            </p>
          ))}
        </>
      </>
    );
  };

  const theme = useTheme();
  return (
    <>
      <Grid item xs={12} sx={{ marginTop: 2 }}>
        <TextField
          placeholder="Search"
          variant="outlined"
          fullWidth
          value={searchStr}
          onChange={(e) => dispatch(setUserLogSearchStrInStore(e.target.value))}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            sx: {
              borderRadius: '10px',
              background: theme.palette.mode === 'dark' ? '#000000' : '#f9f9f9',
              flexBasis: 'unset !important',
            },
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: '10px',
            },
          }}
        />
      </Grid>
      {!_.isEmpty(list) && (
        <Box sx={{ overflowY: 'scroll', paddingRight: '5px' }}>
          <Grid container spacing={2}>
            {/* "Now" Section */}
            <Grid item xs={12} md={6}>
              <Typography variant="h4" sx={{ fontSize: '20px' }}>
                Now
              </Typography>
              <Card variant="outlined" sx={{ marginTop: 2, border: 'none' }}>
                {renderChanges(changes, 'new')}
              </Card>
            </Grid>

            {/* "Before" Section (Only visible if operation is UPDATE) */}
            {currentItem.operation === 'UPDATE' && (
              <Grid item xs={12} md={6}>
                <Typography variant="h4" sx={{ fontSize: '20px' }}>
                  Before
                </Typography>
                <Card variant="outlined" sx={{ marginTop: 2, borderRadius: '26px' }}>
                  {renderChanges(changes, 'old')}
                </Card>
              </Grid>
            )}
          </Grid>
        </Box>
      )}
    </>
  );
};

const LeftPanelContent = () => {
  const dispatch = useDispatch();
  const startDate = useSelector(userLogStartDateSelector);
  const endDate = useSelector(userLogEndDateSelector);

  const userLog = useSelector(userLogListSelector);
  const { list } = userLog;

  const handleStartDateChange = (date: Date | null) => {
    if (date) {
      dispatch(setUserLogStartDateInStore(date!));
    }
  };

  const handleEndDateChange = (date: Date | null) => {
    dispatch(setUserLogEndDateInStore(date!));
  };

  const styles: { [key: string]: CSSProperties } = {
    container: {
      display: 'flex',
      alignItems: 'center',
      border: '1px solid #ccc',
      borderRadius: '5px',
      padding: '5px 10px',
      width: '360px',
      justifyContent: 'space-between',
    },
    datePickerContainer: {
      flex: 1,
    },
    input: {
      width: '100%',
      border: 'none',
      outline: 'none',
      padding: '5px',
      fontSize: '14px',
      borderRadius: '5px',
      backgroundColor: 'transparent',
      textAlign: 'center',
    },
    disabledInput: {
      color: 'red',
    },
    arrow: {
      fontSize: '18px',
      margin: '0 10px',
      color: '#888',
    },
  };
  return (
    <Box p={2} display="flex" flexDirection="column">
      <Box mt={2}>{!_.isEmpty(list) && <PatientList />}</Box>
    </Box>
  );
};

const UserLog = () => {
  const clinicId = useSelector(clinicIdSelector);
  const searchStr = useSelector(userLogSearchStrSelector);
  const startDate = useSelector(userLogStartDateSelector);
  const endDate = useSelector(userLogEndDateSelector);
  const originalMedicalHistory = useSelector(originalPatientMedicalHistorySelector);
  const location = useLocation<any>();
  const dispatch = useDispatch();
  const patientId = location.state.userId;
  const userLog = useSelector(userLogListSelector);
  const { list } = userLog;

  useEffect(() => {
    return () => {
      dispatch(setCurrentUserLogItemInStore({}));
      dispatch({
        type: IUserLogActionTypes.USER_LOG_SUCCESS,
        payload: [],
      });
      dispatch(setUserLogStartDateInStore(undefined));
      dispatch(setUserLogEndDateInStore(undefined));
      dispatch(setUserLogSearchStrInStore(''));
    };
  }, []);

  useEffect(() => {
    if (!clinicId || !patientId) return;

    if (searchStr.length > 2) {
      // Fetch filtered data when `searchStr` is longer than 3 characters
      dispatch(setUserLogInStore(clinicId, patientId, 1, searchStr, startDate, endDate));
    } else if (searchStr.length < 3) {
      // Reset to default state when `searchStr` is short
      dispatch(setUserLogInStore(clinicId, patientId, 1, '', startDate, endDate));
    }
  }, [clinicId, patientId, searchStr, dispatch, startDate, endDate, originalMedicalHistory]);
  const theme = useTheme();

  return (
    <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 2, padding: 2 }}>
      {/* Date Picker Section (Moved to the Top) */}
      <Box
        sx={(theme) => ({
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start', // Aligns text and inputs properly
          flexWrap: 'wrap',
          width: '100%',
          backgroundColor: theme.palette.mode === 'dark' ? '#222' : '#fff', // Dynamic background
          padding: '20px',
          borderRadius: '8px',
          boxShadow: '0px 2px 4px rgba(0,0,0,0.1)',
        })}
      >
        {/* Left Side: Title & "From" Text */}
        <Box display="flex" flexDirection="column" alignItems="center">
          <Typography
            variant="h6"
            fontWeight="600"
            sx={(theme) => ({
              color: theme.palette.mode === 'dark' ? '#fff' : '#000',
              fontSize: '16px',
              marginBottom: '10px',
            })}
          >
            Date
          </Typography>
          <Typography variant="body2" sx={{ color: '#b0b0b0', marginTop: '4px' }}>
            From
          </Typography>
        </Box>

        {/* Right Side: Date Inputs */}
        <Box display="flex" alignItems="center" gap="12px">
          <DatePicker
            selected={startDate}
            onChange={(date) => dispatch(setUserLogStartDateInStore(date))}
            selectsStart
            startDate={startDate}
            endDate={endDate}
            placeholderText="Start Date"
            dateFormat="dd/MM/yyyy"
            maxDate={new Date()}
            customInput={
              <input
                style={{
                  padding: '8px',
                  borderRadius: '8px',
                  border: '1px solid #ccc',
                  backgroundColor: theme.palette.mode === 'dark' ? '#333' : '#f9f9f9', // Dynamic background
                  color: theme.palette.mode === 'dark' ? '#fff' : '#000',
                  width: '140px',
                  textAlign: 'center',
                }}
              />
            }
          />

          <DatePicker
            selected={endDate}
            onChange={(date) => dispatch(setUserLogEndDateInStore(date))}
            selectsEnd
            startDate={startDate}
            endDate={endDate}
            placeholderText="End Date"
            dateFormat="dd/MM/yyyy"
            maxDate={new Date()}
            customInput={
              <input
                style={{
                  padding: '8px',
                  borderRadius: '8px',
                  border: '1px solid #ccc',
                  backgroundColor: theme.palette.mode === 'dark' ? '#333' : '#f9f9f9', // Dynamic background
                  color: theme.palette.mode === 'dark' ? '#fff' : '#000',
                  width: '140px',
                  textAlign: 'center',
                }}
              />
            }
          />
        </Box>
      </Box>
      <Container>
        <LeftPanel>
          <LeftPanelContent />
        </LeftPanel>
        <RightPanel>
          <RightPanelContent />
        </RightPanel>
      </Container>
      {_.isEmpty(list) && (
        <Box display="flex" justifyContent="center" alignItems="center">
          <NoRecordsMessage />
        </Box>
      )}
    </Box>
  );
};

export default UserLog;
