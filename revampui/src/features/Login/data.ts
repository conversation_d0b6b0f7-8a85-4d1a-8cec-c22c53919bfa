import { IFromFieldsType } from '../../models/IFormFields';

const loginFields = [
  { id: 'email', name: 'email', placeholder: 'Email*', type: IFromFieldsType.TEXT },
  { id: 'password', name: 'password', placeholder: 'Password*', type: IFromFieldsType.PASSWORD },
];

const rememberMeField = [
  {
    id: 'rememberMe',
    name: 'rememberMe',
    placeholder: 'Remember me',
    type: IFromFieldsType.SWITCH,
  },
];

const forgotPasswordFields = [{ id: 'email', name: 'email', placeholder: 'Email Address*', type: IFromFieldsType.TEXT }];

const formFields = { loginFields, rememberMeField, forgotPasswordFields };
export default formFields;
