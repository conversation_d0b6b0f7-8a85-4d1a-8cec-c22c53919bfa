import { Theme, makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles((theme: Theme) => ({
  root: {
    backgroundImage: 'url("assets/images/onboarding-background.png")',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100vh',
  },
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'left',
    justifyContent: 'center',
  },
  card: {
    borderRadius: '24px',
    width: '100%',
    [theme.breakpoints.up('sm')]: {
      width: 'auto', // Set to auto for larger screens
    },
    padding: '20px 30px 20px 30px !important',
    // padding: '80px 90px 80px 90px !important',
  },
  button: {
    marginTop: theme.spacing(1.75),
    marginBottom: theme.spacing(1.75),
    borderRadius: '10px',
    padding: '18px 32px 18px 32px',
    textTransform: 'none',
    fontSize: '14px',
    fontWeight: 500,
    lineHeight: '17.64px',
    boxShadow: 'none',
  },

  buttonOtp: {
    borderRadius: '100px',
    padding: '16px 32px 16px 32px',
    textTransform: 'none',
    fontSize: '14px',
    fontWeight: 500,
    lineHeight: '17.64px',
    boxShadow: 'none',
  },
  headingMargin: {
    marginBottom: theme.spacing(1.5),
  },
  formMargin: {
    marginBottom: theme.spacing(2),
    marginTop: theme.spacing(3),
  },
  formMarginOtp: {
    marginBottom: theme.spacing(2),
    marginTop: theme.spacing(0),
  },
  subHeading: {
    marginBottom: theme.spacing(1.5),
    color: '#6A797E',
  },
  outlinedInputRoot: {
      
    '& .MuiOutlinedInput-root': {
      background: '#f9f9f9',
      '& fieldset': {
        borderRadius: '10px',
      },
    },
    '& .MuiOutlinedInput-input': {
      fontWeight: 500,
      fontSize: 14,
      borderRadius: '10px',
      padding: '10.5px 14px',
      width: '100%',
    },
    '& svg': {
    color: 'black',
    fontSize: '16px',
  },
  },
  listHeading: {
    fontFamily: 'UrbanistMedium',
    fontSize: 14,
    fontWeight: 800,
  },
  list: {
    padding: 0,
    color: '#6A797E',
  },
  listItem: {
    padding: 0,
    '&::marker': {
      content: "''",
    },
  },
  listText: {
    margin: 0,
    '& .MuiTypography-body2': {
      fontFamily: 'UrbanistMedium',
      fontWeight: 600,
    },
  },
  errorMessage: {
    color: '#f44336',
    margin: '0px',
    fontSize: '0.75rem',
    fontFamily: 'UrbanistMedium, Helvetica, Arial, sans-serif',
  },
}));

export default useStyles;
