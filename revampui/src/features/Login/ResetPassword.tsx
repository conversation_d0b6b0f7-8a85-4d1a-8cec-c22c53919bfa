import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useFormik } from 'formik';
import { useHistory } from 'react-router-dom';
import {
  Typography,
  Button,
  Grid,
  TextField,
  InputAdornment,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Snackbar,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { Visibility, VisibilityOff } from '@material-ui/icons';

// configs
import { PATH_NAME } from 'configs';

// Styling
import axios, { AxiosError } from 'axios';
import useStyles from './styles';

import validationSchema from '../../helpers/schema/signupValidations';
import defaultValues from '../../helpers/formDefaultValues/signupForm';
import { resetPassword } from '../../actions/auth.action';
import IRootState from '../../models/IRootState';
import OnBoardingLayout from '../../layouts/MainLayout/OnBoardingLayout';

// Type-def
type Severity = 'error' | 'warning' | 'info' | 'success';

const Login = () => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const history = useHistory();
  const snackbarTimeout = 5000;
  const auth = useSelector((state: IRootState) => state.auth);

  const [showPassword, setShowPassword] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<Severity>('success');

  const formik = useFormik({
    initialValues: defaultValues.resetPassword,
    validationSchema: validationSchema.resetPassword,
    onSubmit: async (values) => {
      const submitValues = {
        token: values.token,
        password: values.password,
      };

      try {
        dispatch(resetPassword(submitValues));
      } catch (error: any) {
        console.log(error);
        if (axios.isAxiosError(error)) {
          const msgCode = error.response?.data?.msgCode;

          if (msgCode === 50031) {
            setSnackbarMessage('New password cannot be the same as the old password.');
          } else {
            setSnackbarMessage('Please use another password, or try again later if the problem persists.');
          }

          setSnackbarSeverity('error');
          setOpenSnackbar(true);
        } else {
          // Handle non-Axios errors
          console.error('Unexpected error:', error);
          setSnackbarMessage('An unexpected error occurred. Please try again.');
          setSnackbarSeverity('error');
          setOpenSnackbar(true);
        }
      }
    },
  });

  useEffect(() => {
    let snackbarMessage = '';
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('t');
    if (token) {
      formik.setFieldValue('token', token);
    }

    if (auth.resetSuccess && !auth.resetFailure) {
      snackbarMessage = 'Password has been reset successfully';
      setSnackbarSeverity('success');
      setSnackbarMessage(snackbarMessage);
      setOpenSnackbar(true);
      setTimeout(() => {
        history.push(PATH_NAME.LOGIN); // Redirect to login page
      }, snackbarTimeout);
    } else if (auth.resetFailure && !auth.resetSuccess) {
      snackbarMessage =
        'Unable to reset your password at the moment. Please use another password, or try again later if problem persists.';
      setSnackbarSeverity('error');
      setSnackbarMessage(snackbarMessage);
      setOpenSnackbar(true);
      setTimeout(() => {
        history.push(PATH_NAME.LOGIN); // Redirect to login page
      }, snackbarTimeout);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [auth.resetSuccess, auth.resetFailure]);

  const handleTogglePassword = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };

  const handleCloseSnackbar = () => {
    setOpenSnackbar(false);
  };

  return (
    <OnBoardingLayout
      content={
        <>
          <Typography variant="h4" gutterBottom className={classes.headingMargin}>
            Reset Password
          </Typography>
          <Typography variant="h6" gutterBottom className={classes.subHeading}>
            A password reset link will be sent to your registered email. Please enter your registered email ID
          </Typography>
          <form onSubmit={formik.handleSubmit}>
            <Grid container spacing={1} className={classes.formMargin}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="password"
                  name="password"
                  placeholder="New Password"
                  variant="outlined"
                  type={showPassword ? 'text' : 'password'} // Show password if showPassword is true
                  value={formik.values.password}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.password && Boolean(formik.errors.password)}
                  helperText={formik.touched.password && formik.errors.password}
                  className={classes.outlinedInputRoot}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton onClick={handleTogglePassword} edge="end">
                          {showPassword ? <Visibility /> : <VisibilityOff />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              {/* <Typography className={classes.list}>Cannot be a previously used password</Typography> */}
            </Grid>

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="confirmPassword"
                  name="confirmPassword"
                  placeholder="Confirm New Password"
                  variant="outlined"
                  type={showPassword ? 'text' : 'password'}
                  value={formik.values.confirmPassword}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
                  helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
                  className={classes.outlinedInputRoot}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton onClick={handleTogglePassword} edge="end">
                          {showPassword ? <Visibility /> : <VisibilityOff />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>

              <Grid item xs={12}>
                <Button variant="contained" className={classes.button} type="submit" fullWidth>
                  Reset Password
                </Button>
              </Grid>

              <Grid item xs={12}>
                <Typography className={classes.listHeading} gutterBottom>
                  <strong>Password must contain:</strong>
                </Typography>
                <List dense className={classes.list}>
                  <ListItem className={classes.listItem}>
                    <ListItemText className={classes.listText} primary="Minimum of 10 characters" />
                  </ListItem>
                  <ListItem className={classes.listItem}>
                    <ListItemText className={classes.listText} primary="At least one uppercase letter" />
                  </ListItem>
                  <ListItem className={classes.listItem}>
                    <ListItemText className={classes.listText} primary="At least one special character" />
                  </ListItem>
                </List>
              </Grid>
            </Grid>
          </form>

          <Snackbar
            open={openSnackbar}
            autoHideDuration={snackbarTimeout}
            onClose={handleCloseSnackbar}
            message={snackbarMessage}
            action={
              <IconButton size="small" color="inherit" onClick={handleCloseSnackbar}>
                <VisibilityOff fontSize="small" />
              </IconButton>
            }
          />

          <Snackbar open={openSnackbar} autoHideDuration={snackbarTimeout} onClose={handleCloseSnackbar}>
            <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity}>
              {snackbarMessage}
            </Alert>
          </Snackbar>
        </>
      }
    />
  );
};

export default Login;
