import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { styled } from '@mui/material/styles';
import Switch from '@mui/material/Switch';
import { patientMedicalHistoryCheckboxSelector } from '../../selectors/clinicalLetter.selector';
import { IClinicalLetterActionTypes } from '../../models/IClinicalLetterState';

const StyledSwitch = styled(Switch)(({ theme }) => ({
  padding: 8,
  '& .MuiSwitch-switchBase': {
    '&.Mui-checked': {
      color: '#fff',
      '& + .MuiSwitch-track': {
        backgroundColor: '#000000', // theme.palette.mode === 'dark' ? '#2ECA45' : '#65C466',
        opacity: 1,
        border: 0,
      },
    },
  },
  '&.Mui-focusVisible .MuiSwitch-thumb': {
    color: '#000000',
    border: '6px solid #fff',
  },
  '&.Mui-disabled .MuiSwitch-thumb': {
    color: theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600],
  },
  '&.Mui-disabled + .MuiSwitch-track': {
    opacity: theme.palette.mode === 'light' ? 0.7 : 0.3,
  },
  '& .MuiSwitch-track': {
    borderRadius: 22 / 2,
    '&::before, &::after': {
      content: '""',
      position: 'absolute',
      top: '50%',
      transform: 'translateY(-50%)',
      width: 16,
      height: 16,
    },
    '&::before': {
      left: 12,
    },
    '&::after': {
      right: 12,
    },
  },
  '& .MuiSwitch-thumb': {
    boxShadow: 'none',
    width: 16,
    height: 16,
    margin: 2,
  },
}));

const PatientMedicalHistoryCheckbox = () => {
  // set and get value from redux store
  const dispatch = useDispatch();
  const isChecked = useSelector(patientMedicalHistoryCheckboxSelector);

  const handleOnChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch({ type: IClinicalLetterActionTypes.SET_PATIENT_MEDICAL_HISTORY_CHECKBOX, payload: event.target?.checked });
  };

  return (
    <>
      <StyledSwitch checked={isChecked} onChange={handleOnChange} />
    </>
  );
};

export default PatientMedicalHistoryCheckbox;
