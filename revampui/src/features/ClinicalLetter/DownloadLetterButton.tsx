import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { Button, useTheme } from '@mui/material';

import {
  letterPdfSelector,
  currentLetterSelector,
  isCurrentLetterSavedSelector,
  isViewingLetterSelector,
  pdfDocumentSelector,
  clinicalLetterSelector,
} from 'selectors/clinicalLetter.selector';
import DownloadSvg from '../../themes/svgs/DownloadSvg';
import { downloadPDF } from '../../helpers/templates';

const DownloadLetterButton: React.FC = () => {
  const theme = useTheme();
  const [hovered, setHovered] = useState(false);
  const clinicalLetter = useSelector(currentLetterSelector);
  const isCurrentLetterSaved = useSelector(isCurrentLetterSavedSelector);
  const isViewingClinicalLetter = useSelector(isViewingLetterSelector);
  const pdfDocument = useSelector(pdfDocumentSelector);
  const clinicalLetterStore = useSelector(clinicalLetterSelector);

  const handleDownload = async () => {
    if (clinicalLetter && clinicalLetter.pdf) {
      clinicalLetter.pdf?.save('sample.pdf');
    } else if (clinicalLetter.documentS3Url) {
      await downloadPDF(clinicalLetter.documentS3Url);
    } else {
      const data = await pdfDocument.getData();
      const blob = new Blob([data], { type: 'application/pdf' });

      // Create a URL for the blob and trigger download
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `clinical-letter-${clinicalLetter?.id || 'new'}-${Date.now()}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    }
  };

  return (
    pdfDocument && (
      <Button
        startIcon={<DownloadSvg fill={!isCurrentLetterSaved ? 'gray' : 'black'} />}
        disabled={!isCurrentLetterSaved && !isViewingClinicalLetter}
        className="add-role-button"
        onClick={handleDownload}
        sx={{
          background: 'none',
          '&:hover': { border: 'none', boxShadow: 'none' },
          color: theme?.customProperties.ButtonTextColor,
        }}
      >
        Download
      </Button>
    )
  );
};

export default DownloadLetterButton;
