// @ts-nocheck
/* eslint-disable */
// TODO NEED TO CLEAN FILE AND TS ISSUES
import { Tooltip, Typography, useTheme } from '@mui/material';
import React, { useEffect, useRef, useReducer } from 'react';
import styled from 'styled-components';
import { useSelector, useDispatch, shallowEqual } from 'react-redux';
import { clinicIdSelector, remainingAudioSecondsSelector, userIdSelector, isFreePlanSelector } from '../../selectors/auth.selector';
import NotesService from '../../services/notesService';
import { IAuthActionTypes } from '../../models/IAuthState';
import { IAppActionTypes } from '../../models/IAppState';
import { enqueueSnackbarAction } from '../../actions/app.action';

interface GenerateButtonProps {
  name: string;
  svgPath: React.ElementType | any;
  type?: string;
  disabled?: boolean;
  onClick: (event: any) => void;
  onTimeEnd?: (event: any) => void;
  isActive?: boolean;
  toolTip?: string;
  buttonWithText?: boolean;
}

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

export const GenerateButtons: React.FC<GenerateButtonProps> = ({
  name,
  svgPath: SvgIcon,
  onClick,
  type,
  disabled = false,
  isActive = false,
  buttonWithText = false,
  toolTip = 'button',
  onTimeEnd,
}) => {
  const timeLeft = useSelector(remainingAudioSecondsSelector, shallowEqual);
  const isFreePlan = useSelector(isFreePlanSelector);

  // Instead of state, we use refs to store timer values.
  const elapsedTimeRef = useRef(0);
  const initialTimeLeftRef = useRef(0);
  const isTimerInitialized = useRef(false);

  // We use a reducer solely to force component re-renders when our refs update.
  const [, forceUpdate] = useReducer((x) => x + 1, 0);

  const clinicId = useSelector(clinicIdSelector);
  const userId = useSelector(userIdSelector);
  const dispatch = useDispatch();

  initialTimeLeftRef.current = timeLeft;
  // Initialize timer refs once when the timer becomes active.
  useEffect(() => {
    if (isActive && (name === 'Dictate' || name === 'Transcribe')) {
      if (!isTimerInitialized.current) {
        initialTimeLeftRef.current = timeLeft;
        isTimerInitialized.current = true;
      }
    } else {
      isTimerInitialized.current = false;
    }
  }, [isActive, timeLeft]);

  const updateReduxMinutes = (updateToggle: boolean, Time: number) => ({
    type: IAppActionTypes.UPDATE_REDUX_MINUTES,
    payload: { updateToggle, Time },
  });

  useEffect(() => {
    if (!isActive) {
      const remainingTime = Math.max(initialTimeLeftRef.current - elapsedTimeRef.current, 0);
      if (timeLeft != remainingTime) {
        updateRemainingMinutes(remainingTime);
      }
      dispatch(updateReduxMinutes(true, remainingTime));
    }
  }, [isActive, dispatch]);

  // Timer effect: every second update elapsedTimeRef and trigger a re-render.
  useEffect(() => {
    let interval: NodeJS.Timeout | undefined;
    if (isActive && (name === 'Dictate' || name === 'Transcribe')) {
      interval = setInterval(() => {
        if (elapsedTimeRef.current >= initialTimeLeftRef.current) {
          return;
        }
        elapsedTimeRef.current += 1;
        const remaining = initialTimeLeftRef.current - elapsedTimeRef.current;
        if (remaining <= 0 && onTimeEnd) {
          dispatch(
            enqueueSnackbarAction({
              // @ts-ignore
              message: 'Audio Minutes Limit Reached',
              key: new Date().getTime() + Math.random(),
              variant: 'error',
            }),
          );
          const remainingTime = Math.max(initialTimeLeftRef.current - elapsedTimeRef.current, 0);
          dispatch(updateReduxMinutes(true, remainingTime));
          onTimeEnd(false);
        }
        forceUpdate();
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive, onTimeEnd, dispatch]);

  // Update the remaining minutes on the server every 5 seconds.
  // @ts-nocheck
  const updateRemainingMinutes = async (remainingTime) => {
    try {
      console.log('initialTime', initialTimeLeftRef.current);
      console.log('elapsedSeconds', elapsedTimeRef.current);
      console.log('remainingTime', remainingTime);
      const response = await NotesService.updateRemainingMinutes(clinicId, userId, remainingTime);
      console.log('Updated Remaining Minutes:', response);
    } catch (error) {
      console.error('Error updating remaining minutes:', error);
    }
  };

  useEffect(() => {
    let interval;
    if (isActive) {
      interval = setInterval(() => {
        const remainingTime = Math.max(initialTimeLeftRef.current - elapsedTimeRef.current, 0);
        updateRemainingMinutes(remainingTime);
      }, 5000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive]);

  const handleBeforeUnload = async (event: BeforeUnloadEvent) => {
    if (isActive) {
      event.preventDefault();
      event.returnValue = 'You have an active recording. Are you sure you want to leave?';
      const remainingTime = Math.floor(initialTimeLeftRef.current - elapsedTimeRef.current);
      await updateRemainingMinutes(clinicId, userId, remainingTime);
    }
  };

  useEffect(() => {
    const handleUnloadEvent = (event: BeforeUnloadEvent) => handleBeforeUnload(event);
    window.addEventListener('beforeunload', handleUnloadEvent);
    return () => {
      window.removeEventListener('beforeunload', handleUnloadEvent);
    };
  }, [isActive]);

  const formatTime = (totalSeconds: number) => {
    const h = Math.floor(totalSeconds / 3600);
    const m = Math.floor((totalSeconds % 3600) / 60);
    const s = totalSeconds % 60;
    return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
  };

  const remainingTime = Math.max(initialTimeLeftRef.current - elapsedTimeRef.current, 0);
  const elapsedMinutes = Math.floor(elapsedTimeRef.current / 60);
  const elapsedSeconds = elapsedTimeRef.current % 60;

  if (buttonWithText) {
    return (
      <label htmlFor={`fileInput-${name}`} className="noBorderButton">
        {typeof SvgIcon === 'string' ? (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Tooltip title={toolTip}>
              <svg width="30" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d={SvgIcon} fill={isActive ? 'red' : '#2D2869'} />
              </svg>
            </Tooltip>
            <span className="mt-1 font-medium">{name}</span>
          </div>
        ) : (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Tooltip title={toolTip}>
              <SvgIcon width="30" height="40" sx={{ color: isActive ? 'red' : '#2D2869', fontSize: '35px' }} />
            </Tooltip>
            <span className="mt-1 font-medium">{name}</span>
          </div>
        )}
        <VisuallyHiddenInput id={`fileInput-${name}`} onChange={onClick} type="file" accept="image/*" />
      </label>
    );
  }

  if (type === 'file') {
    return (
      <label htmlFor="fileInput" className="generateBtns">
        {typeof SvgIcon === 'string' ? (
          <Tooltip title={toolTip}>
            <svg width="30" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d={SvgIcon} fill={isActive ? 'red' : '#2D2869'} />
            </svg>
          </Tooltip>
        ) : (
          <Tooltip title={toolTip}>
            <SvgIcon width="30" height="40" sx={{ color: isActive ? 'red' : '#2D2869', fontSize: '30px' }} />
          </Tooltip>
        )}
        <VisuallyHiddenInput id="fileInput" onChange={onClick} type="file" accept="image/*" />
      </label>
    );
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
      {isActive && (name === 'Dictate' || name === 'Transcribe') && isFreePlan && (
        <Typography
          variant="body2"
          style={{
            marginBottom: '5px',
            color: remainingTime === 0 ? 'red' : 'black',
            fontWeight: 'bold',
          }}
        >
          {`Time Left: ${formatTime(remainingTime)}`}
        </Typography>
      )}

      <Tooltip title={toolTip}>
        <span style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
          <button
            type="button"
            className={`${
              isActive
                ? 'Active'
                : disabled || initialTimeLeftRef.current - elapsedTimeRef.current === 0
                ? 'generateBtnsdisable'
                : 'generateBtns'
            }`}
            onClick={onClick}
            disabled={disabled || initialTimeLeftRef.current - elapsedTimeRef.current === 0}
            style={{
              transition: 'box-shadow 0.3s ease-in-out',
            }}
          >
            {typeof SvgIcon === 'string' ? (
              <svg width="30" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d={SvgIcon} fill={isActive ? 'red' : '#2D2869'} />
              </svg>
            ) : (
              <SvgIcon width="30" height="40" sx={{ color: isActive ? 'red' : '#2D2869', fontSize: '30px' }} />
            )}
            {isActive && (name === 'Dictate' || name === 'Transcribe') && (
              <Typography className="mt-1 font-medium" style={{ marginRight: '5px' }}>
                {`${elapsedMinutes.toString().padStart(2, '0')}:${elapsedSeconds.toString().padStart(2, '0')}`}
              </Typography>
            )}
          </button>
          {/*{ initialTimeLeftRef.current - elapsedTimeRef.current === 0 && (*/}
          {/*  <Typography sx={{ color: 'red' }}>Upgrade your plan to get more audio time</Typography>*/}
          {/*)}*/}
        </span>
      </Tooltip>
    </div>
  );
};
