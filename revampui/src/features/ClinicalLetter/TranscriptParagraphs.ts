// eslint-disable-next-line import/no-extraneous-dependencies
import nlp from 'compromise';

// Define interfaces for the new structure
interface SentenceObject {
  text: string;
  start: number;
  end: number;
}

interface ParagraphObject {
  sentences: SentenceObject[];
  num_words: number;
  start: number;
  end: number;
}

interface TranscriptAlternative {
  transcript: string;
  confidence?: number;
  paragraphs?: {
    transcript: string;
    paragraphs: ParagraphObject[];
  };
}

// Adjust existing interface and create union type
export interface LegacyTranscriptObject {
  start: number;
  duration: number;
  channel: {
    alternatives: TranscriptAlternative[];
  };
}

export interface NewTranscriptObject {
  start: number;
  duration: number;
  channels: {
    alternatives: TranscriptAlternative[];
  }[];
}

export type TranscriptObject = LegacyTranscriptObject | NewTranscriptObject;

// Existing SentenceSegment and Paragraph interfaces remain unchanged
interface SentenceSegment {
  text: string;
  startTime: number;
}

export interface Paragraph {
  text: string;
  startTime: number;
}

export class TranscriptParagraphs {
  // Stores all sentences that have arrived but not yet formed into a finalized paragraph
  private sentenceQueue: SentenceSegment[] = [];

  // Holds finalized paragraphs (each up to 5 sentences)
  private paragraphs: Paragraph[] = [];

  // Maximum number of sentences per paragraph
  private readonly SENTENCES_PER_PARAGRAPH = 3;

  /**
   * Add a new transcript chunk and attempt to form paragraphs.
   */
  public addTranscript(transcriptObj: TranscriptObject): void {
    // Extract the appropriate alternatives array
    const alternatives =
      'channels' in transcriptObj ? transcriptObj.channels[0]?.alternatives : transcriptObj.channel.alternatives;

    if (!alternatives || alternatives.length === 0) return;

    const alternative = alternatives[0];
    if (!alternative) return;

    // Check if paragraphs are available in the alternative
    if (alternative.paragraphs?.paragraphs) {
      this.processParagraphs(alternative.paragraphs.paragraphs);
    } else {
      this.processTranscriptText(alternative.transcript, transcriptObj.start);
    }

    this.processQueue();
  }

  private processParagraphs(paragraphs: ParagraphObject[]): void {
    paragraphs.forEach((paragraph) => {
      paragraph.sentences.forEach((sentence) => {
        const trimmed = sentence.text.trim();
        if (trimmed) {
          this.sentenceQueue.push({
            text: trimmed,
            startTime: sentence.start,
          });
        }
      });
    });
  }

  private processTranscriptText(transcript: string, chunkStartTime: number): void {
    const newText = transcript.trim();
    if (!newText) return;

    const doc = nlp(newText);
    const chunkSentences = doc.sentences().out('array');
    chunkSentences.forEach((sentence: string) => {
      const trimmed = sentence.trim();
      if (trimmed) {
        this.sentenceQueue.push({
          text: trimmed,
          startTime: chunkStartTime,
        });
      }
    });
  }

  // The rest of the class (processQueue, getParagraphs, etc.) remains unchanged
  private processQueue(): void {
    while (this.sentenceQueue.length >= this.SENTENCES_PER_PARAGRAPH) {
      const paragraphSegments = this.sentenceQueue.splice(0, this.SENTENCES_PER_PARAGRAPH);
      const paragraphText = paragraphSegments.map((seg) => seg.text).join(' ');
      const paragraphStartTime = paragraphSegments[0].startTime;
      this.paragraphs.push({ text: paragraphText, startTime: paragraphStartTime });
    }
  }

  /**
   * Return only the finalized paragraphs of 5 sentences each.
   */
  public getParagraphs(): Paragraph[] {
    return this.paragraphs;
  }

  /**
   * Return paragraphs for display:
   *   - All finalized paragraphs
   *   - Plus a partial paragraph for leftover sentences (if any)
   */
  public getDisplayParagraphs(): Paragraph[] {
    const results: Paragraph[] = [...this.paragraphs];
    if (this.sentenceQueue.length > 0) {
      const leftoverText = this.sentenceQueue.map((seg) => seg.text).join(' ');
      const earliestStartTime = this.sentenceQueue[0].startTime;
      results.push({ text: leftoverText, startTime: earliestStartTime });
    }
    return results;
  }
}

// formatDisplayParagraphs remains unchanged
export function formatDisplayParagraphs(paragraphs: Paragraph[]): string {
  return paragraphs.map(({ text, startTime }) => `${text.trim()}\n`).join('\n');
}
