// @ts-nocheck
import React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { useSelector } from 'react-redux';
import CustomInputWithLabel from '../Templates/TemplateAdd/CustomInput';
import { renderPriorityIcon, renderStatus } from '../Tasks/helperComponents';
import { tasksSelector } from '../../selectors/task.selector';

export default function TemplatesInfo() {
  const task = useSelector(tasksSelector);

  if (!task) return null;

  const getTaskNote = () => {
    return task.assignments.find((a) => a.staff_id === task.assigned_to).notes;
  };

  return (
    <div>
      <Box display="flex" flexDirection="column" pt={2} gap={2} sx={{ backgroundColor: 'white' }}>
        <CustomInputWithLabel label="Task Name: " value={task.task_name} variant="outlined" fullWidth disabled />
      </Box>
      <Box display="flex" flexDirection="column" sx={{ backgroundColor: 'white' }} mt={2}>
        <Box display="flex" alignItems="center" gap={2} mt={2} mb={2} justifyContent="space-between">
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="h6" fontSize="18px" fontWeight="700" fontFamily="UrbanistMedium">
              Status
            </Typography>
            {renderStatus(task.status)}
          </Box>
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="h6" fontSize="18px" fontWeight="700" fontFamily="UrbanistMedium">
              Priority
            </Typography>
            <span style={{ textTransform: 'capitalize', fontWeight: '500' }}>{renderPriorityIcon(task.priority)} </span>
          </Box>
        </Box>
        <Typography variant="h6" fontSize="18px" fontWeight="700" mb={2} fontFamily="UrbanistMedium">
          Tasks Note
        </Typography>
        <Box
          display="flex"
          justifyContent="left"
          alignItems="left"
          height="100%"
          border="1px solid #e0e0e0"
          borderRadius="8px"
          p={2}
          mb={2}
        >
          {task?.assignments?.length ? (
            <Typography fontFamily="UrbanistMedium">{getTaskNote()}</Typography>
          ) : (
            <Box display="flex" flexDirection="column" justifyContent="center" alignItems="left">
              <Typography variant="h6" fontSize="18px" fontWeight="700" color="textSecondary" fontFamily="UrbanistMedium">
                No note found
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
    </div>
  );
}
