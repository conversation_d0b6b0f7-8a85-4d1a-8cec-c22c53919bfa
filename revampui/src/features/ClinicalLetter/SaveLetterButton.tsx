import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Button, useTheme } from '@mui/material';

import {
  currentLetterSelector,
  isSecondStepSelector,
  isEditingLetterSelector,
  lettersCountSelector,
  selectedPatientIdSelector,
  stepNoSelector,
  selectedPatientSelector,
} from 'selectors/clinicalLetter.selector';
import { saveClinicalLetter } from 'actions/clinicalLetter.action';
import { generatePdf } from 'features/Templates/TemplateAdd/PdfGenerator';

// import { enqueueSnackbarAction } from '../../actions/app.action';
import SaveOutlinedIcon from '@mui/icons-material/SaveOutlined';
import { clinicSelector, specializationsSelector } from '../../selectors/app.selector';
import { replacePlaceHolder } from '../../helpers/templates';

const SaveLetterButton: React.FC = () => {
  const theme = useTheme();
  const [hovered, setHovered] = useState(false);
  const dispatch = useDispatch();
  const currentLetter = useSelector(currentLetterSelector);
  const clinic = useSelector(clinicSelector);

  const selectedPatientId = useSelector(selectedPatientIdSelector);
  const specializations = useSelector(specializationsSelector);
  const selectedPatient = useSelector(selectedPatientSelector);
  const stepNo = useSelector(stepNoSelector);
  const isSecondStep = useSelector(isSecondStepSelector);
  const isEditingLetter = useSelector(isEditingLetterSelector);
  const lettersCount = useSelector(lettersCountSelector);

  const handleSave = async () => {
    // TODO NEED TO HANDLE THIS CASE YET. HEADER Content show null when no template is selecterd
    const { id, template_id, updated_body_content } = currentLetter;
    let { header_content, footer_content } = currentLetter;

    if (header_content === null) {
      header_content = '';
    }

    if (footer_content === null) {
      footer_content = '';
    }
    const pdf = await generatePdf(
      replacePlaceHolder(header_content || '', clinic),
      replacePlaceHolder(updated_body_content || '', clinic, selectedPatient, specializations),
      replacePlaceHolder(footer_content || '', clinic),
    );
    dispatch(saveClinicalLetter(id, selectedPatientId, updated_body_content, header_content, footer_content, template_id, pdf));
  };

  const canSave = isEditingLetter || lettersCount > 0;
  return (
    <Button
      endIcon={<SaveOutlinedIcon sx={{ opacity: canSave ? 1 : 0.5 }} />}
      disabled={!canSave}
      onClick={() => handleSave()}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      className="invite-user-button"
      sx={(theme) => ({
        background: 'none',
        '&:hover': { border: 'none', boxShadow: 'none' },
        color: theme?.customProperties.ButtonTextColor,
      })}
    >
      Save
    </Button>
  );
};
export default SaveLetterButton;
