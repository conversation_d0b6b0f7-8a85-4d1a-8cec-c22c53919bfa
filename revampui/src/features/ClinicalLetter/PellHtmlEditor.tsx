// @ts-nocheck
import React, { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import pell from 'pell';
import 'pell/dist/pell.css';
import { useTheme } from '@material-ui/core/styles';
import CircularProgress from '@material-ui/core/CircularProgress';
import Box from '@material-ui/core/Box';

export interface HTMLEditorHandle {
  getCleanHTML: () => string;
}

interface HTMLEditorProps {
  initialHTML?: string;
  html?: string;
  onEditorChange: (content: string) => void;
  height?: string;
  width?: string;
}

const PAGE_BREAK_CSS = `
  .pell-content hr.page-break {
    border: none;
    border-top: 1px dashed #666;
    margin: 0.5em 0;
    pointer-events: none;
  }
`;

const builtIn = [
  'bold',
  'italic',
  'underline',
  'strikethrough',
  'heading1',
  'heading2',
  'paragraph',
  'quote',
  'olist',
  'ulist',
  'code',
  'line',
  'link',
  'image',
];

const pageBreakAction = {
  name: 'pageBreak',
  icon: `<svg width="18" height="18">
           <line x1="0" y1="9" x2="18" y2="9"
                 stroke="#666" stroke-dasharray="2,2"/>
         </svg>`,
  title: 'Insert Page Break',
  result: () =>
    document.execCommand('insertHTML', false, '<hr class="page-break"/>'),
};

const HTMLEditor = forwardRef<HTMLEditorHandle, HTMLEditorProps>(({
                                                                    initialHTML = '',
                                                                    html,
                                                                    onEditorChange,
                                                                    height = '210px',
                                                                    width = '100%',
                                                                  }, ref) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const editorRef = useRef<HTMLDivElement>(null);
  const contentElRef = useRef<HTMLElement | null>(null);
  const inputRef = useRef<HTMLElement | null>(null);

  // We'll stash the selection here before opening the file dialog...
  const savedRangeRef = useRef<Range | null>(null);

  // Convert comments → placeholders
  const htmlToPlaceholders = (raw: string) =>
    raw.replace(/<!--\s*pagebreak\s*-->/g, '<hr class="page-break" />');

  // Convert placeholders → comments
  const placeholdersToComments = (dirty: string) =>
    dirty.replace(/<hr\s+class="page-break"[^>]*>/g, '<!-- pagebreak -->');

  // Expose a getCleanHTML() for parent refs
  useImperativeHandle(ref, () => ({
    getCleanHTML: () => {
      if (!contentElRef.current) return '';
      const raw = contentElRef.current.innerHTML;
      return placeholdersToComments(raw);
    },
  }), []);

  useEffect(() => {
    // 1) inject our CSS
    const styleTag = document.createElement('style');
    styleTag.textContent = PAGE_BREAK_CSS;
    document.head.appendChild(styleTag);

    try {
      // 2) init Pell
      const { content } = pell.init({
        element: editorRef.current!,
        defaultParagraphSeparator: 'p',
        styleWithCSS: true,
        actions: [
          ...builtIn,
          {
            name: 'image-upload',
            icon: '🖼️',
            title: 'Insert Image',
            result: () => {
              const sel = window.getSelection();
              if (sel?.rangeCount) savedRangeRef.current = sel.getRangeAt(0);
              inputRef.current!.click();
            },
          },
          pageBreakAction,
        ],
        onChange: raw => {
          let cleaned = raw.replace(/<p><br><\/p>/g, '');
          cleaned = placeholdersToComments(cleaned);
          onEditorChange(cleaned);
        },
      });

      // 3) seed any initial HTML
      content.innerHTML = html
        ? htmlToPlaceholders(html)
        : htmlToPlaceholders(initialHTML);

      contentElRef.current = content;
    } catch (err) {
      console.error('Error initializing Pell:', err);
    } finally {
      // 4) hide spinner
      setLoading(false);
    }

    return () => {
      document.head.removeChild(styleTag);
    };
  }, []);



  // useEffect(() => {
  //   if (!editorRef.current) return;
  //
  //   const styleTag = document.createElement('style');
  //   styleTag.textContent = PAGE_BREAK_CSS;
  //   document.head.appendChild(styleTag);
  //
  //   const { content } = pell.init({
  //     element: editorRef.current,
  //     defaultParagraphSeparator: 'p',
  //     styleWithCSS: true,
  //     actions: [...builtIn, pageBreakAction, {
  //       name: 'image-upload',
  //       icon: '🖼️',
  //       title: 'Insert Image',
  //       result: () => {
  //         // save the current cursor/selection
  //         const sel = window.getSelection();
  //         if (sel && sel.rangeCount > 0) {
  //           savedRangeRef.current = sel.getRangeAt(0);
  //         }
  //         // open file picker
  //         inputRef.current?.click();
  //       },
  //     }],   //  ←  FIX
  //     onChange: raw => {
  //       let cleaned = raw.replace(/<p><br><\/p>/g, '');
  //       cleaned = placeholdersToComments(cleaned);
  //       onEditorChange(cleaned);
  //     },
  //   });
  //
  //   content.innerHTML = html
  //     ? htmlToPlaceholders(html)
  //     : htmlToPlaceholders(initialHTML);
  //
  //   contentElRef.current = content;
  //   setLoading(false);
  //
  //   // eslint-disable-next-line consistent-return
  //   return () => document.head.removeChild(styleTag);
  // }, [editorRef.current]);

  // if html prop changes externally, reload it (keeping placeholders)
  useEffect(() => {
    if (contentElRef.current && typeof html === 'string') {
      contentElRef.current.innerHTML = htmlToPlaceholders(html);
    }
  }, [html]);

  const handleFileChange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onloadend = () => {
      const url = reader.result as string;

      // restore the saved cursor
      const sel = window.getSelection();
      sel.removeAllRanges();
      if (savedRangeRef.current) {
        sel.addRange(savedRangeRef.current);
      }
      // focus the editor to ensure execCommand works
      contentElRef.current?.focus();

      // finally insert the image
      const ok = document.execCommand('insertImage', false, url);
      if (!ok) {
        console.error('insertImage command failed');
      }

      // fire onEditorChange so parent sees the new <img>
      const htmlNow = contentElRef.current?.innerHTML || '';
      onEditorChange(placeholdersToComments(htmlNow));

      // clear the savedRange so it doesn’t accidentally reinsert
      savedRangeRef.current = null;
    };
    reader.readAsDataURL(file);

    // clear the input so selecting the same file again still fires change
    e.target.value = '';
  };

  return (
    <div
      style={{
        position: 'relative',
        height,
        width,
        border: '1px solid #ccc',
        borderRadius: 4,
        overflow: 'hidden',
      }}
    >
      {/* 1) Always mount the editor container */}
      <div
        data-testid="pell-editor"
        ref={editorRef}
        style={{
          height: '100%',
          width: '100%',
          opacity: loading ? 0.3 : 1,        // slightly faded while loading
        }}
      />

      {/* 2) Overlay spinner while loading */}
      {loading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            bgcolor: 'rgba(255,255,255,0.7)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <CircularProgress color="inherit" />
        </Box>
      )}

      {/* 3) Hidden file input for image uploads */}
      <input
        type="file"
        accept="image/*"
        ref={inputRef}
        onChange={handleFileChange}
        style={{ display: 'none' }}
      />
    </div>
  );
});

export default HTMLEditor;
