import React, { useEffect, useState } from 'react';
import { useHistory, useLocation, useParams } from 'react-router';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';

import {
  <PERSON>ack,
  Button,
  ImageList,
  ImageListItem,
  ImageListItemBar,
  Box,
  useTheme,
  useMediaQuery,
  Pagination,
} from '@mui/material';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';

import { IClinicalLetterActionTypes, IRouteParams } from 'models/IClinicalLetterState';
import { getLetter, getPatients, getTemplates } from 'actions/clinicalLetter.action';
import {
  selectedTemplateSelector,
  templatesSelector,
  thumbnailsSelector,
  isFirstStepSelector,
  isSecondStepSelector,
  isThirdStepSelector,
  isLoadingSelector,
  selectedTemplateIdSelector,
  isViewingLetterSelector,
  isCurrentLetterSavedSelector,
  lettersCountSelector,
} from 'selectors/clinicalLetter.selector';
import { userSelector, clinicIdSelector } from 'selectors/auth.selector';

import Typography from '@mui/material/Typography';
import CircularProgress from '@material-ui/core/CircularProgress';
import Backdrop from '@mui/material/Backdrop';
import _ from 'lodash';

import VisibilityIcon from '@mui/icons-material/Visibility';
import { ArrowDropDownIcon } from '@mui/x-date-pickers';
import PatientList from './PatientList';
import ClinicalNotes from './ClinicalNotes';
import LetterPDF from './LetterPDF';
import SaveLetterButton from './SaveLetterButton';
import DownloadLetterButton from './DownloadLetterButton';
import CustomAppBar from '../Tasks/CustomAppBar';
import EditSvg from '../../themes/svgs/EditSvg';
import AssignToSvg from '../../themes/svgs/AssignToSvg';
import AssignToDialog from '../Tasks/AssignToDialog';
import { IAppActionTypes } from '../../models/IAppState';
import TemplateService from '../../services/templateService';
import TemplatesInfo from './TemplatesInfo';
import { tasksSelector } from '../../selectors/task.selector';
import { setTaskInStore } from '../../actions/task';
import { ITaskActionTypes } from '../../models/ITaskState';
import { enqueueSnackbarAction } from '../../actions/app.action';
import { canAction } from '../../helpers';
import { PERMISSIONS } from '../../configs';
import LetterTabs from './LetterTabs';
import NoRecordsRow from '../../components/molecules/NoRecordsMessage/NoRecordsMessage';
import { specializationsSelector } from '../../selectors/app.selector';
import { getSpecializations } from '../../services/users';
import PatientMedicalHistoryCheckbox from './PatientMedicalHistoryCheckbox';
import GenerateBtnsMultiple from './GenerateBtnsMultiple';

interface LetterPageState {
  mode?: string; // 'view' | 'edit'
  pageHeading?: string;
}

function ClinicalLetter() {
  const dispatch = useDispatch();
  const history = useHistory();
  const location = useLocation<LetterPageState>();
  const { letterID, taskID } = useParams<IRouteParams>();
  const [isPreview, setIsPreview] = useState<boolean>(false);
  const [bodyHeight, setBodyHeight] = useState<number>(550);
  const [assignToModalOpen, setAssignToModalOpen] = useState<boolean>(false);
  const [editTaskModalOpen, setEditTaskModalOpen] = useState<boolean>(false);
  const [clinic, setClinic] = useState<any>();

  const loginUser = useSelector(userSelector);
  const templates = useSelector(templatesSelector);
  const selectedTemplate = useSelector(selectedTemplateSelector);
  const selectedTemplateId = useSelector(selectedTemplateIdSelector);
  const thumbnailUrls = useSelector(thumbnailsSelector);
  const isFirstStep = useSelector(isFirstStepSelector, shallowEqual);
  const isSecondStep = useSelector(isSecondStepSelector);
  const isThirdStep = useSelector(isThirdStepSelector, shallowEqual);
  const isLoading = useSelector(isLoadingSelector);
  const isViewingLetter = useSelector(isViewingLetterSelector, shallowEqual);
  const lettersCount = useSelector(lettersCountSelector);
  const isCurrentLetterSaved = useSelector(isCurrentLetterSavedSelector);
  const tasks = useSelector(tasksSelector);
  const clinicId = useSelector(clinicIdSelector);
  const specializations = useSelector(specializationsSelector);
  const locationProps = location.state || {};
  const [selectedTemplate_Border, setSelectedTemplate_Border] = useState(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  useEffect(() => {
    dispatch({ type: IAppActionTypes.SET_BREADCRUMB, payload: 'Document History / Create Document' });
    if (loginUser) {
      dispatch(getPatients({ clinic_id: loginUser.clinic_id }));
      TemplateService.clinicDetails().then((_clinic) => {
        setClinic(_clinic);
        dispatch({ type: IAppActionTypes.SET_CLINIC, payload: _clinic });
      });
      // TODO CHECK IF THIS IS NOT USED REMOVE THIS
      if (_.isEmpty(specializations)) {
        getSpecializations()
          .then((_specializations) => {
            dispatch({ type: IAppActionTypes.SET_SPECIALIZATIONS, payload: _specializations });
          })
          .catch((e) => {
            console.log(e);
          });
      }
    }
    dispatch(getTemplates(clinicId as string));

    return () => {
      dispatch({ type: IClinicalLetterActionTypes.RESET_STATE });
      dispatch({ type: ITaskActionTypes.CLEAR_TASK_IN_STORE });
      dispatch({ type: IAppActionTypes.SET_BREADCRUMB, payload: '' });
    };
  }, []);

  useEffect(() => {
    if (letterID) {
      dispatch({
        type: IAppActionTypes.SET_BREADCRUMB,
        payload: locationProps.mode === 'view' ? 'Document History / View Document' : 'Document History / Edit Document',
      });
      // @ts-ignore
      dispatch(getLetter(letterID))
        // @ts-ignore
        .then((_letter: any) => {})
        .catch((err: any) => {
          history.push('/clinical-document');
          dispatch(
            enqueueSnackbarAction({
              message: 'Error: Letter does not exist',
              key: new Date().getTime() + Math.random(),
              variant: 'error',
            }),
          );
        });
    }
    if (taskID) {
      dispatch(setTaskInStore(taskID));
    }
  }, [letterID, taskID]);

  useEffect(() => {
    if (taskID && tasks) {
      // check If Task Belongs To Letter
      if (tasks?.letter_id !== parseInt(letterID as string, 10)) history.push(`/clinical-document/letter/${letterID}`);
    }
  }, [tasks]);

  // useEffect(() => {
  //   if (letter?.header_content || letter?.footer_content) {
  //     updateHeaderAndFooterImages({
  //       letter_header: letter.header_content,
  //       letter_footer: letter.footer_content,
  //     });
  //   }
  // }, [letter]);

  const updateHeaderAndFooterImages = async (_selectedTemplate: any, templateId?: any) => {
    const { letter_header, letter_footer } = _selectedTemplate;
    if (templateId) {
      dispatch({
        type: IClinicalLetterActionTypes.SELECT_TEMPLATE,
        payload: { templateId: templateId, headerHtml: letter_header, footerHtml: letter_footer },
      });
    }
  };
  const updateClearSelection = async () => {
    dispatch({
      type: IClinicalLetterActionTypes.SELECT_TEMPLATE,
      payload: { templateId: null, headerHtml: '', footerHtml: '' },
    });
    setSelectedTemplate_Border(null);
  };

  const updateTemplate = async (templateId: any = null) => {
    const foundTemplate = templates?.find((template: any) => template.id === templateId);
    if (!foundTemplate) return;

    setSelectedTemplate_Border(foundTemplate);
    updateHeaderAndFooterImages(foundTemplate, templateId);
  };

  const updateStep = (step: number) => {
    dispatch({ type: IClinicalLetterActionTypes.SET_STEP, payload: { stepNo: step } });
  };

  const showPreview = () => {
    dispatch({ type: IClinicalLetterActionTypes.UPDATE_INITIAL_BODY_HTML });
    setIsPreview(true);
  };

  const getThumbnailUrl = (thumbnail_uuid: string) => {
    const thumbnailUrl = thumbnailUrls?.find((url) => url.includes(thumbnail_uuid));
    return thumbnailUrl ?? '';
  };

  const Toolbar = () => {
    const theme = useTheme();
    const [hovered, setHovered] = useState(false);
    return (
      <CustomAppBar
        leftContent={
          <> </>
          // <Typography
          //   variant="h6"
          //   noWrap
          //   component="div"
          //   sx={{ flexGrow: 1, display: { sm: 'block' }, fontSize: '18px', fontFamily: 'UrbanistMedium' }}
          // >
          //   {location.state ? location.state.pageHeading : 'Create Document'}
          // </Typography>
        }
        rightContent={
          <Stack direction="row" spacing={2}>
            {!isCurrentLetterSaved && tasks && canAction('do', PERMISSIONS.TASK_EDIT) && (
              <Button
                startIcon={<EditSvg />}
                onClick={() => setEditTaskModalOpen(true)}
                sx={(theme) => ({
                  background: 'none',
                  '&:hover': { border: 'none', boxShadow: 'none' },
                  color: theme?.customProperties.ButtonTextColor,
                })}
                className="add-role-button"
              >
                Edit Task
              </Button>
            )}
            {!isCurrentLetterSaved && (
              <>
                {isPreview ? (
                  <Button
                    startIcon={<EditSvg fill={lettersCount === 0 ? 'gray' : 'black'} />}
                    onClick={() => setIsPreview(false)}
                    disabled={lettersCount === 0}
                    onMouseEnter={() => setHovered(true)}
                    onMouseLeave={() => setHovered(false)}
                    sx={(theme) => ({
                      background: 'none',
                      '&:hover': { border: 'none', boxShadow: 'none' },
                      color: theme?.customProperties.ButtonTextColor,
                    })}
                    className="add-role-button"
                  >
                    Edit
                  </Button>
                ) : null}
                {!isPreview ? (
                  <Button
                    endIcon={<VisibilityIcon sx={{ opacity: lettersCount === 0 ? 0.5 : 1 }} />}
                    onClick={() => showPreview()}
                    disabled={lettersCount === 0}
                    onMouseEnter={() => setHovered(true)}
                    onMouseLeave={() => setHovered(false)}
                    className="add-role-button"
                    sx={(theme) => ({
                      background: 'none',
                      '&:hover': { border: 'none', boxShadow: 'none' },
                      color: theme?.customProperties.ButtonTextColor,
                    })}
                  >
                    Preview
                  </Button>
                ) : null}
                {!!letterID && canAction('do', PERMISSIONS.CLINICAL_NOTE_EDIT) && <SaveLetterButton />}
                {!letterID && canAction('do', PERMISSIONS.CLINICAL_NOTE_ADD) && <SaveLetterButton />}
              </>
            )}
            {isCurrentLetterSaved && (
              <>
                <DownloadLetterButton />

                {canAction('do', PERMISSIONS.TASK_ADD) && (loginUser?.has_tasks) && (
                  <Button
                    startIcon={<AssignToSvg fill={!isThirdStep ? 'gray' : 'black'} />}
                    className="invite-user-button"
                    // disabled={!isCurrentLetterSaved}
                    onClick={() => setAssignToModalOpen(true)}
                    sx={(theme) => ({
                      background: 'none',
                      '&:hover': { border: 'none', boxShadow: 'none' },
                      color: theme?.customProperties.ButtonTextColor,
                    })}
                  >
                    {' '}
                    Assign To
                  </Button>
                )}
              </>
            )}
            {canAction('do', PERMISSIONS.TASK_ADD) && (loginUser?.has_tasks) && (
              <AssignToDialog
                open={assignToModalOpen}
                onClose={() => {
                  setAssignToModalOpen(false);
                }}
              />
            )}
            {!isThirdStep && tasks && canAction('do', PERMISSIONS.TASK_ADD) && (loginUser?.has_tasks) && (
              <AssignToDialog
                open={editTaskModalOpen}
                onClose={() => {
                  setEditTaskModalOpen(false);
                }}
                taskData={tasks}
                setTask={(t: any) => {
                  dispatch(setTaskInStore(tasks.id));
                }}
              />
            )}
          </Stack>
        }
      />
    );
  };

  const ViewLetterToolbar = () => {
    const [hovered, setHovered] = useState(false);
    return (
      <CustomAppBar
        leftContent={
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{ flexGrow: 1, display: { sm: 'block' }, fontSize: '18px', fontFamily: 'UrbanistMedium' }}
          >
            View Document
          </Typography>
        }
        rightContent={
          <>
            <DownloadLetterButton />
            {canAction('do', PERMISSIONS.CLINICAL_NOTE_EDIT) && (loginUser?.has_tasks) && (
              <Button
                startIcon={<AssignToSvg fill={!isThirdStep ? 'gray' : 'black'} />}
                sx={(theme) => ({
                  background: 'none',
                  '&:hover': { border: 'none', boxShadow: 'none' },
                  color: theme?.customProperties.ButtonTextColor,
                })}
                onClick={() => setAssignToModalOpen(true)}
                className="invite-user-button"
              >
                Assign To
              </Button>
            )}
            <AssignToDialog
              open={assignToModalOpen}
              onClose={() => {
                setAssignToModalOpen(false);
              }}
            />
            {tasks && (
              <AssignToDialog
                open={editTaskModalOpen}
                onClose={() => {
                  setEditTaskModalOpen(false);
                }}
                taskData={tasks}
                setTask={(t: any) => {
                  dispatch(setTaskInStore(tasks.id));
                }}
              />
            )}
          </>
        }
      />
    );
  };

  const NotesPane = () => {
    return (
      <Stack
        direction="column"
        sx={{
          width: isMobile ? '100%' : '50%',
          backgroundColor: theme.palette.mode === 'dark' ? '#222' : 'white',
          border: `15px solid ${theme.palette.mode === 'dark' ? '#1a1a1a' : '#F9F9F9'}`,
          borderRadius: '8px',
          padding: isMobile ? '16px' : '24px',
          boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.1)',
          color: theme.palette.mode === 'dark' ? 'white' : 'black',
          height: isMobile ? '100%' : '900px',
        }}
      >
        <Stack direction="column" spacing={2}>
          <PatientList />
        </Stack>
        {loginUser?.has_medical_history && (
          <Stack direction="row" spacing={2} py={2} alignItems="center">
            <Typography variant="body2" sx={{ fontSize: '14px', color: theme.palette.mode === 'dark' ? 'white' : 'black' }}>
              Use Patient Medical History
            </Typography>
            <PatientMedicalHistoryCheckbox />
          </Stack>
        )}
        <ClinicalNotes />

        <Stack
          mt={0}
          direction="row"
          justifyContent="center"
          sx={{
            borderTop: 'none',
            marginTop: '16px',
          }}
        >
          <GenerateBtnsMultiple />
        </Stack>

        <Typography
          sx={{
            color: '#FEA52A', // Orange text
            fontSize: '14px',
            textAlign: 'left',
            lineHeight: '1.5',
            fontWeight: 500,

            backgroundColor: theme.palette.mode === 'dark' ? '#222' : '#fff8ed',
            borderRadius: '10px',
            padding: '16px',
          }}
        >
          ⚠️ Warning: Please review all generated content before use. The accuracy and appropriateness of the information should
          be verified to ensure the best patient care.
        </Typography>
      </Stack>
    );
  };

  const TemplateImageList = () => {
    const Templates = templates || [];
    const ITEMS_PER_PAGE = 6;
    const [page, setPage] = useState(1);

    const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
      setPage(value);
    };

    // Pagination logic
    const paginatedTemplates = Templates.slice((page - 1) * ITEMS_PER_PAGE, page * ITEMS_PER_PAGE);
    return (
      <>
        {selectedTemplate_Border && (
          <div style={{ marginTop: '10px', textAlign: 'right', display: 'flex', justifyContent: 'flex-end' }}>
            <Button className="invite-user-button" sx={{ borderRadius: '30px !important' }} onClick={updateClearSelection}>
              {' '}
              Clear Selection{' '}
            </Button>
          </div>
        )}
        <ImageList
          gap={16}
          sx={(theme) => ({
            padding: '16px',
            display: 'flex',
            justifyContent: 'center',
            flexWrap: 'wrap',
            width: '100%',
            borderRadius: '8px',
          })}
        >
          {Templates.length > 0 ? (
            paginatedTemplates.map((template: any, index: any) => (
              <ImageListItem
                key={template.id}
                sx={(theme) => ({
                  cursor: 'pointer',
                  padding: '16px 30px',
                  borderRadius: '10px',
                  width: {
                    xs: '47%',
                    md: '30%',
                  },
                  textAlign: 'center',
                  display: 'flex',
                  alignItems: 'center',
                  background:
                    theme.palette.mode === 'dark'
                      ? 'linear-gradient(to bottom, #333333, #000000)'
                      : 'linear-gradient(to bottom, #d3d3d3, #ffffff)',
                })}
                onClick={() => updateTemplate(template.id)}
              >
                <img
                  src={getThumbnailUrl(template.thumbnail_uuid)}
                  alt={`Image ${index}`}
                  style={{
                    width: '130px',
                    height: '160px',
                    objectFit: 'contain',
                    border: selectedTemplateId === template.id ? '2px solid black' : 'none',
                    borderRadius: '8px',
                    filter: `drop-shadow(0px 4px 8px rgba(0, 0, 0, ${selectedTemplateId === template.id ? '0.2' : '0.1'}))`,
                  }}
                />
                <ImageListItemBar
                  title={template.name || 'Designs'}
                  position="below"
                  sx={{ textAlign: 'center', fontSize: '10px' }}
                  className="design-title"
                />
              </ImageListItem>
            ))
          ) : (
            <NoRecordsRow />
          )}
        </ImageList>
        {Templates.length > ITEMS_PER_PAGE && (
          <Stack direction="row" justifyContent="center" mt={2}>
            <Pagination
              count={Math.ceil(Templates.length / ITEMS_PER_PAGE)}
              page={page}
              onChange={handlePageChange}
              color="primary"
              shape="rounded"
              sx={{
                '& .MuiPaginationItem-root': {
                  borderRadius: '12px',
                  fontWeight: 500,
                  fontSize: '16px',
                  color: theme.palette.text.primary,
                  '&:hover': {
                    backgroundColor: theme.palette.action.hover,
                  },
                },
                '& .Mui-selected': {
                  backgroundColor: theme.palette.mode === 'dark' ? '#fffff !important' : '#2d2869 !important', // Dark blue/purple active background
                  color: '#fff',
                  '&:hover': {
                    backgroundColor: '#2d2869',
                  },
                },
                '& .MuiPaginationItem-icon': {
                  color: theme.palette.text.primary,
                },
              }}
            />
          </Stack>
        )}
      </>
    );
  };

  const DesignPane = () => {
    return (
      <>
        <Stack
          direction="column"
          spacing={2}
          sx={{
            width: {
              xs: '100%', // mobile
              md: '50%', // medium and up (web/desktop)
            },
            backgroundColor: (theme) => (theme.palette.mode === 'dark' ? '#000000' : '#FAFAFA'),
            padding: 3,
            borderRadius: 2,
          }}
        >
          <Stack direction="column" height="100%" sx={{ marginBottom: '32px' }}>
            <Stack direction="row" spacing={2} justifyContent="space-between" alignItems="center">
              <Typography variant="h6" fontSize="24px" fontWeight="700" letterSpacing="1px">
                {tasks ? 'Task Details' : 'Designs'}
              </Typography>
              <Button
                className="add-role-button"
                sx={(theme) => ({
                  background: 'none',
                  color: theme.palette.text.primary,
                })}
                startIcon={<ArrowBackIosIcon />}
                onClick={() => updateStep(1)}
              >
                Back to Notes
              </Button>
            </Stack>
            <TemplatesInfo />
            {tasks && (
              <Typography variant="h6" fontSize="14px" fontWeight="700">
                Designs
              </Typography>
            )}
            <TemplateImageList />
          </Stack>
        </Stack>
      </>
    );
  };

  const LettersPane = () => {
    return (
      <Stack direction="column" sx={{ width: isMobile ? '100%' : '50%', marginLeft: isMobile ? '0' : '15px !important' }}>
        <Stack direction="column" spacing={1}>
          <LetterTabs />
        </Stack>
      </Stack>
    );
  };

  return (
    <Stack direction="column" sx={{ height: '100%' }}>
      <Backdrop sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }} open={isLoading}>
        <CircularProgress color="inherit" />
      </Backdrop>
      {isViewingLetter && (
        <>
          <ViewLetterToolbar />
          <Stack direction="row" justifyContent="center" spacing={8} sx={{ paddingTop: '30px' }}>
            <LettersPane />
          </Stack>
        </>
      )}
      {!isViewingLetter && (
        <>
          <Toolbar />
          <Stack direction={{ xs: 'column', sm: 'row' }} justifyContent="space-between" spacing={8} sx={{ paddingTop: '30px' }}>
            {isFirstStep && <NotesPane />}
            {isSecondStep && <DesignPane />}
            {isPreview && (
              <Box sx={{ width: { xs: '100%', md: '50%' } }} className="letter-pree">
                <LetterPDF letterId={0} />
              </Box>
            )}
            {!isPreview && <LettersPane />}
          </Stack>
        </>
      )}
    </Stack>
  );
}

export default ClinicalLetter;
