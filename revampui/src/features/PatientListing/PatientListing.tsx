import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useHistory } from 'react-router-dom';

import Backdrop from '@mui/material/Backdrop';
import CircularProgress from '@material-ui/core/CircularProgress';
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined';

import { DeletePatient, PatientListing_Details } from 'services/patientListingService';
import { Table, TableBody, TableContainer, Paper, Box, Pagination, Typography, useTheme } from '@mui/material';
import { IAppActionTypes } from 'models/IAppState';

import SearchIcon from '@mui/icons-material/Search';
import { Search, SearchIconWrapper, StyledInputBase } from '../../components/molecules/SearchBar/SearchBar';

import {
  ButtonContainer,
  InsuranceCell,
  StyledAvatar,
  StyledSpan,
  StyledTableCell,
  StyledTableCellHeading,
  StyledTableHead,
  StyledTableRow,
  StyledTableRowHeading,
  TablePaginationContainer,
  styles,
} from './StyledComponents';

import { stringAvatar } from '../../helpers/avatar';
import { PATH_NAME, PERMISSIONS } from '../../configs';
import { IClinicalLetterActionTypes } from '../../models/IClinicalLetterState';
import IRootState from '../../models/IRootState';

import FormDialog from './FormDialog';
import ViewIcon from '../../themes/icons/ViewIcon';
import EditIcon from '../../themes/icons/EditIcon';
import CreateLetterIcon from '../../themes/icons/CreateLetterIcon';
import TrashIcon from '../../themes/icons/TrashIcon';
import { canAction } from '../../helpers';
import NoRecordsRow from '../../components/molecules/NoRecordsMessage/NoRecordsMessage';
import CustomAppBar from '../Tasks/CustomAppBar';
import { enqueueSnackbarAction } from '../../actions/app.action';

interface RowData {
  id: number;
  patientId: string;
  patientName: string;
  clinic: string;
  emailAddress: string;
  phoneNumber: string;
  insurence: string;
  profilePic: string;
  name: string;
}

interface DeleteField {
  deleteField: String;
}

const decrypt = (
  decryptedData: any,
  setTotalRecords: React.Dispatch<React.SetStateAction<number>>,
  setRowsPerPage: React.Dispatch<React.SetStateAction<number>>,
) => {
  const { list, totalRecords, recordsPerPage } = decryptedData;
  setTotalRecords(totalRecords);
  setRowsPerPage(recordsPerPage);
  return list.map((item: any) => ({
    id: item?.id,
    patientId: item?.id,
    patientName: `${item?.first_name} ${item?.last_name}`,
    clinic: item?.clinic?.name,
    emailAddress: item?.email || '',
    phoneNumber: item?.phone || '',
    insurence: item?.insurence_status || '',
    profilePic: '',
    name: item?.name || '',
  }));
};

const TableComponent: React.FC = () => {
  const [rows, setRows] = useState<RowData[]>([]);
  const [page, setPage] = useState(0);
  const [searchString, setSearchString] = useState('');
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalRecords, setTotalRecords] = useState(0);
  const [hoveredRowIndex, setHoveredRowIndex] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteUser, setDeleteUSer] = useState(0);
  const auth = useSelector((state: IRootState) => state.auth);
  const history = useHistory();
  const dispatch = useDispatch();
  const theme = useTheme();

  const handleFetchData = (page: number, searchString?: string) => {
    if (searchString) {
      fetchData(page + 1, searchString);
    } else {
      fetchData(page + 1);
    }
  };
  useEffect(() => {
    handleFetchData(page);
  }, [page]);

  const handleFormSubmit = (formData: DeleteField) => {
    const data = formData.deleteField;
    deletePatientRecord(deleteUser, data);
    const updatedRows = rows.filter((row) => row.id !== deleteUser);
    setRows(updatedRows);
  };

  const deletePatientRecord = async (Id: number, reason: any) => {
    setLoading(true);
    try {
      const response = await DeletePatient(Id, reason);
      dispatch(
        enqueueSnackbarAction({
          message: 'Patient deleted successfully',
          key: new Date().getTime() + Math.random(),
          variant: 'success',
        }),
      );
    } catch (error) {
      console.error('Error Deleting Patient:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchData = async (pageNumber: number, searchString?: any) => {
    if (!searchString) {
      setLoading(true);
    }
    try {
      // Check if searchString is provided and include it in the request
      const response = await PatientListing_Details(
        pageNumber,
        auth?.user?.clinic_id,
        searchString ? { searchString } : undefined,
      );

      const encryptedData = decrypt(response, setTotalRecords, setRowsPerPage);
      setRows(encryptedData);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleComment = (id: any, name: string) => {
    dispatch({ type: IClinicalLetterActionTypes.SELECT_PATIENT, payload: { selectedPatientId: id } });
    dispatch({ type: IClinicalLetterActionTypes.SET_PATIENT_FILTER, payload: { patientFilter: name } });
    history.push(PATH_NAME.CLINICAL_LETTER);
  };

  const handleChangePage = (event: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage - 1); // Page numbers are 1-based, but internally we use 0-based index
  };

  const handleMouseEnter = (index: number) => {
    setHoveredRowIndex(index);
  };

  const handleMouseLeave = () => {
    setHoveredRowIndex(null);
  };

  const handleClose = () => {
    setDialogOpen(false);
  };
  const handleEditClick = () => {
    dispatch({ type: IAppActionTypes.SET_PAGE_EDITABLE, payload: true });
  };
  return (
    <Box sx={styles.container} className="box-mob">
      <Backdrop sx={styles.backdrop} open={loading}>
        <CircularProgress color="inherit" />
      </Backdrop>
      <CustomAppBar
        leftContent={<></>}
        rightContent={
          <Search className="search-icon-bar">
            <SearchIconWrapper>
              <SearchIcon />
            </SearchIconWrapper>
            <StyledInputBase
              placeholder="Search…"
              inputProps={{ 'aria-label': 'search' }}
              onChange={(e) => handleFetchData(page, e.target.value)}
            />
          </Search>
        }
      />
      <FormDialog
        open={dialogOpen}
        onClose={handleClose}
        onSubmit={handleFormSubmit}
        title="Delete Patient"
        description="Once deleted, all patient details will be lost and cannot be retrieved."
      />

      <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
        <Table className="task-table">
          <StyledTableHead>
            <StyledTableRowHeading>
              <StyledTableCellHeading>Patient ID</StyledTableCellHeading>
              <StyledTableCellHeading>Patient Name</StyledTableCellHeading>
              <StyledTableCellHeading>Clinic</StyledTableCellHeading>
              <StyledTableCellHeading>Email Address</StyledTableCellHeading>
              <StyledTableCellHeading>Phone Number</StyledTableCellHeading>
              <StyledTableCellHeading>Insurance</StyledTableCellHeading>
            </StyledTableRowHeading>
          </StyledTableHead>
          <TableBody>
            {rows.length === 0 ? (
              <NoRecordsRow />
            ) : (
              rows.map((row, index) => (
                <StyledTableRow hover key={row.id} onMouseEnter={() => handleMouseEnter(index)} onMouseLeave={handleMouseLeave}>
                  <StyledTableCell>{row.patientId}</StyledTableCell>
                  <StyledTableCell>
                    <Box display="flex" alignItems="center">
                      <StyledAvatar {...stringAvatar(row.patientName, {}, '22px', '22px', '1rem')} />
                      <StyledSpan>{row.patientName}</StyledSpan>
                    </Box>
                  </StyledTableCell>
                  <StyledTableCell>{row.clinic}</StyledTableCell>
                  <StyledTableCell>{row.emailAddress}</StyledTableCell>
                  <StyledTableCell>{row.phoneNumber}</StyledTableCell>
                  <InsuranceCell>
                    {index === hoveredRowIndex &&
                    (canAction('do', PERMISSIONS.PATIENT_VIEW) ||
                      canAction('do', PERMISSIONS.PATIENT_EDIT) ||
                      canAction('do', PERMISSIONS.PATIENT_DELETE) ||
                      canAction('do', PERMISSIONS.CLINICAL_NOTE_ADD)) ? (
                        <ButtonContainer gap={1}>
                          {canAction('do', PERMISSIONS.PATIENT_VIEW) && (
                          <ViewIcon
                            fontSize="small"
                            component={Link}
                            to={{
                              pathname: PATH_NAME.PATIENT_DETAILS,
                              state: {
                                editable: false,
                                userId: row.id,
                              },
                            }}
                          />
                        )}
                          {canAction('do', PERMISSIONS.CLINICAL_NOTE_ADD) && (
                          <CreateLetterIcon onClick={() => handleComment(row.id, row.name)} fontSize="small" />
                        )}
                          {canAction('do', PERMISSIONS.PATIENT_EDIT) && (
                          <EditIcon
                            fontSize="small"
                            component={Link}
                            onClick={handleEditClick}
                            to={{
                              pathname: PATH_NAME.PATIENT_DETAILS,
                              state: {
                                userId: row.id,
                              },
                            }}
                          />
                        )}
                          {canAction('do', PERMISSIONS.PATIENT_DELETE) && (
                          <TrashIcon
                            onClick={() => {
                              setDeleteUSer(row.id);
                              setDialogOpen(true);
                            }}
                          />
                        )}
                        </ButtonContainer>
                    ) : (
                      <div>{row?.insurence}</div>
                    )}
                  </InsuranceCell>
                </StyledTableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePaginationContainer>
        <Pagination
          count={Math.ceil(totalRecords / rowsPerPage)}
          page={page + 1}
          onChange={handleChangePage}
          color="primary"
          shape="rounded"
          sx={{
            ...styles.pagination,
            '& .MuiPaginationItem-icon': {
              color: theme.palette.text.primary,
            },
          }}
        />
      </TablePaginationContainer>
    </Box>
  );
};

export default TableComponent;
