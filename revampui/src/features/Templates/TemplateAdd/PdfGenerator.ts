import jsPDF from 'jspdf';
import * as pdfjsLib from 'pdfjs-dist';
import httpRequest from '../../../services/httpRequest';

const pdfWorkerUrl = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.2.67/pdf.worker.min.mjs';
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfWorkerUrl;

export const toPixel = (cm: number) => Math.round((cm * 96) / 2.54);

export const toCentimeter = (px: number) => (px * 2.54) / 96;

const cropCanvas = (originalCanvas: HTMLCanvasElement, cropY: number, cropWidth: number, cropHeight: number) => {
  const cropX = 0;
  const croppedCanvas = document.createElement('canvas');
  const croppedCtx = croppedCanvas.getContext('2d');
  croppedCanvas.width = cropWidth;
  croppedCanvas.height = cropHeight;
  if (croppedCtx) {
    croppedCtx.drawImage(originalCanvas, cropX, cropY, cropWidth, cropHeight, 0, 0, cropWidth, cropHeight);
  }
  return croppedCanvas;
};

export const generateImageFromHTML = async (html: string, width: number) => {
  const htmlDiv = document.createElement('div');
  const random = Math.floor(Math.random() * 1000000);
  htmlDiv.id = `dynamicDiv${random}`;
  htmlDiv.style.zIndex = '-999';
  htmlDiv.style.width = `${width}px`;
  htmlDiv.innerHTML = transformUnderlinedHtml(html);
  // maybe to change back
  htmlDiv.style.backgroundColor = 'white';
  htmlDiv.style.color = 'black';
  document.body.appendChild(htmlDiv);
  // @ts-ignore
  const canvas = await window.html2canvas(htmlDiv, { removeContainer: true, x: 0, y: 0, width: width, scale: 1 });
  document.body.removeChild(htmlDiv);
  return canvas;
};

export async function generatePdf(headerHtml: string, bodyHtml: string, footerHtml: string): Promise<jsPDF> {
  try {
    // Call the backend API to generate the PDF using httpRequest.post3
    const response = await httpRequest.post3('/v1/clinical-note/generate-pdf-preview', {
      headerHtml,
      bodyHtml,
      footerHtml,
    }, {
      responseType: 'arraybuffer',
    });
    
    // Convert the response to a Uint8Array
    const pdfData = new Uint8Array(response.data);
    
    // Create a new jsPDF instance
    // eslint-disable-next-line new-cap
    const pdfDocument = new jsPDF({
      format: 'letter',
      unit: 'cm',
      userUnit: 300,
    });
    
    // Store the PDF data in the jsPDF object for later use
    (pdfDocument as any)._serverGeneratedPdf = pdfData;
    
    // Override the save method to use the server-generated PDF
    const originalSave = pdfDocument.save;
    // @ts-ignore
    pdfDocument.save = function (filename?: string) {
      if ((this as any)._serverGeneratedPdf) {
        // Create a blob from the PDF data
        const blob = new Blob([(this as any)._serverGeneratedPdf], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        
        // Create a link and trigger the download
        const link = document.createElement('a');
        link.href = url;
        link.download = filename || 'document.pdf';
        link.click();
        
        // Clean up
        URL.revokeObjectURL(url);
      } else {
        // Fall back to the original save method
        originalSave.call(this, filename);
      }
      
      return this;
    };
    
    // Also override the output method for consistency
    const originalOutput = pdfDocument.output;
    // @ts-ignore
    pdfDocument.output = function (type: string, options?: any) {
      if ((this as any)._serverGeneratedPdf && (type === 'blob' || type === 'arraybuffer')) {
        if (type === 'blob') {
          return new Blob([(this as any)._serverGeneratedPdf], { type: 'application/pdf' });
        } 
          return (this as any)._serverGeneratedPdf.buffer;
      } 
        // Fall back to the original output method
        // @ts-ignore
      return originalOutput.call(this, type, options);
      
    };
    
    return pdfDocument;
  } catch (error) {
    console.error('Error generating PDF:', error);
    
    // Fallback to the client-side PDF generation if the server request fails
    // return generatePdf_OLD(headerHtml, bodyHtml, footerHtml);
    // return empty docu

    throw error;
  }
}
export async function generatePdf_OLD(headerHtml: string, bodyHtml: string, footerHtml: string): Promise<jsPDF> {
  // eslint-disable-next-line new-cap
  const pdfDocument = new jsPDF({
    format: 'letter',
    unit: 'cm',
    userUnit: 300,
  });
  const headerMargin = 1.5;
  const footerMargin = 1.5;
  const pageWidth = pdfDocument.internal.pageSize.getWidth();
  const pageHeight = pdfDocument.internal.pageSize.getHeight();
  const scaledWidth = pageWidth * 0.8; // Reduce width by 20%
  const margin = pageWidth * 0.1;
  const header = await generateImageFromHTML(headerHtml, toPixel(pageWidth));
  const headerHeight = toCentimeter(Number(header.getAttribute('height')));
  const scaledHeaderHeight = headerHeight * (scaledWidth / pageWidth);
  const headerData = headerHtml ? header.toDataURL('image/png') : '';
  if (headerHtml) {
    pdfDocument.addImage(headerData, 'PNG', margin, headerMargin, scaledWidth, scaledHeaderHeight);
  }

  const footer = await generateImageFromHTML(footerHtml, toPixel(pageWidth));
  const footerHeight = toCentimeter(Number(footer.getAttribute('height')));
  const scaledFooterHeight = footerHeight * (scaledWidth / pageWidth);
  const footerData = footerHtml ? footer.toDataURL('image/png') : '';
  if (footerHtml) {
    pdfDocument.addImage(
      footerData,
      'PNG',
      margin,
      pageHeight - scaledFooterHeight - footerMargin,
      scaledWidth,
      scaledFooterHeight,
    );
  }

  if (bodyHtml !== '') {
    const heightAvailable = pageHeight - scaledHeaderHeight - scaledFooterHeight - headerMargin - footerMargin - 0.5 - 0.5; // 1 cm padding between footer and body
    const yCoordinate = scaledHeaderHeight + headerMargin + 0.5; // 0.5 cm padding between header and body
    const htmlPages = bodyHtml?.split('<!-- pagebreak -->');
    if (htmlPages) {
      /* eslint-disable no-await-in-loop */
      for (let i = 0; i < htmlPages.length; i++) {
        const pageHtml = htmlPages[i];
        const body = await generateImageFromHTML(pageHtml, toPixel(pageWidth));
        const bodyWidth = toCentimeter(Number(body.getAttribute('width')));
        const bodyHeight = toCentimeter(Number(body.getAttribute('height')));
        const scaledBodyHeight = bodyHeight * (scaledWidth / pageWidth);
        const scaledXAvailableHeight = heightAvailable * (pageWidth / scaledWidth);
        const noOfPages = Math.ceil(scaledBodyHeight / heightAvailable);

        if (noOfPages > 1) {
          const firstPage = cropCanvas(body, 0, toPixel(bodyWidth), toPixel(scaledXAvailableHeight));
          const firstPageData = firstPage.toDataURL('image/png');
          pdfDocument.addImage(firstPageData, 'PNG', margin, yCoordinate, scaledWidth, heightAvailable);
          for (let i = 1; i < noOfPages; i++) {
            const page = cropCanvas(
              body,
              toPixel(scaledXAvailableHeight * i),
              toPixel(pageWidth),
              toPixel(scaledXAvailableHeight),
            );
            const pageData = page.toDataURL('image/png');
            pdfDocument.addPage();
            if (headerData) pdfDocument.addImage(headerData, 'PNG', margin, headerMargin, scaledWidth, scaledHeaderHeight);
            if (footerData)
              pdfDocument.addImage(
                footerData,
                'PNG',
                margin,
                pageHeight - scaledFooterHeight - footerMargin,
                scaledWidth,
                scaledFooterHeight,
              );
            if (i + 1 === noOfPages) {
              const pageHeight = toCentimeter(Number(page.getAttribute('height')));
              const scaledPageHeight = pageHeight * (scaledWidth / pageWidth);
              pdfDocument.addImage(pageData, 'PNG', margin, yCoordinate, scaledWidth, scaledPageHeight);
              break;
            } else {
              pdfDocument.addImage(pageData, 'PNG', margin, yCoordinate, scaledWidth, heightAvailable);
            }
          }
        } else {
          const bodyData = body.toDataURL('image/png');
          pdfDocument.addImage(bodyData, 'PNG', margin, yCoordinate, scaledWidth, scaledBodyHeight);
        }

        if (i < htmlPages.length - 1) {
          pdfDocument.addPage();
          if (headerData) pdfDocument.addImage(headerData, 'PNG', margin, headerMargin, scaledWidth, scaledHeaderHeight);
          if (footerData)
            pdfDocument.addImage(
              footerData,
              'PNG',
              margin,
              pageHeight - scaledFooterHeight - footerMargin,
              scaledWidth,
              scaledFooterHeight,
            );
        }
      }
    }
    /* eslint-enable no-await-in-loop */
  }
  return pdfDocument;
}

export async function generateImage(headerHtml: string, footerHtml: string): Promise<File> {
  const pageWidth = 850;
  const pageHeight = 1100;
  const scaledWidth = pageWidth * 0.8; // Reduce width by 20%
  const margin = pageWidth * 0.1;
  const mainDiv = document.createElement('div');
  const random = Math.floor(Math.random() * 1000000);
  mainDiv.id = `dynamicDiv${random}`;
  mainDiv.style.width = `${pageWidth}px`;
  mainDiv.style.zIndex = '-999';

  const footerHtmlWithPadding = `${transformUnderlinedHtml(footerHtml)}<p>&nbsp;</p>`;
  const footer = await generateImageFromHTML(footerHtmlWithPadding, pageWidth);
  const footerHeight = Number(footer.getAttribute('height'));
  const headerHeight = pageHeight - footerHeight;

  const headerDiv = document.createElement('div');
  headerDiv.style.width = `${scaledWidth}px`;
  headerDiv.style.height = `${headerHeight}px`;
  headerDiv.style.paddingTop = '57px';
  headerDiv.style.paddingLeft = '85px';
  headerDiv.innerHTML = transformUnderlinedHtml(headerHtml);
  mainDiv.appendChild(headerDiv);

  const footerDiv = document.createElement('div');
  footerDiv.style.width = `${scaledWidth}px`;
  footerDiv.style.paddingLeft = '85px';
  footerDiv.innerHTML = footerHtmlWithPadding;
  mainDiv.appendChild(footerDiv);
  document.body.appendChild(mainDiv);
  // @ts-ignore
  const canvas = await window.html2canvas(mainDiv, { removeContainer: true, x: 0, y: 0, width: 859, scale: 1 });
  document.body.removeChild(mainDiv);
  // const imgData = canvas.toDataURL('image/png');
  const canvasBlob = await new Promise((resolve) => canvas.toBlob(resolve));
  const image = new File([canvasBlob as BlobPart], 'image.png', { type: 'image/png' });
  return image;
}

// Function to wrap root level text in <u> tag
function wrapTextNodesRecursively(node: any, doc: any) {
  // Use a regular for-loop because childNodes will change if we replace nodes
  for (let i = 0; i < node.childNodes.length; i++) {
    const child = node.childNodes[i];

    // If it's a text node with non-whitespace content, wrap it in <u>
    if (child.nodeType === Node.TEXT_NODE && child.nodeValue.trim().length > 0) {
      const u = doc.createElement('u');
      u.textContent = child.nodeValue; // Copy the text
      child.replaceWith(u);
      // After replaceWith, `child` is no longer in the DOM.
    } else if (child.nodeType === Node.ELEMENT_NODE) {
      // Recurse into child elements
      wrapTextNodesRecursively(child, doc);
    }
  }
}

// Function to wrap root level text in <u> tag
function transformUnderlinedHtml(htmlString: any) {
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlString, 'text/html');
  // Query for any inline style that has "underline"
  const underlinedElements = doc.querySelectorAll('[style*="underline"]');
  underlinedElements.forEach((element) => {
    // Remove the "underline" from the element's inline style
    // @ts-ignore
    element.style.textDecoration = element.style.textDecoration.replace('underline', '');
    // Recursively wrap all text nodes below this element
    wrapTextNodesRecursively(element, doc);
  });

  return doc.body.innerHTML;
}
