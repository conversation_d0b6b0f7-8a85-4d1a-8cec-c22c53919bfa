import React from 'react';
import {
  FormControl,
  Input,
  InputAdornment,
  InputProps,
  Typography,
  useTheme,
} from '@mui/material';
import PersonOutlineIcon from '@mui/icons-material/PersonOutline';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';

interface CustomInputProps extends InputProps {
  label?: string;
  helperText?: string;
  maxLength?: string;
}
 
const CustomInputWithLabel: React.FC<CustomInputProps> = ({
  helperText,
  error,
  name,
  sx = {},
  onClick,
  readOnly,
  label,
  onChange,
  onBlur,
  value,
  maxLength,
  disabled,
}) => {
  const theme = useTheme();
  return (
    <FormControl variant='standard' sx={{ width: '100%' }}>
      <Input
        onClick={onClick}
        name={name}
        error={error}
        disabled={disabled}
        inputProps={{ maxLength }}
          // @ts-ignore
        sx={(theme) => ({
          ...sx,
          borderRadius: '12px',
          padding: '8px 12px',
          height: '48px',
          fontSize: '14px',
          fontWeight: '400',
          backgroundColor:
            theme.palette.mode === 'dark' ? '#1E1E1E' : '#F9F9F9',
          color: theme.palette.text.primary,
          border: '1px solid transparent',
          display: 'flex',
          alignItems: 'center',
        })}
        disableUnderline
        readOnly={readOnly}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        startAdornment={
          <InputAdornment position='start'>
            <PersonOutlineIcon
              sx={{
                color: 'text.secondary',
                fontSize: 20,
              }}
            />
          </InputAdornment>
        }
        endAdornment={
          <InputAdornment position='end'>
            <ArrowDropDownIcon
              sx={{
                color: 'text.secondary',
                fontSize: 20,
              }}
            />
          </InputAdornment>
        }
      />
      {helperText && (
        <Typography
          variant='caption'
          color='error'
          sx={{ fontSize: '10px', marginTop: '4px', paddingLeft: '1rem' }}
        >
          {helperText}
        </Typography>
      )}
    </FormControl>
  );
};

export default CustomInputWithLabel;
