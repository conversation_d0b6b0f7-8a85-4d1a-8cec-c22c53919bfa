import React, { FC } from 'react';
import { Chip, Stack } from '@mui/material';

const PATIENT_PLACEHOLDERS = {
  PATIENT_NAME: { label: 'Patient Name', value: '[PATIENT_NAME]' },
  PATIENT_GENDER: { label: 'Patient Gender', value: '[PATIENT_GENDER]' },
  PATIENT_DOB: { label: 'Patient Date of Birth', value: '[PATIENT_DOB]' },
  PATIENT_AGE: { label: 'Patient Age', value: '[PATIENT_AGE]' },
  PATIENT_ADDRESS: { label: 'Patient Address', value: '[PATIENT_ADDRESS]' },
  PATIENT_EMAIL: { label: 'Patient Email', value: '[PATIENT_EMAIL]' },
  PATIENT_PHONE: { label: 'Patient Phone', value: '[PATIENT_PHONE]' },
  PATIENT_INSURANCE_PROVIDER: { label: 'Insurance Provider', value: '[PATIENT_INSURANCE_PROVIDER]' },
  PATIENT_INSURANCE_NUMBER: { label: 'Insurance Number', value: '[PATIENT_INSURANCE_NUMBER]' },
  PATIENT_REFERENCE_NUMBER: { label: 'Reference Number', value: '[PATIENT_REFERENCE_NUMBER]' },
  PATIENT_PRIMARY_PHYSICIAN: {
    label: 'Patient Primary Physician',
    value: `
  Patient Primary Physician:
  Name: [PATIENT_PRIMARY_PHYSICIAN_NAME]
  Email: [PATIENT_PRIMARY_PHYSICIAN_EMAIL]
  Address: [PATIENT_PRIMARY_PHYSICIAN_ADDRESS]
  Phone: [PATIENT_PRIMARY_PHYSICIAN_PHONE]
  Specialization: [PATIENT_PRIMARY_PHYSICIAN_SPECIALIZATION]
  `,
  },
};

interface PlaceholdersProps {
  onPlaceholdersClick: (tag: string) => void;
}

const Placeholders: FC<PlaceholdersProps> = ({ onPlaceholdersClick }) => {
  return (
    <Stack gap={1} direction="row" flexWrap="wrap">
      {Object.entries(PATIENT_PLACEHOLDERS).map(([key, { label, value }]) => (
        <Chip
          key={label}
          label={label}
          onClick={() => onPlaceholdersClick(value)}
          variant="outlined"
          sx={{
            borderRadius: '4px',
            fontSize: '12px',
            border: 'none',
            backgroundColor: (theme) => (theme.palette.mode === 'dark' ? '#000000' : '#f0f0f0'),
            cursor: 'pointer',
            '& .MuiChip-label': {
              whiteSpace: 'normal',
              overflow: 'visible',
              textOverflow: 'clip',
            },
          }}
        />
      ))}
    </Stack>
  );
};

export default Placeholders;
