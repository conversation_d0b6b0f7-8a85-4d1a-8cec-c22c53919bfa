import { <PERSON>, <PERSON><PERSON>, Stack, Typography, useTheme } from '@mui/material';
import React, { FC, useEffect } from 'react';
// @ts-ignore
import pell from 'pell';
import 'pell/dist/pell.css';

interface Props {
  structure: string;
  onNotesChange: (html: string) => void;
  onNotesClick: (e: any) => void;
  onNotesBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  isNextDisabled: boolean;
  setStep: (step: number) => void;
  templateRef: React.RefObject<HTMLDivElement>;
}

const RightPane2: FC<Props> = ({
                                 templateRef,
                                 onNotesBlur,
                                 onNotesChange,
                                 structure,
                                 onNotesClick,
                                 isNextDisabled,
                                 setStep,
                               }) => {
  const theme = useTheme();
  const editorRef = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (editorRef.current) {
      pell.init({
        element: editorRef.current,
        onChange: (html:any) => {
          // Optional: strip empty lines
          const cleanedHtml = html.replace(/<p><br><\/p>/g, '');
          onNotesChange(cleanedHtml);
        },
        actions: ['bold', 'italic', 'underline', 'olist', 'ulist'],
        styleWithCSS: true,
      });

      const contentEl = editorRef.current.querySelector('.pell-content') as HTMLElement;
      if (contentEl) {
        contentEl.innerHTML = structure;

        const style = document.createElement('style');
        style.textContent = `
          .pell-content p {
            margin: 0;
            padding: 0;
            line-height: 1.4;
          }
          .pell-content br {
            line-height: 0.5;
          }
        `;
        document.head.appendChild(style);
      }
    }
  }, []);

  useEffect(() => {
    if (editorRef.current) {
      const contentEl = editorRef.current.querySelector('.pell-content');
      if (contentEl && contentEl.innerHTML !== structure) {
        contentEl.innerHTML = structure;
      }
    }
  }, [structure]);

  return (
    <Box
      data-testid="right-pane-container"
      sx={{
        backgroundColor: theme.palette.mode === 'dark' ? '#1E1E1E' : '#FFFFFF',
        border: '1px solid #F9F9F9',
        padding: '20px',
        borderRadius: '8px',
      }}
    >
      <Stack direction="column">
        <Typography
          variant="h6"
          data-testid="template-label"
          sx={{
            fontSize: '16px',
            fontWeight: '700',
            marginBottom: '14.87px',
          }}
        >
          Template*
        </Typography>

        <Stack direction="row" spacing={2} sx={{ height: '85%', marginBottom: '16px' }}>
          <Box
            ref={editorRef}
            onClick={onNotesClick}
            sx={{
              width: '100%',
              backgroundColor: theme.palette.mode === 'dark' ? '#1E1E1E' : '#F9F9F9',
              borderRadius: '4px',
              border: '1px solid #E2E2E2',
              '& .pell-content': {
                padding: '14px',
                fontSize: '13px',
                color: theme.palette.text.primary,
                minHeight: '400px',
                lineHeight: '1.4',
              },
              '& .pell-actionbar': {
                backgroundColor: theme.palette.mode === 'dark' ? '#2A2A2A' : '#FFFFFF',
                borderBottom: `1px solid ${theme.palette.divider}`,
              },
              '& .pell-button': {
                backgroundColor: 'transparent',
                color: theme.palette.text.primary,
              },
            }}
          />
        </Stack>

        <Button
          data-testid="test-template-button"
          disabled={isNextDisabled}
          fullWidth
          sx={{
            height: '50px',
            borderRadius: '100px',
            fontWeight: '600',
            fontSize: '14px',
            textTransform: 'none',
          }}
          onClick={() => setStep(2)}
        >
          Test Template
        </Button>
      </Stack>
    </Box>
  );
};

export default RightPane2;
