// @ts-nocheck
import React, { useEffect, useState } from 'react';
import { useHistory, useLocation, useParams } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';
import { Backdrop, Box, Button, CircularProgress, Stack, Typography, useTheme } from '@mui/material';

import { enqueueSnackbarAction, setBreadcrumbs } from 'actions/app.action';
import CustomAppBar from 'features/Tasks/CustomAppBar';
import { clinicIdSelector } from 'selectors/auth.selector';
import promptService from 'services/llmPromptService';
import sampleTemplate from 'themes/svgs/SampleTemplate.svg';
import SaveOutlinedIcon from '@mui/icons-material/SaveOutlined';
import LeftPane from './LeftPane';
import LeftPane2 from './LeftPane2';
import RightPane2 from './RightPane2';
import { canAction } from '../../../helpers';
import { PERMISSIONS } from '../../../configs';

const PromptAdd: React.FC = () => {
  const [hovered, setHovered] = useState(false);
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<number>(1);
  const [name, setName] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [structure, setStructure] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [sampleLetter, setSampleLetter] = useState<string>('');
  const [descriptionCursor, setDescriptionCursor] = useState<number>(0);
  const [fistPageData, setFirstPageData] = useState<boolean>(false);
  const [showSaveButton, setShowSaveButton] = useState<Boolean>(false);

  const templateRef = React.useRef<HTMLDivElement | null>(null);

  const dispatch = useDispatch();
  const history = useHistory();
  const params = useParams<{ id: string }>();
  const clinicId = useSelector(clinicIdSelector);
  useEffect(() => {
    if (templateRef.current) {
      if (templateRef.current.innerHTML !== structure) {
        templateRef.current.innerHTML = structure;
      }
    }
  }, [structure, templateRef]);

  const onNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let { value } = e.target;

    if (value.startsWith(' ')) {
      value = value.substring(1);
    }
    value = value.replace(/\s{2,}/g, ' ');
    setName(value);
  };

  const onDescriptionChange = (e: any) => {
    setDescription(e.target.value);
  };
  const onStructureChange = (html: string) => {
    setStructure(html);
    // Remove cursor position logic if not applicable
  };
  const onNotesChange = (e: any) => {
    setNotes(e.target.value);
  };

  const onStepChange = (step: number) => {
    setStep(step);
  };

  const handleDownload = async () => {};

  const verifyData = () => {
    if (name === '') {
      dispatch(
        enqueueSnackbarAction({
          message: 'Please provide name of the template',
          key: new Date().getTime() + Math.random(),
          variant: 'error',
        }),
      );
      return false;
    }
    if (description.trim() === '') {
      dispatch(
        enqueueSnackbarAction({
          message: 'Please provide description of the template',
          key: new Date().getTime() + Math.random(),
          variant: 'error',
        }),
      );
      return false;
    }
    if (structure.trim() === '') {
      dispatch(
        enqueueSnackbarAction({
          message: 'Please provide structure of the template',
          key: new Date().getTime() + Math.random(),
          variant: 'error',
        }),
      );
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    if (!verifyData()) {
      return;
    }
    if (!clinicId) {
      dispatch(
        enqueueSnackbarAction({
          message: 'Please select clinic',
          key: new Date().getTime() + Math.random(),
          variant: 'error',
        }),
      );
      setLoading(false);
      return;
    }
    setLoading(true);
    if (params.id && params.id !== '0') {
      const response = await promptService.update(Number(params.id), name.trim(), description, structure, clinicId);
      history.push('/templates');
    } else {
      const response = await promptService.create(name.trim(), description, structure, false, clinicId);
      dispatch(
        enqueueSnackbarAction({
          message: 'Template created successfully!',
          key: new Date().getTime() + Math.random(),
          variant: 'success',
        }),
      );
      history.push('/templates');
    }
  };

  const createSampleLetter = async () => {
    if (!verifyData()) {
      return;
    }
    if (!notes) {
      dispatch(
        enqueueSnackbarAction({
          message: 'Please provide notes',
          key: new Date().getTime() + Math.random(),
          variant: 'error',
        }),
      );
      setLoading(false);
      return;
    }
    setLoading(true);
    const response = await promptService.sampleNotes(name, description, structure, notes);
    if (response) {
      setSampleLetter(response.letter);
    }
    setLoading(false);
  };
  const convertTextToQuillHTML = (text: string) => {
    return text
    .split('\n')
    .map(paragraph => {
      if (paragraph.trim() === '') return '<p><br></p>';
      return `<p>${paragraph.replace(/ {2}/g, ' &nbsp;')}</p>`;
    })
    .join('');
  };

  const getPrompt = async () => {
    setLoading(true);
    try {
      const prompt = await promptService.view(Number(params.id));
      setName(prompt.name);
      setDescription(prompt.description);

      // Convert legacy text format to Quill HTML

      console.log(prompt.prompt);
      const convertedStructure = prompt.prompt.includes('<p>')
        ? prompt.prompt
        : convertTextToQuillHTML(prompt.prompt);
      console.log(convertedStructure);
      setStructure(convertedStructure);
      setShowSaveButton(!!prompt.clinic_id);
    } finally {
      setLoading(false);
    }

  };


  useEffect(() => {
    if (!params.id || params.id === '0') {
      if (!canAction('do', PERMISSIONS.TEMPLATE_ADD)) {
        history.push('/403');
      }
      dispatch(setBreadcrumbs('Template / Create Template'));
      setShowSaveButton(true);
    }

    if (params.id && params.id !== '0') {
      if (!canAction('do', PERMISSIONS.TEMPLATE_EDIT)) {
        history.push('/403');
      }

      getPrompt().then((e) => {
        if (e) {
          dispatch(setBreadcrumbs('Template / Edit Template'));
        } else {
          dispatch(setBreadcrumbs('Template / View Template'));
        }
      });
    }

    return () => {
      dispatch(setBreadcrumbs(''));
    };
  }, []);

  const Toolbar = ({ canSave }: { canSave: boolean }) => {
    return (
      <Box>
        <CustomAppBar
          leftContent={
            // <Typography
            //   variant="h6"
            //   noWrap
            //   component="div"
            //   sx={{ flexGrow: 1, display: { sm: 'block' }, fontSize: '18px', fontFamily: 'UrbanistMedium' }}
            // >
            //   {showSaveButton ? 'Edit Template' : `View Template${!params.id || params.id === '0' ? ' Create Template' : ''}`}
            // </Typography>
            <></>
          }
          rightContent={
            showSaveButton && (
              <Button
                endIcon={<SaveOutlinedIcon sx={{ opacity: !canSave ? 1 : 0.5 }} />}
                disabled={canSave}
                onClick={() => handleSave()}
                onMouseEnter={() => setHovered(true)}
                onMouseLeave={() => setHovered(false)}
                className="invite-user-button"
                sx={(theme) => ({
                  background: 'none',
                  '&:hover': { border: 'none', boxShadow: 'none' },
                  color: theme?.customProperties.ButtonTextColor,
                })}
              >
                Save
              </Button>
            )
          }
        />
      </Box>
    );
  };

  const RightPane = () => {
    const theme = useTheme();
    return (
      <>
        <Box
          data-testid="sample-document-box"
          sx={{
            backgroundColor: theme.palette.mode === 'dark' ? '#1E1E1E' : '#FFFFFF',
            padding: '24px',
            border: '1px solid #F9F9F9',
            borderRadius: '8px',
            width: '100%',
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontSize: '16px',
              fontWeight: '500',
              marginBottom: '16px',
            }}
            data-testid="sample-document-heading"
          >
            Sample Document
          </Typography>

          <Box
            data-testid="sample-document-content"
            sx={{
              width: '100%',
              padding: '20px',
              borderRadius: '8px',
              backgroundColor: theme.palette.mode === 'dark' ? '#2A2A2A' : '#F9F9F9',
              border: '1px solid #E2E2E2',
              minHeight: '300px',
            }}
          >
            {!sampleLetter && (
              <Box
                component="img"
                src={sampleTemplate}
                alt="Sample Template"
                width="50%"
                height="60%"
                sx={{ display: 'block', margin: '0 auto' }}
                data-testid="sample-document-image"
              />
            )}

            {sampleLetter && (
              <Box
                data-testid="sample-document-letter"
                sx={{ overflow: 'auto', maxHeight: '500px' }}
                dangerouslySetInnerHTML={{ __html: sampleLetter?.letter }}
              />
            )}
          </Box>
        </Box>
      </>
    );
  };

  const onDescriptionClick = (e: any) => {
    setDescriptionCursor(e.target.selectionStart);
  };

  const onPlaceholdersClick = (label: string) => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      // Check if selection is inside the Pell editor
      const pellContent = document.querySelector('.pell-content');
      if (pellContent && pellContent.contains(range.commonAncestorContainer)) {
        const placeholder = document.createTextNode(` ${label} `);
        range.insertNode(placeholder);
        range.collapse(false); // Move cursor after placeholder
      }
    }
  };

  useEffect(() => {
    templateRef?.current?.setSelectionRange(descriptionCursor, descriptionCursor);
  }, [structure]);

  return (
    <Stack direction="column" sx={{ height: '100%' }}>
      <Backdrop sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }} open={loading}>
        <CircularProgress color="inherit" />
      </Backdrop>
      <Toolbar canSave={!(name && description && structure)} />
      <Stack direction={{ xs: 'column', sm: 'row' }} justifyContent="space-between" spacing={2} sx={{ paddingTop: '30px' }}>
        {step === 1 ? (
          <LeftPane
            name={name}
            description={description}
            onNameChange={onNameChange}
            setStep={onStepChange}
            onDescriptionChange={onDescriptionChange}
            onPlaceholdersClick={onPlaceholdersClick}
          />
        ) : (
          <LeftPane2
            structure={structure}
            notes={notes}
            onStructureChange={onStructureChange}
            onNotesChange={onNotesChange}
            setStep={onStepChange}
            generateSample={createSampleLetter}
          />
        )}
        <Stack direction="column" sx={{ width: { xs: '100%', md: '50%' } }}>
          {step === 2 ? (
            <RightPane />
          ) : (
            <RightPane2
              templateRef={templateRef}
              structure={structure}
              onNotesChange={onStructureChange}
              onNotesClick={onDescriptionClick}
              setStep={setStep}
              isNextDisabled={!(name && description && structure)}
            />
          )}
        </Stack>
      </Stack>
    </Stack>
  );
};

export default PromptAdd;
