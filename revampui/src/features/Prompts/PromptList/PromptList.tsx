import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableContainer,
  Paper,
  Box,
  Typography,
  Backdrop,
  CircularProgress,
  TableCell,
  Stack,
  Button,
  Pagination,
  Menu,
  MenuItem,
} from '@mui/material';
import styled from 'styled-components';

import EditIcon from 'themes/icons/EditIcon';
import promptService from 'services/llmPromptService';
import { clinicIdSelector, planTypeSelector, remainingAudioSecondsSelector, remainingCustomTemplateSelector } from 'selectors/auth.selector';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';
import { visuallyHidden } from '@mui/utils';
import { Grid, Tabs, useTheme, Tab } from '@material-ui/core';
import ViewIcon from 'themes/icons/ViewIcon';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';
import BorderAllIcon from '@mui/icons-material/BorderAll';
import SearchIcon from '@mui/icons-material/Search';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import TrashIcon from 'themes/icons/TrashIcon';
import { enqueueSnackbarAction } from 'actions/app.action';
import GridViewIcon from 'themes/icons/GridViewIcon';
import BulletListIcon from 'themes/icons/BulletListIcon';
import Card from './Card';
import { ButtonContainer, StyledTableCell, StyledTableRow, styles, TablePaginationContainer } from './StyledComponents';
import NoRecordsRow from '../../../components/molecules/NoRecordsMessage/NoRecordsMessage';
import { PERMISSIONS } from '../../../configs';
import { canAction } from '../../../helpers';
import CustomAppBar from '../../Tasks/CustomAppBar';
import { Search, SearchIconWrapper, StyledInputBase } from '../../../components/molecules/SearchBar/SearchBar';
import { getTranscriptSelector } from '../../../selectors/clinicalLetter.selector';
import AlertDialogSlide from '../../../components/molecules/ConfirmationDialog';

interface Prompt {
  id: number;
  name: string;
  description: string;
  is_favourite: boolean;
  clinic_id: string;
}

const headCells = [
  {
    id: 'id',
    numeric: true,
    disablePadding: false,
    label: 'Template ID',
    sort: true,
  },
  {
    id: 'name',
    numeric: true,
    disablePadding: false,
    label: 'Name',
    sort: true,
  },
  {
    id: 'description',
    numeric: true,
    disablePadding: false,
    label: 'Description',
    sort: false,
  },
  {
    id: 'type',
    numeric: true,
    disablePadding: false,
    label: 'Type',
    sort: false,
  },
];

function EnhancedTableHead(props: any) {
  const { order, orderBy, onRequestSort } = props;
  const createSortHandler = (property: any, canBeSorted: boolean) => (event: any) => {
    if (canBeSorted) {
      onRequestSort(event, property);
    }
  };
  const theme = useTheme();
  return (
    <TableHead>
      <TableRow>
        {headCells.map((headCell, i) => {
          const _sortOrder = orderBy === headCell.id ? order : false;
          return (
            <TableCell
              key={headCell.id}
              variant="head"
              sx={{ color: ' rgba(0, 0, 0, 0.6)' }}
              padding={headCell.disablePadding ? 'none' : 'normal'}
              sortDirection={headCell.sort ? _sortOrder : false}
            >
              <TableSortLabel
                active={orderBy === headCell.id}
                direction={orderBy === headCell.id ? order : 'asc'}
                onClick={createSortHandler(headCell.id, headCell.sort)}
                sx={{
                  '&.MuiTableSortLabel-root': {
                    color: theme.palette.type === 'dark' ? '#ffffff' : '#000000',
                  },
                }}
              >
                {headCell.label}
                {orderBy === headCell.id ? (
                  <Box component="span" sx={visuallyHidden}>
                    {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                  </Box>
                ) : null}
              </TableSortLabel>
            </TableCell>
          );
        })}
      </TableRow>
    </TableHead>
  );
}

const PromptList: React.FC = () => {

  const ITEM_HEIGHT = 48;
  const options = ['All Templates', 'System Templates', 'Custom Templates'];

  const theme = useTheme();
  const history = useHistory();
  const dispatch = useDispatch();

  const clinicId = useSelector(clinicIdSelector) || '';
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [hoveredRowIndex, setHoveredRowIndex] = useState<number | null>(null);
  const [search, setSearch] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<string>('desc');
  const [sortBy, setSortBy] = useState<string>('id');
  const [isGridSelected, setIsGridSelected] = useState<boolean>(false);
  const [totalRecords, setTotalRecords] = useState<number>(1000);
  const [selectedOption, setSelectedOption] = useState(options[0]);
  const [isGrid, setIsGrid] = useState(false);
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  // delete Dialog States
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPrompt, setSelectedPrompt] = useState<{ id: number; clinic_id: string } | null>(null);

  const openMenu = Boolean(anchorEl);
  const customTemplatesLimit: number = useSelector(remainingCustomTemplateSelector);
  const planType :string = useSelector(planTypeSelector);
  const fetchPrompts = async () => {
    try {
      const searchStr = search.length > 2 ? search : '';
      let type = '';
      switch (selectedOption) {
        case 'All Templates':
          type = '';
          break;
        case 'Custom Templates':
          type = 'custom';
          break;
        default:
          type = 'system';
      }

      const data = await promptService.list(clinicId as string, searchStr, sortOrder, sortBy, page, type);
      setIsLoading(false);
      if (data) {
        setPrompts(data.prompts);
        setTotalRecords(data.totalCount);
      }
    } catch (error) {
      // Handle error here
    }
  };

  const handleRowMouseEnter = (index: number) => {
    if (canAction('do', PERMISSIONS.TEMPLATE_EDIT)) {
      setHoveredRowIndex(index);
    }
  };

  const handleRowMouseLeave = () => {
    setHoveredRowIndex(null);
  };

  const editPrompt = (id: number, clinicId: string) => {
    history.push({
      pathname: `/templates/add/${id}`,
    });
  };

  const deletePrompt = async (id: number, clinicId: string) => {
    try {
      await promptService.delete(id, clinicId as string);
      dispatch(
        enqueueSnackbarAction({
          message: 'Template deleted successfully',
          key: new Date().getTime() + Math.random(),
          variant: 'success',
        }),
      );
      await fetchPrompts();
      setIsLoading(false);
    } catch (e) {
      dispatch(
        enqueueSnackbarAction({
          message: 'Error Deleting Template',
          key: new Date().getTime() + Math.random(),
          variant: 'error',
        }),
      );
      setIsLoading(false);
    }
  };

  // Called when user clicks delete on a card
  const confirmDeletePrompt = (id: number, clinic_id: string) => {
    setSelectedPrompt({ id, clinic_id });
    setIsDeleteDialogOpen(true);
  };

  // Called when user confirms deletion in the dialog
  const handleDeleteConfirmed = async () => {
    if (selectedPrompt) {
      await deletePrompt(selectedPrompt.id, selectedPrompt.clinic_id);
    }
    setIsDeleteDialogOpen(false);
    setSelectedPrompt(null);
  };

  // Called when user cancels deletion
  const handleDeleteCanceled = () => {
    setIsDeleteDialogOpen(false);
    setSelectedPrompt(null);
  };

  useEffect(() => {
    fetchPrompts();
  }, [search, sortOrder, sortBy, page, selectedOption]);

  const handleChangePage = (event: any, newPage: any) => {
    setPage(newPage);
  };

  const onGridChange = (value: any) => {
    setIsGridSelected(value);
    setIsGrid(value);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleClickMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuItemClicked = (e: any) => {
    setAnchorEl(null);
    setPage(1);
    setSelectedOption(e);
  };
  const handleRequestSort = (event: any, property: string) => {
    const isAsc = sortBy === property && sortOrder === 'asc';
    setSortOrder(isAsc ? 'desc' : 'asc');
    setSortBy(property);
  };

  return (
    <>
      <AlertDialogSlide
        open={isDeleteDialogOpen}
        title="Confirm Delete"
        content="Are you sure you want to delete this template?"
        handleOk={handleDeleteConfirmed}
        handleCancel={handleDeleteCanceled}
      />
      <Stack direction="row" sx={{ display: 'flex', justifyContent: 'right', marginBottom: '16px' }}>
        {canAction('do', PERMISSIONS.TEMPLATE_ADD) && (
          <Stack sx={{ display: 'flex', flexDirection: 'column' }}>
            <Button
              sx={(theme) => ({
                '&:hover': {
                  boxShadow:
                    '0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12)',
                  backgroundColor: '#252157',
                  color: '#FFFFFF',
                },
                '&:disabled': {},
                display: 'flex',
                height: '50px',

                justifyContent: 'center',
                alignItems: 'center',
                gap: '6px',
                flexShrink: 0,
                alignSelf: 'stretch',
                width: '200px',
                backgroundColor: '#2E2A62',
                color: '#FFFFFF',
                textTransform: 'none',
                padding: theme.spacing(1, 3),
                borderRadius: '8px',
                fontSize: '14px',
              })}
              onClick={() => {
                history.push({
                  pathname: '/templates/add/0',
                });
              }}
              disabled={customTemplatesLimit <= 0}
            >
              Create New Template
            </Button>
            <Typography
              sx={{ fontWeight: '400', padding: '5px', color: customTemplatesLimit === 0 ? 'red' : '', fontSize: '13px' }}
            >
              {planType !== 'Pro' && (customTemplatesLimit === 0
                ? 'Template Creation Limit Reached'
                : `Custom Template Limit Left: ${customTemplatesLimit}`)}
            </Typography>
          </Stack>
        )}
      </Stack>
      <Stack
        direction="column"
        sx={{
          backgroundColor: theme.palette.type === 'dark' ? '#222' : '#f3f6f6',
          padding: '20px',
          borderRadius: '16px',
          color: theme.palette.type === 'dark' ? 'white' : 'black',
        }}
      >
        <CustomAppBar
          rightContent={
            <Box>
              <Toolbar
                className="tool-bar-right"
                sx={{
                  pl: { sm: 2 },
                  pr: { xs: 1, sm: 1 },
                  borderRadius: '8px',
                }}
              >
                <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, display: { xs: 'none', sm: 'block' } }} />
                <Search className="search-icon-bar">
                  <SearchIconWrapper>
                    <SearchIcon />
                  </SearchIconWrapper>
                  <StyledInputBase
                    placeholder="Search…"
                    inputProps={{ 'aria-label': 'search' }}
                    onChange={(e) => setSearch(e.target.value)}
                  />
                </Search>
                <IconButton
                  size="large"
                  edge="start"
                  color="inherit"
                  aria-label="open drawer"
                  sx={{
                    mr: 2,
                    fontWeight: isGrid ? 'bold' : 'normal',
                    backgroundColor: isGrid ? theme.custom.navbarBackground : theme.custom.hover,
                  }}
                  onClick={() => onGridChange(false)}
                >
                  {' '}
                  <BulletListIcon />
                </IconButton>
                <IconButton
                  size="large"
                  edge="start"
                  color="inherit"
                  aria-label="open drawer"
                  sx={{
                    mr: 2,
                    fontWeight: !isGridSelected ? 'bold' : 'normal',
                    backgroundColor: !isGridSelected ? theme.custom.navbarBackground : theme.custom.hover,
                  }}
                  onClick={() => onGridChange(true)}
                >
                  <GridViewIcon />
                </IconButton>
              </Toolbar>
            </Box>
          }
          leftContent={
            <Toolbar>
              {/* <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, display: { sm: 'block' }, fontSize: '18px' }}>
                {selectedOption}
                <span>
                  <IconButton
                    aria-label="more"
                    id="long-button"
                    aria-controls={openMenu ? 'long-menu' : undefined}
                    aria-expanded={openMenu ? 'true' : undefined}
                    aria-haspopup="true"
                    onClick={handleClickMenu}
                  >
                    <ExpandMoreIcon />
                  </IconButton>
                  <Menu
                    id="long-menu"
                    MenuListProps={{
                      'aria-labelledby': 'long-button',
                    }}
                    anchorEl={anchorEl}
                    open={openMenu}
                    onClose={handleClose}
                    PaperProps={{
                      style: {
                        maxHeight: ITEM_HEIGHT * 4.5,
                        width: '20ch',
                      },
                    }}
                  >
                    {options.map((option) => (
                      <MenuItem key={option} selected={option === selectedOption} onClick={() => handleMenuItemClicked(option)}>
                        {option}
                      </MenuItem>
                    ))}
                  </Menu>
                </span>
              </Typography> */}
              <Tabs
                value={selectedOption}
                onChange={(event, newValue) => setSelectedOption(newValue)}
                indicatorColor="primary"
                textColor="primary"
                variant="scrollable"
                scrollButtons="auto"
                className="tab-clinic"
              >
                <Tab label="All Templates" value="All Templates" />
                <Tab label="System Templates" value="System Templates" />
                <Tab label="Custom Templates" value="Custom Templates" />
              </Tabs>
            </Toolbar>
          }
        />
        {!isGridSelected && (
          <Paper sx={{ width: '100%', mb: 2, boxShadow: 'none', marginTop: '20px' }}>
            <Box sx={styles.container}>
              <Backdrop sx={styles.backdrop} open={isLoading}>
                <CircularProgress color="inherit" />
              </Backdrop>
              {/* <FormDialog open={dialogOpen} onClose={handleClose} onSubmit={handleFormSubmit} /> */}
              <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
                <Table className="task-table">
                  <EnhancedTableHead order={sortOrder} orderBy={sortBy} onRequestSort={handleRequestSort} />
                  <TableBody>
                    {prompts.length === 0 ? (
                      <NoRecordsRow />
                    ) : (
                      prompts.map((row, index) => (
                        <StyledTableRow
                          hover
                          key={row.id}
                          onMouseEnter={() => handleRowMouseEnter(index)}
                          onMouseLeave={handleRowMouseLeave}
                        >
                          <StyledTableCell>{row.id}</StyledTableCell>
                          <StyledTableCell>{row.name}</StyledTableCell>
                          <StyledTableCell>{row.description}</StyledTableCell>
                          {!(hoveredRowIndex === index) && (
                            <StyledTableCell>
                              <div
                                style={{
                                  padding: '10px',
                                  background: '#171819',
                                  borderRadius: '20px',
                                  color: '#fff',
                                  width: 'min-content',
                                  backgroundColor: row.clinic_id ? 'green' : '#1976d2',
                                  display: 'inline-flex',
                                  height: '36px',
                                  alignItems: 'center',
                                }}
                              >
                                {row.clinic_id ? 'Custom' : 'System'}
                              </div>
                            </StyledTableCell>
                          )}
                          {hoveredRowIndex === index && (
                            <StyledTableCell align="left">
                              <div style={{ display: 'inline-flex', height: '36px', alignItems: 'center' }}>
                                {row.clinic_id ? (
                                  <>
                                    <EditIcon onClick={() => editPrompt(row.id, row.clinic_id)} />
                                    <TrashIcon onClick={() => confirmDeletePrompt(row.id, row.clinic_id)} />
                                  </>
                                ) : (
                                  <ViewIcon onClick={() => editPrompt(row.id, row.clinic_id)} />
                                )}
                              </div>
                            </StyledTableCell>
                          )}
                        </StyledTableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          </Paper>
        )}
        {isGridSelected && (
          <div style={{ marginTop: '20px' }}>
            {prompts.length !== 0 ? (
              <Grid container spacing={3}>
                {prompts.map((row, index) => (
                  <Grid item xs={12} sm={6} md={4} key={`card-${index}`}>
                    <Card
                      row={row}
                      key={`card-${index}`}
                      handleEdit={() => editPrompt(row.id, row.clinic_id)}
                      // handleDelete={() => deletePrompt(row.id, row.clinic_id)}
                      handleDelete={() => confirmDeletePrompt(row.id, row.clinic_id)}
                    />
                  </Grid>
                ))}
              </Grid>
            ) : (
              <div className="no-records">
                <NoRecordsRow />
              </div>
            )}
          </div>
        )}
        <TablePaginationContainer>
          <Pagination
            count={Math.ceil(totalRecords / 10)}
            page={page}
            onChange={handleChangePage}
            color="primary"
            shape="rounded"
            sx={{
              ...styles.pagination,
              '& .MuiPaginationItem-icon': {
                color: theme.palette.text.primary,
              },
            }}
          />
        </TablePaginationContainer>
      </Stack>
    </>
  );
};

export default PromptList;
