import React from 'react';
import '../../../Tasks/Card/style.css';
import { Button, Menu, MenuItem, Stack, useTheme } from '@mui/material';
import { useHistory } from 'react-router';
import Typography from '@mui/material/Typography';
import TrashIcon from 'themes/icons/TrashIcon';
import SpreadIcon from 'themes/icons/SpreadIcon';
import ViewIcon from 'themes/icons/ViewIcon';
import EditIcon from 'themes/icons/EditIcon';

interface CardProps {
  row: any;
  handleEdit: () => void;
  handleDelete: () => void;
}
  const Card = ({ row, handleEdit, handleDelete }: CardProps) => {
  const theme = useTheme();

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div className="component" style={{ background: theme.palette.background.paper }}>
      <div className="frame">
        <div className="div" />
        <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
          <div
            style={{
              background: '#171819',
              borderRadius: '4px',
              color: '#fff',
              width: 'min-content',
              padding: '4px',
              fontWeight: '500',
              height: 'fit-content',
              fontSize: '10px',
              display: 'flex',
              alignItems: 'center',
              backgroundColor: row.clinic_id ? 'green' : '#1976d2',
            }}
          >
            {' '}
            {row.clinic_id ? 'Custom' : 'System'}
          </div>

          <div>
            <Button
              sx={{
                background: 'none',
                '&:hover': { background: 'none', border: 'none', boxShadow: 'none' },
              }}
              id="basic-button"
              aria-controls={open ? 'basic-menu' : undefined}
              aria-haspopup="true"
              aria-expanded={open ? 'true' : undefined}
              onClick={handleClick}
            >
              <SpreadIcon />
            </Button>

            <Menu sx={{ borderRadius: '20px !important' }} id="basic-menu" anchorEl={anchorEl} open={open} onClose={handleClose}>
              <MenuItem
                onClick={() => {
                  handleEdit();
                  handleClose();
                }}
              >
                <Stack direction="row" gap="10px" sx={{}} alignItems="center">
                  {row.clinic_id ? (
                    <Stack direction="row" alignItems="center">
                      <EditIcon fontSize="10px" />
                      <div style={{ fontSize: '12px', fontWeight: '600' }}>Edit</div>
                    </Stack>
                  ) : (
                    <Stack direction="row" alignItems="center">
                      <ViewIcon fontSize="10px" />
                      <div style={{ fontSize: '12px', fontWeight: '600' }}>View</div>
                    </Stack>
                  )}
                </Stack>
              </MenuItem>
              {
                  row.clinic_id && (
                    <MenuItem
                      onClick={() => {
                        handleDelete();
                        handleClose();
                      }}
                    >
                      <Stack direction='row' gap='10px' sx={{}} alignItems='center'>
                        <TrashIcon
                          fontSize="10px"
                        />
                        <div style={{ fontSize: '12px', fontWeight: '600' }}>Delete</div>
                      </Stack>
                    </MenuItem>
                  )
                }
            </Menu>
          </div>
        </div>
      </div>
      <div className="frame">
        <div className="icon-text-2" style={{}}>
          <div>
            <Typography sx={{ fontSize: '14px', color: '#A4A4A4', fontWeight: '500' }}>Name</Typography>
            <div style={{ fontSize: '18px', fontWeight: '500' }} className="data">
              {row.name}
            </div>
          </div>
        </div>
      </div>
      <div className="frame">
        <div>
          <Typography sx={{ fontSize: '14px', color: '#A4A4A4', fontWeight: '500' }}>Description</Typography>
          <div className="data text-clamp-1" style={{ fontSize: '12px', fontWeight: '500' }}>
            {row.description}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Card;
