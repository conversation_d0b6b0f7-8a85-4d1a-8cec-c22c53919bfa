import * as Yup from 'yup';

const validationSchema = Yup.object().shape({
  organization_name: Yup.string()
    .required('Organisation Name is required')
    .min(3, 'Organisation Name must be at least 3 characters')
    .max(25, 'Organisation Name cannot exceed 20 characters'),
  first_name: Yup.string()
    .required('First Name is required')
    .min(3, 'First Name must be at least 3 characters')
    .max(25, 'First Name cannot exceed 10 characters'),
  last_name: Yup.string()
    .required('Last Name is required')
    .min(3, 'Last Name must be at least 3 characters')
    .max(25, 'Last Name cannot exceed 10 characters'),
  email: Yup.string().email('Invalid email').required('Please enter your Email'),
  password: Yup.string()
    .nullable() // Allows the field to be empty
    .test(
      'password-validation',
      'Password must be atleast 8 character long with uppercase, number and special character',
      (value) =>
        !value || (value.length >= 8 && /[A-Z]/.test(value) && /[0-9]/.test(value) && /[!@#$%^&*(),.?":{}|<>]/.test(value)),
    ),
  confirmPassword: Yup.string()
    // @ts-ignore
    .oneOf([Yup.ref('password'), null], 'Passwords must match')
    .when('password', {
      is: (value: any) => value?.length > 0,
      then: (schema) => schema.required('Confirm Password is required'),
      otherwise: (schema) => schema.notRequired(),
    }),
});

export default validationSchema;
