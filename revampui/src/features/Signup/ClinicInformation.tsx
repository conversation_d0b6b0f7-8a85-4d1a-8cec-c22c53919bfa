// @ts-nocheck
import React from 'react';
import { useFormik } from 'formik';
import { Typography, Button, Grid, TextField } from '@material-ui/core';
import { useDispatch } from 'react-redux';
import { useHistory } from 'react-router';
import { OutlinedDropdown } from '../../components/molecules/OutlinedDropdown';
import Steppers from '../../components/atoms/Steppers';
import { IFieldOptions, IFromFieldsType } from '../../models/IFormFields';
import { IClinicInfoFormValues } from '../../models/ISignup';
import useStyles from './styles';
import validationSchema from '../../helpers/schema/signupValidations';
import defaultValues from '../../helpers/formDefaultValues/signupForm';
import formFields from './data';
import { registerClinic } from '../../actions/auth.action';
import SignupDropdown from '../../components/molecules/Signup/signupDropdown';
import OnBoardingLayout from '../../layouts/MainLayout/OnBoardingLayout';

const ClinicInformation = () => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const history = useHistory();

  const formik = useFormik({
    initialValues: defaultValues.clicnicInformation,
    validationSchema: validationSchema.clinicInformation,
    onSubmit: (values) => {
      // @ts-ignore
      dispatch(registerClinic(values)).then((response: any) => {
        // Navigate to the desired route after successful registration
        history.push('/hospital', { clinicData: response });
      });
    },
  });

  const renderFileds = (fieldOption: IFieldOptions) => {
    const fieldName = fieldOption.name as keyof IClinicInfoFormValues;

    switch (fieldOption.type) {
      case IFromFieldsType.DROPDOWN:
        return (
          <OutlinedDropdown
            formik={formik}
            id={fieldOption.id}
            name={fieldName}
            placeholder={fieldOption.placeholder}
            options={fieldOption.data || []}
          />
        );
      case IFromFieldsType.TEXT:
      default:
        return (
          <TextField
            fullWidth
            id={fieldOption.id}
            name={fieldName}
            placeholder={fieldOption.placeholder}
            variant="outlined"
            value={formik.values[fieldName] || ''}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched[fieldName] && Boolean(formik.errors[fieldName])}
            helperText={formik.touched[fieldName] && formik.errors[fieldName]}
            className={classes.outlinedInputRoot}
          />
        );
    }
  };

  return (
    <OnBoardingLayout
      content={
        <>
          {/* <SignupDropdown formik={formik} /> */}
          <Steppers activeStep={0} total={3} />
          <Typography variant="h4" gutterBottom className={classes.headingMargin}>
            Clinic Information
          </Typography>
          <Typography variant="h6" gutterBottom className={classes.subHeading}>
            Enter your information just as it appears on your health insurance card or pay stub.
          </Typography>
          <Typography variant="subtitle1">*Required</Typography>
          <form onSubmit={formik.handleSubmit}>
            <Grid container spacing={1} className={classes.formMargin}>
              {formFields.clinicInformation?.map((fieldOption) => {
                return (
                  <Grid item xs={12} key={fieldOption.id}>
                    {renderFileds(fieldOption)}
                  </Grid>
                );
              })}
            </Grid>
            <Grid item xs={12}>
              <Button className={classes.button} type="submit" fullWidth>
                Next
              </Button>
            </Grid>
          </form>
        </>
      }
    />
  );
};

export default ClinicInformation;
