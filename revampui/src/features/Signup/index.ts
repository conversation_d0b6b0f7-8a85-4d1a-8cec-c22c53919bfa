import { lazy } from 'react';

const SignUp = lazy(() => import('./Signup'));
const ClinicInformation = lazy(() => import('./ClinicInformation'));
const IndividualAccountOwner = lazy(() => import('./IndividualAccountOwner'));
const HospitalAccountOwner = lazy(() => import('./HospitalAccountOwner'));
const CreatePassword = lazy(() => import('./CreatePassword'));
const ActivateAccount = lazy(() => import('./ActivateAccount'));
export { SignUp, ClinicInformation, IndividualAccountOwner, HospitalAccountOwner, CreatePassword, ActivateAccount };
