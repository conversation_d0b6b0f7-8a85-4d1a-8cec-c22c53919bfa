import { Theme, makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles((theme: Theme) => ({
  button: {
    borderRadius: '100px',
    padding: '16px 32px 16px 32px',
    textTransform: 'none',
    fontSize: '14px',
    fontWeight: 500,
    lineHeight: '17.64px',
  },

  errorMessages: {
    fontFamily: 'UrbanistMedium',
    fontSize: '12px',
  },

  headingMargin: {
    marginBottom: theme.spacing(1.5),
  },
  formMargin: {
    marginBottom: theme.spacing(3),
    marginTop: theme.spacing(3),
  },
  subHeading: {
    marginBottom: theme.spacing(1.5),
    color: '#6A797E',
  },
  outlinedInputRoot: {
    '& .MuiOutlinedInput-root': {
        background: '#f9f9f9',
      '& fieldset': {
        borderRadius: '10px',
      },
    },
    '& .MuiOutlinedInput-input': {
      fontFamily: 'UrbanistMedium',
      fontWeight: 500,
      fontSize: '14px',

      padding: '16px 24px',
    },
    '& .MuiOutlinedInput-root fieldset': {
      border: 0,
    },
    '& svg': {
      color: 'black',
      fontSize: '16px',
    },
  },
}));

export default useStyles;
