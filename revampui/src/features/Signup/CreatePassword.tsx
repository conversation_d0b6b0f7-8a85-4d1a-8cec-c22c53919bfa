// @ts-nocheck
import React, { useRef, useState } from 'react';
import { useFormik } from 'formik';
import {
  Typography, Button, Box,  Grid, TextField, InputAdornment, IconButton, Alert, AlertTitle } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ReCAPTCHA from 'react-google-recaptcha';
import { Visibility, VisibilityOff } from '@material-ui/icons';
import { useHistory, useLocation } from 'react-router';
import { useDispatch } from 'react-redux';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';
import _ from 'lodash';
import { PATH_NAME } from 'configs/pathName';
import CheckIcon from '@mui/icons-material/Check';
import { OutlinedDropdown } from '../../components/molecules/OutlinedDropdown';
import Steppers from '../../components/atoms/Steppers';
import { IFieldOptions, IFromFieldsType } from '../../models/IFormFields';
import { ICreatePassword } from '../../models/ISignup';
import useStyles from './styles';
import validationSchema from '../../helpers/schema/signupValidations';
import defaultValues from '../../helpers/formDefaultValues/signupForm';
import formFields from './data';
import CustomizedSwitch from '../../components/atoms/CustomizedSwitch';
import SignupDropdown from '../../components/molecules/Signup/signupDropdown';
import OnBoardingLayout from '../../layouts/MainLayout/OnBoardingLayout';
import { LocationState } from '../../utils/types';
import { register, registerClinic } from '../../actions/auth.action';

const CreatePassword = () => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const recaptcha = useRef(null);
  const location = useLocation<LocationState>();
  const history = useHistory();
  const [errorMessage, setErrorMessage] = useState('');
  const [confirmationMessage, setConfirmationMessage] = useState(true);
  const [captchaChecked, setCaptchaChecked] = useState(false); // State to track if captcha is checked
  const [captchaError, setCaptchaError] = useState(''); // State to store captcha error message
  const clinicData = location.state?.clinicData;
  const shouldRegisterClinic = location.state?.registerClinic;
  const [showPassword, setShowPassword] = useState(false);
  const formik = useFormik({
    initialValues: defaultValues.createPassword,
    validationSchema: validationSchema.createPassword,
    onSubmit: (values) => {
      if (!captchaChecked) {
        // Check if captcha is checked
        setCaptchaError('Please verify that you are not a robot.');
        return;
      }

      const payload = {
        ...clinicData,
        ...values,
      };
      if (shouldRegisterClinic) {
        dispatch(registerClinic({ name: payload.organisation_name }))
          .then((res) => {
            payload.clinic_id = res.clinic_id;
            completeRegistration(payload);
          })
          .catch((e) => {
            handleRegistrationError(e);
          });
      } else {
        completeRegistration(payload);
      }
    },
  });

  const completeRegistration = (payload) => {
    let pay = _.omit(payload, [
      'id',
      'name',
      'address',
      'status',
      'updatedAt',
      'createdAt',
      'registartion_number',
      'user_count',
      'letter_count',
      'confirmPassword',
      'termsCheck',
      'client_id',
      'registration_number',
      'type',
      'userData',
      'organisation_name',
    ]);

    if (pay.user_type === 'Clinic') {
      if (!pay?.invite_id) {
        pay = _.omit(pay, ['country_id', 'county_id', 'town_id', 'post_code']);
      } else {
        pay = _.omit(pay, ['clinic_name']);
      }
    }

    dispatch(register(pay))
      .then(() => {
        setConfirmationMessage(false);
      })
      .catch((e: any) => {
        handleRegistrationError(e);
      });
  };

  const handleRegistrationError = (e) => {
    if (e.response.data.msg === 'Clinic Name already exist.') {
      setErrorMessage('The organisation already exists, please enter another to continue');
    } else if (e.response.data.msg === 'User already exist.') {
      setErrorMessage('The Email already exists, please enter another to continue');
    } else {
      setErrorMessage('Something went wrong! Please try again later.');
    }
  };

  const handleTogglePassword = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };

  const onCaptchaChange = (value) => {
    if (value) {
      setCaptchaChecked(true);
      setCaptchaError(''); // Clear captcha error if captcha is checked
    }
  };

  const renderFileds = (fieldOption: IFieldOptions) => {
    const fieldName = fieldOption.name as keyof ICreatePassword;

    switch (fieldOption.type) {
      case IFromFieldsType.SWITCH:
        return <CustomizedSwitch id={fieldOption.id} name={fieldName} formik={formik} placeholder={fieldOption.placeholder} sx={{ margin: '15px 0' }} />;

      case IFromFieldsType.DROPDOWN:
        return (
          <OutlinedDropdown
            formik={formik}
            id={fieldOption.id}
            name={fieldName}
            placeholder={fieldOption.placeholder}
            options={fieldOption.data || []}
          />
        );

      case IFromFieldsType.TEXT:
      default:
        return (
          <TextField
            fullWidth
            id={fieldOption.id}
            name={fieldName}
            placeholder={fieldOption.placeholder}
            variant="outlined"
            type={showPassword ? 'text' : 'password'}
            value={formik.values[fieldName]}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched[fieldName] && Boolean(formik.errors[fieldName])}
            helperText={formik.touched[fieldName] && formik.errors[fieldName]}
            className={classes.outlinedInputRoot}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <LockOutlinedIcon />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={handleTogglePassword} edge="end">
                    {showPassword ? <Visibility /> : <VisibilityOff />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        );
    }
  };

  return (
    <OnBoardingLayout
      content={
        <>
          {errorMessage && (
            <Alert severity="error">
              <AlertTitle>Error</AlertTitle>
              {errorMessage}
            </Alert>
          )}

          {!confirmationMessage && (
            <Alert
              icon={
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                  <CheckIcon fontSize="inherit" />
                </Box>
              }
              severity="success"
              sx={{ alignItems: 'center' }}
            >
              <Box sx={{ padding: '12px 20px' }}>
                <Typography sx={{ fontWeight: 'bold', marginBottom: '5px' }}>
                  Thanks for signing up - you're almost there.
                </Typography>
                <Typography sx={{ marginBottom: '5px' }}>
                  To activate your account, please check your email and click the "Verify My Email" button we’ve just sent to you.
                </Typography>
                <Typography>
                  <span style={{ fontWeight: 'bold' }}>Please note:</span> Sometimes our emails may land in your junk or spam folder, so be sure to check there if it’s not in your inbox.
                </Typography>
              </Box>
            </Alert>

          )}

          {confirmationMessage && (
            <>
              <IconButton
                sx={{ mb: 2 }}
                onClick={() => {
                        // Back button functionality
                        if (shouldRegisterClinic) {
                          history.push(PATH_NAME.HOSPITAL_ACCOUNT_OWNER, { clinicData: clinicData });
                        } else {
                          history.go(-1);
                        }
                      }}
              >
                <ArrowBackIcon />
              </IconButton>
              <Steppers activeStep={2} total={2} />
              <Typography variant="h4" sx={{ fontSize: '25px', fontWeight: 700, mb: 3 }}>
                Create Password
              </Typography>
              {/* <Typography variant="subtitle1">*Required</Typography> */}
              <form onSubmit={formik.handleSubmit}>
                <Grid container spacing={1} className={classes.formMargin}>
                  {formFields.createPassword?.map((fieldOption) => (
                    <Grid item xs={12} key={fieldOption.id} className='create-password'>
                      {renderFileds(fieldOption)}
                    </Grid>
                  ))}
                  <Grid item xs={12} className='recapcha-box'>
                    {process.env.REACT_APP_SITE_KEY ? (
                      <ReCAPTCHA ref={recaptcha} sitekey={process.env.REACT_APP_SITE_KEY} onChange={onCaptchaChange} />
                    ) : null}
                    {captchaError && (
                      <Typography sx={{ marginTop: '5px', marginLeft: '15px' }} className={classes.errorMessages} color="error">
                        {captchaError}
                      </Typography>
                    )}
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Button
                      className={classes.button}
                      type="submit"
                      fullWidth
                      sx={{
                        backgroundColor: '#2D2869',
                        color: '#ffffff',
                        borderRadius: '8px',
                        textTransform: 'none',
                        fontWeight: 600,
                        fontSize: '16px',
                        py: 1.5,
                      }}
                    >
                      Next
                    </Button>
                  </Grid>
                </Grid>
              </form>
            </>
          )}
        </>
      }
    />
  );
};

export default CreatePassword;
