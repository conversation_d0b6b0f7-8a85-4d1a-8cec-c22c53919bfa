import React, { useEffect, useState } from 'react';
import { useHistory, useLocation } from 'react-router';
import Stack from '@mui/material/Stack';
import { Snackbar, SnackbarContent } from '@mui/material';
import { verifyUser } from '../../services/users';

export default function ActivateAccount() {
  const location = useLocation();
  const history = useHistory();

  const [verified, setVerified] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [toast, setToast] = useState({ message: 'Email Verified!', color: '#4caf50' });
  const [open, setOpen] = useState(false);

  // Function to get query parameter value by name
  const getQueryParam = (param: string) => {
    return new URLSearchParams(location.search).get(param);
  };

  // Get the 't' query parameter
  const token = getQueryParam('t');

  useEffect(() => {
    setVerifying(true);
    console.log(token);
    verifyUser({ token })
      .then((response) => {
        console.log(response);
        setVerifying(false);
        setVerified(true);
        setOpen(true);
        history.push('login');
      })
      .catch((e) => {
        console.log(e);
        setVerifying(false);
      });
  }, []);

  return (
    <Stack
      flex="auto"
      justifyContent="center"
      alignItems="center"
      minHeight="100vh"
      flexDirection="row"
      sx={{
        backgroundImage: 'url("assets/images/onboarding-background.png")',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
    >
      {verifying && <h1>Verifying Email</h1>}
      {verified && <h1>Verified Email</h1>}
      {!verified && !verifying && (
        <h1 style={{ backgroundColor: 'white', padding: '20px', borderRadius: '10px' }}>Could not Verify Verified Email</h1>
      )}
      <Snackbar
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        message="Email Verified!"
        open={open}
        autoHideDuration={2000}
        onClose={() => setOpen(false)}
      >
        <SnackbarContent message="Email Verified!" style={{ backgroundColor: toast.color }} />
      </Snackbar>
    </Stack>
  );
}
