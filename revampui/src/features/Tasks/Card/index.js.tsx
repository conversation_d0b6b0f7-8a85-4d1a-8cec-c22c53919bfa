// @ts-nocheck
import React, { useState } from 'react';
import './style.css';
import { format } from 'date-fns';
import { useTheme, Button, Menu, MenuItem, Stack } from '@mui/material';
import { useHistory } from 'react-router';
import CalendarIcon from '../../../themes/icons/CalenderIcon';
import SpreadIcon from '../../../themes/icons/SpreadIcon';
import ViewIcon from '../../../themes/icons/ViewIcon';
import EditIcon from '../../../themes/icons/EditIcon';
import TrashIcon from '../../../themes/icons/TrashIcon';
import { canAction } from '../../../helpers';
import { PERMISSIONS } from '../../../configs';

const Component = ({ row, status, priority, handleEdit, handleDelete }) => {
  const theme = useTheme();
  const history = useHistory();

  // ✅ Ensure `status` & `priority` are always strings
  const statusClass = (status?.toString().toLowerCase() || '').replace(/\s+/g, '-');
  const priorityClass = priority?.toString().toLowerCase() || '';

  // ✅ Menu State Handling
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);

  return (
    <div className="task-card" style={{ background: theme.palette.background.paper }}>
      {/* 🟢 First Row (2 Columns: Patient Name + Date | Assigned To) */}
      <div className="task-card-header">
        {/* Column 1: Patient Name & Date */}
        <div className="task-column">
          <div className="patient-name">{row.letter?.patient?.name}</div>
          <div className="date-assigned">{format(new Date(row.updatedAt), 'dd MMM, yyyy')}</div>
        </div>

        {/* Column 2: Assigned To */}
        <div className="task-column">
          <div className="info-label">Assigned To:</div>
          <div className="assigned-name">
            {row.assignedUser?.first_name} {row.assignedUser?.last_name}
          </div>
        </div>
      </div>

      {/* Divider */}
      <div className="divider" />

      {/* 🟡 Second Row (3 Columns: Status | Priority | Actions) */}
      <div className="task-card-footer">
        {/* Column 1: Status */}
        <div className="task-column">
          <div className="info-label">Status:</div>
          <span className={`status-tag ${statusClass}`}>{status}</span>
        </div>

        {/* Column 2: Priority */}
        <div className="task-column">
          <div className="info-label">Priority:</div>
          <span className={`priority-tag ${priorityClass}`}>{priority}</span>
        </div>

        {/* Column 3: Action Button (Dropdown Menu) */}
        <div className="action-btn">
          {(canAction('do', PERMISSIONS.TASK_EDIT) || canAction('do', PERMISSIONS.TASK_VIEW)) && (
            <div>
              <Button
                sx={{ background: 'none', '&:hover': { background: 'none', border: 'none', boxShadow: 'none' } }}
                id="basic-button"
                aria-controls={open ? 'basic-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={open ? 'true' : undefined}
                onClick={handleClick}
              >
                <SpreadIcon />
              </Button>
              <Menu
                sx={{ borderRadius: '20px !important' }}
                id="basic-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
              >
                {canAction('do', PERMISSIONS.TASK_VIEW) && (
                  <MenuItem
                    onClick={() => {
                      history.push(`/clinical-document/letter/${row?.letter_id}/${row.id}`);
                    }}
                  >
                    <Stack direction="row" gap="10px" sx={{ padding: '6px 8px' }} alignItems="center">
                      <ViewIcon fontSize="16px" />
                      <div style={{ fontWeight: '600' }}>View</div>
                    </Stack>
                  </MenuItem>
                )}
                {canAction('do', PERMISSIONS.TASK_EDIT) && (
                  <MenuItem
                    onClick={() => {
                      handleEdit();
                      handleClose();
                    }}
                  >
                    <Stack direction="row" gap="10px" sx={{ padding: '6px 8px' }} alignItems="center">
                      <EditIcon fontSize="16px" />
                      <div style={{ fontSize: '12px', fontWeight: '600' }}>Edit</div>
                    </Stack>
                  </MenuItem>
                )}
                {canAction('do', PERMISSIONS.TASK_DELETE) && (
                  <MenuItem
                    onClick={() => {
                      handleDelete();
                      handleClose();
                    }}
                  >
                    <Stack direction="row" gap="10px" sx={{ padding: '6px 8px' }} alignItems="center">
                      <TrashIcon fontSize="16px" />
                      <div style={{ fontSize: '12px', fontWeight: '600' }}>Delete</div>
                    </Stack>
                  </MenuItem>
                )}
              </Menu>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Component;
