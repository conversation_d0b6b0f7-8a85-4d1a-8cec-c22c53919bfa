.component {
  align-items: flex-start;
  background-color: #ffffff;
  border: 1px solid;
  border-color: #e6e6e6;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 24px;
  position: relative;
  
}

@media (min-width: 768px){
  .component {
    width: 390px;
   }

}

.component .frame {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 10px;
  position: relative;
  width: 100%;
  min-height: 32px;
}

.component .div {
  flex: 1;

  font-weight: 400;
  letter-spacing: 0;
  line-height: 18px;
  margin-top: -1px;
  position: relative;
}

.component .instance-node {
  flex: 0 0 auto !important;
}

.component .status-badge-instance {
  color: #9a00ff !important;
  font-family: 'UrbanistMedium', Helvetica !important;
  font-size: 12px !important;
  font-style: unset !important;
  font-weight: 500 !important;
  letter-spacing: 0 !important;
  line-height: 18px !important;
}

.component .icon-text-2 {
  align-items: center;
  border-radius: 8px;
  display: flex;
  flex: 1;
  flex-grow: 1;
  flex-wrap: wrap;
  gap: 8px;
  position: relative;
}

.component .female-wrapper {
  align-items: center;
  border-radius: 13.33px;
  display: inline-flex;
  flex: 0 0 auto;
  justify-content: center;
  position: relative;
}

.component .div-wrapper {
  align-items: flex-start;
  border-radius: 8px;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  justify-content: center;
  position: relative;
  padding-left: 5px;
}

.component .text-wrapper-2 {
  align-self: stretch;
  font-family: 'UrbanistMedium', Helvetica;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 18px;
  margin-top: -1px;
  position: relative;
}

.component .frame-2 {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 6px;
  position: relative;
}

.component .text-wrapper-3 {
  color: #a4a4a4;
  font-family: 'UrbanistMedium', Helvetica;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 18px;
  margin-right: -67.5px;
  margin-top: -1px;
  position: relative;
  width: 196px;
}

.component .icon-text-instance {
  border-radius: 5px !important;
  flex: 0 0 auto !important;
}

.component .icon-text-3 {
  font-family: 'UrbanistMedium', Helvetica !important;
  font-size: 12px !important;
  font-style: unset !important;
  font-weight: 500 !important;
  letter-spacing: 0 !important;
  line-height: 18px !important;
}

.data {
  font-size: 12px;
  padding: 3px 0px;
}
.component .text-wrapper-4 {
  align-self: stretch;
  color: #a4a4a4;
  font-family: 'UrbanistMedium', Helvetica;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 18px;
  margin-top: -1px;
  position: relative;
}

.component .icon-text-4 {
  align-self: stretch !important;
  display: flex !important;
  flex: 0 0 auto !important;
  gap: 8px !important;
  width: 100% !important;
}

.component .frame-3 {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.component .text-wrapper-5 {
  color: #a4a4a4;
  font-family: 'UrbanistMedium', Helvetica;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 18px;
  margin-top: -1px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.component .button-high {
  height: 28px !important;
  position: relative !important;
  width: 28px !important;
}

.component .calendar-blank {
  height: 16px !important;
  position: relative !important;
  width: 16px !important;
}

.component:hover {
  background-color: #f2f2f2;
}

.status-badge {
  align-items: center;
  display: inline-flex;
  position: relative;
}

.status-badge .style-circle-weight-fill {
  height: 16px !important;
  position: relative !important;
  width: 16px !important;
}

.status-badge .div {
  margin-top: -1px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.status-badge .in-progress-2 {
  margin-top: -1px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.status-badge.BG-false {
  border-radius: var(--corner-radius-4-duplicate);
  padding: 1px 0px;
}

.status-badge.BG-true {
  border-radius: var(--corner-radius-80);
  padding: 1px var(--spacing-8-duplicate) 1px var(--spacing-8-duplicate);
}

.status-badge.pending.BG-true {
  background-color: #e2f5ff;
}

.status-badge.BG-true.rejected {
  background-color: var(--themes-black-5);
}

.status-badge.BG-true.in-progress {
  background-color: #ededff;
}

.status-badge.approved.BG-true {
  background-color: #fffbd4;
}

.status-badge.BG-true.complete {
  background-color: #def8ee;
}

.status-badge.complete .div {
  color: #4aa785;
}

.status-badge.in-progress .div {
  color: #8a8cd9;
}

.status-badge.approved .div {
  color: #ffc554;
}

.status-badge.rejected .div {
  color: var(--themes-black-40);
}

.status-badge.pending .div {
  color: #59a8d4;
}

.status-badge.complete .in-progress-2 {
  color: #4aa785;
}

.status-badge.in-progress .in-progress-2 {
  color: #8a8cd9;
}

.status-badge.approved .in-progress-2 {
  color: #ffc554;
}

.status-badge.rejected .in-progress-2 {
  color: var(--themes-black-40);
}

.status-badge.pending .in-progress-2 {
  color: #59a8d4;
}

.task-card {
  width: 100%;
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 🟢 First Row: Two Columns */
.task-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

/* 🟡 Second Row: Three Columns */
.task-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.task-column {
  flex: 1; /* Ensures equal width */
  display: flex;
  flex-direction: column;
  align-items: center;
}

.info-label {
  font-size: 14px;
  color: #888;
  line-height: 1.6;
}

.patient-name,
.assigned-name {
  font-size: 14px;

  color: #333;
}

.date-assigned {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.divider {
  width: 100%;
  height: 1px;
  background: #eee;
  margin: 8px 0;
}

/* 🔴 Status Styles */
.status-tag {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
  min-width: 80px;
  text-align: center;
}

.status-tag.not-started {
  background: #fff5f5;
  color: #ff4d4f;
  border: 1px solid #ff4d4f;
}

/* 🟢 Priority Styles */
.priority-tag {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  margin-top: 10px;
  display: inline-block;
  min-width: 80px;
  text-align: center;
}

.priority-tag.normal {
  background: #f6fff2;
  color: #52c41a;
  border: 1px solid #52c41a;
}

/* ⚡ Action Button */
.action-btn {
  text-align: center;
}
