// AssignToDialog.tsx
// @ts-nocheck
import React, { useEffect, useState } from 'react';
import {
  Avatar,
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  TextField,
  Typography,
} from '@mui/material';
import { Search, ArrowBack, Mic } from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import MenuItem from '@material-ui/core/MenuItem';
import Select from '@material-ui/core/Select';
import { createTask, updateTask } from '../../../services/tasks';
import IRootState from '../../../models/IRootState';
import { enqueueSnackbarAction } from '../../../actions/app.action';
import { getUsers } from '../../../services/users';
import SearchBar from '../../../components/atoms/SearchBar';
import { PRIORITY, renderPriorityIcon, renderStatus, STATUS } from '../helperComponents';
import { letterIdForTaskCreation } from '../../../selectors/clinicalLetter.selector';

interface AssignToDialogProps {
  open: boolean;
  onClose: () => void;
  taskData?: any;
  setTask?: any;
  taskId?: any;
}

const AssignToDialog: React.FC<AssignToDialogProps> = ({ open, onClose, taskData, setTask, taskId }) => {
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [taskName, setTaskName] = useState<string>('');
  const [note, setNote] = useState<string>('');
  const auth = useSelector((state: IRootState) => state.auth);
  const dispatch = useDispatch();
  const [users, setUsers] = useState([]);
  const [defaultUsers, setDefaultUsers] = useState([]);
  const [priority, setPriority] = useState(PRIORITY.MEDIUM);
  const [status, setStatus] = useState(STATUS.PENDING);

  const clinicalNoteId = useSelector(letterIdForTaskCreation);

  const handleUserClick = (name: string) => {
    setSelectedUser(name);
  };

  const assignTask = () => {
    let letterId = '';
    if (clinicalNoteId) {
      letterId = clinicalNoteId;
    }
    if (taskName && note && selectedUser) {
      const payload = {
        task_name: taskName,
        notes: note,
        priority: priority,
        letter_id: parseInt(letterId, 10), // 78
        // letter_id: '1',
        assigned_to: selectedUser,
        task_owner: auth.user.id,
        last_modified_by: auth.user.id,
        status: status,
      };
      if (taskData) {
        // editing a task
        payload.letter_id = taskData.letter_id;
        payload.task_owner = taskData.task_owner;
        updateTask(payload, taskData.id)
          .then((response) => {
            setTask && setTask(response);
            onClose();
            dispatch(
              enqueueSnackbarAction({
                message: 'Task Updated',
                key: new Date().getTime() + Math.random(),
                variant: 'success',
              }),
            );
          })
          .catch((error) => {
            console.error(error);
          });
      } else {
        // creating a task
        createTask(payload)
          .then((response) => {
            onClose();
            dispatch(
              enqueueSnackbarAction({
                message: 'Task Created',
                key: new Date().getTime() + Math.random(),
                variant: 'success',
              }),
            );
          })
          .catch((error) => {
            console.error(error);
          });
      }
    } else {
      console.log('Task name or note not filled');
      dispatch(
        enqueueSnackbarAction({
          message: 'Task name or note not filled',
          key: new Date().getTime() + Math.random(),
          variant: 'error',
        }),
      );
    }
  };

  useEffect(() => {
    getUsers(1, auth.user?.clinic_id, '', auth?.user?.role_name)
      .then((res) => {
        setUsers(res.list);
        console.log(res.list);
        setDefaultUsers(res.list);
      })
      .catch((e) => {
        console.log(e);
      });
  }, []);

  useEffect(() => {
    if (taskData) {
      setTaskName(taskData.task_name);
      setSelectedUser(taskData.assigned_to);
      setNote(taskData?.assignments?.find((a) => a.staff_id === taskData.assigned_to)?.notes);
      setPriority(taskData.priority);
      setStatus(taskData.status);
    }
  }, [taskData]);

  const handleSearch = (value) => {
    if (value?.length > 2) {
      getUsers(1, auth.user?.clinic_id, value, auth?.user?.role_name)
        .then((res) => {
          setUsers(res.list);
        })
        .catch((e) => {
          console.log(e);
        });
    } else if (!value?.length) {
      setUsers(defaultUsers);
    }
  };

  return (
    <Dialog
      open={open}
      maxWidth="xs"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: '20px',
          paddingLeft: '10px',
          paddingRight: '10px',
        },
      }}
    >
      <DialogContent>
        <Box mb={2}>
          <DialogTitle sx={{ paddingLeft: '0' }}>
            <Typography variant="body1">
              <strong>Task Name</strong>
            </Typography>
          </DialogTitle>
          <TextField
            variant="outlined"
            placeholder="Enter task name"
            fullWidth
            value={taskName}
            onChange={(e) => setTaskName(e.target.value)}
          />
        </Box>
        <Box mb={2} display="flex">
          <DialogTitle sx={{ paddingLeft: '0' }}>
            <Typography variant="body1">
              <strong>Status</strong>
            </Typography>
          </DialogTitle>
          <Select
            value={status}
            onChange={(e) => setStatus(e.target.value)}
            displayEmpty
            inputProps={{ 'aria-label': 'Without label' }}
          >
            <MenuItem value={STATUS.PENDING}>{renderStatus(STATUS.PENDING)}</MenuItem>
            <MenuItem value={STATUS.NEED_FEEDBACK}>{renderStatus(STATUS.NEED_FEEDBACK)}</MenuItem>
            <MenuItem value={STATUS.IN_PROGRESS}>{renderStatus(STATUS.IN_PROGRESS)}</MenuItem>
            <MenuItem value={STATUS.COMPLETE}>{renderStatus(STATUS.COMPLETE)}</MenuItem>
          </Select>
          <DialogTitle>
            <Typography variant="body1">
              <strong>Priority</strong>
            </Typography>
          </DialogTitle>
          <Select
            value={priority}
            onChange={(e) => setPriority(e.target.value)}
            displayEmpty
            inputProps={{ 'aria-label': 'Without label' }}
          >
            <MenuItem value={PRIORITY.HIGH}>{renderPriorityIcon(PRIORITY.HIGH)}</MenuItem>
            <MenuItem value={PRIORITY.MEDIUM}>{renderPriorityIcon(PRIORITY.MEDIUM)}</MenuItem>
            <MenuItem value={PRIORITY.LOW}>{renderPriorityIcon(PRIORITY.LOW)}</MenuItem>
          </Select>
        </Box>
        <Box mb={2}>
          <DialogTitle sx={{ paddingLeft: '0' }}>
            <Typography variant="body1">
              <strong>Assign To</strong>
            </Typography>
          </DialogTitle>
          <Box display="flex" alignItems="center" mb={2} width="100%">
            <SearchBar onSearch={handleSearch} />
          </Box>
          <List>
            {users.map((user, index) => (
              <ListItem
                button
                key={index}
                onClick={() => handleUserClick(user.id)}
                selected={user.id === selectedUser}
                style={{
                  borderRadius: '10px',
                  marginBottom: '5px',
                }}
              >
                <ListItemAvatar>
                  <Avatar src={user.profileS3Url} />
                </ListItemAvatar>
                <ListItemText
                  primary={`${user.first_name} ${user.last_name}`}
                  primaryTypographyProps={{
                    fontSize: '12px',
                  }}
                />
              </ListItem>
            ))}
          </List>
        </Box>

        <Box mb={2}>
          <DialogTitle sx={{ paddingLeft: '0' }}>
            <Typography variant="body1">
              <strong>Notes</strong>
            </Typography>
          </DialogTitle>
          <TextField
            variant="outlined"
            placeholder="Add comments or observations..."
            multiline
            rows={4}
            fullWidth
            value={note}
            onChange={(e) => setNote(e.target.value)}
          />
        </Box>
        <Box display="flex" justifyContent="space-between" mt={2}>
          <Button
            // startIcon={<ArrowBack />}
            style={{ borderRadius: '50px' }}
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button variant="contained" color="primary" onClick={assignTask} style={{ borderRadius: '50px' }}>
            Save
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default AssignToDialog;
