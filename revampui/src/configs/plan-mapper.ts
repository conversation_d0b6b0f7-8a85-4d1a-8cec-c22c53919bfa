/**
 * Maps internal plan names to user-facing display names
 */
export const PLAN_DISPLAY_NAMES: Record<string, string> = {
  'Starter': 'Free',
  'Starter Plan': 'Free Plan',
};

/**
 * Converts internal plan name to user-facing display name
 * @param planName The internal plan name
 * @returns The user-facing display name
 */
export function getPlanDisplayName(planName: string): string {
  if (!planName) return '';
  
  // Check for exact matches first
  if (PLAN_DISPLAY_NAMES[planName]) {
    return PLAN_DISPLAY_NAMES[planName];
  }
  
  // Check for case-insensitive partial matches
  const lowerPlanName = planName.toLowerCase();
  if (lowerPlanName.includes('starter')) {
    return lowerPlanName.replace(/starter/i, 'Free');
  }
  
  return planName;
}