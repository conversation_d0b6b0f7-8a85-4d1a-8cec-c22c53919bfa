import IRootState from '../models/IRootState';
import cryptoService from './crypto.service';
import httpRequest from './httpRequest';

// AES ENCRYPTION COMPLETED IN THIS FILE

export async function ProfileDetails(): Promise<IRootState> {
  const response = await httpRequest.get('/v1/users/profile-details');
  const decryptedData = cryptoService.decrypt(response.data.data);
  return decryptedData;
}

export async function ProfileDetailsUpdate(details: any): Promise<IRootState> {
  const encryptedData = cryptoService.encrypt(details);
  const data = {
    encrypet: encryptedData,
  };
  const response = await httpRequest.patch('/v1/users/update-profile', data);
  return response.data;
}

export async function ProfileImageUpdate(image: any): Promise<IRootState> {
  const response = await httpRequest.post('/v1/users/upload-file', image);
  const decryptedData = cryptoService.decrypt(response.data.data);
  return decryptedData.location;
}
