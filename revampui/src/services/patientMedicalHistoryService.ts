import cryptoService from './crypto.service';
import httpRequest from './httpRequest';

// AES ENCRYPTION COMPLETED IN THIS FILE

class PatientMedicalHistoryService {
  update = async (clinic_id: string, patientId: number, medicalHistoryId: number, content: any) => {
    const url = `/v1/medical-history/edit/${patientId}/${medicalHistoryId}`;
    const body = { clinic_id, content };
    const encryptedPayload = cryptoService.encrypt(body);
    const response = await httpRequest.post(url, { encrypet: encryptedPayload });
    const { data } = response;
    return cryptoService.decrypt(data.data);
  };

  create = async (clinic_id: string, patientId: number, content: any) => {
    const url = `/v1/medical-history/create/${patientId}`;
    const body = { clinic_id, content };
    const encryptedPayload = cryptoService.encrypt(body);
    const response = await httpRequest.post(url, { encrypet: encryptedPayload });
    const { data } = response;
    return cryptoService.decrypt(data.data);
  };
}

const patientMedicalHistoryService = new PatientMedicalHistoryService();
export default patientMedicalHistoryService;
