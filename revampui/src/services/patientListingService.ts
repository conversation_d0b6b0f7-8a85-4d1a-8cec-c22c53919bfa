// @ts-nocheck
import httpRequest from './httpRequest';
import IRootState from '../models/IRootState';
import cryptoService from './crypto.service';
import { IPatientState } from '../models/IPatientState';

// AES ENCRYPTION COMPLETED IN THIS FILE

export async function DeletePatient(Id: number, deletedReason: string): Promise<IRootState> {
  const response = await httpRequest.delete(`/v1/patients/delete/${Id}?deleted_reason=${deletedReason}`);
  return response;
}

export async function UpdatePatient(payload: object, Id: number): Promise<IRootState> {
  const encryptData = cryptoService.encrypt(payload);
  const _payload = {
    encrypet: encryptData,
  };
  const response = await httpRequest.patch(`/v1/patients/edit/${Id}`, _payload);
  return response;
}

export async function PatientListing_Details(page: number, clinic_id: any, searchString?: any): Promise<IRootState> {
  const requestData = {
    clinic_id: clinic_id.toString(),
  };
  // Construct the URL with the optional searchString
  let url = `/v1/patients/list/${page}`;
  if (searchString) {
    url += `?searchStr=${encodeURIComponent(searchString.searchString)}`;
  }
  const encryptedPayload = cryptoService.encrypt(requestData);
  const response = await httpRequest.post(url, { encrypet: encryptedPayload });
  return cryptoService.decrypt(response.data.data);
}

export async function Patient_Details(Id: number): Promise<IPatientState> {
  const response = await httpRequest.get(`/v1/patients/get-detail/${Id}`);
  return cryptoService.decrypt(response.data.data);
}

export async function PatientGeneratedLetters(
  patientId: number,
  clinicId: string,
  page?: number = 1,
  searchStr?: string = '',
  sortBy: string = 'createdAt',
  sortOrder: string = 'desc',
): Promise<IRootState> {
  const requestData = {
    clinic_id: clinicId.toString(),
    patient_id: patientId.toString(),
  };
  if (searchStr) requestData.searchStr = searchStr;
  const payload = cryptoService.encrypt(requestData);
  const response = await httpRequest.post(`v1/clinical-note/list-for-patient/${page}?sortBy=${sortBy}&sortOrder=${sortOrder}`, {
    encrypet: payload,
  });
  return cryptoService.decrypt(response.data.data);
}

export async function InsuranceDetails(): Promise<IRootState> {
  const response = await httpRequest.get('/v1/genral/clinical-insurence');
  const decryptedData = cryptoService.decrypt(response.data.data);
  return decryptedData;
}
