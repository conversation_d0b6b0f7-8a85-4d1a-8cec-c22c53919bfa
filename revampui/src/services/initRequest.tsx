import axios, { AxiosRequestConfig, AxiosError } from 'axios';

// actions
import { setLoading } from 'actions/app.action';
import { IAuthActionTypes } from '../models/IAuthState';
import cryptoService from './crypto.service';

export type IConfig = AxiosRequestConfig & {
  showSpinner?: boolean;
  encrypet?: string;
};

type IAxiosResponse = AxiosError & {
  config: {
    showSpinner?: boolean;
  };
};

function getAccessToken() {
  const accessToken = window.localStorage.getItem('accessToken');
  return accessToken;
}

const requestConfig: IConfig = {
  baseURL: process.env.REACT_APP_ENDPOINT_URL,
  timeout: 120000,
  showSpinner: false,
};

export const axiosInstance = axios.create(requestConfig);

const { CancelToken } = axios;
const cancel: any = null;

export default function initRequest(store: any) {
  let requestCount = 0;

  function decreaseRequestCount() {
    requestCount -= 1;
    if (requestCount === 0) {
      store.dispatch(setLoading(false));
    }
  }

  axiosInstance.interceptors.request.use(
    (config: IConfig) => {
      // cancel token
      // if (cancel) {
      //   cancel(); // cancel request
      // }
      // console.log('Cancelign request');
      // config.cancelToken = new CancelToken(function executor(c) {
      //   cancel = c;
      // });

      // show loading
      if (config.showSpinner) {
        requestCount += 1;
        store.dispatch(setLoading(true));
      }

      // add x-auth-token
      const accessToken = getAccessToken();
      if (accessToken) {
        // config.headers['x-auth-token'] = accessToken;
        config.headers.Authorization = `Bearer ${accessToken}`;
      }

      return config;
    },
    (error: IAxiosResponse) => {
      if (error.config.showSpinner) {
        decreaseRequestCount();
      }
      return Promise.reject(error);
    },
  );

  axiosInstance.interceptors.response.use(
    (res: any) => {
      if (res.config.showSpinner) {
        decreaseRequestCount();
      }
      return res;
    },
    async (error: IAxiosResponse) => {
      if ((error && error?.config?.showSpinner) || error.code === 'ECONNABORTED') {
        decreaseRequestCount();
      }

      // handle request timeout
      if (error.code === 'ECONNABORTED') {
        store.dispatch(setLoading(false));
      }

      // access token expired
      if (error.response?.status === 401) {
        // error.config._retry = true;
        try {
          const refreshToken = window.localStorage.getItem('refreshToken');

          if (!refreshToken) {
            throw new Error('No refresh token available');
          }

          const result = await axios.post(
            `${process.env.REACT_APP_ENDPOINT_URL}/v1/auth/refresh-token`,
            { refresh_token: refreshToken },
            { headers: { 'Content-Type': 'application/json' } },
          );

          if (result.data && result.data.status === 'success' && result.data.data) {
            const decryptedData = cryptoService.decrypt(result.data.data);
            // const decryptedData = JSON.parse(result.data.data);

            // Store the new tokens
            window.localStorage.setItem('accessToken', decryptedData.access_token);
            window.localStorage.setItem('refreshToken', decryptedData.refresh_token);

            // Update axios headers
            axiosInstance.defaults.headers.common.Authorization = `Bearer ${decryptedData.access_token}`;

            // Retry the original request
            return await axiosInstance(error.config);
          } 
            throw new Error('Failed to refresh token');
          
        } catch (err: any) {
          // Clear all auth data and redirect to login
          store.dispatch({ type: IAuthActionTypes.LOGOUT });
          localStorage.removeItem('user');
          localStorage.removeItem('signature');
          localStorage.removeItem('refreshToken');
          localStorage.removeItem('accessToken');
          window.location.href = '/login';

          if (err.response && err.response.data) {
            return Promise.reject(err.response.data);
          }
          return Promise.reject(err);
        }
      }

      // handle errors
      switch (error.response?.status) {
        case 400: {
          break;
        }
        case 500: {
          break;
        }
        default:
          break;
      }
      return Promise.reject(error);
    },
  );
}
