import cryptoService from './crypto.service';
import httpRequest from './httpRequest';

// AES ENCRYPTION COMPLETED IN THIS FILE

class UserLogService {
  list = async (
    clinic_id: string,
    patient_id: string,
    page: number,
    searchStr: string = '',
    startDate: any = '',
    endDate: any = '',
  ) => {
    const queryParams = new URLSearchParams();

    if (searchStr) queryParams.append('searchStr', searchStr);
    if (startDate) queryParams.append('startDate', startDate.toISOString());
    if (endDate) queryParams.append('endDate', endDate.toISOString());

    const url = `/v1/user-log/list/${page}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const body = { clinic_id, patient_id };
    const encryptedPayload = cryptoService.encrypt(body);
    const response = await httpRequest.post(url, { encrypet: encryptedPayload });
    const { data } = response;
    const patientMedicalHistory = cryptoService.decrypt(data.data);
    return patientMedicalHistory;
  };
}

const patientMedicalHistoryService = new UserLogService();
export default patientMedicalHistoryService;
