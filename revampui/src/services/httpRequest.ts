import { AxiosInstance } from 'axios';
import { axiosInstance, IConfig } from './initRequest';

class HttpRequest {
  api: AxiosInstance;

  constructor() {
    this.api = axiosInstance;
  }

  async get(url: string, config?: IConfig | any) {
    return this.api.get(url, config);
  }

  async post(url: string, config?: IConfig | any) {
    return this.api.post(url, config);
  }

  async post2(url: string, data?: any) {
    return this.api.post(url, data);
  }

  async post3(url: string, data?: any, config?: IConfig | any) {
    return this.api.post(url, data, config);
  }

  async patch(url: string, data?: any) {
    return this.api.patch(url, data);
  }

  async delete(url: string, config?: IConfig | any) {
    return this.api.delete(url, config);
  }
}
const httpRequest = new HttpRequest();

export default httpRequest;
