import cryptoService from './crypto.service';
import httpRequest from './httpRequest';

// AES ENCRYPTION COMPLETED IN THIS FILE EXCEPT DELETE CALL
// TODO DELETE CALL NOT COVERED

class LlmPromptService {
  create = async (name: string, description: string, prompt: string, is_favourite: boolean, clinic_id: string) => {
    const data = {
      name: name,
      description: description,
      prompt: prompt,
      is_favourite: is_favourite,
      clinic_id: clinic_id,
    };
    const encryptedPayload = cryptoService.encrypt(data);
    const response = await httpRequest.post2('/v1/prompts/create', { encrypet: encryptedPayload });
    return response;
  };

  view = async (promptId: number) => {
    const url = `/v1/prompts/get-detail/${promptId}`;
    const response = await httpRequest.get(url);
    const { data } = response;
    return cryptoService.decrypt(data.data);
  };

  update = async (promptId: number, name: string, description: string, prompt: string, clinic_id: string) => {
    const data = {
      name: name,
      description: description,
      prompt: prompt,
      clinic_id: clinic_id,
    };
    const encryptedPayload = cryptoService.encrypt(data);
    const response = await httpRequest.patch(`/v1/prompts/edit/${promptId}`, { encrypet: encryptedPayload });
    return response;
  };

  list = async (clinicId: string, search: string, sortOrder: string, sortBy: string, page: number = 1, type?: string) => {
    const pageSize = 10;
    const url = `/v1/prompts/list?page=${page}&sortBy=${sortBy}&sortOrder=${sortOrder}&pageSize=${pageSize}${search ? `&searchStr=${search}` : ''}${
      type ? `&type=${type}` : ''
    }`;
    const payload = cryptoService.encrypt({ clinic_id: clinicId });
    const response = await httpRequest.post(url, {
      encrypet: payload,
    });
    const { data } = response;
    return cryptoService.decrypt(data.data);
  };

  // TODO clinicID is not encrypted
  delete = async (promptId: number, clinicId: string) => {
    const url = `/v1/prompts/delete/${promptId}/${clinicId}`;
    const response = await httpRequest.delete(url);
    const { data } = response;
    return data;
  };

  sampleNotes = async (name: string, description: string, prompt: string, notes: string) => {
    const url = '/v1/clinical-note/sample';
    const payload = {
      name: name,
      description: description,
      prompt: prompt,
      clinical_note: notes,
    };
    const encryptedPayload = cryptoService.encrypt(payload);
    const response = await httpRequest.post2(url, { encrypet: encryptedPayload });
    const { data } = response;
    return cryptoService.decrypt(data.data);
  };
}

const promptService = new LlmPromptService();
export default promptService;
