import { IForgotPassword, ISignin, IVerifyOTP } from '../models/IAuthState';
import { IClinicInfoFormValues, IResetPasswordRequest, IHospitalFormValues } from '../models/ISignup';
import cryptoService from './crypto.service';
import httpRequest from './httpRequest';

class AuthService {
  handleAuthentication = () => {
    const accessToken = this.getAccessToken();
    if (!accessToken || !this.isValidToken(accessToken)) return;
    this.setSession('accessToken', accessToken);
  };

  loginWithAuth = async (payload: ISignin) => {
    const encryptedData = cryptoService.encrypt(payload);
    const data = {
      encrypet: encryptedData,
    };
    const response = await httpRequest.post('/v1/auth/login', {
      ...data,
    });
    const decryptedData = cryptoService.decrypt(response.data.data);
    this.setSession('signature', decryptedData.signature);
    // this.setSession('accessToken', decryptedData.signature);
    // this.setSession('refreshToken', decryptedData.signature);
    // const userStringify = JSON.stringify({ username, roleUser });
    // this.setSession('user', userStringify);
    return response;
  };

  verifyOTP = async (payload: IVerifyOTP) => {
    const data = {
      ...payload,
      signature: this.getSignature(),
    };
    const response = await httpRequest.post('/v1/auth/verify-otp', {
      ...data,
    });
    const { token, refresh_token } = cryptoService.decrypt(response.data.data);
    this.setSession('accessToken', token);
    this.setSession('refreshToken', refresh_token);
    const response1 = await httpRequest.get('/v1/users/profile-details');
    const user = cryptoService.decrypt(response1.data.data);
    this.setSession('user', JSON.stringify(user));
    return user;
  };

  refreshUser = async () => {
    const response = await httpRequest.get('/v1/users/profile-details');
    const user = cryptoService.decrypt(response.data.data);
    this.setSession('user', JSON.stringify(user));
    return user;
  };

  resendOTP = async (payload: IVerifyOTP) => {
    const data = {
      ...payload,
      signature: this.getSignature(),
    };
    const response = await httpRequest.post('/v1/auth/resend-otp', {
      ...data,
    });

    return response;
  };

  resetPassword = async (payload: IResetPasswordRequest) => {
    const encryptedData = cryptoService.encrypt(payload);
    const data = {
      encrypet: encryptedData,
    };
    const response = await httpRequest.post('/v1/auth/reset-password', {
      ...data,
    });
    return response;
  };

  registerClinic = async (payload: IClinicInfoFormValues) => {
    const encryptedData = cryptoService.encrypt(payload);
    const data = {
      encrypet: encryptedData,
    };
    const response = await httpRequest.post('/v1/auth/register-clinic', {
      ...data,
    });
    return response;
  };

  register = async (payload: IHospitalFormValues) => {
    const payloadData = { ...payload };
    const encryptedData = cryptoService.encrypt(payloadData);
    const data = {
      encrypet: encryptedData,
    };
    const response = await httpRequest.post('/v1/auth/register', {
      ...data,
    });
    return response;
  };

  registerUser = async (payload: any) => {
    const payloadData = { ...payload };
    const encryptedData = cryptoService.encrypt(payloadData);
    const data = {
      encrypet: encryptedData,
    };
    const response = await httpRequest.post('/v1/users/register', {
      ...data,
    });
    return response;
  };

  forgotpassword = async (payload: IForgotPassword) => {
    const encryptedData = cryptoService.encrypt(payload);
    const data = {
      encrypet: encryptedData,
    };
    const response = await httpRequest.post('/v1/auth/forgot-password', {
      ...data,
    });
    return response;
  };

  loginWithToken = async () => {
    return {
      user: 'tonynguyen',
    };
  };

  setSession = (key: string, accessToken: string) => {
    localStorage.setItem(key, accessToken);
  };

  logOut = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('signature');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('accessToken');
  };

  getUser = () => {
    const user = localStorage.getItem('user') || '';
    return JSON.parse(user);
  };

  getAccessToken = () => localStorage.getItem('accessToken');

  getSignature = () => localStorage.getItem('signature');

  isAuthenticated = () => !!this.getAccessToken();

  isValidToken = (accessToken: string | null) => {
    const expireTime = 1606275140.897;
    if (!accessToken) return false;

    const currentTime = Date.now() / 1000;

    return expireTime < currentTime;
  };
}

const authService = new AuthService();

export default authService;
