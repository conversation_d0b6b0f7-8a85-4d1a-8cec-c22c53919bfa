import { createSelector } from 'reselect';

const userLogSelector = (state: any) => state.userLog;

export const userLogListSelector = createSelector(userLogSelector, (app) => app.list);

export const currentUserLogItemSelector = createSelector(userLogSelector, (app) => app.currentItem);

export const userLogSearchStrSelector = createSelector(userLogSelector, (userLog) => userLog.searchStr);

export const userLogStartDateSelector = createSelector(userLogSelector, (userLog) => userLog.startDate);

export const userLogEndDateSelector = createSelector(userLogSelector, (userLog) => userLog.endDate);

export const showUserLogDownloadSelector = createSelector(userLogSelector, (userLog) => userLog.showUserLogDownload);
