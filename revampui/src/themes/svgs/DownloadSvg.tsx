import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

interface DownloadSvgProps {
  fontSize?: any;
  fill?: any;
}

const DownloadSvg: React.FC<DownloadSvgProps> = ({ fontSize, fill = 'black' }) => {
  const theme = useTheme();
  const iconColor = theme.palette.text.primary;

  return (
    <SvgIcon fontSize={fontSize}>
      <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M11.9475 2.27625L10.9475 0.27625C10.9059 0.19318 10.842 0.123333 10.763 0.0745386C10.684 0.0257446 10.5929 -6.63752e-05 10.5 1.28188e-07H1.5C1.40711 -6.63752e-05 1.31604 0.0257446 1.237 0.0745386C1.15795 0.123333 1.09407 0.19318 1.0525 0.27625L0.0525 2.27625C0.0180355 2.34581 7.03031e-05 2.42237 0 2.5V11C0 11.2652 0.105357 11.5196 0.292893 11.7071C0.48043 11.8946 0.734784 12 1 12H11C11.2652 12 11.5196 11.8946 11.7071 11.7071C11.8946 11.5196 12 11.2652 12 11V2.5C11.9999 2.42237 11.982 2.34581 11.9475 2.27625ZM1.80875 1H10.1912L10.6912 2H1.30875L1.80875 1ZM11 11H1V3H11V11ZM8.35375 7.14625C8.40024 7.19269 8.43712 7.24783 8.46228 7.30853C8.48744 7.36923 8.50039 7.43429 8.50039 7.5C8.50039 7.56571 8.48744 7.63077 8.46228 7.69147C8.43712 7.75217 8.40024 7.80731 8.35375 7.85375L6.35375 9.85375C6.30731 9.90024 6.25217 9.93712 6.19147 9.96228C6.13077 9.98744 6.06571 10.0004 6 10.0004C5.93429 10.0004 5.86923 9.98744 5.80853 9.96228C5.74783 9.93712 5.69269 9.90024 5.64625 9.85375L3.64625 7.85375C3.55243 7.75993 3.49972 7.63268 3.49972 7.5C3.49972 7.36732 3.55243 7.24007 3.64625 7.14625C3.74007 7.05243 3.86732 6.99972 4 6.99972C4.13268 6.99972 4.25993 7.05243 4.35375 7.14625L5.5 8.29313V4.5C5.5 4.36739 5.55268 4.24021 5.64645 4.14645C5.74021 4.05268 5.86739 4 6 4C6.13261 4 6.25979 4.05268 6.35355 4.14645C6.44732 4.24021 6.5 4.36739 6.5 4.5V8.29313L7.64625 7.14625C7.69269 7.09976 7.74783 7.06288 7.80853 7.03772C7.86923 7.01256 7.93429 6.99961 8 6.99961C8.06571 6.99961 8.13077 7.01256 8.19147 7.03772C8.25217 7.06288 8.30731 7.09976 8.35375 7.14625Z"
          fill={iconColor}
        />
      </svg>
    </SvgIcon>
  );
};

export default DownloadSvg;
