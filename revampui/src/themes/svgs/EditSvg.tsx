import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

interface EditSvgProps {
  fontSize?: any;
  fill?: any;
  hovered?: boolean;
}

const EditSvg: React.FC<EditSvgProps> = ({ fontSize, fill = 'black', hovered = false }) => {
  const theme = useTheme();
  const iconColor = theme.palette.text.primary;

  return (
    <SvgIcon fontSize={fontSize}>
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          opacity="0.2"
          d="M17.0685 5.97122L14.3992 8.64052L9.35938 3.60071L12.0287 0.931411C12.1637 0.796492 12.3467 0.720703 12.5376 0.720703C12.7285 0.720703 12.9115 0.796492 13.0465 0.931411L17.0685 4.95066C17.1357 5.01756 17.189 5.09707 17.2254 5.18464C17.2618 5.27221 17.2805 5.36611 17.2805 5.46094C17.2805 5.55577 17.2618 5.64966 17.2254 5.73723C17.189 5.8248 17.1357 5.90432 17.0685 5.97122Z"
          fill={iconColor}
        />
        <path
          d="M17.5772 4.44381L13.5562 0.421866C13.4225 0.28812 13.2637 0.182025 13.089 0.109641C12.9143 0.0372562 12.727 0 12.5379 0C12.3488 0 12.1615 0.0372562 11.9868 0.109641C11.812 0.182025 11.6533 0.28812 11.5196 0.421866L0.422091 11.5202C0.287798 11.6535 0.181326 11.8121 0.108866 11.9868C0.0364062 12.1615 -0.000597072 12.3489 7.28518e-06 12.5381V16.56C7.28518e-06 16.9419 0.151715 17.3082 0.421757 17.5782C0.691799 17.8483 1.05806 18 1.43995 18H5.4619C5.65106 18.0006 5.83846 17.9636 6.0132 17.8911C6.18794 17.8187 6.34653 17.7122 6.47976 17.5779L17.5772 6.48043C17.711 6.34672 17.8171 6.18797 17.8895 6.01324C17.9618 5.83852 17.9991 5.65125 17.9991 5.46212C17.9991 5.273 17.9618 5.08573 17.8895 4.911C17.8171 4.73628 17.711 4.57753 17.5772 4.44381ZM1.73784 12.2402L9.35965 4.61841L10.8617 6.12045L3.23988 13.7414L1.73784 12.2402ZM1.43995 13.978L4.02195 16.56H1.43995V13.978ZM5.75979 16.2622L4.25774 14.7601L11.8796 7.13831L13.3816 8.64035L5.75979 16.2622ZM14.3995 7.62249L10.3775 3.60054L12.5374 1.44063L16.5594 5.46167L14.3995 7.62249Z"
          fill={iconColor}
        />
      </svg>
    </SvgIcon>
  );
};

export default EditSvg;
