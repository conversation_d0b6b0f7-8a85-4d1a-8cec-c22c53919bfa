import React, { useState } from 'react';
import { SvgIcon, useTheme } from '@mui/material';

interface SaveSvgProps {
  fontSize?: any;
  disabled?: boolean;
  hovered?: boolean;
}

const SaveSvg: React.FC<SaveSvgProps> = ({ fontSize, disabled = false, hovered = false }) => {
  const theme = useTheme();
  let iconColor;
  if (disabled) {
    iconColor = theme.palette.action.disabled;
  } else {
    iconColor = theme.palette.text.primary;
  }

  return (
    <SvgIcon fontSize={fontSize}>
      <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M13.658 3.5L10.5 0.341985C10.3921 0.233178 10.2636 0.146914 10.122 0.0882052C9.98041 0.0294969 9.82857 -0.000483757 9.67531 5.90256e-06H1.16667C0.857247 5.90256e-06 0.560501 0.122922 0.341709 0.341714C0.122916 0.560507 0 0.857253 0 1.16667V12.8333C0 13.1428 0.122916 13.4395 0.341709 13.6583C0.560501 13.8771 0.857247 14 1.16667 14H12.8333C13.1427 14 13.4395 13.8771 13.6583 13.6583C13.8771 13.4395 14 13.1428 14 12.8333V4.32469C14.0005 4.17143 13.9705 4.01959 13.9118 3.87802C13.8531 3.73644 13.7668 3.60795 13.658 3.5ZM9.91666 12.8333H4.08333V8.75H9.91666V12.8333ZM12.8333 12.8333H11.0833V8.75C11.0833 8.44058 10.9604 8.14384 10.7416 7.92505C10.5228 7.70625 10.2261 7.58334 9.91666 7.58334H4.08333C3.77391 7.58334 3.47717 7.70625 3.25837 7.92505C3.03958 8.14384 2.91667 8.44058 2.91667 8.75V12.8333H1.16667V1.16667H9.67531L12.8333 4.32469V12.8333ZM9.33333 2.91667C9.33333 3.07138 9.27187 3.21975 9.16248 3.32915C9.05308 3.43855 8.90471 3.5 8.75 3.5H4.66666C4.51196 3.5 4.36358 3.43855 4.25419 3.32915C4.14479 3.21975 4.08333 3.07138 4.08333 2.91667C4.08333 2.76196 4.14479 2.61359 4.25419 2.50419C4.36358 2.3948 4.51196 2.33334 4.66666 2.33334H8.75C8.90471 2.33334 9.05308 2.3948 9.16248 2.50419C9.27187 2.61359 9.33333 2.76196 9.33333 2.91667Z"
          fill={iconColor}
        />
      </svg>
    </SvgIcon>
  );
};

export default SaveSvg;
