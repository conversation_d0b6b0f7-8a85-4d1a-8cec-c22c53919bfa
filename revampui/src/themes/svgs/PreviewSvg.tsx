import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

interface PreviewSvgProps {
  fontSize?: any;
  disabled?: boolean;
}

const PreviewSvg: React.FC<PreviewSvgProps> = ({ fontSize, disabled = false }) => {
  const theme = useTheme();
  let iconColor;
  if (disabled) {
    iconColor = theme.palette.action.disabled;
  } else {
    iconColor = theme.palette.text.primary;
  }

  return (
    <SvgIcon fontSize={fontSize} sx={{ '&:hover': { iconColor: 'none' } }}>
      <svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 328.2 321.83">
        <path
          fill={iconColor}
          d="M-.18,160.92c0-42.67-.01-85.34,0-128.01C-.17,12.26,12.12-.03,32.78-.04c70.19-.01,140.37-.02,210.56,0,20.35,0,32.67,12.35,32.7,32.7.02,20.34.02,40.67,0,61.01,0,7.8-3.72,12.25-10.05,12.19-6.22-.06-9.93-4.63-9.94-12.39-.02-20.34,0-40.67-.02-61.01-.01-9.06-3.56-12.5-12.89-12.5-70.19-.01-140.37-.01-210.56,0-9.25,0-12.74,3.48-12.74,12.66,0,85.51,0,171.01,0,256.52,0,9.12,3.53,12.61,12.79,12.61,47.68.02,95.36,0,143.04.01,1.83,0,3.68-.07,5.5.08,5.08.43,8.71,4.05,9.11,8.97.41,5.03-2.41,9.17-7.39,10.43-1.9.48-3.95.5-5.94.5-48.01.03-96.03.03-144.04.02-20.74,0-33.07-12.24-33.07-32.84-.02-42.67,0-85.34,0-128.01Z"
        />
        <path
          fill={iconColor}
          d="M281.1,254.16c14.6,15.07,29.03,29.85,43.33,44.75,6.28,6.55,3.96,15.7-4.27,17.57-4.45,1.01-7.65-1.18-10.57-4.2-13.08-13.54-26.21-27.03-39.31-40.55-1.16-1.19-2.25-2.45-3.56-3.88-22.1,15.57-46.04,20.89-72.13,14.03-19.95-5.25-35.79-16.65-47.47-33.61-23.59-34.27-17.94-81.01,13.05-109,30.67-27.7,77.07-28.67,109.03-2.47,31.34,25.69,43.49,76.04,11.9,117.36ZM152.63,201.21c-.06,34.84,28.25,63.46,62.94,63.64,35.21.18,64.17-28.61,64.05-63.66-.12-34.78-28.58-63.26-63.32-63.35-35.08-.09-63.61,28.3-63.68,63.37Z"
        />
        <path
          fill={iconColor}
          d="M121.17,105.92c-19.32,0-38.65.07-57.97-.04-7.91-.04-12.72-6.45-10.3-13.29,1.59-4.49,5.32-6.66,11.64-6.67,29.65-.01,59.31,0,88.96,0,8.5,0,16.99-.05,25.49.02,6.95.06,11.29,3.93,11.33,9.93.04,6.02-4.27,10.01-11.17,10.03-19.32.06-38.65.02-57.97.02Z"
        />
        <path
          fill={iconColor}
          d="M91,141.68c9.16,0,18.32-.07,27.47.02,6.76.07,11.32,4.26,11.21,10.08-.11,5.66-4.52,9.82-11.02,9.86-18.48.11-36.97.11-55.45,0-6.49-.04-10.87-4.24-10.95-9.92-.08-5.83,4.46-9.95,11.26-10.01,9.16-.08,18.32-.02,27.47-.02Z"
        />
        <path
          fill={iconColor}
          d="M77.38,217.17c-4.99,0-9.98.15-14.96-.04-5.9-.22-9.95-4.17-10.16-9.54-.2-5.31,3.69-9.98,9.5-10.17,10.62-.34,21.27-.35,31.89,0,5.83.2,9.52,4.78,9.32,10.26-.2,5.43-4.14,9.24-10.15,9.45-5.15.18-10.3.04-15.45.03Z"
        />
        <path
          fill="#fff"
          d="M152.63,201.21c.06-35.07,28.59-63.46,63.68-63.37,34.74.09,63.2,28.57,63.32,63.35.12,35.05-28.85,63.84-64.05,63.66-34.69-.18-63-28.8-62.94-63.64Z"
        />
      </svg>
    </SvgIcon>
  );
};

export default PreviewSvg;
