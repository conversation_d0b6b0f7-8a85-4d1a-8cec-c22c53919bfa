// material ui v5
import { createTheme } from '@mui/material/styles';
import typography from '../typography';

// Create a custom theme
const lightTheme = createTheme({
  palette: { mode: 'light' },
  customProperties: {
    textFieldBackground: '#f9f9f9',
    createDocumentBorder: {
      border2px: '2px solid #131313',
      border1pxHover: '1px solid #131313',
      borderBottom: '2px solid #f3f6f6',
    },
    border: '1px solid #E2E2E2',
    templateBorder: '3px solid black',
    color: '#171819',
    labelColor: '#171819',
    lineConnector: '#E6E6E6',
    permissionHeadingColor: 'rgba(0, 0, 0, 0.6)',
    whiteF9F9: '#F9F9F9',
    paginationDiv: '#FFF',
    buttonPrimaryColor: '#FFF',
    buttonSecondary: '#131313',
    templateListBackground: '#F0F0F0',
    templateHeaderFooter: '#F2F2F2',
    ButtonTextColor: '#0D0D0D',
    GradientButton: {
      color: '#fff',
      background: 'linear-gradient(to right, #354bb6, #495cc4, #5d6fd2, #7180e1, #8792f0, #9ba2fe)',
      '&:hover': {
        background: 'linear-gradient(to right, #1d2f74, #2b3f98, #3c4da8, #4e5cb9, #5f6bca, #6f7bc5, #8a91dc)',
        color: '#fff',
        border: 'none',
      },
      '&:Mui-disabled': {
        background: '#2D2869',
        color: '#A4A4A4',
      },
    },
    NavbarBorderedButton: {
      border: '1px solid var(--black-10, rgba(28, 28, 28, 0.10)',
      backgroundColor: '#ff4081 !important',
    },
  },
  components: {
    MuiTableCell: {
      styleOverrides: {
        root: {
          color: '#1C1C1C', // Set text color for table cells
        },
      },
    },
    MuiCssBaseline: {
      styleOverrides: {
        '@global': {
          'input:-webkit-autofill': {
            '-webkit-box-shadow': 'transparent !important',
            '-webkit-text-fill-color': 'black !important', // Set text color to white
            transition: 'background-color 500ms ease-in-out !important', // Adjusted transition duration
          },
          'input:-webkit-autofill:hover': {
            '-webkit-box-shadow': 'transparent !important',
            '-webkit-text-fill-color': 'black !important',
          },
          'input:-webkit-autofill:focus': {
            '-webkit-box-shadow': 'transparent !important',
            '-webkit-text-fill-color': 'black !important',
          },
          'input:-webkit-autofill:active': {
            '-webkit-box-shadow': 'transparent !important',
            '-webkit-text-fill-color': 'black !important',
          },
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        input: {
          '&:-webkit-autofill': {
            caretColor: '#fff',
            borderRadius: 'inherit',
            boxShadow: 'red',
            WebkitTextFillColor: 'black',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          color: '#fff',
          textTransform: 'capitalize',
          background: '#2D2869',
          border: 'none',
          '&:hover': {
            boxShadow:
              '0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12)',
            color: '#0D0D0D',
            background: '#F5F5F5',
            border: '1px solid #2D2869',
          },
          '&.Mui-disabled': {
            color: 'grey',
            backgroundColor: 'none',
          },
          '& .Mui-disabled': {
            backgroundColor: 'none',
          },
        },
      },
    },
  },
  typography,
});
// background: 'linear-gradient(to right, #354bb6, #495cc4, #5d6fd2, #7180e1, #8792f0, #9ba2fe)',
//
export default lightTheme;
