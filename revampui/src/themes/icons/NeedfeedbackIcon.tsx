import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

const NeedfeedbackIcon = () => {
  const theme = useTheme();
  return (
    <IconButton
      size="large"
      edge="start"
      color="inherit"
      aria-label="open drawer"
      // sx={{ mr: 2 }}
    >
      <SvgIcon>
        <svg width={25} height={24} viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            opacity={0.3}
            fillRule="evenodd"
            clipRule="evenodd"
            d="M5.3999 22.5H10.7064C11.1593 22.5019 11.5993 22.3493 11.9539 22.0676C12.3084 21.7858 12.5564 21.3916 12.6569 20.95L13.4644 17.45C13.5321 17.1567 13.5328 16.8519 13.4663 16.5584C13.3998 16.2648 13.268 15.99 13.0805 15.7545C12.8931 15.519 12.6549 15.3288 12.3837 15.1982C12.1126 15.0676 11.8154 14.9998 11.5144 15H9.5099L10.0784 12.1555C10.1738 11.7079 10.095 11.2408 9.85822 10.8491C9.62141 10.4575 9.24432 10.1707 8.80363 10.0472C8.36294 9.92375 7.89176 9.97275 7.48592 10.1843C7.08007 10.3958 6.77005 10.754 6.6189 11.186L5.05005 15.5H3.3999C3.00208 15.5 2.62055 15.658 2.33924 15.9393C2.05794 16.2206 1.8999 16.6022 1.8999 17V21C1.8999 21.3978 2.05794 21.7794 2.33924 22.0607C2.62055 22.342 3.00208 22.5 3.3999 22.5H5.39697C5.39795 22.5 5.39892 22.5 5.3999 22.5ZM4.8999 21.5V16.5H3.3999C3.26729 16.5 3.14012 16.5527 3.04635 16.6464C2.95258 16.7402 2.8999 16.8674 2.8999 17V21C2.8999 21.1326 2.95258 21.2598 3.04635 21.3536C3.14012 21.4473 3.26729 21.5 3.3999 21.5H4.8999ZM5.8999 21.5H10.7064C10.9328 21.5008 11.1527 21.4245 11.3299 21.2836C11.5071 21.1427 11.6311 20.9457 11.6814 20.725L12.4889 17.225C12.5228 17.0784 12.5231 16.9261 12.4899 16.7793C12.4567 16.6326 12.3908 16.4952 12.2971 16.3775C12.2035 16.2597 12.0844 16.1646 11.9489 16.0993C11.8134 16.0339 11.6649 16 11.5144 16H8.8999C8.82575 16 8.75252 15.9835 8.68553 15.9518C8.61853 15.92 8.55944 15.8737 8.51253 15.8163C8.46563 15.7588 8.43209 15.6917 8.41434 15.6197C8.39659 15.5477 8.39507 15.4727 8.4099 15.4L9.0999 11.959C9.12353 11.8425 9.1209 11.7222 9.09219 11.6068C9.06349 11.4915 9.00944 11.384 8.93397 11.2921C8.85849 11.2003 8.7635 11.1264 8.6559 11.0759C8.54829 11.0253 8.43078 10.9994 8.3119 11C8.1473 11.0003 7.98676 11.0511 7.85193 11.1455C7.7171 11.2399 7.61447 11.3734 7.5579 11.528L5.8999 16.088V21.5Z"
            fill="#FACC15"
          />
          <path
            d="M21.3999 14H19.3999C19.2673 14 19.1401 13.9473 19.0463 13.8536C18.9526 13.7598 18.8999 13.6326 18.8999 13.5V7.5C18.8999 7.36739 18.9526 7.24021 19.0463 7.14645C19.1401 7.05268 19.2673 7 19.3999 7H21.3999C21.7977 7 22.1793 7.15804 22.4606 7.43934C22.7419 7.72064 22.8999 8.10218 22.8999 8.5V12.5C22.8999 12.8978 22.7419 13.2794 22.4606 13.5607C22.1793 13.842 21.7977 14 21.3999 14ZM19.8999 13H21.3999C21.5325 13 21.6597 12.9473 21.7535 12.8536C21.8472 12.7598 21.8999 12.6326 21.8999 12.5V8.5C21.8999 8.36739 21.8472 8.24021 21.7535 8.14645C21.6597 8.05268 21.5325 8 21.3999 8H19.8999V13Z"
            fill="#FACC15"
          />
          <path
            d="M19.4 13.9998H14.0935C13.6407 14.0015 13.2008 13.8488 12.8464 13.5671C12.4919 13.2854 12.244 12.8913 12.1435 12.4498L11.336 8.94982C11.2682 8.65654 11.2676 8.35175 11.334 8.05818C11.4005 7.76461 11.5324 7.48983 11.7198 7.25433C11.9073 7.01882 12.1455 6.82865 12.4166 6.69801C12.6878 6.56737 12.985 6.49962 13.286 6.49982H15.29L14.7215 3.65532C14.625 3.20725 14.7031 2.73926 14.9399 2.3468C15.1766 1.95435 15.5542 1.66698 15.9955 1.54332C16.4368 1.41966 16.9087 1.46901 17.3149 1.68131C17.7211 1.89361 18.031 2.25287 18.1815 2.68582L19.8695 7.32882C19.8895 7.3836 19.8998 7.44147 19.9 7.49982V13.4998C19.9 13.6324 19.8473 13.7596 19.7535 13.8534C19.6597 13.9471 19.5326 13.9998 19.4 13.9998ZM13.2855 7.49982C13.135 7.4998 12.9865 7.53373 12.8509 7.59908C12.7154 7.66443 12.5964 7.75952 12.5027 7.87727C12.4091 7.99501 12.3432 8.13237 12.31 8.27912C12.2768 8.42587 12.2771 8.57822 12.311 8.72482L13.119 12.2248C13.1691 12.4455 13.2929 12.6426 13.4701 12.7835C13.6472 12.9244 13.8671 13.0007 14.0935 12.9998H18.9V7.58782L17.2419 3.02782C17.194 2.89441 17.1116 2.77612 17.0029 2.68506C16.8943 2.59401 16.7634 2.53345 16.6237 2.5096C16.4839 2.48574 16.3404 2.49944 16.2077 2.54929C16.075 2.59914 15.9579 2.68336 15.8685 2.79332C15.7925 2.88464 15.7379 2.99181 15.7088 3.10697C15.6796 3.22213 15.6766 3.34235 15.7 3.45882L16.39 6.89982C16.4048 6.97247 16.4033 7.04751 16.3855 7.11951C16.3678 7.19151 16.3342 7.25865 16.2873 7.31608C16.2404 7.37352 16.1813 7.4198 16.1143 7.45158C16.0473 7.48335 15.9741 7.49983 15.9 7.49982H13.2855Z"
            fill="#FACC15"
          />
        </svg>
      </SvgIcon>
    </IconButton>
  );
};

export default NeedfeedbackIcon;
