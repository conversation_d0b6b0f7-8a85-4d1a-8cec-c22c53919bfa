import React from 'react';
import { SvgIcon, useTheme } from '@mui/material';

const DeleteIcon = () => {
  const theme = useTheme();
  return (
    <SvgIcon>
      <svg width={24} height={25} viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          opacity={0.4}
          d="M19.3238 9.96777C19.3238 9.96777 18.7808 16.7028 18.4658 19.5398C18.3158 20.8948 17.4788 21.6888 16.1078 21.7138C13.4988 21.7608 10.8868 21.7638 8.27881 21.7088C6.95981 21.6818 6.13681 20.8778 5.98981 19.5468C5.67281 16.6848 5.13281 9.96777 5.13281 9.96777"
          stroke="#212121"
          strokeWidth={1.5}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M20.708 6.73926H3.75" stroke="#212121" strokeWidth={1.5} strokeLinecap="round" strokeLinejoin="round" />
        <path
          d="M17.4406 6.73949C16.6556 6.73949 15.9796 6.18449 15.8256 5.41549L15.5826 4.19949C15.4326 3.63849 14.9246 3.25049 14.3456 3.25049H10.1126C9.53358 3.25049 9.02558 3.63849 8.87558 4.19949L8.63258 5.41549C8.47858 6.18449 7.80258 6.73949 7.01758 6.73949"
          stroke="#212121"
          strokeWidth={1.5}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </SvgIcon>
  );
};

export default DeleteIcon;
