import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

const ClinicalLetterIcon2 = () => {
  const theme = useTheme();
  return (
    <SvgIcon> 
      {/* <svg width="28" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g id="Group 1729">
            <path id="Vector" opacity="0.2" d="M16 6.17578H10.75V0.925781L16 6.17578Z" fill={theme.palette.text.primary} />
            <path
              id="Vector_2"
              d="M16.5306 5.64516L11.2806 0.395156C11.2109 0.325531 11.1282 0.27032 11.0371 0.232679C10.9461 0.195038 10.8485 0.175704 10.75 0.175781H1.75C1.35218 0.175781 0.970644 0.333817 0.68934 0.615121C0.408035 0.896426 0.25 1.27796 0.25 1.67578V18.1758C0.25 18.5736 0.408035 18.9551 0.68934 19.2364C0.970644 19.5177 1.35218 19.6758 1.75 19.6758H15.25C15.6478 19.6758 16.0294 19.5177 16.3107 19.2364C16.592 18.9551 16.75 18.5736 16.75 18.1758V6.17578C16.7501 6.07726 16.7307 5.97969 16.6931 5.88864C16.6555 5.7976 16.6003 5.71486 16.5306 5.64516ZM11.5 2.73609L14.1897 5.42578H11.5V2.73609ZM15.25 18.1758H1.75V1.67578H10V6.17578C10 6.37469 10.079 6.56546 10.2197 6.70611C10.3603 6.84676 10.5511 6.92578 10.75 6.92578H15.25V18.1758ZM12.25 10.6758C12.25 10.8747 12.171 11.0655 12.0303 11.2061C11.8897 11.3468 11.6989 11.4258 11.5 11.4258H5.5C5.30109 11.4258 5.11032 11.3468 4.96967 11.2061C4.82902 11.0655 4.75 10.8747 4.75 10.6758C4.75 10.4769 4.82902 10.2861 4.96967 10.1455C5.11032 10.0048 5.30109 9.92578 5.5 9.92578H11.5C11.6989 9.92578 11.8897 10.0048 12.0303 10.1455C12.171 10.2861 12.25 10.4769 12.25 10.6758ZM12.25 13.6758C12.25 13.8747 12.171 14.0655 12.0303 14.2061C11.8897 14.3468 11.6989 14.4258 11.5 14.4258H5.5C5.30109 14.4258 5.11032 14.3468 4.96967 14.2061C4.82902 14.0655 4.75 13.8747 4.75 13.6758C4.75 13.4769 4.82902 13.2861 4.96967 13.1455C5.11032 13.0048 5.30109 12.9258 5.5 12.9258H11.5C11.6989 12.9258 11.8897 13.0048 12.0303 13.1455C12.171 13.2861 12.25 13.4769 12.25 13.6758Z"
              fill={theme.palette.text.primary}
            />
          </g>
        </svg> */}
      <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          opacity="0.3"
          d="M15.7161 16.7234H8.49609"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          opacity="0.3"
          d="M15.7161 12.5369H8.49609"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          opacity="0.3"
          d="M11.2511 8.36011H8.49609"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.9085 3.24976C15.9085 3.24976 8.23149 3.25376 8.21949 3.25376C5.45949 3.27076 3.75049 5.08676 3.75049 7.85676V17.0528C3.75049 19.8368 5.47249 21.6598 8.25649 21.6598C8.25649 21.6598 15.9325 21.6568 15.9455 21.6568C18.7055 21.6398 20.4155 19.8228 20.4155 17.0528V7.85676C20.4155 5.07276 18.6925 3.24976 15.9085 3.24976Z"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </SvgIcon>
  );
};

export default ClinicalLetterIcon2;
