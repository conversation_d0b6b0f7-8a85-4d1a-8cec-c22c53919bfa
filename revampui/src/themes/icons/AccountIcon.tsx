import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

const AccountIcon = () => {
  const theme = useTheme();
  return (

    <SvgIcon>
      {/* fill={theme.palette.text.primary} */}
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="21" viewBox="0 0 16 21" fill="none">
        <path fillRule="evenodd" clipRule="evenodd" d="M7.98493 13.8462C4.11731 13.8462 0.814453 14.431 0.814453 16.7729C0.814453 19.1148 4.09636 19.7205 7.98493 19.7205C11.8525 19.7205 15.1545 19.1348 15.1545 16.7938C15.1545 14.4529 11.8735 13.8462 7.98493 13.8462Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        <path opacity="0.4" fillRule="evenodd" clipRule="evenodd" d="M7.98489 10.5059C10.523 10.5059 12.5801 8.44779 12.5801 5.90969C12.5801 3.3716 10.523 1.31445 7.98489 1.31445C5.44679 1.31445 3.3887 3.3716 3.3887 5.90969C3.38013 8.43922 5.42394 10.4973 7.95251 10.5059H7.98489Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    </SvgIcon>

  );
};

export default AccountIcon;