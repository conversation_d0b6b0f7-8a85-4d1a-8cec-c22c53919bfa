import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

interface EditIconProps {
  onClick?: any;
  component?: any;
  to?: any;
  fontSize?: any;
}

const ViewIcon: React.FC<EditIconProps> = ({ onClick, component, to, fontSize }) => {
  const theme = useTheme();
  return (
    <IconButton
      size="small"
      edge="start"
      // color="inherit"
      aria-label="open drawer"
      onClick={onClick}
      component={component}
      to={to}
      sx={{
        background: 'none !important',
        backgroundColor: 'none !important',
      }}
    >
      <SvgIcon fontSize={fontSize}>
        
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="18" viewBox="0 0 22 18" fill="none">
          <g id="Show">
            <path
              id="Stroke 1"
              opacity="0.4"
              fillRule="evenodd"
              clipRule="evenodd"
              d="M14.1614 9.05311C14.1614 10.7991 12.7454 12.2141 10.9994 12.2141C9.25338 12.2141 7.83838 10.7991 7.83838 9.05311C7.83838 7.30611 9.25338 5.89111 10.9994 5.89111C12.7454 5.89111 14.1614 7.30611 14.1614 9.05311Z"
              stroke="#212121"
              strokeWidth="1.55187"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              id="Stroke 3"
              fillRule="evenodd"
              clipRule="evenodd"
              d="M10.998 16.355C14.806 16.355 18.289 13.617 20.25 9.05298C18.289 4.48898 14.806 1.75098 10.998 1.75098H11.002C7.194 1.75098 3.711 4.48898 1.75 9.05298C3.711 13.617 7.194 16.355 11.002 16.355H10.998Z"
              stroke="#212121"
              strokeWidth="1.55187"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </g>
        </svg>
      </SvgIcon>
    </IconButton>
  );
};

export default ViewIcon;
