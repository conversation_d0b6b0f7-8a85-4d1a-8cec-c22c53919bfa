import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

export interface DashboardIconProps {}

const DashboardIcon = () => {
  const theme = useTheme();
  return (
    <IconButton
      size="large"
      edge="start"
      color="inherit"
      aria-label="open drawer"
      disableRipple
      disableFocusRipple
      disableTouchRipple
      sx={{ '&:hover': { backgroundColor: 'transparent' } }}
    >
      <SvgIcon>
        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect opacity="0.3" x="2.5" y="3" width="8" height="6" rx="2" stroke="currentColor" strokeWidth="1.8" />
          <rect x="13.5" y="3" width="8" height="10" rx="2" stroke="currentColor" strokeWidth="1.8" />
          <rect x="2.5" y="12" width="8" height="10" rx="2" stroke="currentColor" strokeWidth="1.8" />
          <rect opacity="0.3" x="13.5" y="16" width="8" height="6" rx="2" stroke="currentColor" strokeWidth="1.8" />
        </svg>
      </SvgIcon>
    </IconButton>
  );
};

export default DashboardIcon;
