import React from 'react';
import { SvgIcon, useTheme } from '@mui/material';

const GoogleIcon = () => {
  const theme = useTheme();
  return (
    <SvgIcon>
      <svg
        width={24}
        height={25}
        viewBox="0 0 24 25"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_6031_19835)">
          <path
            d="M23.5078 12.3336C23.5078 11.3503 23.4295 10.6328 23.2598 9.88867H12.2295V14.3267H18.7041C18.5736 15.4296 17.8687 17.0905 16.3022 18.2066L16.2802 18.3552L19.7678 21.1054L20.0095 21.1299C22.2285 19.0438 23.5078 15.9743 23.5078 12.3336Z"
            fill="#4285F4"
          />
          <path
            d="M12.2296 24.0272C15.4016 24.0272 18.0645 22.9641 20.0095 21.1305L16.3023 18.2072C15.3102 18.9114 13.9787 19.4031 12.2296 19.4031C9.1228 19.4031 6.48598 17.317 5.54603 14.4336L5.40825 14.4455L1.7818 17.3023L1.73438 17.4365C3.6663 21.343 7.63462 24.0272 12.2296 24.0272Z"
            fill="#34A853"
          />
          <path
            d="M5.54621 14.4332C5.2982 13.6891 5.15466 12.8918 5.15466 12.068C5.15466 11.2441 5.2982 10.4469 5.53316 9.70279L5.52659 9.54432L1.8547 6.6416L1.73457 6.69977C0.938328 8.32087 0.481445 10.1413 0.481445 12.068C0.481445 13.9947 0.938328 15.815 1.73457 17.4361L5.54621 14.4332Z"
            fill="#FBBC05"
          />
          <path
            d="M12.2295 4.73341C14.4356 4.73341 15.9237 5.70339 16.7722 6.51398L20.0878 3.21866C18.0515 1.29197 15.4015 0.109375 12.2295 0.109375C7.63462 0.109375 3.6663 2.79344 1.73438 6.69994L5.53297 9.70296C6.48597 6.81957 9.12279 4.73341 12.2295 4.73341Z"
            fill="#EB4335"
          />
        </g>
        <defs>
          <clipPath id="clip0_6031_19835">
            <rect
              width={23.04}
              height={24}
              fill="white"
              transform="translate(0.47998 0.109375)"
            />
          </clipPath>
        </defs>
      </svg>
    </SvgIcon>
  );
};

export default GoogleIcon;
