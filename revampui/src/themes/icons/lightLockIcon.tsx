
import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

const LightLockIcon = () => {
  const theme = useTheme();
  const isDarkTheme = theme.palette.mode === 'dark';
  const strokeColor = isDarkTheme ? theme.palette.text.primary : '#212121'; // #E0E0E0
  return (
    <IconButton
      size="small"
      sx={{ '&:hover': { backgroundColor: 'transparent' } }}

    >
      <SvgIcon sx={{ fontSize: 16 }}>
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 20" fill="none">
          <path opacity="0.4" d="M13.4235 7.44756V5.30056C13.4235 2.78756 11.3855 0.749556 8.87249 0.749556C6.35949 0.738556 4.31349 2.76656 4.30249 5.28056V5.30056V7.44756" stroke={strokeColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          <path fillRule="evenodd" clipRule="evenodd" d="M12.6832 19.2495H5.04224C2.94824 19.2495 1.25024 17.5525 1.25024 15.4575V11.1685C1.25024 9.07346 2.94824 7.37646 5.04224 7.37646H12.6832C14.7772 7.37646 16.4752 9.07346 16.4752 11.1685V15.4575C16.4752 17.5525 14.7772 19.2495 12.6832 19.2495Z" stroke={strokeColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          <path d="M8.86304 12.2026V14.4236" stroke={strokeColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        </svg>

      </SvgIcon>

    </IconButton>
  );
};

export default LightLockIcon;