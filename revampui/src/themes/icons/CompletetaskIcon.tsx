import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

const CompletetaskIcon = () => {
  const theme = useTheme();
  return (
    <IconButton
      size="large"
      edge="start"
      color="inherit"
      aria-label="open drawer"
      // sx={{ mr: 2 }}
    >
      <SvgIcon>
        <svg width={25} height={24} viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9.80005 11L12.8 14L22.8 4" stroke="#4ADE80" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
          <path
            opacity={0.3}
            d="M21.8 12V19C21.8 19.5304 21.5893 20.0391 21.2143 20.4142C20.8392 20.7893 20.3305 21 19.8 21H5.80005C5.26962 21 4.76091 20.7893 4.38584 20.4142C4.01076 20.0391 3.80005 19.5304 3.80005 19V5C3.80005 4.46957 4.01076 3.96086 4.38584 3.58579C4.76091 3.21071 5.26962 3 5.80005 3H16.8"
            stroke="#4ADE80"
            strokeWidth={2}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </SvgIcon>
    </IconButton>
  );
};

export default CompletetaskIcon;
