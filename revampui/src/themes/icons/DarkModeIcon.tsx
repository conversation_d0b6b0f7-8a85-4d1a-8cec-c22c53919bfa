import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon } from '@mui/material';

interface ClinicalLetterIconProps {
  onClick: () => void;
}

const ClinicalLetterIcon: React.FC<ClinicalLetterIconProps> = ({ onClick }) => {
  return (
    <IconButton
      size="large"
      disableRipple={true}
      color="inherit"
      aria-label="open drawer"
      onClick={onClick}
      sx={{ '&:hover': { backgroundColor: 'transparent' } }}
      // sx={{ mr: 2 }}
    >
      <SvgIcon>
        <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
          <circle cx="12.7236" cy="12" r="6" stroke="#212121" strokeOpacity="0.3" strokeWidth="2" />
          <path d="M12.7236 1V2M12.7236 23L12.7236 22" stroke="#212121" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          <path d="M4.94531 19.7783L5.65242 19.0712M20.5017 4.22197L19.7946 4.92908" stroke="#212121" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          <path d="M4.94531 4.22192L5.65242 4.92903M20.5017 19.7783L19.7946 19.0712" stroke="#212121" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          <path d="M1.72363 12H2.72363M23.7236 12L22.7236 12" stroke="#212121" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      </SvgIcon>
    </IconButton>
  );
};

export default ClinicalLetterIcon;
