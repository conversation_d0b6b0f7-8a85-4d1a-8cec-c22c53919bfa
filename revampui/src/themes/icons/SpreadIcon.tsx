import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

const SpreadIcon = () => {
  const theme = useTheme();
  return (
    <IconButton size="small" edge="start" color="inherit" aria-label="open drawer">
      <SvgIcon fontSize="small" sx={{ fontSize: '16px' }}>
        <svg width="14" height="4" viewBox="0 0 14 4" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M13.5 2C13.5 2.5625 13.1875 3.03125 12.75 3.3125C12.2812 3.59375 11.6875 3.59375 11.25 3.3125C10.7812 3.03125 10.5 2.5625 10.5 2C10.5 1.46875 10.7812 1 11.25 0.71875C11.6875 0.4375 12.2812 0.4375 12.75 0.71875C13.1875 1 13.5 1.46875 13.5 2ZM8.5 2C8.5 2.5625 8.1875 3.03125 7.75 3.3125C7.28125 3.59375 6.6875 3.59375 6.25 3.3125C5.78125 3.03125 5.5 2.5625 5.5 2C5.5 1.46875 5.78125 1 6.25 0.71875C6.6875 0.4375 7.28125 0.4375 7.75 0.71875C8.1875 1 8.5 1.46875 8.5 2ZM2 3.5C1.4375 3.5 0.96875 3.21875 0.6875 2.75C0.40625 2.3125 0.40625 1.71875 0.6875 1.25C0.96875 0.8125 1.4375 0.5 2 0.5C2.53125 0.5 3 0.8125 3.28125 1.25C3.5625 1.71875 3.5625 2.3125 3.28125 2.75C3 3.21875 2.53125 3.5 2 3.5Z"
            fill={theme.palette.text.primary}
          />
        </svg>
      </SvgIcon>
    </IconButton>
  );
};

export default SpreadIcon;
