import { colors, createMuiTheme } from '@material-ui/core';

const lightTheme = createMuiTheme({
  palette: {
    type: 'light',
    common: {
      white: '#fff',
    },
    action: {
      active: colors.blueGrey[600],
    },
    background: {
      default: '#fff',
      paper: '#fff',
    },
    primary: {
      light: '   ',
      main: '#F9F9F9',
      dark: '#303f9f',
      contrastText: '#fff',
    },
    secondary: {
      light: '#ff4081',
      main: '#1976d2',
      dark: 'black',
      contrastText: '#fff',
    },

    text: {
      primary: 'rgba(0, 0, 0, 0.87)',
      secondary: 'rgba(0, 0, 0, 0.54)',
      disabled: 'rgba(0, 0, 0, 0.38)',
      hint: 'rgba(0, 0, 0, 0.38)',
    },
  },
  custom: {
    primaryColor: '#fff',
    secondary: 'black',
    navbarSelectedBackground: '#fff',
    navbarBackground: '#F3F6F6',
    hover: '#EDEFF1',
    ButtonTextColor: '#0D0D0D',
    GradientButton: {
      color: '#fff',
      background: 'linear-gradient(to right, #354bb6, #495cc4, #5d6fd2, #7180e1, #8792f0, #9ba2fe)',
      '&:hover': {
        background: 'linear-gradient(to right, #1d2f74, #2b3f98, #3c4da8, #4e5cb9, #5f6bca, #6f7bc5, #8a91dc)',
        color: '#fff',
        border: 'none',
      },
    },
    NavbarBorderedButton: {
      border: '1px solid var(--black-10, rgba(28, 28, 28, 0.10)',
      background: 'transparent',
      color: '#1C1C1C',
      '&:hover': {
        background: '#F5F5F5',
        border: '1px solid #fff',
      },
    },
  },
  cards: {
    total: {
      background: '#E5ECF6',
    },
    complete: {
      background: '#E6F6E5',
    },
    inProgress: {
      background: '#FFEFD6',
    },
    needFeedback: {
      background: '#F4E3FF',
    },
    notStarted: {
      background: '#FFE3E3',
    },
  },
  customToolbar: {
    background: {
      color: '#f3f6f6',
    },
  },
  table: {
    background: '#101C2D',
    text: '#1C1C1C',
  },
  textFields: {
    background: '#131313',
  },
  inputTextColor: {
    color: '#131313',
  },

  stepper: {
    color1: 'black',
    color2: '#EBEBEB',
  },

  templateListBackground: {
    background: '#F0F0F0',
  },
  overrides: {
    MuiTextField: {
      root: {},
    },
    MuiFormHelperText: {
      root: {
        fontFamily: 'UrbanistMedium',
      },
    },
    MuiOutlinedInput: {
      root: {
        color: '#131313',
        border: '1px solid #E2E2E2',
        borderRadius: '24px',
        '&:-webkit-autofill': {
          caretColor: '#fff',
          borderRadius: 'inherit',
          boxShadow: 'none',
          WebkitTextFillColor: 'black',
        },
        backgroundColor: '#f9f9f9',
      },
      input: {
        '&:-webkit-autofill': {
          caretColor: '#fff',
          borderRadius: 'inherit',
          boxShadow: 'none',
          WebkitTextFillColor: 'black',
        },
      },
    },
    MuiCssBaseline: {
      '@global': {
        'input:-webkit-autofill': {
          '-webkit-box-shadow': 'transparent !important',
          '-webkit-text-fill-color': 'inherit !important',
          transition: 'background-color 5000s ease-in-out !important',
        },
        'input:-webkit-autofill:hover': {
          '-webkit-box-shadow': 'transparent !important',
          '-webkit-text-fill-color': 'inherit !important',
        },
        'input:-webkit-autofill:focus': {
          '-webkit-box-shadow': 'transparent !important',
          '-webkit-text-fill-color': 'inherit !important',
        },
        'input:-webkit-autofill:active': {
          '-webkit-box-shadow': 'transparent !important',
          '-webkit-text-fill-color': 'inherit !important',
        },
      },
    },

    MuiButton: {
      root: {
        background: '#2D2869',
        '&:hover': {
          background: '#F5F5F5',
          border: '1px solid #2D2869',
          boxShadow:
            '0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12)',
        },
      },
      text: {
        color: '#FFF',
        '&:hover': {
          color: '#0D0D0D',
        },
      },
    },
  },
});

export default lightTheme;
