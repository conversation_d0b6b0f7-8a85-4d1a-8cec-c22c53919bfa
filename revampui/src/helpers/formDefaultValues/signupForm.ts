import { IForgotPassword, ISignin, TRecaptcha } from '../../models/IAuthState';
import {
  IClinicInfoFormValues,
  ICreatePassword,
  IHospitalFormValues,
  IResetPassword,
  IResetPasswordRequest,
  ISignupTypes,
} from '../../models/ISignup';

const clicnicInformation: IClinicInfoFormValues = {
  name: '',
  address: '',
  country_id: '',
  county_id: '',
  town_id: '',
  phone: null,
  post_code: '',
  // user_type: ISignupTypes.CLINIC,
};

const hospitalOwner: IHospitalFormValues = {
  first_name: '',
  last_name: '',
  email: '',
  // phone: null,
  // clinical_specializations_id: '',
  user_type: ISignupTypes.CLINIC,
  clinic_id: '',
  organisation_name: '',
};

const createPassword: ICreatePassword = {
  password: '',
  confirmPassword: '',
  termsCheck: false,
};

const signin: ISignin & TRecaptcha = {
  email: '',
  password: '',
  recaptcha: null,
  rememberMe: false,
};

const forgotpassword: IForgotPassword & TRecaptcha = {
  email: '',
  recaptcha: null,
};

const resetPassword: IResetPassword = {
  token: '',
  password: '',
  confirmPassword: '',
};

const resetPasswordRequest: IResetPasswordRequest = {
  token: '',
  password: '',
};

const defaultValues = {
  clicnicInformation,
  hospitalOwner,
  createPassword,
  signin,
  forgotpassword,
  resetPassword,
  resetPasswordRequest,
};

export default defaultValues;
