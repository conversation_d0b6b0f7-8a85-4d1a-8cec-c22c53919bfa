export enum IAuthActionTypes {
  LOGIN_REQUEST = 'AUTH/LOGIN_REQUEST',
  LOGIN_SUCCESS = 'AUTH/LOGIN_SUCESS',
  LOGIN_SUBMIT_OTP = 'AUTH/LOGIN_SUBMIT_OTP',
  LOGI<PERSON>_RESEND_OTP = 'AUTH/LOGIN_RESEND_OTP',
  LOGIN_FAILURE = 'AUTH/LOGIN_FAILURE',
  SILENT_LOGIN = 'AUTH/SILENT_LOGIN',
  LOGOUT = 'AUTH/LOGOUT',
  REGISTER = 'AUTH/REGISTER',
  REGISTER_SUCCESS = 'REGISTER_SUCCESS',
  REGISTER_FAILURE = 'REGISTER_FAILURE',
  REGISTER_CLINIC = 'REGISTER_CLINIC',
  REGISTER_CLINIC_SUCCESS = 'REGISTER_CLINIC_SUCCESS',
  REGISTER_CLINIC_FAILURE = 'REGISTER_CLINIC_FAILURE',
  FORGOT_PASSWORD = 'FORGOT_PASSWORD',
  FORGOT_PASSWORD_SUCCESS = 'FORGOT_PASSWORD_SUCCESS',
  FORGOT_PASSWORD_FAILURE = 'FORGOT_PASSWORD_FAILURE',
  RESET_PASSWORD = 'RESET_PASSWORD',
  RESET_PASSWORD_SUCCESS = 'RESET_PASSWORD_SUCCESS',
  RESET_PASSWORD_FAILURE = 'RESET_PASSWORD_FAILURE',
  REFRESH_USER = 'REFRESH_USER',
  UPDATE_PROFILE = 'UPDATE_PROFILE',
  UPDATE_PROFILE_DETAILS = 'UPDATE_PROFILE_DETAILS',
  SET_PERMISSIONS = 'SET_PERMISSIONS',
  UPDATE_REMAINING_MINUTES = 'UPDATE_REMAINING_MINUTES',
}

export type User = {
  id: number;
  role_id: number;
  user_type: string;
  reg_gmc_no: string | null;
  clinical_specializations_id: number;
  first_name: string;
  last_name: string;
  country: string;
  county_id: string;
  town_id: string;
  address: string;
  pincode: string;
  phone: string;
  email: string;
  profile_image_url: string | null;
  status: number;
  is_email_verified: boolean;
  created_at: string;
  updated_at: string;
  owner_user_id: string | null;
  others: string | null;
  credit_documents: number;
  subscription_documents: number;
  stripe_customer_id: string | null;
  subscription_dictation_minutes: number;
  subscription_team_members: number;
  subscription_additional_document_price: number | null;
  subscription_currency: string | null;
  subscription_status: string | null;
  role_name: string;
  roledetailcount: string;
  specializations_name: string;
  usertype: string;
  clinic_id: string;
  clinic_name: string;
  header_image: string | null;
  footer_image: string | null;
  owner_info: string | null;
  invitedUsersCount: number;
  profileS3Url: string;
  remaining_audio_consultation_minutes: number;
  remaining_custom_templates: number | null;
  remaining_design_templates: number;
  has_invite_user: boolean;
  has_medical_history: boolean;
  has_user_logs: boolean;
  has_tasks: boolean;
  allows_team_members: boolean;
  subscription_plan_name: string;
  subscriptionPlan : {
    price : string | null;
    plan_id : number | null;
  }
};

export type TRecaptcha = {
  recaptcha: string | null;
};

export type IAuthState = {
  user: User | null;
  role: string | null;
  data: [] | null;
  otpPending: boolean | false;
  resetSuccess: boolean | false;
  resetFailure: boolean | false;
  isLoading: boolean | false;
  permissions: [] | null;
};

export type IAuthActionCreator = {
  type: string;
  payload: IAuthState;
};

export type ISignin = {
  email: string;
  password: string;
  rememberMe: boolean;
};

export type IForgotPassword = {
  email: string;
};

export type IVerifyOTP = {
  otp: string;
  email: string;
  keepMeLoggedIn: boolean;
};
