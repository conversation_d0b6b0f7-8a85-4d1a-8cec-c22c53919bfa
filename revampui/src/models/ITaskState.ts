export enum ITaskActionTypes {
  SET_TASK_IN_STORE_REQUEST = 'TASK/SET_TASK_IN_STORE_REQUEST',
  SET_TASK_IN_STORE_SUCCESS = 'TASK/SET_TASK_IN_STORE_SUCCESS',
  SET_TASK_IN_STORE_FAILURE = 'TASK/SET_TASK_IN_STORE_FAILURE',
  CLEAR_TASK_IN_STORE = 'TASK/CLEAR_TASK_IN_STORE',
}

export type ITaskState = {
  task: any;
  loading: boolean;
};

export type ITaskActionCreator = {
  type: string;
  payload: ITaskState;
};

export type ITaskFormValues = {
  patient_id: number;
  template_id: number;
  user_id: number;
  notes: string;
  header_html: string;
  body_html: string;
  footer_html: string;
};
