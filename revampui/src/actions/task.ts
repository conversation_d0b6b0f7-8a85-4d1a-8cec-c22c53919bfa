import { Dispatch } from 'redux';
import { ITaskActionTypes } from '../models/ITaskState';
import { getTask } from '../services/tasks';
import { enqueueSnackbarAction } from './app.action';

export const setTaskInStore = (taskId: any) => async (dispatch: Dispatch<any>, getState: any) => {
  try {
    dispatch({ type: ITaskActionTypes.SET_TASK_IN_STORE_REQUEST });
    const task = await getTask(taskId);
    dispatch({
      type: ITaskActionTypes.SET_TASK_IN_STORE_SUCCESS,
      payload: {
        task,
      },
    });
  } catch (e) {
    dispatch(
      enqueueSnackbarAction({
        // @ts-ignore
        message: `Error: ${e?.response?.data?.msg}`,
        key: new Date().getTime() + Math.random(),
        variant: 'error',
      }),
    );
    dispatch({ type: ITaskActionTypes.SET_TASK_IN_STORE_FAILURE });
  }
};
