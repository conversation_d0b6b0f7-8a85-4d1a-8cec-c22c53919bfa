import { Dispatch } from 'redux';
import { useSelector } from 'react-redux';
import { IRoleManagementActionType } from '../models/IRoleManagementState';
import { enqueueSnackbarAction } from './app.action';
import { getRolesList } from '../services/permissions';
import { IAppActionTypes } from '../models/IAppState';
import { roleListSelector } from '../selectors/roleManagement.selector';

export const updateRoleManagementList = (auth: any) => async (dispatch: Dispatch<any>) => {
  dispatch({ type: IAppActionTypes.SET_LOADING, payload: true });
  try {
    const list = await getRolesList(auth);
    dispatch({
      type: IRoleManagementActionType.SET_ROLE_LIST,
      payload: { list },
    });
  } catch (e) {
    dispatch(
      enqueueSnackbarAction({
        // @ts-ignore
        message: `Error: ${e?.response?.data?.msg}`,
        key: new Date().getTime() + Math.random(),
        variant: 'error',
      }),
    );
  } finally {
    dispatch({ type: IAppActionTypes.SET_LOADING, payload: false });
  }
};

export const applyRoleListSorting = (
  roleList: any[] = [],
  order: 'asc' | 'desc',
  orderBy: 'name' | 'role_descriptions' | 'type',
) => {
  if (!Array.isArray(roleList)) {
    return [];
  }
  const sortKey = orderBy === 'type' ? 'name' : orderBy;

  return roleList.sort((a, b) => {
    if (a[sortKey] && b[sortKey]) {
      return order === 'asc' ? a[sortKey].localeCompare(b[sortKey]) : b[sortKey].localeCompare(a[sortKey]);
    }
    return 0;
  });
};

export const sortRoleList =
  (roleList: any[], order: 'asc' | 'desc', orderBy: 'name' | 'role_descriptions' | 'type') => async (dispatch: Dispatch<any>) => {
    dispatch({ type: IAppActionTypes.SET_LOADING, payload: true });
    const list = applyRoleListSorting(roleList, order, orderBy);

    dispatch({
      type: IRoleManagementActionType.SET_ROLE_LIST,
      payload: { list },
    });

    dispatch({ type: IAppActionTypes.SET_LOADING, payload: false });
  };
