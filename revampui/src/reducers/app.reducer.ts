import { IAppActionTypes, IAppActionCreator, IAppState } from 'models/IAppState';

const initialState: IAppState = {
  isLoading: false,
  updateReduxRemainingMinutes: false,
  remainingTime: 0,
  dialog: {
    type: 'error',
    isShow: false,
    content: '',
  },
  notifications: {},
  navigation: {
    breadcrumb: '',
  },
  tasks: {
    selectedTask: null,
  },
  clinic: null,
  pageEditable: false,
  specializations: [],
};

const reducer = (state = initialState, { type, payload }: IAppActionCreator) => {
  switch (type) {
    case IAppActionTypes.SET_CLINIC:
      return {
        ...state,
        clinic: payload,
      };
    case IAppActionTypes.UPDATE_REDUX_MINUTES:
      return {
        ...state,
        updateReduxRemainingMinutes: payload.updateToggle,
        remainingTime: payload.Time,
      };
    case IAppActionTypes.SET_BREADCRUMB:
      return {
        ...state,
        navigation: {
          breadcrumb: payload,
        },
      };
    case IAppActionTypes.SET_SELECTED_TASK:
      return {
        ...state,
        tasks: {
          selectedTask: payload,
        },
      };
    case IAppActionTypes.SET_LOADING:
      return {
        ...state,
        isLoading: payload,
      };
    case IAppActionTypes.SET_DIALOG:
      return {
        ...state,
        dialog: {
          type: payload.dialog.type,
          isShow: payload.dialog.isShow,
          content: payload.dialog.content,
        },
      };
    case IAppActionTypes.ENQUEUE_SNACKBAR: {
      const { key, message, variant } = payload;
      return {
        ...state,
        notifications: {
          ...state.notifications,
          [key]: {
            key,
            message,
            variant,
          },
        },
      };
    }
    case IAppActionTypes.REMOVE_SNACKBAR: {
      const newNotfi = { ...state.notifications };
      delete newNotfi[payload];
      return {
        ...state,
        notifications: newNotfi,
      };
    }
    case IAppActionTypes.SET_PAGE_EDITABLE: {
      return {
        ...state,
        pageEditable: payload,
      };
    }
    case IAppActionTypes.SET_SPECIALIZATIONS:
      return {
        ...state,
        specializations: payload,
      };
    default:
      return state;
  }
};

export default reducer;
