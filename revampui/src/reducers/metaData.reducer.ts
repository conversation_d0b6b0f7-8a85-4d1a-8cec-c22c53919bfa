import normalizer from '../helpers/normalizer/metaData';
import { IMetaDataCreator, IMetaDataState, IMetaDataTypes } from '../models/IMetaDataTypes';

const initialState: IMetaDataState = {
  data: null,
};

const reducer = (state = initialState, { type, payload }: IMetaDataCreator) => {
  switch (type) {
    case IMetaDataTypes.SPECIALIZATION_SUCCESS:
      return {
        ...state,
        data: normalizer.specialization(payload.data || []),
      };
    case IMetaDataTypes.SPECIALIZATION_FAILURE:
      return {
        ...state,
        data: null,
      };
    default:
      return state;
  }
};

export default reducer;
