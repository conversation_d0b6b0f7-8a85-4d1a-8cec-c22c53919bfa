import { ITaskActionTypes, ITaskActionCreator, ITaskState } from 'models/ITaskState';

const initialState: ITaskState = {
  task: null,
  loading: false,
};

const reducer = (state = initialState, { type, payload }: ITaskActionCreator) => {
  switch (type) {
    case ITaskActionTypes.SET_TASK_IN_STORE_REQUEST:
      return {
        ...state,
        loading: true,
      };
    case ITaskActionTypes.SET_TASK_IN_STORE_SUCCESS:
      return {
        ...state,
        task: payload.task,
        loading: false,
      };
    case ITaskActionTypes.SET_TASK_IN_STORE_FAILURE:
      return {
        ...state,
        task: null,
        loading: false,
      };
    case ITaskActionTypes.CLEAR_TASK_IN_STORE:
      return {
        ...state,
        task: null,
      };
    default:
      return state;
  }
};

export default reducer;
