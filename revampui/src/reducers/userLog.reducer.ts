import { IUserLogActionCreator, IUserLogActionTypes, IUserLogState } from '../models/IUserLogState';

const initialState: IUserLogState = {
  list: [],
  currentItem: {},
  startDate: undefined,
  endDate: undefined,
  searchStr: '',
  showUserLogDownload: false,
};

const reducer = (state = initialState, { type, payload }: IUserLogActionCreator) => {
  switch (type) {
    case IUserLogActionTypes.USER_LOG_SUCCESS:
      return {
        ...state,
        list: payload,
      };
    case IUserLogActionTypes.SET_SHOW_USER_LOG_DOWNLOAD:
      return {
        ...state,
        showUserLogDownload: payload,
      };
    case IUserLogActionTypes.SET_CURRENT_ITEM:
      return {
        ...state,
        currentItem: payload,
      };
    case IUserLogActionTypes.SET_START_DATE:
      return {
        ...state,
        startDate: payload,
      };
    case IUserLogActionTypes.SET_END_DATE:
      return {
        ...state,
        endDate: payload,
      };
    case IUserLogActionTypes.SET_SEARCH_STR:
      return {
        ...state,
        searchStr: payload,
      };

    default:
      return state;
  }
};

export default reducer;
