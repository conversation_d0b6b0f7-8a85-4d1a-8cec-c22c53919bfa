import { IRoleManagementActionCreator, IRoleManagementActionType, IRoleManagementState } from '../models/IRoleManagementState';

const initialState: IRoleManagementState = {
  roleList: [],
};

const reducer = (state = initialState, { type, payload }: IRoleManagementActionCreator) => {
  switch (type) {
    case IRoleManagementActionType.SET_ROLE_LIST:
      return {
        ...state,
        roleList: payload.list,
      };

    default:
      return state;
  }
};

export default reducer;
