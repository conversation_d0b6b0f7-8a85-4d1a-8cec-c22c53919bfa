import { combineReducers } from 'redux';

// reducers
import app from 'reducers/app.reducer';
import auth from 'reducers/auth.reducer';
import metaData from 'reducers/metaData.reducer';
import clinicalLetter from 'reducers/clinicalLetter.reducer';
import task from 'reducers/task.reducer';
import roleManagement from 'reducers/roleManagement.reducer';
import userLog from 'reducers/userLog.reducer';

const reducers = combineReducers({
  app,
  auth,
  clinicalLetter,
  metaData,
  task,
  roleManagement,
  userLog,
});

export default reducers;
