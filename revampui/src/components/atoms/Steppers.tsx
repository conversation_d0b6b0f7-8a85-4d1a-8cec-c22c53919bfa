// @ts-nocheck
import React, { useState } from 'react';
import { Stepper, Step, Box, makeStyles, Theme } from '@material-ui/core';

const useStyles = makeStyles((theme: Theme) => ({
  stepperBox: {
    // cursor: 'pointer',
  },
  stepper: {
    padding: 0,
    paddingTop: theme.spacing(1.5),
    paddingBottom: theme.spacing(1),
    '& .MuiStep-horizontal': {
      paddingLeft: 0,
    },
  },
}));

interface SteppersProps {
  activeStep: number;
  total: number;
}

const Steppers = ({ activeStep, total }: SteppersProps) => {
  const classes = useStyles();
  // const [activeStep, setActiveStep] = useState(0);
  //
  // const handleStepChange = (step: any) => {
  //   setActiveStep(step);
  // };

  return (
    <Stepper activeStep={activeStep} connector={<div className="hiddenConnector" />} className={classes.stepper}>
      {[...Array(total).keys()].map((step) => (
        <Step key={step}>
          <Box
            bgcolor={activeStep >= step ? 'black' : 'transparent'}
            color={activeStep >= step ? 'white' : 'black'}
            // onClick={() => handleStepChange(step)}
            width={50}
            height={3}
            display="flex"
            alignItems="center"
            justifyContent="center"
            border="1px solid"
            borderColor={activeStep === step ? 'transparent' : 'black'}
            borderRadius={8}
            className={classes.stepperBox}
          >
            {/* <StepLabel>{`Step ${step + 1}`}</StepLabel> */}
          </Box>
        </Step>
      ))}
    </Stepper>
  );
};

export default Steppers;
