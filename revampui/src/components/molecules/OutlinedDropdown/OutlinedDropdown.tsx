import React from 'react';
import { Select, MenuItem, FormControl } from '@material-ui/core';
import useStyles from './styles';
import { IDropdown } from '../../../models/IFormFields';

const OutlinedDropdown = ({ formik, id, name, placeholder, options, disabled }: IDropdown) => {
  const classes = useStyles();

  return (
    <FormControl fullWidth variant="outlined" className={classes.outlinedInputRoot}>
      <Select
        id={id}
        name={name}
        placeholder={placeholder}
        value={formik.values[name] || ''}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        disabled={disabled}
        error={formik.touched[name] && Boolean(formik.errors[name])}
      >
        {options?.map((option, index) => {
          return (
            <MenuItem key={index} value={option.value} className={classes.menuItem}>
              {option.label}
            </MenuItem>
          );
        })}
      </Select>
      {formik.touched[name] && formik.errors[name] && <span>{formik.errors[name]}</span>}
    </FormControl>
  );
};

export default OutlinedDropdown;
