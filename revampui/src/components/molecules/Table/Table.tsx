import React from 'react';
import PropTypes from 'prop-types';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Checkbox from '@material-ui/core/Checkbox';
import Paper from '@material-ui/core/Paper';
import Typography from '@material-ui/core/Typography';

const CustomizableTable = ({ showCheckboxColumn, headerTextColor, rows, renderRowData, renderCellData }: any) => {
  return (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            {showCheckboxColumn && <TableCell />}
            {renderCellData.map((_cellData: { header: string; index: number }) => (
              <TableCell key={_cellData.index}>
                <Typography style={{ color: headerTextColor }}>{_cellData.header}</Typography>
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {rows.map((_row: { row: any; rowIndex: number }) => (
            <TableRow key={_row.rowIndex}>
              {showCheckboxColumn && (
                <TableCell>
                  <Checkbox />
                </TableCell>
              )}
              {renderRowData.map((_data: { cellData: any; cellIndex: number }) => (
                <TableCell key={_data.cellIndex}>{_data.cellData(_row.row)}</TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

CustomizableTable.propTypes = {
  showCheckboxColumn: PropTypes.bool,
  headerTextColor: PropTypes.string,
  // eslint-disable-next-line react/forbid-prop-types
  rows: PropTypes.array.isRequired,
  renderRowData: PropTypes.arrayOf(PropTypes.func).isRequired,
  renderCellData: PropTypes.arrayOf(PropTypes.func).isRequired,
};

CustomizableTable.defaultProps = {
  showCheckboxColumn: false,
  headerTextColor: '#000000',
};

export default CustomizableTable;
