import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles((theme) => ({
  formControl: {
    minWidth: 120,
  },
  nativeSelect: {
    fontFamily: 'UrbanistBold',
    fontSize: 14,
    '&:focus': {
      backgroundColor: 'transparent',
      padding: theme.spacing(4),
    },
    '&:before': {
      display: 'none',
    },
    '&:after': {
      display: 'none',
    },
    '& option': {
      padding: '8px',
      fontFamily: 'UrbanistMedium',
      fontSize: 14,
      backgroundColor: theme.palette.background.default,
    },
  },
}));

export default useStyles;
