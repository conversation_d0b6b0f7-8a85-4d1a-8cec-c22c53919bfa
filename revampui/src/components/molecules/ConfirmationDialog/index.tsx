import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Slide from '@mui/material/Slide';
import { TransitionProps } from '@mui/material/transitions';

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

// props interface
interface AlertDialogSlideProps {
  open: boolean;
  title: string;
  content: string;
  handleOk: () => void;
  handleCancel: () => void;
}

export default function AlertDialogSlide({ title, content, open, handleOk, handleCancel }: AlertDialogSlideProps) {
  const onHandleClose = () => {
    handleCancel && handleCancel();
  };

  const onHandleOk = () => {
    handleOk && handleOk();
  };

  return (
    <>
      <Dialog
        open={open}
        TransitionComponent={Transition}
        keepMounted
        onClose={onHandleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        <DialogTitle>{title}</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-slide-description">{content}</DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button sx={{ borderRadius: '35px', padding: '7px 20px', height: '35px', width: '75px' }} onClick={onHandleClose}>
            Cancel
          </Button>
          <Button sx={{ borderRadius: '35px', padding: '7px 20px', height: '35px', width: '75px' }} onClick={onHandleOk}>
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
