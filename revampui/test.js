// function transformObjectAtoB(objectA) {
//   const transformedObject = {};
//
//   // Define mappings for all keys in Object A
//   const mappings = [
//     { source: "healthManagement.carePlan", key: "healthManagement.carePlan" },
//     { source: "healthManagement.recommendations", key: "healthManagement.recommendations" },
//     { source: "otherInformation", key: "otherInformation" },
//     { source: "presentingProblem.problemList", key: "presentingProblem.problemList" },
//     { source: "vitalsAndObservations.vitals", key: "vitalsAndObservations.vitals" },
//     { source: "vitalsAndObservations.observations", key: "vitalsAndObservations.observations" },
//     { source: "familyAndSocialHistory.familyHistory", key: "familyAndSocialHistory.familyHistory" },
//     { source: "familyAndSocialHistory.socialHistory", key: "familyAndSocialHistory.socialHistory" },
//     { source: "medicationsAndAllergies.allergies", key: "medicationsAndAllergies.allergies" },
//     { source: "medicationsAndAllergies.pastMedications", key: "medicationsAndAllergies.pastMedications" },
//     { source: "medicationsAndAllergies.currentMedications", key: "medicationsAndAllergies.currentMedications" },
//     { source: "pastMedicalAndSurgicalHistory.surgicalHistory", key: "pastMedicalAndSurgicalHistory.surgicalHistory" },
//     { source: "pastMedicalAndSurgicalHistory.pastMedicalHistory", key: "pastMedicalAndSurgicalHistory.pastMedicalHistory" },
//     { source: "labResultsAndDiagnosticImaging.labResults", key: "labResultsAndDiagnosticImaging.labResults" },
//     { source: "labResultsAndDiagnosticImaging.diagnosticImaging", key: "labResultsAndDiagnosticImaging.diagnosticImaging" },
//   ];
//
//   // Transform Object A based on the mappings
//   mappings.forEach(({ source, key }) => {
//     const value = source.split('.').reduce((acc, curr) => acc?.[curr], objectA);
//     if (value !== undefined) {
//       transformedObject[key] = {
//         new: value,
//         old: '', // Replace this with actual old data if available
//       };
//     }
//   });
//
//   return transformedObject;
// }
//
// // Example usage
// const objectA = {
//   healthManagement: {
//     carePlan:
//       "<ul><li>Reduce fever and cough</li><li>Improve respiratory function</li><li>Enhance overall comfort</li><li>Achieve full recovery from COVID-19</li><li>Restore energy levels</li><li>Return to normal daily activities</li></ul>",
//     recommendations:
//       "<ul><li>Limit physical exertion and avoid crowded places</li><li>Stay hydrated and monitor symptoms</li><li>Rest as needed</li><li>Use over-the-counter medications for symptom relief as directed</li><li>Maintain a balanced diet rich in vitamins and minerals</li></ul>",
//   },
//   otherInformation:
//     "<ul><li>Monitor temperature and respiratory symptoms daily</li><li>Schedule follow-up appointments as needed</li><li>Contact healthcare provider if severe symptoms occur</li></ul>",
//   presentingProblem: {
//     problemList:
//       "<ul><li>COVID-19 with moderate symptoms including fever, cough, and fatigue</li></ul>",
//   },
//   vitalsAndObservations: {
//     vitals:
//       "<ul><li>Temperature: [Latest temperature]</li><li>Oxygen Saturation: [Latest oxygen saturation]</li><li>Blood Pressure: [Latest blood pressure]</li><li>Heart Rate: [Latest heart rate]</li></ul>",
//     observations: "<ul><li>Moderate symptoms observed</li><li>No wheezing noted</li></ul>",
//   },
//   familyAndSocialHistory: {
//     familyHistory: "<ul><li>No relevant family medical history reported</li></ul>",
//     socialHistory: "<ul><li>No smoking status reported</li><li>No pet exposure reported</li></ul>",
//   },
//   medicationsAndAllergies: {
//     allergies: "<ul><li>No known allergies reported</li></ul>",
//     pastMedications: "<ul><li>No past medications reported</li></ul>",
//     currentMedications: "<ul><li>[Medication prescribed]: [Dosage and frequency]</li></ul>",
//   },
//   pastMedicalAndSurgicalHistory: {
//     surgicalHistory: "<ul><li>No surgical history reported</li></ul>",
//     pastMedicalHistory: "<ul><li>No significant past medical history reported</li></ul>",
//   },
//   labResultsAndDiagnosticImaging: {
//     labResults: "<ul><li>No recent lab results reported</li></ul>",
//     diagnosticImaging: "<ul><li>No recent diagnostic imaging reported</li></ul>",
//   },
// };
//
// const objectB = transformObjectAtoB(objectA);
// console.log(objectB);


////////////////////////////////////////////////////////////////////////////////////////////


function mapMedicalHistoryHierarchical(entries) {
  const result = {};

  entries.forEach(entry => {
    Object.entries(entry.content).forEach(([section, content]) => {
      // Ensure the section exists in the result
      if (!result[section]) {
        result[section] = {};
      }

      // Handle content as subsections if it's an object
      if (typeof content === "object" && !Array.isArray(content)) {
        Object.entries(content).forEach(([subsection, info]) => {
          if (!result[section][subsection]) {
            result[section][subsection] = [];
          }
          result[section][subsection].push({
            info: info,
            time: entry.created_at
          });
        });
      } else {
        // Handle general content directly if it's not an object
        if (!result[section].general) {
          result[section].general = [];
        }
        result[section].general.push({
          info: content,
        });
      }
    });
  });

  return result;
}

// Example Usage
const entries = [
  {
    "id": 13,
    "patient_id": 17,
    "letter_id": 270,
    "user_id": 1,
    "content": {
      "healthManagement": {
        "carePlan": "<ul><li>Rest and avoid strenuous activities.</li><li>Stay hydrated, use over-the-counter medications as needed for symptom relief, and monitor temperature regularly.</li><li>Maintain a balanced diet with plenty of fluids; consider warm soups and herbal teas.</li><li><br></li></ul>",
        "recommendations": "<ul><li>Follow a balanced diet rich in fruits, vegetables, and hydration.</li><li>Gradually increase activity levels as tolerated; avoid strenuous activities for at least two weeks.</li><li>If applicable, follow standard wound care procedures.</li><li>Contact attending physician or primary care physician for any concerns or questions.</li></ul>"
      },
      "otherInformation": "<ul><li>Rest and avoid strenuous activities.</li><li>Stay hydrated, use over-the-counter medications as needed for symptom relief, and monitor temperature regularly.</li><li>Maintain a balanced diet with plenty of fluids; consider warm soups and herbal teas.</li></ul><p><br></p>",
      "presentingProblem": {
        "problemList": "<ul><li>aCOVID-19ssw2sdaaaxascwwwwwwwwwwww</li></ul>"
      },
      "vitalsAndObservations": {
        "vitals": "<ul><li>Rest and avoid strenuous activities.</li><li>Stay hydrated, use over-the-counter medications as needed for symptom relief, and monitor temperature regularly.</li><li>Maintain a balanced diet with plenty of fluids; consider warm soups and herbal teas.</li></ul><p><br></p>",
        "observations": "<ul><li>Patient was monitored closely for respiratory function and vital signs.</li><li>Condition improved over the course of treatment.</li><li>Stable vitals maintained.</li></ul>"
      },
      "familyAndSocialHistory": {
        "familyHistory": "<p>aaaa</p>",
        "socialHistory": "<ul><li>Rest and avoid strenuous activities.</li><li>Stay hydrated, use over-the-counter medications as needed for symptom relief, and monitor temperature regularly.</li><li>Maintain a balanced diet with plenty of fluids; consider warm soups and herbal teas.</li></ul><p><br></p>"
      },
      "medicationsAndAllergies": {
        "allergies": "<p>aaaa</p>",
        "pastMedications": "<p>aaa</p>",
        "currentMedications": "<p>aaaa</p>"
      },
      "pastMedicalAndSurgicalHistory": {
        "surgicalHistory": "<p>fewa</p>",
        "pastMedicalHistory": "<p>aaaa</p>"
      },
      "labResultsAndDiagnosticImaging": {
        "labResults": "<ul><li>Rest and avoid strenuous activities.</li><li>Stay hydrated, use over-the-counter medications as needed for symptom relief, and monitor temperature regularly.</li><li>Maintain a balanced diet with plenty of fluids; consider warm soups and herbal teas.</li></ul><p><br></p>",
        "diagnosticImaging": "<ul><li>Rest and avoid strenuous activities.</li><li>Stay hydrated, use over-the-counter medications as needed for symptom relief, and monitor temperature regularly.</li><li>Maintain a balanced diet with plenty of fluids; consider warm soups and herbal teas.</li></ul><p><br></p>"
      }
    },
    "created_at": "2024-11-26T11:07:58.908Z",
    "updated_at": "2024-12-19T16:57:18.332Z",
    "users": {
      "id": 1,
      "first_name": "Luffy",
      "last_name": "Monkey D",
      "email": "<EMAIL>",
      "phone": "03205757964",
      "country": "Pakistan",
      "state": "Punjab",
      "city": "Lahore",
      "address": "222"
    }
  },
  {
    "id": 12,
    "patient_id": 17,
    "letter_id": 270,
    "user_id": 1,
    "content": {
      "healthManagement": {
        "carePlan": "<ul><li>Supportive care including oxygen therapy as needed</li><li>Monitor respiratory function and vital signs</li></ul>",
        "recommendations": "<ul><li>Follow a balanced diet rich in fruits, vegetables, and hydration</li><li>Gradually increase activity levels as tolerated; avoid strenuous activities for at least two weeks</li><li>Contact attending physician or primary care physician for any concerns</li></ul>"
      },
      "otherInformation": "<ul></ul>",
      "presentingProblem": {
        "problemList": "<ul><li>COVID-19ddw</li></ul>"
      },
      "vitalsAndObservations": {
        "vitals": "<ul></ul>",
        "observations": "<ul><li>No wheezing</li><li>Improved breathing</li></ul>"
      },
      "familyAndSocialHistory": {
        "familyHistory": "<ul></ul>",
        "socialHistory": "<ul></ul>"
      },
      "medicationsAndAllergies": {
        "allergies": "<ul></ul>",
        "pastMedications": "<ul></ul>",
        "currentMedications": "<ul></ul>"
      },
      "pastMedicalAndSurgicalHistory": {
        "surgicalHistory": "<ul></ul>",
        "pastMedicalHistory": "<ul></ul>"
      },
      "labResultsAndDiagnosticImaging": {
        "labResults": "<ul></ul>",
        "diagnosticImaging": "<ul></ul>"
      }
    },
    "created_at": "2024-11-26T11:04:26.856Z",
    "updated_at": "2024-12-19T16:56:43.631Z",
    "users": {
      "id": 1,
      "first_name": "Luffy",
      "last_name": "Monkey D",
      "email": "<EMAIL>",
      "phone": "03205757964",
      "country": "Pakistan",
      "state": "Punjab",
      "city": "Lahore",
      "address": "222"
    }
  },
  {
    "id": 11,
    "patient_id": 17,
    "letter_id": 269,
    "user_id": 1,
    "content": {
      "healthManagement": {
        "carePlan": "<ul><li>Reduce fever, alleviate cough, and improve overall comfort.</li><li>Full recovery from flu symptoms and prevention of complications.</li></ul>",
        "recommendations": "<ul><li>Monitor symptoms daily, including temperature and overall well-being.</li><li>Schedule a follow-up appointment in one week or sooner if symptoms worsen.</li><li>Reevaluation if symptoms persist beyond expected recovery time or if new symptoms develop.</li></ul>"
      },
      "otherInformation": "<ul><li>Signs to watch for: Difficulty breathing, persistent high fever, or worsening symptoms that require immediate medical attention.d</li><li>Emergency contacts: [Who to contact in case of an emergency]</li><li>Plan created by: [Clinician's Name], [Clinician's Title]</li></ul>",
      "presentingProblem": {
        "problemList": "<ul><li>Flu</li></ul>"
      },
      "vitalsAndObservations": {
        "vitals": "<ul></ul>",
        "observations": "<ul><li>Moderate flu symptoms including fever, cough, and fatigue.</li></ul>"
      },
      "familyAndSocialHistory": {
        "familyHistory": "<ul></ul>",
        "socialHistory": "<ul><li>Rest and avoid strenuous activities.</li><li>Stay hydrated, use over-the-counter medications as needed for symptom relief, and monitor temperature regularly.</li><li>Maintain a balanced diet with plenty of fluids; consider warm soups and herbal teas.</li></ul>"
      },
      "medicationsAndAllergies": {
        "allergies": "<ul></ul>",
        "pastMedications": "<ul></ul>",
        "currentMedications": "<ul><li>[Medication prescribed]: [Dosage and frequency]</li></ul>"
      },
      "pastMedicalAndSurgicalHistory": {
        "surgicalHistory": "<ul></ul>",
        "pastMedicalHistory": "<ul></ul>"
      },
      "labResultsAndDiagnosticImaging": {
        "labResults": "<ul></ul>",
        "diagnosticImaging": "<ul></ul>"
      }
    },
    "created_at": "2024-11-26T09:22:12.155Z",
    "updated_at": "2024-11-27T13:10:23.454Z",
    "users": {
      "id": 1,
      "first_name": "Luffy",
      "last_name": "Monkey D",
      "email": "<EMAIL>",
      "phone": "03205757964",
      "country": "Pakistan",
      "state": "Punjab",
      "city": "Lahore",
      "address": "222"
    }
  },
  {
    "id": 10,
    "patient_id": 17,
    "letter_id": 268,
    "user_id": 1,
    "content": {
      "healthManagement": {
        "carePlan": "<ul><li>Alleviate symptoms of fever and respiratory distress</li><li>Reduce viral load and prevent complications</li><li>Achieve full recovery from both COVID-19 and influenza</li><li>Restore normal respiratory function and overall health</li></ul>",
        "recommendations": "<ul><li>Limit physical activity to essential tasks; rest as needed.</li><li>Monitor symptoms closely; maintain hydration and nutrition; use over-the-counter medications as directed for symptom relief.</li><li>Increase fluid intake; consider a balanced diet rich in vitamins and minerals to support immune function.</li></ul>"
      },
      "otherInformation": "<ul><li>Regularly assess symptoms and overall health status; utilize pulse oximetry to monitor oxygen levels.</li><li>Schedule follow-up appointments as needed based on symptom progression.</li><li>If symptoms worsen or do not improve within a specified timeframe, reevaluate the treatment plan.</li><li>Signs to watch for: Increased difficulty breathing, persistent high fever, or any new concerning symptoms.</li><li>Contact the healthcare provider or emergency services if severe symptoms develop.asd</li></ul>",
      "presentingProblem": {
        "problemList": "<ul><li>COVID-19dwa</li><li>Influenzaedwascaxs</li></ul>"
      },
      "vitalsAndObservations": {
        "vitals": "<ul></ul>",
        "observations": "<ul><li>Moderate respiratory distress</li><li>Fever</li><li>Fatigue</li></ul>"
      },
      "familyAndSocialHistory": {
        "familyHistory": "<ul></ul>",
        "socialHistory": "<ul></ul>"
      },
      "medicationsAndAllergies": {
        "allergies": "<ul></ul>",
        "pastMedications": "<ul></ul>",
        "currentMedications": "<ul><li>[Medication prescribed] - [Dosage and frequency]</li></ul>"
      },
      "pastMedicalAndSurgicalHistory": {
        "surgicalHistory": "<ul></ul>",
        "pastMedicalHistory": "<ul></ul>"
      },
      "labResultsAndDiagnosticImaging": {
        "labResults": "<ul></ul>",
        "diagnosticImaging": "<ul></ul>"
      }
    },
    "created_at": "2024-11-18T11:04:42.822Z",
    "updated_at": "2024-11-27T12:45:01.649Z",
    "users": {
      "id": 1,
      "first_name": "Luffy",
      "last_name": "Monkey D",
      "email": "<EMAIL>",
      "phone": "03205757964",
      "country": "Pakistan",
      "state": "Punjab",
      "city": "Lahore",
      "address": "222"
    }
  },
  {
    "id": 9,
    "patient_id": 17,
    "letter_id": null,
    "user_id": 1,
    "content": {
      "healthManagement": {
        "carePlan": "<ul></ul>",
        "recommendations": "<ul>></ul>"
      },
      "otherInformation": "<ul></ul>",
      "presentingProblem": {
        "problemList": "<p>aefrefewda</p>"
      },
      "vitalsAndObservations": {
        "vitals": "<ul></ul>",
        "observations": "<ul></ul>"
      },
      "familyAndSocialHistory": {
        "familyHistory": "<ul></ul>",
        "socialHistory": "<ul></ul>"
      },
      "medicationsAndAllergies": {
        "allergies": "<ul></ul>",
        "pastMedications": "<ul></ul>",
        "currentMedications": "<ul></ul>"
      },
      "pastMedicalAndSurgicalHistory": {
        "surgicalHistory": "<ul></ul>",
        "pastMedicalHistory": "<ul></ul>"
      },
      "labResultsAndDiagnosticImaging": {
        "labResults": "<ul></ul>",
        "diagnosticImaging": "<ul></ul>"
      }
    },
    "created_at": "2024-11-18T11:01:13.812Z",
    "updated_at": "2024-11-26T09:11:48.604Z",
    "users": {
      "id": 1,
      "first_name": "Luffy",
      "last_name": "Monkey D",
      "email": "<EMAIL>",
      "phone": "03205757964",
      "country": "Pakistan",
      "state": "Punjab",
      "city": "Lahore",
      "address": "222"
    }
  }
];

// Call the function to map the data
const structuredData = mapMedicalHistoryHierarchical(entries);
console.log(JSON.stringify(structuredData, null, 2));


function printMedicalHistory(structuredData) {
  for (const section in structuredData) {
    console.log(`${section.replace(/([A-Z])/g, ' $1').trim()}:`); // Format section names
    const subsections = structuredData[section];
    for (const subsection in subsections) {
      console.log(`  - ${subsection.replace(/([A-Z])/g, ' $1').trim()}:`);
      const entries = subsections[subsection];
      entries.forEach(entry => {
        console.log(`    - Info: ${entry.info}`);
        console.log(`      Patient ID: ${entry.patient_id}`);
        console.log(`      Time: ${entry.time}`);
        // console.log(`      Letter ID: ${entry.letter_id}`);
        // console.log(`      Created At: ${entry.created_at}`);
        // console.log(`      Updated At: ${entry.updated_at}`);
        // console.log(`      User: ${entry.user.first_name} ${entry.user.last_name}`);
      });
      console.log();
    }
  }
}

printMedicalHistory(structuredData);

function main() {
  console.log('main');
}
