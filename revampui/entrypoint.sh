#!/bin/bash

set -eo pipefail
COMMAND="$@"

### below command will replace the variables in env.php
sed -i \
-e 's/'PORT-HERE'/'$PORT_STRING'/' \
-e 's/'REACT-APP-DRAWER-WIDTH-HERE'/'$REACT_APP_DRAWER_WIDTH_STRING'/' \
-e 's/'REACT-APP-THEME-HERE'/'$REACT_APP_THEME_STRING'/' \
-e 's/'REACT-APP-LANGUAGE-HERE'/'$REACT_APP_LANGUAGE_STRING'/' \
-e 's/'REACT-APP-DELAY-GET-DATA-HERE'/'$REACT_APP_DELAY_GET_DATA_STRING'/' \
-e 's/'REACT-APP-DEBOUNCE-TIME-HERE'/'$REACT_APP_DEBOUNCE_TIME_STRING'/' \
-e 's/'REACT-APP-MAX-SNACKBAR-HERE'/'$REACT_APP_MAX_SNACKBAR_STRING'/' \
-e 's/'REACT-APP-AUTO-HIDE-SNACKBAR-HERE'/'$REACT_APP_AUTO_HIDE_SNACKBAR_STRING'/' \
-e 's/'REACT-APP-PUBLIC-URL-HERE'/'$REACT_APP_PUBLIC_URL_STRING'/' \
-e 's/'REACT-APP-ENDPOINT-URL-HERE'/'$REACT_APP_ENDPOINT_URL_STRING'/' \
-e 's/'REACT-APP-SITE-KEY-HERE'/'$REACT_APP_SITE_KEY_STRING'/' \
-e 's/'REACT-APP-SECRET-KEY-HERE'/'$REACT_APP_SECRET_KEY_STRING'/' \
-e 's/'SITE-SECRET-HERE'/'$SITE_SECRET_STRING'/' \
-e 's/'REACT-APP-TINYMCE-API-KEY-HERE'/'$REACT_APP_TINYMCE_API_KEY_STRING'/' \
-e 's/'REACT-APP-DEEPGRAM-API-KEY-HERE'/'$REACT_APP_DEEPGRAM_API_KEY_STRING'/' \
/usr/share/nginx/html/.env

nginx -g 'daemon off;'
