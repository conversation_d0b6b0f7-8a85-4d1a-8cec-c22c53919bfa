// // import { js<PERSON><PERSON> } from "jspdf";
// const { jsPDF } = require('jspdf')
//
// // Function to generate a PDF
// const generateMedicalHistoryPDF = (medicalHistoryData) => {
//   const doc = new jsPDF();
//
//   // Define some margins and line spacing
//   const marginX = 10;
//   let currentY = 10; // Start position for the first line
//   const lineHeight = 7;
//
//   // Loop through each record
//   medicalHistoryData.forEach((entry, index) => {
//     const { content, users, created_at } = entry;
//
//     // Add patient information
//     doc.setFontSize(12);
//     doc.text(`Patient Name: ${users.first_name} ${users.last_name}`, marginX, currentY);
//     currentY += lineHeight;
//     doc.text(`Patient Email: ${users.email}`, marginX, currentY);
//     currentY += lineHeight;
//     doc.text(`Created At: ${new Date(created_at).toLocaleString()}`, marginX, currentY);
//     currentY += lineHeight * 2; // Extra space after header
//
//     // Add content sections
//     Object.entries(content).forEach(([section, details]) => {
//       doc.setFontSize(14);
//       doc.setFont("helvetica", "bold");
//       doc.text(section.replace(/([A-Z])/g, " $1").trim(), marginX, currentY); // Add section title
//       currentY += lineHeight;
//
//       doc.setFontSize(12);
//       doc.setFont("helvetica", "normal");
//
//       // Decode HTML content
//       const parser = new DOMParser();
//       const decodedDetails = parser.parseFromString(details, "text/html").body.textContent || "";
//
//       const lines = doc.splitTextToSize(decodedDetails, 190); // Wrap text to fit the page width
//       lines.forEach((line) => {
//         if (currentY > 280) {
//           // Add a new page if the content exceeds page height
//           doc.addPage();
//           currentY = 10;
//         }
//         doc.text(line, marginX, currentY);
//         currentY += lineHeight;
//       });
//
//       currentY += lineHeight; // Space after each section
//     });
//
//     // Add a page break if there is another record
//     if (index < medicalHistoryData.length - 1) {
//       doc.addPage();
//       currentY = 10;
//     }
//   });
//
//   // Save the PDF
//   doc.save("medical_history.pdf");
// };
//
// // Example data
// const medicalHistoryData = [
//   {
//     id: 13,
//     patient_id: 17,
//     letter_id: 270,
//     user_id: 1,
//     content: {
//       healthManagement: {
//         carePlan: "<ul><li>Supportive care provided, including oxygen therapy as needed.</li></ul>",
//         recommendations:
//           "<ul><li>Follow a balanced diet rich in fruits, vegetables, and hydration.</li><li>Gradually increase activity levels as tolerated; avoid strenuous activities for at least two weeks.</li><li>If applicable, follow standard wound care procedures.</li><li>Contact attending physician or primary care physician for any concerns or questions.</li></ul>",
//       },
//       presentingProblem: {
//         problemList: "<ul><li>COVID-19</li></ul>",
//       },
//       vitalsAndObservations: {
//         observations:
//           "<ul><li>Patient was monitored closely for respiratory function and vital signs.</li><li>Condition improved over the course of treatment.</li><li>Stable vitals maintained.</li></ul>",
//       },
//     },
//     created_at: "2024-11-26T11:07:58.908Z",
//     users: {
//       id: 1,
//       first_name: "Luffy",
//       last_name: "Monkey D",
//       email: "<EMAIL>",
//       phone: "03205757964",
//       country: "Pakistan",
//       state: "Punjab",
//       city: "Lahore",
//       address: "222",
//     },
//   },
// ];
//
// // Generate PDF
// generateMedicalHistoryPDF(medicalHistoryData);


import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import * as pdfjsLib from 'pdfjs-dist';

const pdfWorkerUrl = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.2.67/pdf.worker.min.mjs';
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfWorkerUrl;

export const toPixel = (cm: number) => Math.round((cm * 96) / 2.54);

export const toCentimeter = (px: number) => (px * 2.54) / 96;

const cropCanvas = (originalCanvas: HTMLCanvasElement, cropY: number, cropWidth: number, cropHeight: number) => {
  const cropX = 0;
  const croppedCanvas = document.createElement('canvas');
  const croppedCtx = croppedCanvas.getContext('2d');
  croppedCanvas.width = cropWidth;
  croppedCanvas.height = cropHeight;
  if (croppedCtx) {
    croppedCtx.drawImage(originalCanvas, cropX, cropY, cropWidth, cropHeight, 0, 0, cropWidth, cropHeight);
  }
  return croppedCanvas;
};

export const generateImageFromHTML = async (html: string, width: number) => {
  const htmlDiv = document.createElement('div');
  const random = Math.floor(Math.random() * 1000000);
  htmlDiv.id = `dynamicDiv${random}`;
  htmlDiv.style.zIndex = '-999';
  htmlDiv.style.width = `${width}px`;
  htmlDiv.innerHTML = html;
  // maybe to change back
  htmlDiv.style.backgroundColor = 'white';
  htmlDiv.style.color = 'black';
  document.body.appendChild(htmlDiv);
  const canvas = await html2canvas(htmlDiv, { removeContainer: true, x: 0, y: 0, width: width, scale: 1 });
  document.body.removeChild(htmlDiv);
  return canvas;
};

export async function generatePdf(headerHtml: string, bodyHtml: string, footerHtml: string): Promise<jsPDF> {
  // eslint-disable-next-line new-cap
  const pdfDocument = new jsPDF({
    format: 'letter',
    unit: 'cm',
    userUnit: 300,
  });
  const headerMargin = 1.5;
  const footerMargin = 1.5;
  const pageWidth = pdfDocument.internal.pageSize.getWidth();
  const pageHeight = pdfDocument.internal.pageSize.getHeight();
  const scaledWidth = pageWidth * 0.8; // Reduce width by 20%
  const margin = pageWidth * 0.1;
  const header = await generateImageFromHTML(headerHtml, toPixel(pageWidth));
  const headerHeight = toCentimeter(Number(header.getAttribute('height')));
  const scaledHeaderHeight = headerHeight * (scaledWidth / pageWidth);
  const headerData = headerHtml ? header.toDataURL('image/png') : '';
  if (headerHtml) {
    pdfDocument.addImage(headerData, 'PNG', margin, headerMargin, scaledWidth, scaledHeaderHeight);
  }

  const footer = await generateImageFromHTML(footerHtml, toPixel(pageWidth));
  const footerHeight = toCentimeter(Number(footer.getAttribute('height')));
  const scaledFooterHeight = footerHeight * (scaledWidth / pageWidth);
  const footerData = footerHtml ? footer.toDataURL('image/png') : '';
  if (footerHtml) {
    pdfDocument.addImage(footerData, 'PNG', margin, pageHeight - scaledFooterHeight - footerMargin, scaledWidth, scaledFooterHeight);
  }

  if (bodyHtml !== '') {
    const heightAvailable = pageHeight - scaledHeaderHeight - scaledFooterHeight - headerMargin - footerMargin - 0.5 - 0.5; // 1 cm padding between footer and body
    const yCoordinate = scaledHeaderHeight + headerMargin + 0.5; // 0.5 cm padding between header and body
    const htmlPages = bodyHtml?.split('<!-- pagebreak -->');
    if (htmlPages) {
      /* eslint-disable no-await-in-loop */
      for (let i = 0; i < htmlPages.length; i++) {
        const pageHtml = htmlPages[i];
        const body = await generateImageFromHTML(pageHtml, toPixel(pageWidth));
        const bodyWidth = toCentimeter(Number(body.getAttribute('width')));
        const bodyHeight = toCentimeter(Number(body.getAttribute('height')));
        const scaledBodyHeight = bodyHeight * (scaledWidth / pageWidth);
        const scaledXAvailableHeight = heightAvailable * (pageWidth / scaledWidth);
        const noOfPages = Math.ceil(scaledBodyHeight / heightAvailable);

        if (noOfPages > 1) {
          const firstPage = cropCanvas(body, 0, toPixel(bodyWidth), toPixel(scaledXAvailableHeight));
          const firstPageData = firstPage.toDataURL('image/png');
          pdfDocument.addImage(firstPageData, 'PNG', margin, yCoordinate, scaledWidth, heightAvailable);
          for (let i = 1; i < noOfPages; i++) {
            const page = cropCanvas(body, toPixel(scaledXAvailableHeight * i), toPixel(pageWidth), toPixel(scaledXAvailableHeight));
            const pageData = page.toDataURL('image/png');
            pdfDocument.addPage();
            if (headerData) pdfDocument.addImage(headerData, 'PNG', margin, headerMargin, scaledWidth, scaledHeaderHeight);
            if (footerData) pdfDocument.addImage(footerData, 'PNG', margin, pageHeight - scaledFooterHeight - footerMargin, scaledWidth, scaledFooterHeight);
            if (i + 1 === noOfPages) {
              const pageHeight = toCentimeter(Number(page.getAttribute('height')));
              const scaledPageHeight = pageHeight * (scaledWidth / pageWidth);
              pdfDocument.addImage(pageData, 'PNG', margin, yCoordinate, scaledWidth, scaledPageHeight);
              break;
            } else {
              pdfDocument.addImage(pageData, 'PNG', margin, yCoordinate, scaledWidth, heightAvailable);
            }
          }
        } else {
          const bodyData = body.toDataURL('image/png');
          pdfDocument.addImage(bodyData, 'PNG', margin, yCoordinate, scaledWidth, scaledBodyHeight);
        }

        if (i < htmlPages.length - 1) {
          pdfDocument.addPage();
          if (headerData) pdfDocument.addImage(headerData, 'PNG', margin, headerMargin, scaledWidth, scaledHeaderHeight);
          if (footerData) pdfDocument.addImage(footerData, 'PNG', margin, pageHeight - scaledFooterHeight - footerMargin, scaledWidth, scaledFooterHeight);
        }
      }
    }
    /* eslint-enable no-await-in-loop */
  }

  return pdfDocument;
}

export async function generateImage(headerHtml: string, footerHtml: string): Promise<File> {
  const pageWidth = 850;
  const pageHeight = 1100;
  const scaledWidth = pageWidth * 0.8; // Reduce width by 20%
  const margin = pageWidth * 0.1;

  const mainDiv = document.createElement('div');
  const random = Math.floor(Math.random() * 1000000);
  mainDiv.id = `dynamicDiv${random}`;
  mainDiv.style.width = `${pageWidth}px`;
  mainDiv.style.zIndex = '-999';

  const footerHtmlWithPadding = `${footerHtml}<p>&nbsp;</p>`;
  const footer = await generateImageFromHTML(footerHtmlWithPadding, pageWidth);
  const footerHeight = Number(footer.getAttribute('height'));
  const headerHeight = pageHeight - footerHeight;

  const headerDiv = document.createElement('div');
  headerDiv.style.width = `${scaledWidth}px`;
  headerDiv.style.height = `${headerHeight}px`;
  headerDiv.style.paddingTop = '57px';
  headerDiv.style.paddingLeft = '85px';
  headerDiv.innerHTML = headerHtml;
  mainDiv.appendChild(headerDiv);

  const footerDiv = document.createElement('div');
  footerDiv.style.width = `${scaledWidth}px`;
  footerDiv.style.paddingLeft = '85px';
  footerDiv.innerHTML = footerHtmlWithPadding;
  mainDiv.appendChild(footerDiv);

  document.body.appendChild(mainDiv);
  const canvas = await html2canvas(mainDiv, { removeContainer: true, x: 0, y: 0, width: 859, scale: 1 });
  document.body.removeChild(mainDiv);
  // const imgData = canvas.toDataURL('image/png');
  const canvasBlob = await new Promise((resolve) => canvas.toBlob(resolve));
  const image = new File([canvasBlob as BlobPart], 'image.png', { type: 'image/png' });
  return image;
}
