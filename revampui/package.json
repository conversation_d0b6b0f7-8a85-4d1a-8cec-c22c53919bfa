{"name": "react-boilerplate-material", "version": "1.1.1", "private": true, "dependencies": {"@casl/ability": "^5.3.1", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@ffmpeg/ffmpeg": "^0.12.15", "@material-ui/core": "^4.11.0", "@material-ui/icons": "^4.9.1", "@material-ui/lab": "^4.0.0-alpha.57", "@mui/icons-material": "^5.15.17", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.15", "@mui/utils": "^5.15.14", "@mui/x-date-pickers": "^7.5.0", "@testing-library/jest-dom": "^5.11.6", "@testing-library/react": "^11.2.0", "@testing-library/user-event": "^12.2.2", "@tinymce/tinymce-react": "^5.0.1", "@types/jest": "^26.0.15", "@types/node": "^12.19.5", "@types/react": "^16.9.56", "@types/react-dom": "^18.3.0", "apexcharts": "^3.27.3", "auth0-js": "^9.14.0", "autosuggest-highlight": "^3.1.1", "axios": "^0.21.0", "clsx": "^1.1.1", "compromise": "^14.14.3", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dayjs": "^1.11.11", "env-cmd": "^10.1.0", "formik": "^2.4.5", "html2canvas": "^1.4.1", "i18next": "^19.8.4", "i18next-browser-languagedetector": "^6.0.1", "jspdf": "^2.5.1", "lodash": "^4.17.20", "notistack": "^1.0.3", "pdf-lib": "^1.17.1", "pdfjs-dist": "^4.2.67", "pell": "^1.0.6", "prop-types": "^15.8.1", "react": "^18.3.1", "react-apexcharts": "^1.3.9", "react-datepicker": "^7.5.0", "react-dom": "^18.3.1", "react-error-boundary": "^3.0.2", "react-google-recaptcha": "^3.1.0", "react-i18next": "^11.7.3", "react-otp-input": "^3.1.1", "react-portal": "^4.2.1", "react-quill": "^2.0.0", "react-redux": "^7.2.2", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.1", "react-window": "^1.8.6", "redux": "^4.0.5", "redux-devtools-extension": "^2.13.8", "redux-persist": "^6.0.0", "redux-thunk": "^2.3.0", "reselect": "^4.0.0", "source-map-explorer": "^2.5.2", "styled-components": "^6.1.11", "tesseract.js": "^5.1.1", "typescript": "^4.0.5", "wavesurfer.js": "^4.6.0", "web-vitals": "^0.2.4", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "build:dev": "env-cmd -f .env.development react-scripts build", "build:stage": "env-cmd -f .env.staging react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint --ext .ts,.tsx src --color", "lint:fix": "eslint --ext .ts,.tsx src --color --fix", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "isready": "npm run format && npm run lint && npm run build", "docker": "docker build -t react-docker .", "react-docker": "docker run -p 3000:80 react-docker", "analyze": "source-map-explorer 'build/static/js/*.js'", "husky-install": "husky install & npx husky add .husky/pre-commit 'yarn run lint-staged' & npx husky add .husky/commit-msg  'pre-received' "}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/auth0-js": "^9.14.2", "@types/autosuggest-highlight": "^3.1.1", "@types/crypto-js": "^4.2.2", "@types/lodash": "^4.14.165", "@types/pell": "^1.0.4", "@types/react-google-recaptcha": "^2.1.9", "@types/react-portal": "^4.0.2", "@types/react-redux": "^7.1.11", "@types/react-router-dom": "^5.1.6", "@types/react-window": "^1.8.2", "@types/wavesurfer.js": "^3.3.2", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-eslint": "^10.1.0", "eslint": "^7.13.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-airbnb-typescript": "^12.0.0", "eslint-config-airbnb-typescript-prettier": "^4.0.0", "eslint-config-prettier": "^6.15.0", "eslint-config-react-app": "^6.0.0", "eslint-import-resolver-typescript": "^2.3.0", "eslint-loader": "^4.0.2", "eslint-plugin-flowtype": "^5.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "husky": "^8.0.3", "lint-staged": "^13.2.3", "prettier": "^2.1.2", "prettier-eslint": "^11.0.0", "prettier-eslint-cli": "^5.0.0", "pretty-quick": "^3.1.0"}, "lint-staged": {"*.{ts,tsx}": ["npm run lint", "prettier --write"]}}