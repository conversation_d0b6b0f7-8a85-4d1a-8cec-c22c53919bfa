import cryptoService from "../crypto.service";
import httpRequest from "../httpRequest";

class BillingServices {

  getCoupons = async () => {
    return await httpRequest.get('/subscription-plan/coupons')
    .then(response => response.data.data);
  };

  createDiscount = async (discountData: any) => {
    const encryptedData = cryptoService.encrypt(discountData);
    const response = await httpRequest.post("/subscription-plan/coupons", { encrypet: encryptedData });
    return response.data;
  };

  //TODO NO ENCRYPTED DATA BEING ACCEPTED AT BACKEND
  applyCoupon = async (couponData: any) => {
    const payload = cryptoService.encrypt(couponData);
    const response = await httpRequest.post("/subscription-plan/coupons/apply", { encrypet: payload });
    return response.data;
  };

  getBillingStats = async (filter: string = "ALL") => {
    const response = await httpRequest.get(`/subscription-plan/billing-stats?filter=${filter}`);
      //const decryptedData = cryptoService.decrypt(response.data.data);
      return response.data.data;
    }
}

export default new BillingServices();
