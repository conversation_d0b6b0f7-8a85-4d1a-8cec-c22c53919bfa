
// @ts-nocheck
import axios, { AxiosRequestConfig, AxiosError } from 'axios';
import { customNavigate } from '../hooks/useCustomNavigate';

// actions
import { IAuthActionTypes } from '../models/IAuthState';

export type IConfig = AxiosRequestConfig & {
  showSpinner?: boolean;
  encrypet?: string;
};

type IAxiosResponse = AxiosError & {
  config: {
    showSpinner?: boolean;
  };
};

function getAccessToken() {
  const accessToken = window.localStorage.getItem('accessTokenAdmin');
  return accessToken;
}

const requestConfig: IConfig = {
  baseURL: process.env.REACT_APP_BASE_API_ADMIN_URL,
  timeout: 120000,
  showSpinner: false,
};

export const axiosInstance = axios.create(requestConfig);

const { CancelToken } = axios;
const cancel: any = null;

export default function initRequest(store: any) {
  let requestCount = 0;

  function decreaseRequestCount() {
    requestCount -= 1;
    if (requestCount === 0) {
      //store.dispatch(setLoading(false));
    }
  }

  axiosInstance.interceptors.request.use(
    (config: IConfig) => {
      // cancel token
      // if (cancel) {
      //   cancel(); // cancel request
      // }
      // console.log('Cancelign request');
      // config.cancelToken = new CancelToken(function executor(c) {
      //   cancel = c;
      // });

      // show loading
      if (config.showSpinner) {
        requestCount += 1;
       // store.dispatch(setLoading(true));
      }

      // add x-auth-token
      const accessToken = getAccessToken();
      if (accessToken) {
        // config.headers['x-auth-token'] = accessToken;
        config.headers.Authorization = `Bearer ${accessToken}`;
      }

      return config;
    },
    (error: IAxiosResponse) => {
      if (error.config.showSpinner) {
        decreaseRequestCount();
      }
      return Promise.reject(error);
    },
  );

  axiosInstance.interceptors.response.use(
    (res: any) => {
      if (res.config.showSpinner) {
        decreaseRequestCount();
      }
      return res;
    },
    async (error: IAxiosResponse) => {
      if ((error && error?.config?.showSpinner) || error.code === 'ECONNABORTED') {
        decreaseRequestCount();
      }

      // handle request timeout
      if (error.code === 'ECONNABORTED') {
       // store.dispatch(setLoading(false));
      }

      // access token expired
      if (error.response?.status === 401) {
        // error.config._retry = true;
        try {
          const result = await axiosInstance.post('/api/user/refresh-token', {
            data: {
              refresh_token: window.localStorage.getItem('refreshTokenAdmin'),
            },
          });

          window.localStorage.setItem('accessTokenAdmin', result.data.data.access_token);
          axiosInstance.defaults.headers.common['x-auth-token'] = result.data.data.access_token; // (option 1)
          // axiosInstance.defaults.headers.common.Authorization = `Bearer ${result.data.accessToken}`; // (option 2)

          return await axiosInstance(error.config);
        } catch (err: any) {
          store.dispatch({ type: IAuthActionTypes.LOGOUT });
          localStorage.removeItem('userAdmin');
          localStorage.removeItem('signatureAdmin');
          localStorage.removeItem('refreshTokenAdmin');
          localStorage.removeItem('accessTokenAdmin');
          customNavigate('/login');
          if (err.response && err.response.data) {
            return Promise.reject(err.response.data);
          }
          return Promise.reject(err);
        }
      }

      // handle errors
      switch (error.response?.status) {
        case 400: {
          break;
        }
        case 500: {
          break;
        }
        default:
          break;
      }
      return Promise.reject(error);
    },
  );
}
