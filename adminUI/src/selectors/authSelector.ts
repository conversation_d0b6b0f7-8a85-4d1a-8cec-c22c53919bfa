import { RootState } from "../app/store";

// Using reselect for memoization (optional)
import { createSelector } from "@reduxjs/toolkit";

// Simple selectors
export const selectAuthState = (state: RootState) => state.auth;
export const selectUser = (state: RootState) => state.auth.user;
export const selectIsLoggedIn = (state: RootState) => state.auth.isLoggedIn;
export const selectAuthData = (state: RootState) => state.auth.data;
export const selectOtpPending = (state: RootState) => state.auth.otpPending;

export const selectUserMemoized = createSelector(
  selectAuthState,
  (auth) => auth.user
);

export const selectIsLoggedInMemoized = createSelector(
  selectAuthState,
  (auth) => auth.isLoggedIn
);
