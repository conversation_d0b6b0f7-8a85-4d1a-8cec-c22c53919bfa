import { createSlice, PayloadAction } from "@reduxjs/toolkit";

type BreadcrumbObject = {
  path: string; // The path (e.g., '/analytics')
  heading?: string; // The heading (e.g., 'Analytics Dashboard') - optional
};

type INotifier = {
  key: number;
  message: string;
  variant: "success" | "error" | "warning" | "info";
};

// Define the AppState type
type AppState = {
  drawerOpen: boolean;
  breadcrumb: string | BreadcrumbObject;
  notifications: Record<number, INotifier>;
  loading: boolean; // Add loading state
};

// Initial state
const initialState: AppState = {
  drawerOpen: true,
  breadcrumb: '/analytics',
  notifications: {},
  loading: false, // Initialize loading as false
};

// Create the slice
const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    reset: () => initialState,

    setDrawerOpen: (state, action: PayloadAction<boolean>) => {
      state.drawerOpen = action.payload;
    },

    setBreadcrumb: (state, action: PayloadAction<string | BreadcrumbObject>) => {
      state.breadcrumb = action.payload;
    },

    enqueueSnackbar: (state, action: PayloadAction<INotifier>) => {
      state.notifications[action.payload.key] = action.payload;
    },

    removeSnackbar: (state, action: PayloadAction<number>) => {
      delete state.notifications[action.payload];
    },

    // Add loading state actions
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
  },
});

export const appActions = appSlice.actions;

// Export reducer as default
export default appSlice.reducer