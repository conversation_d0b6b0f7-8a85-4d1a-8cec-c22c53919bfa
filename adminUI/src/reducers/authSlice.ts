import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export type User = {
  id: number;
  role_id: number;
  user_type: string;
  reg_gmc_no: string | null;
  clinical_specializations_id: number;
  first_name: string;
  last_name: string;
  country: string;
  county_id: string;
  town_id: string;
  address: string;
  pincode: string;
  phone: string;
  email: string;
  profile_image_url: string | null;
  status: number;
  is_email_verified: boolean;
  created_at: string;
  updated_at: string;
  owner_user_id: string | null;
  others: string | null;
  credit_documents: number;
  subscription_documents: number;
  stripe_customer_id: string | null;
  subscription_dictation_minutes: number;
  subscription_team_members: number;
  subscription_additional_document_price: number | null;
  subscription_currency: string | null;
  subscription_status: string | null;
  role_name: string;
  roledetailcount: string;
  specializations_name: string;
  usertype: string;
  clinic_id: string;
  clinic_name: string;
  header_image: string | null;
  footer_image: string | null;
  owner_info: string | null;
  invitedUsersCount: number;
  profileS3Url: string;
};

type IAuthState = {
  user: User | null;
  role: string | null;
  data: [] | null;
  otpPending: boolean;
  resetSuccess: boolean;
  resetFailure: boolean;
  isLoading: boolean;
  permissions: [] | null;
  isLoggedIn: boolean;
};

const initialState: IAuthState = {
  user: null,
  role: null,
  data: null,
  otpPending: false,
  resetSuccess: false,
  resetFailure: false,
  isLoading: false,
  permissions: null,
  isLoggedIn: false
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    reset: () => initialState,
    setUser: (state, action: PayloadAction<User | null>) => {
      if (action.payload) {
        state.user = action.payload;
   //     localStorage.setItem('user', JSON.stringify(action.payload));
      } else {
        state.user = null;
        localStorage.removeItem('userAdmin');
      }
    },
    setIsLoggedIn: (state, action: PayloadAction<boolean>) => {
      state.isLoggedIn = action.payload;
    },
    setLoginSuccess: (state, action: PayloadAction<any> ) => {
        state.otpPending = true;
    },
    setLogout: (state  ) => {
      state.user = null;
      state.role = null;
      state.data = null;
      state.otpPending = false;
      state.resetSuccess = false;
      state.resetFailure = false;
      state.isLoading = false;
      state.permissions = null;
      state.isLoggedIn = false;
      localStorage.removeItem('userAdmin');
    }
  },
});

export const authActions = authSlice.actions;
export default authSlice.reducer;