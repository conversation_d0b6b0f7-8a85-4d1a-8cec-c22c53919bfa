
@font-face {
  font-family: "GilroyRegular";
  src: local("GilroyRegular"),
    url("../theme/fonts/Gilroy-Regular.ttf") format("truetype");
}
@font-face {
  font-family: "GilroyBold";
  src: local("GilroyBold"),
    url("../theme/fonts/Gilroy-Bold.ttf") format("truetype");
}
@font-face {
  font-family: "GilroyMedium";
  src: local("GilroyMedium"),
    url("../theme/fonts/Gilroy-Medium.ttf") format("truetype");
}

@font-face {
  font-family: "GilroyBlack";
  src: local("GilroyBlack"),
    url("../theme/fonts/Gilroy-Black.ttf") format("truetype");
}

body {
  margin: 0;
  font-family: GilroyRegular, GilroyMedium, GilroyBold, GilroyBlack;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

::-webkit-scrollbar-track {
}

::-webkit-scrollbar {
  width: 6px;
  background-color: #f5f5f5;
}

::-webkit-scrollbar-thumb {
  border-radius: 3px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}


.dot {
  display: flex;
  align-items: center;
}

.dot::before {
  content: "\2022"; /* Unicode character for bullet point */
  /*color: black; !* Color of the bullet *!*/
  margin-right: 0.5em; /* Adjust the distance between the bullet and the text */
  font-size: 26px;
  padding-top: 2px;
}

.status-need-feedback {
  color: #9B00FF;
  font-size: 12px;
  font-feature-settings: 'cv11' on, 'cv01' on, 'ss01' on;
  font-style: normal;
  font-weight: 500;
  line-height: 18px; /* 150% */
}

.status-Active {
  color: #49BD42;
  font-size: 12px;
  font-feature-settings: 'cv11' on, 'cv01' on, 'ss01' on;
  font-style: normal;
  font-weight: 500;
  line-height: 18px;  
}

.status-Resolved {
  color: #4F74FC;
  font-size: 12px;
  font-feature-settings: 'cv11' on, 'cv01' on, 'ss01' on;
  font-style: normal;
  font-weight: 500;
  line-height: 18px;
}
.status-InActive {
  color: #F00;
  font-size: 12px;
  font-feature-settings: 'cv11' on, 'cv01' on, 'ss01' on;
  font-style: normal;
  font-weight: 500;
  line-height: 18px; /* 150% */
}

.status-in-progress {
  color: #F90;
  font-size: 12px;
  font-feature-settings: 'cv11' on, 'cv01' on, 'ss01' on;
  font-style: normal;
  font-weight: 500;
  line-height: 18px; /* 150% */
}