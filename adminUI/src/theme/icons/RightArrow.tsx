import React, { FC } from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

interface DropDownArrowProps {
  onClick?: any;
}

const RightArrow: FC<DropDownArrowProps> = ({ onClick }) => {
    const theme = useTheme();
    return (

      <IconButton size="large" edge="start" color="inherit" onClick={onClick} aria-label="open drawer">
        <SvgIcon>
        <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="22" height="22" rx="11"   fill={'none'}/>
        <path d="M14.9766 10.1484C15.332 10.4766 15.332 11.0508 14.9766 11.3789L9.72656 16.6289C9.39844 16.9844 8.82422 16.9844 8.49609 16.6289C8.14062 16.3008 8.14062 15.7266 8.49609 15.3984L13.1172 10.75L8.49609 6.12891C8.14062 5.80078 8.14062 5.22656 8.49609 4.89844C8.82422 4.54297 9.39844 4.54297 9.72656 4.89844L14.9766 10.1484Z"   fill={theme.palette.text.primary}/>
</svg>

        </SvgIcon>
      </IconButton>
  );
};

export default RightArrow;
