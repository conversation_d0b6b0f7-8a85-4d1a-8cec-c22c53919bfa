import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

interface EditIconProps {
  onClick?: any;
  component?: any;
  to?: any;
  fontSize?: any;
} 

const ViewIcon: React.FC<EditIconProps> = ({ onClick, component, to, fontSize }) => {
  const theme = useTheme();
  return (
    <IconButton
      size="small"
      edge="start" 
      aria-label="open drawer"
      onClick={onClick}
      component={component}
      to={to}
      disableRipple={true}
    >
      <SvgIcon fontSize={fontSize}>
        <svg width="18" height="18" viewBox="0 0 18 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g id="Group 1723">
            <path
              id="Vector"
              opacity="0.2"
              d="M9.00157 0.599854C3.00156 0.599854 0.601562 5.99986 0.601562 5.99986C0.601562 5.99986 3.00156 11.3999 9.00157 11.3999C15.0016 11.3999 17.4016 5.99986 17.4016 5.99986C17.4016 5.99986 15.0016 0.599854 9.00157 0.599854ZM9.00157 8.99986C8.40822 8.99986 7.8282 8.82391 7.33486 8.49427C6.84151 8.16462 6.45699 7.69609 6.22993 7.14791C6.00286 6.59973 5.94345 5.99653 6.05921 5.41459C6.17497 4.83264 6.46069 4.29809 6.88025 3.87854C7.2998 3.45898 7.83435 3.17326 8.4163 3.0575C8.99824 2.94174 9.60144 3.00115 10.1496 3.22822C10.6978 3.45528 11.1663 3.8398 11.496 4.33315C11.8256 4.82649 12.0016 5.40651 12.0016 5.99986C12.0016 6.79551 11.6855 7.55857 11.1229 8.12118C10.5603 8.68379 9.79722 8.99986 9.00157 8.99986Z"
              fill={theme.palette.text.primary}
            />
            <path
              id="Vector_2"
              d="M17.9483 5.757C17.922 5.69775 17.2868 4.2885 15.8745 2.87625C13.9928 0.994501 11.616 0 9 0C6.384 0 4.00725 0.994501 2.12549 2.87625C0.713243 4.2885 0.0749929 5.7 0.0517429 5.757C0.0176277 5.83374 0 5.91678 0 6.00075C0 6.08473 0.0176277 6.16777 0.0517429 6.2445C0.0779929 6.30375 0.713243 7.71226 2.12549 9.12451C4.00725 11.0055 6.384 12 9 12C11.616 12 13.9928 11.0055 15.8745 9.12451C17.2868 7.71226 17.922 6.30375 17.9483 6.2445C17.9824 6.16777 18 6.08473 18 6.00075C18 5.91678 17.9824 5.83374 17.9483 5.757ZM9 10.8C6.6915 10.8 4.67475 9.96076 3.00524 8.30626C2.32023 7.62503 1.73743 6.84822 1.27499 6C1.73731 5.15171 2.32012 4.37489 3.00524 3.69375C4.67475 2.03925 6.6915 1.2 9 1.2C11.3085 1.2 13.3253 2.03925 14.9948 3.69375C15.6811 4.37472 16.2652 5.15154 16.7288 6C16.188 7.00951 13.8323 10.8 9 10.8ZM9 2.4C8.28799 2.4 7.59196 2.61114 6.99995 3.00671C6.40793 3.40229 5.94651 3.96453 5.67403 4.62234C5.40156 5.28016 5.33026 6.004 5.46917 6.70233C5.60808 7.40066 5.95094 8.04212 6.45441 8.54559C6.95788 9.04906 7.59934 9.39193 8.29767 9.53083C8.99601 9.66974 9.71985 9.59845 10.3777 9.32597C11.0355 9.0535 11.5977 8.59208 11.9933 8.00006C12.3889 7.40804 12.6 6.71202 12.6 6C12.599 5.04553 12.2194 4.13043 11.5445 3.45551C10.8696 2.7806 9.95448 2.40099 9 2.4ZM9 8.40001C8.52532 8.40001 8.06131 8.25925 7.66663 7.99553C7.27195 7.73182 6.96434 7.35699 6.78269 6.91845C6.60104 6.4799 6.55351 5.99734 6.64611 5.53179C6.73872 5.06623 6.9673 4.63859 7.30294 4.30295C7.63859 3.9673 8.06623 3.73872 8.53178 3.64612C8.99734 3.55351 9.4799 3.60104 9.91844 3.78269C10.357 3.96434 10.7318 4.27196 10.9955 4.66664C11.2592 5.06131 11.4 5.52533 11.4 6C11.4 6.63652 11.1471 7.24697 10.6971 7.69706C10.247 8.14715 9.63652 8.40001 9 8.40001Z"
              fill={theme.palette.text.primary}
            />
          </g>
        </svg> 
      </SvgIcon>
    </IconButton>
  );
};

export default ViewIcon;