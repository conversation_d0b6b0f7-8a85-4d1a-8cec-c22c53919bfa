import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

const AnalyticsIcon = ({ isSelected = false }) => {
  const theme = useTheme();

  return (
    <IconButton
      size="small"
      edge="start"
      color="inherit"
      aria-label="open drawer"
      disableRipple
      sx={{
        cursor: 'default',
        '&:hover': {
          cursor: 'default',
        },
      }}
    >
      <SvgIcon fontSize="small" sx={{ fontSize: '24px' }}>
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <g clipPath="url(#clip0_230_2340)">
            <path
              
              d="M19.5 6.75V17.25H4.5V6.75H19.5Z"
              fill={'theme.palette.text.primary'}
            />
            <path
              d="M8.25 13.5V12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25C9.19891 11.25 9.38968 11.329 9.53033 11.4697C9.67098 11.6103 9.75 11.8011 9.75 12V13.5C9.75 13.6989 9.67098 13.8897 9.53033 14.0303C9.38968 14.171 9.19891 14.25 9 14.25C8.80109 14.25 8.61032 14.171 8.46967 14.0303C8.32902 13.8897 8.25 13.6989 8.25 13.5ZM12 14.25C12.1989 14.25 12.3897 14.171 12.5303 14.0303C12.671 13.8897 12.75 13.6989 12.75 13.5V11.25C12.75 11.0511 12.671 10.8603 12.5303 10.7197C12.3897 10.579 12.1989 10.5 12 10.5C11.8011 10.5 11.6103 10.579 11.4697 10.7197C11.329 10.8603 11.25 11.0511 11.25 11.25V13.5C11.25 13.6989 11.329 13.8897 11.4697 14.0303C11.6103 14.171 11.8011 14.25 12 14.25ZM15 14.25C15.1989 14.25 15.3897 14.171 15.5303 14.0303C15.671 13.8897 15.75 13.6989 15.75 13.5V10.5C15.75 10.3011 15.671 10.1103 15.5303 9.96967C15.3897 9.82902 15.1989 9.75 15 9.75C14.8011 9.75 14.6103 9.82902 14.4697 9.96967C14.329 10.1103 14.25 10.3011 14.25 10.5V13.5C14.25 13.6989 14.329 13.8897 14.4697 14.0303C14.6103 14.171 14.8011 14.25 15 14.25ZM20.25 7.5V16.5H21C21.1989 16.5 21.3897 16.579 21.5303 16.7197C21.671 16.8603 21.75 17.0511 21.75 17.25C21.75 17.4489 21.671 17.6397 21.5303 17.7803C21.3897 17.921 21.1989 18 21 18H12.75V19.6294C13.2504 19.8063 13.6722 20.1544 13.9407 20.6122C14.2093 21.07 14.3073 21.6081 14.2176 22.1312C14.1278 22.6543 13.856 23.1288 13.4502 23.471C13.0444 23.8131 12.5308 24.0007 12 24.0007C11.4692 24.0007 10.9556 23.8131 10.5498 23.471C10.144 23.1288 9.87216 22.6543 9.7824 22.1312C9.69265 21.6081 9.79072 21.07 10.0593 20.6122C10.3278 20.1544 10.7496 19.8063 11.25 19.6294V18H3C2.80109 18 2.61032 17.921 2.46967 17.7803C2.32902 17.6397 2.25 17.4489 2.25 17.25C2.25 17.0511 2.32902 16.8603 2.46967 16.7197C2.61032 16.579 2.80109 16.5 3 16.5H3.75V7.5C3.35218 7.5 2.97064 7.34196 2.68934 7.06066C2.40804 6.77936 2.25 6.39782 2.25 6V4.5C2.25 4.10218 2.40804 3.72064 2.68934 3.43934C2.97064 3.15804 3.35218 3 3.75 3H20.25C20.6478 3 21.0294 3.15804 21.3107 3.43934C21.592 3.72064 21.75 4.10218 21.75 4.5V6C21.75 6.39782 21.592 6.77936 21.3107 7.06066C21.0294 7.34196 20.6478 7.5 20.25 7.5ZM12.75 21.75C12.75 21.6017 12.706 21.4567 12.6236 21.3333C12.5412 21.21 12.4241 21.1139 12.287 21.0571C12.15 21.0003 11.9992 20.9855 11.8537 21.0144C11.7082 21.0433 11.5746 21.1148 11.4697 21.2197C11.3648 21.3246 11.2934 21.4582 11.2644 21.6037C11.2355 21.7492 11.2503 21.9 11.3071 22.037C11.3639 22.1741 11.46 22.2912 11.5833 22.3736C11.7067 22.456 11.8517 22.5 12 22.5C12.1989 22.5 12.3897 22.421 12.5303 22.2803C12.671 22.1397 12.75 21.9489 12.75 21.75ZM3.75 6H20.25V4.5H3.75V6ZM18.75 7.5H5.25V16.5H18.75V7.5Z"
              fill={theme.palette.text.primary}
            />
          </g>
          <defs>
            <clipPath id="clip0_230_2340">
              <rect width="24" height="24" fill={'theme.palette.text.primary'} />
            </clipPath>
          </defs>
        </svg>
      </SvgIcon>
    </IconButton>
  );
};

export default AnalyticsIcon;
