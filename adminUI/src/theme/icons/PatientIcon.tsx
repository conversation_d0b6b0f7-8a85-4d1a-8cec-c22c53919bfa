import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

const PatientIcon = () => {
  const theme = useTheme();
  return (
    <IconButton
      size="large"
      edge="start"
      color="inherit"
      aria-label="open drawer"
      disableRipple
      disableFocusRipple
      disableTouchRipple
      sx={{ '&:hover': { backgroundColor: 'transparent' } }}
    >
      <SvgIcon>
        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g id="UsersThree">
            <path
              id="Vector"
              d="M15.75 14.4258C15.75 15.1675 15.5301 15.8925 15.118 16.5092C14.706 17.1259 14.1203 17.6065 13.4351 17.8903C12.7498 18.1742 11.9958 18.2484 11.2684 18.1037C10.541 17.959 9.8728 17.6019 9.34835 17.0774C8.8239 16.553 8.46675 15.8848 8.32206 15.1574C8.17736 14.4299 8.25162 13.6759 8.53545 12.9907C8.81928 12.3055 9.29993 11.7198 9.91661 11.3078C10.5333 10.8957 11.2583 10.6758 12 10.6758C12.9946 10.6758 13.9484 11.0709 14.6517 11.7741C15.3549 12.4774 15.75 13.4312 15.75 14.4258ZM6 6.17578C5.40666 6.17578 4.82664 6.35173 4.33329 6.68137C3.83994 7.01102 3.45543 7.47955 3.22836 8.02773C3.0013 8.57591 2.94189 9.17911 3.05765 9.76105C3.1734 10.343 3.45912 10.8775 3.87868 11.2971C4.29824 11.7167 4.83279 12.0024 5.41473 12.1181C5.99667 12.2339 6.59987 12.1745 7.14805 11.9474C7.69623 11.7204 8.16477 11.3358 8.49441 10.8425C8.82405 10.3491 9 9.76913 9 9.17578C9 8.38013 8.68393 7.61707 8.12132 7.05446C7.55871 6.49185 6.79565 6.17578 6 6.17578ZM18 6.17578C17.4067 6.17578 16.8266 6.35173 16.3333 6.68137C15.8399 7.01102 15.4554 7.47955 15.2284 8.02773C15.0013 8.57591 14.9419 9.17911 15.0576 9.76105C15.1734 10.343 15.4591 10.8775 15.8787 11.2971C16.2982 11.7167 16.8328 12.0024 17.4147 12.1181C17.9967 12.2339 18.5999 12.1745 19.1481 11.9474C19.6962 11.7204 20.1648 11.3358 20.4944 10.8425C20.8241 10.3491 21 9.76913 21 9.17578C21 8.38013 20.6839 7.61707 20.1213 7.05446C19.5587 6.49185 18.7957 6.17578 18 6.17578Z"
              fill={theme.palette.text.primary}
              fillOpacity="0.1"
            />
            <path
              id="Vector_2"
              d="M22.95 15.025C22.8712 15.0841 22.7816 15.1271 22.6862 15.1515C22.5908 15.176 22.4915 15.1814 22.394 15.1674C22.2965 15.1535 22.2027 15.1205 22.1179 15.0703C22.0332 15.0201 21.9591 14.9538 21.9 14.875C21.448 14.2673 20.8596 13.7743 20.1822 13.4356C19.5048 13.097 18.7574 12.922 18 12.925C17.8011 12.925 17.6104 12.846 17.4697 12.7053C17.3291 12.5647 17.25 12.3739 17.25 12.175C17.25 11.9761 17.3291 11.7853 17.4697 11.6446C17.6104 11.504 17.8011 11.425 18 11.425C18.4209 11.4249 18.8332 11.3069 19.1903 11.0842C19.5474 10.8616 19.8349 10.5432 20.0201 10.1654C20.2054 9.78753 20.2809 9.3653 20.2382 8.94666C20.1955 8.52801 20.0363 8.12973 19.7786 7.79706C19.5209 7.46438 19.175 7.21065 18.7803 7.06468C18.3856 6.9187 17.9579 6.88634 17.5458 6.97127C17.1336 7.0562 16.7535 7.25501 16.4487 7.54512C16.1439 7.83523 15.9265 8.20502 15.8213 8.61247C15.7967 8.70789 15.7535 8.79751 15.6942 8.87624C15.635 8.95497 15.5608 9.02126 15.4759 9.07131C15.391 9.12137 15.2971 9.15422 15.1995 9.16798C15.102 9.18175 15.0026 9.17616 14.9072 9.15153C14.8118 9.12691 14.7222 9.08374 14.6435 9.02448C14.5647 8.96521 14.4984 8.89103 14.4484 8.80615C14.3983 8.72127 14.3655 8.62736 14.3517 8.52979C14.338 8.43221 14.3435 8.33289 14.3682 8.23747C14.5142 7.67245 14.7901 7.14931 15.1738 6.70968C15.5576 6.27004 16.0387 5.92604 16.5788 5.70505C17.119 5.48406 17.7032 5.39218 18.2851 5.43672C18.867 5.48126 19.4304 5.66099 19.9306 5.96161C20.4308 6.26223 20.8539 6.67544 21.1663 7.16836C21.4787 7.66129 21.6718 8.22032 21.7301 8.80098C21.7884 9.38164 21.7104 9.9679 21.5023 10.5131C21.2942 11.0583 20.9617 11.5474 20.5313 11.9415C21.5511 12.3831 22.4377 13.0838 23.1028 13.974C23.1619 14.053 23.2049 14.1429 23.2292 14.2385C23.2535 14.3341 23.2587 14.4336 23.2445 14.5312C23.2303 14.6288 23.197 14.7227 23.1465 14.8074C23.0959 14.8922 23.0292 14.9661 22.95 15.025ZM17.8988 20.8C17.9511 20.8853 17.9859 20.9803 18.0009 21.0794C18.016 21.1784 18.011 21.2794 17.9864 21.3765C17.9618 21.4736 17.918 21.5647 17.8575 21.6446C17.7971 21.7245 17.7213 21.7914 17.6346 21.8415C17.5479 21.8916 17.452 21.9238 17.3526 21.9363C17.2532 21.9487 17.1524 21.9411 17.056 21.9139C16.9596 21.8868 16.8696 21.8406 16.7913 21.778C16.7131 21.7155 16.6481 21.638 16.6004 21.55C16.1278 20.7502 15.455 20.0874 14.6482 19.627C13.8414 19.1666 12.9285 18.9244 11.9996 18.9244C11.0706 18.9244 10.1577 19.1666 9.35095 19.627C8.54415 20.0874 7.87131 20.7502 7.39879 21.55C7.3519 21.6396 7.28737 21.7188 7.20908 21.7829C7.13078 21.8469 7.04034 21.8944 6.94319 21.9226C6.84605 21.9508 6.7442 21.9591 6.64379 21.9469C6.54338 21.9347 6.44647 21.9023 6.35889 21.8517C6.27132 21.801 6.19489 21.7332 6.1342 21.6523C6.07352 21.5714 6.02983 21.479 6.00576 21.3808C5.9817 21.2825 5.97775 21.1804 5.99417 21.0806C6.01059 20.9808 6.04702 20.8853 6.10129 20.8C6.82841 19.5506 7.9371 18.5675 9.26441 17.995C8.51753 17.4231 7.96863 16.6316 7.69485 15.7317C7.42108 14.8318 7.4362 13.8687 7.73808 12.9778C8.03997 12.0869 8.61346 11.313 9.37792 10.7649C10.1424 10.2167 11.0594 9.92197 12 9.92197C12.9407 9.92197 13.8577 10.2167 14.6222 10.7649C15.3866 11.313 15.9601 12.0869 16.262 12.9778C16.5639 13.8687 16.579 14.8318 16.3052 15.7317C16.0315 16.6316 15.4825 17.4231 14.7357 17.995C16.063 18.5675 17.1717 19.5506 17.8988 20.8ZM12 17.425C12.5934 17.425 13.1734 17.249 13.6667 16.9194C14.1601 16.5897 14.5446 16.1212 14.7717 15.573C14.9987 15.0248 15.0582 14.4216 14.9424 13.8397C14.8266 13.2578 14.5409 12.7232 14.1214 12.3037C13.7018 11.8841 13.1673 11.5984 12.5853 11.4826C12.0034 11.3669 11.4002 11.4263 10.852 11.6533C10.3038 11.8804 9.83528 12.2649 9.50563 12.7583C9.17599 13.2516 9.00004 13.8316 9.00004 14.425C9.00004 15.2206 9.31611 15.9837 9.87872 16.5463C10.4413 17.1089 11.2044 17.425 12 17.425ZM6.75004 12.175C6.75004 11.9761 6.67102 11.7853 6.53037 11.6446C6.38972 11.504 6.19895 11.425 6.00004 11.425C5.57922 11.4249 5.16685 11.3069 4.80976 11.0842C4.45267 10.8616 4.16518 10.5432 3.97994 10.1654C3.79471 9.78753 3.71915 9.3653 3.76185 8.94666C3.80455 8.52801 3.9638 8.12973 4.22152 7.79706C4.47923 7.46438 4.82507 7.21065 5.21976 7.06468C5.61445 6.9187 6.04216 6.88634 6.45432 6.97127C6.86648 7.0562 7.24656 7.25501 7.55139 7.54512C7.85622 7.83523 8.07359 8.20502 8.17879 8.61247C8.22852 8.80517 8.35276 8.97022 8.52418 9.07131C8.6956 9.17241 8.90016 9.20126 9.09285 9.15153C9.28555 9.10181 9.4506 8.97757 9.55169 8.80615C9.65279 8.63473 9.68164 8.43017 9.63192 8.23747C9.48589 7.67245 9.21002 7.14931 8.82624 6.70968C8.44246 6.27004 7.96137 5.92604 7.42125 5.70505C6.88112 5.48406 6.29688 5.39218 5.715 5.43672C5.13312 5.48126 4.56967 5.66099 4.06947 5.96161C3.56928 6.26223 3.14614 6.67544 2.83375 7.16836C2.52135 7.66129 2.32831 8.22032 2.26998 8.80098C2.21165 9.38164 2.28965 9.9679 2.49777 10.5131C2.70589 11.0583 3.03838 11.5474 3.46879 11.9415C2.44999 12.3835 1.56446 13.0842 0.90004 13.974C0.780569 14.1332 0.729204 14.3332 0.757247 14.5302C0.785289 14.7272 0.890442 14.905 1.04957 15.0245C1.2087 15.144 1.40877 15.1953 1.60577 15.1673C1.80278 15.1393 1.98057 15.0341 2.10004 14.875C2.55209 14.2673 3.14049 13.7743 3.81788 13.4356C4.49526 13.097 5.24271 12.922 6.00004 12.925C6.19895 12.925 6.38972 12.846 6.53037 12.7053C6.67102 12.5647 6.75004 12.3739 6.75004 12.175Z"
              fill={theme.palette.text.primary}
            />
          </g>
        </svg>
      </SvgIcon>
    </IconButton>
  );
};

export default PatientIcon;
