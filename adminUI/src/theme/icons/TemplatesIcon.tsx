import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

const TemplatesIcon = () => {
  const theme = useTheme();
  return (
    <IconButton
      size="large"
      edge="start"
      color="inherit"
      aria-label="open drawer"
      disableRipple
      disableFocusRipple
      disableTouchRipple
      sx={{ '&:hover': { backgroundColor: 'transparent' } }}
    >
      <SvgIcon>
        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g id="FolderNotch">
            <path
              id="Vector"
              d="M12 8.42578L9.19969 10.5258C9.06987 10.6231 8.91197 10.6758 8.74969 10.6758H3V6.92578C3 6.72687 3.07902 6.5361 3.21967 6.39545C3.36032 6.2548 3.55109 6.17578 3.75 6.17578H8.74969C8.91197 6.17578 9.06987 6.22841 9.19969 6.32578L12 8.42578Z"
              fill={theme.palette.text.primary}
              fillOpacity="0.1"
            />
            <path
              id="Vector_2"
              d="M20.25 7.67578H12.2503L9.64969 5.72578C9.38967 5.53177 9.07411 5.42659 8.74969 5.42578H3.75C3.35218 5.42578 2.97064 5.58382 2.68934 5.86512C2.40804 6.14643 2.25 6.52796 2.25 6.92578V19.6758C2.25 20.0736 2.40804 20.4551 2.68934 20.7364C2.97064 21.0177 3.35218 21.1758 3.75 21.1758H20.25C20.6478 21.1758 21.0294 21.0177 21.3107 20.7364C21.592 20.4551 21.75 20.0736 21.75 19.6758V9.17578C21.75 8.77796 21.592 8.39643 21.3107 8.11512C21.0294 7.83382 20.6478 7.67578 20.25 7.67578ZM3.75 6.92578H8.74969L10.7503 8.42578L8.74969 9.92578H3.75V6.92578ZM20.25 19.6758H3.75V11.4258H8.74969C9.07411 11.425 9.38967 11.3198 9.64969 11.1258L12.2503 9.17578H20.25V19.6758Z"
              fill={theme.palette.text.primary}
            />
          </g>
        </svg>
      </SvgIcon>
    </IconButton>
  );
};

export default TemplatesIcon;
