import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon } from '@mui/material';

const TaskHighIcon = () => {
  return (
    <IconButton
      size="small"
      edge="start"
      color="inherit"
      aria-label="open drawer"
      sx={{
        // Prevent cursor from changing to pointer
        pointerEvents: 'none',
        cursor: 'default', color: 'transparent',
        '&:hover': {
          cursor: 'default',
        },
      }}
    >
      <SvgIcon>
        <svg width="18" height="16" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g id="DotsThreeOutlineVertical">
            <path
              id="Vector 4"
              d="M1.96094 10.2158L9.56494 5.25391L16.6887 10.2158"
              stroke="#FF0000"
              strokeWidth="2.5"
              strokeLinecap="round"
            />
          </g>
        </svg>
      </SvgIcon>
    </IconButton>
  );
};

export default TaskHighIcon;
