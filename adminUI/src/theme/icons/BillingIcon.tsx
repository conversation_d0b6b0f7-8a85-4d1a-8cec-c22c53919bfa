import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

const BillingIcon = ({ isSelected = false }) => {
  const theme = useTheme();

  return (
    <IconButton
      size="small"
      edge="start"
      color="inherit"
      aria-label="open drawer"
      disableRipple
      sx={{
        cursor: 'default',
        '&:hover': {
          cursor: 'default',
        },
      }}
    >
      <SvgIcon fontSize="small" sx={{ fontSize: '24px' }}>
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
<path opacity="0.2" d="M21 9.75V18C21 18.1989 20.921 18.3897 20.7803 18.5303C20.6397 18.671 20.4489 18.75 20.25 18.75H15.75V9.75H21Z" fill="black"/>
<path d="M2.625 12C2.42609 12 2.23532 11.921 2.09467 11.7803C1.95402 11.6397 1.875 11.4489 1.875 11.25C1.875 11.0511 1.95402 10.8603 2.09467 10.7197C2.23532 10.579 2.42609 10.5 2.625 10.5H5.25C5.44891 10.5 5.63968 10.421 5.78033 10.2803C5.92098 10.1397 6 9.94891 6 9.75C6 9.55109 5.92098 9.36032 5.78033 9.21967C5.63968 9.07902 5.44891 9 5.25 9H3.75C3.15326 9 2.58097 8.76295 2.15901 8.34099C1.73705 7.91903 1.5 7.34674 1.5 6.75C1.5 6.15326 1.73705 5.58097 2.15901 5.15901C2.58097 4.73705 3.15326 4.5 3.75 4.5C3.75 4.30109 3.82902 4.11032 3.96967 3.96967C4.11032 3.82902 4.30109 3.75 4.5 3.75C4.69891 3.75 4.88968 3.82902 5.03033 3.96967C5.17098 4.11032 5.25 4.30109 5.25 4.5H6C6.19891 4.5 6.38968 4.57902 6.53033 4.71967C6.67098 4.86032 6.75 5.05109 6.75 5.25C6.75 5.44891 6.67098 5.63968 6.53033 5.78033C6.38968 5.92098 6.19891 6 6 6H3.75C3.55109 6 3.36032 6.07902 3.21967 6.21967C3.07902 6.36032 3 6.55109 3 6.75C3 6.94891 3.07902 7.13968 3.21967 7.28033C3.36032 7.42098 3.55109 7.5 3.75 7.5H5.25C5.84674 7.5 6.41903 7.73705 6.84099 8.15901C7.26295 8.58097 7.5 9.15326 7.5 9.75C7.5 10.3467 7.26295 10.919 6.84099 11.341C6.41903 11.7629 5.84674 12 5.25 12C5.25 12.1989 5.17098 12.3897 5.03033 12.5303C4.88968 12.671 4.69891 12.75 4.5 12.75C4.30109 12.75 4.11032 12.671 3.96967 12.5303C3.82902 12.3897 3.75 12.1989 3.75 12H2.625ZM21.75 5.25V18C21.75 18.3978 21.592 18.7794 21.3107 19.0607C21.0294 19.342 20.6478 19.5 20.25 19.5H3.75C3.35218 19.5 2.97064 19.342 2.68934 19.0607C2.40804 18.7794 2.25 18.3978 2.25 18V14.25C2.25 14.0511 2.32902 13.8603 2.46967 13.7197C2.61032 13.579 2.80109 13.5 3 13.5C3.19891 13.5 3.38968 13.579 3.53033 13.7197C3.67098 13.8603 3.75 14.0511 3.75 14.25V18H15V15H7.5C7.30109 15 7.11032 14.921 6.96967 14.7803C6.82902 14.6397 6.75 14.4489 6.75 14.25C6.75 14.0511 6.82902 13.8603 6.96967 13.7197C7.11032 13.579 7.30109 13.5 7.5 13.5H15V10.5H9.75C9.55109 10.5 9.36032 10.421 9.21967 10.2803C9.07902 10.1397 9 9.94891 9 9.75C9 9.55109 9.07902 9.36032 9.21967 9.21967C9.36032 9.07902 9.55109 9 9.75 9H20.25V6H9C8.80109 6 8.61032 5.92098 8.46967 5.78033C8.32902 5.63968 8.25 5.44891 8.25 5.25C8.25 5.05109 8.32902 4.86032 8.46967 4.71967C8.61032 4.57902 8.80109 4.5 9 4.5H21C21.1989 4.5 21.3897 4.57902 21.5303 4.71967C21.671 4.86032 21.75 5.05109 21.75 5.25ZM16.5 13.5H20.25V10.5H16.5V13.5ZM20.25 18V15H16.5V18H20.25Z"  fill={theme.palette.text.primary}/>
</svg>
      </SvgIcon>
    </IconButton>
  );
};

export default BillingIcon;
