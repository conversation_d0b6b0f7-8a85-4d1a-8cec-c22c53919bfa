import React from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

const PermissionIcon = () => {
  const theme = useTheme();
  return (
    <IconButton
      size="large"
      edge="start"
      color="inherit"
      aria-label="open drawer"
      disableRipple
      disableFocusRipple
      disableTouchRipple
      sx={{ '&:hover': { backgroundColor: 'transparent' } }}
    >
      <SvgIcon>
        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g id="puzzle-piece-duotone 2">
            <path
              id="Vector"
              opacity="0.2"
              d="M20.25 6.17578V11.6855C20.25 19.5773 13.5647 22.192 12.2306 22.6355C12.0812 22.687 11.9188 22.687 11.7694 22.6355C10.4353 22.1939 3.75 19.582 3.75 11.6873V6.17578C3.75 5.97687 3.82902 5.7861 3.96967 5.64545C4.11032 5.5048 4.30109 5.42578 4.5 5.42578H19.5C19.6989 5.42578 19.8897 5.5048 20.0303 5.64545C20.171 5.7861 20.25 5.97687 20.25 6.17578Z"
              fill={theme.palette.text.primary}
            />
            <path
              id="Vector_2"
              d="M5.055 4.17044C4.94222 4.04675 4.7913 3.96435 4.62627 3.93635C4.46124 3.90835 4.29158 3.93636 4.14431 4.01593C3.99704 4.09549 3.88062 4.22203 3.81358 4.37541C3.74655 4.52879 3.73274 4.70019 3.77437 4.86231C3.53982 4.99196 3.34428 5.18208 3.2081 5.4129C3.07191 5.64372 3.00006 5.90681 3 6.17481V11.6845C3 20.0864 10.1081 22.8726 11.5312 23.3451C11.8351 23.4491 12.1649 23.4491 12.4688 23.3451C14.3867 22.6795 16.1342 21.5988 17.5866 20.1801L18.9469 21.6801C19.0132 21.753 19.0932 21.8121 19.1823 21.8541C19.2715 21.8961 19.368 21.92 19.4664 21.9247C19.5648 21.9294 19.6632 21.9146 19.7559 21.8812C19.8486 21.8479 19.9338 21.7966 20.0067 21.7303C20.0796 21.664 20.1387 21.584 20.1807 21.4948C20.2226 21.4057 20.2466 21.3092 20.2513 21.2107C20.256 21.1123 20.2412 21.014 20.2078 20.9213C20.1745 20.8286 20.1232 20.7433 20.0569 20.6704L5.055 4.17044ZM12 21.9248C10.7316 21.5029 4.5 19.0486 4.5 11.6892V6.17481H4.84969L16.575 19.072C15.2818 20.3522 13.7187 21.327 12 21.9248ZM21 6.17481V11.6845C21 13.5286 20.6503 15.2404 19.9594 16.7742C19.8777 16.9556 19.7273 17.0971 19.5413 17.1676C19.3553 17.2381 19.1489 17.2318 18.9675 17.1501C18.7861 17.0684 18.6446 16.9181 18.5741 16.732C18.5036 16.546 18.5099 16.3396 18.5916 16.1583C19.1944 14.8195 19.5 13.3148 19.5 11.6845V6.17481H9.23625C9.03734 6.17481 8.84657 6.0958 8.70592 5.95514C8.56527 5.81449 8.48625 5.62373 8.48625 5.42481C8.48625 5.2259 8.56527 5.03514 8.70592 4.89448C8.84657 4.75383 9.03734 4.67481 9.23625 4.67481H19.5C19.8978 4.67481 20.2794 4.83285 20.5607 5.11415C20.842 5.39546 21 5.77699 21 6.17481Z"
              fill={theme.palette.text.primary}
            />
          </g>
        </svg>
      </SvgIcon>
    </IconButton>
  );
};

export default PermissionIcon;
