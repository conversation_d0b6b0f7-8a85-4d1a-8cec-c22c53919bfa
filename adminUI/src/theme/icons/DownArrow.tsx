import React, { FC } from 'react';
import IconButton from '@mui/material/IconButton';
import { SvgIcon, useTheme } from '@mui/material';

interface DropDownArrowProps {
  onClick?: any;
}
 
const DownArrow: FC<DropDownArrowProps> = ({ onClick }) => {
    const theme = useTheme();
    return (

      <IconButton size="large" edge="start" color="inherit" onClick={onClick} aria-label="open drawer">
        <SvgIcon>
          <svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
            
            <path
              d="M10.6016 14.8789L5.35156 9.62891C4.99609 9.30078 4.99609 8.72656 5.35156 8.39844C5.67969 8.04297 6.25391 8.04297 6.58203 8.39844L11.2305 13.0195L15.8516 8.39844C16.1797 8.04297 16.7539 8.04297 17.082 8.39844C17.4375 8.72656 17.4375 9.30078 17.082 9.62891L11.832 14.8789C11.5039 15.2344 10.9297 15.2344 10.6016 14.8789Z"
              fill={theme.palette.text.primary}
            />
          </svg>
        </SvgIcon>
      </IconButton>
  );
};

export default DownArrow;
