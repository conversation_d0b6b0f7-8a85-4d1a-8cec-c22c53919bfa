import { type PaletteMode } from '@mui/material';
import { PaletteColor, ThemeOptions } from "@mui/material/styles";
import { TypographyOptions } from "@mui/material/styles/createTypography";
import type { CustomPalette } from "../types";
import lightTheme from './lightTheme';
import darkTheme from './darkTheme'; 

declare module '@mui/material/styles' {
  interface Theme {
    customProperties: {
      tabBorderBottom:string;
      genericTableHeaderBorder:string;
      customAppBar:string,
      textFieldBackground: string;
      createDocumentBorder: {
        border2px: string;
        border1pxHover: string;
        borderBottom: string;
      };
      border: string;
      templateBorder: string;
      color: string;
      labelColor: string;
      lineConnector: string;
      permissionHeadingColor: string;
      whiteF9F9: string;
      paginationDiv: string;
      buttonPrimaryColor: string;
      buttonSecondary: string;
      templateListBackground: string;
      templateHeaderFooter: string;
      ButtonTextColor: string;
      GradientButton: {
        color: string;
        background: string;
        '&:hover': {
          background?: string;
          border?: string;
          color?: string;
          boxShadow?: string;
        };
        '& .Mui-disabled': {
          background: string;
        };
      };
      NavbarBorderedButton: {
        border: string;
        backgroundColor: string;
      };
    };

    custom: {
      palette: {
        orange: string;
      };
    };
  }

  interface ThemeOptions {
    customProperties?: {
      tabBorderBottom:string;
      genericTableHeaderBorder:string;
      customAppBar:string,
      textFieldBackground: string;
      createDocumentBorder: {
        border2px: string;
        border1pxHover: string;
        borderBottom: string;
      };
      border: string;
      templateBorder: string;
      color: string;
      labelColor: string;
      lineConnector: string;
      permissionHeadingColor: string;
      whiteF9F9: string;
      paginationDiv: string;
      buttonPrimaryColor: string;
      buttonSecondary: string;
      templateListBackground: string;
      templateHeaderFooter: string;
      ButtonTextColor: string;
      GradientButton: {
        color: string;
        background: string;
        '&:hover': {
          background?: string;
          border?: string;
          color?: string;
          boxShadow?: string;
        };
        '& .Mui-disabled': {
          background: string;
        };
      };
      NavbarBorderedButton: {
        border: string;
        backgroundColor: string;
      };
    };
  }
}

// Define the custom palette
const custom: CustomPalette = {
  palette: {
    orange: '#e25f22',
  },
};

// Define light mode palette colors
const light: PaletteColor = {
  main: '#red',  
  light: '#ffffff',   
  dark: '#bfbfbf',    
  contrastText: '#red',   
};

// Define dark mode palette colors
const dark: PaletteColor = {
  main: '#424242',   
  light: '#6d6d6d',   
  dark: '#1b1b1b',   
  contrastText: '#ffffff',   
};

// Typography options for both themes
const typography: TypographyOptions = {
  fontFamily: [
    'Roboto',
    'sans-serif',
  ].join(','),
};

export const createPaletteMode = (mode: PaletteMode): ThemeOptions => {
  return mode === 'light' ? lightTheme : darkTheme;
};