import { createTheme, ThemeOptions } from '@mui/material/styles';
import typography from './typography';

// Create a custom theme
const darkTheme = createTheme({
  zIndex: {
    drawer: 1100,
    modal: 1300,
  },
  palette: {
    mode: 'dark',
    common: {
      black: '#000',
      white: '#fff',
    },
    background: {
      default: '#0D0D0D',
      paper: '#424242',
    },
    action: {
      selected: 'red',
    },
    primary: {
      light: '#333',
      main: '#131313',
      dark: '#FFF',
      contrastText: '#fff',

    },
    secondary: {
      light: '#ff4081',
      main: '#90caf9',
      dark: '#FFF',
      contrastText: '#fff',
    },
    text: {
      primary: '#fff',
      secondary: '#fff',
      disabled: 'rgba(255, 255, 255, 0.5)',

    },
  },
  customProperties: {border:'1px solid #2A2A2A',
    tabBorderBottom: '2px solid #fff',
    genericTableHeaderBorder: '#fff',
    customAppBar: '  #131313',
    NavbarBorderedButton: {
      border: '1px solid rgba(28, 28, 28, 0.10)',
      backgroundColor: '#ff4081 !important',
    },
    
    templateBorder: '3px solid #FFF',
    createDocumentBorder: {
      border2px: '2px solid #f3f6f6',
      border1pxHover: '1px solid #f3f6f6',
      borderBottom: '2px solid #131313',
    },
    textFieldBackground: '#131313',
    color: '#FFF',
    labelColor: '#FFF',
    lineConnector: '#262626',
    permissionHeadingColor: '#E5ECF6',
    whiteF9F9: '#0D0D0D',
    paginationDiv: '#0D0D0D',
    buttonPrimaryColor: '#131313',
    buttonSecondary: '#FFF',
    templateListBackground: '#131313',
    templateHeaderFooter: '#1A1A1A',
    ButtonTextColor: '#fff',
    GradientButton: {
      color: '#0D0D0D',
      background: '#fff',
      '&:hover': {
        background: '#171819',
        color: '#fff',
        border: '1px solid #fff',
        boxShadow: '0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12)',
      },
      '& .Mui-disabled': {
        background: '#fff',
      },
    },

  }
  ,
  components: {
    MuiCheckbox: {
      styleOverrides: {
        root: {
          color: "gray",
          "&.Mui-checked": {
            color: "#2D2869",
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          color: '#E3E3E3',
          borderBottom: '1px solid rgba(227, 227, 227, 0.05)',
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          backgroundColor: '#0D0D0D',
          '&:nth-of-type(odd)': {
            backgroundColor: '#0D0D0D',
          },
        },
      },
    },

    MuiCssBaseline: {
      styleOverrides: {
        '@global': {
          'input:-webkit-autofill': {
            '-webkit-box-shadow': 'transparent !important',
            '-webkit-text-fill-color': 'white !important', // Set text color to white
            'transition': 'background-color 5000s ease-in-out !important',
          },
          'input:-webkit-autofill:hover': {
            '-webkit-box-shadow': 'transparent !important',
            '-webkit-text-fill-color': 'white !important',
          },
          'input:-webkit-autofill:focus': {
            '-webkit-box-shadow': 'transparent !important',
            '-webkit-text-fill-color': 'white !important',
          },
          'input:-webkit-autofill:active': {
            '-webkit-box-shadow': 'transparent !important',
            '-webkit-text-fill-color': 'white !important',
          },
        },
      },

    }, MuiOutlinedInput: {
      styleOverrides: {
        input: {
          '&:-webkit-autofill': {
            caretColor: '#fff',
            borderRadius: 'inherit',
            boxShadow: '0 0 0 100px #FFF inset',
            WebkitTextFillColor: '#fff',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          color: '#0D0D0D',
          textTransform: 'capitalize',
          background: '#fff',
          borderRadius:'100px',
          '&:hover': {
            background: '#171819',
            color: '#fff',
            border: '1px solid #fff',
            boxShadow: '0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12)',
          },
          '&.Mui-disabled': {
            color: 'grey',
            backgroundColor: 'none',
          },
        },

      },
    },
    MuiSwitch: {
      styleOverrides: {
        root: {
          width: 50,
          height: 25,
          padding: 0,
          
        },
        switchBase: {
          padding: 1,
          '&.Mui-checked': {
            transform: 'translateX(23px)',
            color: '#fff',
            '& + .MuiSwitch-track': {
              backgroundColor: '#424242',
              opacity: 1,
            },
          },
          '&.Mui-disabled': {
            '& + .MuiSwitch-track': {
              backgroundColor: '#CCCCCC',
              opacity: 1,
            },
            '& .MuiSwitch-thumb': {
              backgroundColor: '#E8E8E8',
            },
            '&.Mui-checked': {
            transform: 'translateX(23px)',
            color: '#fff',
            '& + .MuiSwitch-track': {
              backgroundColor: '#CCCCCC',
              opacity: 1,
            },
          },
          }
        },
        thumb: {
          width: 18,
          height: 18,
          backgroundColor: '#FFF',
        },
        track: {
          borderRadius: 26 / 2,
          backgroundColor: '#CCCCCC',
          opacity: 1,
          transition: 'background-color 300ms cubic-bezier(0.4, 0, 0.2, 1)',
        },
      },
    },
    MuiToggleButton: {
      styleOverrides: {
        root: {
          '&.Mui-selected': {
            backgroundColor: 'red',
            color: '#212121',
            '&:hover': {
              backgroundColor: '#d5d5d5',
            },
          },
          borderColor: 'rgba(0, 0, 0, 0.23)',
          color: 'rgba(0, 0, 0, 0.6)',
        },
      },
    },
    MuiRadio: {
      styleOverrides: {
        root: {
          color: '#E6E6E6',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
          },
        },
        colorPrimary: {
          color: '#E6E6E6',
          '&.Mui-checked': {
            color: '#CCCCCC',
          },
          '&.Mui-disabled': {
            color: '#5E5E5E', // Disabled color for light mode
          },
        },
          
      },
    },
  
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
         
        },
      },
    },
    
  },
  typography,
});

export default darkTheme;
