import { useEffect } from 'react';
import { useSnackbar } from 'notistack';
import { useDispatch, useSelector } from 'react-redux';
import {RootState} from "../app/store";
import {appActions} from "../reducers/appSlice";

const SnackbarListener = () => {
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const notifications = useSelector((state: RootState) => state.app.notifications);

  useEffect(() => {
    Object.values(notifications).forEach(({ key, message, variant }) => {
      enqueueSnackbar(message, { variant });
      dispatch(appActions.removeSnackbar(key)); // Remove after displaying
    });
  }, [notifications, enqueueSnackbar, dispatch]);

  return null;
};

export default SnackbarListener;
