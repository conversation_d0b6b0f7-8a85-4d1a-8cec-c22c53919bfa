import { 
  Login as LoginIcon,
  Logout as LogoutIcon
} from '@mui/icons-material';

import type { LinkItem } from '../types';
import AnalyticsIcon from '../theme/icons/AnalyticsIcon';
import UserManagementIcon from '../theme/icons/UserManagementIcon';
import SubscriptionIcon from '../theme/icons/SubcriptionsIcon';
import CommunicationIcon from '../theme/icons/CommunicationIcon';
import FeedbackIcon from '../theme/icons/FeedbackIcon';
import BillingIcon from '../theme/icons/BillingIcon';
import SettingIcon from '../theme/icons/SettingIcon';
import AnalyticsMobile from '../theme/icons/AnalyticsMobile';
import ReportIcon from '../theme/icons/ReportIcon';
import DiscountIcon from '../theme/icons/DiscountIcon';

/**
 * App Navigation Link Items
 */
export const linkItems = [
  {
    id: 0,
    name: 'Analytics',
    to: '/',
    icon: <AnalyticsIcon />,
    nested: [
      { name: 'Analytics', icon: <AnalyticsMobile />, to: '/' },
      { name: 'Reports', icon: <ReportIcon />, to: '/analytics/reports' },
    ],
  },
  { id: 1, name: 'User Management', to: '/userManagement', icon: <UserManagementIcon /> },
  { id: 2, name: 'Subscriptions', to: '/subscriptions', icon: <SubscriptionIcon /> },
  { id: 3, name: 'Communication', to: '/communication', icon: <CommunicationIcon /> },
  { id: 4, name: 'Feedback', to: '/feedback', icon: <FeedbackIcon /> },
  {
    id: 5,
    name: 'Billings',
    to: '/billings',
    icon: <BillingIcon />,
    nested: [
      { name: 'Billing', icon: <BillingIcon />,to: '/billings/billing' },
      { name: 'Discounts', icon: <DiscountIcon />, to: '/billings/discounts' },
    ],
  },
  { id: 6, name: 'Settings', to: '/settings', icon: <SettingIcon /> },
];


/**
 * App Authentication Link Items
 */
export const authItems: LinkItem[] = [
  { id: 3, name: 'login', description: 'Login', to: '/login', icon: <LoginIcon /> },
  { id: 4, name: 'logout', description: 'Logout', to: '/login', icon: <LogoutIcon /> },
];