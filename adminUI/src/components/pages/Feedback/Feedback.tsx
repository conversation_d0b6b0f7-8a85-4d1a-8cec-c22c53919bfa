import {<PERSON><PERSON><PERSON>, Box, Button, CircularP<PERSON>ress, <PERSON><PERSON>, Too<PERSON>bar, Typography} from "@mui/material";
import CustomAppBar from "../../design/CustomAppBar";
import { Search, SearchIconWrapper, StyledInputBase } from "../../design/StyledComponents";
import SearchIcon from "@mui/icons-material/Search";
import React, { useState, useEffect } from "react";
import { Rating } from "@mui/material";
import GenericTable from "../../design/GenericTable";
import { Pagination } from "@mui/material";
import UserFeedbackModal from "./UserFeedbackModal";
import FeedbackServices from "../../../service/apiCalls/FeedbackServices";
import NoRecordsMessage from "../../design/NoRecordsMessage";
import {appActions} from "../../../reducers/appSlice";
import {useDispatch} from "react-redux";

const Feedback = () => {
  const dispatch = useDispatch();

  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchStr, setSearchStr] = useState("");
  const [selectedRow, setSelectedRow] = useState<{
    id: number;
    emailCampaign: string;
    userName: string;
    rating: JSX.Element;
    value: number;
    date: string;
    status: string;
    feedback: string;
  } | null>(null);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchData();
  }, [searchStr,page]);

  const transformApiResponse = (apiResponse:any) => {
    return apiResponse.list.map((item:any) => ({
      emailCampaign: item.feedback_text
        ? item.feedback_text.length > 20
          ? item.feedback_text.substring(0,20) + "........"
          : item.feedback_text
        : "N/A",
      userName: item.name || "N/A",
      rating: <Rating name="rating" value={item.rating} readOnly />,
      value: item.rating,
      date: new Date(item.createdAt).toLocaleDateString("en-US"),
      status: item.status,
      feedback: item.feedback_text || "N/A",
      id:item.id,
    }));
  };

  const fetchData = async () => {
    if (searchStr.trim() && searchStr.length < 3) return;
    setLoading(true);
    setLoading(true);
    try {
      const response = await FeedbackServices.getFeedbackList(page,searchStr);
      console.log(response)
      setData(transformApiResponse(response));
      setTotalPages(Math.ceil(response.totalRecords / response.recordsPerPage));
    } catch (error) {
      dispatch(
        appActions.enqueueSnackbar({
          key: new Date().getTime() + Math.random(),
          message: "Error Occurred Fetching Feedback",
          variant: "error",
        }))
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    { label: "Email Campaign", field: "emailCampaign", sorting: true },
    { label: "User Name", field: "userName", sorting: true },
    { label: "Rating", field: "rating" },
    { label: "Date", field: "date", sorting: true },
    { label: "Status", field: "status" },
  ];

  return (
    <Box>
      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={loading}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
      <Typography sx={{ fontWeight: "600", fontSize: "14px", mb: "8px" }}>Users Feedback</Typography>
      <CustomAppBar
        rightContent={
          <Toolbar sx={{ pl: { sm: 2 }, pr: { xs: 1, sm: 1 }, borderRadius: "8px" }}>
            <Search>
              <SearchIconWrapper>
                <SearchIcon />
              </SearchIconWrapper>
              <StyledInputBase
                placeholder="Search…"
                inputProps={{ "aria-label": "search" }}
                value={searchStr}
                onChange={(e) => setSearchStr(e.target.value)}
              />
            </Search>
          </Toolbar>
        }
        leftContent={<div></div>}
      />
      <Stack>
        {data.length === 0 && !loading && (
          <NoRecordsMessage message="No Feedback Found" />
        )}
        {data.length >=1 && (
          <>
            <GenericTable
              columns={columns}
              data={data}
              multipleValues={false}
              headingBackground={false}
              //@ts-ignore
              onRowClick={(row) => setSelectedRow(row)}
              sortBy="date"
              sortOrder="asc"
            />
            <Pagination
              count={totalPages}
              page={page}
              onChange={(e, value) => setPage(value)}
              sx={{ marginTop: "16px", display: "flex", justifyContent: "center" }}
            />
          </>
      )}
      </Stack>
      {selectedRow && (
        <UserFeedbackModal
          open={true}
          id={selectedRow.id}
          onClose={() => {
            setSelectedRow(null);
            fetchData();
          }}
        />
      )}
    </Box>
  );
};

export default Feedback;
