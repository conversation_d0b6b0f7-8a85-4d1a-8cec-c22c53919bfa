  //@ts-nocheck
  import * as React from 'react';
  import { useEffect, useState } from 'react';
  import Button from '@mui/material/Button';
  import Dialog from '@mui/material/Dialog';
  import DialogContent from '@mui/material/DialogContent';
  import DialogTitle from '@mui/material/DialogTitle';
  import {Backdrop, Box, CircularProgress, Input, MenuItem, Select, Stack, TextField} from '@mui/material';
  import Typography from '@mui/material/Typography';
  import { useDispatch, useSelector } from 'react-redux';
  import { useFormik, FieldArray, FormikProvider } from 'formik';
  import Grid from '@mui/material/Grid';
  import IconButton from '@mui/material/IconButton';
  import { Delete } from '@mui/icons-material';
  import PersonAddAltIcon from '@mui/icons-material/PersonAddAlt';
  import IRootState from "../../../models/IRootState";
  import settingServices from "../../../service/apiCalls/SettingServices";
  import {appActions} from "../../../reducers/appSlice";
  import * as Yup from 'yup';
  import Loader from "../../design/Loader";

  interface InviteUserDialogProps {
    open: boolean;
    handleClose: (event?: {}, reason?: 'backdropClick' | 'escapeKeyDown') => void;
    setIsUserInvited: (isUserInvited: boolean) => void;
  }

  const inviteUserSchema = Yup.object().shape({
    users: Yup.array().of(
      Yup.object().shape({
        firstName: Yup.string().trim().min(2, "Too short").max(50, "Too long").required("First name is required"),
        lastName: Yup.string().trim().min(2, "Too short").max(50, "Too long").required("Last name is required"),
        email: Yup.string().trim().email("Invalid email").required("Email is required"),
        role: Yup.string().required("Role is required"),
      })
    ),
  });

  export default function InviteUser({ open, handleClose, setIsUserInvited }: InviteUserDialogProps) {
    const dispatch = useDispatch();
    const auth = useSelector((state: IRootState) => state.auth);
    const [roleList, setRoleList] = React.useState([]);
    const [roleDefaultPermissions, setRoleDefaultPermissions] = useState();
    const [loading , setLoading] = useState(false)
    useEffect(() => {
      const fetchRoles = async () => {
        setLoading(true);
        try {
         const roles = await settingServices.getSystemRoles();
         setRoleList(roles);
        } catch (error) {
          dispatch(
            appActions.enqueueSnackbar({
              key: new Date().getTime() + Math.random(),
              //@ts-ignore
              message: error?.response?.data?.msg || "Something went wrong. Please try again.",
              variant: "error",
            }))
        } finally{
          setLoading(false)
        }
      };
      if (open) {
        fetchRoles();
      } else {
        formik.resetForm();
      }
    }, [open, auth]);

    const handleRoleChange = async (selectedRole:any, index:any) => {
      const { id, name } = selectedRole;
      formik.setFieldValue(`users[${index}].role`, id);
      setLoading(true)
      try {
        setLoading(true)
        const response = await settingServices.getDefaultPermissionsForRole(id);
        setRoleDefaultPermissions(response)
      } catch (error) {
        dispatch(
          appActions.enqueueSnackbar({
            key: new Date().getTime() + Math.random(),
            //@ts-ignore
            message: error?.response?.data?.msg || "Error fetching role details",
            variant: "error",
          })
        );
      } finally{
        setLoading(false)
      }
    };

    const formik = useFormik({
      initialValues: {
        users: [{ firstName: '', lastName: '', email: '', role: '' }], // Initialize as an array with one empty user object
      },
      validateOnBlur: true,
      validateOnChange: true,
      validationSchema: inviteUserSchema,
      onSubmit: (values) => {
        const payload = values.users
        .map(user => ({
          first_name: user.firstName,
          last_name: user.lastName,
          email: user.email,
          role_id: user.role,
          permissions: roleDefaultPermissions
          .filter(permission => permission.isSelected === 1)
          .map(permission => permission.id),
        }))[0];

        try{
          setLoading(true)
          const response = settingServices.addAdmin(payload)
          dispatch(
            appActions.enqueueSnackbar({
              key: new Date().getTime() + Math.random(),
              //@ts-ignore
              message: "Invitation Send Successfully",
              variant: "success",
            }))
          setIsUserInvited(true)
        } catch(error){
          dispatch(
            appActions.enqueueSnackbar({
              key: new Date().getTime() + Math.random(),
              //@ts-ignore
              message: error?.response?.data?.msg || "Something went wrong. Please try again.",
              variant: "error",
            }))
        } finally{
          setLoading(false)
        }
        handleClose();
      },
    });

    return (
      <Dialog
        sx={(theme) => ({
        })}
        open={open}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            handleClose(event, reason); // only close if reason is not backdrop click or escape key
          }
        }}
        PaperProps={{
          component: 'form',
          onSubmit: formik.handleSubmit,
        }}
        maxWidth="md"
      >
            <Backdrop
              sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
              open={loading}
            >
              <CircularProgress
                color="inherit" />
            </Backdrop>
            <FormikProvider value={formik}>
              <DialogTitle sx={(theme) => ({
                fontWeight: '600',
              })}
              >
                Invite User
              </DialogTitle>
              <DialogContent
                sx={(theme) => ({
                  backgroundColor:theme?.customProperties.paginationDiv,
                })}
              >
                {/* Headers */}
                <Grid container spacing={2} style={{ marginBottom: '16px', marginTop:'10px' }} />
                <FieldArray name="users">
                  {({ push, remove }) => (
                    <>
                      {formik.values.users.map((row, index) => (
                        <Box
                          key={row.id}
                          sx={{
                            display: 'flex',
                            gap: 2,
                            marginBottom: '16px',
                            alignItems: 'center',
                          }}
                        >
                          <Box sx={{ flex: 1, minWidth: '190px' }}>
                            <TextField
                              fullWidth
                              variant="outlined"
                              placeholder="First Name"
                              name={`users[${index}].firstName`}
                              value={formik.values.users[index].firstName}
                              onChange={formik.handleChange}
                              onBlur={event => {
                                formik.handleBlur(event, index);
                              }}
                              InputProps={{
                                style: {
                                  padding: '0px 10px',
                                },
                              }}
                            />
                            {formik.touched.users?.[index]?.firstName && formik.errors.users?.[index]?.firstName ? (
                              <div style={{ color: 'red' }}>{formik.errors.users[index].firstName}</div>
                            ) : null}
                          </Box>

                          <Box sx={{ flex: 1, minWidth: '190px' }}>
                            <TextField
                              variant="outlined"
                              fullWidth
                              placeholder="Last Name"
                              name={`users[${index}].lastName`}
                              value={formik.values.users[index].lastName}
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                              InputProps={{
                                style: {
                                  padding: '0px 10px',
                                },
                              }}
                            />
                            {formik.touched.users?.[index]?.lastName && formik.errors.users?.[index]?.lastName ? (
                              <div style={{ color: 'red' }}>{formik.errors.users[index].lastName}</div>
                            ) : null}
                          </Box>

                          <Box sx={{ flex: 1, minWidth: '190px' }}>
                            <TextField
                              variant="outlined"
                              fullWidth
                              placeholder="Email"
                              name={`users[${index}].email`}
                              value={formik.values.users[index].email}
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                              InputProps={{
                                style: {
                                  padding: '0px 10px',
                                },
                              }}
                            />
                            {formik.touched.users?.[index]?.email && formik.errors.users?.[index]?.email ? (
                              <div style={{ color: 'red' }}>{formik.errors.users[index].email}</div>
                            ) : null}
                          </Box>

                          <Box sx={{ flex: 1, minWidth: '190px', display: 'flex', alignItems: 'center', flexDirection:'column' }}>
                            <Select
                              fullWidth
                              displayEmpty
                              name={`users[${index}].role`}
                              value={formik.values.users[index].role}
                              onChange={(event) => {
                                const selectedRole = roleList.find(role => role.id === event.target.value);
                                if (selectedRole) handleRoleChange(selectedRole, index);
                              }}
                              onBlur={formik.handleBlur}
                              sx={{ borderRadius: '24px' }}
                            >
                              <MenuItem value="">
                                <em>Select role</em>
                              </MenuItem>
                              {roleList.map((role) => (
                                <MenuItem key={role.id} value={role.id}>
                                  {role.name}
                                </MenuItem>
                              ))}
                            </Select>
                            {formik.touched.users?.[index]?.role && formik.errors.users?.[index]?.role ? (
                              <div style={{ color: 'red' }}>{formik.errors.users[index].role}</div>
                            ) : null}
                          </Box>
                          {/*<Box sx={{ flexShrink: 0, display: 'flex', alignItems: 'center' }}>*/}
                          {/*  {formik.values.users.length > 1 && (*/}
                          {/*    <IconButton*/}
                          {/*      aria-label="delete"*/}
                          {/*      onClick={() => remove(index)}*/}
                          {/*      style={{ marginLeft: '8px' }}*/}
                          {/*    >*/}
                          {/*      <Delete />*/}
                          {/*    </IconButton>*/}
                          {/*  )}*/}
                          {/*</Box>*/}
                        </Box>
                      ))}
                    </>
                  )}
                </FieldArray>
              </DialogContent>
              <Stack
                sx={(theme) => ({
                  backgroundColor: theme?.customProperties.paginationDiv,
                  padding: '24px',
                  display: 'flex',
                  justifyContent:'center',
                  flexDirection: 'row',
                  gap: '5px',
                  borderTop:'1px solid #E6E6E6',
                })}
              >
                <Button
                  onClick={handleClose}
                  sx={(theme) => ({
                    background: 'none',
                    backgroundColor: theme.palette.mode === 'dark' ? '#333' : 'none', // Dark mode background color
                    color: theme.palette.mode === 'dark' ? '#fff' : 'black', // Text color for dark mode
                    border: theme.palette.mode === 'dark' ? '1px solid #444' : '1px solid #171819', // Border color for dark mode
                    display: 'flex',
                    padding: '16px 32px',
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: '10px',
                    alignSelf: 'stretch',
                    borderRadius: '100px',
                    margin: '0px',
                    width: '30%',
                  })}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  sx={(theme) => ({
                    display: 'flex',
                    padding: '16px 32px',
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: '10px',
                    alignSelf: 'stretch', borderRadius: '100px',
                    width: '30%',

                  })}
                  disabled={!formik.isValid || formik.values.users.length === 0}
                >
                  Send Invite
                </Button>
              </Stack>
            </FormikProvider>

      </Dialog>
    );
  }
