import { Theme, makeStyles } from '@mui/material';

const useStyles = makeStyles((theme: Theme) => ({
  root: {
    backgroundImage: 'url("assets/images/onboarding-background.png")',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100vh',
  },
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  card: {
    borderRadius: '24px',
    width: '100%',
    [theme.breakpoints.up('sm')]: {
      width: 'auto', // Set to auto for larger screens
    },
    padding: '20px 30px 20px 30px !important',
    // padding: '80px 90px 80px 90px !important',
  },
  button: {
    marginTop: theme.spacing(1.75),
    marginBottom: theme.spacing(1.75),
    borderRadius: '100px',
    padding: '16px 32px 16px 32px',
    textTransform: 'none',
    fontSize: '14px',
    fontWeight: 500,
    lineHeight: '17.64px',
    boxShadow: 'none',
  },

  buttonOtp: {
    borderRadius: '100px',
    padding: '16px 32px 16px 32px',
    textTransform: 'none',
    fontSize: '14px',
    fontWeight: 500,
    lineHeight: '17.64px',
    boxShadow: 'none',
  },
  headingMargin: {
    marginBottom: theme.spacing(1.5),
  },
  formMargin: {
    marginBottom: theme.spacing(2),
    marginTop: theme.spacing(3),
  },
  formMarginOtp: {
    marginBottom: theme.spacing(2),
    marginTop: theme.spacing(0),
  },
  subHeading: {
    marginBottom: theme.spacing(1.5),
    color: '#6A797E',
  },
  outlinedInputRoot: {
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        borderRadius: '100px',
      },
    },
    '& .MuiOutlinedInput-input': {
      fontFamily: 'GilroyMedium',
      fontWeight: 500,
      fontSize: 14,
      borderRadius: '100px',
      padding: '10.5px 14px',
      background: '#F9F9F9',
    },
  },
  listHeading: {
    fontFamily: 'GilroyMedium',
    fontSize: 14,
    fontWeight: 800,
  },
  list: {
    padding: 0,
    color: '#6A797E',
  },
  listItem: {
    padding: 0,
    '&::marker': {
      content: "''",
    },
  },
  listText: {
    margin: 0,
    '& .MuiTypography-body2': {
      fontFamily: 'GilroyMedium',
      fontWeight: 600,
    },
  },
  errorMessage: {
    color: '#f44336',
    margin: '0px',
    fontSize: '0.75rem',
    fontFamily: 'Roboto, Helvetica, Arial, sans-serif',
  },
}));

export default useStyles;
