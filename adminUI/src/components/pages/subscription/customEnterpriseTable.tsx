import React, {useEffect, useState} from 'react';
import ViewIcon from '../../../theme/icons/ViewIcon';
import EditIcon from '../../../theme/icons/EditIcon';
import TrashIcon from '../../../theme/icons/TrashIcon';
import GenericTable from '../../design/GenericTable';
import { Box } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import httpRequest from "../../../service/httpRequest";
import cryptoService from "../../../service/crypto.service";
import dayjs from "dayjs";

interface Column {
    label: string;
    field: string;
    align?: "right" | "left" | "center";
}

interface DataItem {
    id: number;
    subscriptionPlan: string;
    subscribers: number;
    lastUpdated: string;
    status: string;
    actionsButtons: boolean | JSX.Element;
}

interface ActionButtonGroupProps {
    record: DataItem;
    handleEdit: (id: any) => void;
    handleDelete: (id: number) => void;
    handleView: (id: any) => void;
}

const ActionButtonGroup: React.FC<ActionButtonGroupProps> = ({
    record,
    handleEdit,
    handleDelete,
    handleView,
}) => {
    return (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ViewIcon onClick={() => handleView(record)} />
            <EditIcon onClick={() => handleEdit(record)} />
            {/*<TrashIcon onClick={() => handleDelete(record.id)} />*/}
        </Box>
    );
};

const CustomEnterprise: React.FC = () => {
    
    const navigate = useNavigate();
    const [data, setData] = useState([]);

    const columns: Column[] = [
        { label: "Subscription Plan", field: "subscriptionPlan" },
        { label: "Subscribers", field: "subscribers", align: "right" },
        { label: "Last Updated", field: "lastUpdated" },
        { label: "Status", field: "status" },
        { label: "Actions", field: "actions" },
    ];

    // const data: DataItem[] = [
    //     {
    //         id: 1,
    //         subscriptionPlan: "Basic Plan 22222222",
    //         subscribers: 120,
    //         lastUpdated: "2024-12-01",
    //         status: "Active",
    //         actionsButtons: true,
    //     },
    //     {
    //         id: 2,
    //         subscriptionPlan: "Premium Plan33333333333333",
    //         subscribers: 250,
    //         lastUpdated: "2024-11-15",
    //         status: "Inactive",
    //         actionsButtons: true,
    //     },
    //     {
    //         id: 3,
    //         subscriptionPlan: "Enterprise Plan",
    //         subscribers: 500,
    //         lastUpdated: "2024-12-20",
    //         status: "Active",
    //         actionsButtons: true,
    //     },
    // ];

    useEffect(() => {
        httpRequest.post('/subscription-plan/list/1?custom=true').then((response) => {
            console.log(cryptoService.decrypt(response.data.data));
            const plans = cryptoService.decrypt(response.data.data);
            const plansList = plans.list;
            const mappedPlans = plansList.map((plan: any): DataItem => {
                return {
                    id: plan.id,
                    subscriptionPlan: plan.name,
                    subscribers: 0,
                    lastUpdated: dayjs(plan.updatedAt).format("DD-MM-YYYY"),
                    status: plan.status === 1 ? "Active" : "Inactive",
                    actionsButtons: true,
                };
            });
            setData(mappedPlans);
        });
    }, []);

    const handleDelete = (id: number) => {
        console.log(`Deleting record with ID: ${id}`);
    };

    const handleEdit = (record: any) => {
        console.log(`Viewing record with ID: ${record}`);
            navigate(`/subscriptions/subscriptionDetails/${record.id}`, {
                state: { Heading: `${record.subscriptionPlan} Details`, Edit: true, Type:'Enterprise'}
            });
    };
    const handleView = (record: any) => {
        console.log(`Viewing record with ID: ${record.id}`);
        navigate(`/subscriptions/subscriptionDetails/${record.id}`, {
            state: { Heading: `${record.subscriptionPlan} Details`, Edit: false, Type:'Enterprise'}
        });
    };

    const handleRowClick = (row: any) => {
        console.log("Row clicked:", row);
    };


    return (
        <GenericTable
            columns={columns}
            data={data.map((item) => ({
                // @ts-ignore
                ...item,
                // @ts-ignore
                actionsButtons: item.actionsButtons ? (
                    <ActionButtonGroup
                        record={item}
                        handleEdit={handleEdit}
                        handleDelete={handleDelete}
                        handleView={handleView}
                    />
                ) : null,
            }))}
        />
    );
};

export default CustomEnterprise;
