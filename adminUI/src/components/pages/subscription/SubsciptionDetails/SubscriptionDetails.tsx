// @ts-nocheck
import React, { useEffect, useState } from 'react';
import { Box, Button, Stack, Typography, useTheme, Checkbox, FormControlLabel } from '@mui/material';
import {useLocation, useNavigate, useParams} from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import dayjs from 'dayjs';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

import { PageItem } from '../../../shared';
import CustomAppBar from '../../../design/CustomAppBar';
import DeleteIcon from '../../../../theme/icons/TrashIcon';
import EditIcon from '../../../../theme/icons/EditIcon';
import SaveIcon from '../../../../theme/icons/SaveIcon';
import CancelIcon from '../../../../theme/icons/CancelIcon';
import { appActions } from '../../../../reducers/appSlice';
import GenericDateInput from '../../../design/GenericDateInput';
import CustomInputField from '../../../design/GenericInputField';
import { validationSchemaEnterprise, validationSchemaStandard } from '../../../../constants/validationShemas';
import { EnterpriseSubscription, StandardSubscription } from '../../../../constants/fields';
import SubscriptionBilling from '../SubscriptionBilling';
import httpRequest from "../../../../service/httpRequest";
import cryptoService from "../../../../service/crypto.service";

// Mapper for converting form field labels to snake_case keys for the API payload
export const enterpriseMapper = {
  'Enterprise Name': 'name',
  // 'Date Assigned': 'date_assigned',
  'Description': 'description',
  'Audio Consultation (mins)': 'audio_consultation_mins',
  'Pre-made Templates': 'pre_made_templates',
  'Custom Templates': 'custom_templates_limit',
  'Designs': 'design_templates_limit',
  'Team Members': 'allows_team_members',
  'Customer Email': 'customer_email',
  'Trial Days': 'trial_period_days',
  'Price': 'price',
  'User Log': 'has_user_logs',
  'Medical History': 'has_medical_history',
  'Tasks': 'has_tasks',
  'Plan Details': 'plan_details',
};

// Mapper for converting form field labels to snake_case keys for the API payload
export const standardMapper = {
  'Name': 'name',
  // 'Date Assigned': 'date_assigned',
  'Description': 'description',
  'Audio Consultation (mins)': 'audio_consultation_mins',
  'Pre-made Templates': 'pre_made_templates',
  'Custom Templates': 'custom_templates_limit',
  'Designs': 'design_templates_limit',
  'Team Members': 'allows_team_members',
  'Trial Days': 'trial_period_days',
  'Price': 'price',
  'User Log': 'has_user_logs',
  'Medical History': 'has_medical_history',
  'Tasks': 'has_tasks',
  'Plan Details': 'plan_details',
};

// Function to map the API response (snake_case keys) to our Enterprise form keys
const mapApiToEnterpriseForm = (apiData) => {
  console.log(apiData)
  return {
    'Enterprise Name': apiData.name || '',
    // 'Date Assigned': apiData.createdAt ? dayjs(apiData.createdAt) : dayjs(),
    'Description': apiData.description || '',
    'Audio Consultation (mins)': apiData.audio_consultation_minutes || 0,
    'Pre-made Templates': apiData.has_pre_made_templates || false, // default value if not provided
    'Custom Templates': apiData.custom_templates_limit || 0,
    'Designs': apiData.design_templates_limit || 0,
    'Team Members': apiData.allows_team_members || false,
    'Customer Email': apiData.user?.email, // not provided by API
    'Trial Days': apiData.trial_period_days || 0,
    'Price': apiData.price || '',
    'User Log': apiData.has_user_logs || false,
    'Medical History': apiData.has_medical_history || false,
    'Tasks': apiData.has_tasks || false,
    'Plan Details': apiData.plan_details || '' // New rich text field for plan/subscription details
  };
};

// Function to map the API response (snake_case keys) to our Enterprise form keys
const mapApiToStandardForm = (apiData) => {
  return {
    'Name': apiData.name || '',
    // 'Date Assigned': apiData.createdAt ? dayjs(apiData.createdAt) : dayjs(),
    'Description': apiData.description || '',
    'Audio Consultation (mins)': apiData.audio_consultation_minutes || 0,
    'Pre-made Templates':  apiData.has_pre_made_templates ||false, // default value if not provided
    'Custom Templates': apiData.custom_templates_limit || 0,
    'Designs': apiData.design_templates_limit || 0,
    'Team Members': apiData.allows_team_members || false,
    // 'Customer Email': apiData.user?.email, // not provided by API
    'Trial Days': apiData.trial_period_days || 0,
    'Price': apiData.price || '',
    'User Log': apiData.has_user_logs || false,
    'Medical History': apiData.has_medical_history || false,
    'Tasks': apiData.has_tasks || false,
    'Plan Details': apiData.plan_details || '' // New rich text field for plan/subscription details
  };
};

const SubscriptionDetails = () => {
  const location = useLocation();
  const { id } = useParams(); // subscriptionPlanId from URL
  const theme = useTheme();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [isEditSubscription, setIsEditSubscription] = useState(location.state?.Edit);

  const [isActive, setIsActive] = useState(true);
  const [billingType, setBillingType] = useState("monthly");
  const [disabled, setDisabled] = useState(false);
  const [disbaledBillingDiv, setDsiableBillingDiv] = useState(!location.state?.Edit);

  const features = [
    "Integration",
    "Roles and Permissions",
    "Assignments",
    "Custom Terms",
    "Silo & Hosting",
  ];

  const [settings, setSettings] = useState(
    Object.fromEntries(features.map((feature) => [
      feature.toLowerCase().replace(/ & /g, "").replace(/ /g, ""),
      false,
    ]))
  );

  const handleSettingToggle = (key: string) => {
    if (disabled) return;
    setSettings((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };
  // Default initial values based on subscription type (Enterprise vs Standard)
  const defaultValues = location.state?.Type === 'Enterprise' ? {
    'Enterprise Name': '',
    // 'Date Assigned': dayjs('2023-12-01'),
    'Description': '',
    'Audio Consultation (mins)': 60,
    'Pre-made Templates': false,
    'Custom Templates': 5,
    'Designs': 1,
    'Team Members': true,
    'Customer Email': '',
    'Trial Days': 30,
    'Price': '20',
    'User Log': true,
    'Medical History': true,
    'Tasks': true,
    'Plan Details': '' // New rich text field for plan/subscription details
  } : {
    Name: 'Sample Text',
    // 'Date Assigned': dayjs('2023-12-01'),
    'Description': '',
    'Audio Consultation (mins)': 60,
    'Pre-made Templates': false,
    'Custom Templates': 5,
    'Designs': 1,
    'Team Members': true,
    'Trial Days': 30,
    'Price': '20',
    'User Log': true,
    'Medical History': true,
    'Tasks': true,
    'Plan Details': '' // New rich text field for plan/subscription details
  };

  // Local state to hold the form's initial values
  const [initialFormValues, setInitialFormValues] = useState(defaultValues);

  useEffect(() => {
    if (!isEditSubscription) {
      if (location.state?.Type === 'standard') {
        dispatch(appActions.setBreadcrumb('/viewSubscriptionStandard'));
      } else {
        dispatch(
          appActions.setBreadcrumb({
            path: '/viewSubscriptionStandard',
            heading: `Subscriptions / Custom Enterprise / ${location.state?.Heading}`,
          })
        );
      }
    } else {
      if (location.state?.Type === 'standard') {
        dispatch(appActions.setBreadcrumb('/editSubscriptionStandard'));
      } else {
        if (id === "0") {
          dispatch(
            appActions.setBreadcrumb({
              path: '/viewSubscriptionEnterprise',
              heading: `Subscriptions / Custom Enterprise / Create`,
            })
          );
        } else {
          dispatch(
            appActions.setBreadcrumb({
              path: '/viewSubscriptionEnterprise',
              heading: `Subscriptions / Custom Enterprise / ${location.state?.Heading} / Edit`,
            })
          );
        }
      }
    }
  }, [isEditSubscription, location.state, dispatch]);

  // Fetch subscription details if editing (id is not "0")
  useEffect(() => {
    if (id && id !== "0") {
      httpRequest.get(`/subscription-plan/detail/${id}`)
        .then((response) => {
          if (response.data && response.data.data) {
            const decryptedData = cryptoService.decrypt(response.data.data);
            console.log(decryptedData)
            const mappedData = location.state?.Type === 'Enterprise' ? mapApiToEnterpriseForm(decryptedData) : mapApiToStandardForm(decryptedData);
            console.log(mappedData)
            if(decryptedData?.subscription_features){
              setSettings(decryptedData.subscription_features);
            }
            setBillingType(decryptedData.plan_type)
            setInitialFormValues(mappedData);
            setIsActive(decryptedData.status === 1);
          }
        })
        .catch((error) => {
          console.error("Error fetching subscription details", error);
        });
    }
  }, [id]);

  const buttonHeadingStyles = () => ({
    background: 'none',
    '&:hover': { border: 'none', boxShadow: 'none' },
    color: theme.customProperties.ButtonTextColor,
    textTransform: 'none',
    fontSize: '14px',
    padding: '0px 20px',
    fontWeight: 500,
    gap: '0px',
  });

  const handleTextInputChange = (value, fieldType) => {
    let processedValue = value;
    if (fieldType === 'number') {
      processedValue = processedValue.replace(/[^0-9]/g, '');
    }
    return processedValue;
  };

  const handleToggleAllSettings = (value: boolean) => {
    setDisabled(!value);
  };

  const getHeaderTitle = () => {
    if (id === "0") {
      return "Create Subscription";
    }

    return location.state ? `${isEditSubscription ? 'Edit' : ''} ${location.state.Heading}` : 'Details';
  };

  console.log(settings)
  return (
    <PageItem>
      <CustomAppBar
        leftContent={
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{ flexGrow: 1, display: { sm: 'block' }, fontSize: '18px', fontFamily: 'GilroyMedium' }}
          >
            {getHeaderTitle()}
          </Typography>
        }
        rightContent={
          <Box sx={{ display: 'flex', gap: '10px' }}>
            {!isEditSubscription && (
              <>
                <Button
                  size="small"
                  sx={buttonHeadingStyles}
                  startIcon={<EditIcon />}
                  onClick={() => {
                    setIsEditSubscription(true);
                    setDsiableBillingDiv(false)
                  }}
                >
                  Edit Subscription
                </Button>
                {/*<Button size="small" sx={buttonHeadingStyles} startIcon={<DeleteIcon />}>*/}
                {/*  Delete Subscription*/}
                {/*</Button>*/}
              </>
            )}
            {isEditSubscription && (
              <>
                <Button
                  size="small"
                  sx={buttonHeadingStyles}
                  startIcon={<CancelIcon />}
                  onClick={() => {
                    setIsEditSubscription(false);
                    setDsiableBillingDiv(true)
                  }}
                >
                  Cancel
                </Button>
                <Button
                  size="small"
                  sx={buttonHeadingStyles}
                  startIcon={<SaveIcon />}
                  type="submit"
                  form="subscriptionForm"
                >
                  Save
                </Button>
              </>
            )}
          </Box>
        }
      />
      <Stack direction={'row'}>
        <Stack width={'50%'}>
          <Formik
            initialValues={initialFormValues}
            validationSchema={location.state?.Type === 'Enterprise' ? validationSchemaEnterprise : validationSchemaStandard}
            enableReinitialize
            onSubmit={(values, { resetForm }) => {

              const cleanedValues = { ...values };
              Object.keys(cleanedValues).forEach((key) => {
                if (typeof cleanedValues[key] === 'string') {
                  let processedValue = cleanedValues[key];
                  processedValue = processedValue.trim();
                  processedValue = processedValue.replace(/\s+/g, ' ');
                  processedValue = processedValue.replace(/^\s+|\s+$/g, '');
                  cleanedValues[key] = processedValue;
                }
              });
              console.log('cleanedValues:', cleanedValues);
              console.log('Settings:', settings);
              console.log('Billing:', billingType);
              const snakeCaseValues = {};
              if (location.state?.Type === 'Enterprise') {
                Object.keys(enterpriseMapper).forEach((formKey) => {
                  snakeCaseValues[enterpriseMapper[formKey]] = cleanedValues[formKey];
                });
              } else {
                Object.keys(standardMapper).forEach((formKey) => {
                  snakeCaseValues[standardMapper[formKey]] = cleanedValues[formKey];
                });
              }
              console.log('Saved Data:', snakeCaseValues);
              console.log(snakeCaseValues)
              const payload = {
                name: snakeCaseValues.name,
                price: snakeCaseValues.price,
                currency: 'USD',
                description: snakeCaseValues.description,
                audio_consultation_mins: cleanedValues['Audio Consultation (mins)'],
                pre_made_templates: snakeCaseValues.pre_made_templates,
                custom_templates: snakeCaseValues.custom_templates_limit,
                designs: snakeCaseValues.design_templates_limit,
                team_members: snakeCaseValues.allows_team_members,
                email: snakeCaseValues.customer_email,
                trial_period_days: snakeCaseValues.trial_period_days,
                user_log: snakeCaseValues.has_user_logs,
                medical_history: snakeCaseValues.has_medical_history,
                tasks: snakeCaseValues.has_tasks,
                is_custom: location.state?.Type === 'Enterprise',
                plan_type: billingType.charAt(0).toUpperCase() + billingType.slice(1),
                plan_details: snakeCaseValues.plan_details,
                status: isActive ? 1 : 0,
                subscription_features: settings,
              };
              console.log(payload);
              // If id is "0", create a new subscription; otherwise, update the existing one.
              if (id === "0") {
                dispatch(appActions.setLoading(true));
                httpRequest.post('/subscription-plan/add', {
                  encrypet: cryptoService.encrypt(payload),
                }).then(() => {
                  navigate('/subscriptions')
                  dispatch(appActions.setLoading(false));
                }).catch((err) => {
                  dispatch(appActions.setLoading(false));
                  // setIsEditSubscription(false);
                  // resetForm();
                  console.log(err);
                  dispatch(
                    appActions.enqueueSnackbar({
                      key: new Date().getTime() + Math.random(),
                      message: `Something went wrong: \n ${err}`,
                      variant: "error",
                    }));
                });;
              } else {
                dispatch(appActions.setLoading(true));
                httpRequest.patch(`/subscription-plan/edit/${id}`, {
                  encrypet: cryptoService.encrypt(payload),
                }).then(() => {
                  dispatch(appActions.setLoading(false));
                  setIsEditSubscription(false);
                  setDsiableBillingDiv(true)
                  // resetForm();
                  dispatch(
                    appActions.enqueueSnackbar({
                      key: new Date().getTime() + Math.random(),
                      message: 'Subscription plan saved successfully',
                      variant: "success",
                    }));
                }).catch((err) => {
                  dispatch(appActions.setLoading(false));
                  // setIsEditSubscription(false);
                  // resetForm();
                  console.log(err);
                  dispatch(
                    appActions.enqueueSnackbar({
                      key: new Date().getTime() + Math.random(),
                      message: `Something went wrong: \n ${err}`,
                      variant: "error",
                    }));
                });
              }
            }}
          >
            {({ values, errors, touched, handleChange, setFieldValue, handleBlur, resetForm }) => (
              <Form id="subscriptionForm">
                <Box sx={{ padding: '20px' }}>
                  {(
                    location.state?.Type === 'Enterprise'
                      ? EnterpriseSubscription
                      : StandardSubscription
                  ).map((field) => {
                    // console.log({field, values, [field.label]: values[field.label]});
                    return (
                      <Box key={field.label} sx={{marginBottom: '20px'}}>
                        {field.type === 'date' ? (
                          <GenericDateInput
                            label={field.label}
                            value={values[field.label]}
                            editable={isEditSubscription}
                            onChange={(value) => setFieldValue(field.label, value)}
                            error={touched[field.label] && errors[field.label]}
                            type="date"
                            color="black"
                            disableFuture={true}
                            labelBold={true}
                          />
                        ) : field.type === 'checkbox' || typeof values[field.label] === 'boolean' ? (
                          <FormControlLabel
                            control={
                              <Field
                                name={field.label}
                                type="checkbox"
                                as={Checkbox}
                                disabled={!isEditSubscription}
                                checked={values[field.label]}
                                onChange={handleChange}
                              />
                            }
                            label={field.label}
                          />
                        ) : field.type === 'richtext' ? (
                          <Box>
                            <Typography variant="subtitle1" gutterBottom>
                              {field.label}
                            </Typography>
                            <ReactQuill
                              theme="snow"
                              value={values[field.label]}
                              onChange={(content, delta, source, editor) =>
                                setFieldValue(field.label, editor.getHTML())
                              }
                              readOnly={!isEditSubscription}
                              style={{
                                width: '650px',
                                maxHeight: '400px',
                                overflowY: 'auto',
                              }}
                            />
                            <div style={{color:'red', fontWeight:'500'}}>
                              {touched[field.label] && errors[field.label]}
                            </div>
                          </Box>

                        ) : (
                          <CustomInputField
                            id={field.label}
                            label={field.label}
                            type={field.type}
                            value={values[field.label]}
                            editable={isEditSubscription}
                            onChange={(value) => {
                              const processedValue = handleTextInputChange(value, field.type);
                              setFieldValue(field.label, processedValue);
                            }}
                            onBlur={handleBlur}
                            error={touched[field.label] && errors[field.label]}
                          />
                        )}
                      </Box>
                    );
                  })}
                </Box>
              </Form>
            )}
          </Formik>
        </Stack>
        <Stack width={'30%'}>
          <SubscriptionBilling
            isActive={isActive}
            disableWholeDiv={disbaledBillingDiv}
            billingType={billingType}
            settings={settings}
            disabled={disabled}
            features={features}
            activeLabel="Subscription Active"
            onActiveChange={setIsActive}
            onBillingChange={setBillingType}
            onSettingToggle={handleSettingToggle}
            onToggleAllSettings={handleToggleAllSettings}
          />
        </Stack>
      </Stack>
    </PageItem>
  );
};

export default SubscriptionDetails;
