import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Box, Typography, useTheme } from '@mui/material';
import TaskCard from "../../design/TaskCard";
import { cardStyles } from "../../design/Styles";
import TasksServices from "../../../service/apiCalls/TasksServices";
import DropDown from "../../design/DropDown";
import Loader from "../../design/Loader";
import {useAppDispatch} from "../../../app/hooks";
import {appActions} from "../../../reducers/appSlice";

const initialCardsData = [
  { label: 'Total Task', value: '0', percentage: '+0%', color: '#E5ECF6' },
  { label: 'Completed', value: '0', percentage: '+0%', color: '#E6F6E5' },
  { label: 'In Progress', value: '0', percentage: '+0%', color: '#FFEFD6' },
  { label: 'Need Feedback', value: '0', percentage: '+0%', color: '#F4E3FF' },
  { label: 'Not Started', value: '0', percentage: '+0%', color: '#FFE3E3' },
];

const options = ['TODAY', 'THIS_MONTH', 'THIS_YEAR', 'ALL'];

const Cards = () => {
  const dispatch = useAppDispatch();
  const [selectedOption, setSelectedOption] = useState(options[0]);
  const [cardsData, setCardsData] = useState(initialCardsData);
  const [loaded, setLoaded] = useState(false);

  const fetchTaskStatus = async () => {
    try {
      const response = await TasksServices.getTasks(selectedOption);
        const { total, completed, inProgress, needFeedback, notStarted } = response;
        setCardsData([
          { label: 'Total Task', value: total.toString(), percentage: ``, color: '#E5ECF6' },
          { label: 'Completed', value: completed.toString(), percentage: ``, color: '#E6F6E5' },
          { label: 'In Progress', value: inProgress.toString(), percentage: ``, color: '#FFEFD6' },
          { label: 'Need Feedback', value: needFeedback.toString(), percentage: ``, color: '#F4E3FF' },
          { label: 'Not Started', value: notStarted.toString(), percentage: ``, color: '#FFE3E3' },
        ]);
        setLoaded(true);
    } catch (error) {
      dispatch(
        appActions.enqueueSnackbar({
          key: new Date().getTime() + Math.random(),
          //@ts-ignore
          message: error?.response?.data?.msg || "Something went wrong. Please try again.",
          variant: "error",
        }))

    }finally{
      setLoaded(true);
    }
  };

  useEffect(() => {
    fetchTaskStatus();
  }, [selectedOption]);

  return (
    <Box>
      {!loaded ? (
        <Box sx={{height:'60px'}}>
          <Loader />
        </Box>
      ) : (
        <Box>
          <Typography variant="h6" noWrap component="div" sx={{ fontSize: '14px', fontWeight: '600', mb: '5px' }}>
            {selectedOption}
            <DropDown options={options} selectedOption={selectedOption} onSelect={setSelectedOption} />
          </Typography>
          <Box sx={cardStyles.gridContainer}>
            {cardsData.map((data, index) => (
              <Box key={index} sx={cardStyles.gridItem}>
                <TaskCard {...data} loaded={loaded} />
              </Box>
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default Cards;
