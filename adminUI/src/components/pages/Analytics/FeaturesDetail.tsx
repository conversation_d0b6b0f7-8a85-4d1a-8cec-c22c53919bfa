import React, { useEffect, useState } from "react";

import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  List,
  ListItem,
  ListItemText,
  styled,
  Box,
} from "@mui/material";

import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

import analyticsPageServices from "../../../service/apiCalls/AnalyticsServices";

const StyledAccordion = styled(Accordion)({
  borderRadius: "8px",
  backgroundColor: "#F8F8FC",
  border: "1px solid #EFEFEF",
  margin: "12px 0px",
  boxShadow: "none",
  "&::before": {
    display: "none",
  },
});

const StyledAccordionSummary = styled(AccordionSummary)({
  minHeight: "40px !important",
  "& .MuiAccordionSummary-content": {
    margin: "0px",
  },
  borderRadius: "16px",
});

const StyledAccordionDetails = styled(AccordionDetails)({
  padding: "8px 16px !important",
  borderRadius: "8px",
});

// Define the type for each document
interface DocumentData {
  name: string;
  usagePercentage: number;
}

const FeaturesDetail: React.FC = () => {
  const [expanded, setExpanded] = useState<number | null>(null);
  const [mostUsedDocuments, setMostUsedDocuments] = useState<DocumentData[]>([]);

  // Typing parameters explicitly
  const handleAccordionChange =
    (panel: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpanded(isExpanded ? panel : null);
    };

  const fetchMostUsedDocuments = async () => {
    try {
      const data: { list: DocumentData[] } = await analyticsPageServices.getMostUsedDocumentsStatus();
      setMostUsedDocuments(data.list);
    } catch (error) {
      console.error("Error fetching most used documents:", error);
    }
  };

  useEffect(() => {
    fetchMostUsedDocuments();
  }, []);

  const sections = [
    {
      title: "5 Most Used Document",
      content: mostUsedDocuments.slice(0, 5).map((doc) => ({
        name: doc.name,
        percentage: doc.usagePercentage,
      })),
    },
    { title: "Note Input Stats", content: "Some statistics here..." },

  ];

  return (
    <Box
      sx={{
        padding: "24px",
        border: "1px solid #DEDEF2",
        borderRadius: "16px",
        minHeight: "42%",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
      }}
    >
      {sections.map((section, index) => (
        <StyledAccordion
          key={index}
          expanded={expanded === index}
          onChange={handleAccordionChange(index)}
        >
          <StyledAccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography
              sx={{
                fontSize: "16px",
                fontWeight: expanded === index ? "600" : "400",
                padding: "0px",
              }}
            >
              {section.title}
            </Typography>
          </StyledAccordionSummary>
          <StyledAccordionDetails>
            {Array.isArray(section.content) ? (
              <List>
                {section.content.map((doc, idx) => (
                  <ListItem key={idx} disableGutters>
                    <ListItemText primary={doc.name} />
                    <Typography>{doc.percentage}</Typography>
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography>{section.content}</Typography>
            )}
          </StyledAccordionDetails>
        </StyledAccordion>
      ))}
    </Box>
  );
};

export default FeaturesDetail;
