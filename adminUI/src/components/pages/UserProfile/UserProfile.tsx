//@ts-nocheck
import React, { useEffect, useRef, useState } from 'react';
import { useFormik } from 'formik';
import {
  Typo<PERSON>,
  Button,
  Stack,
  Avatar,
  Backdrop,
  Box,
  CircularProgress,
  Alert,
} from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';
import { useDispatch} from 'react-redux';
import { shallowEqual, useSelector } from 'react-redux';
import EditIcon from '@mui/icons-material/Edit';
import DoneOutlinedIcon from '@mui/icons-material/DoneOutlined';
import { fields } from './Fields';
import validationSchema from './ValidationSchema';
import CustomInputFieldProfile from './CustomInputField';
import IRootState from "../../../models/IRootState";
import CustomAppBar from "../../design/CustomAppBar";
import {selectUser} from "../../../selectors/authSelector";
import adminProfilePage from "../../../pages/AdminProfilePage";
import adminProfileServices from "../../../service/apiCalls/AdminProfileServices";
import {appActions} from "../../../reducers/appSlice";

const buttonHeadingStyles = (theme) => ({
  background: 'none', '&:hover': { border: 'none', boxShadow: 'none' },
  color: theme.customProperties.ButtonTextColor,
  textTransform: 'none',
  fontSize: '14px',
  Padding: '0px 20px',
  fontWeight: 500,
});

const iconHeadingStyles = {
  marginRight: '0px',
  width: '35px',
  padding: '0px',
  height: '24px',
};

const UserProfile = () => {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [editable, setEditable] = useState(false);
  const [initialValues, setInitialValues] = useState({});
  const [confirmationMessage, setConfirmationMessage] = useState(true);
  const [errorHandle, setErrorHandle] = useState(false);
  const [displayMessage, setDisplayMessage] = useState('');
  const [specialisation, setSpecialisation] = useState(5);
  const [organizationName, SetOrganizationName] = useState('');
  const [profileImage, setProfileImage] = useState(null);
  const [capticaError, setCapticaError] = useState(false);
  const dispatch = useDispatch();
  const recaptcha = useRef(null);

  const adminSelector = useSelector(selectUser, shallowEqual);
  const [isFormReady, setIsFormReady] = useState(false);
  console.log(adminSelector)
  const responseMessges = {
    profilepictureUploded: 'Profile photo has been updated successfully!',
    profilepictureError: 'Something went wrong!, Try again later',
    ProfileUpdated: 'Profile has been updated successfully!',
    ProfileError: 'Something went wrong!, Try again later',
    AccountDeleted: 'Account Deleted Successfully!',
  };

  useEffect(() => {
    dispatch(appActions.setBreadcrumb("/adminProfile"));
  }, [dispatch]);

  useEffect(() => {
    if (adminSelector) {
      fetchInitialFormValues();
    }
  }, [adminSelector]);

  async function fetchInitialFormValues() {
    setLoading(true);
    try {
     // const response = await ProfileDetails();
     //  if (!response) {
     //    throw new Error('Failed to fetch initial form values');
     //  }
     //  const data = response;
      console.log('selector',adminSelector)
      const Values = {
        first_name: adminSelector.first_name || '',
        last_name: adminSelector.last_name || '',
        email: adminSelector.email || '',
        newPassword: '',
        confirmNewPassword: '',
        oldPassword: '',
        role_name: adminSelector.roleDetail.name || '',
      };
      setInitialValues(Values);
      // setProfileImage(data.profileS3Url);
      // SetOrganizationName(data.clinic_name);
      setIsFormReady(true);
    } catch (error) {
      console.error('Error fetching initial form values:', error);
    } finally {
      setLoading(false);
    }
  }

  // async function UpateProfile(image: any) {
  //   setLoading(true);
  //   try {
  //    // const response = await ProfileImageUpdate(image);
  //     //setProfileImage(null);
  //     //setDisplayMessage(responseMessges.profilepictureUploded);
  //
  //   } catch (error) {
  //     setErrorHandle(true);
  //     setDisplayMessage(responseMessges.profilepictureError);
  //   } finally {
  //     setLoading(false);
  //   }
  // }

  // const handleFileChange = (event: any) => {
  //   const fileInput = event.target;
  //   const file = fileInput.files[0];
  //   if (file) {
  //     const reader = new FileReader();
  //     reader.onloadend = () => {
  //      // setProfileImage(reader.result);
  //     };
  //     reader.readAsDataURL(file);
  //
  //     const formData = new FormData();
  //     formData.append('file', file);
  //
  //     UpateProfile(formData);
  //   }
  //
  //   fileInput.value = '';
  // };
  //
  const handleSubmit = () => {
    formik.handleSubmit();
  };

  const handleEditPatient = () => {
    setEditable(true);
  };

  // const removeProfilePic = () => {
  //   UpateProfile(null);
  // };

  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: validationSchema,
    enableReinitialize: true,
    validateOnBlur: true,
    validateOnChange: true,
    onSubmit: async (values, { setErrors, resetForm }) => { // Destructure resetForm here
      setLoading(true);
       const respose = await validationSchema.validate(values, { abortEarly: false });

      const payload = {
        old_password: values.oldPassword,
        password: values.newPassword,
      };
      try {
        const response = await adminProfileServices.changePassword(payload);
        dispatch(
          appActions.enqueueSnackbar({
            key: new Date().getTime() + Math.random(),
            message: "Your password has been updated successfully",
            variant: "success",
          }))
        setEditable(false)
      } catch (error) {
        dispatch(
          appActions.enqueueSnackbar({
            key: new Date().getTime() + Math.random(),
            message: error?.response?.data?.msg || "Something went wrong. Please try again.",
            variant: "error",
          }))
        const errorMap = {};
          error.inner.forEach((error) => {
          errorMap[error.path] = error.message; // Map errors to fields
        });

        formik.setErrors(errorMap);
      } finally {
        resetForm();
        setLoading(false);
      }
    }
  });

  const showDialog = () => {
    setOpen(true);
  };

  if (!isFormReady) return <CircularProgress />;

  return (
    <Box>
      <Backdrop sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }} open={loading}>
        <CircularProgress color="inherit" />
      </Backdrop>

      {!confirmationMessage && !errorHandle && (
        <Alert icon={<CheckIcon fontSize="inherit" />} severity="success">
          {displayMessage}
        </Alert>
      )}

      {!confirmationMessage && errorHandle && (
        <Alert icon={<CloseIcon fontSize="inherit" />} severity="error">
          {displayMessage}
        </Alert>
      )}

      <CustomAppBar
        rightContent={
          <Box>

            {!editable ? (
              <Button
                size="small"
                sx={buttonHeadingStyles}
                startIcon={<EditIcon style={iconHeadingStyles} />}
                onClick={handleEditPatient}
              >
                Change Password
              </Button>
            ) : (
              <Button
                size="small"
                sx={buttonHeadingStyles}
                startIcon={<DoneOutlinedIcon style={iconHeadingStyles} />}
                onClick={handleSubmit}
              >
                Update Password
              </Button>
            )}
          </Box>
        }
        leftContent={
          <Typography
            variant="h1"
            sx={{
              fontSize: '18px',
              fontStyle: 'normal',
              fontWeight: 500,
            }}
          >
           Admin Profile Details
          </Typography>
        }
      />

      <Box sx={{ marginLeft: '32px' }}>
        {/*<Stack*/}
        {/*  direction="row"*/}
        {/*  sx={{*/}
        {/*    alignItems: 'center',*/}
        {/*    gap: '10px',*/}
        {/*    paddingTop: '2.5rem',*/}
        {/*  }}*/}
        {/*>*/}
        {/*  <Avatar src={profileImage} sx={{ height: '120px', width: '120px' }} />*/}
        {/*  <Stack sx={{ gap: '5px' }}>*/}
        {/*    <Button*/}
        {/*      size="small"*/}
        {/*      component="label"*/}
        {/*      htmlFor="profile-image-input"*/}
        {/*      startIcon={<EditTwoToneIcon className="custom-icon" sx={{ fontSize: '12px' }} />}*/}
        {/*      sx={{*/}
        {/*        fontSize: '12px',*/}
        {/*        borderRadius: '100px',*/}
        {/*        padding: '4px 12px 4px 6px',*/}
        {/*      }}*/}
        {/*    >*/}
        {/*      Change Photo*/}
        {/*      <input*/}
        {/*        id="profile-image-input"*/}
        {/*        type="file"*/}
        {/*        accept="image/*"*/}
        {/*      //    onChange={handleFileChange}*/}
        {/*        style={{ display: 'none' }}  */}
        {/*      />*/}
        {/*    </Button>*/}
        {/*    {profileImage && (*/}
        {/*      <Button*/}
        {/*        size="small"*/}
        {/*        onClick={removeProfilePic}*/}
        {/*        startIcon={<CloseIcon className="custom-icon" sx={{ fontSize: '12px' }} />}*/}
        {/*        sx={{*/}
        {/*          fontSize: '12px',*/}
        {/*          borderRadius: '100px',*/}
        {/*        }}*/}
        {/*      >*/}
        {/*        Remove Photo*/}
        {/*      </Button>*/}
        {/*    )}*/}
        {/*  </Stack>  */}
        {/*</Stack>*/}

        <Stack  direction="row" spacing={0} sx={{ marginTop: '25px', border: 'none'  }}>
         <Box sx={{width:'50%'}}>
           <Typography
             variant="h2"
             sx={{
               textTransform: 'none',
               fontSize: '16px',
               marginBottom: '40px',
               fontWeight: '700',
             }}
           >
             User Information
           </Typography>

           <form style={{ width:'90%' }} onSubmit={formik.handleSubmit}>
             {fields.map((field) => (
               <Box key={field.id} sx={{ paddingBottom: '20px' }}>
                 <CustomInputFieldProfile
                   id={field.id}
                   label={field.label}
                   value={formik.values[field.id]}
                   onChange={formik.handleChange(field.id)}
                   // onBlur={formik.handleBlur(field.id)}
                   editable= {false}

                   // error={formik.touched[field.id] && formik.errors[field.id]}
                   pattern={field.pattern}
                   maxLength={field.maxLength}
                   type={field.type}
                 />
               </Box>
             ))}
           </form>

         </Box>

          <Box sx={{width:'50%'}}>
          <Typography
              variant="h2"
              sx={{
                textTransform: 'none',
                fontSize: '16px',
                marginBottom: '40px',
                fontWeight: '700',
              }}
            >
              Reset Password
            </Typography>


            <form onSubmit={formik.handleSubmit}>
              <Box sx={{ paddingBottom: '15px', width: '90%' }}>
                <CustomInputFieldProfile
                  id="oldPassword"
                  label="Old Password"
                  value={formik.values.oldPassword}
                  onChange={formik.handleChange('oldPassword')}
                  onBlur={formik.handleBlur('oldPassword')}
                  editable={editable}
                  error={formik.touched.oldPassword && formik.errors.oldPassword}
                  autoComplete="off" // ✅ Prevent autofill
                  name="fake-old-password" // ✅ Trick the browser
                />
              </Box>
              <Box sx={{ paddingBottom: '15px', width: '90%' }}>
                <CustomInputFieldProfile
                  id="newPassword"
                  label="New Password"
                  value={formik.values.newPassword}
                  onChange={formik.handleChange('newPassword')}
                  onBlur={formik.handleBlur('newPassword')}
                  editable={editable}
                  error={formik.touched.newPassword && formik.errors.newPassword}
                  type="password"
                  autoComplete="off" // ✅ Prevent autofill
                  name="fake-new-password" // ✅ Trick the browser
                />
              </Box>
              <Box sx={{ paddingBottom: '15px', width: '90%' }}>
                <CustomInputFieldProfile
                  id="confirmNewPassword"
                  label="Confirm New Password"
                  value={formik.values.confirmNewPassword}
                  onChange={formik.handleChange('confirmNewPassword')}
                  onBlur={formik.handleBlur('confirmNewPassword')}
                  editable={editable}
                  error={formik.touched.confirmNewPassword && formik.errors.confirmNewPassword}
                  autoComplete="off" // ✅ Prevent autofill
                  name="fake-new2-password" // ✅ Trick the browser
                />
              </Box>
            </form>
          </Box>

        {/*{process.env.REACT_APP_SITE_KEY && editable ? (*/}
          {/*  <ReCAPTCHA*/}
          {/*    style={{ marginTop: '10px', marginLeft: '5px' }}*/}
          {/*    ref={recaptcha}*/}
          {/*    sitekey={process.env.REACT_APP_SITE_KEY}*/}
          {/*    onChange={(response) => {*/}
          {/*        formik.setFieldValue('recaptcha', response);*/}
          {/*        setCapticaError(false);*/}
          {/*      }}*/}
          {/*  />*/}
          {/*  ) : null}*/}
          {/*{editable && capticaError && <span style={{ color: 'red', marginLeft: '26px' }}>Please check the box</span>}*/}

        </Stack>
      </Box>
    </Box>
  );
};

export default UserProfile;
