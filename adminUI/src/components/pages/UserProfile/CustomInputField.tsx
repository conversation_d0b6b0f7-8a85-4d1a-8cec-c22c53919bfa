
import {Visibility, VisibilityOff} from '@mui/icons-material';
import { FormControl, IconButton, Input, InputAdornment, Typography } from '@mui/material';
import React, { useState } from 'react';

interface InputFieldProps {
    id: string;
    label: string;
    padding?: string;
    value?: string;
    editable?: boolean;
    error?: any;
    onChange?: (value: string) => void;
    onBlur?: () => void;
    maxLength?: number;
  }
  
  interface CustomInputFieldProps extends InputFieldProps {
    editable: boolean;
    pattern?: RegExp;
  }

const CustomInputFieldProfile: React.FC<CustomInputFieldProps> = ({
    id,
    label,
    padding = '12px 24px 11px 24px',
    value = '',
    onChange,
    onBlur,
    editable = false,
    error,
    pattern,
    maxLength,
  }) => {
    const [showPassword, setShowPassword] = useState(true);
  
    const handleInputChange = (e: { target: { value: any } }) => {
      const newValue = e.target.value;
      if ((!pattern || pattern.test(newValue)) && (!maxLength || newValue.length <= maxLength)) {
        onChange?.(newValue);
      }
    };
  
    const handleTogglePasswordVisibility = () => {
      setShowPassword((prevShowPassword: any) => !prevShowPassword);
    };
  
    const isPasswordField = id.includes('oldPassword') || id.includes('confirmNewPassword')|| id.includes('newPassword');
  
    return (
      <FormControl variant="standard" sx={{ width: '100%' }}>
        <Input
          sx={(theme) => ({
                borderRadius: '100px',
                border: theme?.customProperties.border,
                backgroundColor: theme?.customProperties.textFieldBackground,
                color: theme?.customProperties.color,
                padding: padding,
                fontSize: '14px',
                fontWeight: '400',
            })}
          id={id}
          value={value}
          onChange={handleInputChange}
          onBlur={onBlur}
          disableUnderline
          disabled={!editable}
          readOnly={!editable}
          error={Boolean(error)}
          type={isPasswordField && showPassword ? 'password' : 'text'}
          startAdornment={
            <InputAdornment position="start">
              <Typography
                variant="body1"
                sx={(theme) => ({
                        fontWeight: '700',
                        textShadow: 'none',
                        color: theme?.customProperties.labelColor,
                        fontSize: '14px',
                    })}
              >
                {label}:
              </Typography>
            </InputAdornment>
          }
          endAdornment={
            editable && isPasswordField && (
              <InputAdornment position="end">
                <IconButton onClick={handleTogglePasswordVisibility} edge="end">
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            )
          }
        />
        {editable && error &&
        <span style={{
              backgroundColor: 'red !important',
              color: 'red',
              marginLeft: '26px',
              fontSize: '12px',
            }}
        >{error}
        </span>}
      </FormControl>
    );
  };
export default CustomInputFieldProfile;