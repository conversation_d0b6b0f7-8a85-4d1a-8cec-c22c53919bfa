import { useEffect, useState } from "react";
import { Box } from "@mui/material";

import communicationPageServices from "../../../service/apiCalls/CommunicationServices";

import TaskCard from "../../design/TaskCard";
import { cardStyles } from "../../design/Styles";
import Loader from "../../design/Loader";
import {appActions} from "../../../reducers/appSlice";
import {useDispatch} from "react-redux";

const CommunicationCards = () => {
  const dispatch = useDispatch();
  const [cardsData, setCardsData] = useState([
    { label: "Recipients", value: "0", percentage: "", color: "#E5ECF6" },
    { label: "Unsubscribed", value: "0", percentage: "", color: "#E6F6E5" },
    { label: "Open rate", value: "0", percentage: "", color: "#FFEFD6" },
    { label: "Click rate", value: "0", percentage: "", color: "#F4E3FF" },
    { label: "Bounce rate", value: "0", percentage: "", color: "#FFE3E3" },
  ]);
  const [loaded, setLoaded] = useState(false);

  const fetchStatus = async () => {
    try {
      const response = await communicationPageServices.getEmailOverview();
      if (response) {
        setCardsData([
          { label: "Recipients", value: response.recipients, percentage: "", color: "#E5ECF6" },
          { label: "Unsubscribed", value: response.unsubscribed, percentage: "", color: "#E6F6E5" },
          { label: "Open rate", value: `${(response.openRate * 100).toFixed(2)}%`, percentage: "", color: "#FFEFD6" },
          { label: "Click rate", value: `${(response.clickRate * 100).toFixed(2)}%`, percentage: "", color: "#F4E3FF" },
          { label: "Bounce rate", value: `${response.bounceRate}%`, percentage: "", color: "#FFE3E3" },
        ]);
        setLoaded(true);
      }
    } catch (error) {
      dispatch(
        appActions.enqueueSnackbar({
          key: new Date().getTime() + Math.random(),
          //@ts-ignore
          message: error?.response?.data?.msg || "Something went wrong. Please try again.",
          variant: "error",
        }))
    }finally{
      setLoaded(true);
    }
  };

  useEffect(() => {
    fetchStatus();
  }, []);

  return (
    <Box sx={cardStyles.gridContainer}>
      {!loaded ? (
        <Loader />
      ) : (
        cardsData.map((data, index) => (
          <Box key={index} sx={cardStyles.gridItem}>
            <TaskCard {...data} loaded={loaded} />
          </Box>
        ))
      )}
    </Box>
  );
};


export default CommunicationCards;