//@ts-nocheck
//TODO TS ISSUES
import React, {useEffect, useState} from "react";
    import {
  Box,
  Button,
  Stack,
  Typography,
  Modal,
  TextField,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox, Input, CircularProgress, Backdrop,
} from "@mui/material";
    import CustomAppBar from "../../../design/CustomAppBar";
    import GenericTable from "../../../design/GenericTable";

    import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';

    import DatePicker from 'react-datepicker';
    import 'react-datepicker/dist/react-datepicker.css';
    import BillingServices from "../../../../service/apiCalls/BillingServices";
    import billingServices from "../../../../service/apiCalls/BillingServices";
    import CustomInputField from "../../../design/GenericInputField";
import Loader from "../../../design/Loader";
import NoRecordsMessage from "../../../design/NoRecordsMessage";
import {appActions} from "../../../../reducers/appSlice";
import {useDispatch} from "react-redux";
  const columns = [
      { label: "Promo Name", field: "promoName", sorting: true },
      { label: "Code", field: "code", sorting: true },
      { label: "Status", field: "status", sorting: true },
      { label: "Date of Creation", field: "date", sorting: true },
      { label: "Redemption", field: "redemption", sorting: true },
      { label: "Amount", field: "amount", sorting: true },
    ];

    const data = [
      {
        promoName: "Welcome Offer",
        code: <span style={{ display: "inline-block", background: "#E0E0E0", padding: "4px 8px", borderRadius: "12px" }}>Hello123</span>,
        status: "Active",
        date: "01/06/2025",
        redemption: 2356,
        amount: "15%",
      },
      {
        promoName: "Black Friday",
        code: <span style={{ display: "inline-block", background: "#E0E0E0", padding: "4px 8px", borderRadius: "12px" }}>BlackFriday</span>,
        status: "Active",
        date: "01/22/2025",
        redemption: 1456,
        amount: "50%",
      },
      {
        promoName: "PROMO",
        code: <span style={{ display: "inline-block", background: "#E0E0E0", padding: "4px 8px", borderRadius: "12px" }}>Promo123</span>,
        status: "Expired",
        date: "01/06/2025",
        redemption: 2356,
        amount: "15%",
      },
    ];
    const styles = {
      container: {
        display: 'flex',
        alignItems: 'center',
        border: '1px solid #ccc',
        borderRadius: '5px',
        padding: '5px 10px',
        width: '360px',
        justifyContent: 'space-between',
      },
      datePickerContainer: {
        flex: 1,
        margin:'0px'
      },
      input: {
        width: '100%',
        border: 'none',
        outline: 'none',
        padding: '5px',
        fontSize: '14px',
        borderRadius: '5px',
        backgroundColor: 'transparent',
        textAlign: 'center',

      },
      disabledInput: {
        color: 'red',
      },
      arrow: {
        fontSize: '18px',
        margin: '0 10px',
        color: '#888',
      },
    };

    const Discount = () => {

      const dispatch = useDispatch();
      const [modalOpen, setModalOpen] = useState(false);
      const [startDate,setStartDate] = useState('');
      const [endDate,setEndDate] = useState('');
      const [promoName, setPromoName] = useState("");
      const [discountMode, setDiscountMode] = useState("once");
      const [discountValue, setDiscountValue] = useState("");
      const [discountType, setDiscountType] = useState("%");
      const [isEnabled, setIsEnabled] = useState(true);
      const [coupons, setCoupons] = useState<{ id: string; name: string }[]>([]);
      const [loading, setLoading] = useState(false);
      const isFormValid = promoName && discountValue && startDate && endDate;
      const [tableData, setTableData] = useState([]);

      useEffect(() => {
        dispatch(appActions.setBreadcrumb("/discounts"));
      }, [dispatch]);

      useEffect(() => {
        fetchAdminCoupons();
      }, []);

      const fetchAdminCoupons = async () => {
        setLoading(true)
        try {
          const couponsData = await billingServices.getCoupons();
          console.log(couponsData)
          const formattedCoupons = couponsData.map((coupon) => ({
            promoName: coupon.local.name,
            code: (
              <span
                style={{
                  display: "inline-block",
                  background: "#E0E0E0",
                  padding: "4px 8px",
                  borderRadius: "12px",
                }}
              >
          {coupon.local.stripe_coupon_id}
        </span>
            ),
            status: coupon.active ? "Active" : "Expired",
            date: new Date(coupon.local.createdAt).toLocaleDateString("en-US"),
            redemption: coupon.times_redeemed,
            amount: coupon.local.percent_off
              ? `${coupon.local.percent_off}%`
              : `$${coupon.local.amount_off}`,
          }));
          setTableData(formattedCoupons);
        } catch (error) {
          dispatch(
            appActions.enqueueSnackbar({
              key: new Date().getTime() + Math.random(),
              message: "Error Occurred Fetching Discounts",
              variant: "error",
            }))
        }finally {
          setLoading(false)
        }
      };

      const handleCreateDiscount = async () => {
        const sanitizedName = promoName.trim().replace(/\s+/g, ' ');

        const discountData = {
          name: sanitizedName ,
          duration: discountMode,
          active: isEnabled,
        //  redeem_by: endDate ? Math.floor(endDate.getTime() / 1000) : undefined,
          metadata: {
            startDate: startDate ? startDate.toISOString() : null,
            endDate: endDate ? endDate.toISOString() : null,
          },
        };

        if (discountType === "%") {
          discountData.percent_off = parseInt(discountValue, 10);
        } else {
          discountData.amount_off = parseInt(discountValue, 10);
        }

        setLoading(true)
        setModalOpen(false);
        try {
          await BillingServices.createDiscount(discountData);
          dispatch(
            appActions.enqueueSnackbar({
              key: new Date().getTime() + Math.random(),
              message: "Coupon Created Successfully",
              variant: "success",
            }))

        } catch (error) {
          dispatch(
            appActions.enqueueSnackbar({
              key: new Date().getTime() + Math.random(),
              message: error?.response?.data?.msg || "Something went wrong. Please try again.",
              variant: "error",
            }))
        }
        finally{
          setLoading(false)
          fetchAdminCoupons()
        }
      };

      const handlePromoNameChange = (e) => {
        let value = e;
        value = value.replace(/[^a-zA-Z0-9-_@ ]/g, "");
        setPromoName(value);
      };

      const handleDiscountValueChange = (e) => {
        let value = e.replace(/\D/g, "");
        if (discountType === "%" && Number(value) > 100) {
          value = "100";
        }
        setDiscountValue(value);
      };

      return (
        <Box>
          <Backdrop
            sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
            open={loading}
          >
            <CircularProgress color="inherit" />
          </Backdrop>
          <Stack>
            <Typography sx={{ fontSize: '14px', fontWeight: '600', mb: '8px' }}>Discounts</Typography>
            <CustomAppBar
              rightContent={
                <Button variant="contained" onClick={() => setModalOpen(true)}>
                  Create Discount
                </Button>
              }
            />
          </Stack>

          {tableData.length === 0 && !loading && (
            <NoRecordsMessage message="No Coupons Found" />
          )}

          {tableData.length !== 0 && (
            <GenericTable
              columns={columns}
              data={tableData}
              sortBy="date"
              sortOrder="asc"
              onSort={(field, order) => console.log(`Sorting by ${field} in ${order} order`)}
            />
          )}
          <Modal sx={{ padding: 3, background: "white", borderRadius: 2, margin: "auto", marginTop: "5%"}} open={modalOpen} onClose={() => setModalOpen(false)}>
            <Box sx={{ display:'flex', flexDirection: 'column', width: 750, padding: 3, background: "white", borderRadius: 2, margin: "auto", marginTop: "5%" }}>
              <Typography variant="h6" sx={{ mb: 2 , fontWeight:'700', fontSize:'16px'}}>Create Discount</Typography>
             <Stack sx={{display:'flex', flexDirection: 'row',justifyContent:'space-between', alignItems: 'center', gap:'15px', height:'72px'}}>
               <Stack sx={{width:'55%'}}>
                 <Typography sx={{fontWeight:'600', fontSize:'14px', mb:'8px'}}>
                   Promotion Name
                 </Typography>
                 <CustomInputField editable={true}   placeholder={'Enter Promotion Name'}   id={'promotionName'} value={promoName} onChange={handlePromoNameChange}  />
               </Stack>
              <Stack justifyContent={'flex-start'} sx={{width:'45%'}}>
                <Typography sx={{fontWeight:'600', fontSize:'14px', mb:'8px'}}>
                  Validity
                </Typography>
                <Box display="flex" justifyContent="space-between" alignItems="center" gap="5px">
                  <Typography variant="h6" alignContent="center" fontWeight="600" >
                    From
                  </Typography>

                  <div style={styles.container}>
                    <div style={styles.datePickerContainer}>
                      <DatePicker
                        selected={startDate}
                        onChange={setStartDate}
                        selectsStart
                        startDate={startDate}
                        endDate={endDate}
                        placeholderText="Start Date"
                        dateFormat="dd/MM/yyyy"
                        minDate={new Date()}
                        customInput={<input style={styles.input} placeholder="Start Date" />}
                      />
                    </div>
                    <span style={styles.arrow}>→</span>
                    <div style={styles.datePickerContainer}>
                      <DatePicker
                        selected={endDate}
                        onChange={setEndDate}
                        selectsEnd
                        startDate={startDate}
                        endDate={endDate}
                        placeholderText="End Date"
                        dateFormat="dd/MM/yyyy"
                        minDate={startDate || new Date()} // Ensures end date is not before start date
                        customInput={<input style={styles.input} placeholder="End Date" />}
                      />
                    </div>
                    <CalendarMonthIcon style={styles.calendarIcon} />
                  </div>
                </Box>
              </Stack>
             </Stack>
              <Stack sx={{display:'flex', flexDirection: 'row', alignItems:'flex-start', justifyContent:'space-between', marginTop:'10px'}}>
                <Stack sx={{display:'flex', flexDirection: 'column'}}>
                  <Typography sx={{  fontWeight:'700', fontSize:'14px'}}>Discount Mode</Typography>
                  <RadioGroup value={discountMode} onChange={(e) => setDiscountMode(e.target.value)}>
                    <FormControlLabel value="once" control={<Radio />} label="Once" />
                    <FormControlLabel value="forever" control={<Radio />} label="Forever" />
                  </RadioGroup>
                </Stack>
                <Stack  sx={ {display:'flex', flexDirection:'column'}}>
                  <Typography sx={{  fontWeight:'700', fontSize:'14px', mb:'8px'}}>Discount Value</Typography>
                  <CustomInputField editable={true}   placeholder={'Enter Discount'}   id={'promotionvalue'} value={discountValue} onChange={(e) => handleDiscountValueChange(e)} />

                </Stack>
                <Stack sx={{display:'flex', flexDirection: 'column'}}>
                  <Typography sx={{fontWeight:'700', fontSize:'14px'}} >Discount Type</Typography>
                  <RadioGroup value={discountType} onChange={(e) => setDiscountType(e.target.value)}>
                    <FormControlLabel value="$" control={<Radio />} label="Flat" />
                    <FormControlLabel value="%" control={<Radio />} label="Percentage %" />
                  </RadioGroup>
                </Stack>
                <Stack sx={{display:'flex', flexDirection: 'column'}}>
                  <Typography sx={{ fontWeight:'700', fontSize:'14px'}}>Discount Enabled</Typography>
                  <FormControlLabel control={<Checkbox checked={isEnabled} onChange={(e) => setIsEnabled(e.target.checked)} />} label="Discount Enabled" />
                </Stack>
              </Stack>
              <Stack direction="row" spacing={2} justifyContent="flex-end" sx={{ mt: 2 }}>
                <Button variant="outlined" onClick={() => setModalOpen(false)}>Close</Button>
                <Button variant="contained" disabled={!isFormValid} onClick={handleCreateDiscount}>Create</Button>
              </Stack>
            </Box>
          </Modal>
        </Box>
      );
    };

    export default Discount;
