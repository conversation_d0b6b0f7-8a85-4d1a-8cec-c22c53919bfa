import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";

import { <PERSON>, But<PERSON>, Stack, Typography, Toolbar } from "@mui/material";
import { Pagination } from "@mui/lab";
import SearchIcon from "@mui/icons-material/Search";

import { appActions } from "../../../../reducers/appSlice";
import OwnerListServices from "../../../../service/apiCalls/OwnerListServices";

import BillingCards from "./BillingCards";
import ApplyDiscountModal from "./AccountDiscountModal";
import CustomAppBar from "../../../design/CustomAppBar";
import GenericTable from "../../../design/GenericTable";
import { Search, SearchIconWrapper, StyledInputBase } from "../../../design/StyledComponents";
import Loader from "../../../design/Loader";
import NoRecordsMessage from "../../../design/NoRecordsMessage";

interface BillingData {
  id: number;
  first_name: string;
  last_name: string;
  created_at: string;
  billing_cycle?: string;
  payment_method?: string;
  invoice_status?: string;
  total_amount?: number;
  amount_due?: number;
}


const Billing = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [modalOpen, setModalOpen] = useState(false);
  const [data, setData] = useState<BillingData[]>([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchStr, setSearchStr] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    dispatch(appActions.setBreadcrumb("/billing"));
  }, [dispatch]);

  useEffect(() => {
    fetchData();
  }, [page, searchStr]);

  const fetchData = async () => {
    if (searchStr.trim() && searchStr.length < 3) {
      return;
    }
    setLoading(true)
    try {
      const response = await OwnerListServices.getUsers(page, searchStr);
      setData(response.list as BillingData[]);
      console.log(response);
      const pages = Math.ceil(response.totalRecords / response.recordsPerPage);
      setTotalPages(pages);
    } catch (error) {
      dispatch(
        appActions.enqueueSnackbar({
          key: new Date().getTime() + Math.random(),
          message: "Error Occurred Fetching List",
          variant: "error",
        }))
    }
    finally{
      setLoading(false)
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchStr(e.target.value);
    setPage(1);
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  const columns = [
    { label: "User Name", field: "userName" },
    { label: "Billing Cycle", field: "billingCycle" },
    { label: "Payment Method", field: "paymentMethod" },
    { label: "Date", field: "date" },
    { label: "Invoice Status", field: "status" },
    { label: "Total", field: "total" },
    { label: "Amount Due", field: "amountDue" },
  ];

  const handleRowClick = (row: BillingData & { userName: string; date: string }) => {
    navigate(`/billings/billing/${row.id}`, {
      state: { name: row.userName, id: row.id }
    });
  };

  return (
    <Box>
      <BillingCards />
      <Typography sx={{ fontWeight: "600", fontSize: "14px", lineHeight: "20px", marginTop: "24px" }}>
        Billings
      </Typography>

        <Box>
          <Stack sx={{ marginTop: "8px" }}>
            <CustomAppBar
              rightContent={
                <Button variant="contained" onClick={() => setModalOpen(true)}>
                  Apply Discount
                </Button>
              }
              leftContent={
                <Toolbar sx={{ pl: { sm: 2 }, pr: { xs: 1, sm: 1 }, borderRadius: "8px" }}>
                  <Search>
                    <SearchIconWrapper>
                      <SearchIcon />
                    </SearchIconWrapper>
                    <StyledInputBase
                      placeholder="Search…"
                      inputProps={{ "aria-label": "search" }}
                      value={searchStr}
                      onChange={handleSearch}
                    />
                  </Search>
                </Toolbar>
              }
            />
          </Stack>

          <Box sx={{ marginTop: "16px" }}>
            {loading ? (
              <Box sx={{ height: "60px", display: "flex", justifyContent: "center", alignItems: "center" }}>
                <Loader />
              </Box>
            ) : data.length === 0 ? (
              <NoRecordsMessage message="No Records Found" />
            ) : (
              <>
                <GenericTable
                  columns={columns}
                  data={data.map((item) => ({
                    id: item.id,
                    userName: `${item.first_name ?? "N/A"} ${item.last_name ?? "N/A"}`,
                    billingCycle: item.billing_cycle ?? "N/A",
                    paymentMethod: item.payment_method ?? "N/A",
                    date: item.created_at ? new Date(item.created_at).toLocaleDateString() : "N/A",
                    status: item.invoice_status ?? "N/A",
                    total: item.total_amount ?? "N/A",
                    amountDue: item.amount_due ?? "N/A",
                  }))}
                  //@ts-ignore
                  onRowClick={handleRowClick}
                />
                <Pagination
                  count={totalPages}
                  page={page}
                  onChange={handlePageChange}
                  sx={{ marginTop: "16px", display: "flex", justifyContent: "center" }}
                />
              </>
            )}
          </Box>
        </Box>
      <ApplyDiscountModal open={modalOpen} handleClose={() => setModalOpen(false)} />
    </Box>
  );
};

export default Billing;
