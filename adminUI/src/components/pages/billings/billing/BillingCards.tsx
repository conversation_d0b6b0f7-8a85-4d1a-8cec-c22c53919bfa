import { Box, Typography } from "@mui/material";
import TaskCard from "../../../design/TaskCard";
import React, { useEffect, useState } from "react";
import Loader from "../../../design/Loader";
import { cardStyles } from "../../../design/Styles";
import DropDown from "../../../design/DropDown";
import billingServices from "../../../../service/apiCalls/BillingServices";
import {appActions} from "../../../../reducers/appSlice";
import {useAppDispatch} from "../../../../app/hooks";

const options = ["TODAY", "THIS_MONTH", "THIS_YEAR", "ALL"];

const BillingCards = () => {
  const dispatch = useAppDispatch();
  const [loaded, setLoaded] = useState(false);
  const [selectedOption, setSelectedOption] = useState(options[0]);
  const [cardsData, setCardsData] = useState( [
    { label: "Total Revenue", value: "0", percentage: "", color: "#E5ECF6" },
    { label: "Recurring Revenue", value: "3126", percentage: "", color: "#E6F6E5" },
    { label: "Renewal Rate", value: "0", percentage: "", color: "#FFEFD6" },
    { label: "% Outstanding Invoices", value: "0", percentage: "", color: "#F4E3FF" },
    { label: "Churn Rate %", value: "0", percentage: "", color: "#FFE3E3" },
  ]);

  const fetchCardStatus = async () => {
    setLoaded(false);
    try {
      const response = await billingServices.getBillingStats(selectedOption);

      // Map API response to card format
      const formattedData = [
        { label: "Total Revenue", value: response.totalRevenue, percentage: "", color: "#E5ECF6" },
        { label: "Recurring Revenue", value: response.recurringRevenue, percentage: "", color: "#E6F6E5" },
        { label: "Renewal Rate", value: response.renewalRate, percentage: "", color: "#FFEFD6" },
        { label: "% Outstanding Invoices", value: response.outstandingInvoices, percentage: "", color: "#F4E3FF" },
        { label: "Churn Rate %", value: response.churnRate, percentage: "", color: "#FFE3E3" },

      ];
      //@ts-ignore
      setCardsData(formattedData);
    } catch (error) {
      dispatch(
        appActions.enqueueSnackbar({
          key: new Date().getTime() + Math.random(),
          //@ts-ignore
          message: error?.response?.data?.msg || "Something went wrong. Please try again.",
          variant: "error",
        }))
    } finally {
      setLoaded(true);
    }
  };

  useEffect(() => {
    fetchCardStatus();
  }, [selectedOption]);

  return (
    <Box>
      {!loaded ? (
        <Loader />
      ) : (
        <Box>
            <Typography variant="h6" noWrap component="div" sx={{ fontSize: '14px', fontWeight: '600', mb: '5px' }}>
              {selectedOption}
              <DropDown options={options} selectedOption={selectedOption} onSelect={setSelectedOption} />
            </Typography>
          <Box sx={cardStyles.gridContainer}>
            {cardsData.map((data, index) => (
              <Box key={index} sx={cardStyles.gridItem}>
                <TaskCard {...data} loaded={loaded} />
              </Box>
            ))}
          </Box>
        </Box>
      )}
    </Box>

  );
};

export default BillingCards;
