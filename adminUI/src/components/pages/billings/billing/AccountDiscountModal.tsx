import React, {useCallback, useEffect, useState} from "react";
import {
  <PERSON>,
  <PERSON>ton,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  MenuItem,
  Select,
  Typography,
  IconButton,
  ListItemText,
  Radio,
  SelectChangeEvent, Backdrop, CircularProgress,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CheckIcon from "@mui/icons-material/Check";
import billingServices from "../../../../service/apiCalls/BillingServices";
import OwnerListServices from "../../../../service/apiCalls/OwnerListServices";
import {appActions} from "../../../../reducers/appSlice";
import {useDispatch} from "react-redux";

const PAGE_SIZE = 10;

const styles = {
  dialogPaper: {
    width: "670px",
    maxWidth: "90vw",
    padding: "24px",
    borderRadius: "8px",
  },
  dialogTitle: {
    marginBottom: "16px",
    borderBottom: "1px solid #E3E3E3",
    textAlign: "left",
    paddingLeft: "0px",
    position: "relative",
  },
  dialogTitleText: {
    fontSize: "16px",
    fontWeight: "600",
  },
  dialogContent: {
    display: "flex",
    flexDirection: "column",
    gap: "10px",
    paddingLeft: "0px",
  },
  applyOnButton: {
    width: "300px",
    borderRadius: "100px",
    padding: "0px 16px",
    backgroundColor: "#F4F4F4",
    color: "#000",
    height: "50px",
    justifyContent: "flex-start",
    border: "1px solid #D1D1D1",
    "&.MuiButton-contained": {
      backgroundColor: "#FFFFFF",
    },
  },
  radio: {
    marginRight: "2px",
    padding: "0px",
    transform: "scale(0.8)",
  },
  selectedDiscountsBox: {
    display: "flex",
    flexWrap: "wrap",
    maxHeight: "80px",
    maxWidth: "60%",
    overflowY: "auto",
    gap: 1,
  },
  actionButtonsBox: {
    display: "flex",
    gap: "10px",
  },
  checkIcon: {
    color: "#000",
    marginRight: "8px",
  },
  emptyBox: {
    width: 24,
    height: 24,
    marginRight: "8px",
    border: "1px solid #000",
    borderRadius: "4px",
  },
  applyOnSectionTitle: {
    fontWeight: "600",
    fontSize: "14px",
  },
  selectUserDropdown: {
    marginTop: "10px",
  },
  selectDiscountDropdown: {
    marginTop: "10px",
  },
  closeIconButton: {
    position: "absolute",
    right: 16,
    top: 16,
  },
};

interface ApplyDiscountModalProps {
  open: boolean;
  handleClose: () => void;
}

const ApplyDiscountModal: React.FC<ApplyDiscountModalProps> = ({ open, handleClose }) => {
  const dispatch = useDispatch();

  const [applyOn, setApplyOn] = useState<"all" | "individual">("all");
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [selectedDiscountName, setSelectedDiscountName] = useState<string>("");

  const [selectedDiscount, setSelectedDiscount] = useState<string>("");

  const [coupons, setCoupons] = useState<{ id: string; name: string }[]>([]);
  const [users, setUsers] = useState([]);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore]   = useState(true);
  const [loading, setLoading] = useState(false);

  const handleUserChange = (event: SelectChangeEvent<string[]>) => {
    console.log(event.target.value);
    setSelectedUsers(event.target.value as string[]);
  };

  const handleDiscountChange = (event: SelectChangeEvent<string>) => {
    const selectedId = event.target.value;
    setSelectedDiscount(selectedId);
    const selectedCoupon = coupons.find((coupon) => coupon.id === selectedId);
    setSelectedDiscountName(selectedCoupon ? selectedCoupon.name : "");
};

  const fetchAdminCoupons = async () => {
    setLoading(true)
    try {
      const couponsData = await billingServices.getCoupons();
      console.log(couponsData)
      const formattedCoupons = couponsData.map((coupon: any) => ({
        id: coupon.local.stripe_coupon_id,
        name: coupon.local.name,
      }));

      console.log(couponsData)
      setCoupons(formattedCoupons);
    } catch (error) {
      appActions.enqueueSnackbar({
        key: new Date().getTime() + Math.random(),
        message: "Error Occurred While Fetching Coupons",
        variant: "error",
      })
    }finally{
      setLoading(false)
    }
  };

  const fetchUsers = useCallback(async (nextPage:number) => {
    if (loading || !hasMore) return;
    setLoading(true);
    try {
      const response = await OwnerListServices.getUsers(nextPage);
      const list = response.list || [];
      console.log(response)
      setUsers(prev => nextPage === 1 ? list : [...prev, ...list]);
      setHasMore(response.totalRecords > ((PAGE_SIZE * (nextPage - 1)) + list.length ) );
      setPage(nextPage);
    } catch (e) {
      console.error(e);
      dispatch(appActions.enqueueSnackbar({
        key: Date.now(),
        message: "Error fetching users",
        variant: "error",
      }));
    } finally {
      setLoading(false);
    }
  }, [hasMore, loading, dispatch]);

  useEffect(() => {
    fetchAdminCoupons();
  }, []);

  // only load page 1 on mount
  useEffect(() => {
    fetchUsers(1);
  }, []);

  const applyDiscount = async () => {
    if (!selectedDiscount) return;
    const payload: {
      coupon_id: string;
      scope: string;
      user_ids?: string[];
    } = {
      coupon_id: selectedDiscount.toString(),
      scope: applyOn === "all" ? "all" : "selected",
    };
    if (selectedUsers.length && applyOn !== 'all') {
      payload.user_ids = selectedUsers.map((user: any) => user.id);
    }
    console.log(payload)
    setLoading(true);
    try {
      await billingServices.applyCoupon(payload);
      dispatch(
        appActions.enqueueSnackbar({
          key: new Date().getTime() + Math.random(),
          message: "Discount Applied Successfully",
          variant: "success",
        }))
      handleClose();
    } catch (error) {
      dispatch(
        appActions.enqueueSnackbar({
          key: new Date().getTime() + Math.random(),
          message: "Error Occurred While Applying Discount",
          variant: "error",
        }))
    }finally {
      setLoading(false)
    }
  };

  // scroll handler on the **list**
  const handleScroll = useCallback((e: React.UIEvent<HTMLElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollTop + clientHeight >= scrollHeight - 1 && hasMore && !loading) {
      fetchUsers(page + 1);
    }
  }, [hasMore, loading, page, fetchUsers]);

  // // Infinite scroll handler: When scroll reaches the bottom, load more users
  // const handleScroll = useCallback(async (event: React.UIEvent<HTMLUListElement>) => {
  //   const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
  //   // Check if scrolled to bottom (with a small tolerance)
  //   if (scrollHeight - scrollTop <= clientHeight + 1) {
  //     // Load more users
  //     const nextPage = page + 1;
  //     // debugger;
  //     const moreUsers = await fetchUsers(nextPage);
  //   }
  // }, [page]);

  return (
    <Dialog
      PaperProps={{ sx: styles.dialogPaper }}
      open={open}
      onClose={handleClose}
      maxWidth={"sm"}
      fullWidth
    >
      <Backdrop sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }} open={loading}>
        <CircularProgress color="inherit" />
      </Backdrop>
          <DialogTitle sx={styles.dialogTitle}>
          <Typography sx={styles.dialogTitleText}>Apply Discount</Typography>
          <IconButton onClick={handleClose} sx={styles.closeIconButton}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
          <DialogContent sx={styles.dialogContent}>
            {/* Apply On Section */}
            <Typography variant="subtitle1" sx={styles.applyOnSectionTitle}>
              Apply On
            </Typography>
            <Box sx={{ display: "flex", gap: "12px" }}>
              <Button
                variant={applyOn === "all" ? "contained" : "outlined"}
                onClick={() => setApplyOn("all")}
                sx={styles.applyOnButton}
              >
                <Radio checked={applyOn === "all"} sx={styles.radio} />
                All Users
              </Button>

              <Button
                variant={applyOn === "individual" ? "contained" : "outlined"}
                onClick={() => setApplyOn("individual")}
                sx={styles.applyOnButton}
              >
                <Radio checked={applyOn === "individual"} sx={styles.radio} />
                Individual Users
              </Button>
            </Box>

            {/* Select User Dropdown (Only for "Individual Users") */}
            {applyOn === "individual" && (
              <>
                <Typography variant="subtitle1" sx={styles.selectUserDropdown}>
                  Select User
                </Typography>
                <FormControl fullWidth>
                  <Select
                    multiple
                    value={selectedUsers}
                    onChange={handleUserChange}
                    displayEmpty
                    // Attach the scroll handler via MenuProps
                    MenuProps={{
                      // onScroll: handleScroll,
                      PaperProps: {
                        //@ts-ignore
                        onScroll: handleScroll,
                        style: {
                          // maxHeight: ITEM_HEIGHT * 4.5, // for example, 4.5 items height
                          maxHeight: 48 * 4.5, // for example, 4.5 items height
                        },
                      },
                      // MenuListProps passes the onScroll handler to the internal menu list
                      MenuListProps: {
                        onScroll: handleScroll,
                      },
                    }}
                    renderValue={(selected) => (selected.length ? selected.map((sUser: any) => `${sUser.first_name} ${sUser.last_name}`).join(", ") : "Select User")}
                  >
                    {users.map((user: any) => (
                      <MenuItem key={`discount-users-${user.id}`} value={user}>
                        {selectedUsers.find((sUser: any) => sUser.id === user.id) ? (
                          <CheckIcon sx={styles.checkIcon} />
                        ) : (
                          <Box sx={styles.emptyBox} />
                        )}
                        <ListItemText primary={user.first_name + ' ' + user.last_name} />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </>
            )}

            {/* Select Discount Dropdown */}
            <Typography variant="subtitle1" sx={styles.selectDiscountDropdown}>
              Select Discount
            </Typography>
            <FormControl fullWidth>
              <Select
                value={selectedDiscount}
                onChange={handleDiscountChange}
                displayEmpty
              >
                <MenuItem value="">Select Discount</MenuItem>
                {coupons.map((coupon) => (
                  <MenuItem key={coupon.id} value={coupon.id}>
                    {coupon.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </DialogContent>

          <DialogActions sx={{ justifyContent: "space-between" }}>
            {/* Selected Discount */}
            <Box sx={styles.selectedDiscountsBox}>
              {selectedDiscount && (
                <Chip
                  label={selectedDiscountName}
                  onDelete={() => {
                    setSelectedDiscount("");
                    setSelectedDiscountName("");
                  }}
                />
              )}
            </Box>

            {/* Action Buttons */}
            <Box sx={styles.actionButtonsBox}>
              <Button onClick={handleClose}>Close</Button>
              <Button
                variant="contained"
                color="primary"
                disabled={
                  (applyOn === "individual" && (selectedUsers.length === 0 || !selectedDiscount)) ||
                  (applyOn === "all" && !selectedDiscount)
                }
                onClick={applyDiscount}
              >
                Apply
              </Button>

            </Box>
          </DialogActions>
    </Dialog>
  );
};

export default ApplyDiscountModal;