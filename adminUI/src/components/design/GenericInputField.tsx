// @ts-nocheck
// TO-DO FIX TS ERRORS
import React from 'react';
import { FormControl, Input, InputAdornment, Typography } from '@mui/material';

interface InputFieldProps {
    id: string;
    label?: string;
    padding?: string;
    value?: string;
    error?: any;
    placeholder?: any;
    onChange?: (value: string) => void;
}

interface CustomInputFieldProps extends InputFieldProps {
    editable: boolean;
    labelWeight?: string | number;  // Add the labelWeight prop
}

const CustomInputField: React.FC<CustomInputFieldProps> = ({
    id,
    label,
    padding = '12px 24px 11px 24px',
    value = '',
    onChange,
    editable = false,
    error,
    labelWeight,
    placeholder = '',  
}) => (
    <FormControl variant="standard" sx={{ width: '100%' }}>
        <Input
            sx={(theme) => ({
                borderRadius: '100px',
                border: !error ? theme?.customProperties.border : '1px solid red',
                backgroundColor: theme?.customProperties.textFieldBackground,
                color: theme?.customProperties.color,
                padding: padding,
                fontSize: '14px',
                fontWeight: '400',
            })}
            id={id}
            value={value}
            onChange={(e) => onChange && onChange(e.target.value)}
            disableUnderline
            placeholder={placeholder}
            disabled={!editable}
            error={true}
            startAdornment={
              label ? (
                <InputAdornment position="start">
                  <Typography
                    variant="body1"
                    sx={(theme) => ({
                      fontWeight: labelWeight || "700",
                      textShadow: "none",
                      color: theme?.customProperties?.labelColor,
                      fontSize: "14px",
                    })}
                  >
                    {label}
                  </Typography>
                </InputAdornment>
              ) : null
            }
        />
        {error &&
            <span style={{
                marginLeft: '26px',
                color: 'red',
                fontSize: '12px',
            }}
            >{error}
            </span>}
    </FormControl>
);

export default CustomInputField;
