import React from 'react';
import { Card, CardContent, Typography } from '@mui/material';

interface TaskCardProps {
  label: string;
  value: number | string;
  percentage: string;
  color: string;
  loaded: boolean;
}

const styles: Record<string, React.CSSProperties> = {
  card: {
    padding: '20px',
    borderRadius: '16px',
    boxShadow: 'none',
    height: '140px',
    minWidth: '231px'
  },
  label: {
    fontWeight: 600,
  },
  value: {
    fontWeight: 700,
  },
  percentage: {
    fontWeight: 400,
    fontSize: '12px',
  },
};

const TaskCard: React.FC<TaskCardProps> = ({ label, value, percentage, color, loaded }) => {
  return (
    <Card sx={{ ...styles.card, backgroundColor: color }}>
      <CardContent sx={{ padding: 0 }}>
        <Typography variant="h5" gutterBottom sx={styles.label}>
          {label}
        </Typography>
        <Typography variant="h2" sx={styles.value}>
          {loaded ? value : 0}
        </Typography>
        <Typography variant="h2" sx={styles.percentage}>
          {loaded ? percentage : 0}
        </Typography>
      </CardContent>
    </Card>
  );
};

export default TaskCard;
