import { Card, CardContent, Stack, Typography, useTheme, Theme } from '@mui/material';
import { SxProps } from '@mui/system';

interface ProfileCardProps {
  title: string;
  value: number | string;
  unit?: string;
  bgColor: string;
}

const styles: { [key: string]: SxProps<Theme> } = {
  card: {
    borderRadius: '12px',
    boxShadow: 'none',
    height: '140px',
    padding: '20px',
    minWidth: '231px'
  },
  cardContent: {
    padding: '0px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    height: '100%'
  },
  valueText: {
    fontSize: '28px'
  },
  unitText: {
    fontSize: '0.6em',
    fontWeight: 400,
    ml: 0.5
  }
};

const ProfileCard = ({ title, value, unit, bgColor }: ProfileCardProps) => {
  const theme = useTheme();

  return (
    <Card sx={{ ...styles.card, backgroundColor: bgColor }}>
      <CardContent sx={styles.cardContent}>
        <Typography variant="h6">{title}</Typography>
        <Typography variant="h4" sx={styles.valueText}>
          {value}
          {unit && (
            <Typography
              component="span"
              sx={{ ...styles.unitText, color: theme.palette.text.secondary }}
            >
              {unit}
            </Typography>
          )}
        </Typography>
      </CardContent>
    </Card>
  );
};

export default ProfileCard;