import { Box, Typography } from '@mui/material';
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined';
import React from 'react';
import {styles} from "../styled/StyledComponents";

const NoRecordsMessage = ({ message = 'No Records Found' }) => {
  return (
    <Box>
      <Box sx={{ borderBottom: 'none' }}  >
        <Box sx={styles.noRecordsBox}>
          <DescriptionOutlinedIcon sx={styles.descriptionIcon} fontSize="large" />
          <Typography variant="h6">{message}</Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default NoRecordsMessage;