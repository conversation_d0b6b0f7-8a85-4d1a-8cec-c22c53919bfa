//@ts-nocheck
//TODO-Update the adapter dayjs to latest version
import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { DatePicker } from '@mui/x-date-pickers'; 
  
interface GenericDateInputProps {
  label: string;
  value: any;
  onChange: (newValue: any) => void;
  editable: boolean;
  type: 'date' | 'datetime';
  disableFuture?: boolean;
  isRequired?: boolean;
  color?: string;
  labelBold?: boolean;
}
const sx = (theme:any) => ({
  width: '100%',
  '& .MuiInputBase-input': {
    border: 'none',
  },
  '& .MuiInputBase-input.Mui-disabled': {
    color:theme?.customProperties.color,
  },
  '& .MuiInput-root.Mui-disabled:before ':{
    
    color:theme?.customProperties.color,
    borderBottomStyle:'none',
    BorderBottom:'none',
  },
  '& .MuiInput-root::before': {
    borderBottom: 'none', // Remove bottom border
  },
 });
const GenericDateInput: React.FC<GenericDateInputProps> = ({
  label,
  value,
  onChange,
  editable,
  type,
  disableFuture,
  isRequired,
  color,
  labelBold,
}) => {
  const [error, setError] = useState('');
  return (
    <Box>
      <Box
        sx={(theme) => ({
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'center',
          borderRadius: '100px',
          border: theme?.customProperties.border,
          backgroundColor: theme?.customProperties.textFieldBackground,
          padding: '12px 24px 11px 24px',
          fontSize: '14px',
          fontWeight: labelBold ? '700' : '500',
          fontFamily: 'GilroyMedium',
        })}
      >
        <Typography
          sx={(theme) => ({
            fontWeight: labelBold ? '700' : '500',
            fontSize: '14px',
            paddingRight: '5px',
            display: 'inline-block',
            whiteSpace: 'nowrap',
            fontFamily: 'GilroyMedium',
          })}
        >
          {label}
        </Typography>

        <LocalizationProvider
          //@ts-ignore
          dateAdapter={AdapterDayjs}>
          {type === 'date' ? (
            <DatePicker
              sx={sx}
              autoFocus={false}
              value={value}
              disabled={!editable}
              onChange={onChange}
              disableFuture={disableFuture}
              onError={(e : any) => setError(e)}
              //@ts-ignore
              slotProps={{ textField: { variant: 'standard', error: error, InputProps:{ disableUnderline: true } } }}
            />
          ) : (
            <DateTimePicker
              sx={sx}
              autoFocus={false}
              value={value}
              disabled={!editable}
              onChange={onChange}
              ampm={false}
              disableFuture={disableFuture}
              format="DD/MM/YY | HH:mm"
              //@ts-ignore
              slotProps={{ textField: { variant: 'standard', error: error, InputProps:{ disableUnderline: true  } } }}
            />
          )}
        </LocalizationProvider>
      </Box>
      {(error || isRequired) && (
        <Typography
          variant="body1"
          sx={(theme) => ({
            paddingLeft: theme.spacing(2),
            fontSize: '12px',
            color: 'red',
          })}
        >
          Please Select a Valid Date
        </Typography>
      )}
    </Box>
  );
};

export default GenericDateInput;
