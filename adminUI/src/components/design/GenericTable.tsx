import React from "react";
import {
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    TableSortLabel,
    Checkbox,
    useTheme,
} from "@mui/material";

interface Column {
    label: string;
    field: string;
    sorting?: boolean;
}

interface Row {
    [key: string]: any;
}

interface GenericTableProps {
    columns: Column[];
    data: Row[];
    multipleValues?: boolean;
    headingBackground?: boolean;
    onRowClick?: (row: Row) => void;
    sortBy?: string | null;
    sortOrder?: "asc" | "desc";
    onSort?: (field: string, order: "asc" | "desc") => void;
    enableCheckboxSelection?: boolean;
    selectedRows?: string[];
    onSelectRow?: (id: string) => void;
    onSelectAll?: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const GenericTable: React.FC<GenericTableProps> = ({
                                                       columns,
                                                       data,
                                                       multipleValues = false,
                                                       headingBackground = false,
                                                       onRowClick,
                                                       sortBy,
                                                       sortOrder = "asc",
                                                       onSort,
                                                       enableCheckboxSelection = false,
                                                       selectedRows = [],
                                                       onSelectRow,
                                                       onSelectAll,
                                                   }) => {
    const theme = useTheme();

    const STATUS = {
        ACTIVE: "Active",
        IN_ACTIVE: "Inactive",
        COMPLETE: "completed",
        IN_PROGRESS: "in_progress",
        PAID: "paid",
        RESOLVED: "Resolved",
        NA: "N/A",
        READ: "read",
        UNREAD: "unread",
    };

    const renderStatus = (status: string) => {
        switch (status) {
            case STATUS.ACTIVE:
                return <span className="dot status-Active">Active</span>;
            case STATUS.READ:
                return <span className="dot status-Active">Read</span>;
            case STATUS.PAID:
                return <span className="dot status-Active">Paid</span>;
            case STATUS.IN_ACTIVE:
                return <span className="dot status-InActive">Inactive</span>;
            case STATUS.UNREAD:
                return <span className="dot status-InActive">Unread</span>;
            case STATUS.COMPLETE:
                return <span className="dot status-complete">Complete</span>;
            case STATUS.IN_PROGRESS:
                return <span className="dot status-in-progress">In Progress</span>;
            case STATUS.RESOLVED:
                return <span className="dot status-Resolved">Resolved</span>;
            case STATUS.NA:
                return <span className="dot status-Resolved">N/A</span>;
            default:
                return <span>Unknown Status</span>;
        }
    };

    const handleSort = (field: string) => {
        if (!onSort) return;
        const isCurrentField = sortBy === field;
        const newOrder = isCurrentField ? (sortOrder === "asc" ? "desc" : "asc") : "asc";
        onSort(field, newOrder);
    };

    return (
      <TableContainer component={Paper} style={{ boxShadow: "none" }}>
          <Table>
              <TableHead>
                  <TableRow>
                      {enableCheckboxSelection && (
                        <TableCell>
                            <Checkbox
                              onChange={onSelectAll}
                              checked={selectedRows.length === data.length}
                              indeterminate={selectedRows.length > 0 && selectedRows.length < data.length}
                              sx={{
                                  color: "#4A4A4A",
                                  "&.Mui-checked": { color: "#1976d2" },
                                  "&.MuiCheckbox-indeterminate": { color: "#1976d2" },
                              }}
                            />
                        </TableCell>
                      )}
                      {columns.map((column, index) => (
                        <TableCell
                          key={index}
                          align="left"
                          style={{
                              borderBottom: multipleValues || headingBackground
                                ? "none"
                                : `2px solid ${theme.customProperties.genericTableHeaderBorder}`,
                              fontSize: "14px",
                              color: "#A4A4A4",
                              backgroundColor: multipleValues || headingBackground ? "#F8F8FC" : "",
                              borderRadius: multipleValues || headingBackground ? "8px" : "",
                          }}
                        >
                            {column.sorting ? (
                              <TableSortLabel
                                active={sortBy === column.field}
                                direction={sortBy === column.field ? sortOrder : "asc"}
                                onClick={() => handleSort(column.field)}
                              >
                                  {column.label}
                              </TableSortLabel>
                            ) : (
                              column.label
                            )}
                        </TableCell>
                      ))}
                  </TableRow>
              </TableHead>
              <TableBody>
                  {data.map((row, rowIndex) => {
                      const isSelected = selectedRows.includes(row.id);
                      return (
                        <TableRow
                          key={rowIndex}
                          hover
                          onClick={() => onRowClick?.(row)}
                          sx={{
                              backgroundColor: isSelected ? theme.palette.action.selected : "inherit",
                          }}
                        >
                            {enableCheckboxSelection && (
                              <TableCell>
                                  <Checkbox
                                    checked={isSelected}
                                    onChange={() => onSelectRow?.(row.id)}
                                    sx={{
                                        color: "#4A4A4A",
                                        "&.Mui-checked": { color: "#1976d2" },
                                        "&.MuiCheckbox-indeterminate": { color: "#1976d2" },
                                    }}
                                  />
                              </TableCell>
                            )}
                            {columns.map((column, colIndex) => (
                              <TableCell
                                key={colIndex}
                                align="left"
                                style={{
                                    fontSize: "12px",
                                    fontWeight: 400,
                                    padding: "20px 12px",
                                    borderBottom: multipleValues || headingBackground
                                      ? "none"
                                      : "1px solid #ddd",
                                }}
                              >
                                  {column.field === "status" && renderStatus(row[column.field])}
                                  {column.field === "actions" && row.actionsButtons
                                    ? row.actionsButtons
                                    : column.field !== "status" &&
                                    column.field !== "actions" &&
                                    (multipleValues && Array.isArray(row[column.field]) ? (
                                      <>
                                                    <span style={{ fontWeight: "bold" }}>
                                                        {row[column.field][0]}
                                                    </span>
                                          <span
                                            style={{
                                                marginLeft: "8px",
                                                color: row[column.field][1] >= 0 ? "#49BD42" : "red",
                                                fontSize: "13px",
                                            }}
                                          >
                                                        ({row[column.field][1] >= 0 ? "+" : ""}
                                              {row[column.field][1]})
                                                    </span>
                                      </>
                                    ) : (
                                      row[column.field]
                                    ))}
                              </TableCell>
                            ))}
                        </TableRow>
                      );
                  })}
              </TableBody>
          </Table>
      </TableContainer>
    );
};

export default GenericTable;
