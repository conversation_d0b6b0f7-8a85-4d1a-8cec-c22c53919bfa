import { IconButton } from "@mui/material";
import { Menu as MenuIcon } from "@mui/icons-material";

import ForwardArrow from "../../theme/svgs/ForwardArrow.svg";
/**
 * Sidebar Menu Button Props
 */
interface SidebarMenuButtonProps {
  drawerOpen: boolean;
  onClick: () => void;
}

/**
 * Sidebar Menu Button
 */
export const SidebarMenuButton = ({
  drawerOpen,
  onClick,
}: SidebarMenuButtonProps) => (
  <IconButton
    disableRipple
    onClick={onClick}
    aria-label="open drawer"
    style={{ justifyContent: "center", paddingLeft: "0px" }}
    sx={{
      color: "primary.contrastText",

      ...(drawerOpen && { display: "none" }),
    }}
  >
    <img src={ForwardArrow} alt="Forward Arrow" />
  </IconButton>
);
