import React, { useState, useEffect, useMemo } from "react";
import { Link, useNavigate } from "react-router-dom";
import {
  Divider,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  IconButton,
  Stack,
  Collapse,
  List,
} from "@mui/material";
import {
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  ExpandLess,
  ExpandMore,
} from "@mui/icons-material";
import { useTheme } from "@mui/material/styles";

import { Drawer, DrawerHeader } from "../styled/Sidebar.styled";
import { appActions } from "../../reducers/appSlice";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import { usePathname } from "../../hooks";
import { linkItems } from "../../constants";
import ExpandableItemAvatar from "./ExpandableItemAvatar";
import ClinicalpadLogo from "../../theme/svgs/ClinicalPadLogo.svg";
import ClinicalpadLogoDarkMode from "../../theme/svgs/ClinicalPadLogoDarkMode.svg";
import ForwardArrow from "../../theme/svgs/ForwardArrow.svg";
import BackwardArrow from "../../theme/svgs/BackwardArrow.svg";
/**
 * Sidebar Component
 */
const Sidebar = ({ children }: { children: React.ReactNode }) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const theme = useTheme();
  const pathname = usePathname();

  const [openItems, setOpenItems] = useState<Record<number, boolean>>({});
  const [selectedItem, setSelectedItem] = useState<string | null>(pathname);

  const handleToggle = (id: number, nested?: any[]) => {
    setOpenItems((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
    if (nested && nested.length === 2 && !openItems[id]) {
      navigate(nested[0].to);
      setSelectedItem(nested[0].to);
    }
  };

  useEffect(() => {
    setSelectedItem(pathname);  
  }, [pathname]);

  const drawerOpen: boolean = useAppSelector((state) => state.app.drawerOpen);

  const toggleDrawer = () => {
    dispatch(appActions.setDrawerOpen(!drawerOpen));
  };

  const isParentSelected = (nested: any[]) =>
    nested.some((item: any) => item.to === selectedItem);

  return (
    <React.Fragment>
      <Drawer variant="permanent" open={drawerOpen} sx={{  zIndex: (theme) => theme.zIndex.drawer + 1 }} >
        <DrawerHeader
          sx={{
            display: "flex",
            justifyContent: drawerOpen ? "space-between" : "center",
            alignItems: "center !important",
            flexDirection: "row", 
            padding: drawerOpen ? "28px 10px 0px 10px" : "28px 0px 0px 0px",
          
          }}
        >
          {drawerOpen && (
            <Link to="/">
              <img
                width="147"
                height="65px"
                src={
                  theme.palette.mode === "light"
                    ? ClinicalpadLogo
                    : ClinicalpadLogoDarkMode
                }
                alt="Logo"
              />
            </Link>
          )}
          
            <IconButton onClick={toggleDrawer}
              disableRipple
              style={{  padding: "0px" }}
            >
              <img
                src={drawerOpen ? BackwardArrow : ForwardArrow}
                alt={drawerOpen ? "Backward Arrow" : "Forward Arrow"}
              />
            </IconButton>
        
        </DrawerHeader>
        <Stack
          sx={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            height: "100%",
            padding: drawerOpen ? "32px 8px" : "32px 0px",
            paddingBottom: "30px",  
          }}
        >
          <List component="nav" disablePadding>
            {linkItems.map(({ id, name, to, icon, nested }: any) => (
              <React.Fragment key={id}>
                <ListItemButton
                  onClick={() =>
                    nested ? handleToggle(id, nested) : setSelectedItem(to)
                  }
                  component={!nested ? Link : "div"}
                  to={!nested ? to : undefined}
                  selected={
                    selectedItem === to || (nested && isParentSelected(nested))
                  }
                  sx={{
                    margin: "4px",
                    padding: "12px",
                    borderRadius: "8px",
                    display:'flex', 
                    "&.Mui-selected": {
                      backgroundColor:
                        selectedItem === to ||
                        (nested && isParentSelected(nested))
                          ? theme.palette.action.selected
                          : "transparent",
                      "&:hover": {
                        backgroundColor: theme.palette.action.hover,
                      },
                    },
                  }}
                >
                  <ListItemIcon sx={{display:'flex', justifyContent:'center', alignItems:'center'}}> {icon}</ListItemIcon>
                  {drawerOpen && (
                    <>
                      <ListItemText
                        primary={name}
                        sx={{
                          "& .MuiTypography-root": {
                            fontSize: "14px",
                            fontWeight: "400",
                          },
                        }}
                      />{" "}
                      {nested &&
                        (openItems[id] ? <ExpandLess /> : <ExpandMore />)}
                    </>
                  )}
                </ListItemButton>
                {nested && (
                  <Collapse in={openItems[id]} timeout="auto" unmountOnExit>
                    <List component="div" disablePadding>
                      {nested.map(({ name, to, icon }: any) => (
                        <ListItemButton
                          key={to}
                          component={Link}
                          to={to}
                          sx={{
                            pl: 4,
                            margin: "4px",
                            padding: drawerOpen ? "8px 20px" : "12px",
                            borderRadius: "8px",
                            "&.Mui-selected": {
                              backgroundColor:
                                selectedItem === to ||
                                (nested && isParentSelected(nested))
                                  ? theme.palette.action.selected
                                  : "transparent",
                              "&:hover": {
                                backgroundColor: theme.palette.action.hover,
                              },
                            },
                          }}
                          selected={selectedItem === to}
                          onClick={() => setSelectedItem(to)}
                        >
                          <ListItemIcon sx={{display:'flex', justifyContent:'center', alignItems:'center'}}>{icon}</ListItemIcon>
                          {drawerOpen && (
                            <ListItemText
                              primary={name}
                              sx={{
                                "& .MuiTypography-root": {
                                  fontSize: "14px",
                                  fontWeight: "400",
                                },
                              }}
                            />
                          )}
                        </ListItemButton>
                      ))}
                    </List>
                  </Collapse>
                )}
              </React.Fragment>
            ))}
          </List>
          <ExpandableItemAvatar isDrawer={drawerOpen} />
        </Stack>
      </Drawer>
      {children}
    </React.Fragment>
  );
};

export default Sidebar;
