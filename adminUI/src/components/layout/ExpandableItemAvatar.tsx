import React, { useState, useMemo } from "react";
import { Avatar, Box, ListItem, ListItemIcon, ListItemText, Stack, Typography } from "@mui/material";
import { useNavigate } from 'react-router-dom';
import { useSelector, shallowEqual } from "react-redux";
import { authActions } from "../../reducers/authSlice";
import { useAppDispatch } from "../../app/hooks";
import { selectUser } from "../../selectors/authSelector";
import Person2OutlinedIcon from '@mui/icons-material/ManageAccountsOutlined';
import LogoutIcon from "../../theme/icons/LogoutIcon";
import RightArrow from "../../theme/icons/RightArrow";
import DownArrow from "../../theme/icons/DownArrow";

// @ts-ignore
const ExpandableItemAvatar = ({ isDrawer }) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  // Use shallowEqual to avoid unnecessary re-renders when Redux state changes
  //const adminSelector = useSelector(selectUser, shallowEqual);
  const user = useSelector(selectUser, shallowEqual);

  const [showOptions, setShowOptions] = useState(false);

  const handleButtonClick = () => {
    setShowOptions((prev) => !prev);
  };

  const Logout = () => {
    dispatch(authActions.setLogout());

    localStorage.removeItem('userAdmin');
    localStorage.removeItem('signatureAdmin');
    localStorage.removeItem('refreshTokenAdmin');
    localStorage.removeItem('accessTokenAdmin');
    navigate('/login', { replace: true })
  };

  const handleClick = () => {
    navigate("/profile"); // Navigate to the profile page
  };

  return (
    <div style={{ cursor: "pointer" }}>
      <Stack
        onClick={handleButtonClick}
        color="inherit"
        direction="row"
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          width: "100%",
          marginBottom: "12px",
          cursor: "pointer",
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            flexDirection: "row",
            gap: "24px",
            padding:"8px 24px"
          }}
        >
          <Avatar sx={{ height: "40px", width: "40px" }} />
          <Typography sx={{ fontSize: "14px", fontWeight: "600" }}>
            {isDrawer ? `${user?.first_name} ${user?.last_name}` : "Admin"}
          </Typography>
        </div>
        {isDrawer && (showOptions ? <DownArrow /> : <RightArrow />)}
      </Stack>

      {showOptions && (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            justifyContent:'center',
            alignItems:'center',
            gap: "12px",
            width:'100%',
            padding: !isDrawer ? '12px 10px' : '12px 0px'
          }}
        >
          <ListItem button sx={{ width: "90%", padding: "8px 16px", borderRadius: "8px" }} onClick={handleClick}>
            <ListItemIcon>
              <Person2OutlinedIcon />
            </ListItemIcon>
            {isDrawer && (
              <ListItemText
                primaryTypographyProps={{ style: { fontSize: "14px" } }}
                primary="Admin Profile"
              />
            )}
          </ListItem>
          <ListItem button sx={{ width: "90%", padding: "8px 16px", borderRadius: "8px" }} onClick={Logout}>
            <ListItemIcon>
              <LogoutIcon />
            </ListItemIcon>
            {isDrawer && (
              <ListItemText
                primary="Logout"
                primaryTypographyProps={{ style: { fontSize: "14px" } }}
              />
            )}
          </ListItem>
        </Box>
      )}
    </div>
  );
};

export default React.memo(ExpandableItemAvatar);
