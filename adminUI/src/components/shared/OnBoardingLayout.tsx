// @ts-nocheck
import React, { ReactNode } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, Typography, Grid, Box, useTheme } from '@material-ui/core';
import { useLocation } from 'react-router';
import { FOOTER_LINKS } from 'configs/externalLinks';
import useStyles from './styles';
import { PATH_NAME } from '../../configs';
import ClinicalpadLogo from '../../themes/svgs/ClinicalPadLogo.svg';
import ClinicalpadLogoDark from '../../themes/svgs/ClinicalPadLogoDarkMode.svg';


const OnBoardingLayout = ({ content }: { content: ReactNode }) => {
  const classes = useStyles();
  const location = useLocation();
  const { pathname } = location;
  const theme = useTheme();

  return (
    <Box className={classes.background}>
      <img
        style={{ position: 'absolute', top: 0, left: 0, padding: '10px 32px 10px 32px' }}
        width="140"
        height="65px"
        src={theme.palette.type === 'light' ? ClinicalpadLogo : ClinicalpadLogoDark}
        alt="Logo"
      />
      <Grid container className={classes.container}>
        <Grid item xs={12} sm={4}>
          <Card className={classes.card}>
            <CardContent>{content}</CardContent>
          </Card>
        </Grid>
      </Grid>
      <Grid container className={classes.footer}>
        <Grid item>
          {pathname === PATH_NAME.LOGIN ? (
            <Typography variant="h6">
              Don't have an account?
              <Link to={PATH_NAME.HOSPITAL_ACCOUNT_OWNER} className={classes.boldtext}>
                Sign Up
              </Link>
            </Typography>
          ) : (
            <Typography variant="h6">
              Already have an account?
              <Link to={PATH_NAME.LOGIN} className={classes.boldtext}>
                Sign In
              </Link>
            </Typography>
          )}
        </Grid>
        <Grid item>
          <Typography variant="h6">
            <a target="_blank" href={FOOTER_LINKS.PRIVACY_POLICY} className={classes.footerOptions} rel="noreferrer">
              Privacy
            </a>
            <a
              target="_blank"
              href={FOOTER_LINKS.TERMS_AND_CONDITIONS}
              className={classes.footerOptions}
              rel="noreferrer"
            >
              Terms
            </a>
            {/* <a target="_blank" href={FOOTER_LINKS.GET_HELP} className={classes.footerOptions}>
              Get Help
            </a> */}
          </Typography>
        </Grid>
      </Grid>
    </Box>
  );
};

export default OnBoardingLayout;
