{"name": "mui-react-app", "version": "0.1.0", "private": true, "homepage": "/admin", "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@fontsource/roboto": "^4.5.8", "@mui/icons-material": "^5.16.14", "@mui/lab": "^5.0.0-alpha.124", "@mui/material": "^5.11.14", "@mui/x-data-grid": "^6.0.4", "@mui/x-date-pickers": "^6.0.4", "@reduxjs/toolkit": "^1.9.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^27.5.2", "@types/node": "^17.0.45", "@types/react": "^18.0.29", "@types/react-dom": "^18.0.11", "axios": "^1.7.9", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "dayjs": "^1.11.7", "env-cmd": "^10.1.0", "formik": "^2.4.6", "lodash": "^4.17.21", "notistack": "^3.0.1", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-datepicker": "^7.5.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.2", "react-google-recaptcha": "^3.1.0", "react-quill": "^2.0.0", "react-redux": "^8.0.5", "react-router-dom": "^6.9.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "build:stage": "env-cmd -f .env.staging react-scripts build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/react-copy-to-clipboard": "^5.0.4"}}