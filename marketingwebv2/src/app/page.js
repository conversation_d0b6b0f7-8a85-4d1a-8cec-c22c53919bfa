import React from "react";
import dynamic from "next/dynamic";
import Head from "next/head";
import Header from "../component/Header";
import FirstSection from "../component/homepage/FirstSection";
import HomeImageWithText from "../component/HomeImageWithText";
import Box from "@mui/material/Box";
import Image from "next/image";
import Support from "@/component/Support";
import {homeSupportQuestions, securitySupportQuestions} from "@/utils/supportQuestions";

const BenefitsSection = dynamic(() => import("../component/homepage/BenefitsSection"));
const JoinProduct = dynamic(() => import("../component/JoinProduct"));
const ContactUs = dynamic(() => import("../component/ContactUs"));
const Footer = dynamic(() => import("../component/Footer"));
export const metadata = {
  title: "Revolutionise Clinical Docs with Medical Dictation Software",
  description:
    "Streamline healthcare documentation with ClinicalPad’s medical dictation software for fast, accurate, and secure voice-to-text transcription and record management.",
  openGraph: {
    title: "ClinicalPad - Your Clinical Documentation Solution",
    description:
      "Streamline clinical communication and note-taking with ClinicalPad.",
    url: "https://www.clinicalpad.com",
    siteName: "ClinicalPad",
    type: "website",
  },
  verification: {
    google: "wbwxM52QXUDa3GlXueUj8rO2R8ZbWXOghbj8bMnBjpI",
  },
};

const Home = () => {

  return (
    <div>
      <Box style={{backgroundColor: "#f3f6f6"}}>
        <Header/>

        <FirstSection />

        <Image
          src="/images/home/<USER>"
          alt="section2_image"
          layout="responsive"
          width={600}
          height={400}
        />
        <Box sx={{ backgroundColor: "#dce5e5" }}>
          <HomeImageWithText
            imageUrl="/images/home/<USER>"
            heading="Enhancing Patient Care with ClinicalPad"
            paragraph="Streamline clinical documentation with speed and precision, tailored for the healthcare industry. ClinicalPad empowers healthcare professionals with fast and precise clinical documentation through advanced voice-to-text medical transcription technology, designed exclusively for the healthcare sector. Whether you’re managing a private clinic, working in a busy hospital, or navigating both, ClinicalPad offers tailored solutions to suit your needs. By automating workflows and minimising administrative errors, we help you save time, reduce delays, and ensure your patients receive the care they deserve. Experience the efficiency of the best medical transcription software for clinical documentation today."
            imagePosition="right"
            hideButton={false}
          />
        </Box>
        {/* Home Image with Text Section 1 */}
        <Box sx={{ backgroundColor: "#f3f6f6" }}>
          <HomeImageWithText
            imageUrl="/images/home/<USER>"
            heading="Ease of Note Taking"
            paragraph="ClinicalPad features an intuitive, user-friendly online document creator designed to accommodate various note-taking preferences. Users can type, dictate, record consultations, upload images of handwritten notes, and attach supporting images or files. This versatile functionality caters to diverse documentation needs, making it easier for healthcare professionals to capture and manage information efficiently. As the best medical dictation software, it adapts to your workflow, ensuring seamless integration into your practice. The platform’s medical transcription software capabilities provide both flexibility and accuracy."
            imagePosition="right"
            hideButton={false}
          />
        </Box>

        {/* Home Image with Text Section 2 */}
        <Box sx={{ backgroundColor: "#dce5e5" }}>
          <HomeImageWithText
            imageUrl="/images/home/<USER>"
            heading="Clinical Templates"
            paragraph="Choose from over 50 professionally curated clinical templates to generate a variety of clinical documents. These templates are designed to meet the unique requirements of different medical specialities. Additionally, ClinicalPad empowers users to create their own templates, enabling personalised clinical documents tailored to meet specific needs. This feature, combined with our advanced voice-to-text medical transcription capabilities, ensures that every document is both accurate and efficient. ClinicalPad’s medical dictation software streamlines the documentation process and ensures your practice runs smoothly."
            imagePosition="left"
            backgroundColor="#F3F6F6"
            hideButton={false}
          />
        </Box>

        {/* Home Image with Text Section 3 */}
          <Box sx={{ backgroundColor: "#f3f6f6" }}>
            <HomeImageWithText
              imageUrl="/images/home/<USER>"
              heading="Medical History Management"
              paragraph="Every time you create a clinical document with ClinicalPad, your patient’s medical history is automatically and accurately updated. The platform captures and organises this data in a clear, easy-to-read format, eliminating the need for manual updates. This streamlined process not only saves time but also ensures accurate and comprehensive patient record management. ClinicalPad stands as the best medical transcription software for improving record accuracy and efficiency, enhancing your patient record management system."
              imagePosition="right"
              backgroundColor="#dce5e5"
              hideButton={false}
            />
          </Box>
        <Box sx={{ backgroundColor: "#dce5e5" }}>
          <HomeImageWithText
            imageUrl="/images/home/<USER>"
            heading="Streamline your workflow with next-level clinical documentation now!"
            paragraph=""
            imagePosition="left"
            backgroundColor="#F3F6F6"
            hideButton={false}
          />
        </Box>
        <Box sx={{ backgroundColor: "#f3f6f6" }}>
          <BenefitsSection />
        </Box>

        <Box sx={{ backgroundColor: "#dce5e5  " }}>
          <JoinProduct />
        </Box>

        <ContactUs />
        <Support supportQuestions={homeSupportQuestions} />
        <Footer />
      </Box>
    </div>
  );
};

export default Home;
