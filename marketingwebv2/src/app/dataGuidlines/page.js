
import { Typography, Hidden, Box } from "@mui/material";
import Footer from "../../component/Footer";
import Header from "../../component/Header";
import dataUsageGuidelines from "../../utils/dataUsageGuidelines.json";
import TermsCondition from "../../component/TermsCondition";

const DataUsage = () => {


  return (
    <Box style={{backgroundColor: "#F3F6F6"}}>
      <Header/>
      <div  hidden={true}>
        {dataUsageGuidelines.map((item) => (
          <div key={item.id} style={{marginBottom: '20px'}}>
            <h2>{item.title}</h2>
            <div dangerouslySetInnerHTML={{__html: item.detail}}/>
          </div>
        ))}
      </div>
      <Box
        sx={{
          textAlign: "center",
          pt: {xs: "30px", md: "40px"},
          pb: {xs: "20px", md: "40px"},
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Box
          className="container"
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Hidden mdDown>
            <img
              src="/images/termsConditionsHeader.webp"
              alt="header1"
            />
          </Hidden>
          <Typography
            sx={{
              fontFamily: "GilroyMedium",
              fontWeight: 700,
              fontSize: {xs: "40px", md: "65px"},
              lineHeight: {xs: 1.4, md: "75px"},
              ml: {md: 5},
            }}
          >
            Data Usage Guidelines
          </Typography>
        </Box>
      </Box>

      <Box sx={{display: "flex", flexDirection: "column", alignItems: "center"}}>
        <div
          className="container"
          style={{
            fontFamily: "GilroyMedium",
            fontWeight: 500,
            fontSize: 18,
            lineHeight: "32px",
            pb: "20px",
          }}
        >
          <div>
            <p><span style={{fontWeight: '400'}}>This document outlines the guidelines and practices governing the collection, processing, and usage of data for the purpose of training and generating clinical documentation by ClinicalPad.</span>
            </p>
            <p><span style={{fontWeight: '400'}}>For a comprehensive breakdown of the privacy of data and terms of use, please see our </span><strong>Privacy
              Policy</strong><span style={{fontWeight: '400'}}> and </span><strong>Terms and Conditions</strong><span
              style={{fontWeight: '400'}}> documents.</span></p>
            <p><span style={{fontWeight: '400'}}>We collect and process the following types of data when using ClinicalPad.</span>
            </p>
          </div>
        </div>

      </Box>

      <TermsCondition data={dataUsageGuidelines}/>

      <Footer/>
    </Box>
  );
};

export default DataUsage;
