
import { Typography, Hidden, Box } from "@mui/material";
import Footer from "../../component/Footer";
import Header from "../../component/Header";
import dataAgreement from "../../utils/dataAgreement.json";
import TermsCondition from "../../component/TermsCondition";
import Image from "next/image"; // Import for optimized images

const DataProcessingAgreement = () => {

  const cleanedData = dataAgreement.map(item => ({
    ...item,
    detail: item.detail.replace(/<[^>]*>/g, '')
  }));


  return (
    <Box style={{backgroundColor: "#F3F6F6"}}>
      <Header/>
      <div hidden={true}>
        {cleanedData.map(item => (
          <div key={item.id}>
            <h2>{item.title}</h2>
            {item.subtitles && item.subtitles.length > 0 && (
              <ul>
                {item.subtitles.map((subtitle, idx) => (
                  <li key={idx}>{subtitle}</li>
                ))}
              </ul>
            )}
            <p>{item.detail}</p>
          </div>
        ))}
      </div>

      {/* SECTION 1 */}
      <Box
        sx={{
          textAlign: "center",
          pt: {xs: "30px", md: "40px"},
          pb: {xs: "20px", md: "40px"},
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Box
          className="container"
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Hidden mdDown>
            <Image
              src="/images/termsConditionsHeader.webp"
              alt="header1"
              width={180}
              height={115}
            />
          </Hidden>
          <Typography
            sx={{
              fontFamily: "GilroyMedium",
              fontWeight: 700,
              fontSize: {xs: "40px", md: "65px"},
              lineHeight: {xs: 1.4, md: "75px"},
              ml: {md: 5},
            }}
          >
            Data Processing Agreement
          </Typography>
        </Box>
      </Box>
      <Box sx={{display: "flex", flexDirection: "column", alignItems: "center"}}>
        <Typography
          className="container"
          sx={{
            fontFamily: "GilroyMedium",
            fontWeight: 500,
            fontSize: 18,
            lineHeight: "32px",
            pb: "20px",
            textAlign: "center", // Center-aligned content
          }}
        >
    <span style={{fontWeight: 400, display: 'block', marginBottom: '15px'}}>
      This Data Processing Agreement (&#34;<span style={{fontWeight: 600}}>Agreement</span>&#34;) forms part of the Contract for Services (“Principal Agreement”) between
    </span>
          <span style={{fontWeight: 400, display: 'block', marginBottom: '15px'}}>
      (the &#34;<span style={{fontWeight: 600}}>Company/User</span>&#34;)
    </span>
          <span style={{fontWeight: 400, display: 'block', marginBottom: '15px'}}>
      and
    </span>
          <span style={{fontWeight: 400, display: 'block', marginBottom: '15px'}}>
      (the “CLINICALPAD LTD, Data Processor”)
    </span>
          <span style={{fontWeight: 400, display: 'block', marginBottom: '15px'}}>
      Together as the (&#34;<span style={{fontWeight: 600}}>Parties</span>&#34;)
    </span>
          <span style={{fontWeight: 400, display: 'block', marginBottom: '15px'}}>
      WHEREAS
    </span>
        </Typography>

        <Typography
          className="container"
          sx={{
            fontFamily: "GilroyMedium",
            fontWeight: 500,
            fontSize: 18,
            lineHeight: "32px",
            pb: "20px",
          }}
        >
    <span style={{fontWeight: 400, display: 'block', marginBottom: '15px'}}>
      (A) The Company/User acts as a Data Controller.
    </span>
          <span style={{fontWeight: 400, display: 'block', marginBottom: '15px'}}>
      (B) The Company/User wishes to subcontract certain Services, which imply the processing of personal data, to the Data Processor.
    </span>
          <span style={{fontWeight: 400, display: 'block', marginBottom: '15px'}}>
      (C) The Parties seek to implement a data processing agreement that complies with the requirements of the current legal framework in relation to data processing and with the Regulation (EU) 2016/679 of the European Parliament and of the Council of 27 April 2016 on the protection of natural persons with regard to the processing of personal data and on the free movement of such data, and repealing Directive 95/46/EC (General Data Protection Regulation).
    </span>
          <span style={{fontWeight: 400, display: 'block', marginBottom: '15px'}}>
      (D) The Parties wish to lay down their rights and obligations.
    </span>
          <span style={{fontWeight: 400, display: 'block', marginBottom: '15px'}}>
      IT IS AGREED AS FOLLOWS:
    </span>
        </Typography>
      </Box>
      <TermsCondition data={dataAgreement}/>
      <Footer/>
    </Box>
  );
};

export default DataProcessingAgreement;
