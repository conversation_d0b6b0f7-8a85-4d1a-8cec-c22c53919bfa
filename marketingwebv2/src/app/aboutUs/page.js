import React from "react";
import Head from "next/head";
import dynamic from "next/dynamic";
import { Box } from "@mui/material";
import Image from "next/image"; // Import Next.js Image component

// Dynamically load components
const Header = dynamic(() => import("../../component/Header"));
const Footer = dynamic(() => import("../../component/Footer"));
const Support = dynamic(() => import("../../component/Support"));
const MeetTeam = dynamic(() => import("../../component/MeetTeam"));
const ImageWithText = dynamic(() => import("../../component/ImageWithText"));
const SecondSection = dynamic(() => import("../../component/aboutPage/SecondSection"));
import { supportQuestions } from "../../utils/supportQuestions";

const AboutUs = () => {
  return (
    <>
      <Box style={{ backgroundColor: "#f3f6f6" }}>
        <Header />

        <Box sx={{ backgroundColor: "#f3f6f6" }}>
          <ImageWithText
            key="about-us"
            imageUrl="/images/aboutUs/about1.webp"
            heading="About Us"
            paragraph="Welcome to ClinicalPad, where tradition meets innovation in the realm of medical documentation. Initially a traditional medical transcription company, ClinicalPad has evolved dramatically to address the pressing needs of modern healthcare professionals. Today, it stands as a pioneer in medical documentation, integrating the power of Artificial Intelligence (AI) and Machine Learning (ML) to enhance the accuracy and efficiency of clinical records."
            imagePosition="right"
            hideButton={true}
            gridImage={6}
            gridText={6}
            redirectToIndividual={() => {}}
            image={
              <Image
                src="/images/aboutUs/about1.webp"
                alt="About Us"
                width={600} // Define width
                height={400} // Define height
                layout="intrinsic" // Keep the aspect ratio intact
              />
            }
          />
        </Box>

        <Box sx={{ backgroundColor: "#dce5e5" }}>
          <ImageWithText
            key="history"
            imageUrl="/images/aboutUs/about2.webp"
            heading="It All Started in 2018"
            paragraph="Our journey began with a simple yet profound vision: to transform the cumbersome, often inefficient world of medical transcription. Recognising the challenges faced by clinicians with outdated transcription services that were not only costly but also time-consuming, ClinicalPad was reimagined in 2022 by a team of visionary entrepreneurs and clinicians. Their goal was to develop a tool that not only met but exceeded the rigorous demands of healthcare documentation."
            imagePosition="left"
            hideButton={true}
            gridImage={6}
            gridText={6}
            redirectToIndividual={() => {}}
            image={
              <Image
                src="/images/aboutUs/about2.webp"
                alt="It All Started in 2018"
                width={600}
                height={400}
                layout="intrinsic"
              />
            }
          />
        </Box>

        <Box sx={{ backgroundColor: "#f3f6f6" }}>
          <ImageWithText
            key="mission"
            imageUrl="/images/aboutUs/about3.webp"
            heading="We are on a Mission"
            paragraph="ClinicalPad is more than just a web-based writing tool; it is a testament to the dedication of our founders who designed the platform specifically for clinicians. This user-friendly platform drastically reduces the time required to create clinical documents—starting with comprehensive clinical letters, ClinicalPad will be expanding its template selection to include a variety of clinical documents including SOAP notes, H&P notes, discharge summaries, and more. Each feature, from voice-to-text dictation to customisable templates and collaborative tools, is crafted to streamline the workflow in medical practices."
            imagePosition="right"
            hideButton={true}
            gridImage={6}
            gridText={6}
            redirectToIndividual={() => {}}
            image={
              <Image
                src="/images/aboutUs/about3.webp"
                alt="Mission"
                width={600}
                height={400}
                layout="intrinsic"
              />
            }
          />
        </Box>

        <Box sx={{ backgroundColor: "#dce5e5" }}>
          <MeetTeam />
        </Box>

        <Box sx={{ backgroundColor: "#F3F6F6", paddingBottom: "10px" }}>
          <Support supportQuestions={supportQuestions} />
        </Box>

        <Footer />
      </Box>
    </>
  );
};

export default AboutUs;
