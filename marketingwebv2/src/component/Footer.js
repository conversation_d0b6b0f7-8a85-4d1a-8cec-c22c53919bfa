import { Box, But<PERSON>, Divider, Typography } from "@mui/material";
import LinkedInIcon from "@mui/icons-material/LinkedIn";
import InstagramIcon from "@mui/icons-material/Instagram";
import Link from "next/link";
import Image from "next/image";
import SubscribeDialogue from "./SubscribeDialogue"; // Adjust path if necessary

const pages = [
  { name: "About Us", route: "/aboutUs" },
  { name: "Data Security", route: "/security" },
  { name: "Terms and Conditions", route: "/termsAndConditions" },
  { name: "Privacy Policy", route: "/privacyPolicy" },
  { name: "Data Processing Agreement", route: "/dataProcessingAgreement" },
  { name: "Data Usage Guidelines", route: "/dataGuidlines" },
  { name: "Cookie Policy", route: "/cookiePolicy" },
];

const Footer = () => {
  return (
    <Box
      sx={{
        pt: 8,
        pb: 5,
        backgroundColor: "#dce5e5",
        display: "flex",
        justifyContent: "center",
        flexDirection: "column",
      }}
    >
      {/* Logo Section with Next.js Image */}
      <Box
        sx={{
          width: "235px",
          height: "95px",
          display: "block",
          margin: "auto",
          marginBottom: "40px",
        }}
      >
        <Image
          src="/images/ClinicalPadLogo.svg"
          alt="logo"
          layout="responsive"
          width={235}
          height={95}
        />
      </Box>

      {/* Navigation Links */}
      <Box className="container">
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", md: "row" },
            justifyContent: "center",
            gap: 2,
            mb: 4,
          }}
        >
          {pages.map(({ name, route }) => (
            <Link key={name} href={route} style={{ textDecoration: "none" }} passHref>
              <Button
                sx={{
                  color: "black",
                  display: "flex",
                  textTransform: "none",
                  fontWeight: 600,
                  fontSize: 15,
                  fontFamily: "GilroyMedium",
                  lineHeight: "24px",

                }}
              >
                {name}
              </Button>
            </Link>
          ))}
        </Box>

        {/* Divider Section */}
        <Divider sx={{ borderColor: "#2C2E30" }} />

        {/* Social Media Section */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            flexDirection: { xs: "column", md: "row" },
            pt: 6,
            alignItems: "center",
          }}
        >
          {/* Social Media Links */}
          <Box
            sx={{
              display: "flex",
              gap: { xs: 6, md: 1 },
              mb: { xs: "40px", md: 0 },
            }}
          >
            <Link
              href="https://www.linkedin.com/company/clinicalpad/"
              passHref
            >
              <LinkedInIcon
                sx={{
                  color: "black",
                  cursor: "pointer",
                  width: { xs: 50, md: 30 },
                  height: { xs: 50, md: 30 },
                }}
              />
            </Link>
            <Link href="https://www.instagram.com/clinicalpad/" passHref>
              <InstagramIcon
                sx={{
                  color: "black",
                  cursor: "pointer",
                  width: { xs: 50, md: 30 },
                  height: { xs: 50, md: 30 },
                }}
              />
            </Link>
          </Box>

          {/* Copyright */}
          <Typography
            sx={{
              color: "black",
              fontWeight: 600,
              fontSize: 15,
              fontFamily: "GilroyMedium",
              textAlign: { xs: "center", md: "end" },
              lineHeight: { xs: "32px", md: "16px" },
            }}
          >
            Copyright 2025 | All Rights Reserved ClinicalPad Ltd.
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default Footer;
