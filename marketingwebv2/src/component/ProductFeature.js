import { Typography, Box } from "@mui/material";
import Image from "next/image";

const ProductFeature = () => {
  return (
    <Box sx={{ backgroundColor: "#F3F6F6" }}>
      <Box
        className="container"
        sx={{
          gap: "24px",
          display: "flex",
          flexDirection: "column",
          pt: { xs: "80px" },
          pb: { xs: "80px" },
        }}
      >
        <Typography
          sx={{
            marginBottom: { xs: "0px", md: "40px" },
            fontFamily: "GilroyMedium",
            fontWeight: 700,
            textAlign: "center",
            fontSize: { xs: "30px", md: "45px" },
            lineHeight: { xs: 1.3, md: "50px" },
          }}
        >
          Our Features
        </Typography>
        <Box sx={{ position: "relative", width: "100%", height: "auto" }}>
          <Image
            src="/images/product/product6.webp"
            alt="features"
            layout="responsive"
            width={1200}
            height={600}
            style={{
              borderRadius: "8px",
            }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default ProductFeature;
