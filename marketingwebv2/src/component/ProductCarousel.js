import React from "react";
import { Typography, Grid } from "@mui/material";
import Image from "next/image";

const ProductCarousel = ({ cardsData, md }) => {
  return (
    <Grid container spacing={{ xs: 6, md: 4 }}>
      {cardsData.map((cardData, index) => (
        <Grid item xs={12} sm={12} md={md} key={index}>
          <Image
            src={cardData.imageUrl}
            alt={cardData.title}
            width={500}
            height={300}
            layout="responsive"
            priority={index === 0}
            style={{
              borderRadius: "8px",
            }}
          />
          <Typography
            sx={{
              fontFamily: "GilroyMedium",
              fontWeight: 700,
              fontSize: { xs: "20px", md: "26px" },
              lineHeight: { xs: 1.2 },
              mt: { xs: "10px" },
              mb: { xs: "10px" },
            }}
          >
            {cardData.title}
          </Typography>
          <Typography
            sx={{
              fontFamily: "GilroyMedium",
              fontWeight: 500,
              fontSize: 16,
              lineHeight: "21px",
            }}
          >
            {cardData.description}
          </Typography>
        </Grid>
      ))}
    </Grid>
  );
};

export default ProductCarousel;
