import React from "react";
import { Typo<PERSON>, <PERSON>, Button, Grid } from "@mui/material";
import Link from "next/link";
import Image from "next/image";

const HomeImage = ({ imageUrl, heading }) => (
  <Box sx={{ position: "relative", width: "100%", height: "100%" }}>
    <Image src={imageUrl} alt={heading} layout="responsive" width={700} height={400} />
  </Box>
);

const TextContent = ({ heading, paragraph, hideButton, buttonText, buttonLink }) => (
  <Box>
    {/* Main Heading */}
    <Typography
      variant="h4"
      gutterBottom
      sx={{
        fontFamily: "GilroyMedium",
        fontWeight: 700,
        fontSize: { xs: "30px", md: "45px" },
        lineHeight: { xs: 1.3, md: "50px" },
      }}
    >
      {heading}
    </Typography>

    {/* Paragraph Content */}
    <Typography
      variant="body1"
      paragraph
      sx={{
        fontFamily: "GilroyMedium",
        fontWeight: 500,
        fontSize: "16px",
        lineHeight: "21px",
      }}
    >
      {paragraph}
    </Typography>

    {/* Conditional Button */}
    {!hideButton && (
      <Link href={buttonLink || "/product"} passHref>
        <Button
          variant="contained"
          sx={{
            borderRadius: "100px",
            padding: "11px 32px",
            color: "#fff",
            background: "linear-gradient(to right, #354bb6, #495cc4, #5d6fd2, #7180e1, #8792f0, #9ba2fe)",
            textTransform: "none",
            fontSize: "16px",
            fontWeight: 700,
            boxShadow: "none",
            fontFamily: "PlusJakartaSans",
            mb: { xs: "30px", md: 0 },
          }}
        >
          {buttonText || "Explore More"}
        </Button>
      </Link>
    )}
  </Box>
);

// Main Component
const HomeImageWithText = ({ imageUrl, heading, paragraph, imagePosition, backgroundColor, hideButton, buttonText, buttonLink }) => {
  return (
    <Box className="container">
      <Grid
        container
        alignItems="center"
        justifyContent="center"
        sx={{
          paddingBottom: { xs: "20px", md: "70px" },
          paddingTop: { xs: "60px", md: 0 },
        }}
        spacing={{ xs: 0, md: 3 }}
      >
        {/* Left or Right Position Content */}
        {imagePosition === "left" && (
          <>
            <Grid item xs={12} md={8} className="containerFluid">
              <HomeImage imageUrl={imageUrl} heading={heading} />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextContent
                heading={heading}
                paragraph={paragraph}
                hideButton={hideButton}
                buttonText={buttonText}
                buttonLink={buttonLink}
              />
            </Grid>
          </>
        )}

        {imagePosition === "right" && (
          <>
            <Grid item xs={12} md={4}>
              <TextContent
                heading={heading}
                paragraph={paragraph}
                hideButton={hideButton}
                buttonText={buttonText}
                buttonLink={buttonLink}
              />
            </Grid>
            <Grid item xs={12} md={8} className="containerFluid">
              <HomeImage imageUrl={imageUrl} heading={heading} />
            </Grid>
          </>
        )}
      </Grid>
    </Box>
  );
};

export default HomeImageWithText;
