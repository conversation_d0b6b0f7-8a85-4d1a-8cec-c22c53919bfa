import React, { useState, useEffect, useRef } from "react";
import { Transition } from "react-transition-group";
import { styled } from "styletron-react";
import { ThemeContext } from "../../core/ThemeContext";
import Div from "../div/Div";
import Icon from "../icon/Icon";
import findDropdownDir from "./DropdownMenu.style";

// Transition configurations
const duration = 300;

const defaultMenuStyle = {
  transition: `all ${duration}ms ease-in-out`,
  opacity: 0,
  transform: "scaleY(1)",
};

const menuTransitionStyles = {
  entering: { opacity: 0, transform: "scaleY(0)" },
  entered: { opacity: 1, transform: "scaleY(1)" },
  exiting: { opacity: 0, transform: "scaleY(0)" },
  exited: { opacity: 0, transform: "scaleY(0)" },
};

// Styled dropdown menu with dynamic styles
const BaseDropdownMenu = styled(Div, props => ({ ...props.$style }));

const Dropdown = ({
                    w,
                    minW,
                    maxW,
                    m,
                    children,
                    menu,
                    isOpen,
                    onClick,
                    targetHover,
                    prefix,
                    openSuffix,
                    closeSuffix,
                    isLoading,
                    borderColor,
                    focusBorderColor,
                    bg,
                    focusBg,
                    direction,
                    zIndex,
                    ...rest
                  }) => {
  const [hovered, setHovered] = useState(false);
  const dropdownRef = useRef();

  const handleOutsideClick = (e) => {
    if (dropdownRef.current && !dropdownRef.current.contains(e.target)) {
      if (onClick) onClick();
    }
  };

  const onHover = (value) => {
    if (isLoading) return;
    setHovered(value);
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener("mousedown", handleOutsideClick);
    } else {
      document.removeEventListener("mousedown", handleOutsideClick);
    }

    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
    };
  }, [isOpen]);

  const menustyle = findDropdownDir(direction);

  const showDropdown = isOpen || hovered;

  return (
    <Div
      w={w}
      minW={minW}
      maxW={maxW}
      m={m}
      onMouseEnter={() => onHover(true)}
      onMouseLeave={() => onHover(false)}
      zIndex={zIndex}
      pos="relative"
      ref={dropdownRef}
    >
      <Div
        tag="button"
        tabIndex="0"
        w="100%"
        onClick={onClick}
        borderColor={showDropdown ? focusBorderColor : borderColor}
        bg={showDropdown ? focusBg : bg}
        {...rest}
      >
        {prefix}
        {children}
        {isLoading ? (
          <Icon name="Loading" color="gray600" size="18px" />
        ) : isOpen ? (
          openSuffix
        ) : (
          closeSuffix
        )}
      </Div>
      <Transition
        in={showDropdown}
        timeout={duration}
        unmountOnExit
      >
        {state => (
          <BaseDropdownMenu
            $style={menustyle}
            zIndex="10"
            tabIndex="-1"
            style={{
              ...defaultMenuStyle,
              ...menuTransitionStyles[state],
            }}
            transition
            pos="absolute"
            shadow="4"
            minW="100%"
            rounded="md"
            bg="white"
          >
            {menu}
          </BaseDropdownMenu>
        )}
      </Transition>
    </Div>
  );
};

Dropdown.defaultProps = {
  tag: "div",
  d: "flex",
  align: "center",
  textColor: "medium",
  textWeight: "500",
  justify: "space-between",
  p: { x: "0.75rem" },
  h: "2.5rem",
  textSize: "body",
  rounded: "md",
  border: "1px solid",
  borderColor: "gray500",
  w: "100%",
  bg: "white",
  focusBg: "gray100",
  focusBorderColor: "gray800",
  cursor: "pointer",
  onClick: () => {},
  openSuffix: (
    <Icon name="UpArrow" size="18px" m={{ l: "1rem" }} color="medium" />
  ),
  closeSuffix: (
    <Icon name="DownArrow" size="18px" m={{ l: "1rem" }} color="medium" />
  ),
};

Dropdown.contextType = ThemeContext;

export default Dropdown;
