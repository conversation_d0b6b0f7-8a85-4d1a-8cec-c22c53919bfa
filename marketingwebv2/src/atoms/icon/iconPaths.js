import React from "react";

export const iconPaths = {
  Add: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M11,11 L8,11 C7.44771525,11 7,11.4477153 7,12 C7,12.5522847 7.44771525,13 8,13 L11,13 L11,16 C11,16.5522847 11.4477153,17 12,17 C12.5522847,17 13,16.5522847 13,16 L13,13 L16,13 C16.5522847,13 17,12.5522847 17,12 C17,11.4477153 16.5522847,11 16,11 L13,11 L13,8 C13,7.44771525 12.5522847,7 12,7 C11.4477153,7 11,7.44771525 11,8 L11,11 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z"
      />
    );
  },
  AlertSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M13.7557911,3.21895043 L22.3867068,19.0422957 C22.9156322,20.0119924 22.5583166,21.2268657 21.5886199,21.7557911 C21.2948427,21.9160333 20.9655536,22 20.6309156,22 L3.36908438,22 C2.26451488,22 1.36908438,21.1045695 1.36908438,20 C1.36908438,19.665362 1.45305112,19.3360729 1.61329323,19.0422957 L10.2442089,3.21895043 C10.7731343,2.24925376 11.9880076,1.89193809 12.9577043,2.42086355 C13.2948373,2.6047543 13.5719004,2.8818174 13.7557911,3.21895043 Z M12.0952381,16.5 C11.5429533,16.5 11.0952381,16.9477153 11.0952381,17.5 C11.0952381,18.0522847 11.5429533,18.5 12.0952381,18.5 C12.6475228,18.5 13.0952381,18.0522847 13.0952381,17.5 C13.0952381,16.9477153 12.6475228,16.5 12.0952381,16.5 Z M12.0952381,9 C11.5429533,9 11.0952381,9.44771525 11.0952381,10 L11.0952381,14 C11.0952381,14.5522847 11.5429533,15 12.0952381,15 C12.6475228,15 13.0952381,14.5522847 13.0952381,14 L13.0952381,10 C13.0952381,9.44771525 12.6475228,9 12.0952381,9 Z"
      />
    );
  },
  Alert: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M13.3867068,3.04267403 L22.0176224,18.8660193 C22.5465478,19.835716 22.1892322,21.0505893 21.2195355,21.5795147 C20.9257583,21.7397568 20.5964692,21.8237236 20.2618312,21.8237236 L3,21.8237236 C1.8954305,21.8237236 1,20.9282931 1,19.8237236 C1,19.4890856 1.08396675,19.1597965 1.24420885,18.8660193 L9.87512447,3.04267403 C10.4040499,2.07297735 11.6189232,1.71566168 12.5886199,2.24458714 C12.9257529,2.42847789 13.202816,2.70554099 13.3867068,3.04267403 Z M11.6309156,4.00037829 L3,19.8237236 L20.2618312,19.8237236 L11.6309156,4.00037829 Z M11.6309156,16.3237236 C12.1832004,16.3237236 12.6309156,16.7714388 12.6309156,17.3237236 C12.6309156,17.8760083 12.1832004,18.3237236 11.6309156,18.3237236 C11.0786309,18.3237236 10.6309156,17.8760083 10.6309156,17.3237236 C10.6309156,16.7714388 11.0786309,16.3237236 11.6309156,16.3237236 Z M11.6309156,8.82372359 C12.1832004,8.82372359 12.6309156,9.27143884 12.6309156,9.82372359 L12.6309156,13.8237236 C12.6309156,14.3760083 12.1832004,14.8237236 11.6309156,14.8237236 C11.0786309,14.8237236 10.6309156,14.3760083 10.6309156,13.8237236 L10.6309156,9.82372359 C10.6309156,9.27143884 11.0786309,8.82372359 11.6309156,8.82372359 Z"
      />
    );
  },
  Attachment: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M16.663227,13.835664 C17.0537513,13.4451398 17.6869163,13.4451398 18.0774406,13.835664 C18.4679649,14.2261883 18.4679649,14.8593533 18.0774406,15.2498776 L13.7764086,19.5509096 C11.5399199,21.7873983 7.91385523,21.7873983 5.67736653,19.5509096 C3.44087782,17.3144209 3.44087782,13.6883563 5.67736653,11.4518676 L12.9133587,4.21587538 C14.534526,2.59470819 17.1629574,2.59470821 18.7841246,4.21587543 C20.4052918,5.83704264 20.4052918,8.46547402 18.78374,10.0870257 L11.5402586,17.3226335 C10.5344129,18.3284792 8.90361468,18.3284792 7.89776892,17.3226335 C6.89192316,16.3167877 6.89192316,14.6859895 7.89818572,13.6797272 L12.2528571,9.330185 C12.6436114,8.9398909 13.2767763,8.94026401 13.6670704,9.33101836 C14.0573645,9.72177271 14.0569914,10.3549376 13.666237,10.7452317 C11.9727537,12.4367203 11.9727537,12.4367203 9.31198248,15.0943573 C9.0871853,15.3191545 9.0871853,15.6836227 9.31198248,15.9084199 C9.53677966,16.1332171 9.9012479,16.1332171 10.1264297,15.9080355 L17.369911,8.67242772 C18.2100297,7.83230907 18.2100297,6.47020763 17.369911,5.63008899 C16.5297924,4.78997035 15.1676909,4.78997034 14.3275723,5.63008895 L7.09158009,12.8660812 C5.63613997,14.3215213 5.63613997,16.6812559 7.09158009,18.1366961 C8.54702021,19.5921362 10.9067549,19.5921362 12.362195,18.1366961 L16.663227,13.835664 Z"
      />
    );
  },
  Back: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12.2925,15.4625 C12.6825,15.8525 12.6725,16.4925 12.2925,16.8825 C11.9025,17.2725 11.2725,17.2725 10.8825,16.8825 L6.2925,12.2925 C5.9025,11.9025 5.9025,11.2725 6.2925,10.8825 L10.8825,6.2925 C11.2725,5.9025 11.9025,5.9025 12.2925,6.2925 C12.6825,6.6825 12.6825,7.3125 12.2925,7.7025 L8.4125,11.5825 L12.2925,15.4625 Z M18.2925,15.4625 C18.6825,15.8525 18.6725,16.4925 18.2925,16.8825 C17.9025,17.2725 17.2725,17.2725 16.8825,16.8825 L12.2925,12.2925 C11.9025,11.9025 11.9025,11.2725 12.2925,10.8825 L16.8825,6.2925 C17.2725,5.9025 17.9025,5.9025 18.2925,6.2925 C18.6825,6.6825 18.6825,7.3125 18.2925,7.7025 L14.4125,11.5825 L18.2925,15.4625 Z"
      />
    );
  },
  Bag: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M22,9.0125 L17.21,9.0125 L12.82,2.4425 C12.42,1.8525 11.55,1.8525 11.16,2.4425 L6.77,9.0125 L2,9.0125 C1.45,9.0125 1,9.4625 1,10.0125 C1,10.1025 1.01,10.1925 1.04,10.2825 L3.58,19.5525 C3.81,20.3925 4.58,21.0125 5.5,21.0125 L18.5,21.0125 C19.42,21.0125 20.19,20.3925 20.43,19.5525 L22.97,10.2825 L23,10.0125 C23,9.4625 22.55,9.0125 22,9.0125 Z M11.99,4.8025 L14.8,9.0125 L9.18,9.0125 L11.99,4.8025 Z M12,17.0125 C10.9,17.0125 10,16.1125 10,15.0125 C10,13.9125 10.9,13.0125 12,13.0125 C13.1,13.0125 14,13.9125 14,15.0125 C14,16.1125 13.1,17.0125 12,17.0125 Z"
      />
    );
  },
  Behance: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M19.6924776,7.70686567 L14.8668209,7.70686567 L14.8668209,6.50820896 L19.6928806,6.50820896 L19.6928806,7.70686567 L19.6924776,7.70686567 L19.6924776,7.70686567 Z M11.6542687,12.6221642 C11.9656418,13.1044478 12.1212388,13.6900299 12.1212388,14.3775224 C12.1212388,15.0884776 11.945806,15.7262687 11.5888955,16.2896418 C11.3618358,16.6624478 11.0799254,16.9773582 10.741194,17.2308806 C10.3597015,17.5240746 9.90804478,17.7251194 9.3888209,17.8325373 C8.8679403,17.9401343 8.30416418,17.9944925 7.69686567,17.9944925 L2.3,17.9944925 L2.3,6.0110597 L8.08731343,6.0110597 C9.54607463,6.03479104 10.5802687,6.45743284 11.1909701,7.28744776 C11.5574179,7.79650746 11.7389851,8.40734328 11.7389851,9.11735821 C11.7389851,9.84998507 11.5552239,10.436194 11.185194,10.8815821 C10.9793582,11.1312985 10.6752836,11.3584478 10.2727463,11.5634776 C10.8824627,11.7866866 11.3450896,12.1381791 11.6542687,12.6221642 Z M5.06367164,10.7355224 L7.59943284,10.7355224 C8.12053731,10.7355224 8.54179104,10.6364776 8.86623881,10.4384776 C9.19046269,10.240791 9.35223881,9.88938806 9.35223881,9.38485075 C9.35223881,8.82743284 9.13802985,8.45744776 8.70858209,8.27937313 C8.33944776,8.15565672 7.86714925,8.0919403 7.2940597,8.0919403 L5.06367164,8.0919403 L5.06367164,10.7355224 Z M9.59640299,14.2063433 C9.59640299,13.5838657 9.34198507,13.1535224 8.8341791,12.9226119 C8.55020896,12.7913731 8.14955224,12.7242985 7.63583582,12.7195075 L5.06367164,12.7195075 L5.06367164,15.9130299 L7.59602985,15.9130299 C8.11628358,15.9130299 8.51895522,15.8449254 8.80955224,15.7028507 C9.33361194,15.4416269 9.59640299,14.9444776 9.59640299,14.2063433 Z M21.4829403,12.2568358 C21.5412388,12.6488955 21.5676119,13.2177313 21.5569104,13.9612388 L15.3071493,13.9612388 C15.3416269,14.8238955 15.6394328,15.4271642 16.2042388,15.7723881 C16.5448507,15.9888806 16.9578209,16.094597 17.441403,16.094597 C17.9513134,16.094597 18.3669254,15.9656866 18.6868507,15.7007015 C18.8611642,15.5602388 19.0149254,15.3619701 19.1478657,15.111806 L21.4386119,15.111806 C21.3786119,15.6213134 21.102791,16.1380746 20.6071194,16.663209 C19.8391642,17.4978806 18.7621194,17.916403 17.3788955,17.916403 C16.236209,17.916403 15.2291493,17.5634328 14.3550299,16.8600448 C13.4838209,16.154194 13.0462687,15.0094478 13.0462687,13.4214627 C13.0462687,11.9323433 13.4390896,10.7925672 14.2273731,9.99922388 C15.0185672,9.20440299 16.0396418,8.80880597 17.2982537,8.80880597 C18.0442687,8.80880597 18.7167612,8.94214925 19.3167164,9.21022388 C19.914791,9.47856716 20.4090746,9.90089552 20.7983582,10.4813284 C21.1506567,10.9924925 21.3772239,11.5835373 21.4829403,12.2568358 Z M19.2281493,12.4804478 C19.1861493,11.8839403 18.9869851,11.4324627 18.6283731,11.1239104 C18.2727164,10.8145075 17.828403,10.6591791 17.2982537,10.6591791 C16.7212687,10.6591791 16.2757463,10.825209 15.9591791,11.1515373 C15.6404627,11.477194 15.4421493,11.9199403 15.3608806,12.4804925 L19.2281493,12.4804925 L19.2281493,12.4804478 Z"
      />
    );
  },
  BookmarkSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M7.8458278,2 L16.1541722,2 C17.4914503,2 17.9763797,2.13923842 18.4652686,2.40069906 C18.9541574,2.66215969 19.3378403,3.04584256 19.5993009,3.53473144 C19.8607616,4.02362033 20,4.50854969 20,5.8458278 L20,21 C20,21.5522847 19.5522847,22 19,22 C18.7836298,22 18.5730962,21.9298221 18.4,21.8 L12,17 L5.6,21.8 C5.1581722,22.1313708 4.53137085,22.0418278 4.2,21.6 C4.07017787,21.4269038 4,21.2163702 4,21 L4,5.8458278 C4,4.50854969 4.13923842,4.02362033 4.40069906,3.53473144 C4.66215969,3.04584256 5.04584256,2.66215969 5.53473144,2.40069906 C6.02362033,2.13923842 6.50854969,2 7.8458278,2 Z"
      />
    );
  },
  Bookmark: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M10.8,15.4 C11.5111111,14.8666667 12.4888889,14.8666667 13.2,15.4 L18,19 L18,5 C18,4.44771525 17.5522847,4 17,4 L7,4 C6.44771525,4 6,4.44771525 6,5 L6,19 L10.8,15.4 Z M7,2 L17,2 C18.6568542,2 20,3.34314575 20,5 L20,21 C20,21.5522847 19.5522847,22 19,22 C18.7836298,22 18.5730962,21.9298221 18.4,21.8 L12,17 L5.6,21.8 C5.1581722,22.1313708 4.53137085,22.0418278 4.2,21.6 C4.07017787,21.4269038 4,21.2163702 4,21 L4,5 C4,3.34314575 5.34314575,2 7,2 Z"
      />
    );
  },
  Bulk: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M8,20 L16,20 C16.55,20 17,19.55 17,19 L17,6 C17,5.45 16.55,5 16,5 L8,5 C7.45,5 7,5.45 7,6 L7,19 C7,19.55 7.45,20 8,20 Z M3,18 L5,18 C5.55,18 6,17.55 6,17 L6,8 C6,7.45 5.55,7 5,7 L3,7 C2.45,7 2,7.45 2,8 L2,17 C2,17.55 2.45,18 3,18 Z M18,8 L18,17 C18,17.55 18.45,18 19,18 L21,18 C21.55,18 22,17.55 22,17 L22,8 C22,7.45 21.55,7 21,7 L19,7 C18.45,7 18,7.45 18,8 Z"
      />
    );
  },
  CameraSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M3.5638852,6 L7,6 L9,3 L15,3 L17,6 L20.4361148,6 C21.3276335,6 21.6509198,6.09282561 21.9768457,6.2671327 C22.3027716,6.4414398 22.5585602,6.69722837 22.7328673,7.0231543 C22.9071744,7.34908022 23,7.67236646 23,8.5638852 L23,18.4361148 C23,19.3276335 22.9071744,19.6509198 22.7328673,19.9768457 C22.5585602,20.3027716 22.3027716,20.5585602 21.9768457,20.7328673 C21.6509198,20.9071744 21.3276335,21 20.4361148,21 L3.5638852,21 C2.67236646,21 2.34908022,20.9071744 2.0231543,20.7328673 C1.69722837,20.5585602 1.4414398,20.3027716 1.2671327,19.9768457 C1.09282561,19.6509198 1,19.3276335 1,18.4361148 L1,8.5638852 C1,7.67236646 1.09282561,7.34908022 1.2671327,7.0231543 C1.4414398,6.69722837 1.69722837,6.4414398 2.0231543,6.2671327 C2.34908022,6.09282561 2.67236646,6 3.5638852,6 Z M10,13 C10,11.8912362 10.8912362,11 12,11 C13.1087638,11 14,11.8912362 14,13 C14,14.1087638 13.1087638,15 12,15 C10.8912362,15 10,14.1087638 10,13 Z M8,13 C8,15.2133333 9.78666667,17 12,17 C14.2133333,17 16,15.2133333 16,13 C16,10.7866667 14.2133333,9 12,9 C9.78666667,9 8,10.7866667 8,13 Z"
      />
    );
  },
  Camera: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M13.9296325,5 L10.0703675,5 L8.66410059,7.10940039 C8.29316903,7.66579773 7.66870638,8 7,8 L3,8 L3,19 L21,19 L21,8 L17,8 C16.3312936,8 15.706831,7.66579773 15.3358994,7.10940039 L13.9296325,5 Z M3,6 L7,6 L9,3 L15,3 L17,6 L21,6 C22.1045695,6 23,6.8954305 23,8 L23,19 C23,20.1045695 22.1045695,21 21,21 L3,21 C1.8954305,21 1,20.1045695 1,19 L1,8 C1,6.8954305 1.8954305,6 3,6 Z M10,13 C10,11.8912362 10.8912362,11 12,11 C13.1087638,11 14,11.8912362 14,13 C14,14.1087638 13.1087638,15 12,15 C10.8912362,15 10,14.1087638 10,13 Z M8,13 C8,15.2133333 9.78666667,17 12,17 C14.2133333,17 16,15.2133333 16,13 C16,10.7866667 14.2133333,9 12,9 C9.78666667,9 8,10.7866667 8,13 Z"
      />
    );
  },
  Card: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M20,4 L4,4 C2.89,4 2.01,4.89 2.01,6 L2,18 C2,19.11 2.89,20 4,20 L20,20 C21.11,20 22,19.11 22,18 L22,6 C22,4.89 21.11,4 20,4 Z M19,18 L5,18 C4.45,18 4,17.55 4,17 L4,12 L20,12 L20,17 C20,17.55 19.55,18 19,18 Z M20,8 L4,8 L4,6 L20,6 L20,8 Z"
      />
    );
  },
  Checked: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M18.2928932,7.29289322 C18.6834175,6.90236893 19.3165825,6.90236893 19.7071068,7.29289322 C20.0976311,7.68341751 20.0976311,8.31658249 19.7071068,8.70710678 L10.7071068,17.7071068 C10.3165825,18.0976311 9.68341751,18.0976311 9.29289322,17.7071068 L5.29289322,13.7071068 C4.90236893,13.3165825 4.90236893,12.6834175 5.29289322,12.2928932 C5.68341751,11.9023689 6.31658249,11.9023689 6.70710678,12.2928932 L10,15.5857864 L18.2928932,7.29289322 Z"
      />
    );
  },
  CloseSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M13.4142136,12 L15.7071068,9.70710678 C16.0976311,9.31658249 16.0976311,8.68341751 15.7071068,8.29289322 C15.3165825,7.90236893 14.6834175,7.90236893 14.2928932,8.29289322 L12,10.5857864 L9.70710678,8.29289322 C9.31658249,7.90236893 8.68341751,7.90236893 8.29289322,8.29289322 C7.90236893,8.68341751 7.90236893,9.31658249 8.29289322,9.70710678 L10.5857864,12 L8.29289322,14.2928932 C7.90236893,14.6834175 7.90236893,15.3165825 8.29289322,15.7071068 C8.68341751,16.0976311 9.31658249,16.0976311 9.70710678,15.7071068 L12,13.4142136 L14.2928932,15.7071068 C14.6834175,16.0976311 15.3165825,16.0976311 15.7071068,15.7071068 C16.0976311,15.3165825 16.0976311,14.6834175 15.7071068,14.2928932 L13.4142136,12 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z"
      />
    );
  },
  Close: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,20 C16.418278,20 20,16.418278 20,12 C20,7.581722 16.418278,4 12,4 C7.581722,4 4,7.581722 4,12 C4,16.418278 7.581722,20 12,20 Z M13.4142136,12 L15.7071068,9.70710678 C16.0976311,9.31658249 16.0976311,8.68341751 15.7071068,8.29289322 C15.3165825,7.90236893 14.6834175,7.90236893 14.2928932,8.29289322 L12,10.5857864 L9.70710678,8.29289322 C9.31658249,7.90236893 8.68341751,7.90236893 8.29289322,8.29289322 C7.90236893,8.68341751 7.90236893,9.31658249 8.29289322,9.70710678 L10.5857864,12 L8.29289322,14.2928932 C7.90236893,14.6834175 7.90236893,15.3165825 8.29289322,15.7071068 C8.68341751,16.0976311 9.31658249,16.0976311 9.70710678,15.7071068 L12,13.4142136 L14.2928932,15.7071068 C14.6834175,16.0976311 15.3165825,16.0976311 15.7071068,15.7071068 C16.0976311,15.3165825 16.0976311,14.6834175 15.7071068,14.2928932 L13.4142136,12 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z"
      />
    );
  },
  Coin: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm0 3c-4.971 0-9 4.029-9 9s4.029 9 9 9 9-4.029 9-9-4.029-9-9-9zm1 13.947v1.053h-1v-.998c-1.035-.018-2.106-.265-3-.727l.455-1.644c.956.371 2.229.765 3.225.54 1.149-.26 1.385-1.442.114-2.011-.931-.434-3.778-.805-3.778-3.243 0-1.363 1.039-2.583 2.984-2.85v-1.067h1v1.018c.725.019 1.535.145 2.442.42l-.362 1.648c-.768-.27-1.616-.515-2.442-.465-1.489.087-1.62 1.376-.581 1.916 1.711.804 3.943 1.401 3.943 3.546.002 1.718-1.344 2.632-3 2.864z"
      />
    )
  },
  Cross: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M13.4142136,12 L17.7071068,16.2928932 C18.0976311,16.6834175 18.0976311,17.3165825 17.7071068,17.7071068 C17.3165825,18.0976311 16.6834175,18.0976311 16.2928932,17.7071068 L12,13.4142136 L7.70710678,17.7071068 C7.31658249,18.0976311 6.68341751,18.0976311 6.29289322,17.7071068 C5.90236893,17.3165825 5.90236893,16.6834175 6.29289322,16.2928932 L10.5857864,12 L6.29289322,7.70710678 C5.90236893,7.31658249 5.90236893,6.68341751 6.29289322,6.29289322 C6.68341751,5.90236893 7.31658249,5.90236893 7.70710678,6.29289322 L12,10.5857864 L16.2928932,6.29289322 C16.6834175,5.90236893 17.3165825,5.90236893 17.7071068,6.29289322 C18.0976311,6.68341751 18.0976311,7.31658249 17.7071068,7.70710678 L13.4142136,12 Z"
      />
    );
  },
  DeleteSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M5,7 L4,7 C3.44771525,7 3,6.55228475 3,6 C3,5.44771525 3.44771525,5 4,5 L8,5 L8,3 C8,2.44771525 8.44771525,2 9,2 L15,2 C15.5522847,2 16,2.44771525 16,3 L16,5 L20,5 C20.5522847,5 21,5.44771525 21,6 C21,6.55228475 20.5522847,7 20,7 L19,7 L19,20.5 C19,21.3284271 18.3284271,22 17.5,22 L6.5,22 C5.67157288,22 5,21.3284271 5,20.5 L5,7 Z M11,17 L11,11 C11,10.4477153 10.5522847,10 10,10 C9.44771525,10 9,10.4477153 9,11 L9,17 C9,17.5522847 9.44771525,18 10,18 C10.5522847,18 11,17.5522847 11,17 Z M15,17 L15,11 C15,10.4477153 14.5522847,10 14,10 C13.4477153,10 13,10.4477153 13,11 L13,17 C13,17.5522847 13.4477153,18 14,18 C14.5522847,18 15,17.5522847 15,17 Z"
      />
    );
  },
  Delete: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M4,5 L8,5 L8,3 C8,2.44771525 8.44771525,2 9,2 L15,2 C15.5522847,2 16,2.44771525 16,3 L16,5 L20,5 C20.5522847,5 21,5.44771525 21,6 C21,6.55228475 20.5522847,7 20,7 L19,7 L19,20.5 C19,21.3284271 18.3284271,22 17.5,22 L6.5,22 C5.67157288,22 5,21.3284271 5,20.5 L5,7 L4,7 C3.44771525,7 3,6.55228475 3,6 C3,5.44771525 3.44771525,5 4,5 Z M11,17 L11,11 C11,10.4477153 10.5522847,10 10,10 C9.44771525,10 9,10.4477153 9,11 L9,17 C9,17.5522847 9.44771525,18 10,18 C10.5522847,18 11,17.5522847 11,17 Z M15,17 L15,11 C15,10.4477153 14.5522847,10 14,10 C13.4477153,10 13,10.4477153 13,11 L13,17 C13,17.5522847 13.4477153,18 14,18 C14.5522847,18 15,17.5522847 15,17 Z M10,4 L10,5 L14,5 L14,4 L10,4 Z M17,7 L7,7 L7,20 L17,20 L17,7 Z"
      />
    );
  },
  Dot: ({ color, ...rest }) => {
    return (
      <circle cx="12" cy="12" r="5" fill={color} {...rest} fillRule="evenodd" />
    );
  },
  DownArrowCircle: ({ color, secColor, ...rest }) => {
    return (
      <g fill="none" fillRule="evenodd">
        <circle cx="12" cy="12" r="12" fill={secColor || "#F7F8F9"} {...rest} />
        <path
          fill={color}
          {...rest}
          d="M12,13.5857864 L16.2928932,9.29289322 C16.6834175,8.90236893 17.3165825,8.90236893 17.7071068,9.29289322 C18.0976311,9.68341751 18.0976311,10.3165825 17.7071068,10.7071068 L12.7071068,15.7071068 C12.3165825,16.0976311 11.6834175,16.0976311 11.2928932,15.7071068 L6.29289322,10.7071068 C5.90236893,10.3165825 5.90236893,9.68341751 6.29289322,9.29289322 C6.68341751,8.90236893 7.31658249,8.90236893 7.70710678,9.29289322 L12,13.5857864 Z"
        />
      </g>
    );
  },
  DownArrowSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12.7071068,16.7071068 L16.7071068,12.7071068 C17.0976311,12.3165825 17.0976311,11.6834175 16.7071068,11.2928932 C16.3165825,10.9023689 15.6834175,10.9023689 15.2928932,11.2928932 L13,13.5857864 L13,8 C13,7.44771525 12.5522847,7 12,7 C11.4477153,7 11,7.44771525 11,8 L11,13.5857864 L8.70710678,11.2928932 C8.31658249,10.9023689 7.68341751,10.9023689 7.29289322,11.2928932 C6.90236893,11.6834175 6.90236893,12.3165825 7.29289322,12.7071068 L11.2928932,16.7071068 C11.4738576,16.8880712 11.7238576,17 12,17 C12.2761424,17 12.5261424,16.8880712 12.7071068,16.7071068 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z"
      />
    );
  },
  DownArrow: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,13.5857864 L16.2928932,9.29289322 C16.6834175,8.90236893 17.3165825,8.90236893 17.7071068,9.29289322 C18.0976311,9.68341751 18.0976311,10.3165825 17.7071068,10.7071068 L12.7071068,15.7071068 C12.3165825,16.0976311 11.6834175,16.0976311 11.2928932,15.7071068 L6.29289322,10.7071068 C5.90236893,10.3165825 5.90236893,9.68341751 6.29289322,9.29289322 C6.68341751,8.90236893 7.31658249,8.90236893 7.70710678,9.29289322 L12,13.5857864 Z"
      />
    );
  },
  Down: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M11,15.5857864 L11,6 C11,5.44771525 11.4477153,5 12,5 C12.5522847,5 13,5.44771525 13,6 L13,15.5857864 L16.2928932,12.2928932 C16.6834175,11.9023689 17.3165825,11.9023689 17.7071068,12.2928932 C18.0976311,12.6834175 18.0976311,13.3165825 17.7071068,13.7071068 L12.7071068,18.7071068 C12.3165825,19.0976311 11.6834175,19.0976311 11.2928932,18.7071068 L6.29289322,13.7071068 C5.90236893,13.3165825 5.90236893,12.6834175 6.29289322,12.2928932 C6.68341751,11.9023689 7.31658249,11.9023689 7.70710678,12.2928932 L11,15.5857864 Z"
      />
    );
  },
  Download: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M11,12.5857864 L11,3 C11,2.44771525 11.4477153,2 12,2 C12.5522847,2 13,2.44771525 13,3 L13,12.5857864 L16.2928932,9.29289322 C16.6834175,8.90236893 17.3165825,8.90236893 17.7071068,9.29289322 C18.0976311,9.68341751 18.0976311,10.3165825 17.7071068,10.7071068 L12.7071068,15.7071068 C12.3165825,16.0976311 11.6834175,16.0976311 11.2928932,15.7071068 L6.29289322,10.7071068 C5.90236893,10.3165825 5.90236893,9.68341751 6.29289322,9.29289322 C6.68341751,8.90236893 7.31658249,8.90236893 7.70710678,9.29289322 L11,12.5857864 Z M20,16 C20,15.4477153 20.4477153,15 21,15 C21.5522847,15 22,15.4477153 22,16 L22,19.5 C22,20.8807119 20.8807119,22 19.5,22 L4.5,22 C3.11928813,22 2,20.8807119 2,19.5 L2,16 C2,15.4477153 2.44771525,15 3,15 C3.55228475,15 4,15.4477153 4,16 L4,19.5 C4,19.7761424 4.22385763,20 4.5,20 L19.5,20 C19.7761424,20 20,19.7761424 20,19.5 L20,16 Z"
      />
    );
  },
  Draft: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M13.4648162,14 L15.1679497,11.4452998 C15.3534155,11.1671011 15.6656468,11 16,11 L21,11 C21.5522847,11 22,11.4477153 22,12 L22,18 C22,19.6568542 20.6568542,21 19,21 L5,21 C3.34314575,21 2,19.6568542 2,18 L2,12 C2,11.4477153 2.44771525,11 3,11 L8,11 C8.33435319,11 8.64658452,11.1671011 8.83205029,11.4452998 L10.5351838,14 L13.4648162,14 Z M4,7 L20,7 C20.5522847,7 21,7.44771525 21,8 C21,8.55228475 20.5522847,9 20,9 L4,9 C3.44771525,9 3,8.55228475 3,8 C3,7.44771525 3.44771525,7 4,7 Z M6,3 L18,3 C18.5522847,3 19,3.44771525 19,4 C19,4.55228475 18.5522847,5 18,5 L6,5 C5.44771525,5 5,4.55228475 5,4 C5,3.44771525 5.44771525,3 6,3 Z"
      />
    );
  },
  Dribbble: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M16.0584606,18.3744488 C15.9815361,17.9109295 15.5792415,15.6547122 14.5885782,12.8852372 C17.0262545,12.4883885 19.1375213,13.1688518 19.2934494,13.2207067 C18.9576842,15.3626315 17.7528795,17.209301 16.0584606,18.3744488 L16.0584606,18.3744488 L16.0584606,18.3744488 Z M11.8400851,19.6866953 C10.0126075,19.6866953 8.33689992,19.0252807 7.03022224,17.9246869 C7.13001624,18.0072315 7.19966413,18.0590864 7.19966413,18.0590864 C7.19966413,18.0590864 8.64667712,14.8461991 13.1238933,13.2577459 C13.1405256,13.2513963 13.1581975,13.2471633 13.1748298,13.241872 C14.2372201,16.0515609 14.6748584,18.4051385 14.7871266,19.0771356 C13.881704,19.468693 12.8848035,19.6866953 11.8400851,19.6866953 L11.8400851,19.6866953 L11.8400851,19.6866953 Z M4.29004534,11.9994709 C4.29004534,11.9179846 4.29420343,11.8375566 4.29628247,11.7571286 C4.43038066,11.7603034 8.16018138,11.8460227 12.0469497,10.6618261 C12.2631701,11.0925392 12.4700347,11.5317185 12.6592275,11.9687812 C12.5594335,11.9962961 12.460679,12.0269857 12.3619245,12.0608501 C8.28804244,13.4006114 6.22147504,17.1320477 6.22147504,17.1320477 C6.22147504,17.1320477 6.2245936,17.1352225 6.2245936,17.1362808 C5.021868,15.774296 4.29004534,13.9741901 4.29004534,11.9994709 L4.29004534,11.9994709 L4.29004534,11.9994709 Z M8.61861006,5.04985596 C8.71944358,5.18637192 10.11552,7.11346934 11.4221977,9.47762949 C7.79738861,10.4586395 4.65075907,10.4194838 4.45221059,10.416309 C4.9418249,8.03521665 6.51254087,6.06155565 8.61861006,5.04985596 L8.61861006,5.04985596 L8.61861006,5.04985596 Z M10.0313189,4.53765653 C10.0313189,4.5387148 10.0302793,4.53977306 10.0302793,4.53977306 C10.0302793,4.53977306 10.0209237,4.54188959 10.0074099,4.54400611 C10.015726,4.54188959 10.0230027,4.54083132 10.0313189,4.53765653 L10.0313189,4.53765653 L10.0313189,4.53765653 Z M16.825627,6.2329943 C16.8069156,6.26156741 15.7320511,7.92833206 12.881685,9.01516844 C11.5916396,6.60126992 10.1737332,4.68369687 10.0604254,4.53130696 C10.6311224,4.39055794 11.2267678,4.31436298 11.8400851,4.31436298 C13.7507244,4.31436298 15.4960798,5.04033159 16.825627,6.2329943 L16.825627,6.2329943 L16.825627,6.2329943 Z M19.3870063,11.9232759 C19.2778566,11.8989359 16.7227144,11.3391146 14.12911,11.6714093 C14.0750549,11.5401846 14.0220393,11.4079017 13.9659052,11.2756188 C13.8089375,10.8988771 13.6394956,10.5263684 13.4638166,10.1602093 C16.4451624,8.92098301 17.6551646,7.13992592 17.6697179,7.11876066 C18.7279501,8.42783232 19.3693344,10.0998883 19.3870063,11.9232759 L19.3870063,11.9232759 L19.3870063,11.9232759 Z M20.5003331,10.1887824 C20.3839068,9.61097066 20.2092673,9.04056676 19.9836912,8.49661944 C19.7633128,7.96325475 19.4878398,7.4489388 19.1687069,6.96848727 C18.8537321,6.4922688 18.4898998,6.04356517 18.0907238,5.63719207 C17.6905083,5.22976071 17.250791,4.86042683 16.7819671,4.5387148 C16.3100246,4.21276971 15.8048175,3.93444647 15.280899,3.70797813 C14.7465853,3.47939326 14.1873231,3.30160503 13.6197447,3.18413781 C13.0376131,3.06243753 12.4388491,3 11.8400851,3 C11.2402816,3 10.6415176,3.06243753 10.0593859,3.18413781 C9.49180755,3.30160503 8.93254535,3.47939326 8.39823164,3.70797813 C7.87431315,3.93444647 7.36910603,4.21276971 6.89612405,4.5387148 C6.42833968,4.86042683 5.98862237,5.22976071 5.58840685,5.63719207 C5.18923086,6.04356517 4.82643809,6.4922688 4.51042376,6.96848727 C4.19129087,7.4489388 3.91581785,7.96325475 3.69543943,8.49661944 C3.46882389,9.04056676 3.29522391,9.61097066 3.17879758,10.1887824 C3.06029221,10.7803516 3,11.3899112 3,11.9994709 C3,12.611147 3.06029221,13.2207067 3.17879758,13.8133341 C3.29522391,14.3911459 3.46882389,14.9604915 3.69543943,15.5033806 C3.91581785,16.0367453 4.19129087,16.5531777 4.51042376,17.0336293 C4.82643809,17.5098477 5.18923086,17.9564348 5.58840685,18.3649245 C5.98862237,18.7712975 6.42833968,19.1395732 6.89612405,19.4623435 C7.36910603,19.7872303 7.87431315,20.0666118 8.39823164,20.2920219 C8.93254535,20.5227233 9.49180755,20.698395 10.0593859,20.8158622 C10.6415176,20.9386207 11.2402816,21 11.8400851,21 C12.4388491,21 13.0376131,20.9386207 13.6197447,20.8158622 C14.1873231,20.698395 14.7465853,20.5227233 15.280899,20.2920219 C15.8048175,20.0666118 16.3100246,19.7872303 16.7819671,19.4623435 C17.250791,19.1395732 17.6905083,18.7712975 18.0907238,18.3649245 C18.4898998,17.9564348 18.8537321,17.5098477 19.1687069,17.0336293 C19.4878398,16.5531777 19.7633128,16.0367453 19.9836912,15.5033806 C20.2092673,14.9604915 20.3839068,14.3911459 20.5003331,13.8133341 C20.6188385,13.2207067 20.6791307,12.611147 20.6791307,11.9994709 C20.6791307,11.3899112 20.6188385,10.7803516 20.5003331,10.1887824 L20.5003331,10.1887824 L20.5003331,10.1887824 Z"
      />
    );
  },
  EditSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M19.2928932,9.29289322 L9.29289322,19.2928932 C9.10535684,19.4804296 8.85100293,19.5857864 8.58578644,19.5857864 L5,19.5857864 C4.44771525,19.5857864 4,19.1380712 4,18.5857864 L4,15 C4,14.7347835 4.10535684,14.4804296 4.29289322,14.2928932 L14.2928932,4.29289322 C14.6834175,3.90236893 15.3165825,3.90236893 15.7071068,4.29289322 L19.2928932,7.87867966 C19.6834175,8.26920395 19.6834175,8.90236893 19.2928932,9.29289322 Z"
      />
    );
  },
  Edit: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M15,6.41421356 L6,15.4142136 L6,17.5857864 L8.17157288,17.5857864 L17.1715729,8.58578644 L15,6.41421356 Z M19.2928932,9.29289322 L9.29289322,19.2928932 C9.10535684,19.4804296 8.85100293,19.5857864 8.58578644,19.5857864 L5,19.5857864 C4.44771525,19.5857864 4,19.1380712 4,18.5857864 L4,15 C4,14.7347835 4.10535684,14.4804296 4.29289322,14.2928932 L14.2928932,4.29289322 C14.6834175,3.90236893 15.3165825,3.90236893 15.7071068,4.29289322 L19.2928932,7.87867966 C19.6834175,8.26920395 19.6834175,8.90236893 19.2928932,9.29289322 Z"
      />
    );
  },
  Email: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M22.0001063,5.98448999 C22.0002481,5.99422236 22.0002479,6.00395091 22.0001063,6.01367238 L22.0001063,18.5 C22.0001063,19.3284271 21.3285334,20 20.5001063,20 L3.50010627,20 C2.67167915,20 2.00010627,19.3284271 2.00010627,18.5 L2.00010627,6.01367238 C1.99996468,6.00395091 1.99996447,5.99422236 2.00010627,5.98448999 L2.00010627,5.5 C2.00010627,4.67157288 2.67167915,4 3.50010627,4 L20.5001063,4 C21.3285334,4 22.0001063,4.67157288 22.0001063,5.5 L22.0001063,5.98448994 Z M4.80288191,6 L12.0001063,10.7981496 L19.1973306,6 L4.80288191,6 Z M20.0001063,7.86851709 L12.5548065,12.8320503 C12.2189071,13.0559832 11.7813055,13.0559832 11.4454061,12.8320503 L4.00010627,7.86851709 L4.00010627,18 L20.0001063,18 L20.0001063,7.86851709 Z"
      />
    );
  },
  Expand: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M14.85,4.85 L16.3,6.3 L13.12,9.46 C12.73,9.85 12.73,10.49 13.12,10.88 C13.51,11.27 14.15,11.27 14.54,10.88 L17.7,7.7 L19.15,9.15 C19.46,9.46 20,9.24 20,8.79 L20,4.5 C20,4.22 19.78,4 19.5,4 L15.21,4 C14.76,4 14.54,4.54 14.85,4.85 Z M9.15,19.15 L7.7,17.7 L10.88,14.54 C11.27,14.15 11.27,13.51 10.88,13.12 C10.49,12.73 9.85,12.73 9.46,13.12 L6.3,16.3 L4.85,14.85 C4.54,14.54 4,14.76 4,15.21 L4,19.5 C4,19.78 4.22,20 4.5,20 L8.79,20 C9.24,20 9.46,19.46 9.15,19.15 Z"
      />
    );
  },
  External: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M18.5857864,4 L15,4 C14.4477153,4 14,3.55228475 14,3 C14,2.44771525 14.4477153,2 15,2 L21,2 C21.5522847,2 22,2.44771525 22,3 L22,9 C22,9.55228475 21.5522847,10 21,10 C20.4477153,10 20,9.55228475 20,9 L20,5.41421356 L12.7071068,12.7071068 C12.3165825,13.0976311 11.6834175,13.0976311 11.2928932,12.7071068 C10.9023689,12.3165825 10.9023689,11.6834175 11.2928932,11.2928932 L18.5857864,4 Z M18,13 C18,12.4477153 18.4477153,12 19,12 C19.5522847,12 20,12.4477153 20,13 L20,19.5 C20,20.8807119 18.8807119,22 17.5,22 L4.5,22 C3.11928813,22 2,20.8807119 2,19.5 L2,6.5 C2,5.11928813 3.11928813,4 4.5,4 L11,4 C11.5522847,4 12,4.44771525 12,5 C12,5.55228475 11.5522847,6 11,6 L4.5,6 C4.22385763,6 4,6.22385763 4,6.5 L4,19.5 C4,19.7761424 4.22385763,20 4.5,20 L17.5,20 C17.7761424,20 18,19.7761424 18,19.5 L18,13 Z"
      />
    );
  },
  EyeSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M1,12 C2.903,7.43896104 6.5,4 12,4 C17.5,4 21.097,7.43896104 23,12 C21.097,16.561039 17.5,20 12,20 C6.5,20 2.903,16.561039 1,12 Z M9,12 C9,10.3379028 10.3379028,9 12,9 C13.6620972,9 15,10.3379028 15,12 C15,13.6620972 13.6620972,15 12,15 C10.3379028,15 9,13.6620972 9,12 Z M7,12 C7,14.7666667 9.23333333,17 12,17 C14.7666667,17 17,14.7666667 17,12 C17,9.23333333 14.7666667,7 12,7 C9.23333333,7 7,9.23333333 7,12 Z"
      />
    );
  },
  Eye: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,18 C15.9720575,18 18.9649443,15.8444121 20.8094763,12 C18.9649443,8.15558794 15.9720575,6 12,6 C8.02794253,6 5.03505566,8.15558794 3.19052368,12 C5.03505566,15.8444121 8.02794253,18 12,18 Z M1,12 C2.903,7.43896104 6.5,4 12,4 C17.5,4 21.097,7.43896104 23,12 C21.097,16.561039 17.5,20 12,20 C6.5,20 2.903,16.561039 1,12 Z M10,12 C10,10.8912362 10.8912362,10 12,10 C13.1087638,10 14,10.8912362 14,12 C14,13.1087638 13.1087638,14 12,14 C10.8912362,14 10,13.1087638 10,12 Z M8,12 C8,14.2133333 9.78666667,16 12,16 C14.2133333,16 16,14.2133333 16,12 C16,9.78666667 14.2133333,8 12,8 C9.78666667,8 8,9.78666667 8,12 Z"
      />
    );
  },
  Facebook: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M16,9.57483963 L13.2754811,9.57483963 L13.2754811,7.78795438 C13.2754811,7.11689237 13.7202423,6.96044191 14.0334996,6.96044191 C14.3460442,6.96044191 15.9561654,6.96044191 15.9561654,6.96044191 L15.9561654,4.010335 L13.308268,4 C10.3688525,4 9.69992872,6.2002851 9.69992872,7.60833927 L9.69992872,9.57483963 L8,9.57483963 L8,12.6147541 L9.69992872,12.6147541 C9.69992872,16.5160371 9.69992872,21.2166785 9.69992872,21.2166785 L13.2754811,21.2166785 C13.2754811,21.2166785 13.2754811,16.4697078 13.2754811,12.6147541 L15.6881682,12.6147541 L16,9.57483963 Z"
      />
    );
  },
  FolderSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M20.75,6.5 C21.4403559,6.5 22,7.05964406 22,7.75 L22,18.7483993 C22,19.4387552 21.4403559,19.9983993 20.75,19.9983993 L3.25,19.9983993 C2.55964406,19.9983993 2,19.4387552 2,18.7483993 L2,5.25 C2,4.55964406 2.55964406,4 3.25,4 L7.99528972,4 C8.35574295,4 8.69865047,4.15560054 8.93601059,4.42686924 L10.75,6.5 L20.75,6.5 Z"
      />
    );
  },
  Folder: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M20,8.5 L9.84246355,8.5 L7.65496355,6 L4,6 L4,17.9983993 L20,17.9983993 L20,8.5 Z M20.75,6.5 C21.4403559,6.5 22,7.05964406 22,7.75 L22,18.7483993 C22,19.4387552 21.4403559,19.9983993 20.75,19.9983993 L3.25,19.9983993 C2.55964406,19.9983993 2,19.4387552 2,18.7483993 L2,5.25 C2,4.55964406 2.55964406,4 3.25,4 L7.99528972,4 C8.35574295,4 8.69865047,4.15560054 8.93601059,4.42686924 L10.75,6.5 L20.75,6.5 Z"
      />
    );
  },
  Github: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M17.1663776,4.59746853 C16.3436643,3.18784615 15.2277063,2.07184615 13.8182098,1.24921678 C12.4085035,0.426545455 10.8695245,0.0153146853 9.20018182,0.0153146853 C7.53104895,0.0153146853 5.99156643,0.426671329 4.58215385,1.24921678 C3.17253147,2.0718042 2.05661538,3.18784615 1.2339021,4.59746853 C0.411314685,6.00704895 0,7.54632168 0,9.21524476 C0,11.22 0.584895105,13.0227273 1.75497902,14.6238881 C2.92493706,16.2251748 4.43634965,17.3332028 6.28909091,17.9480979 C6.50475524,17.9881259 6.66440559,17.959972 6.76820979,17.8643497 C6.87205594,17.7686014 6.92391608,17.6486853 6.92391608,17.5051049 C6.92391608,17.4811469 6.92186014,17.2656084 6.91787413,16.8582378 C6.91376224,16.4508671 6.91183217,16.0954825 6.91183217,15.7922517 L6.63629371,15.8399161 C6.46061538,15.8720979 6.23899301,15.8857343 5.97142657,15.8818741 C5.70398601,15.8781399 5.42634965,15.8501119 5.1388951,15.798 C4.85131469,15.7463497 4.58383217,15.6265594 4.33623776,15.4388811 C4.08876923,15.2512028 3.91309091,15.0055385 3.80924476,14.7022657 L3.68945455,14.4266014 C3.60960839,14.2430769 3.4839021,14.0392028 3.31216783,13.8157343 C3.14043357,13.5920559 2.96676923,13.4404196 2.79109091,13.3605734 L2.70721678,13.3005315 C2.65132867,13.2606294 2.59946853,13.2125035 2.55151049,13.1566573 C2.50359441,13.1008112 2.46772028,13.0449231 2.44376224,12.988951 C2.41976224,12.9329371 2.43965035,12.886993 2.50363636,12.850951 C2.56762238,12.8149091 2.68325874,12.7974126 2.85104895,12.7974126 L3.09054545,12.8332028 C3.25027972,12.8652168 3.44786014,12.9608392 3.68353846,13.1206573 C3.91909091,13.2803497 4.11272727,13.487958 4.26448951,13.7433986 C4.44826573,14.0709231 4.66967832,14.3204895 4.92935664,14.4922238 C5.18882517,14.663958 5.45043357,14.7496783 5.71393007,14.7496783 C5.97742657,14.7496783 6.20500699,14.7297063 6.39675524,14.689972 C6.58829371,14.650028 6.768,14.589986 6.93579021,14.5101818 C7.00766434,13.9748811 7.20335664,13.5636503 7.5226993,13.2761958 C7.06753846,13.2283636 6.65832168,13.1563217 6.29483916,13.0605315 C5.93156643,12.9646154 5.55616783,12.808951 5.1688951,12.5931189 C4.78141259,12.3775804 4.45997203,12.1099301 4.20448951,11.7906294 C3.94896503,11.4711608 3.73925874,11.0517483 3.57566434,10.5327692 C3.41198601,10.0135804 3.33012587,9.41467133 3.33012587,8.73587413 C3.33012587,7.76937063 3.64565035,6.94690909 4.27657343,6.26802797 C3.98102098,5.5413986 4.00892308,4.72682517 4.36036364,3.82439161 C4.59197203,3.75243357 4.93544056,3.80643357 5.3906014,3.98605594 C5.84584615,4.16576224 6.17916084,4.31970629 6.39088112,4.44734266 C6.6026014,4.57493706 6.77223776,4.68306294 6.90004196,4.77075524 C7.64290909,4.56318881 8.40952448,4.45938462 9.2000979,4.45938462 C9.99067133,4.45938462 10.7574545,4.56318881 11.5003636,4.77075524 L11.9555664,4.48338462 C12.2668531,4.29163636 12.6344476,4.11591608 13.0574685,3.95618182 C13.4807413,3.79653147 13.8044056,3.75255944 14.0281259,3.82451748 C14.3873706,4.72699301 14.4193846,5.54152448 14.1237483,6.26815385 C14.7546294,6.94703497 15.0702797,7.76970629 15.0702797,8.736 C15.0702797,9.4147972 14.9881259,10.0155944 14.8246993,10.5387692 C14.6610629,11.062028 14.4495524,11.481021 14.1900839,11.7966294 C13.9303217,12.1121958 13.6068252,12.3777483 13.2195524,12.5932448 C12.8321958,12.8089091 12.4566713,12.9645734 12.0933986,13.0604895 C11.729958,13.1564056 11.3207413,13.2284895 10.8655804,13.2764056 C11.2807133,13.6356503 11.4883217,14.2027133 11.4883217,14.9773427 L11.4883217,17.5047692 C11.4883217,17.6483497 11.5382517,17.7682238 11.6381958,17.864014 C11.738014,17.9596364 11.8956503,17.9877902 12.1113147,17.9477203 C13.9643077,17.3329091 15.4757203,16.2248392 16.6456364,14.6235524 C17.8154266,13.0223916 18.4005315,11.2196643 18.4005315,9.21490909 C18.4001119,7.5461958 17.9885874,6.00704895 17.1663776,4.59746853 Z"
        transform="translate(3 2.85)"
      />
    );
  },
  HeartSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M20.4578131,5.54218691 C19.4708558,4.55476617 18.1319762,4 16.73588,4 C15.3397838,4 14.0009043,4.55476617 13.0139469,5.54218691 L11.9997441,6.55638976 L10.9855413,5.54218691 C8.92997437,3.48662009 5.59724201,3.48662011 3.54167516,5.54218697 C1.4861083,7.59775382 1.48610828,10.9304862 3.5416751,12.9860531 L4.55587795,14.0002559 L11.993314,20.2446014 C11.9970324,20.2477233 12.0024558,20.2477233 12.0061741,20.2446014 L19.4436102,14.0002559 L20.4578131,12.9860531 C21.4452338,11.9990957 22,10.6602162 22,9.26411999 C22,7.86802382 21.4452338,6.52914425 20.4578131,5.54218691 Z"
      />
    );
  },
  Heart: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M11.9997441,17.6385638 L18.0909417,12.5244974 L19.0439315,11.5715076 C19.6560764,10.95965 20,10.1296205 20,9.26411999 C20,8.39861947 19.6560764,7.56858999 19.0439315,6.95673237 C18.43141,6.34392359 17.6013805,6 16.73588,6 C15.8703795,6 15.04035,6.34392359 14.4281605,6.95640048 L11.9997441,9.38481688 L9.57132771,6.9564005 C8.29680941,5.68188223 6.230407,5.68188225 4.95588872,6.95640053 C3.68137044,8.23091881 3.68137042,10.2973212 4.95588866,11.5718395 L5.90854655,12.5244974 L11.9997441,17.6385638 Z M11.9997441,6.55638976 L13.0156962,5.54043764 C14.002486,4.55411099 15.3406085,4 16.73588,4 C18.1319762,4 19.4708558,4.55476617 20.4578131,5.54218691 C21.4452338,6.52914425 22,7.86802382 22,9.26411999 C22,10.6602162 21.4452338,11.9990957 20.4578131,12.9860531 L19.4436102,14.0002559 L12.0061741,20.2446014 C12.0036953,20.2466827 9.5202632,18.1652342 4.55587795,14.0002559 L3.5416751,12.9860531 C1.48610828,10.9304862 1.4861083,7.59775382 3.54167516,5.54218697 C5.59724201,3.48662011 8.92997437,3.48662009 10.9855413,5.54218691 L11.9997441,6.55638976 Z"
      />
    );
  },
  History: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M12.9825,18.54 C12.6225,18.82 12.1125,18.82 11.7525,18.54 L5.6025,13.76 C5.2425,13.48 4.7425,13.48 4.3825,13.76 L4.3825,13.76 C3.8725,14.16 3.8725,14.93 4.3825,15.33 L11.1425,20.59 C11.8625,21.15 12.8725,21.15 13.6025,20.59 L20.3625,15.33 C20.8725,14.93 20.8725,14.16 20.3625,13.76 L20.3525,13.75 C19.9925,13.47 19.4925,13.47 19.1325,13.75 L12.9825,18.54 Z M13.6125,15.52 L20.3725,10.26 C20.8825,9.86 20.8825,9.08 20.3725,8.68 L13.6125,3.42 C12.8925,2.86 11.8825,2.86 11.1525,3.42 L4.3925,8.69 C3.8825,9.09 3.8825,9.87 4.3925,10.27 L11.1525,15.53 C11.8725,16.09 12.8925,16.09 13.6125,15.52 Z"
      />
    );
  },
  HomeSolid2: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M10.197852,19.655 L10.197852,14.655 L14.197852,14.655 L14.197852,19.655 C14.197852,20.205 14.647852,20.655 15.197852,20.655 L18.197852,20.655 C18.747852,20.655 19.197852,20.205 19.197852,19.655 L19.197852,12.655 L20.897852,12.655 C21.357852,12.655 21.577852,12.085 21.227852,11.785 L12.867852,4.255 C12.487852,3.915 11.907852,3.915 11.527852,4.255 L3.16785197,11.785 C2.82785197,12.085 3.03785197,12.655 3.49785197,12.655 L5.19785197,12.655 L5.19785197,19.655 C5.19785197,20.205 5.64785197,20.655 6.19785197,20.655 L9.19785197,20.655 C9.74785197,20.655 10.197852,20.205 10.197852,19.655 Z"
      />
    );
  },
  HomeSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M15,22 L15,16 C15,14.3431458 13.6568542,13 12,13 C10.3431458,13 9,14.3431458 9,16 L9,22 L3,22 C2.44771525,22 2,21.5522847 2,21 L2,10.4806248 C2,10.1768411 2.13808972,9.88952822 2.37530495,9.69975604 L11.375305,2.49975604 C11.7405236,2.20758112 12.2594764,2.20758112 12.624695,2.49975604 L21.624695,9.69975604 C21.8619103,9.88952822 22,10.1768411 22,10.4806248 L22,21 C22,21.5522847 21.5522847,22 21,22 L15,22 Z"
      />
    );
  },
  Home: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M2.37530495,9.69975604 L11.375305,2.49975604 C11.7405236,2.20758112 12.2594764,2.20758112 12.624695,2.49975604 L21.624695,9.69975604 C21.8619103,9.88952822 22,10.1768411 22,10.4806248 L22,21 C22,21.5522847 21.5522847,22 21,22 L3,22 C2.44771525,22 2,21.5522847 2,21 L2,10.4806248 C2,10.1768411 2.13808972,9.88952822 2.37530495,9.69975604 Z M16,20 L20,20 L20,10.9612497 L12,4.56124969 L4,10.9612497 L4,20 L8,20 L8,16 C8,13.790861 9.790861,12 12,12 C14.209139,12 16,13.790861 16,16 L16,20 L16,20 Z M14,20 L14,16 C14,14.8954305 13.1045695,14 12,14 C10.8954305,14 10,14.8954305 10,16 L10,20 L14,20 L14,20 Z"
      />
    );
  },
  InfoSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M12,10.5 C11.4477153,10.5 11,10.9477153 11,11.5 L11,16 C11,16.5522847 11.4477153,17 12,17 C12.5522847,17 13,16.5522847 13,16 L13,11.5 C13,10.9477153 12.5522847,10.5 12,10.5 Z M12,7 C11.4477153,7 11,7.44771525 11,8 C11,8.55228475 11.4477153,9 12,9 C12.5522847,9 13,8.55228475 13,8 C13,7.44771525 12.5522847,7 12,7 Z"
      />
    );
  },
  Info: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,20 C16.418278,20 20,16.418278 20,12 C20,7.581722 16.418278,4 12,4 C7.581722,4 4,7.581722 4,12 C4,16.418278 7.581722,20 12,20 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M12,10.5 C11.4477153,10.5 11,10.9477153 11,11.5 L11,15.5 C11,16.0522847 11.4477153,16.5 12,16.5 C12.5522847,16.5 13,16.0522847 13,15.5 L13,11.5 C13,10.9477153 12.5522847,10.5 12,10.5 Z M12,7 C11.4477153,7 11,7.44771525 11,8 C11,8.55228475 11.4477153,9 12,9 C12.5522847,9 13,8.55228475 13,8 C13,7.44771525 12.5522847,7 12,7 Z"
      />
    );
  },
  Instagram: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12.0000179,3 C14.4442532,3 14.7507428,3.01036041 15.7106885,3.05415994 C16.6686336,3.0978523 17.3228757,3.25000744 17.895342,3.47250621 C18.4871715,3.70247161 18.9890799,4.01021155 19.489452,4.51054797 C19.9897884,5.01092011 20.2975284,5.51282845 20.5275295,6.10465802 C20.7499926,6.67712433 20.9021477,7.33136644 20.9458401,8.28931151 C20.9896396,9.24925721 21,9.55574675 21,12.0000179 C21,14.4442532 20.9896396,14.7507428 20.9458401,15.7106885 C20.9021477,16.6686336 20.7499926,17.3228757 20.5275295,17.895342 C20.2975284,18.4871715 19.9897884,18.9890799 19.489452,19.489452 C18.9890799,19.9897884 18.4871715,20.2975284 17.895342,20.5275295 C17.3228757,20.7499926 16.6686336,20.9021477 15.7106885,20.9458401 C14.7507428,20.9896396 14.4442532,21 12.0000179,21 C9.55574675,21 9.24925721,20.9896396 8.28931151,20.9458401 C7.33136644,20.9021477 6.67712433,20.7499926 6.10465802,20.5275295 C5.51282845,20.2975284 5.01092011,19.9897884 4.51054797,19.489452 C4.01021155,18.9890799 3.70247161,18.4871715 3.47250621,17.895342 C3.25000744,17.3228757 3.0978523,16.6686336 3.05415994,15.7106885 C3.01036041,14.7507428 3,14.4442532 3,12.0000179 C3,9.55574675 3.01036041,9.24925721 3.05415994,8.28931151 C3.0978523,7.33136644 3.25000744,6.67712433 3.47250621,6.10465802 C3.70247161,5.51282845 4.01021155,5.01092011 4.51054797,4.51054797 C5.01092011,4.01021155 5.51282845,3.70247161 6.10465802,3.47250621 C6.67712433,3.25000744 7.33136644,3.0978523 8.28931151,3.05415994 C9.24925721,3.01036041 9.55574675,3 12.0000179,3 Z M12.0000179,4.62161872 C9.59690259,4.62161872 9.31224136,4.63080019 8.36322768,4.67409957 C7.48573657,4.71411219 7.00919338,4.86072987 6.69205761,4.98398304 C6.2719608,5.14724883 5.97215193,5.34227465 5.65723115,5.65723115 C5.34227465,5.97215193 5.14724883,6.2719608 4.98398304,6.69205761 C4.86072987,7.00919338 4.71411219,7.48573657 4.67409957,8.36322768 C4.63080019,9.31224136 4.62161872,9.59690259 4.62161872,12.0000179 C4.62161872,14.4030974 4.63080019,14.6877586 4.67409957,15.6367723 C4.71411219,16.5142634 4.86072987,16.9908066 4.98398304,17.3079424 C5.14724883,17.7280392 5.34231037,18.0278481 5.65723115,18.3427688 C5.97215193,18.6577254 6.2719608,18.8527512 6.69205761,19.016017 C7.00919338,19.1392701 7.48573657,19.2858878 8.36322768,19.3259004 C9.31213419,19.3691998 9.59672397,19.3783813 12.0000179,19.3783813 C14.403276,19.3783813 14.6879015,19.3691998 15.6367723,19.3259004 C16.5142634,19.2858878 16.9908066,19.1392701 17.3079424,19.016017 C17.7280392,18.8527512 18.0278481,18.6577254 18.3427688,18.3427688 C18.6577254,18.0278481 18.8527512,17.7280392 19.016017,17.3079424 C19.1392701,16.9908066 19.2858878,16.5142634 19.3259004,15.6367723 C19.3691998,14.6877586 19.3783813,14.4030974 19.3783813,12.0000179 C19.3783813,9.59690259 19.3691998,9.31224136 19.3259004,8.36322768 C19.2858878,7.48573657 19.1392701,7.00919338 19.016017,6.69205761 C18.8527512,6.2719608 18.6577254,5.97215193 18.3427688,5.65723115 C18.0278481,5.34227465 17.7280392,5.14724883 17.3079424,4.98398304 C16.9908066,4.86072987 16.5142634,4.71411219 15.6367723,4.67409957 C14.6877586,4.63080019 14.4030974,4.62161872 12.0000179,4.62161872 Z M12.0000179,7.37838128 C14.552466,7.37838128 16.6216187,9.44753404 16.6216187,12.0000179 C16.6216187,14.552466 14.552466,16.6216187 12.0000179,16.6216187 C9.44753404,16.6216187 7.37838128,14.552466 7.37838128,12.0000179 C7.37838128,9.44753404 9.44753404,7.37838128 12.0000179,7.37838128 Z M12.0000179,15 C13.656862,15 15,13.656862 15,12.0000179 C15,10.343138 13.656862,9 12.0000179,9 C10.343138,9 9,10.343138 9,12.0000179 C9,13.656862 10.343138,15 12.0000179,15 Z M17.8842313,7.19578796 C17.8842313,7.79226184 17.4006859,8.27577152 16.804212,8.27577152 C16.2077739,8.27577152 15.7242285,7.79226184 15.7242285,7.19578796 C15.7242285,6.59931407 16.2077739,6.11576867 16.804212,6.11576867 C17.4006859,6.11576867 17.8842313,6.59931407 17.8842313,7.19578796 Z"
      />
    );
  },
  LeftArrowSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M7.29289322,12.7071068 L11.2928932,16.7071068 C11.6834175,17.0976311 12.3165825,17.0976311 12.7071068,16.7071068 C13.0976311,16.3165825 13.0976311,15.6834175 12.7071068,15.2928932 L10.4142136,13 L16,13 C16.5522847,13 17,12.5522847 17,12 C17,11.4477153 16.5522847,11 16,11 L10.4142136,11 L12.7071068,8.70710678 C13.0976311,8.31658249 13.0976311,7.68341751 12.7071068,7.29289322 C12.3165825,6.90236893 11.6834175,6.90236893 11.2928932,7.29289322 L7.29289322,11.2928932 C7.11192881,11.4738576 7,11.7238576 7,12 C7,12.2761424 7.11192881,12.5261424 7.29289322,12.7071068 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z"
      />
    );
  },
  LeftArrow: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M10.4142136,12 L14.7071068,16.2928932 C15.0976311,16.6834175 15.0976311,17.3165825 14.7071068,17.7071068 C14.3165825,18.0976311 13.6834175,18.0976311 13.2928932,17.7071068 L8.29289322,12.7071068 C7.90236893,12.3165825 7.90236893,11.6834175 8.29289322,11.2928932 L13.2928932,6.29289322 C13.6834175,5.90236893 14.3165825,5.90236893 14.7071068,6.29289322 C15.0976311,6.68341751 15.0976311,7.31658249 14.7071068,7.70710678 L10.4142136,12 Z"
      />
    );
  },
  LeftUp: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M9,10.4142136 L9,15 C9,15.5522847 8.55228475,16 8,16 C7.44771525,16 7,15.5522847 7,15 L7,8 C7,7.44771525 7.44771525,7 8,7 L15,7 C15.5522847,7 16,7.44771525 16,8 C16,8.55228475 15.5522847,9 15,9 L10.4142136,9 L16.7071068,15.2928932 C17.0976311,15.6834175 17.0976311,16.3165825 16.7071068,16.7071068 C16.3165825,17.0976311 15.6834175,17.0976311 15.2928932,16.7071068 L9,10.4142136 Z"
      />
    );
  },
  Link: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M3.95433801,11.38 C4.23433801,9.91 5.61433801,8.9 7.11433801,8.9 L10.044338,8.9 C10.564338,8.9 10.994338,8.47 10.994338,7.95 L10.994338,7.95 C10.994338,7.43 10.564338,7 10.044338,7 L7.21433801,7 C4.60433801,7 2.27433801,8.91 2.02433801,11.51 C1.73433801,14.49 4.07433801,17 6.99433801,17 L10.044338,17 C10.564338,17 10.994338,16.57 10.994338,16.05 L10.994338,16.05 C10.994338,15.53 10.564338,15.1 10.044338,15.1 L6.99433801,15.1 C5.08433801,15.1 3.57433801,13.36 3.95433801,11.38 Z M8.99433801,13 L14.994338,13 C15.544338,13 15.994338,12.55 15.994338,12 L15.994338,12 C15.994338,11.45 15.544338,11 14.994338,11 L8.99433801,11 C8.44433801,11 7.99433801,11.45 7.99433801,12 L7.99433801,12 C7.99433801,12.55 8.44433801,13 8.99433801,13 Z M16.774338,7 L13.944338,7 C13.424338,7 12.994338,7.43 12.994338,7.95 L12.994338,7.95 C12.994338,8.47 13.424338,8.9 13.944338,8.9 L16.874338,8.9 C18.374338,8.9 19.754338,9.91 20.034338,11.38 C20.414338,13.36 18.904338,15.1 16.994338,15.1 L13.944338,15.1 C13.424338,15.1 12.994338,15.53 12.994338,16.05 L12.994338,16.05 C12.994338,16.57 13.424338,17 13.944338,17 L16.994338,17 C19.914338,17 22.254338,14.49 21.974338,11.51 C21.724338,8.91 19.384338,7 16.774338,7 Z"
      />
    );
  },
  Linkedin: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M7.53439798,19.9999996 L7.53439798,9.56759745 L4.32438473,9.56759745 L4.32438473,19.9999996 L7.53439798,19.9999996 L7.53439798,19.9999996 Z M12.7713547,20 L9.54495313,20 C9.54495313,20 9.59135892,10.503372 9.54495313,9.50464178 L12.7710411,9.50464178 L12.7478383,10.899932 C13.2109556,10.1736399 14.0450059,9.1065638 15.9451357,9.1065638 C18.2626042,9.1065638 20,10.6278087 20,13.8970679 L20,20 L16.7683223,19.9999999 L16.7683223,14.0561214 C16.7683223,12.6255842 15.9689657,11.8989773 14.6943738,11.8989773 C13.7214195,11.8989773 13.1419741,12.5572385 12.8873692,13.1931378 C12.7942441,13.4199071 12.7713547,13.7380143 12.7713547,14.0561215 L12.7713547,20 Z M5.91549304,7.83098607 C4.85759544,7.83098607 4,6.97339063 4,5.91549304 C4,4.85759544 4.85759544,4 5.91549304,4 C6.97339063,4 7.83098607,4.85759544 7.83098607,5.91549304 C7.83098607,6.97339063 6.97339063,7.83098607 5.91549304,7.83098607 Z"
      />
    );
  },
  Loading: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M3,12 C3,11.4477153 3.44771525,11 4,11 C4.55228475,11 5,11.4477153 5,12 C5,13.8826787 5.74532135,15.6448163 7.05025253,16.9497475 C8.35518372,18.2546787 10.1173213,19 12,19 C15.8659932,19 19,15.8659932 19,12 C19,8.13400675 15.8659932,5 12,5 C11.0675482,5 10.162173,5.1818644 9.32086761,5.53072194 C8.81070399,5.74226751 8.2256431,5.50018975 8.01409753,4.99002613 C7.80255196,4.47986251 8.04462972,3.89480162 8.55479334,3.68325605 C9.63789587,3.23413437 10.8034866,3 12,3 C16.9705627,3 21,7.02943725 21,12 C21,16.9705627 16.9705627,21 12,21 C9.58143618,21 7.31232472,20.0402468 5.63603897,18.363961 C3.95975322,16.6876753 3,14.4185638 3,12 Z"
      >
        <animateTransform
          attributeName="transform"
          attributeType="XML"
          type="rotate"
          from="0 12 12"
          to="360 12 12"
          dur="0.8s"
          repeatCount="indefinite"
        />
      </path>
    );
  },
  Loading2: ({ color, ...rest }) => {
    return (
      <>
        <circle cx="5" cy="12" r="2" fill={color} {...rest}>
          <animateTransform
            attributeName="transform"
            dur="1s"
            type="translate"
            values="0 4 ; 0 -4; 0 4"
            repeatCount="indefinite"
            begin="0.1"
          />
        </circle>
        <circle cx="12" cy="12" r="2" fill={color} {...rest}>
          <animateTransform
            attributeName="transform"
            dur="1s"
            type="translate"
            values="0 3 ; 0 -3; 0 3"
            repeatCount="indefinite"
            begin="0.2"
          />
        </circle>
        <circle cx="19" cy="12" r="2" fill={color} {...rest}>
          <animateTransform
            attributeName="transform"
            dur="1s"
            type="translate"
            values="0 2 ; 0 -2; 0 2"
            repeatCount="indefinite"
            begin="0.3"
          />
        </circle>
      </>
    );
  },
  Loading3: ({ color, ...rest }) => {
    return (
      <>
        <circle cx="5" cy="12" r="2" fill={color} {...rest}>
          <animate
            attributeName="opacity"
            dur="1s"
            values="0;1;0"
            repeatCount="indefinite"
            begin="0.1"
          />
        </circle>
        <circle cx="12" cy="12" r="2" fill={color} {...rest}>
          <animate
            attributeName="opacity"
            dur="1s"
            values="0;1;0"
            repeatCount="indefinite"
            begin="0.2"
          />
        </circle>
        <circle cx="19" cy="12" r="2" fill={color} {...rest}>
          <animate
            attributeName="opacity"
            dur="1s"
            values="0;1;0"
            repeatCount="indefinite"
            begin="0.3"
          />
        </circle>
      </>
    );
  },
  LocationSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M11.7190978,22.811 C5.90636593,18.8635961 3,14.5932627 3,9.99999987 C3.00000007,5.02943717 7.0294373,1 12,1 C16.9705627,1 20.9999999,5.02943717 21,9.99999987 C21,14.5932656 18.0936304,18.8636014 12.2808913,22.8110074 C12.1113248,22.9261486 11.8886593,22.9261485 11.7190978,22.811 Z M12,13 C13.6568542,13 15,11.6568542 15,10 C15,8.34314575 13.6568542,7 12,7 C10.3431458,7 9,8.34314575 9,10 C9,11.6568542 10.3431458,13 12,13 Z"
      />
    );
  },
  Location: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,11.9999999 C13.1045695,11.9999999 14,11.1045694 14,9.99999988 C14,8.89543038 13.1045695,7.99999988 12,7.99999988 C10.8954305,7.99999988 10,8.89543038 10,9.99999988 C10,11.1045694 10.8954305,11.9999999 12,11.9999999 Z M11.7190978,22.811 C5.90636593,18.8635961 3,14.5932627 3,9.99999987 C3.00000007,5.02943717 7.0294373,1 12,1 C16.9705627,1 20.9999999,5.02943717 21,9.99999987 C21,14.5932656 18.0936304,18.8636014 12.2808913,22.8110074 C12.1113248,22.9261486 11.8886593,22.9261485 11.7190978,22.811 Z M19,9.9999999 C18.9999999,6.13400669 15.8659932,3 12,3 C8.13400679,3 5.00000006,6.13400669 5,9.99999987 C5,13.6245185 7.26023824,17.142669 12,20.5664732 C16.7397617,17.142669 19,13.6245185 19,9.9999999 Z M12,13.9999999 C9.790861,13.9999999 8,12.2091389 8,9.99999988 C8,7.79086088 9.790861,5.99999988 12,5.99999988 C14.209139,5.99999988 16,7.79086088 16,9.99999988 C16,12.2091389 14.209139,13.9999999 12,13.9999999 Z"
      />
    );
  },
  LockSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M7,9 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,9 L19,9 C19.5522847,9 20,9.44771525 20,10 L20,20 C20,20.5522847 19.5522847,21 19,21 L5,21 C4.44771525,21 4,20.5522847 4,20 L4,10 C4,9.44771525 4.44771525,9 5,9 L7,9 Z M9,9 L15,9 L15,8 C15,6.34314575 13.6568542,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,9 Z M12,13 C11.4477153,13 11,13.4477153 11,14 L11,16 C11,16.5522847 11.4477153,17 12,17 C12.5522847,17 13,16.5522847 13,16 L13,14 C13,13.4477153 12.5522847,13 12,13 Z"
      />
    );
  },
  Lock: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M17,10 L7,10 L6.5,10 C6.22385763,10 6,10.2238576 6,10.5 L6,18.5 C6,18.7761424 6.22385763,19 6.5,19 L17.5,19 C17.7761424,19 18,18.7761424 18,18.5 L18,10.5 C18,10.2238576 17.7761424,10 17.5,10 L17,10 Z M7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L19,8 C19.5522847,8 20,8.44771525 20,9 L20,20 C20,20.5522847 19.5522847,21 19,21 L5,21 C4.44771525,21 4,20.5522847 4,20 L4,9 C4,8.44771525 4.44771525,8 5,8 L7,8 Z M15,8 C15,6.34314575 13.6568542,5 12,5 C10.3431458,5 9,6.34314575 9,8 L15,8 Z M12,13 C12.5522847,13 13,13.4477153 13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 C11,13.4477153 11.4477153,13 12,13 Z"
      />
    );
  },
  Logout: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M10.79,16.29 L10.79,16.29 C11.18,16.68 11.81,16.68 12.2,16.29 L15.79,12.7 C16.18,12.31 16.18,11.68 15.79,11.29 L12.2,7.7 C11.81,7.31 11.18,7.31 10.79,7.7 L10.79,7.7 C10.4,8.09 10.4,8.72 10.79,9.11 L12.67,11 L4,11 C3.45,11 3,11.45 3,12 L3,12 C3,12.55 3.45,13 4,13 L12.67,13 L10.79,14.88 C10.4,15.27 10.41,15.91 10.79,16.29 Z M19,3 L5,3 C3.89,3 3,3.9 3,5 L3,8 C3,8.55 3.45,9 4,9 L4,9 C4.55,9 5,8.55 5,8 L5,6 C5,5.45 5.45,5 6,5 L18,5 C18.55,5 19,5.45 19,6 L19,18 C19,18.55 18.55,19 18,19 L6,19 C5.45,19 5,18.55 5,18 L5,16 C5,15.45 4.55,15 4,15 L4,15 C3.45,15 3,15.45 3,16 L3,19 C3,20.1 3.9,21 5,21 L19,21 C20.1,21 21,20.1 21,19 L21,5 C21,3.9 20.1,3 19,3 Z"
      />
    );
  },
  LongLeft: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M8.41421356,11 L18,11 C18.5522847,11 19,11.4477153 19,12 C19,12.5522847 18.5522847,13 18,13 L8.41421356,13 L11.7071068,16.2928932 C12.0976311,16.6834175 12.0976311,17.3165825 11.7071068,17.7071068 C11.3165825,18.0976311 10.6834175,18.0976311 10.2928932,17.7071068 L5.29289322,12.7071068 C4.90236893,12.3165825 4.90236893,11.6834175 5.29289322,11.2928932 L10.2928932,6.29289322 C10.6834175,5.90236893 11.3165825,5.90236893 11.7071068,6.29289322 C12.0976311,6.68341751 12.0976311,7.31658249 11.7071068,7.70710678 L8.41421356,11 Z"
      />
    );
  },
  LongRight: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M15.5857864,13 L6,13 C5.44771525,13 5,12.5522847 5,12 C5,11.4477153 5.44771525,11 6,11 L15.5857864,11 L12.2928932,7.70710678 C11.9023689,7.31658249 11.9023689,6.68341751 12.2928932,6.29289322 C12.6834175,5.90236893 13.3165825,5.90236893 13.7071068,6.29289322 L18.7071068,11.2928932 C19.0976311,11.6834175 19.0976311,12.3165825 18.7071068,12.7071068 L13.7071068,17.7071068 C13.3165825,18.0976311 12.6834175,18.0976311 12.2928932,17.7071068 C11.9023689,17.3165825 11.9023689,16.6834175 12.2928932,16.2928932 L15.5857864,13 Z"
      />
    );
  },
  Mail: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M21.99,10 C21.99,9.28 21.62,8.65 21.05,8.3 L12,3 L2.95,8.3 C2.38,8.65 2,9.28 2,10 L2,20 C2,21.1 2.9,22 4,22 L20,22 C21.1,22 22,21.1 22,20 L21.99,10 Z M12,15 L3.74,9.84 L12,5 L20.26,9.84 L12,15 Z"
      />
    );
  },
  MasterCard: ({ color, ...rest }) => {
    return (
      <g fill="none" transform="translate(1 5)">
        <path
          fill="#F79F1A"
          d="M21.97667,6.78219639 C21.97667,10.5278996 18.9338906,13.5643924 15.1804314,13.5643924 C11.4269722,13.5643924 8.3841919,10.5278996 8.3841919,6.78219639 C8.3841919,3.03649264 11.4269722,0 15.1804314,0 C18.9338906,0 21.97667,3.03649264 21.97667,6.78219639 L21.97667,6.78219639 Z"
        />
        <path
          fill="#EA001B"
          d="M13.5924781,6.78219639 C13.5924781,10.5278996 10.5496988,13.5643924 6.79623948,13.5643924 C3.04278026,13.5643924 0,10.5278996 0,6.78219639 C0,3.03649264 3.04278026,0 6.79623948,0 C10.5496988,0 13.5924781,3.03649264 13.5924781,6.78219639 L13.5924781,6.78219639 Z"
        />
        <path
          fill="#FF5F01"
          d="M10.9883492,1.44320186 C9.40261474,2.68480141 8.3847202,4.61417194 8.3847202,6.78156091 C8.3847202,8.94894985 9.40261474,10.879793 10.9883492,12.1213926 C12.5740836,10.879793 13.5919782,8.94894985 13.5919782,6.78156091 C13.5919782,4.61417194 12.5740836,2.68480141 10.9883492,1.44320186 L10.9883492,1.44320186 Z"
        />
      </g>
    );
  },
  Menu: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M4,13 C3.44771525,13 3,12.5522847 3,12 C3,11.4477153 3.44771525,11 4,11 L20,11 C20.5522847,11 21,11.4477153 21,12 C21,12.5522847 20.5522847,13 20,13 L4,13 Z M4,7 C3.44771525,7 3,6.55228475 3,6 C3,5.44771525 3.44771525,5 4,5 L20,5 C20.5522847,5 21,5.44771525 21,6 C21,6.55228475 20.5522847,7 20,7 L4,7 Z M4,19 C3.44771525,19 3,18.5522847 3,18 C3,17.4477153 3.44771525,17 4,17 L20,17 C20.5522847,17 21,17.4477153 21,18 C21,18.5522847 20.5522847,19 20,19 L4,19 Z"
      />
    );
  },
  MessageSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M19.65,16.9985025 L8.4,16.9985025 L4.47616596,20.2683642 C4.09431675,20.5865718 3.52680852,20.5349802 3.20860085,20.153131 C3.0738146,19.9913875 3,19.7875079 3,19.576965 L3,5.35 C3,4.60441559 3.60441559,4 4.35,4 L19.65,4 C20.3955844,4 21,4.60441559 21,5.35 L21,15.6485025 C21,16.3940869 20.3955844,16.9985025 19.65,16.9985025 Z"
      />
    );
  },
  Message: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M19,6 L5,6 L5,17.3419209 L7.3796773,15.3936192 C7.69417855,15.1361291 8.09061112,14.9951167 8.5,14.9951167 L19,14.9951167 L19,6 Z M19.65,16.9985025 L8.4,16.9985025 L4.47616596,20.2683642 C4.09431675,20.5865718 3.52680852,20.5349802 3.20860085,20.153131 C3.0738146,19.9913875 3,19.7875079 3,19.576965 L3,5.35 C3,4.60441559 3.60441559,4 4.35,4 L19.65,4 C20.3955844,4 21,4.60441559 21,5.35 L21,15.6485025 C21,16.3940869 20.3955844,16.9985025 19.65,16.9985025 Z"
      />
    );
  },
  Minus: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M6,13 C5.44771525,13 5,12.5522847 5,12 C5,11.4477153 5.44771525,11 6,11 L18,11 C18.5522847,11 19,11.4477153 19,12 C19,12.5522847 18.5522847,13 18,13 L6,13 Z"
      />
    );
  },
  Money: ({ color, ...rest }) => {
    return(
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M11.8,10.9 C9.53,10.31 8.8,9.7 8.8,8.75 C8.8,7.66 9.81,6.9 11.5,6.9 C12.92,6.9 13.63,7.44 13.89,8.3 C14.01,8.7 14.34,9 14.76,9 L15.06,9 C15.72,9 16.19,8.35 15.96,7.73 C15.54,6.55 14.56,5.57 13,5.19 L13,4.5 C13,3.67 12.33,3 11.5,3 C10.67,3 10,3.67 10,4.5 L10,5.16 C8.06,5.58 6.5,6.84 6.5,8.77 C6.5,11.08 8.41,12.23 11.2,12.9 C13.7,13.5 14.2,14.38 14.2,15.31 C14.2,16 13.71,17.1 11.5,17.1 C9.85,17.1 9,16.51 8.67,15.67 C8.52,15.28 8.18,15 7.77,15 L7.49,15 C6.82,15 6.35,15.68 6.6,16.3 C7.17,17.69 8.5,18.51 10,18.83 L10,19.5 C10,20.33 10.67,21 11.5,21 C12.33,21 13,20.33 13,19.5 L13,18.85 C14.95,18.48 16.5,17.35 16.5,15.3 C16.5,12.46 14.07,11.49 11.8,10.9 Z"
      />
    );
  },
  Next: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12.2925,15.4625 C12.6825,15.8525 12.6725,16.4925 12.2925,16.8825 C11.9025,17.2725 11.2725,17.2725 10.8825,16.8825 L6.2925,12.2925 C5.9025,11.9025 5.9025,11.2725 6.2925,10.8825 L10.8825,6.2925 C11.2725,5.9025 11.9025,5.9025 12.2925,6.2925 C12.6825,6.6825 12.6825,7.3125 12.2925,7.7025 L8.4125,11.5825 L12.2925,15.4625 Z M18.2925,15.4625 C18.6825,15.8525 18.6725,16.4925 18.2925,16.8825 C17.9025,17.2725 17.2725,17.2725 16.8825,16.8825 L12.2925,12.2925 C11.9025,11.9025 11.9025,11.2725 12.2925,10.8825 L16.8825,6.2925 C17.2725,5.9025 17.9025,5.9025 18.2925,6.2925 C18.6825,6.6825 18.6825,7.3125 18.2925,7.7025 L14.4125,11.5825 L18.2925,15.4625 Z"
        transform="matrix(-1 0 0 1 24.585 0)"
      />
    );
  },
  NotificationSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M10,3.28595626 L10,3 C10,1.8954305 10.8954305,1 12,1 C13.1045695,1 14,1.8954305 14,3 L14,3.28595626 C17.063626,4.18405038 19.3,7.0939611 19.3,10.5333333 L19.3,14.9999999 C19.3,16.0591846 20.1257333,17 21,17 C22.3333333,17 22.3333333,19 21,19 L3,19 C1.66666667,19 1.66666667,17 3,17 C3.87426666,17 4.7,16.0591846 4.7,14.9999999 L4.7,10.5333333 C4.70000005,7.09396109 6.93637396,4.18405038 10,3.28595626 Z M15,19.9999999 C14.3795415,21.2336086 13.2365352,21.9929125 12,21.9929125 C10.7634648,21.9929125 9.62045855,21.2336086 9,19.9999999 L15,19.9999999 Z"
      />
    );
  },
  Notification: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M17.8194716,17 C17.4896007,16.4014825 17.3,15.7133029 17.3,14.9999999 L17.3,10.5333333 C17.3,7.46938408 14.9188081,5 12,5 C9.08119186,5 6.70000004,7.46938408 6.7,10.5333333 L6.7,14.9999999 C6.7,15.7133029 6.51039933,16.4014825 6.18052835,17 L17.8194716,17 Z M10,3.28595626 L10,3 C10,1.8954305 10.8954305,1 12,1 C13.1045695,1 14,1.8954305 14,3 L14,3.28595626 C17.063626,4.18405038 19.3,7.0939611 19.3,10.5333333 L19.3,14.9999999 C19.3,16.0591846 20.1257333,17 21,17 C22.3333333,17 22.3333333,19 21,19 L3,19 C1.66666667,19 1.66666667,17 3,17 C3.87426666,17 4.7,16.0591846 4.7,14.9999999 L4.7,10.5333333 C4.70000005,7.09396109 6.93637396,4.18405038 10,3.28595626 Z M15,19.9999999 C14.3795415,21.2336086 13.2365352,21.9929125 12,21.9929125 C10.7634648,21.9929125 9.62045855,21.2336086 9,19.9999999 L15,19.9999999 Z"
      />
    );
  },
  OptionsVertical: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,14 C10.8954305,14 10,13.1045695 10,12 C10,10.8954305 10.8954305,10 12,10 C13.1045695,10 14,10.8954305 14,12 C14,13.1045695 13.1045695,14 12,14 Z M12,21 C10.8954305,21 10,20.1045695 10,19 C10,17.8954305 10.8954305,17 12,17 C13.1045695,17 14,17.8954305 14,19 C14,20.1045695 13.1045695,21 12,21 Z M12,7 C10.8954305,7 10,6.1045695 10,5 C10,3.8954305 10.8954305,3 12,3 C13.1045695,3 14,3.8954305 14,5 C14,6.1045695 13.1045695,7 12,7 Z"
      />
    );
  },
  Options: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,14 C10.8954305,14 10,13.1045695 10,12 C10,10.8954305 10.8954305,10 12,10 C13.1045695,10 14,10.8954305 14,12 C14,13.1045695 13.1045695,14 12,14 Z M19,14 C17.8954305,14 17,13.1045695 17,12 C17,10.8954305 17.8954305,10 19,10 C20.1045695,10 21,10.8954305 21,12 C21,13.1045695 20.1045695,14 19,14 Z M5,14 C3.8954305,14 3,13.1045695 3,12 C3,10.8954305 3.8954305,10 5,10 C6.1045695,10 7,10.8954305 7,12 C7,13.1045695 6.1045695,14 5,14 Z"
      />
    );
  },
  Photo: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M19,10.5857864 L19,6 C19,5.44771525 18.5522847,5 18,5 L6,5 C5.44771525,5 5,5.44771525 5,6 L5,14.5857864 L7.29289322,12.2928932 C7.6299576,11.9558288 8.15807761,11.9035347 8.5547002,12.1679497 L10.807036,13.6695069 L15.2317787,8.3598156 C15.6082272,7.9080774 16.2913058,7.87709226 16.7071068,8.29289322 L19,10.5857864 Z M19,13.4142136 L16.0671984,10.4814119 L11.7682213,15.6401844 C11.4410564,16.0327823 10.8705173,16.1155287 10.4452998,15.8320503 L8.12741788,14.2867957 L5,17.4142136 L5,18 C5,18.5522847 5.44771525,19 6,19 L18,19 C18.5522847,19 19,18.5522847 19,18 L19,13.4142136 Z M6,3 L18,3 C19.6568542,3 21,4.34314575 21,6 L21,18 C21,19.6568542 19.6568542,21 18,21 L6,21 C4.34314575,21 3,19.6568542 3,18 L3,6 C3,4.34314575 4.34314575,3 6,3 Z M9.5,10 C8.67157288,10 8,9.32842712 8,8.5 C8,7.67157288 8.67157288,7 9.5,7 C10.3284271,7 11,7.67157288 11,8.5 C11,9.32842712 10.3284271,10 9.5,10 Z"
      />
    );
  },
  Pause: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M14.5,11.9209875 L16.5,11.9209875 L14.5,11.9209875 Z M16.5,11.9209875 C16.5,13.2543208 14.5,13.2543208 14.5,11.9209875 L14.5,7.5 C14.5,6.16666667 16.5,6.16666667 16.5,7.5 L16.5,11.9209875 L16.5,16.3419749 C16.5,17.6753083 14.5,17.6753083 14.5,16.3419749 L14.5,11.9209875 C14.5,10.5876541 16.5,10.5876541 16.5,11.9209875 Z M7.5,11.9209875 L9.5,11.9209875 L7.5,11.9209875 Z M9.5,11.9209875 C9.5,13.2543208 7.5,13.2543208 7.5,11.9209875 L7.5,7.5 C7.5,6.16666667 9.5,6.16666667 9.5,7.5 L9.5,11.9209875 L9.5,16.3419749 C9.5,17.6753083 7.5,17.6753083 7.5,16.3419749 L7.5,11.9209875 C7.5,10.5876541 9.5,10.5876541 9.5,11.9209875 Z"
      />
    );
  },
  Play: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M17.8436076,12.841119 L8.54075759,18.8215226 C8.07618755,19.1201747 7.4574737,18.9856717 7.15882152,18.5211017 C7.05513038,18.3598044 7,18.1720957 7,17.9803441 L7,6.01953696 C7,5.46725221 7.44771525,5.01953696 8,5.01953696 C8.19175162,5.01953696 8.37946026,5.07466734 8.54075759,5.17835848 L17.8436076,11.1587621 C18.3081776,11.4574142 18.4426806,12.0761281 18.1440285,12.5406981 C18.0665625,12.6612008 17.9641102,12.763653 17.8436076,12.841119 Z"
      />
    );
  },
  PlayNext: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M15.5,12.9770244 L7.7703788,17.9460666 C7.53809377,18.0953927 7.22873685,18.0281412 7.07941076,17.7958562 C7.02756519,17.7152075 7,17.6213532 7,17.5254774 L7,6.5 C7,6.22385763 7.22385763,6 7.5,6 C7.59587581,6 7.68973013,6.02756519 7.7703788,6.07941076 L15.5,11.048453 L15.5,7.58416725 C15.5,7.0318825 15.9477153,6.58416725 16.5,6.58416725 C17.0522847,6.58416725 17.5,7.0318825 17.5,7.58416725 L17.5,16.5841673 C17.5,17.136452 17.0522847,17.5841673 16.5,17.5841673 C15.9477153,17.5841673 15.5,17.136452 15.5,16.5841673 L15.5,12.9770244 Z"
      />
    );
  },
  PlayPrev: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M9,12.9770244 L9,16.5841673 C9,17.136452 8.55228475,17.5841673 8,17.5841673 C7.44771525,17.5841673 7,17.136452 7,16.5841673 L7,7.58416725 C7,7.0318825 7.44771525,6.58416725 8,6.58416725 C8.55228475,6.58416725 9,7.0318825 9,7.58416725 L9,11.048453 L16.7296212,6.07941076 C16.8102699,6.02756519 16.9041242,6 17,6 C17.2761424,6 17.5,6.22385763 17.5,6.5 L17.5,17.5254774 C17.5,17.6213532 17.4724348,17.7152075 17.4205892,17.7958562 C17.2712632,18.0281412 16.9619062,18.0953927 16.7296212,17.9460666 L9,12.9770244 Z"
      />
    );
  },
  Plus: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M11,11 L11,6 C11,5.44771525 11.4477153,5 12,5 C12.5522847,5 13,5.44771525 13,6 L13,11 L18,11 C18.5522847,11 19,11.4477153 19,12 C19,12.5522847 18.5522847,13 18,13 L13,13 L13,18 C13,18.5522847 12.5522847,19 12,19 C11.4477153,19 11,18.5522847 11,18 L11,13 L6,13 C5.44771525,13 5,12.5522847 5,12 C5,11.4477153 5.44771525,11 6,11 L11,11 Z"
      />
    );
  },
  Power: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M6.96726387,4.53866185 C7.42512868,4.22982813 8.04666063,4.35064171 8.35549434,4.80850652 C8.66432806,5.26637132 8.54351448,5.88790328 8.08564968,6.19673699 C6.5248383,7.24951756 5.48073844,8.85279179 5.12860972,10.664337 C4.77648099,12.4758823 5.14395642,14.3535389 6.19673699,15.9143503 C8.35857298,19.119404 12.7092967,19.965099 15.9143503,17.803263 C19.119404,15.641427 19.965099,11.2907033 17.803263,8.08564968 C17.2818426,7.31261209 16.6247908,6.66371931 15.8651228,6.16132421 C15.4044637,5.85667418 15.2779933,5.23626855 15.5826433,4.77560945 C15.8872934,4.31495035 16.507699,4.18848001 16.9683581,4.49313004 C17.9463601,5.13991707 18.7922563,5.97530928 19.4613382,6.96726387 C22.2408416,11.0880471 21.1535194,16.6818347 17.0327361,19.4613382 C12.9119529,22.2408416 7.31816526,21.1535194 4.53866185,17.0327361 C3.18621812,15.0276559 2.71301857,12.60979 3.16535535,10.282719 C3.61769212,7.95564807 4.96218359,5.89110557 6.96726387,4.53866185 Z M11,3.5 C11,2.94771525 11.4477153,2.5 12,2.5 C12.5522847,2.5 13,2.94771525 13,3.5 L13,11.5 C13,12.0522847 12.5522847,12.5 12,12.5 C11.4477153,12.5 11,12.0522847 11,11.5 L11,3.5 Z"
      />
    );
  },
  Print: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M19,8 L5,8 C3.34,8 2,9.34 2,11 L2,15 C2,16.1 2.9,17 4,17 L6,17 L6,19 C6,20.1 6.9,21 8,21 L16,21 C17.1,21 18,20.1 18,19 L18,17 L20,17 C21.1,17 22,16.1 22,15 L22,11 C22,9.34 20.66,8 19,8 Z M15,19 L9,19 C8.45,19 8,18.55 8,18 L8,14 L16,14 L16,18 C16,18.55 15.55,19 15,19 Z M19,12 C18.45,12 18,11.55 18,11 C18,10.45 18.45,10 19,10 C19.55,10 20,10.45 20,11 C20,11.55 19.55,12 19,12 Z M17,3 L7,3 C6.45,3 6,3.45 6,4 L6,6 C6,6.55 6.45,7 7,7 L17,7 C17.55,7 18,6.55 18,6 L18,4 C18,3.45 17.55,3 17,3 Z"
      />
    );
  },
  QuestionSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M12,2 C6.48,2 2,6.48 2,12 C2,17.52 6.48,22 12,22 C17.52,22 22,17.52 22,12 C22,6.48 17.52,2 12,2 Z M13,19 L11,19 L11,17 L13,17 L13,19 Z M15.07,11.25 L14.17,12.17 C13.67,12.68 13.31,13.14 13.13,13.86 C13.05,14.18 13,14.54 13,15 L11,15 L11,14.5 C11,14.04 11.08,13.6 11.22,13.19 C11.42,12.61 11.75,12.09 12.17,11.67 L13.41,10.41 C13.87,9.97 14.09,9.31 13.96,8.61 C13.83,7.89 13.27,7.28 12.57,7.08 C11.46,6.77 10.43,7.4 10.1,8.35 C9.98,8.72 9.67,9 9.28,9 L8.98,9 C8.4,9 8,8.44 8.16,7.88 C8.59,6.41 9.84,5.29 11.39,5.05 C12.91,4.81 14.36,5.6 15.26,6.85 C16.44,8.48 16.09,10.23 15.07,11.25 Z"
      />
    );
  },
  Question: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M12,15.5 C11.4477153,15.5 11,15.9477153 11,16.5 C11,17.0522847 11.4477153,17.5 12,17.5 C12.5522847,17.5 13,17.0522847 13,16.5 C13,15.9477153 12.5522847,15.5 12,15.5 Z M8.60000038,9.640625 L10.2171879,9.640625 C10.2875004,8.640625 10.9828129,8.0078125 12.0609379,8.0078125 C13.1312504,8.0078125 13.8265629,8.6328125 13.8265629,9.4921875 C13.8265629,10.28125 13.4906254,10.7265625 12.5453129,11.296875 C11.4750004,11.9375 10.9984379,12.640625 11.0375004,13.765625 L11.0453129,14.3000002 L12.6625004,14.3000002 L12.6625004,13.890625 C12.6625004,13.1015625 12.9515629,12.6875 13.9593754,12.09375 C14.9906254,11.4765625 15.6000004,10.625 15.6000004,9.421875 C15.6000004,7.7265625 14.2093754,6.5 12.1468754,6.5 C9.88125038,6.5 8.67031288,7.84375 8.60000038,9.640625 Z"
      />
    );
  },
  Refresh: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M14.2046214,19.1771466 C14.2741374,19.5713914 13.8752407,19.8833937 13.5093469,19.7209647 L10.4246992,18.3516155 C10.1109231,18.2123227 10.0276405,17.8054991 10.2614386,17.5541102 L12.5612561,15.0968839 C12.8424185,14.794567 13.3131423,14.9015854 13.4458308,15.2393166 L13.7015842,16.3242808 C15.974952,15.4706724 17.5927671,13.2769407 17.5927671,10.7053481 C17.5927671,7.39163961 14.9064756,4.70534811 11.5927671,4.70534811 C8.27905858,4.70534811 5.59276708,7.39163961 5.59276708,10.7053481 C5.59276708,11.829557 5.90148528,12.9058866 6.47660835,13.8417172 C6.76577753,14.3122484 6.61875454,14.9281073 6.14822332,15.2172765 C5.67769209,15.5064456 5.06183324,15.3594227 4.77266406,14.8888914 C4.00532231,13.6402858 3.59276708,12.2019339 3.59276708,10.7053481 C3.59276708,6.28707011 7.17448908,2.70534811 11.5927671,2.70534811 C16.0110451,2.70534811 19.5927671,6.28707011 19.5927671,10.7053481 C19.5927671,14.2651007 17.2677538,17.2818174 14.0534447,18.3197808 L14.2046214,19.1771466 Z"
        transform="rotate(-120 11.593 11.235)"
      />
    );
  },
  RemoveSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M8,13 L16,13 C16.5522847,13 17,12.5522847 17,12 C17,11.4477153 16.5522847,11 16,11 L8,11 C7.44771525,11 7,11.4477153 7,12 C7,12.5522847 7.44771525,13 8,13 Z"
      />
    );
  },
  Remove: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,20 C16.418278,20 20,16.418278 20,12 C20,7.581722 16.418278,4 12,4 C7.581722,4 4,7.581722 4,12 C4,16.418278 7.581722,20 12,20 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M8,13 L16,13 C16.5522847,13 17,12.5522847 17,12 C17,11.4477153 16.5522847,11 16,11 L8,11 C7.44771525,11 7,11.4477153 7,12 C7,12.5522847 7.44771525,13 8,13 Z"
      />
    );
  },
  Rename: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M18,4.41421356 L9,13.4142136 L9,15.5857864 L11.1715729,15.5857864 L20.1715729,6.58578644 L18,4.41421356 Z M22.2928932,7.29289322 L12.2928932,17.2928932 C12.1053568,17.4804296 11.8510029,17.5857864 11.5857864,17.5857864 L8,17.5857864 C7.44771525,17.5857864 7,17.1380712 7,16.5857864 L7,13 C7,12.7347835 7.10535684,12.4804296 7.29289322,12.2928932 L17.2928932,2.29289322 C17.6834175,1.90236893 18.3165825,1.90236893 18.7071068,2.29289322 L22.2928932,5.87867966 C22.6834175,6.26920395 22.6834175,6.90236893 22.2928932,7.29289322 Z M18,14.5857864 C18,14.0335017 18.4477153,13.5857864 19,13.5857864 C19.5522847,13.5857864 20,14.0335017 20,14.5857864 L20,20.0857864 C20,21.4664983 18.8807119,22.5857864 17.5,22.5857864 L4.5,22.5857864 C3.11928813,22.5857864 2,21.4664983 2,20.0857864 L2,7.08578644 C2,5.70507456 3.11928813,4.58578644 4.5,4.58578644 L10,4.58578644 C10.5522847,4.58578644 11,5.03350169 11,5.58578644 C11,6.13807119 10.5522847,6.58578644 10,6.58578644 L4.5,6.58578644 C4.22385763,6.58578644 4,6.80964406 4,7.08578644 L4,20.0857864 C4,20.3619288 4.22385763,20.5857864 4.5,20.5857864 L17.5,20.5857864 C17.7761424,20.5857864 18,20.3619288 18,20.0857864 L18,14.5857864 Z"
      />
    );
  },
  RightArrowSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M16.7071068,11.2928932 L12.7071068,7.29289322 C12.3165825,6.90236893 11.6834175,6.90236893 11.2928932,7.29289322 C10.9023689,7.68341751 10.9023689,8.31658249 11.2928932,8.70710678 L13.5857864,11 L8,11 C7.44771525,11 7,11.4477153 7,12 C7,12.5522847 7.44771525,13 8,13 L13.5857864,13 L11.2928932,15.2928932 C10.9023689,15.6834175 10.9023689,16.3165825 11.2928932,16.7071068 C11.6834175,17.0976311 12.3165825,17.0976311 12.7071068,16.7071068 L16.7071068,12.7071068 C16.8880712,12.5261424 17,12.2761424 17,12 C17,11.7238576 16.8880712,11.4738576 16.7071068,11.2928932 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z"
      />
    );
  },
  RightArrow: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M13.5857864,12 L9.29289322,7.70710678 C8.90236893,7.31658249 8.90236893,6.68341751 9.29289322,6.29289322 C9.68341751,5.90236893 10.3165825,5.90236893 10.7071068,6.29289322 L15.7071068,11.2928932 C16.0976311,11.6834175 16.0976311,12.3165825 15.7071068,12.7071068 L10.7071068,17.7071068 C10.3165825,18.0976311 9.68341751,18.0976311 9.29289322,17.7071068 C8.90236893,17.3165825 8.90236893,16.6834175 9.29289322,16.2928932 L13.5857864,12 Z"
      />
    );
  },
  RightUp: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M13.5857864,9 L9,9 C8.44771525,9 8,8.55228475 8,8 C8,7.44771525 8.44771525,7 9,7 L16,7 C16.5522847,7 17,7.44771525 17,8 L17,15 C17,15.5522847 16.5522847,16 16,16 C15.4477153,16 15,15.5522847 15,15 L15,10.4142136 L8.70710678,16.7071068 C8.31658249,17.0976311 7.68341751,17.0976311 7.29289322,16.7071068 C6.90236893,16.3165825 6.90236893,15.6834175 7.29289322,15.2928932 L13.5857864,9 Z"
      />
    );
  },
  Search: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M11,16 C13.7614237,16 16,13.7614237 16,11 C16,8.23857625 13.7614237,6 11,6 C8.23857625,6 6,8.23857625 6,11 C6,13.7614237 8.23857625,16 11,16 Z M16.6063847,15.1921711 L19.7071068,18.2928932 C20.0976311,18.6834175 20.0976311,19.3165825 19.7071068,19.7071068 C19.3165825,20.0976311 18.6834175,20.0976311 18.2928932,19.7071068 L15.1921711,16.6063847 C14.0235906,17.4815965 12.5723351,18 11,18 C7.13400675,18 4,14.8659932 4,11 C4,7.13400675 7.13400675,4 11,4 C14.8659932,4 18,7.13400675 18,11 C18,12.5723351 17.4815965,14.0235906 16.6063847,15.1921711 Z"
      />
    );
  },
  SettingsSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M9.77777778,2 L14.2222222,2 L14.8148148,4.90865385 L16.8641975,6.08653846 L19.7777778,5.125 L22,8.875 L19.7530864,10.7980769 L19.7530864,13.2019231 L22,15.125 L19.7777778,18.875 L16.8641975,17.9134615 L14.8148148,19.0913462 L14.2222222,22 L9.77777778,22 L9.18518519,19.0913462 L7.13580247,17.9134615 L4.22222222,18.875 L2,15.125 L4.24691358,13.2019231 L4.24691358,10.7980769 L2,8.875 L4.22222222,5.125 L7.13580247,6.08653846 L9.18518519,4.90865385 L9.77777778,2 Z M12,15 C13.6568542,15 15,13.6568542 15,12 C15,10.3431458 13.6568542,9 12,9 C10.3431458,9 9,10.3431458 9,12 C9,13.6568542 10.3431458,15 12,15 Z"
      />
    );
  },
  Settings: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M11.4113948,4 L11.1449265,5.30792041 C11.0303224,5.87043808 10.6795242,6.35658722 10.1818028,6.64265305 L8.13242012,7.82053767 C7.63938589,8.10390955 7.04903358,8.16400171 6.50901407,7.98578471 L5.12282813,7.52831624 L4.57751802,8.44852705 L5.54738534,9.27861215 C5.99134314,9.65858448 6.24691358,10.2137162 6.24691358,10.7980769 L6.24691358,13.2019231 C6.24691358,13.7862838 5.99134314,14.3414155 5.54738534,14.7213879 L4.57751802,15.5514729 L5.12282813,16.4716838 L6.50901407,16.0142153 C7.04903358,15.8359983 7.63938589,15.8960904 8.13242012,16.1794623 L10.1818028,17.3573469 C10.6795242,17.6434128 11.0303224,18.1295619 11.1449265,18.6920796 L11.4113948,20 L12.5886052,20 L12.8550735,18.6920796 C12.9696776,18.1295619 13.3204758,17.6434128 13.8181972,17.3573469 L15.8675799,16.1794623 C16.3606141,15.8960904 16.9509664,15.8359983 17.4909859,16.0142153 L18.8771719,16.4716838 L19.422482,15.5514729 L18.4526147,14.7213879 C18.0086569,14.3414155 17.7530864,13.7862838 17.7530864,13.2019231 L17.7530864,10.7980769 C17.7530864,10.2137162 18.0086569,9.65858448 18.4526147,9.27861215 L19.422482,8.44852705 L18.8771719,7.52831624 L17.4909859,7.98578471 C16.9509664,8.16400171 16.3606141,8.10390955 15.8675799,7.82053767 L13.8181972,6.64265305 C13.3204758,6.35658722 12.9696776,5.87043808 12.8550735,5.30792041 L12.5886052,4 L11.4113948,4 Z M9.77777778,2 L14.2222222,2 L14.8148148,4.90865385 L16.8641975,6.08653846 L19.7777778,5.125 L22,8.875 L19.7530864,10.7980769 L19.7530864,13.2019231 L22,15.125 L19.7777778,18.875 L16.8641975,17.9134615 L14.8148148,19.0913462 L14.2222222,22 L9.77777778,22 L9.18518519,19.0913462 L7.13580247,17.9134615 L4.22222222,18.875 L2,15.125 L4.24691358,13.2019231 L4.24691358,10.7980769 L2,8.875 L4.22222222,5.125 L7.13580247,6.08653846 L9.18518519,4.90865385 L9.77777778,2 Z M11,12 C11,11.4445695 11.4445695,11 12,11 C12.5554305,11 13,11.4445695 13,12 C13,12.5554305 12.5554305,13 12,13 C11.4445695,13 11,12.5554305 11,12 Z M9,12 C9,13.66 10.34,15 12,15 C13.66,15 15,13.66 15,12 C15,10.34 13.66,9 12,9 C10.34,9 9,10.34 9,12 Z"
      />
    );
  },
  StarSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M7.36808291,20.1015012 C6.7388727,20.4346625 6.00157634,19.8989853 6.12399487,19.1976184 L7.02517625,14.0345209 L3.26131268,10.3871885 C2.75002085,9.89172653 3.031643,9.02498267 3.73651203,8.92467531 L8.92538983,8.18626488 L11.2311113,3.4795302 C11.5443258,2.8401566 12.4556742,2.8401566 12.7688887,3.4795302 L15.0746102,8.18626488 L20.263488,8.92467531 C20.968357,9.02498267 21.2499792,9.89172653 20.7386873,10.3871885 L16.9748238,14.0345209 L17.8760051,19.1976184 C17.9984237,19.8989853 17.2611273,20.4346625 16.6319171,20.1015012 L12,17.648942 L7.36808291,20.1015012 Z"
      />
    );
  },
  Status: ({ color, ...rest }) => {
    return (
      <g fill="none" fillRule="evenodd">
        <defs>
          <circle id="online-copy-b" cx="12" cy="12" r="12" />
          <filter
            id="online-copy-a"
            width="237.5%"
            height="250%"
            x="-68.8%"
            y="-43.8%"
            filterUnits="objectBoundingBox"
          >
            <feMorphology
              in="SourceAlpha"
              radius="1.5"
              result="shadowSpreadOuter1"
            />
            <feOffset
              dy="9"
              in="shadowSpreadOuter1"
              result="shadowOffsetOuter1"
            />
            <feGaussianBlur
              in="shadowOffsetOuter1"
              result="shadowBlurOuter1"
              stdDeviation="4.5"
            />
            <feColorMatrix
              in="shadowBlurOuter1"
              result="shadowMatrixOuter1"
              values="0 0 0 0 0.0392156863   0 0 0 0 0.0862745098   0 0 0 0 0.274509804  0 0 0 0.1 0"
            />
            <feOffset in="SourceAlpha" result="shadowOffsetOuter2" />
            <feGaussianBlur
              in="shadowOffsetOuter2"
              result="shadowBlurOuter2"
              stdDeviation="1.5"
            />
            <feColorMatrix
              in="shadowBlurOuter2"
              result="shadowMatrixOuter2"
              values="0 0 0 0 0.0392156863   0 0 0 0 0.0862745098   0 0 0 0 0.274509804  0 0 0 0.06 0"
            />
            <feMerge>
              <feMergeNode in="shadowMatrixOuter1" />
              <feMergeNode in="shadowMatrixOuter2" />
            </feMerge>
          </filter>
        </defs>
        <use
          fill="#000"
          filter="url(#online-copy-a)"
          xlinkHref="#online-copy-b"
        />
        <use fill="#FFF" xlinkHref="#online-copy-b" />
        <circle cx="12" cy="12" r="9" fill={color} {...rest} />
      </g>
    );
  },
  Stop: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M8,6 L16,6 C17.1045695,6 18,6.8954305 18,8 L18,16 C18,17.1045695 17.1045695,18 16,18 L8,18 C6.8954305,18 6,17.1045695 6,16 L6,8 C6,6.8954305 6.8954305,6 8,6 Z"
      />
    );
  },
  Store: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M20.16,7.8 C20.07,7.34 19.66,7 19.18,7 L4.82,7 C4.34,7 3.93,7.34 3.84,7.8 L3,12 L3,13 C3,13.55 3.45,14 4,14 L4,19 C4,19.55 4.45,20 5,20 L13,20 C13.55,20 14,19.55 14,19 L14,14 L18,14 L18,19 C18,19.55 18.45,20 19,20 C19.55,20 20,19.55 20,19 L20,14 C20.55,14 21,13.55 21,13 L21,12 L20.16,7.8 Z M12,18 L6,18 L6,14 L12,14 L12,18 Z M5,6 C4.45,6 4,5.55 4,5 C4,4.45 4.45,4 5,4 L19,4 C19.55,4 20,4.45 20,5 C20,5.55 19.55,6 19,6 L5,6 Z"
      />
    );
  },
  Success: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M15.3141427,8.79289322 L10.0212495,14.0857864 L8.20710678,12.2716438 C7.81658249,11.8811195 7.18341751,11.8811195 6.79289322,12.2716438 C6.40236893,12.6621681 6.40236893,13.295333 6.79289322,13.6858573 L9.31414268,16.2071068 C9.70466697,16.5976311 10.3378319,16.5976311 10.7283562,16.2071068 L16.7283562,10.2071068 C17.1188805,9.81658249 17.1188805,9.18341751 16.7283562,8.79289322 C16.3378319,8.40236893 15.704667,8.40236893 15.3141427,8.79289322 Z"
      />
    );
  },
  TimestampSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M13,11 L13,7 C13,6.44771525 12.5522847,6 12,6 C11.4477153,6 11,6.44771525 11,7 L11,12 C11,12.5522847 11.4477153,13 12,13 L17,13 C17.5522847,13 18,12.5522847 18,12 C18,11.4477153 17.5522847,11 17,11 L13,11 Z"
      />
    );
  },
  Timestamp: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,20 C16.418278,20 20,16.418278 20,12 C20,7.581722 16.418278,4 12,4 C7.581722,4 4,7.581722 4,12 C4,16.418278 7.581722,20 12,20 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M13,11 L13,7 C13,6.44771525 12.5522847,6 12,6 C11.4477153,6 11,6.44771525 11,7 L11,12 C11,12.5522847 11.4477153,13 12,13 L17,13 C17.5522847,13 18,12.5522847 18,12 C18,11.4477153 17.5522847,11 17,11 L13,11 Z"
      />
    );
  },
  Twitter: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M20.4240011,6.75697057 C19.7837813,7.05498022 19.0944711,7.25651193 18.3714114,7.34655801 C19.1098119,6.88346388 19.6763962,6.14915952 19.9433249,5.27442615 C19.2519693,5.70321702 18.488001,6.01516237 17.6718741,6.18239081 C17.0214272,5.45451832 16.0917789,5 15.0629273,5 C13.0901094,5 11.48956,6.67764426 11.48956,8.74656019 C11.48956,9.04028193 11.5202414,9.32542786 11.5816043,9.59985401 C8.6106163,9.44334534 5.97712432,7.95329709 4.21294038,5.68392143 C3.90510307,6.2392056 3.72919603,6.88346388 3.72919603,7.56952926 C3.72919603,8.86876558 4.36021139,10.0157812 5.31951837,10.6879108 C4.7345252,10.6696872 4.18225892,10.4992429 3.69953729,10.2205288 L3.69953729,10.2666238 C3.69953729,12.0825531 4.93190926,13.5972569 6.56825378,13.9402895 C6.26859819,14.0281917 5.95257916,14.0721427 5.62633297,14.0721427 C5.39622202,14.0721427 5.17122464,14.0496312 4.95338628,14.0056802 C5.4084946,15.4935845 6.72779738,16.5773534 8.29255183,16.6062968 C7.0693843,17.6118113 5.52712958,18.2110466 3.85294458,18.2110466 C3.56453886,18.2110466 3.27920128,18.1938949 3,18.1595917 C4.58214061,19.2208491 6.46189139,19.8415238 8.47970874,19.8415238 C15.0557683,19.8415238 18.6506127,14.1332454 18.6506127,9.18178292 C18.6506127,9.01884239 18.6475445,8.85590186 18.6414083,8.69617726 C19.3399228,8.16769252 19.946393,7.50842656 20.4240011,6.75697057"
      />
    );
  },
  UpArrowSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12.7071068,7.29289322 C12.5261424,7.11192881 12.2761424,7 12,7 C11.7238576,7 11.4738576,7.11192881 11.2928932,7.29289322 L7.29289322,11.2928932 C6.90236893,11.6834175 6.90236893,12.3165825 7.29289322,12.7071068 C7.68341751,13.0976311 8.31658249,13.0976311 8.70710678,12.7071068 L11,10.4142136 L11,16 C11,16.5522847 11.4477153,17 12,17 C12.5522847,17 13,16.5522847 13,16 L13,10.4142136 L15.2928932,12.7071068 C15.6834175,13.0976311 16.3165825,13.0976311 16.7071068,12.7071068 C17.0976311,12.3165825 17.0976311,11.6834175 16.7071068,11.2928932 L12.7071068,7.29289322 L12.7071068,7.29289322 Z M12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 Z"
      />
    );
  },
  UpArrow: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,10.4142136 L7.70710678,14.7071068 C7.31658249,15.0976311 6.68341751,15.0976311 6.29289322,14.7071068 C5.90236893,14.3165825 5.90236893,13.6834175 6.29289322,13.2928932 L11.2928932,8.29289322 C11.6834175,7.90236893 12.3165825,7.90236893 12.7071068,8.29289322 L17.7071068,13.2928932 C18.0976311,13.6834175 18.0976311,14.3165825 17.7071068,14.7071068 C17.3165825,15.0976311 16.6834175,15.0976311 16.2928932,14.7071068 L12,10.4142136 Z"
      />
    );
  },
  Up: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M11,8.41421356 L7.70710678,11.7071068 C7.31658249,12.0976311 6.68341751,12.0976311 6.29289322,11.7071068 C5.90236893,11.3165825 5.90236893,10.6834175 6.29289322,10.2928932 L11.2928932,5.29289322 C11.6834175,4.90236893 12.3165825,4.90236893 12.7071068,5.29289322 L17.7071068,10.2928932 C18.0976311,10.6834175 18.0976311,11.3165825 17.7071068,11.7071068 C17.3165825,12.0976311 16.6834175,12.0976311 16.2928932,11.7071068 L13,8.41421356 L13,18 C13,18.5522847 12.5522847,19 12,19 C11.4477153,19 11,18.5522847 11,18 L11,8.41421356 Z"
      />
    );
  },
  Upload: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M11,5.41421356 L7.70710678,8.70710678 C7.31658249,9.09763107 6.68341751,9.09763107 6.29289322,8.70710678 C5.90236893,8.31658249 5.90236893,7.68341751 6.29289322,7.29289322 L11.2928932,2.29289322 C11.6834175,1.90236893 12.3165825,1.90236893 12.7071068,2.29289322 L17.7071068,7.29289322 C18.0976311,7.68341751 18.0976311,8.31658249 17.7071068,8.70710678 C17.3165825,9.09763107 16.6834175,9.09763107 16.2928932,8.70710678 L13,5.41421356 L13,15 C13,15.5522847 12.5522847,16 12,16 C11.4477153,16 11,15.5522847 11,15 L11,5.41421356 Z M20,16 C20,15.4477153 20.4477153,15 21,15 C21.5522847,15 22,15.4477153 22,16 L22,19.5 C22,20.8807119 20.8807119,22 19.5,22 L4.5,22 C3.11928813,22 2,20.8807119 2,19.5 L2,16 C2,15.4477153 2.44771525,15 3,15 C3.55228475,15 4,15.4477153 4,16 L4,19.5 C4,19.7761424 4.22385763,20 4.5,20 L19.5,20 C19.7761424,20 20,19.7761424 20,19.5 L20,16 Z"
      />
    );
  },
  UserCircle: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,2 C6.48,2 2,6.48 2,12 C2,17.52 6.48,22 12,22 C17.52,22 22,17.52 22,12 C22,6.48 17.52,2 12,2 Z M12,5 C13.66,5 15,6.34 15,8 C15,9.66 13.66,11 12,11 C10.34,11 9,9.66 9,8 C9,6.34 10.34,5 12,5 Z M12,19.2 C9.5,19.2 7.29,17.92 6,15.98 C6.03,13.99 10,12.9 12,12.9 C13.99,12.9 17.97,13.99 18,15.98 C16.71,17.92 14.5,19.2 12,19.2 Z"
      />
    );
  },
  UserSolid: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M21,21 L3,21 C3,16.275 6.48532777,14 12,14 C17.5146722,14 21,16.275 21,21 Z M12,12 C9.8,12 8,10.2 8,8 C8,5.8 9.8,4 12,4 C14.2,4 16,5.8 16,8 C16,10.2 14.2,12 12,12 Z"
      />
    );
  },
  User: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,16 C8.34235386,16 6.15493194,17.0095669 5.35170515,19 L18.6482948,19 C17.8450681,17.0095669 15.6576461,16 12,16 Z M21,21 L3,21 C3,16.275 6.48532777,14 12,14 C17.5146722,14 21,16.275 21,21 Z M12,12 C9.8,12 8,10.2 8,8 C8,5.8 9.8,4 12,4 C14.2,4 16,5.8 16,8 C16,10.2 14.2,12 12,12 Z M12,10 C13.0954305,10 14,9.0954305 14,8 C14,6.9045695 13.0954305,6 12,6 C10.9045695,6 10,6.9045695 10,8 C10,9.0954305 10.9045695,10 12,10 Z"
      />
    );
  },
  Visa: ({ color, ...rest }) => {
    return (
      <g fill="none" transform="translate(2 6)">
        <rect width="20" height="10.4" y="1.17" fill="#F6F6F6" />
        <path
          fill="#265697"
          d="M18.5,1.31490693e-07 C17.5,2.29637964e-07 6.91259765,-2.08413885e-07 1,1.31490693e-07 C0.5,1.60234792e-07 4.61852779e-14,0.520000131 7.10542736e-14,1.04000013 L7.10542736e-14,2.60000013 L20,2.60000013 C20,2.60000013 19.9999998,5.01435234 20,1.56000013 C20,0.520000131 19.5,1.31490693e-07 18.5,1.31490693e-07 Z"
        />
        <path
          fill="#D97B16"
          d="M0,10.4 C0,10.4 0,10.4 0,11.4400001 C-5.78549837e-08,12.48 0.5,13 1.5,13 C7.0779012,13 17.5,13 18.5,13 C19.5,13 20,12.48 20,11.4400001 L20,10.4 L0,10.4 Z"
        />
        <path
          fill="#265697"
          d="M13.9518181 3.87167409C13.6715 3.75992276 13.232303 3.64 12.6837841 3.64 11.2857992 3.64 10.301053 4.38816473 10.2926477 5.46036886 10.2847727 6.25296926 10.9956704 6.69509991 11.5323371 6.95898069 12.0830568 7.22934226 12.2681856 7.40178759 12.2655341 7.64321103 12.2620606 8.01295409 11.8257538 8.18187724 11.4191174 8.18187724 10.8528333 8.18187724 10.5519924 8.09827507 10.0873409 7.8923269L9.90502272 7.8046672 9.70645076 9.03948276C10.0368826 9.19344374 10.6479773 9.32686344 11.2824583 9.3337669 12.7696666 9.3337669 13.7351364 8.59419626 13.7461136 7.44921005 13.7514431 6.82170069 13.3744772 6.34417931 12.5582614 5.9505137 12.0637273 5.6953397 11.7608712 5.52506404 11.7640795 5.26667783 11.7640795 5.03739882 12.020428 4.79222778 12.574303 4.79222778 13.0369659 4.78459172 13.3720909 4.8918065 13.6332386 5.00355783L13.7600076 5.06721045 13.9518181 3.87167409M17.5798069 3.74112847L16.4865606 3.74112847C16.1479091 3.74112847 15.8944508 3.83935468 15.7457273 4.1985594L13.6445076 9.25365871 15.130178 9.25365871C15.130178 9.25365871 15.3730834 8.57396493 15.4280228 8.42473774 15.590375 8.42473774 17.0336478 8.42710463 17.2399621 8.42710463 17.2823069 8.62020394 17.4120985 9.25365871 17.4120985 9.25365871L18.7249166 9.25365871 17.5798069 3.74112847 17.5798069 3.74112847zM15.8351894 7.29648886C15.9522007 6.97867665 16.398875 5.75451211 16.398875 5.75451211 16.3905227 5.76919251 16.5150114 5.43515015 16.5864697 5.22804669L16.6820834 5.70362385C16.6820834 5.70362385 16.9529886 7.02032276 17.009625 7.29643251L15.8351894 7.29648886 15.8351894 7.29648886 15.8351894 7.29648886 15.8351894 7.29648886z"
        />
        <polygon
          fill="#265697"
          points="7.406 9.252 8.291 3.736 9.706 3.736 8.82 9.252"
        />
        <path
          fill="#265697"
          d="M6.21960228,3.73988867 L4.83445076,7.50142266 L4.68686743,6.73699961 C4.42900758,5.85580966 3.62559849,4.90107684 2.72742424,4.42310463 L3.99397349,9.24714976 L5.49088636,9.24534641 L7.71826515,3.73988867 L6.21960228,3.73988867"
        />
        <path
          fill="#D97B16"
          d="M3.54944698,3.73647921 L1.26805681,3.73647921 L1.25,3.85124551 C3.02489773,4.30783114 4.19933334,5.41122759 4.68684091,6.73699961 L4.19076894,4.20216611 C4.105125,3.8528798 3.85673106,3.74865183 3.54944698,3.73647921"
        />
      </g>
    );
  },
  CBChecked: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M5.8458278,2 L18.1541722,2 C19.4914503,2 19.9763797,2.13923842 20.4652686,2.40069906 C20.9541574,2.66215969 21.3378403,3.04584256 21.5993009,3.53473144 C21.8607616,4.02362033 22,4.50854969 22,5.8458278 L22,18.1541722 C22,19.4914503 21.8607616,19.9763797 21.5993009,20.4652686 C21.3378403,20.9541574 20.9541574,21.3378403 20.4652686,21.5993009 C19.9763797,21.8607616 19.4914503,22 18.1541722,22 L5.8458278,22 C4.50854969,22 4.02362033,21.8607616 3.53473144,21.5993009 C3.04584256,21.3378403 2.66215969,20.9541574 2.40069906,20.4652686 C2.13923842,19.9763797 2,19.4914503 2,18.1541722 L2,5.8458278 C2,4.50854969 2.13923842,4.02362033 2.40069906,3.53473144 C2.66215969,3.04584256 3.04584256,2.66215969 3.53473144,2.40069906 C4.02362033,2.13923842 4.50854969,2 5.8458278,2 Z M15.3141427,8.79289322 L10.0212495,14.0857864 L8.20710678,12.2716438 C7.81658249,11.8811195 7.18341751,11.8811195 6.79289322,12.2716438 C6.40236893,12.6621681 6.40236893,13.295333 6.79289322,13.6858573 L9.31414268,16.2071068 C9.70466697,16.5976311 10.3378319,16.5976311 10.7283562,16.2071068 L16.7283562,10.2071068 C17.1188805,9.81658249 17.1188805,9.18341751 16.7283562,8.79289322 C16.3378319,8.40236893 15.704667,8.40236893 15.3141427,8.79289322 Z"
      />
    );
  },
  CBDisabled: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M5.8458278,4 C4.92853228,4 4.70418221,4.043324 4.47792916,4.16432545 C4.33757722,4.23938649 4.23938649,4.33757722 4.16432545,4.47792916 C4.043324,4.70418221 4,4.92853228 4,5.8458278 L4,18.1541722 C4,19.0714677 4.043324,19.2958178 4.16432545,19.5220708 C4.23938649,19.6624228 4.33757722,19.7606135 4.47792916,19.8356745 C4.70418221,19.956676 4.92853228,20 5.8458278,20 L18.1541722,20 C19.0714677,20 19.2958178,19.956676 19.5220708,19.8356745 C19.6624228,19.7606135 19.7606135,19.6624228 19.8356745,19.5220708 C19.956676,19.2958178 20,19.0714677 20,18.1541722 L20,5.8458278 C20,4.92853228 19.956676,4.70418221 19.8356745,4.47792916 C19.7606135,4.33757722 19.6624228,4.23938649 19.5220708,4.16432545 C19.2958178,4.043324 19.0714677,4 18.1541722,4 L5.8458278,4 Z M5.8458278,2 L18.1541722,2 C19.4914503,2 19.9763797,2.13923842 20.4652686,2.40069906 C20.9541574,2.66215969 21.3378403,3.04584256 21.5993009,3.53473144 C21.8607616,4.02362033 22,4.50854969 22,5.8458278 L22,18.1541722 C22,19.4914503 21.8607616,19.9763797 21.5993009,20.4652686 C21.3378403,20.9541574 20.9541574,21.3378403 20.4652686,21.5993009 C19.9763797,21.8607616 19.4914503,22 18.1541722,22 L5.8458278,22 C4.50854969,22 4.02362033,21.8607616 3.53473144,21.5993009 C3.04584256,21.3378403 2.66215969,20.9541574 2.40069906,20.4652686 C2.13923842,19.9763797 2,19.4914503 2,18.1541722 L2,5.8458278 C2,4.50854969 2.13923842,4.02362033 2.40069906,3.53473144 C2.66215969,3.04584256 3.04584256,2.66215969 3.53473144,2.40069906 C4.02362033,2.13923842 4.50854969,2 5.8458278,2 Z M8,13 C7.44771525,13 7,12.5522847 7,12 C7,11.4477153 7.44771525,11 8,11 L16,11 C16.5522847,11 17,11.4477153 17,12 C17,12.5522847 16.5522847,13 16,13 L8,13 Z"
      />
    );
  },
  CBIndetermine: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M5.8458278,2 L18.1541722,2 C19.4914503,2 19.9763797,2.13923842 20.4652686,2.40069906 C20.9541574,2.66215969 21.3378403,3.04584256 21.5993009,3.53473144 C21.8607616,4.02362033 22,4.50854969 22,5.8458278 L22,18.1541722 C22,19.4914503 21.8607616,19.9763797 21.5993009,20.4652686 C21.3378403,20.9541574 20.9541574,21.3378403 20.4652686,21.5993009 C19.9763797,21.8607616 19.4914503,22 18.1541722,22 L5.8458278,22 C4.50854969,22 4.02362033,21.8607616 3.53473144,21.5993009 C3.04584256,21.3378403 2.66215969,20.9541574 2.40069906,20.4652686 C2.13923842,19.9763797 2,19.4914503 2,18.1541722 L2,5.8458278 C2,4.50854969 2.13923842,4.02362033 2.40069906,3.53473144 C2.66215969,3.04584256 3.04584256,2.66215969 3.53473144,2.40069906 C4.02362033,2.13923842 4.50854969,2 5.8458278,2 Z M8,13 L16,13 C16.5522847,13 17,12.5522847 17,12 C17,11.4477153 16.5522847,11 16,11 L8,11 C7.44771525,11 7,11.4477153 7,12 C7,12.5522847 7.44771525,13 8,13 Z"
      />
    );
  },
  CBUnchecked: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M5.8458278,4 C4.92853228,4 4.70418221,4.043324 4.47792916,4.16432545 C4.33757722,4.23938649 4.23938649,4.33757722 4.16432545,4.47792916 C4.043324,4.70418221 4,4.92853228 4,5.8458278 L4,18.1541722 C4,19.0714677 4.043324,19.2958178 4.16432545,19.5220708 C4.23938649,19.6624228 4.33757722,19.7606135 4.47792916,19.8356745 C4.70418221,19.956676 4.92853228,20 5.8458278,20 L18.1541722,20 C19.0714677,20 19.2958178,19.956676 19.5220708,19.8356745 C19.6624228,19.7606135 19.7606135,19.6624228 19.8356745,19.5220708 C19.956676,19.2958178 20,19.0714677 20,18.1541722 L20,5.8458278 C20,4.92853228 19.956676,4.70418221 19.8356745,4.47792916 C19.7606135,4.33757722 19.6624228,4.23938649 19.5220708,4.16432545 C19.2958178,4.043324 19.0714677,4 18.1541722,4 L5.8458278,4 Z M5.8458278,2 L18.1541722,2 C19.4914503,2 19.9763797,2.13923842 20.4652686,2.40069906 C20.9541574,2.66215969 21.3378403,3.04584256 21.5993009,3.53473144 C21.8607616,4.02362033 22,4.50854969 22,5.8458278 L22,18.1541722 C22,19.4914503 21.8607616,19.9763797 21.5993009,20.4652686 C21.3378403,20.9541574 20.9541574,21.3378403 20.4652686,21.5993009 C19.9763797,21.8607616 19.4914503,22 18.1541722,22 L5.8458278,22 C4.50854969,22 4.02362033,21.8607616 3.53473144,21.5993009 C3.04584256,21.3378403 2.66215969,20.9541574 2.40069906,20.4652686 C2.13923842,19.9763797 2,19.4914503 2,18.1541722 L2,5.8458278 C2,4.50854969 2.13923842,4.02362033 2.40069906,3.53473144 C2.66215969,3.04584256 3.04584256,2.66215969 3.53473144,2.40069906 C4.02362033,2.13923842 4.50854969,2 5.8458278,2 Z"
      />
    );
  },
  RBChecked: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        fillRule="evenodd"
        d="M12,20 C16.418278,20 20,16.418278 20,12 C20,7.581722 16.418278,4 12,4 C7.581722,4 4,7.581722 4,12 C4,16.418278 7.581722,20 12,20 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M12,17 C14.7614237,17 17,14.7614237 17,12 C17,9.23857625 14.7614237,7 12,7 C9.23857625,7 7,9.23857625 7,12 C7,14.7614237 9.23857625,17 12,17 Z"
      />
    );
  },
  RBUnchecked: ({ color, ...rest }) => {
    return (
      <path
        fill={color}
        {...rest}
        d="M12,20 C16.418278,20 20,16.418278 20,12 C20,7.581722 16.418278,4 12,4 C7.581722,4 4,7.581722 4,12 C4,16.418278 7.581722,20 12,20 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z"
      />
    );
  }
};
