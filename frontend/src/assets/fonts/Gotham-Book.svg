<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Sun Jan 22 11:00:44 2006
 By Aleksey,,,
HTF Gotham\252 Copr. 2000 The Hoefler Type Foundry, Inc. Info: www.typography.com
</metadata>
<defs>
<font id="Gotham-Book" horiz-adv-x="500" >
  <font-face 
    font-family="Gotham"
    font-weight="400"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 5 4 5 0 0 2 0 4"
    ascent="800"
    descent="-200"
    x-height="517"
    cap-height="700"
    bbox="-68 -164.694 1158 907"
    underline-thickness="20"
    underline-position="-163"
    unicode-range="U+0020-FB02"
  />
<missing-glyph 
 />
    <glyph glyph-name=".notdef" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="300" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="300" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="271" 
d="M91 700v-57l23 -445h43l24 445v57h-90zM182 0v105h-92v-105h92z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="440" 
d="M260 428h42l81 267v5h-92zM65 428h42l81 267v5h-93z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="700" 
d="M654 458v69h-108l29 173h-70l-30 -173h-190l29 173h-70l-30 -173h-130v-69h119l-37 -212h-121v-69h109l-30 -177h70l31 177h190l-30 -177h70l31 177h129v69h-118l37 212h120zM463 458l-36 -212h-190l36 212h190z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="636" 
d="M569 189q0 76 -51 122.5t-165 72.5v246q40 -7 77.5 -24t73.5 -48l43 60q-44 34 -89 54.5t-102 25.5v60h-64v-58q-46 -2 -84.5 -17t-66.5 -39.5t-43.5 -57t-15.5 -71.5q0 -79 51 -124t162 -71v-250q-57 6 -103 30t-91 65l-45 -60q53 -45 111 -71t125 -32v-100h64v98
q47 3 86.5 17.5t67.5 39.5t43.5 58t15.5 74zM295 398q-41 10 -67.5 22.5t-41.5 27.5t-21 33t-6 39q0 23 9.5 43t27 35t42.5 24.5t57 10.5v-235zM492 184q0 -50 -37.5 -82t-101.5 -35v239q42 -10 68.5 -22t42.5 -27t22 -33t6 -40z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="834" 
d="M369 530q0 36 -11.5 68t-32 56.5t-49.5 39t-64 14.5q-36 0 -65.5 -14.5t-50.5 -39.5t-32.5 -57.5t-11.5 -68.5t11.5 -68t32 -56.5t49.5 -39t65 -14.5t65 14.5t50 39.5t32.5 57.5t11.5 68.5zM709 700h-71l-514 -700h72zM781 172q0 36 -11 68t-31.5 56.5t-49.5 39t-65 14.5
t-65 -14.5t-50 -39.5t-32.5 -57.5t-11.5 -68.5t11.5 -68t32 -56.5t49.5 -39t64 -14.5q36 0 65.5 14.5t50 39.5t32 57.5t11.5 68.5zM303 528q0 -26 -6.5 -48t-18.5 -38.5t-29 -26t-37 -9.5t-36.5 9.5t-29.5 26t-20 39t-7 49.5q0 25 6.5 47.5t18.5 39t28.5 26t37.5 9.5
q19 0 36 -9.5t29.5 -26t20 -39t7.5 -49.5zM715 170q0 -26 -6.5 -48t-18.5 -38.5t-28.5 -26t-37.5 -9.5q-20 0 -36.5 9.5t-29.5 26t-20 39t-7 49.5q0 25 6.5 47.5t18.5 39t29 26t37 9.5q19 0 36 -9.5t30 -26t20 -39t7 -49.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="696" 
d="M664 32l-122 125q29 38 53.5 82t47.5 94l-65 30q-19 -44 -40 -83t-44 -73l-160 163q77 27 121 70.5t44 112.5q0 33 -13 62t-35.5 50.5t-53.5 34t-68 12.5q-42 0 -76 -13t-58 -35.5t-37.5 -53t-13.5 -65.5q0 -42 17 -77.5t53 -77.5q-81 -32 -125 -82t-44 -121
q0 -44 16.5 -80.5t46 -62.5t70 -40t87.5 -14q69 0 125 28.5t104 80.5l110 -113zM425 549q0 -47 -34 -79t-101 -55q-20 21 -33.5 38.5t-21.5 32.5t-11.5 29t-3.5 30q0 44 29 73t77 29q43 0 71 -27.5t28 -70.5zM446 148q-38 -42 -81.5 -66.5t-93.5 -24.5q-32 0 -59 10
t-46.5 28t-30 42t-10.5 53q0 45 32 85t102 66z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="245" 
d="M65 428h43l81 267v5h-94z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="434" 
d="M378 664l-33 51q-134 -75 -205.5 -182t-71.5 -246t71.5 -246t205.5 -182l33 51q-115 73 -173.5 164.5t-58.5 212.5t58.5 212.5t173.5 164.5z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="434" 
d="M56 664q115 -73 173.5 -164.5t58.5 -212.5t-58.5 -212.5t-173.5 -164.5l33 -51q134 75 205.5 182t71.5 246t-71.5 246t-205.5 182z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="430" 
d="M361 496l-116 53l116 54l-27 46l-103 -72l10 125h-52l10 -125l-103 72l-27 -46l116 -54l-116 -53l27 -47l103 73l-10 -125h52l-10 125l103 -73z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="640" 
d="M283 388h-208v-72h208v-206h74v206h208v72h-208v206h-74v-206z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="245" 
d="M46 -87l12 -35q57 13 84 44.5t27 92.5v90h-92v-105h36q3 -32 -13 -52.5t-54 -34.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="408" 
d="M343 260v81h-278v-81h278z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="245" 
d="M169 0v105h-92v-105h92z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="508" 
d="M443 798l-464 -926h71l464 926h-71z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="715" 
d="M653 351q0 74 -20.5 139.5t-59 115t-93 78t-121.5 28.5q-68 0 -123 -29t-93.5 -78.5t-59.5 -115.5t-21 -140t20.5 -139.5t59 -115t93 -78t122.5 -28.5q67 0 122 29t93.5 78.5t59.5 115.5t21 140zM571 349q0 -58 -14.5 -110.5t-42 -92t-67 -63t-88.5 -23.5t-89 23.5
t-68 63.5t-43 93t-15 111t15 110.5t42.5 92t67 63t88.5 23.5t88.5 -24t67.5 -64t43 -92.5t15 -110.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="358" 
d="M28 646l19 -64l132 41v-623h78v705h-58z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="599" 
d="M307 639q28 0 54 -9.5t45 -27t30.5 -42t11.5 -55.5q0 -26 -7.5 -50.5t-25 -50t-45.5 -54.5t-68 -65l-254 -224v-61h489v72h-371l187 166q46 40 79 73.5t55 65t32.5 63.5t10.5 71q0 43 -16 79.5t-45 63t-68.5 41.5t-87.5 15q-46 0 -82.5 -10t-66 -29t-54.5 -47t-49 -63
l58 -42q42 59 85 89.5t103 30.5z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="613" 
d="M429 629l-221 -249l16 -47h45q87 0 141.5 -35t54.5 -101q0 -31 -12 -56.5t-32.5 -43.5t-48.5 -27.5t-60 -9.5q-65 0 -114.5 27t-90.5 77l-57 -51q43 -56 108 -90.5t153 -34.5q49 0 91.5 15.5t74 43.5t50 67t18.5 86q0 48 -19 84t-50.5 60t-73.5 37t-88 17l220 244v58
h-447v-71h342z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="671" 
d="M439 705l-396 -482l20 -57h369v-166h76v166h109v66h-109v473h-69zM432 232h-294l294 361v-361z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="612" 
d="M99 359l53 -35q31 17 66.5 28.5t80.5 11.5q39 0 71 -11t55 -30.5t35.5 -46.5t12.5 -60q0 -34 -12.5 -62.5t-34.5 -49.5t-53.5 -32.5t-68.5 -11.5q-54 0 -103.5 25.5t-95.5 70.5l-52 -57q48 -48 111.5 -79.5t138.5 -31.5q55 0 101 16.5t79 47t51.5 73t18.5 94.5
q0 50 -18.5 90t-51.5 67.5t-77 42.5t-95 15q-44 0 -75.5 -8.5t-62.5 -22.5l15 224h334v73h-403z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="647" 
d="M559 632q-46 38 -95 59t-112 21q-69 0 -123 -29.5t-91.5 -81t-57 -121t-19.5 -149.5q0 -54 6 -95.5t17 -73.5t27.5 -57t37.5 -46q33 -33 79.5 -52t104.5 -19q53 0 99 17.5t79.5 48.5t53 74t19.5 94q0 49 -19.5 89t-52.5 68.5t-77 43.5t-93 15q-38 0 -68.5 -9t-55 -23.5
t-43 -34t-32.5 -40.5q0 68 14 124.5t40.5 97.5t64.5 64t86 23t87 -18t78 -51zM503 219q0 -34 -12 -63.5t-34 -51t-53 -34t-70 -12.5t-71.5 12t-56 33t-36.5 50t-13 63q0 30 12.5 57.5t35.5 49t55.5 34.5t71.5 13q38 0 69.5 -11.5t54 -31.5t35 -47.5t12.5 -60.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="596" 
d="M68 628h385l-316 -628h88l318 641v59h-475v-72z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="630" 
d="M578 190q0 63 -40.5 106t-103.5 66q24 11 46 26.5t38.5 35t26 44.5t9.5 56q0 41 -19.5 75t-52 59t-76 38.5t-91.5 13.5t-91.5 -13.5t-76 -38.5t-52 -59t-19.5 -75q0 -31 9.5 -56t26 -44.5t38.5 -35t46 -26.5q-63 -23 -103.5 -66.5t-40.5 -106.5q0 -44 20 -81t55.5 -63
t83.5 -40.5t104 -14.5t104 14.5t83.5 40.5t55.5 63t20 82zM475 519q0 -29 -12.5 -52.5t-34 -40.5t-51 -26.5t-62.5 -9.5t-62.5 9.5t-51 26.5t-34 40.5t-12.5 52.5q0 27 12 49t33.5 38.5t51 25.5t63.5 9t63.5 -9t51 -25.5t33.5 -39t12 -48.5zM498 191q0 -27 -12.5 -51
t-36 -42t-57.5 -28.5t-77 -10.5t-77 10.5t-57.5 28.5t-36 42t-12.5 51q0 30 14 54.5t39 42t58.5 27.5t71.5 10t71.5 -10t58 -27.5t39 -42t14.5 -54.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="647" 
d="M586 368q0 53 -6 94.5t-17 74t-27 57.5t-37 46q-34 34 -79 53t-105 19q-56 0 -102.5 -18.5t-79.5 -51t-51.5 -76t-18.5 -93.5q0 -48 18 -88t50 -69t77 -45t99 -16q73 0 121 33t77 79q1 -67 -13.5 -123.5t-42 -97t-66.5 -63.5t-86 -23t-88 18.5t-84 56.5l-46 -61
q45 -37 96 -61.5t120 -24.5q65 0 118.5 28t92 78.5t59.5 120t21 153.5zM490 482q0 -30 -12 -59t-34.5 -51t-55 -35.5t-72.5 -13.5q-38 0 -70 11.5t-54.5 32t-35 48.5t-12.5 62q0 33 11.5 63t33 52.5t53 36t70.5 13.5t72 -12.5t56.5 -34t36.5 -50.5t13 -63z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="255" 
d="M174 412v105h-92v-105h92zM174 0v105h-92v-105h92z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="255" 
d="M174 412v105h-92v-105h92zM51 -87l12 -35q57 13 84 44.5t27 92.5v90h-92v-105h36q3 -32 -13 -52.5t-54 -34.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="640" 
d="M72 385v-66l469 -233v78l-385 189l385 188v77z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="640" 
d="M90 506v-74h460v74h-460zM90 272v-74h460v74h-460z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="640" 
d="M568 319v66l-469 233v-78l385 -189l-385 -188v-77z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="537" 
d="M210 365l-5 -5l15 -162h51l10 108q43 6 80.5 21t66 40t45 60.5t16.5 82.5q0 43 -16 79.5t-45.5 63t-70.5 41.5t-92 15q-80 0 -136 -32t-99 -84l51 -49q38 45 82 69.5t100 24.5q34 0 61 -10t46 -28t29 -42t10 -51q0 -64 -50.5 -102t-148.5 -40zM291 0v105h-92v-105h92z
" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="980" 
d="M927 316q0 78 -33.5 150t-92.5 126.5t-138.5 87t-171.5 32.5t-172 -35t-139 -94.5t-93 -139t-34 -169.5t33.5 -169t93.5 -138.5t141.5 -94t177.5 -34.5q78 0 142.5 20t122.5 55l-16 27q-58 -34 -116 -51t-133 -17q-90 0 -165.5 31.5t-129.5 86t-84.5 127.5t-30.5 157
q0 83 30.5 156.5t84.5 128.5t127.5 87t159.5 32t159 -30.5t126.5 -81.5t84 -116t30.5 -134q0 -53 -12 -92t-31.5 -64.5t-44.5 -37.5t-52 -12q-41 0 -63 21.5t-22 59.5q0 8 1.5 24t5.5 37l40 227l-67 9l-13 -69q-9 16 -22 31t-31 26.5t-41 18.5t-52 7q-42 0 -83.5 -19
t-74.5 -52.5t-53 -79t-20 -98.5q0 -41 14 -74.5t38.5 -57.5t57 -37t69.5 -13q56 0 96 24.5t69 59.5q14 -38 49.5 -61t90.5 -23q34 0 68 14.5t60 44.5t42.5 76t16.5 110zM614 313q0 -35 -13.5 -67.5t-36 -57.5t-52.5 -40t-62 -15q-54 0 -88 32.5t-34 92.5q0 39 14 72
t36.5 57.5t52 38.5t61.5 14q26 0 48.5 -9.5t39 -26.5t25.5 -40.5t9 -50.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="790" 
d="M432 705h-74l-319 -705h81l83 185h381l82 -185h85zM553 256h-319l160 357z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="722" 
d="M397 700h-298v-700h311q57 0 104 13t80.5 37.5t51.5 60.5t18 81q0 36 -12 64t-32 48t-47.5 34.5t-58.5 23.5q21 9 40.5 22.5t35.5 33.5t25.5 46t9.5 61q0 40 -16 72.5t-46 55t-72 35t-94 12.5zM384 388h-207v241h213q75 0 115 -31t40 -84q0 -62 -45 -94t-116 -32zM412 71
h-235v247h221q92 0 139 -31.5t47 -89.5q0 -60 -46 -93t-126 -33z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="738" 
d="M699 111l-52 51q-50 -48 -102 -74.5t-124 -26.5q-58 0 -107.5 22.5t-86 61.5t-57 92t-20.5 114t20.5 113.5t57 91.5t86 61t107.5 22q72 0 123.5 -27t96.5 -69l54 58q-26 25 -54 45.5t-61 35t-71.5 22.5t-86.5 8q-77 0 -142 -28.5t-112 -78t-73.5 -115.5t-26.5 -141
q0 -76 26.5 -142t73.5 -114.5t111 -76.5t140 -28q48 0 87 9t73 25t63.5 38.5t56.5 50.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="782" 
d="M714 351q0 74 -27 138t-76.5 111t-118 73.5t-150.5 26.5h-243v-700h243q82 0 150.5 27t118 74t76.5 111.5t27 138.5zM632 349q0 -59 -20.5 -109t-58 -87.5t-91.5 -58.5t-120 -21h-164v554h164q66 0 120 -21.5t91.5 -59t58 -88.5t20.5 -109z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="670" 
d="M605 700h-506v-700h511v72h-432v245h382v72h-382v239h427v72z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="656" 
d="M602 700h-503v-700h79v305h379v72h-379v250h424v73z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="784" 
d="M705 95v279h-289v-71h213v-174q-36 -29 -89 -49.5t-113 -20.5q-64 0 -115 22t-87 61t-55.5 92.5t-19.5 116.5q0 59 20 111.5t55 91.5t84 62t107 23q37 0 67.5 -6t56 -16.5t47.5 -25t42 -32.5l51 60q-26 22 -53.5 39.5t-59 29.5t-68 18t-80.5 6q-79 0 -143.5 -29.5
t-110.5 -79.5t-71.5 -116t-25.5 -138q0 -75 24.5 -141t71 -115t112 -77t148.5 -28q89 0 160.5 31.5t120.5 75.5z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="760" 
d="M582 315v-315h79v700h-79v-311h-404v311h-79v-700h79v315h404z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="291" 
d="M106 700v-700h79v700h-79z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="553" 
d="M465 232v468h-80v-469q0 -83 -39 -125.5t-98 -42.5q-55 0 -91.5 25.5t-68.5 74.5l-57 -49q33 -54 85 -89t131 -35q47 0 87 15t69 45.5t45.5 76t16.5 105.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="719" 
d="M382 394l300 306h-102l-402 -418v418h-79v-700h79v188l149 151l269 -339h99z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="619" 
d="M99 0h473v73h-394v627h-79v-700z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="868" 
d="M690 567v-567h79v700h-80l-255 -382l-255 382h-80v-700h77v566l255 -374h4z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="790" 
d="M614 139l-441 561h-74v-700h77v574l452 -574h63v700h-77v-561z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="850" 
d="M782 351q0 73 -25.5 138.5t-72.5 115t-112.5 78.5t-145.5 29t-146 -29.5t-113 -79.5t-73 -115.5t-26 -138.5t25.5 -138.5t72.5 -115t112.5 -78.5t145.5 -29t146 29.5t113 79.5t73 115.5t26 138.5zM700 349q0 -60 -20.5 -112.5t-56.5 -91.5t-86.5 -61.5t-110.5 -22.5
t-110.5 23t-87.5 62t-57.5 92t-20.5 113t20.5 112.5t56.5 91.5t86.5 61.5t110.5 22.5t110.5 -23t87.5 -62t57.5 -92t20.5 -113z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="668" 
d="M623 477q0 52 -18.5 93.5t-53 70t-83 44t-107.5 15.5h-262v-700h79v247h170q56 0 106 14.5t87.5 43.5t59.5 72t22 100zM543 474q0 -35 -13.5 -63.5t-39 -49t-61 -31.5t-78.5 -11h-173v308h177q85 0 136.5 -38.5t51.5 -114.5z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="850" 
d="M698 115q41 48 62.5 109t21.5 127q0 73 -25.5 138.5t-72.5 115t-112.5 78.5t-145.5 29t-146 -29.5t-113 -79.5t-73 -115.5t-26 -138.5t25.5 -138.5t72.5 -115t112.5 -78.5t145.5 -29q66 0 121 20t100 55l92 -84l54 59zM587 114q-32 -25 -73 -39t-88 -14q-60 0 -110.5 23
t-87.5 62t-57.5 92t-20.5 113t20.5 112.5t56.5 91.5t86.5 61.5t110.5 22.5t110.5 -23t87.5 -62t57.5 -92t20.5 -113q0 -52 -16 -98.5t-45 -83.5l-129 117l-53 -59z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="723" 
d="M678 0l-215 286q41 7 76 24t60.5 42.5t39.5 60t14 78.5q0 47 -18 85.5t-51 66t-79.5 42.5t-104.5 15h-301v-700h79v272h200l203 -272h97zM573 488q0 -34 -13.5 -61t-37.5 -45.5t-57 -28.5t-72 -10h-215v284h216q85 0 132 -36.5t47 -102.5z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="640" 
d="M160 525q0 24 10.5 45t30 36t47 24t61.5 9q54 0 103.5 -18t98.5 -58l46 61q-54 43 -111.5 64.5t-134.5 21.5q-50 0 -92 -14.5t-73 -40t-48 -60.5t-17 -77q0 -44 14 -76t43 -56t73.5 -41t104.5 -30q55 -12 90.5 -25t55.5 -29t28 -35.5t8 -44.5q0 -54 -42.5 -87t-113.5 -33
q-73 0 -129 24.5t-111 74.5l-49 -58q63 -57 132.5 -84.5t153.5 -27.5q52 0 96 14t75.5 40t49.5 63t18 82q0 81 -55.5 127t-172.5 72q-58 12 -95 25.5t-58 30t-29 36.5t-8 45z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="648" 
d="M364 627h235v73h-550v-73h235v-627h80v627z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="759" 
d="M671 700h-79v-407q0 -115 -57 -173t-154 -58q-101 0 -157.5 61t-56.5 175v402h-79v-407q0 -74 21 -131t59.5 -95.5t92 -58t118.5 -19.5q66 0 119.5 19.5t92 58.5t59.5 97t21 135v401z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="750" 
d="M376 97l-249 603h-88l301 -705h70l301 705h-85z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1107" 
d="M331 117l-200 583h-86l250 -705h66l193 568l192 -568h67l250 705h-83l-200 -583l-192 585h-65z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="725" 
d="M679 0l-269 358l259 342h-92l-213 -286l-215 286h-93l259 -343l-269 -357h91l224 301l224 -301h94z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="716" 
d="M398 278l291 422h-92l-238 -350l-236 350h-96l291 -423v-277h80v278z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="693" 
d="M520 629l-453 -576v-53h561v71h-454l454 576v53h-546v-71h438z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="439" 
d="M380 700h-286v-830h286v60h-213v710h213v60z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="508" 
d="M65 798h-71l464 -926h71z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="439" 
d="M59 700v-60h214v-710h-214v-60h286v830h-286z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M249 647l118 -154h68l-155 209h-60l-155 -209h66z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="600" 
d="M-2 -97v-63h604v63h-604z" />
    <glyph glyph-name="grave" unicode="`" 
d="M257 595h62l-95 145l-80 -37z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="579" 
d="M273 456q73 0 113.5 -35.5t40.5 -105.5v-18q-35 10 -72 16t-87 6q-49 0 -89.5 -11t-70 -32t-45.5 -52.5t-16 -72.5t16.5 -71t44 -50.5t62 -30.5t70.5 -10q69 0 114.5 27t72.5 62v-78h74v316q0 103 -57 155.5t-163 52.5q-57 0 -101 -11.5t-87 -31.5l23 -63
q36 17 73.5 27.5t83.5 10.5zM276 262q48 0 85.5 -7t66.5 -15v-50q0 -31 -13.5 -56.5t-37 -44t-55 -29t-67.5 -10.5q-26 0 -49.5 7t-41.5 20.5t-28.5 32.5t-10.5 44q0 50 39 79t112 29z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="664" 
d="M608 259q0 64 -21 114t-56 84.5t-79.5 52.5t-90.5 18q-36 0 -65.5 -9.5t-53.5 -25t-43.5 -36.5t-35.5 -44v317h-77v-730h77v99q15 -22 34.5 -42t43.5 -35t54 -24t66 -9q47 0 91.5 18.5t79 53t55.5 84.5t21 114zM529 258q0 -47 -14.5 -84.5t-39 -63t-58 -39t-70.5 -13.5
q-36 0 -69.5 14.5t-60 40.5t-42 63.5t-15.5 82.5t15.5 82t42 63t60 40.5t69.5 14.5q37 0 70 -14t58 -40.5t39.5 -63.5t14.5 -83z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="571" 
d="M530 88l-49 46q-31 -33 -69.5 -55t-88.5 -22q-41 0 -75.5 15.5t-60 43t-40 64.5t-14.5 79t14 78.5t39 64t59 43t73 15.5q53 0 89.5 -23t67.5 -55l51 54q-18 19 -39 36t-46 29.5t-55 19.5t-67 7q-57 0 -105.5 -21.5t-84 -58.5t-55.5 -86.5t-20 -104.5t20 -104t55.5 -85.5
t84 -58t105.5 -21.5q73 0 122.5 28t88.5 72z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="664" 
d="M578 0v730h-77v-312q-15 22 -34.5 42t-43.5 35t-54 24t-66 9q-47 0 -91 -18.5t-79 -53t-56 -84.5t-21 -114t21 -114t56 -84.5t79 -52.5t91 -18q36 0 65.5 9t53.5 25t43.5 37t35.5 44v-104h77zM504 259q0 -45 -15.5 -82.5t-42 -63.5t-60 -40.5t-69.5 -14.5q-37 0 -70 14
t-58 40.5t-39.5 63.5t-14.5 83q0 47 14.5 84.5t39 63t57.5 39t71 13.5q36 0 69.5 -14.5t60 -40.5t42 -63t15.5 -82z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="592" 
d="M132 230h410q1 7 1 13v11q0 57 -16 107t-47 87t-76 58.5t-102 21.5q-54 0 -99.5 -21t-78.5 -57.5t-51.5 -86t-18.5 -105.5q0 -60 20.5 -110t55.5 -85.5t82 -55t100 -19.5q74 0 124 26.5t89 70.5l-48 43q-32 -33 -70.5 -53t-92.5 -20q-34 0 -65 11.5t-56 34t-41 55
t-20 74.5zM132 287q4 38 18 70t36 55.5t51 37t63 13.5q39 0 68.5 -14.5t50 -38.5t32 -56t14.5 -67h-333z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="367" 
d="M191 515v42q0 110 90 110q20 0 37 -3.5t38 -9.5v67q-19 7 -38.5 10t-46.5 3q-75 0 -115.5 -44t-40.5 -129v-45h-71v-67h71v-449h77v449h163v66h-164z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="664" 
d="M578 90v427h-77v-93q-16 21 -36 40t-44.5 33t-55 22.5t-66.5 8.5q-46 0 -90 -17t-78 -48.5t-54.5 -77t-20.5 -102.5t20.5 -102t54.5 -76.5t77.5 -48t90.5 -16.5q71 0 120 32t83 77v-61q0 -91 -51 -137.5t-137 -46.5q-56 0 -105 17t-94 49l-35 -60q52 -36 111 -54t124 -18
q60 0 108.5 16t83 47.5t53 78.5t18.5 110zM504 284q0 -40 -16 -72.5t-42.5 -55.5t-60.5 -35.5t-71 -12.5q-36 0 -68.5 12.5t-57 36t-39 56t-14.5 72.5t14 72.5t38.5 55.5t57 35t69.5 12t71 -12.5t60.5 -35.5t42.5 -55.5t16 -72.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="616" 
d="M163 296q0 36 11.5 66t32.5 51.5t49.5 33t61.5 11.5q69 0 106 -42t37 -114v-302h77v321q0 46 -13.5 84t-39 65.5t-62 42.5t-83.5 15q-67 0 -109 -29.5t-68 -71.5v303h-77v-730h77v296z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="263" 
d="M176 630v85h-88v-85h88zM170 0v517h-77v-517h77z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="263" 
d="M176 630v85h-88v-85h88zM93 517v-546q0 -36 -16.5 -52.5t-42.5 -16.5q-8 0 -18.5 0.5t-19.5 2.5v-63q13 -3 23.5 -4t24.5 -1q59 0 92.5 32t33.5 101v547h-77z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="569" 
d="M321 295l217 222h-96l-279 -291v504h-77v-730h77v135l105 106l185 -241h92z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="263" 
d="M170 0v730h-77v-730h77z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="951" 
d="M496 425q-21 44 -62 73.5t-104 29.5q-33 0 -58 -8t-45 -21.5t-35.5 -31.5t-28.5 -37v87h-77v-517h77v295q0 36 11 65.5t30.5 51.5t45.5 34t57 12q62 0 97.5 -40.5t35.5 -113.5v-304h76v298q0 38 11.5 68t31 50.5t45.5 31t55 10.5q63 0 98.5 -40.5t35.5 -115.5v-302h77
v320q0 97 -51 152.5t-141 55.5q-34 0 -61.5 -8t-49.5 -22t-39.5 -33t-31.5 -40z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="616" 
d="M163 296q0 36 11.5 66t32.5 51.5t49.5 33t61.5 11.5q69 0 106 -42t37 -114v-302h77v321q0 46 -13.5 84t-39 65.5t-62 42.5t-83.5 15q-67 0 -109 -29.5t-68 -71.5v90h-77v-517h77v296z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="646" 
d="M593 259q0 55 -20 104t-56 85.5t-85.5 58t-107.5 21.5q-59 0 -108.5 -21.5t-85.5 -58.5t-56 -86.5t-20 -104.5t20 -104t56 -85.5t85 -58t107 -21.5t108 21.5t86 58.5t56.5 86.5t20.5 104.5zM514 257q0 -42 -14 -78.5t-39 -63.5t-60 -42.5t-77 -15.5q-41 0 -76 16
t-60.5 43t-40 64t-14.5 79t14 78.5t38.5 64t59.5 43t77 15.5q41 0 76 -16t61 -43.5t40.5 -64.5t14.5 -79z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="664" 
d="M608 259q0 64 -21 114t-56 84.5t-79.5 52.5t-90.5 18q-36 0 -65.5 -9.5t-53.5 -25t-43.5 -36.5t-35.5 -44v104h-77v-677h77v259q15 -22 34.5 -42t43.5 -35t54 -24t66 -9q47 0 91.5 18.5t79 53t55.5 84.5t21 114zM529 258q0 -47 -14.5 -84.5t-39 -63t-58 -39t-70.5 -13.5
q-36 0 -69.5 14.5t-60 40.5t-42 63.5t-15.5 82.5t15.5 82t42 63t60 40.5t69.5 14.5q37 0 70 -14t58 -40.5t39.5 -63.5t14.5 -83z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="664" 
d="M578 -160v677h-77v-99q-15 22 -34.5 42t-43.5 35t-54 24t-66 9q-47 0 -91 -18.5t-79 -53t-56 -84.5t-21 -114t21 -114t56 -84.5t79 -52.5t91 -18q36 0 65.5 9t53.5 25t43.5 37t35.5 44v-264h77zM504 259q0 -45 -15.5 -82.5t-42 -63.5t-60 -40.5t-69.5 -14.5q-37 0 -70 14
t-58 40.5t-39.5 63.5t-14.5 83q0 47 14.5 84.5t39 63t57.5 39t71 13.5q36 0 69.5 -14.5t60 -40.5t42 -63t15.5 -82z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="405" 
d="M163 206q0 58 16 102.5t44 74.5t65.5 45t79.5 15h6v83q-37 1 -69 -9t-59 -29.5t-48 -46.5t-35 -59v135h-77v-517h77v206z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="498" 
d="M441 145v2q0 35 -14.5 59t-38.5 41t-53.5 28t-59.5 20q-26 8 -50 16t-42.5 18t-29.5 23.5t-11 31.5v2q0 32 27 53.5t73 21.5q38 0 77.5 -13.5t75.5 -36.5l35 58q-40 26 -89 41.5t-97 15.5q-38 0 -70.5 -10.5t-56 -30t-36.5 -47t-13 -60.5v-2q0 -36 15.5 -60t40 -40
t55 -26.5t60.5 -19.5q25 -7 48.5 -15t41.5 -18.5t28.5 -24.5t10.5 -33v-2q0 -38 -30 -60t-77 -22q-46 0 -91.5 17t-87.5 49l-39 -55q44 -35 101 -55.5t113 -20.5q39 0 72.5 10.5t58 30.5t39 48.5t14.5 65.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="404" 
d="M190 449h164v68h-164v156h-77v-156h-72v-68h72v-312q0 -39 11 -67t30.5 -45.5t46.5 -25.5t58 -8q28 0 50.5 5.5t42.5 16.5v66q-20 -10 -37.5 -14t-37.5 -4q-39 0 -63 19t-24 67v302z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="616" 
d="M453 221q0 -36 -12 -66t-32.5 -51.5t-49 -33t-61.5 -11.5q-69 0 -106 42t-37 114v302h-77v-321q0 -46 13.5 -84t39 -65.5t62 -42.5t83.5 -15q67 0 109 29.5t68 71.5v-90h76v517h-76v-296z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="587" 
d="M294 88l-174 429h-85l224 -521h68l225 521h-83z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="861" 
d="M463 519h-64l-139 -417l-136 415h-82l181 -521h68l140 408l139 -408h67l182 521h-80l-136 -415z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="574" 
d="M331 265l196 252h-86l-153 -200l-153 200h-88l195 -254l-203 -263h86l161 211l160 -211h89z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="591" 
d="M555 517h-82l-166 -431l-188 431h-85l235 -515q-23 -55 -47 -76t-59 -21q-25 0 -43.5 4.5t-37.5 14.5l-26 -61q26 -13 52 -19.5t58 -6.5q58 0 98.5 33t73.5 113z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="556" 
d="M399 452l-343 -403v-49h448v65h-344l344 403v49h-436v-65h331z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="483" 
d="M428 664l-12 51q-68 -12 -110 -30.5t-66 -43.5t-32.5 -57t-8.5 -71v-16t0.5 -21.5t0.5 -22.5v-16q0 -59 -25.5 -89.5t-92.5 -30.5h-26v-60h26q67 0 92.5 -31t25.5 -89v-17t-0.5 -22t-0.5 -21v-16q0 -39 8.5 -71t32.5 -57t66 -43.5t110 -30.5l12 51q-53 13 -83.5 27.5
t-47 33t-21 42.5t-4.5 58q0 15 0.5 37t0.5 38q0 31 -7 54t-20 39.5t-32.5 28t-44.5 19.5q48 15 76 46.5t28 94.5q0 16 -0.5 38t-0.5 37q0 34 4.5 58t21 42.5t47 32.5t83.5 28z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="303" 
d="M184 -128v926h-65v-926h65z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="483" 
d="M54 -90l13 -51q67 12 109.5 30.5t66.5 43.5t32.5 57t8.5 71v16t-0.5 21t-0.5 22v17q0 59 25 89.5t92 30.5h26v60h-26q-67 0 -92 30.5t-25 89.5v16q0 11 0.5 22.5t0.5 21.5v16q0 39 -8.5 71t-32.5 57t-66.5 43.5t-109.5 30.5l-13 -51q53 -14 84 -28t47.5 -32.5t21 -42.5
t4.5 -58q0 -15 -0.5 -37t-0.5 -38q0 -63 27 -94t77 -47q-48 -15 -76 -46.5t-28 -94.5q0 -16 0.5 -38t0.5 -37q0 -34 -4.5 -58t-21 -42.5t-47.5 -33t-84 -27.5z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="473" 
d="M400 352l-46 13q-11 -28 -21.5 -39t-30.5 -11q-13 0 -29.5 6.5t-34 14t-36 14t-34.5 6.5q-36 0 -58 -24.5t-37 -81.5l47 -13q11 28 21.5 39t30.5 11q13 0 29 -6.5t34 -14t36 -14t35 -6.5q36 0 57.5 24.5t36.5 81.5z" />
    <glyph glyph-name="nonbreakingspace" unicode="&#xa0;" horiz-adv-x="300" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="271" 
d="M90 700v-105h92v105h-92zM181 0v57l-24 445h-43l-23 -445v-57h90z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="581" 
d="M533 186l-48 45q-30 -32 -68 -54t-86 -23q-7 0 -13 0.5t-12 1.5l75 391q31 -9 54 -27t45 -40l50 52q-26 29 -57.5 51t-74.5 33l18 90h-65l-15 -81h-13q-56 0 -104.5 -21.5t-84 -58.5t-56 -86.5t-20.5 -104.5q0 -44 13 -83.5t36 -73t55.5 -58.5t71.5 -39l-22 -106h65
l18 93q8 -2 14 -2h15q71 0 120.5 28t88.5 73zM325 556l-74 -386q-51 23 -83 73t-32 113q0 42 14 78t38.5 63t58 43t72.5 16h6z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="644" 
d="M151 364h-84v-69h84v-228l-84 -21v-46h518v70h-355v225h279v69h-279v108q0 76 36.5 120.5t104.5 44.5q55 0 90 -23t64 -60l61 48q-35 46 -85 77t-129 31q-53 0 -94 -17t-69 -48.5t-43 -75.5t-15 -97v-108z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="720" 
d="M139 408q-2 -14 -3 -29t-1 -30v-25t2 -24h-81v-63h91q13 -56 39 -101.5t62.5 -78.5t82.5 -51t99 -18q42 0 77 11t64 30.5t52.5 46.5t44.5 58l-57 40q-36 -53 -77.5 -84t-100.5 -31q-75 0 -128.5 48.5t-75.5 129.5h259v63h-272q-1 13 -1.5 25t-0.5 26t1 28.5t3 28.5h270
v62h-257q23 76 73.5 123.5t117.5 47.5q32 0 57.5 -7t47.5 -21t41 -34.5t38 -46.5l60 43q-20 30 -43.5 55t-52.5 43t-65 28.5t-80 10.5q-51 0 -95 -18t-79 -50.5t-61 -77t-40 -96.5h-94v-62h83z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="686" 
d="M381 266h205v62h-182l255 372h-90l-225 -342l-223 342h-94l254 -372h-181v-62h205v-88h-205v-62h205v-116h76v116h205v62h-205v88z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="632" 
d="M570 341q0 26 -11 46.5t-36 37.5t-64.5 31t-96.5 28q-47 11 -78.5 21.5t-50 22t-26.5 23.5t-8 26q0 29 25 48t78 19q55 0 100.5 -21.5t80.5 -55.5l50 48q-42 43 -100.5 69t-128.5 26q-85 0 -134.5 -36.5t-49.5 -97.5q0 -33 20 -59t54 -43q-60 -10 -96 -39.5t-36 -75.5
q0 -26 11 -46.5t36 -37.5t64.5 -31.5t96.5 -27.5q47 -11 78.5 -21.5t50 -22t26.5 -23.5t8 -26q0 -29 -25 -48t-78 -19q-55 0 -100.5 21.5t-80.5 55.5l-50 -48q42 -43 100.5 -69t128.5 -26q85 0 134.5 36.5t49.5 97.5q0 33 -20 59t-54 43q60 10 96 39.5t36 75.5zM496 333
q0 -29 -29.5 -49.5t-79.5 -20.5q-20 0 -46 3.5t-63 12.5q-41 10 -68.5 20.5t-44 21t-23 22t-6.5 24.5q0 29 29.5 49.5t79.5 20.5q20 0 45.5 -3.5t63.5 -12.5q41 -10 68.5 -20.5t44 -21t23 -22t6.5 -24.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" 
d="M383 595v95h-90v-95h90zM207 595v95h-90v-95h90z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="830" 
d="M777 351q0 74 -28 140t-76.5 115t-114.5 77.5t-142 28.5t-142.5 -29t-115.5 -78.5t-77 -115.5t-28 -140t28 -140t76.5 -115t114.5 -77.5t142 -28.5t142.5 29t115.5 78.5t77 115.5t28 140zM744 351q0 -68 -25 -128.5t-69.5 -105.5t-105 -71.5t-130.5 -26.5t-130 26
t-104 71t-69 105t-25 128t25 128.5t69.5 105.5t104.5 71.5t131 26.5q70 0 130 -26t104 -71t69 -105t25 -128zM567 229l-36 35q-25 -23 -50 -36.5t-60 -13.5q-28 0 -52 11t-42 30.5t-28 45.5t-10 55t9.5 54.5t27 45t41.5 30.5t53 11q33 0 59.5 -14.5t48.5 -35.5l37 40
q-28 26 -60.5 43t-83.5 17q-41 0 -75.5 -15t-59.5 -41.5t-39 -61.5t-14 -74q0 -40 14 -75t39 -61t59 -41t74 -15q51 0 85 18t63 48z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="390" 
d="M313 414v173q0 57 -31.5 86.5t-90.5 29.5q-32 0 -56 -7t-50 -18l15 -42q22 10 42.5 16t43.5 6q37 0 57.5 -18t20.5 -53v-5q-17 5 -35.5 8t-44.5 3q-55 0 -89 -24t-34 -70q0 -23 9.5 -40t24.5 -28.5t34 -17t39 -5.5q32 0 55.5 12t40.5 32v-38h49zM318 289v47h-260v-47h260
zM264 520q0 -32 -26 -51t-61 -19q-26 0 -45 13t-19 38q0 24 19.5 39t55.5 15q24 0 42.5 -3t33.5 -8v-24z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="550" 
d="M433 476l-162 -213v-8l162 -214l58 34l-135 185l135 183zM210 476l-162 -213v-8l162 -214l59 34l-135 185l135 183z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="830" 
d="M777 351q0 74 -28 140t-76.5 115t-114.5 77.5t-142 28.5t-142.5 -29t-115.5 -78.5t-77 -115.5t-28 -140t28 -140t76.5 -115t114.5 -77.5t142 -28.5t142.5 29t115.5 78.5t77 115.5t28 140zM744 351q0 -68 -25 -128.5t-69.5 -105.5t-105 -71.5t-130.5 -26.5t-130 26
t-104 71t-69 105t-25 128t25 128.5t69.5 105.5t104.5 71.5t131 26.5q70 0 130 -26t104 -71t69 -105t25 -128zM579 434q0 52 -39 82t-101 30h-166v-371h55v140h95l107 -140h68l-115 149q42 10 69 37t27 73zM522 431q0 -32 -24.5 -49.5t-63.5 -17.5h-106v132h109
q40 0 62.5 -17t22.5 -48z" />
    <glyph glyph-name="macron" unicode="&#xaf;" 
d="M408 600v67h-316v-67h316z" />
    <glyph glyph-name="macron" unicode="&#x2c9;" 
d="M408 600v67h-316v-67h316z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="458" 
d="M386 553q0 31 -13 58.5t-35 48.5t-50 33.5t-59 12.5t-59.5 -12.5t-50 -33.5t-34.5 -48.5t-13 -58.5t13 -58.5t34.5 -48.5t50 -33.5t59.5 -12.5t59 12.5t50 33.5t35 48.5t13 58.5zM328 553q0 -20 -7.5 -38t-20.5 -32t-31.5 -22.5t-39.5 -8.5t-39.5 8.5t-31.5 22.5
t-20.5 32t-7.5 38t7.5 38t20.5 32t31.5 22.5t39.5 8.5t39.5 -8.5t31.5 -22.5t20.5 -32t7.5 -38z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="604" 
d="M546 191q0 34 -11 60.5t-29.5 47.5t-44.5 35.5t-56 24.5q23 9 45 23t39 34t27.5 46.5t10.5 61.5q0 41 -17 75.5t-47 59.5t-72 39t-92 14q-83 0 -141 -35.5t-100 -94.5l58 -42q36 48 79 74t102 26q69 0 109.5 -34.5t40.5 -88.5q0 -30 -12.5 -54.5t-34.5 -41t-52.5 -25.5
t-66.5 -9h-74v-68h72q89 0 138 -34t49 -94q0 -30 -12 -54t-33 -41.5t-50 -26.5t-63 -9q-67 0 -118 28t-89 76l-58 -50q44 -55 111 -90.5t152 -35.5q54 0 98 15.5t75.5 43t49 64.5t17.5 80z" />
    <glyph glyph-name="acute" unicode="&#xb4;" 
d="M243 595l113 108l-80 37l-95 -145h62z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="850" 
d="M782 351q0 73 -25.5 138.5t-72.5 115t-112.5 78.5t-145.5 29t-146 -29.5t-113 -79.5t-73 -115.5t-26 -138.5t25.5 -138.5t72.5 -115t112.5 -78.5t145.5 -29q83 0 153 34l132 -123h116v6l-172 165q61 51 95 124t34 157zM700 349q0 -60 -20.5 -112.5t-56.5 -91.5
t-86.5 -61.5t-110.5 -22.5t-110.5 23t-87.5 62t-57.5 92t-20.5 113t20.5 112.5t56.5 91.5t86.5 61.5t110.5 22.5t110.5 -23t87.5 -62t57.5 -92t20.5 -113z" />
    <glyph glyph-name="mu" unicode="&#x3bc;" horiz-adv-x="850" 
d="M782 351q0 73 -25.5 138.5t-72.5 115t-112.5 78.5t-145.5 29t-146 -29.5t-113 -79.5t-73 -115.5t-26 -138.5t25.5 -138.5t72.5 -115t112.5 -78.5t145.5 -29q83 0 153 34l132 -123h116v6l-172 165q61 51 95 124t34 157zM700 349q0 -60 -20.5 -112.5t-56.5 -91.5
t-86.5 -61.5t-110.5 -22.5t-110.5 23t-87.5 62t-57.5 92t-20.5 113t20.5 112.5t56.5 91.5t86.5 61.5t110.5 22.5t110.5 -23t87.5 -62t57.5 -92t20.5 -113z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="510" 
d="M312 700q-59 0 -107.5 -15t-83 -43t-54 -68.5t-19.5 -91.5q0 -53 21.5 -93.5t59 -68.5t88.5 -42t110 -14h11v-264h80v700h-106z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="245" 
d="M169 248v105h-92v-105h92z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" 
d="M240 10l-90 -131l75 -34l76 165h-61z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="390" 
d="M342 557q0 30 -11 57t-30.5 47.5t-46.5 32t-58 11.5q-32 0 -59 -12t-47 -32.5t-31 -47.5t-11 -58q0 -30 11 -57t30.5 -47.5t46 -32t58.5 -11.5t59 12t47 32.5t31 47.5t11 58zM339 289v47h-288v-47h288zM288 555q0 -42 -25 -71t-67 -29q-20 0 -37.5 8t-30 22t-19.5 32.5
t-7 39.5q0 42 25 71t67 29q41 0 67.5 -29.5t26.5 -72.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="550" 
d="M340 41l162 214v8l-162 213l-59 -34l135 -184l-135 -184zM117 41l162 214v8l-162 213l-58 -34l135 -184l-135 -184z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="537" 
d="M246 700v-105h92v105h-92zM327 335l5 5l-16 162h-51l-9 -108q-43 -6 -80.5 -21t-66 -40t-45 -60.5t-16.5 -82.5q0 -43 16 -79.5t45 -63t70.5 -41.5t92.5 -15q80 0 135.5 32t98.5 84l-51 49q-38 -45 -82 -69.5t-99 -24.5q-34 0 -61 10t-46 28t-29 42t-10 51q0 64 50.5 102
t148.5 40z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="790" 
d="M432 705h-74l-319 -705h81l83 185h381l82 -185h85zM553 256h-319l160 357zM363 762h62l-95 145l-80 -37z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="790" 
d="M432 705h-74l-319 -705h81l83 185h381l82 -185h85zM553 256h-319l160 357zM427 762l113 108l-80 37l-95 -145h62z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="790" 
d="M432 705h-74l-319 -705h81l83 185h381l82 -185h85zM553 256h-319l160 357zM394 826l75 -67h66l-106 131h-68l-106 -131h64z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="790" 
d="M432 705h-74l-319 -705h81l83 185h381l82 -185h85zM553 256h-319l160 357zM559 867l-47 13q-11 -27 -21.5 -38t-30.5 -11q-13 0 -29 6t-34 13.5t-36 14t-35 6.5q-36 0 -58 -24t-37 -81l47 -13q11 27 21.5 38t30.5 11q13 0 29 -6.5t34 -14t36 -13.5t35 -6q36 0 58 24
t37 81z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="790" 
d="M432 705h-74l-319 -705h81l83 185h381l82 -185h85zM553 256h-319l160 357zM528 762v95h-90v-95h90zM352 762v95h-90v-95h90z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="790" 
d="M346 680l-307 -680h81l83 185h381l82 -185h85l-307 680q24 13 39.5 35.5t15.5 51.5q0 21 -8.5 39t-23 31.5t-33 21.5t-39.5 8t-39.5 -8t-33 -21.5t-23 -31.5t-8.5 -39q0 -29 15.5 -51.5t39.5 -35.5zM457 767q0 -26 -18 -44t-44 -18t-44 18t-18 44t18 44t44 18t44 -18
t18 -44zM553 256h-319l160 357z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1045" 
d="M209 185h282v-185h494v72h-414v245h364v72h-364v239h409v72h-552l-411 -700h83zM467 630h24v-374h-242z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="738" 
d="M371 -9l-77 -112l75 -34l66 143q44 2 81 11.5t69 25.5t60 37.5t54 48.5l-52 51q-50 -48 -102 -74.5t-124 -26.5q-58 0 -107.5 22.5t-86 61.5t-57 92t-20.5 114t20.5 113.5t57 91.5t86 61t107.5 22q72 0 123.5 -27t96.5 -69l54 58q-26 25 -54 45.5t-61 35t-71.5 22.5
t-86.5 8q-77 0 -142 -28.5t-112 -78t-73.5 -115.5t-26.5 -141q0 -70 22.5 -131.5t62.5 -109t96 -78t122 -39.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="670" 
d="M605 700h-506v-700h511v72h-432v245h382v72h-382v239h427v72zM340 762h62l-95 145l-80 -37z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="670" 
d="M605 700h-506v-700h511v72h-432v245h382v72h-382v239h427v72zM359 762l113 108l-80 37l-95 -145h62z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="670" 
d="M605 700h-506v-700h511v72h-432v245h382v72h-382v239h427v72zM347 829l75 -67h66l-106 131h-68l-106 -131h64z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="670" 
d="M605 700h-506v-700h511v72h-432v245h382v72h-382v239h427v72zM481 762v95h-90v-95h90zM305 762v95h-90v-95h90z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="291" 
d="M106 700v-700h79v700h-79zM220 762h62l-95 145l-80 -37z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="291" 
d="M106 700v-700h79v700h-79zM177 762l113 108l-80 37l-95 -145h62z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="291" 
d="M106 700v-700h79v700h-79zM246 829l75 -67h66l-106 131h-68l-106 -131h64z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="291" 
d="M106 700v-700h79v700h-79zM373 762v95h-90v-95h90zM197 762v95h-90v-95h90z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="812" 
d="M129 316v-316h243q82 0 150.5 27t118 74t76.5 111.5t27 138.5t-27 138t-76.5 111t-118 73.5t-150.5 26.5h-243v-312h-75v-72h75zM208 388v239h164q66 0 120 -21.5t91.5 -59t58 -88.5t20.5 -109q0 -59 -20.5 -109t-58 -87.5t-91.5 -58.5t-120 -21h-164v243h210v72h-210z
" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="790" 
d="M614 139l-441 561h-74v-700h77v574l452 -574h63v700h-77v-561zM559 867l-47 13q-11 -27 -21.5 -38t-30.5 -11q-13 0 -29 6t-34 13.5t-36 14t-35 6.5q-36 0 -58 -24t-37 -81l47 -13q11 27 21.5 38t30.5 11q13 0 29 -6.5t34 -14t36 -13.5t35 -6q36 0 58 24t37 81z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="850" 
d="M782 351q0 73 -25.5 138.5t-72.5 115t-112.5 78.5t-145.5 29t-146 -29.5t-113 -79.5t-73 -115.5t-26 -138.5t25.5 -138.5t72.5 -115t112.5 -78.5t145.5 -29t146 29.5t113 79.5t73 115.5t26 138.5zM700 349q0 -60 -20.5 -112.5t-56.5 -91.5t-86.5 -61.5t-110.5 -22.5
t-110.5 23t-87.5 62t-57.5 92t-20.5 113t20.5 112.5t56.5 91.5t86.5 61.5t110.5 22.5t110.5 -23t87.5 -62t57.5 -92t20.5 -113zM389 762h62l-95 145l-80 -37z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="850" 
d="M782 351q0 73 -25.5 138.5t-72.5 115t-112.5 78.5t-145.5 29t-146 -29.5t-113 -79.5t-73 -115.5t-26 -138.5t25.5 -138.5t72.5 -115t112.5 -78.5t145.5 -29t146 29.5t113 79.5t73 115.5t26 138.5zM700 349q0 -60 -20.5 -112.5t-56.5 -91.5t-86.5 -61.5t-110.5 -22.5
t-110.5 23t-87.5 62t-57.5 92t-20.5 113t20.5 112.5t56.5 91.5t86.5 61.5t110.5 22.5t110.5 -23t87.5 -62t57.5 -92t20.5 -113zM461 762l113 108l-80 37l-95 -145h62z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="850" 
d="M782 351q0 73 -25.5 138.5t-72.5 115t-112.5 78.5t-145.5 29t-146 -29.5t-113 -79.5t-73 -115.5t-26 -138.5t25.5 -138.5t72.5 -115t112.5 -78.5t145.5 -29t146 29.5t113 79.5t73 115.5t26 138.5zM700 349q0 -60 -20.5 -112.5t-56.5 -91.5t-86.5 -61.5t-110.5 -22.5
t-110.5 23t-87.5 62t-57.5 92t-20.5 113t20.5 112.5t56.5 91.5t86.5 61.5t110.5 22.5t110.5 -23t87.5 -62t57.5 -92t20.5 -113zM424 829l75 -67h66l-106 131h-68l-106 -131h64z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="850" 
d="M782 351q0 73 -25.5 138.5t-72.5 115t-112.5 78.5t-145.5 29t-146 -29.5t-113 -79.5t-73 -115.5t-26 -138.5t25.5 -138.5t72.5 -115t112.5 -78.5t145.5 -29t146 29.5t113 79.5t73 115.5t26 138.5zM700 349q0 -60 -20.5 -112.5t-56.5 -91.5t-86.5 -61.5t-110.5 -22.5
t-110.5 23t-87.5 62t-57.5 92t-20.5 113t20.5 112.5t56.5 91.5t86.5 61.5t110.5 22.5t110.5 -23t87.5 -62t57.5 -92t20.5 -113zM589 867l-47 13q-11 -27 -21.5 -38t-30.5 -11q-13 0 -29 6t-34 13.5t-36 14t-35 6.5q-36 0 -58 -24t-37 -81l47 -13q11 27 21.5 38t30.5 11
q13 0 29 -6.5t34 -14t36 -13.5t35 -6q36 0 58 24t37 81z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="850" 
d="M782 351q0 73 -25.5 138.5t-72.5 115t-112.5 78.5t-145.5 29t-146 -29.5t-113 -79.5t-73 -115.5t-26 -138.5t25.5 -138.5t72.5 -115t112.5 -78.5t145.5 -29t146 29.5t113 79.5t73 115.5t26 138.5zM700 349q0 -60 -20.5 -112.5t-56.5 -91.5t-86.5 -61.5t-110.5 -22.5
t-110.5 23t-87.5 62t-57.5 92t-20.5 113t20.5 112.5t56.5 91.5t86.5 61.5t110.5 22.5t110.5 -23t87.5 -62t57.5 -92t20.5 -113zM558 762v95h-90v-95h90zM382 762v95h-90v-95h90z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="850" 
d="M782 351q0 70 -23.5 133.5t-67.5 111.5l99 109h-83l-62 -67q-44 35 -99 54.5t-120 19.5q-80 0 -146 -29.5t-113 -79.5t-73 -115.5t-26 -138.5q0 -70 23.5 -133.5t67.5 -112.5l-99 -108h83l62 67q44 -35 99 -54.5t120 -19.5q80 0 146 29.5t113 79.5t73 115.5t26 138.5z
M595 580l-383 -417q-30 38 -46.5 86t-16.5 102q0 60 20.5 113t57 92t87 62t110.5 23q51 0 93.5 -16.5t77.5 -44.5zM701 349q0 -60 -20.5 -113t-57 -92t-87 -62t-110.5 -23q-51 0 -93.5 16.5t-77.5 44.5l383 417q30 -38 46.5 -86t16.5 -102z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="759" 
d="M671 700h-79v-407q0 -115 -57 -173t-154 -58q-101 0 -157.5 61t-56.5 175v402h-79v-407q0 -74 21 -131t59.5 -95.5t92 -58t118.5 -19.5q66 0 119.5 19.5t92 58.5t59.5 97t21 135v401zM354 757h62l-95 145l-80 -37z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="759" 
d="M671 700h-79v-407q0 -115 -57 -173t-154 -58q-101 0 -157.5 61t-56.5 175v402h-79v-407q0 -74 21 -131t59.5 -95.5t92 -58t118.5 -19.5q66 0 119.5 19.5t92 58.5t59.5 97t21 135v401zM405 757l113 108l-80 37l-95 -145h62z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="759" 
d="M671 700h-79v-407q0 -115 -57 -173t-154 -58q-101 0 -157.5 61t-56.5 175v402h-79v-407q0 -74 21 -131t59.5 -95.5t92 -58t118.5 -19.5q66 0 119.5 19.5t92 58.5t59.5 97t21 135v401zM378 826l75 -67h66l-106 131h-68l-106 -131h64z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="759" 
d="M671 700h-79v-407q0 -115 -57 -173t-154 -58q-101 0 -157.5 61t-56.5 175v402h-79v-407q0 -74 21 -131t59.5 -95.5t92 -58t118.5 -19.5q66 0 119.5 19.5t92 58.5t59.5 97t21 135v401zM512 759v95h-90v-95h90zM336 759v95h-90v-95h90z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="716" 
d="M398 278l291 422h-92l-238 -350l-236 350h-96l291 -423v-277h80v278zM391 757l113 108l-80 37l-95 -145h62z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="673" 
d="M623 357q0 52 -18.5 93.5t-53 70t-83 44t-107.5 15.5h-183v120h-79v-700h79v126h170q56 0 106 14.5t87.5 44t59.5 72.5t22 100zM543 354q0 -35 -13.5 -63.5t-39 -49t-61 -32t-78.5 -11.5h-173v309h177q85 0 136.5 -38.5t51.5 -114.5z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="620" 
d="M519 556q0 38 -14.5 71t-41.5 57.5t-65 38.5t-85 14q-52 0 -94 -17t-71.5 -48t-45.5 -74.5t-16 -96.5v-501h77v502q0 77 41 122t105 45q59 0 95 -32.5t36 -85.5q0 -31 -11.5 -56t-31 -45t-47 -35t-58.5 -27v-57q88 -9 139.5 -43t51.5 -95q0 -64 -51 -97.5t-140 -35.5v-62
q62 -1 112 12t85 38t54 61.5t19 83.5q0 39 -15 69t-40.5 52t-58 36t-68.5 22q26 11 51 28t44.5 39.5t31.5 51.5t12 65z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="579" 
d="M273 456q73 0 113.5 -35.5t40.5 -105.5v-18q-35 10 -72 16t-87 6q-49 0 -89.5 -11t-70 -32t-45.5 -52.5t-16 -72.5t16.5 -71t44 -50.5t62 -30.5t70.5 -10q69 0 114.5 27t72.5 62v-78h74v316q0 103 -57 155.5t-163 52.5q-57 0 -101 -11.5t-87 -31.5l23 -63
q36 17 73.5 27.5t83.5 10.5zM276 262q48 0 85.5 -7t66.5 -15v-50q0 -31 -13.5 -56.5t-37 -44t-55 -29t-67.5 -10.5q-26 0 -49.5 7t-41.5 20.5t-28.5 32.5t-10.5 44q0 50 39 79t112 29zM261 595h62l-95 145l-80 -37z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="579" 
d="M273 456q73 0 113.5 -35.5t40.5 -105.5v-18q-35 10 -72 16t-87 6q-49 0 -89.5 -11t-70 -32t-45.5 -52.5t-16 -72.5t16.5 -71t44 -50.5t62 -30.5t70.5 -10q69 0 114.5 27t72.5 62v-78h74v316q0 103 -57 155.5t-163 52.5q-57 0 -101 -11.5t-87 -31.5l23 -63
q36 17 73.5 27.5t83.5 10.5zM276 262q48 0 85.5 -7t66.5 -15v-50q0 -31 -13.5 -56.5t-37 -44t-55 -29t-67.5 -10.5q-26 0 -49.5 7t-41.5 20.5t-28.5 32.5t-10.5 44q0 50 39 79t112 29zM310 595l113 108l-80 37l-95 -145h62z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="579" 
d="M273 456q73 0 113.5 -35.5t40.5 -105.5v-18q-35 10 -72 16t-87 6q-49 0 -89.5 -11t-70 -32t-45.5 -52.5t-16 -72.5t16.5 -71t44 -50.5t62 -30.5t70.5 -10q69 0 114.5 27t72.5 62v-78h74v316q0 103 -57 155.5t-163 52.5q-57 0 -101 -11.5t-87 -31.5l23 -63
q36 17 73.5 27.5t83.5 10.5zM276 262q48 0 85.5 -7t66.5 -15v-50q0 -31 -13.5 -56.5t-37 -44t-55 -29t-67.5 -10.5q-26 0 -49.5 7t-41.5 20.5t-28.5 32.5t-10.5 44q0 50 39 79t112 29zM290 662l75 -67h66l-106 131h-68l-106 -131h64z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="579" 
d="M273 456q73 0 113.5 -35.5t40.5 -105.5v-18q-35 10 -72 16t-87 6q-49 0 -89.5 -11t-70 -32t-45.5 -52.5t-16 -72.5t16.5 -71t44 -50.5t62 -30.5t70.5 -10q69 0 114.5 27t72.5 62v-78h74v316q0 103 -57 155.5t-163 52.5q-57 0 -101 -11.5t-87 -31.5l23 -63
q36 17 73.5 27.5t83.5 10.5zM276 262q48 0 85.5 -7t66.5 -15v-50q0 -31 -13.5 -56.5t-37 -44t-55 -29t-67.5 -10.5q-26 0 -49.5 7t-41.5 20.5t-28.5 32.5t-10.5 44q0 50 39 79t112 29zM456 700l-47 13q-11 -27 -21.5 -38t-30.5 -11q-13 0 -29 6t-34 13.5t-36 14t-35 6.5
q-36 0 -58 -24t-37 -81l47 -13q11 27 21.5 38t30.5 11q13 0 29 -6.5t34 -14t36 -13.5t35 -6q36 0 58 24t37 81z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="579" 
d="M273 456q73 0 113.5 -35.5t40.5 -105.5v-18q-35 10 -72 16t-87 6q-49 0 -89.5 -11t-70 -32t-45.5 -52.5t-16 -72.5t16.5 -71t44 -50.5t62 -30.5t70.5 -10q69 0 114.5 27t72.5 62v-78h74v316q0 103 -57 155.5t-163 52.5q-57 0 -101 -11.5t-87 -31.5l23 -63
q36 17 73.5 27.5t83.5 10.5zM276 262q48 0 85.5 -7t66.5 -15v-50q0 -31 -13.5 -56.5t-37 -44t-55 -29t-67.5 -10.5q-26 0 -49.5 7t-41.5 20.5t-28.5 32.5t-10.5 44q0 50 39 79t112 29zM424 595v95h-90v-95h90zM248 595v95h-90v-95h90z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="579" 
d="M273 456q73 0 113.5 -35.5t40.5 -105.5v-18q-35 10 -72 16t-87 6q-49 0 -89.5 -11t-70 -32t-45.5 -52.5t-16 -72.5t16.5 -71t44 -50.5t62 -30.5t70.5 -10q69 0 114.5 27t72.5 62v-78h74v316q0 103 -57 155.5t-163 52.5q-57 0 -101 -11.5t-87 -31.5l23 -63
q36 17 73.5 27.5t83.5 10.5zM276 262q48 0 85.5 -7t66.5 -15v-50q0 -31 -13.5 -56.5t-37 -44t-55 -29t-67.5 -10.5q-26 0 -49.5 7t-41.5 20.5t-28.5 32.5t-10.5 44q0 50 39 79t112 29zM395 693q0 21 -8.5 39t-23 31.5t-33 21.5t-39.5 8t-39.5 -8t-33 -21.5t-23 -31.5
t-8.5 -39t8.5 -39t23 -31.5t33 -21.5t39.5 -8t39.5 8t33 21.5t23 31.5t8.5 39zM353 693q0 -26 -18 -44t-44 -18t-44 18t-18 44t18 44t44 18t44 -18t18 -44z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="953" 
d="M468 92q35 -49 88.5 -76.5t116.5 -27.5q74 0 124.5 26.5t88.5 70.5l-48 43q-32 -33 -70.5 -53t-92.5 -20q-34 0 -65 11.5t-55.5 34t-40.5 55t-20 74.5h409q1 7 1.5 13t0.5 11q0 57 -16.5 107t-47 87t-75.5 58.5t-102 21.5q-64 0 -115.5 -31.5t-82.5 -83.5
q-20 56 -68 83.5t-122 27.5q-56 0 -98 -11.5t-85 -31.5l23 -63q36 17 71.5 27.5t80.5 10.5q72 0 110.5 -35.5t38.5 -105.5v-18q-35 10 -70 16t-83 6t-87.5 -11t-68.5 -32t-45 -52.5t-16 -72.5q0 -40 16.5 -70.5t43.5 -50.5t62 -30.5t73 -10.5q72 0 128 30t98 73zM493 287
q4 38 18 70t36 55.5t51.5 37t63.5 13.5q38 0 67.5 -14.5t50 -38.5t32 -56t14.5 -67h-333zM416 241q3 -53 21 -98q-36 -41 -82.5 -66.5t-103.5 -25.5q-26 0 -49 7t-40 20.5t-27 32.5t-10 44q0 50 38 78.5t109 28.5q43 0 79 -6.5t65 -14.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="571" 
d="M271 -7l-78 -114l75 -34l66 143q67 4 112.5 31t83.5 69l-49 46q-31 -33 -69.5 -55t-88.5 -22q-41 0 -75.5 15.5t-60 43t-40 64.5t-14.5 79t14 78.5t39 64t59 43t73 15.5q53 0 89.5 -23t67.5 -55l51 54q-18 19 -39 36t-46 29.5t-55 19.5t-67 7q-57 0 -105.5 -21.5
t-84 -58.5t-55.5 -86.5t-20 -104.5q0 -49 16 -93.5t45 -79.5t69 -59t87 -32z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="592" 
d="M132 230h410q1 7 1 13v11q0 57 -16 107t-47 87t-76 58.5t-102 21.5q-54 0 -99.5 -21t-78.5 -57.5t-51.5 -86t-18.5 -105.5q0 -60 20.5 -110t55.5 -85.5t82 -55t100 -19.5q74 0 124 26.5t89 70.5l-48 43q-32 -33 -70.5 -53t-92.5 -20q-34 0 -65 11.5t-56 34t-41 55
t-20 74.5zM132 287q4 38 18 70t36 55.5t51 37t63 13.5q39 0 68.5 -14.5t50 -38.5t32 -56t14.5 -67h-333zM276 595h62l-95 145l-80 -37z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="592" 
d="M132 230h410q1 7 1 13v11q0 57 -16 107t-47 87t-76 58.5t-102 21.5q-54 0 -99.5 -21t-78.5 -57.5t-51.5 -86t-18.5 -105.5q0 -60 20.5 -110t55.5 -85.5t82 -55t100 -19.5q74 0 124 26.5t89 70.5l-48 43q-32 -33 -70.5 -53t-92.5 -20q-34 0 -65 11.5t-56 34t-41 55
t-20 74.5zM132 287q4 38 18 70t36 55.5t51 37t63 13.5q39 0 68.5 -14.5t50 -38.5t32 -56t14.5 -67h-333zM316 595l113 108l-80 37l-95 -145h62z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="592" 
d="M132 230h410q1 7 1 13v11q0 57 -16 107t-47 87t-76 58.5t-102 21.5q-54 0 -99.5 -21t-78.5 -57.5t-51.5 -86t-18.5 -105.5q0 -60 20.5 -110t55.5 -85.5t82 -55t100 -19.5q74 0 124 26.5t89 70.5l-48 43q-32 -33 -70.5 -53t-92.5 -20q-34 0 -65 11.5t-56 34t-41 55
t-20 74.5zM132 287q4 38 18 70t36 55.5t51 37t63 13.5q39 0 68.5 -14.5t50 -38.5t32 -56t14.5 -67h-333zM298 662l75 -67h66l-106 131h-68l-106 -131h64z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="592" 
d="M132 230h410q1 7 1 13v11q0 57 -16 107t-47 87t-76 58.5t-102 21.5q-54 0 -99.5 -21t-78.5 -57.5t-51.5 -86t-18.5 -105.5q0 -60 20.5 -110t55.5 -85.5t82 -55t100 -19.5q74 0 124 26.5t89 70.5l-48 43q-32 -33 -70.5 -53t-92.5 -20q-34 0 -65 11.5t-56 34t-41 55
t-20 74.5zM132 287q4 38 18 70t36 55.5t51 37t63 13.5q39 0 68.5 -14.5t50 -38.5t32 -56t14.5 -67h-333zM432 595v95h-90v-95h90zM256 595v95h-90v-95h90z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="263" 
d="M170 0v517h-77v-517h77zM207 595h62l-95 145l-80 -37z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="263" 
d="M170 0v517h-77v-517h77zM163 595l113 108l-80 37l-95 -145h62z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="263" 
d="M170 0v517h-77v-517h77zM233 662l75 -67h66l-106 131h-68l-106 -131h64z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="263" 
d="M170 0v517h-77v-517h77zM360 595v95h-90v-95h90zM184 595v95h-90v-95h90z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="633" 
d="M322 615l131 -184q-33 23 -70.5 39t-91.5 16q-48 0 -91 -17.5t-75.5 -49t-51.5 -75.5t-19 -98q0 -53 19.5 -100t54 -82t81.5 -55.5t103 -20.5q61 0 110.5 21.5t84 57.5t53 84t18.5 101q0 35 -6 66.5t-17.5 60.5t-27.5 58t-37 59l-101 149l102 47l-35 47l-99 -48l-26 39
h-92l50 -70l-131 -61l36 -47zM499 244q0 -38 -13 -72t-37 -60t-57.5 -41t-74.5 -15t-74.5 15.5t-58.5 41.5t-38.5 59.5t-13.5 70.5q0 38 12.5 71.5t35.5 58t55.5 38.5t73.5 14t75.5 -14.5t60 -39t40 -57.5t14.5 -70z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="616" 
d="M163 296q0 36 11.5 66t32.5 51.5t49.5 33t61.5 11.5q69 0 106 -42t37 -114v-302h77v321q0 46 -13.5 84t-39 65.5t-62 42.5t-83.5 15q-67 0 -109 -29.5t-68 -71.5v90h-77v-517h77v296zM476 700l-47 13q-11 -27 -21.5 -38t-30.5 -11q-13 0 -29 6t-34 13.5t-36 14t-35 6.5
q-36 0 -58 -24t-37 -81l47 -13q11 27 21.5 38t30.5 11q13 0 29 -6.5t34 -14t36 -13.5t35 -6q36 0 58 24t37 81z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="646" 
d="M593 259q0 55 -20 104t-56 85.5t-85.5 58t-107.5 21.5q-59 0 -108.5 -21.5t-85.5 -58.5t-56 -86.5t-20 -104.5t20 -104t56 -85.5t85 -58t107 -21.5t108 21.5t86 58.5t56.5 86.5t20.5 104.5zM514 257q0 -42 -14 -78.5t-39 -63.5t-60 -42.5t-77 -15.5q-41 0 -76 16
t-60.5 43t-40 64t-14.5 79t14 78.5t38.5 64t59.5 43t77 15.5q41 0 76 -16t61 -43.5t40.5 -64.5t14.5 -79zM292 595h62l-95 145l-80 -37z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="646" 
d="M593 259q0 55 -20 104t-56 85.5t-85.5 58t-107.5 21.5q-59 0 -108.5 -21.5t-85.5 -58.5t-56 -86.5t-20 -104.5t20 -104t56 -85.5t85 -58t107 -21.5t108 21.5t86 58.5t56.5 86.5t20.5 104.5zM514 257q0 -42 -14 -78.5t-39 -63.5t-60 -42.5t-77 -15.5q-41 0 -76 16
t-60.5 43t-40 64t-14.5 79t14 78.5t38.5 64t59.5 43t77 15.5q41 0 76 -16t61 -43.5t40.5 -64.5t14.5 -79zM355 595l113 108l-80 37l-95 -145h62z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="646" 
d="M593 259q0 55 -20 104t-56 85.5t-85.5 58t-107.5 21.5q-59 0 -108.5 -21.5t-85.5 -58.5t-56 -86.5t-20 -104.5t20 -104t56 -85.5t85 -58t107 -21.5t108 21.5t86 58.5t56.5 86.5t20.5 104.5zM514 257q0 -42 -14 -78.5t-39 -63.5t-60 -42.5t-77 -15.5q-41 0 -76 16
t-60.5 43t-40 64t-14.5 79t14 78.5t38.5 64t59.5 43t77 15.5q41 0 76 -16t61 -43.5t40.5 -64.5t14.5 -79zM322 662l75 -67h66l-106 131h-68l-106 -131h64z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="646" 
d="M593 259q0 55 -20 104t-56 85.5t-85.5 58t-107.5 21.5q-59 0 -108.5 -21.5t-85.5 -58.5t-56 -86.5t-20 -104.5t20 -104t56 -85.5t85 -58t107 -21.5t108 21.5t86 58.5t56.5 86.5t20.5 104.5zM514 257q0 -42 -14 -78.5t-39 -63.5t-60 -42.5t-77 -15.5q-41 0 -76 16
t-60.5 43t-40 64t-14.5 79t14 78.5t38.5 64t59.5 43t77 15.5q41 0 76 -16t61 -43.5t40.5 -64.5t14.5 -79zM487 700l-47 13q-11 -27 -21.5 -38t-30.5 -11q-13 0 -29 6t-34 13.5t-36 14t-35 6.5q-36 0 -58 -24t-37 -81l47 -13q11 27 21.5 38t30.5 11q13 0 29 -6.5t34 -14
t36 -13.5t35 -6q36 0 58 24t37 81z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="646" 
d="M593 259q0 55 -20 104t-56 85.5t-85.5 58t-107.5 21.5q-59 0 -108.5 -21.5t-85.5 -58.5t-56 -86.5t-20 -104.5t20 -104t56 -85.5t85 -58t107 -21.5t108 21.5t86 58.5t56.5 86.5t20.5 104.5zM514 257q0 -42 -14 -78.5t-39 -63.5t-60 -42.5t-77 -15.5q-41 0 -76 16
t-60.5 43t-40 64t-14.5 79t14 78.5t38.5 64t59.5 43t77 15.5q41 0 76 -16t61 -43.5t40.5 -64.5t14.5 -79zM456 595v95h-90v-95h90zM280 595v95h-90v-95h90z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="646" 
d="M593 259q0 52 -17 97.5t-48 80.5l80 86h-78l-45 -49q-32 26 -73 40t-88 14q-59 0 -108.5 -21.5t-85.5 -58.5t-56 -86.5t-20 -104.5q0 -51 17 -96t47 -81l-79 -85h77l45 46q33 -25 73.5 -39t87.5 -14q58 0 108 21.5t86 58.5t56.5 86.5t20.5 104.5zM438 419l-268 -283
q-39 53 -39 123q0 42 14 79t39.5 64.5t60.5 43t77 15.5q68 0 116 -42zM515 257q0 -42 -14 -79t-39 -64.5t-60.5 -43t-77.5 -15.5q-66 0 -116 41l268 285q39 -53 39 -124z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="616" 
d="M453 221q0 -36 -12 -66t-32.5 -51.5t-49 -33t-61.5 -11.5q-69 0 -106 42t-37 114v302h-77v-321q0 -46 13.5 -84t39 -65.5t62 -42.5t83.5 -15q67 0 109 29.5t68 71.5v-90h76v517h-76v-296zM284 592h62l-95 145l-80 -37z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="616" 
d="M453 221q0 -36 -12 -66t-32.5 -51.5t-49 -33t-61.5 -11.5q-69 0 -106 42t-37 114v302h-77v-321q0 -46 13.5 -84t39 -65.5t62 -42.5t83.5 -15q67 0 109 29.5t68 71.5v-90h76v517h-76v-296zM328 592l113 108l-80 37l-95 -145h62z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="616" 
d="M453 221q0 -36 -12 -66t-32.5 -51.5t-49 -33t-61.5 -11.5q-69 0 -106 42t-37 114v302h-77v-321q0 -46 13.5 -84t39 -65.5t62 -42.5t83.5 -15q67 0 109 29.5t68 71.5v-90h76v517h-76v-296zM308 662l75 -67h66l-106 131h-68l-106 -131h64z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="616" 
d="M453 221q0 -36 -12 -66t-32.5 -51.5t-49 -33t-61.5 -11.5q-69 0 -106 42t-37 114v302h-77v-321q0 -46 13.5 -84t39 -65.5t62 -42.5t83.5 -15q67 0 109 29.5t68 71.5v-90h76v517h-76v-296zM442 595v95h-90v-95h90zM266 595v95h-90v-95h90z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="591" 
d="M555 517h-82l-166 -431l-188 431h-85l235 -515q-23 -55 -47 -76t-59 -21q-25 0 -43.5 4.5t-37.5 14.5l-26 -61q26 -13 52 -19.5t58 -6.5q58 0 98.5 33t73.5 113zM329 592l113 108l-80 37l-95 -145h62z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="664" 
d="M608 259q0 64 -21 114t-56 84.5t-79.5 52.5t-90.5 18q-36 0 -65.5 -9.5t-53.5 -25t-43.5 -36.5t-35.5 -44v317h-77v-890h77v259q15 -22 34.5 -42t43.5 -35t54 -24t66 -9q47 0 91.5 18.5t79 53t55.5 84.5t21 114zM529 258q0 -47 -14.5 -84.5t-39 -63t-58 -39t-70.5 -13.5
q-36 0 -69.5 14.5t-60 40.5t-42 63.5t-15.5 82.5t15.5 82t42 63t60 40.5t69.5 14.5q37 0 70 -14t58 -40.5t39.5 -63.5t14.5 -83z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="591" 
d="M555 517h-82l-166 -431l-188 431h-85l235 -515q-23 -55 -47 -76t-59 -21q-25 0 -43.5 4.5t-37.5 14.5l-26 -61q26 -13 52 -19.5t58 -6.5q58 0 98.5 33t73.5 113zM429 595v95h-90v-95h90zM253 595v95h-90v-95h90z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="263" 
d="M170 0v517h-77v-517h77z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="649" 
d="M602 0v73h-395v253l216 65v74l-216 -66v301h-78v-325l-75 -23v-73l75 22v-301h473z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="323" 
d="M200 343l76 24v75l-76 -24v312h-76v-335l-76 -23v-75l76 24v-321h76v343z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1131" 
d="M1071 0v72h-414v245h364v72h-364v239h409v72h-626q-83 0 -151 -27t-117.5 -74.5t-76.5 -111.5t-27 -138t27 -138t76.5 -111t117.5 -73.5t151 -26.5h631zM578 73h-138q-66 0 -120 21.5t-91.5 59t-58 88t-20.5 109.5q0 58 20.5 108.5t58 88t91.5 58.5t120 21h138v-554z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1049" 
d="M548 128q31 -66 90.5 -103t131.5 -37q73 0 123.5 26.5t88.5 70.5l-48 43q-32 -33 -70.5 -53t-92.5 -20q-34 0 -65 11.5t-55.5 34t-40.5 55t-20 74.5h409q1 7 1 13v11q0 57 -16 107t-47 87t-75 58.5t-101 21.5q-72 0 -127.5 -38t-84.5 -101q-32 62 -91 100.5t-136 38.5
q-57 0 -106 -21.5t-85 -58.5t-56.5 -86.5t-20.5 -104.5t20.5 -104t56 -85.5t84 -58t104.5 -21.5q78 0 137.5 39t91.5 101zM589 287q4 38 18 70t36 55.5t51.5 37t63.5 13.5q38 0 67.5 -14.5t50 -38.5t32 -56t14.5 -67h-333zM511 257q0 -42 -14 -78.5t-38.5 -63.5t-59 -42.5
t-76.5 -15.5q-41 0 -75.5 16t-60 43t-40 64t-14.5 79t14 78.5t38.5 64t59 43t76.5 15.5q40 0 75 -16t60.5 -43.5t40 -64.5t14.5 -79z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="640" 
d="M160 525q0 24 10.5 45t30 36t47 24t61.5 9q54 0 103.5 -18t98.5 -58l46 61q-54 43 -111.5 64.5t-134.5 21.5q-50 0 -92 -14.5t-73 -40t-48 -60.5t-17 -77q0 -44 14 -76t43 -56t73.5 -41t104.5 -30q55 -12 90.5 -25t55.5 -29t28 -35.5t8 -44.5q0 -54 -42.5 -87t-113.5 -33
q-73 0 -129 24.5t-111 74.5l-49 -58q63 -57 132.5 -84.5t153.5 -27.5q52 0 96 14t75.5 40t49.5 63t18 82q0 81 -55.5 127t-172.5 72q-58 12 -95 25.5t-58 30t-29 36.5t-8 45zM325 826l-75 67h-66l106 -131h68l106 131h-64z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="498" 
d="M441 145v2q0 35 -14.5 59t-38.5 41t-53.5 28t-59.5 20q-26 8 -50 16t-42.5 18t-29.5 23.5t-11 31.5v2q0 32 27 53.5t73 21.5q38 0 77.5 -13.5t75.5 -36.5l35 58q-40 26 -89 41.5t-97 15.5q-38 0 -70.5 -10.5t-56 -30t-36.5 -47t-13 -60.5v-2q0 -36 15.5 -60t40 -40
t55 -26.5t60.5 -19.5q25 -7 48.5 -15t41.5 -18.5t28.5 -24.5t10.5 -33v-2q0 -38 -30 -60t-77 -22q-46 0 -91.5 17t-87.5 49l-39 -55q44 -35 101 -55.5t113 -20.5q39 0 72.5 10.5t58 30.5t39 48.5t14.5 65.5zM253 659l-75 67h-66l106 -131h68l106 131h-64z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="716" 
d="M398 278l291 422h-92l-238 -350l-236 350h-96l291 -423v-277h80v278zM491 759v95h-90v-95h90zM315 759v95h-90v-95h90z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="693" 
d="M520 629l-453 -576v-53h561v71h-454l454 576v53h-546v-71h438zM352 826l-75 67h-66l106 -131h68l106 131h-64z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="556" 
d="M399 452l-343 -403v-49h448v65h-344l344 403v49h-436v-65h331zM288 659l-75 67h-66l106 -131h68l106 131h-64z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="514" 
d="M478 628v66q-18 6 -36.5 8.5t-44.5 2.5q-69 0 -105.5 -35.5t-50.5 -110.5l-23 -119h-109v-67h98l-44 -236q-8 -40 -27 -58t-51 -18q-25 0 -44 4v-66q11 -2 23 -3t29 -1q64 0 100 32t49 102l45 244h156v67h-144l20 108q9 48 30 69t60 21q16 0 33.5 -2.5t35.5 -7.5z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" 
d="M249 662l75 -67h66l-106 131h-68l-106 -131h64z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" 
d="M251 659l-75 67h-66l106 -131h68l106 131h-64z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" 
d="M398 726h-56q-9 -32 -31.5 -50.5t-60.5 -18.5t-60.5 18.5t-31.5 50.5h-56q5 -63 45 -98t103 -35t103 35t45 98z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" 
d="M297 595v96h-94v-96h94z" />
    <glyph glyph-name="ring" unicode="&#x2da;" 
d="M354 693q0 21 -8.5 39t-23 31.5t-33 21.5t-39.5 8t-39.5 -8t-33 -21.5t-23 -31.5t-8.5 -39t8.5 -39t23 -31.5t33 -21.5t39.5 -8t39.5 8t33 21.5t23 31.5t8.5 39zM312 693q0 -26 -18 -44t-44 -18t-44 18t-18 44t18 44t44 18t44 -18t18 -44z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" 
d="M257 10h-60q-6 -14 -10 -31t-4 -35q0 -51 44.5 -82t132.5 -26v41q-57 3 -83 23.5t-26 58.5q0 25 6 51z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" 
d="M414 700l-47 13q-11 -27 -21.5 -38t-30.5 -11q-13 0 -29 6t-34 13.5t-36 14t-35 6.5q-36 0 -58 -24t-37 -81l47 -13q11 27 21.5 38t30.5 11q13 0 29 -6.5t34 -14t36 -13.5t35 -6q36 0 58 24t37 81z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" 
d="M309 595l109 108l-75 37l-95 -145h61zM146 595l108 108l-75 37l-95 -145h62z" />
    <glyph glyph-name="Omega" unicode="&#x3a9;" horiz-adv-x="735" 
d="M559 515v42q0 110 90 110q20 0 37 -3.5t37 -9.5v67q-18 7 -38 10t-47 3q-75 0 -115 -44t-40 -129v-45h-71v-67h71v-449h76v449h163v66h-163zM191 515v42q0 110 90 110q20 0 37 -3.5t38 -9.5v67q-19 7 -38.5 10t-46.5 3q-75 0 -115.5 -44t-40.5 -129v-45h-71v-67h71v-449
h77v449h163v66h-164z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="830" 
d="M777 351q0 74 -28 140t-76.5 115t-114.5 77.5t-142 28.5t-142.5 -29t-115.5 -78.5t-77 -115.5t-28 -140t28 -140t76.5 -115t114.5 -77.5t142 -28.5t142.5 29t115.5 78.5t77 115.5t28 140zM744 351q0 -68 -25 -128.5t-69.5 -105.5t-105 -71.5t-130.5 -26.5t-130 26
t-104 71t-69 105t-25 128t25 128.5t69.5 105.5t104.5 71.5t131 26.5q70 0 130 -26t104 -71t69 -105t25 -128zM582 422q0 58 -39 89.5t-105 31.5h-146v-371h56v125h83q30 0 57.5 7.5t48 23.5t33 39t12.5 55zM526 421q0 -35 -25.5 -54.5t-68.5 -19.5h-84v146h85q43 0 68 -17.5
t25 -54.5z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="528" 
d="M463 262v77h-398v-77h398z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="898" 
d="M833 262v77h-768v-77h768z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="245" 
d="M194 670l-12 35q-57 -13 -83.5 -44.5t-26.5 -92.5v-90h92v105h-37q-2 32 13.5 52.5t53.5 34.5z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="245" 
d="M51 508l12 -35q57 13 84 44.5t27 92.5v90h-92v-105h36q3 -32 -13 -52.5t-54 -34.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="245" 
d="M46 -87l12 -35q57 13 84 44.5t27 92.5v90h-92v-105h36q3 -32 -13 -52.5t-54 -34.5z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="445" 
d="M394 670l-12 35q-57 -13 -83.5 -44.5t-26.5 -92.5v-90h91v105h-36q-2 32 13.5 52.5t53.5 34.5zM194 670l-12 35q-57 -13 -83.5 -44.5t-26.5 -92.5v-90h92v105h-37q-2 32 13.5 52.5t53.5 34.5z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="445" 
d="M251 508l12 -35q57 13 83.5 44.5t26.5 92.5v90h-91v-105h36q3 -32 -13 -52.5t-54 -34.5zM51 508l12 -35q57 13 84 44.5t27 92.5v90h-92v-105h36q3 -32 -13 -52.5t-54 -34.5z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="445" 
d="M246 -87l12 -35q57 13 83.5 44.5t26.5 92.5v90h-91v-105h36q3 -32 -13 -52.5t-54 -34.5zM46 -87l12 -35q57 13 84 44.5t27 92.5v90h-92v-105h36q3 -32 -13 -52.5t-54 -34.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="410" 
d="M226 520l124 -8v55l-123 -8l9 141h-62l9 -141l-123 8v-55l124 8l-6 -282h54z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="430" 
d="M360 133v55l-124 -8l6 170l-6 170l124 -8v55l-123 -8l9 141h-62l9 -141l-123 8v-55l124 8l-6 -170l6 -170l-124 8v-55l123 8l-9 -141h62l-9 141z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="478" 
d="M370 352q0 27 -10.5 51t-28 41.5t-41.5 28t-51 10.5t-51 -10.5t-41.5 -28t-28 -41.5t-10.5 -51t10.5 -51t28 -41.5t41.5 -28t51 -10.5t51 10.5t41.5 28t28 41.5t10.5 51z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="723" 
d="M647 0v103h-90v-103h90zM406 0v103h-89v-103h89zM166 0v103h-89v-103h89z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1211" 
d="M369 530q0 36 -11.5 68t-32 56.5t-49.5 39t-64 14.5q-36 0 -65.5 -14.5t-50.5 -39.5t-32.5 -57.5t-11.5 -68.5t11.5 -68t32 -56.5t49.5 -39t65 -14.5t65 14.5t50 39.5t32.5 57.5t11.5 68.5zM638 700l-514 -700h72l513 700h-71zM1158 172q0 36 -11 68t-31.5 56.5t-49.5 39
t-65 14.5t-65 -14.5t-50 -39.5t-32.5 -57.5t-11.5 -68.5t11.5 -68t32 -56.5t49.5 -39t64 -14.5q36 0 65.5 14.5t50 39.5t32 57.5t11.5 68.5zM781 172q0 36 -11 68t-31.5 56.5t-49.5 39t-65 14.5t-65 -14.5t-50 -39.5t-32.5 -57.5t-11.5 -68.5t11.5 -68t32 -56.5t49.5 -39
t64 -14.5q36 0 65.5 14.5t50 39.5t32 57.5t11.5 68.5zM303 528q0 -26 -6.5 -48t-18.5 -38.5t-29 -26t-37 -9.5t-36.5 9.5t-29.5 26t-20 39t-7 49.5q0 25 6.5 47.5t18.5 39t28.5 26t37.5 9.5q19 0 36 -9.5t29.5 -26t20 -39t7.5 -49.5zM1092 170q0 -26 -6.5 -48t-18.5 -38.5
t-28.5 -26t-37.5 -9.5q-20 0 -36.5 9.5t-29.5 26t-20 39t-7 49.5q0 25 6.5 47.5t18.5 39t29 26t37 9.5q19 0 36 -9.5t30 -26t20 -39t7 -49.5zM715 170q0 -26 -6.5 -48t-18.5 -38.5t-28.5 -26t-37.5 -9.5q-20 0 -36.5 9.5t-29.5 26t-20 39t-7 49.5q0 25 6.5 47.5t18.5 39
t29 26t37 9.5q19 0 36 -9.5t30 -26t20 -39t7 -49.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="328" 
d="M210 476l-162 -213v-8l162 -214l59 34l-135 185l135 183z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="328" 
d="M117 41l162 214v8l-162 213l-58 -34l135 -184l-135 -184z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="690" 
d="M605 628v-236h43v308h-46l-109 -169l-110 169h-47v-308h44v236l109 -167h6zM169 658h99v42h-242v-42h99v-266h44v266z" />
    <glyph glyph-name="fraction" unicode="&#x2215;" horiz-adv-x="429" 
d="M399 758l-467 -856h76l467 856h-76z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="998" 
d="M559 515v42q0 110 90 110q20 0 37 -3.5t37 -9.5v67q-18 7 -38 10t-47 3q-75 0 -115 -44t-40 -129v-45h-71v-67h71v-449h76v449h163v66h-163zM191 515v42q0 110 90 110q20 0 37 -3.5t38 -9.5v67q-19 7 -38.5 10t-46.5 3q-75 0 -115.5 -44t-40.5 -129v-45h-71v-67h71v-449
h77v449h163v66h-164zM905 0v730h-77v-730h77z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="998" 
d="M910 630v85h-87v-85h87zM559 515v42q0 110 90 110q20 0 37 -3.5t37 -9.5v67q-18 7 -38 10t-47 3q-75 0 -115 -44t-40 -129v-45h-71v-67h71v-449h76v449h163v66h-163zM191 515v42q0 110 90 110q20 0 37 -3.5t38 -9.5v67q-19 7 -38.5 10t-46.5 3q-75 0 -115.5 -44
t-40.5 -129v-45h-71v-67h71v-449h77v449h163v66h-164zM905 0v517h-77v-517h77z" />
    <glyph glyph-name="apple" unicode="&#xf8ff;" horiz-adv-x="967" 
d="M868 0v750h-767v-750h767zM859 -104l-6 34h-1l-13 -26l-13 26h-1l-6 -34h6l3 20l10 -21h2l10 21l3 -20h6zM806 -87q0 7 -4.5 12t-11.5 5q-8 0 -13 -5t-5 -13q0 -7 4.5 -12t12.5 -5t12.5 5t4.5 13zM758 -96q-6 -4 -10 -4q-13 0 -13 13q0 5 3.5 8.5t8.5 3.5q6 0 9 -3l2 5
q-5 3 -11 3q-8 0 -13 -5t-5 -13q0 -17 18 -17q7 0 10 3zM702 -71h-7l-9 -14l-11 15l-6 -1l14 -19v-14h6v14zM656 -104v33h-6v-13h-16v13h-6v-33h6v15h16v-15h6zM612 -82q0 5 -4.5 9t-18.5 2v-33h6v10q17 0 17 12zM576 -104l-16 34h-2l-15 -34h6l3 8h14l4 -8h6zM534 -104
l-10 13q7 3 7 10q0 5 -4.5 8.5t-18.5 1.5v-33h6v11h5l8 -11h7zM491 -101v15h-15v-5h9v-7q-4 -2 -6 -2q-13 0 -13 12q0 13 12 13q7 0 11 -4l2 5q-6 4 -12 4q-8 0 -13.5 -5t-5.5 -13t5 -12.5t13 -4.5q7 0 13 4zM447 -87q0 7 -4.5 12t-11.5 5q-8 0 -13 -5t-5 -13q0 -7 4.5 -12
t12.5 -5t12.5 5t4.5 13zM400 -82q0 5 -4.5 9t-18.5 2v-33h6v10q17 0 17 12zM364 -71h-7l-9 -14l-11 15l-6 -1l14 -19v-14h6v14zM323 -71h-30v-5h12v-28h6v28h12v5zM267 -71h-6l-7 -22l-10 23h-1l-10 -23l-7 23l-6 -1l12 -34h1l10 23l11 -23h1zM211 -71h-6l-7 -22l-10 23h-1
l-10 -23l-7 23l-6 -1l12 -34h1l10 23l11 -23h1zM155 -71h-6l-7 -22l-10 23h-1l-10 -23l-7 23l-6 -1l12 -34h1l10 23l11 -23h1zM716 -101q0 4 -4 4t-4 -4t4 -4t4 4zM284 -101q0 4 -4 4t-4 -4t4 -4t4 4zM559 686h-2l-2.5 4.5t-7.5 4.5q-3 0 -3 -3v-23q0 -5 2 -5h3v-2h-17v2h2
q3 0 3 5v23q0 3 -3 3q-5 0 -7.5 -4.5l-2.5 -4.5h-2l0.5 4.5t0.5 7.5q3 -1 18 -1t17 1q1 -3 1 -8v-4zM504 662h-18v2h2q3 0 3 5v10h-17v-10q0 -4 2.5 -4.5l2.5 -0.5v-2h-17v2l2.5 0.5t2.5 4.5v21q0 5 -3 5h-2v2h17v-1v-1h-3q-2 0 -2 -5v-9h17v9q0 5 -3 5h-2v1v1h18v-2h-3
t-3 -5v-21q0 -5 3 -5h3v-2zM439 664v-2h-30l-2 11l2 1l2.5 -4.5t7.5 -4.5h4q4 0 4 3v11h-4q-5 0 -5.5 -3l-0.5 -3h-2v13h2l0.5 -2.5t5.5 -2.5h4v10q0 3 -4 3h-4q-5 0 -7 -4l-2 -4l-2 1l2 10h29v-2h-3q-2 0 -2 -5v-21q0 -5 2 -5h3zM827 618h-685v6h685v-6zM696 540
q0 -12 -8 -17t-15 -5q-14 0 -25.5 11t-11.5 27q0 12 8 17t15 5q14 0 25.5 -11.5t11.5 -26.5zM589 537q0 -8 -6 -13.5t-19 -5.5q-16 0 -16 13q0 10 9 10q7 0 7 -5t-4 -5q-2 0 -3 1t-2 1q-3 0 -3 -3q0 -9 12 -9t12 14q0 10 -6 15q-5 -5 -11 -5q-8 0 -8 6q0 8 16 6q2 2 2 6
q0 12 -10 12q-7 0 -7 -5q0 -2 1.5 -2.5t1.5 -3.5t-4 -3q-5 0 -5 6q0 11 13 11q11 0 16 -5t5 -10q0 -6 -5 -9q14 -5 14 -17zM295 537q0 -8 -6 -13.5t-19 -5.5q-16 0 -16 13q0 10 9 10q7 0 7 -5t-4 -5q-2 0 -3 1t-2 1q-3 0 -3 -3q0 -9 12 -9t12 14q0 10 -6 15q-5 -5 -11 -5
q-8 0 -8 6q0 8 16 6q2 2 2 6q0 12 -10 12q-7 0 -7 -5q0 -2 1.5 -2.5t1.5 -3.5t-4 -3q-5 0 -5 6q0 11 13 11q11 0 16 -5t5 -10q0 -6 -5 -9q14 -5 14 -17zM820 522l1 -3h-29l-1 3l4.5 0.5t2.5 7.5l-3 16h-24l3 -16q2 -7 6 -7.5l4 -0.5l1 -3h-29l-1 3l4.5 0.5t2.5 7.5l-7 35
q-2 7 -6 7.5l-4 0.5l-1 3h29l1 -3l-4.5 -0.5t-2.5 -7.5l3 -15h24l-3 15q-2 7 -5.5 7.5l-3.5 0.5l-1 3h28l1 -3l-4.5 -0.5t-2.5 -7.5l7 -35q2 -7 6 -7.5zM498 519h-29l-1 3l4.5 0.5t2.5 7.5l-3 17h-4q-7 0 -7.5 -5.5l-0.5 -5.5h-3l-5 23h3l2.5 -4.5t9.5 -4.5h4l-3 16
q-1 6 -6 6h-4q-11 0 -12 -6.5l-1 -6.5h-4l0.5 7t-0.5 10h45l1 -3l-4.5 -0.5t-2.5 -7.5l7 -35q2 -7 6 -7.5l4 -0.5zM202 522l1 -3h-29l-1 3l4.5 0.5t2.5 7.5l-3 15q-4 0 -5 -3l-12 -23h-19l-1 3l5 0.5t9 7.5l9 16q-8 1 -13.5 5t-5.5 10q0 15 20 15h27l1 -3l-4.5 -0.5
t-2.5 -7.5l7 -35q2 -7 6 -7.5zM400 521q0 -3 -4 -3q-9 0 -12 7q-8 0 -10 -6.5t-15 -6.5q-5 0 -10 3.5t-5 8.5q0 7 5 7t5 -5q0 -2 -1 -2.5t-1 -2.5q0 -4 5 -4t8.5 6.5t14.5 6.5q-6 6 -8.5 13.5t-3.5 14.5t-2.5 12t-6.5 5t-5 -6t-4 -6q-5 0 -5 5q0 3 4 6.5t9 3.5
q12 0 16.5 -6.5t6 -15t2 -16t4.5 -10.5q7 -1 10 -4t3 -5zM827 475h-685v6h685v-6zM827 378h-36v28h-11v-65h14v-33h-81v33h14v65h-11v-28h-36v62h147v-62zM648 406h-14l-37 -45v-20h15v-33h-81v33h14v19l-37 46h-12v34h62v-34h-10l15 -23l15 23h-11v34h81v-34zM458 308h-81
v33h15v19h-30q-22 0 -34.5 11.5t-12.5 28.5t12.5 28.5t34.5 11.5h96v-34h-14v-65h14v-33zM283 308h-141v61h41v-28h33v21h-24v24h24v20h-33v-28h-41v62h141v-34h-14v-65h14v-33zM827 271h-685v6h685v-6zM728 192q0 -12 -8 -17t-15 -5q-14 0 -25.5 11t-11.5 27q0 12 8 17
t15 5q14 0 25.5 -11.5t11.5 -26.5zM203 217l-2 -2l-4.5 4.5t-7.5 2.5q-9 0 -9 -13q0 -8 5 -25q2 -8 6 -8.5l4 -0.5l1 -4h-28l-1 4l4.5 1t1.5 14q-3 12 -8.5 22.5t-15.5 10.5q-8 0 -8 -6l-3 1q0 6 3.5 9t8.5 3q8 0 13 -6.5t8 -16.5q-1 23 14 23q8 0 11.5 -4t6.5 -9zM823 171
h-29l-1 3l4.5 0.5t2.5 7.5l-3 17h-4q-7 0 -7.5 -5.5l-0.5 -5.5h-3l-5 23h3l2.5 -4.5t9.5 -4.5h4l-3 16q-1 6 -6 6h-4q-11 0 -12 -6.5l-1 -6.5h-4l0.5 7t-0.5 10h45l1 -3l-4.5 -0.5t-2.5 -7.5l7 -35q2 -7 6 -7.5l4 -0.5zM621 225l-5 -0.5t-3 -8.5l5 -22q2 -8 0 -16t-19 -8
q-14 0 -20 9.5t-8 17.5l-4 19q-2 8 -6.5 8.5l-4.5 0.5l-1 3h24v-3l-4.5 -0.5t-2.5 -8.5l4 -19q2 -9 7 -15.5t13 -6.5q11 0 12 5t-1 15l-5 21q-2 8 -6 8.5l-4 0.5l-1 3h29zM522 174l1 -3h-21l-1 3l4 1t2 9l-6 30l-30 -44l-3 1l-8 45q-2 7 -5.5 8l-3.5 1l-1 3h21l1 -3l-4.5 -1
t-2.5 -8l5 -30l29 42h14l1 -3l-5 -0.5t-3 -8.5l7 -32q2 -8 5.5 -9zM414 171h-32q-13 0 -24 9.5t-11 25.5q0 11 8 16.5t17 5.5h30l1 -3q-11 -1 -8 -10l8 -31q2 -8 6 -8.5l4 -0.5zM306 174l1 -3h-29l-1 3l4.5 0.5t2.5 7.5l-3 15q-4 0 -5 -3l-12 -23h-19l-1 3l5 0.5t9 7.5l9 16
q-8 1 -13.5 5t-5.5 10q0 15 20 15h27l1 -3l-4.5 -0.5t-2.5 -7.5l7 -35q2 -7 6 -7.5zM827 127h-685v6h685v-6zM694 46h-14v2l2.5 1t2.5 4v20l-25 -28h-3v28q0 4 -3 5l-3 1v2h15v-2l-3 -1t-3 -5v-18l24 26h10v-2l-3 -2t-3 -4v-20q0 -3 3 -4l3 -1v-2zM633 48v-2h-30l-2 11l2 1
l2.5 -4.5t7.5 -4.5h4q4 0 4 3v11h-4q-5 0 -5.5 -3l-0.5 -3h-2v13h2l0.5 -2.5t5.5 -2.5h4v10q0 3 -4 3h-4q-5 0 -7 -4l-2 -4l-2 1l2 10h29v-2h-3q-2 0 -2 -5v-21q0 -5 2 -5h3zM583 81v-2l-2.5 -0.5t-4.5 -5.5l-12 -27h-1l-9 16l-8 -16q0 -1 -0.5 -0.5l-0.5 0.5h-1l-13 27
q-2 4 -4 5l-2 1v2h13v-2l-2 -1t0 -5l8 -18l7 13l-3 5q-3 5 -5 5.5l-2 0.5l-1 2h26v-2l-2 -1.5t-4 -4.5l-4 -8l5 -10l9 19q2 3 0 4l-2 1v2h15zM480 79l-2.5 -0.5t-4.5 -4.5l-8 -12v-8q0 -5 2.5 -5.5l2.5 -0.5v-2h-17v2l2.5 0.5t2.5 5.5v8l-8 12q-2 4 -5 4.5l-3 0.5v2h15v-2
l-2.5 -0.5t-0.5 -4.5l6 -9l6 9q2 4 0.5 4.5l-1.5 0.5v2h15v-2zM425 64q0 -8 -5 -13.5t-15 -5.5t-15 5.5t-5 13.5t5 13t15 5t15 -5t5 -13zM364 46h-17v2l2.5 0.5t2.5 4.5v9q-4 0 -7 -4l-7 -12h-11v2l2.5 0.5t5.5 4.5l7 10q-9 4 -9 9q0 9 13 9h18v-2l-2.5 -0.5t-2.5 -5.5v-20
q0 -4 2.5 -4.5l2.5 -0.5v-2zM309 46h-18v2l3 0.5t3 5.5v9q-2 0 -4 -2l-8 -8q-3 -4 -0.5 -4.5l2.5 -0.5v-2h-17v2l2.5 1.5t4.5 3.5l13 13l-10 9q-2 2 -5 3l-3 1v2h14v-2l-1.5 -1t0.5 -3l8 -8q2 -2 4 -2v9q0 4 -3 4.5l-3 0.5v2h18v-2l-3 -0.5t-3 -4.5v-20q0 -4 3 -5l3 -1v-2z
M606 -83q0 -6 -11 -6v13q11 1 11 -7zM525 -82q0 -7 -11 -6v12q11 2 11 -6zM394 -83q0 -6 -11 -6v13q11 1 11 -7zM564 -92h-10l5 12zM800 -88q0 -12 -11 -12t-11 13q0 12 11 12t11 -13zM441 -88q0 -12 -11 -12t-11 13q0 12 11 12t11 -13zM176 548l-3 19q-1 5 -9 5t-8 -9
q0 -5 3.5 -10t11.5 -5h5zM566 553q-2 1 -7 1q-4 0 -4 -3q0 -2 4 -2t7 4zM272 553q-2 1 -7 1q-4 0 -4 -3q0 -2 4 -2t7 4zM684 535q0 16 -8 27.5t-17 11.5q-11 0 -11 -14q0 -15 8 -26.5t17 -11.5q11 0 11 13zM392 384v22h-11q-13 0 -13 -11t13 -11h11zM280 200l-3 19
q-1 5 -9 5t-8 -9q0 -5 3.5 -10t11.5 -5h5zM716 187q0 16 -8 27.5t-17 11.5q-11 0 -11 -14q0 -15 8 -26.5t17 -11.5q11 0 11 13zM393 182l-9 35q-2 5 -2.5 5.5t-3.5 1.5h-6q-14 0 -14 -17q0 -12 7.5 -22t18.5 -10h5q4 0 4 7zM553 79q-5 0 0 -9q4 9 0 9zM352 64v10q0 4 -4 4
q-7 0 -7 -6q0 -8 8 -8h3zM417 64q0 16 -12 16t-12 -16q0 -7 3 -12t9 -5q7 0 9.5 5t2.5 12z" />
    <glyph glyph-name="fi" unicode="&#xfb01;" horiz-adv-x="631" 
d="M543 630v85h-88v-85h88zM191 515v42q0 110 90 110q20 0 37 -3.5t38 -9.5v67q-19 7 -38.5 10t-46.5 3q-75 0 -115.5 -44t-40.5 -129v-45h-71v-67h71v-449h77v449h163v66h-164zM537 0v517h-76v-517h76z" />
    <glyph glyph-name="fl" unicode="&#xfb02;" horiz-adv-x="631" 
d="M191 515v42q0 110 90 110q20 0 37 -3.5t38 -9.5v67q-19 7 -38.5 10t-46.5 3q-75 0 -115.5 -44t-40.5 -129v-45h-71v-67h71v-449h77v449h163v66h-164zM537 0v730h-76v-730h76z" />
    <glyph glyph-name="NUL" horiz-adv-x="0" 
 />
    <hkern u1="&#x24;" u2="&#x37;" k="10" />
    <hkern u1="&#x26;" u2="Y" k="67" />
    <hkern u1="&#x26;" u2="W" k="49" />
    <hkern u1="&#x26;" u2="V" k="59" />
    <hkern u1="&#x26;" u2="T" k="79" />
    <hkern u1="&#x26;" u2="S" k="7" />
    <hkern u1="&#x28;" u2="&#x153;" k="30" />
    <hkern u1="&#x28;" u2="&#x152;" k="30" />
    <hkern u1="&#x28;" u2="&#xb5;" k="30" />
    <hkern u1="&#x28;" u2="s" k="15" />
    <hkern u1="&#x28;" u2="q" k="30" />
    <hkern u1="&#x28;" u2="o" k="30" />
    <hkern u1="&#x28;" u2="j" k="-30" />
    <hkern u1="&#x28;" u2="g" k="20" />
    <hkern u1="&#x28;" u2="e" k="30" />
    <hkern u1="&#x28;" u2="d" k="30" />
    <hkern u1="&#x28;" u2="c" k="30" />
    <hkern u1="&#x28;" u2="Q" k="30" />
    <hkern u1="&#x28;" u2="O" k="30" />
    <hkern u1="&#x28;" u2="J" k="15" />
    <hkern u1="&#x28;" u2="G" k="30" />
    <hkern u1="&#x28;" u2="C" k="30" />
    <hkern u1="&#x2a;" u2="&#x153;" k="20" />
    <hkern u1="&#x2a;" u2="&#xe6;" k="10" />
    <hkern u1="&#x2a;" u2="&#xc6;" k="100" />
    <hkern u1="&#x2a;" u2="t" k="-10" />
    <hkern u1="&#x2a;" u2="s" k="10" />
    <hkern u1="&#x2a;" u2="q" k="15" />
    <hkern u1="&#x2a;" u2="o" k="20" />
    <hkern u1="&#x2a;" u2="g" k="15" />
    <hkern u1="&#x2a;" u2="e" k="20" />
    <hkern u1="&#x2a;" u2="d" k="15" />
    <hkern u1="&#x2a;" u2="c" k="20" />
    <hkern u1="&#x2a;" u2="a" k="10" />
    <hkern u1="&#x2a;" u2="J" k="80" />
    <hkern u1="&#x2a;" u2="A" k="100" />
    <hkern u1="&#x2c;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x2c;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x2c;" u2="&#x2248;" k="15" />
    <hkern u1="&#x2c;" u2="&#x221a;" k="15" />
    <hkern u1="&#x2c;" u2="&#x201d;" k="40" />
    <hkern u1="&#x2c;" u2="&#x2019;" k="40" />
    <hkern u1="&#x2c;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x2c;" u2="&#x153;" k="20" />
    <hkern u1="&#x2c;" u2="&#x152;" k="40" />
    <hkern u1="&#x2c;" u2="&#xb5;" k="40" />
    <hkern u1="&#x2c;" u2="y" k="45" />
    <hkern u1="&#x2c;" u2="w" k="70" />
    <hkern u1="&#x2c;" u2="v" k="85" />
    <hkern u1="&#x2c;" u2="t" k="25" />
    <hkern u1="&#x2c;" u2="q" k="10" />
    <hkern u1="&#x2c;" u2="o" k="20" />
    <hkern u1="&#x2c;" u2="j" k="-15" />
    <hkern u1="&#x2c;" u2="g" k="10" />
    <hkern u1="&#x2c;" u2="f" k="15" />
    <hkern u1="&#x2c;" u2="e" k="20" />
    <hkern u1="&#x2c;" u2="d" k="10" />
    <hkern u1="&#x2c;" u2="c" k="20" />
    <hkern u1="&#x2c;" u2="Y" k="130" />
    <hkern u1="&#x2c;" u2="W" k="100" />
    <hkern u1="&#x2c;" u2="V" k="120" />
    <hkern u1="&#x2c;" u2="U" k="15" />
    <hkern u1="&#x2c;" u2="T" k="100" />
    <hkern u1="&#x2c;" u2="Q" k="40" />
    <hkern u1="&#x2c;" u2="O" k="40" />
    <hkern u1="&#x2c;" u2="G" k="40" />
    <hkern u1="&#x2c;" u2="C" k="40" />
    <hkern u1="&#x2c;" u2="&#x37;" k="20" />
    <hkern u1="&#x2c;" u2="&#x31;" k="50" />
    <hkern u1="&#x2c;" u2="&#x30;" k="20" />
    <hkern u1="&#x2d;" u2="&#xc6;" k="40" />
    <hkern u1="&#x2d;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2d;" u2="z" k="10" />
    <hkern u1="&#x2d;" u2="y" k="15" />
    <hkern u1="&#x2d;" u2="x" k="30" />
    <hkern u1="&#x2d;" u2="w" k="10" />
    <hkern u1="&#x2d;" u2="v" k="15" />
    <hkern u1="&#x2d;" u2="Z" k="30" />
    <hkern u1="&#x2d;" u2="Y" k="80" />
    <hkern u1="&#x2d;" u2="X" k="50" />
    <hkern u1="&#x2d;" u2="W" k="35" />
    <hkern u1="&#x2d;" u2="V" k="40" />
    <hkern u1="&#x2d;" u2="T" k="90" />
    <hkern u1="&#x2d;" u2="A" k="40" />
    <hkern u1="&#x2d;" u2="&#x37;" k="40" />
    <hkern u1="&#x2d;" u2="&#x33;" k="10" />
    <hkern u1="&#x2d;" u2="&#x31;" k="30" />
    <hkern u1="&#x2e;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x2e;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x2e;" u2="&#x2248;" k="15" />
    <hkern u1="&#x2e;" u2="&#x221a;" k="15" />
    <hkern u1="&#x2e;" u2="&#x201d;" k="40" />
    <hkern u1="&#x2e;" u2="&#x2019;" k="40" />
    <hkern u1="&#x2e;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x2e;" u2="&#x153;" k="20" />
    <hkern u1="&#x2e;" u2="&#x152;" k="40" />
    <hkern u1="&#x2e;" u2="&#xb5;" k="40" />
    <hkern u1="&#x2e;" u2="y" k="60" />
    <hkern u1="&#x2e;" u2="w" k="70" />
    <hkern u1="&#x2e;" u2="v" k="85" />
    <hkern u1="&#x2e;" u2="t" k="25" />
    <hkern u1="&#x2e;" u2="q" k="10" />
    <hkern u1="&#x2e;" u2="o" k="20" />
    <hkern u1="&#x2e;" u2="g" k="10" />
    <hkern u1="&#x2e;" u2="f" k="15" />
    <hkern u1="&#x2e;" u2="e" k="20" />
    <hkern u1="&#x2e;" u2="d" k="10" />
    <hkern u1="&#x2e;" u2="c" k="20" />
    <hkern u1="&#x2e;" u2="Y" k="130" />
    <hkern u1="&#x2e;" u2="W" k="100" />
    <hkern u1="&#x2e;" u2="V" k="120" />
    <hkern u1="&#x2e;" u2="U" k="15" />
    <hkern u1="&#x2e;" u2="T" k="100" />
    <hkern u1="&#x2e;" u2="Q" k="40" />
    <hkern u1="&#x2e;" u2="O" k="40" />
    <hkern u1="&#x2e;" u2="G" k="40" />
    <hkern u1="&#x2e;" u2="C" k="40" />
    <hkern u1="&#x2e;" u2="&#x37;" k="20" />
    <hkern u1="&#x2e;" u2="&#x31;" k="50" />
    <hkern u1="&#x2e;" u2="&#x30;" k="20" />
    <hkern u1="&#x2f;" u2="&#xfb02;" k="25" />
    <hkern u1="&#x2f;" u2="&#xfb01;" k="25" />
    <hkern u1="&#x2f;" u2="&#x2248;" k="25" />
    <hkern u1="&#x2f;" u2="&#x221a;" k="25" />
    <hkern u1="&#x2f;" u2="&#x3a9;" k="25" />
    <hkern u1="&#x2f;" u2="&#x153;" k="80" />
    <hkern u1="&#x2f;" u2="&#x152;" k="40" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="65" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="120" />
    <hkern u1="&#x2f;" u2="&#xb5;" k="40" />
    <hkern u1="&#x2f;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2f;" u2="z" k="60" />
    <hkern u1="&#x2f;" u2="y" k="50" />
    <hkern u1="&#x2f;" u2="x" k="50" />
    <hkern u1="&#x2f;" u2="w" k="50" />
    <hkern u1="&#x2f;" u2="v" k="50" />
    <hkern u1="&#x2f;" u2="u" k="50" />
    <hkern u1="&#x2f;" u2="t" k="20" />
    <hkern u1="&#x2f;" u2="s" k="85" />
    <hkern u1="&#x2f;" u2="r" k="50" />
    <hkern u1="&#x2f;" u2="q" k="70" />
    <hkern u1="&#x2f;" u2="p" k="50" />
    <hkern u1="&#x2f;" u2="o" k="80" />
    <hkern u1="&#x2f;" u2="n" k="50" />
    <hkern u1="&#x2f;" u2="m" k="50" />
    <hkern u1="&#x2f;" u2="g" k="70" />
    <hkern u1="&#x2f;" u2="f" k="25" />
    <hkern u1="&#x2f;" u2="e" k="80" />
    <hkern u1="&#x2f;" u2="d" k="70" />
    <hkern u1="&#x2f;" u2="c" k="80" />
    <hkern u1="&#x2f;" u2="a" k="65" />
    <hkern u1="&#x2f;" u2="Z" k="20" />
    <hkern u1="&#x2f;" u2="S" k="30" />
    <hkern u1="&#x2f;" u2="Q" k="40" />
    <hkern u1="&#x2f;" u2="O" k="40" />
    <hkern u1="&#x2f;" u2="J" k="130" />
    <hkern u1="&#x2f;" u2="G" k="40" />
    <hkern u1="&#x2f;" u2="C" k="40" />
    <hkern u1="&#x2f;" u2="A" k="120" />
    <hkern u1="&#x2f;" u2="&#x39;" k="20" />
    <hkern u1="&#x2f;" u2="&#x38;" k="15" />
    <hkern u1="&#x2f;" u2="&#x37;" k="10" />
    <hkern u1="&#x2f;" u2="&#x36;" k="35" />
    <hkern u1="&#x2f;" u2="&#x35;" k="20" />
    <hkern u1="&#x2f;" u2="&#x34;" k="95" />
    <hkern u1="&#x2f;" u2="&#x33;" k="10" />
    <hkern u1="&#x2f;" u2="&#x32;" k="20" />
    <hkern u1="&#x2f;" u2="&#x31;" k="-10" />
    <hkern u1="&#x2f;" u2="&#x30;" k="35" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="167" />
    <hkern u1="&#x30;" u2="&#xb1;" k="15" />
    <hkern u1="&#x30;" u2="&#x37;" k="29" />
    <hkern u1="&#x30;" u2="&#x33;" k="10" />
    <hkern u1="&#x30;" u2="&#x32;" k="10" />
    <hkern u1="&#x30;" u2="&#x31;" k="5" />
    <hkern u1="&#x30;" u2="&#x2f;" k="35" />
    <hkern u1="&#x30;" u2="&#x2e;" k="20" />
    <hkern u1="&#x30;" u2="&#x2c;" k="20" />
    <hkern u1="&#x31;" u2="&#x2215;" k="-55" />
    <hkern u1="&#x32;" u2="&#x2215;" k="-60" />
    <hkern u1="&#x32;" u2="&#x37;" k="12" />
    <hkern u1="&#x32;" u2="&#x34;" k="26" />
    <hkern u1="&#x33;" u2="&#x2215;" k="-30" />
    <hkern u1="&#x33;" u2="&#x39;" k="5" />
    <hkern u1="&#x33;" u2="&#x37;" k="24" />
    <hkern u1="&#x33;" u2="&#x35;" k="5" />
    <hkern u1="&#x33;" u2="&#x2f;" k="10" />
    <hkern u1="&#x34;" u2="&#x2215;" k="-30" />
    <hkern u1="&#x34;" u2="&#x39;" k="10" />
    <hkern u1="&#x34;" u2="&#x37;" k="37" />
    <hkern u1="&#x34;" u2="&#x31;" k="20" />
    <hkern u1="&#x34;" u2="&#x2f;" k="20" />
    <hkern u1="&#x35;" u2="&#x2215;" k="-20" />
    <hkern u1="&#x35;" u2="&#xb1;" k="5" />
    <hkern u1="&#x35;" u2="&#x39;" k="5" />
    <hkern u1="&#x35;" u2="&#x37;" k="30" />
    <hkern u1="&#x35;" u2="&#x33;" k="5" />
    <hkern u1="&#x35;" u2="&#x32;" k="10" />
    <hkern u1="&#x35;" u2="&#x2f;" k="20" />
    <hkern u1="&#x36;" u2="&#x2215;" k="-30" />
    <hkern u1="&#x36;" u2="&#xb1;" k="5" />
    <hkern u1="&#x36;" u2="&#x39;" k="10" />
    <hkern u1="&#x36;" u2="&#x37;" k="22" />
    <hkern u1="&#x36;" u2="&#x33;" k="10" />
    <hkern u1="&#x36;" u2="&#x31;" k="17" />
    <hkern u1="&#x36;" u2="&#x2f;" k="10" />
    <hkern u1="&#x37;" u2="&#x2215;" k="110" />
    <hkern u1="&#x37;" u2="&#x2014;" k="30" />
    <hkern u1="&#x37;" u2="&#x2013;" k="30" />
    <hkern u1="&#x37;" u2="&#xb1;" k="10" />
    <hkern u1="&#x37;" u2="&#x39;" k="15" />
    <hkern u1="&#x37;" u2="&#x38;" k="10" />
    <hkern u1="&#x37;" u2="&#x36;" k="20" />
    <hkern u1="&#x37;" u2="&#x35;" k="25" />
    <hkern u1="&#x37;" u2="&#x34;" k="85" />
    <hkern u1="&#x37;" u2="&#x33;" k="20" />
    <hkern u1="&#x37;" u2="&#x32;" k="15" />
    <hkern u1="&#x37;" u2="&#x31;" k="-10" />
    <hkern u1="&#x37;" u2="&#x30;" k="20" />
    <hkern u1="&#x37;" u2="&#x2f;" k="140" />
    <hkern u1="&#x37;" u2="&#x2e;" k="100" />
    <hkern u1="&#x37;" u2="&#x2d;" k="30" />
    <hkern u1="&#x37;" u2="&#x2c;" k="100" />
    <hkern u1="&#x38;" u2="&#x2215;" k="-40" />
    <hkern u1="&#x38;" u2="&#xb1;" k="5" />
    <hkern u1="&#x38;" u2="&#x39;" k="5" />
    <hkern u1="&#x38;" u2="&#x37;" k="10" />
    <hkern u1="&#x39;" u2="&#x2215;" k="-15" />
    <hkern u1="&#x39;" u2="&#xb1;" k="10" />
    <hkern u1="&#x39;" u2="&#x37;" k="21" />
    <hkern u1="&#x39;" u2="&#x35;" k="5" />
    <hkern u1="&#x39;" u2="&#x33;" k="10" />
    <hkern u1="&#x39;" u2="&#x32;" k="10" />
    <hkern u1="&#x39;" u2="&#x2f;" k="25" />
    <hkern u1="&#x39;" u2="&#x2e;" k="10" />
    <hkern u1="&#x39;" u2="&#x2c;" k="10" />
    <hkern u1="&#x3a;" u2="Y" k="40" />
    <hkern u1="&#x3a;" u2="W" k="15" />
    <hkern u1="&#x3a;" u2="V" k="20" />
    <hkern u1="&#x3a;" u2="T" k="39" />
    <hkern u1="&#x3b;" u2="Y" k="40" />
    <hkern u1="&#x3b;" u2="W" k="15" />
    <hkern u1="&#x3b;" u2="V" k="20" />
    <hkern u1="&#x3b;" u2="T" k="39" />
    <hkern u1="A" u2="&#xfb02;" k="20" />
    <hkern u1="A" u2="&#xfb01;" k="20" />
    <hkern u1="A" u2="&#x2248;" k="20" />
    <hkern u1="A" u2="&#x221a;" k="20" />
    <hkern u1="A" u2="&#x2122;" k="100" />
    <hkern u1="A" u2="&#x201d;" k="80" />
    <hkern u1="A" u2="&#x201c;" k="80" />
    <hkern u1="A" u2="&#x2019;" k="80" />
    <hkern u1="A" u2="&#x2018;" k="80" />
    <hkern u1="A" u2="&#x2014;" k="40" />
    <hkern u1="A" u2="&#x2013;" k="40" />
    <hkern u1="A" u2="&#x3a9;" k="20" />
    <hkern u1="A" u2="&#x153;" k="25" />
    <hkern u1="A" u2="&#x152;" k="41" />
    <hkern u1="A" u2="&#xc6;" k="5" />
    <hkern u1="A" u2="&#xb5;" k="41" />
    <hkern u1="A" u2="&#xab;" k="20" />
    <hkern u1="A" u2="y" k="43" />
    <hkern u1="A" u2="w" k="48" />
    <hkern u1="A" u2="v" k="63" />
    <hkern u1="A" u2="u" k="10" />
    <hkern u1="A" u2="t" k="30" />
    <hkern u1="A" u2="q" k="25" />
    <hkern u1="A" u2="o" k="25" />
    <hkern u1="A" u2="g" k="25" />
    <hkern u1="A" u2="f" k="20" />
    <hkern u1="A" u2="e" k="25" />
    <hkern u1="A" u2="d" k="25" />
    <hkern u1="A" u2="c" k="25" />
    <hkern u1="A" u2="\" k="120" />
    <hkern u1="A" u2="Y" k="110" />
    <hkern u1="A" u2="X" k="5" />
    <hkern u1="A" u2="W" k="90" />
    <hkern u1="A" u2="V" k="100" />
    <hkern u1="A" u2="U" k="25" />
    <hkern u1="A" u2="T" k="90" />
    <hkern u1="A" u2="S" k="14" />
    <hkern u1="A" u2="Q" k="41" />
    <hkern u1="A" u2="O" k="41" />
    <hkern u1="A" u2="G" k="41" />
    <hkern u1="A" u2="C" k="41" />
    <hkern u1="A" u2="A" k="5" />
    <hkern u1="A" u2="&#x3f;" k="60" />
    <hkern u1="A" u2="&#x2d;" k="40" />
    <hkern u1="A" u2="&#x2a;" k="100" />
    <hkern u1="B" u2="y" k="10" />
    <hkern u1="B" u2="w" k="10" />
    <hkern u1="B" u2="v" k="10" />
    <hkern u1="B" u2="Y" k="30" />
    <hkern u1="B" u2="X" k="20" />
    <hkern u1="B" u2="W" k="15" />
    <hkern u1="B" u2="V" k="20" />
    <hkern u1="B" u2="T" k="17" />
    <hkern u1="B" u2="&#x3f;" k="5" />
    <hkern u1="B" u2="&#x26;" k="-4" />
    <hkern u1="C" u2="&#x2014;" k="10" />
    <hkern u1="C" u2="&#x2013;" k="10" />
    <hkern u1="C" u2="&#x153;" k="10" />
    <hkern u1="C" u2="&#x152;" k="20" />
    <hkern u1="C" u2="&#xb5;" k="20" />
    <hkern u1="C" u2="y" k="10" />
    <hkern u1="C" u2="x" k="10" />
    <hkern u1="C" u2="w" k="10" />
    <hkern u1="C" u2="v" k="10" />
    <hkern u1="C" u2="q" k="10" />
    <hkern u1="C" u2="o" k="10" />
    <hkern u1="C" u2="g" k="10" />
    <hkern u1="C" u2="e" k="10" />
    <hkern u1="C" u2="d" k="10" />
    <hkern u1="C" u2="c" k="10" />
    <hkern u1="C" u2="Y" k="10" />
    <hkern u1="C" u2="X" k="10" />
    <hkern u1="C" u2="W" k="1" />
    <hkern u1="C" u2="V" k="1" />
    <hkern u1="C" u2="Q" k="20" />
    <hkern u1="C" u2="O" k="20" />
    <hkern u1="C" u2="G" k="20" />
    <hkern u1="C" u2="C" k="20" />
    <hkern u1="C" u2="&#x2d;" k="10" />
    <hkern u1="D" u2="&#x2026;" k="40" />
    <hkern u1="D" u2="&#xc6;" k="46" />
    <hkern u1="D" u2="&#x7d;" k="20" />
    <hkern u1="D" u2="x" k="10" />
    <hkern u1="D" u2="]" k="20" />
    <hkern u1="D" u2="\" k="40" />
    <hkern u1="D" u2="Z" k="44" />
    <hkern u1="D" u2="Y" k="65" />
    <hkern u1="D" u2="X" k="55" />
    <hkern u1="D" u2="W" k="36" />
    <hkern u1="D" u2="V" k="45" />
    <hkern u1="D" u2="T" k="51" />
    <hkern u1="D" u2="S" k="10" />
    <hkern u1="D" u2="J" k="40" />
    <hkern u1="D" u2="A" k="46" />
    <hkern u1="D" u2="&#x3f;" k="20" />
    <hkern u1="D" u2="&#x2f;" k="40" />
    <hkern u1="D" u2="&#x2e;" k="40" />
    <hkern u1="D" u2="&#x2c;" k="40" />
    <hkern u1="D" u2="&#x29;" k="30" />
    <hkern u1="E" u2="&#x153;" k="10" />
    <hkern u1="E" u2="y" k="10" />
    <hkern u1="E" u2="w" k="10" />
    <hkern u1="E" u2="v" k="10" />
    <hkern u1="E" u2="o" k="10" />
    <hkern u1="E" u2="e" k="10" />
    <hkern u1="E" u2="d" k="10" />
    <hkern u1="E" u2="c" k="10" />
    <hkern u1="F" u2="&#x203a;" k="15" />
    <hkern u1="F" u2="&#x2026;" k="100" />
    <hkern u1="F" u2="&#x201d;" k="-20" />
    <hkern u1="F" u2="&#x2019;" k="-20" />
    <hkern u1="F" u2="&#x153;" k="15" />
    <hkern u1="F" u2="&#x152;" k="15" />
    <hkern u1="F" u2="&#xe6;" k="25" />
    <hkern u1="F" u2="&#xc6;" k="80" />
    <hkern u1="F" u2="&#xbb;" k="15" />
    <hkern u1="F" u2="&#xb5;" k="15" />
    <hkern u1="F" u2="z" k="15" />
    <hkern u1="F" u2="y" k="15" />
    <hkern u1="F" u2="w" k="10" />
    <hkern u1="F" u2="v" k="15" />
    <hkern u1="F" u2="s" k="10" />
    <hkern u1="F" u2="q" k="10" />
    <hkern u1="F" u2="o" k="15" />
    <hkern u1="F" u2="g" k="10" />
    <hkern u1="F" u2="e" k="15" />
    <hkern u1="F" u2="d" k="10" />
    <hkern u1="F" u2="c" k="15" />
    <hkern u1="F" u2="a" k="25" />
    <hkern u1="F" u2="Z" k="10" />
    <hkern u1="F" u2="Q" k="15" />
    <hkern u1="F" u2="O" k="15" />
    <hkern u1="F" u2="J" k="110" />
    <hkern u1="F" u2="G" k="15" />
    <hkern u1="F" u2="C" k="15" />
    <hkern u1="F" u2="A" k="80" />
    <hkern u1="F" u2="&#x3f;" k="-10" />
    <hkern u1="F" u2="&#x2f;" k="70" />
    <hkern u1="F" u2="&#x2e;" k="100" />
    <hkern u1="F" u2="&#x2c;" k="100" />
    <hkern u1="F" u2="&#x26;" k="26" />
    <hkern u1="G" u2="&#xe6;" k="-10" />
    <hkern u1="G" u2="y" k="5" />
    <hkern u1="G" u2="v" k="5" />
    <hkern u1="G" u2="a" k="-10" />
    <hkern u1="G" u2="\" k="15" />
    <hkern u1="G" u2="Y" k="30" />
    <hkern u1="G" u2="X" k="10" />
    <hkern u1="G" u2="W" k="16" />
    <hkern u1="G" u2="V" k="21" />
    <hkern u1="G" u2="T" k="17" />
    <hkern u1="G" u2="&#x3f;" k="10" />
    <hkern u1="J" u2="&#x2026;" k="15" />
    <hkern u1="J" u2="&#xc6;" k="25" />
    <hkern u1="J" u2="J" k="20" />
    <hkern u1="J" u2="A" k="25" />
    <hkern u1="J" u2="&#x2e;" k="15" />
    <hkern u1="J" u2="&#x2c;" k="15" />
    <hkern u1="K" u2="&#xfb02;" k="20" />
    <hkern u1="K" u2="&#xfb01;" k="20" />
    <hkern u1="K" u2="&#x2248;" k="20" />
    <hkern u1="K" u2="&#x221a;" k="20" />
    <hkern u1="K" u2="&#x2014;" k="50" />
    <hkern u1="K" u2="&#x2013;" k="50" />
    <hkern u1="K" u2="&#x3a9;" k="20" />
    <hkern u1="K" u2="&#x153;" k="30" />
    <hkern u1="K" u2="&#x152;" k="51" />
    <hkern u1="K" u2="&#xe6;" k="10" />
    <hkern u1="K" u2="&#xc6;" k="5" />
    <hkern u1="K" u2="&#xb5;" k="51" />
    <hkern u1="K" u2="&#xab;" k="20" />
    <hkern u1="K" u2="y" k="50" />
    <hkern u1="K" u2="w" k="50" />
    <hkern u1="K" u2="v" k="60" />
    <hkern u1="K" u2="u" k="20" />
    <hkern u1="K" u2="t" k="25" />
    <hkern u1="K" u2="q" k="25" />
    <hkern u1="K" u2="o" k="30" />
    <hkern u1="K" u2="g" k="25" />
    <hkern u1="K" u2="f" k="20" />
    <hkern u1="K" u2="e" k="30" />
    <hkern u1="K" u2="d" k="25" />
    <hkern u1="K" u2="c" k="30" />
    <hkern u1="K" u2="a" k="10" />
    <hkern u1="K" u2="Y" k="37" />
    <hkern u1="K" u2="W" k="30" />
    <hkern u1="K" u2="V" k="30" />
    <hkern u1="K" u2="U" k="15" />
    <hkern u1="K" u2="T" k="10" />
    <hkern u1="K" u2="S" k="10" />
    <hkern u1="K" u2="Q" k="51" />
    <hkern u1="K" u2="O" k="51" />
    <hkern u1="K" u2="G" k="51" />
    <hkern u1="K" u2="C" k="51" />
    <hkern u1="K" u2="A" k="5" />
    <hkern u1="K" u2="&#x2d;" k="50" />
    <hkern u1="K" u2="&#x26;" k="7" />
    <hkern u1="L" u2="&#xfb02;" k="20" />
    <hkern u1="L" u2="&#xfb01;" k="20" />
    <hkern u1="L" u2="&#x2248;" k="20" />
    <hkern u1="L" u2="&#x221a;" k="20" />
    <hkern u1="L" u2="&#x2122;" k="90" />
    <hkern u1="L" u2="&#x201d;" k="40" />
    <hkern u1="L" u2="&#x201c;" k="40" />
    <hkern u1="L" u2="&#x2019;" k="40" />
    <hkern u1="L" u2="&#x2018;" k="40" />
    <hkern u1="L" u2="&#x2014;" k="40" />
    <hkern u1="L" u2="&#x2013;" k="40" />
    <hkern u1="L" u2="&#x3a9;" k="20" />
    <hkern u1="L" u2="&#x153;" k="10" />
    <hkern u1="L" u2="&#x152;" k="40" />
    <hkern u1="L" u2="&#xb5;" k="40" />
    <hkern u1="L" u2="y" k="60" />
    <hkern u1="L" u2="w" k="50" />
    <hkern u1="L" u2="v" k="60" />
    <hkern u1="L" u2="t" k="20" />
    <hkern u1="L" u2="q" k="5" />
    <hkern u1="L" u2="o" k="10" />
    <hkern u1="L" u2="g" k="5" />
    <hkern u1="L" u2="f" k="20" />
    <hkern u1="L" u2="e" k="10" />
    <hkern u1="L" u2="d" k="5" />
    <hkern u1="L" u2="c" k="10" />
    <hkern u1="L" u2="\" k="120" />
    <hkern u1="L" u2="Y" k="130" />
    <hkern u1="L" u2="W" k="100" />
    <hkern u1="L" u2="V" k="115" />
    <hkern u1="L" u2="U" k="20" />
    <hkern u1="L" u2="T" k="100" />
    <hkern u1="L" u2="Q" k="40" />
    <hkern u1="L" u2="O" k="40" />
    <hkern u1="L" u2="G" k="40" />
    <hkern u1="L" u2="C" k="40" />
    <hkern u1="L" u2="&#x3f;" k="60" />
    <hkern u1="L" u2="&#x2d;" k="40" />
    <hkern u1="L" u2="&#x2a;" k="80" />
    <hkern u1="L" u2="&#x26;" k="7" />
    <hkern u1="O" u2="&#x2026;" k="40" />
    <hkern u1="O" u2="&#xc6;" k="41" />
    <hkern u1="O" u2="&#x7d;" k="20" />
    <hkern u1="O" u2="x" k="5" />
    <hkern u1="O" u2="]" k="20" />
    <hkern u1="O" u2="\" k="40" />
    <hkern u1="O" u2="Z" k="39" />
    <hkern u1="O" u2="Y" k="60" />
    <hkern u1="O" u2="X" k="50" />
    <hkern u1="O" u2="W" k="36" />
    <hkern u1="O" u2="V" k="41" />
    <hkern u1="O" u2="T" k="48" />
    <hkern u1="O" u2="S" k="5" />
    <hkern u1="O" u2="J" k="30" />
    <hkern u1="O" u2="A" k="41" />
    <hkern u1="O" u2="&#x3f;" k="20" />
    <hkern u1="O" u2="&#x2f;" k="40" />
    <hkern u1="O" u2="&#x2e;" k="40" />
    <hkern u1="O" u2="&#x2c;" k="40" />
    <hkern u1="O" u2="&#x29;" k="30" />
    <hkern u1="P" u2="&#xfb02;" k="-15" />
    <hkern u1="P" u2="&#xfb01;" k="-15" />
    <hkern u1="P" u2="&#x2248;" k="-15" />
    <hkern u1="P" u2="&#x221a;" k="-15" />
    <hkern u1="P" u2="&#x2026;" k="100" />
    <hkern u1="P" u2="&#x201d;" k="-20" />
    <hkern u1="P" u2="&#x2019;" k="-20" />
    <hkern u1="P" u2="&#x3a9;" k="-15" />
    <hkern u1="P" u2="&#x153;" k="5" />
    <hkern u1="P" u2="&#xe6;" k="10" />
    <hkern u1="P" u2="&#xc6;" k="70" />
    <hkern u1="P" u2="&#xab;" k="-10" />
    <hkern u1="P" u2="y" k="-10" />
    <hkern u1="P" u2="w" k="-10" />
    <hkern u1="P" u2="v" k="-10" />
    <hkern u1="P" u2="u" k="-5" />
    <hkern u1="P" u2="t" k="-15" />
    <hkern u1="P" u2="o" k="5" />
    <hkern u1="P" u2="f" k="-15" />
    <hkern u1="P" u2="e" k="5" />
    <hkern u1="P" u2="c" k="5" />
    <hkern u1="P" u2="a" k="10" />
    <hkern u1="P" u2="Z" k="15" />
    <hkern u1="P" u2="Y" k="10" />
    <hkern u1="P" u2="X" k="30" />
    <hkern u1="P" u2="W" k="5" />
    <hkern u1="P" u2="V" k="10" />
    <hkern u1="P" u2="J" k="100" />
    <hkern u1="P" u2="A" k="70" />
    <hkern u1="P" u2="&#x2f;" k="60" />
    <hkern u1="P" u2="&#x2e;" k="100" />
    <hkern u1="P" u2="&#x2c;" k="100" />
    <hkern u1="P" u2="&#x26;" k="15" />
    <hkern u1="Q" u2="Y" k="65" />
    <hkern u1="Q" u2="W" k="36" />
    <hkern u1="Q" u2="V" k="41" />
    <hkern u1="Q" u2="T" k="48" />
    <hkern u1="Q" u2="&#x3f;" k="20" />
    <hkern u1="Q" u2="&#x29;" k="10" />
    <hkern u1="R" u2="&#xfb02;" k="-10" />
    <hkern u1="R" u2="&#xfb01;" k="-10" />
    <hkern u1="R" u2="&#x2248;" k="-10" />
    <hkern u1="R" u2="&#x221a;" k="-10" />
    <hkern u1="R" u2="&#x3a9;" k="-10" />
    <hkern u1="R" u2="&#x153;" k="10" />
    <hkern u1="R" u2="t" k="-10" />
    <hkern u1="R" u2="q" k="5" />
    <hkern u1="R" u2="o" k="10" />
    <hkern u1="R" u2="g" k="5" />
    <hkern u1="R" u2="f" k="-10" />
    <hkern u1="R" u2="e" k="10" />
    <hkern u1="R" u2="d" k="5" />
    <hkern u1="R" u2="c" k="10" />
    <hkern u1="R" u2="Y" k="25" />
    <hkern u1="R" u2="W" k="15" />
    <hkern u1="R" u2="V" k="20" />
    <hkern u1="R" u2="T" k="7" />
    <hkern u1="R" u2="J" k="5" />
    <hkern u1="S" u2="&#xfb02;" k="5" />
    <hkern u1="S" u2="&#xfb01;" k="5" />
    <hkern u1="S" u2="&#x2248;" k="5" />
    <hkern u1="S" u2="&#x221a;" k="5" />
    <hkern u1="S" u2="&#x3a9;" k="5" />
    <hkern u1="S" u2="&#xc6;" k="15" />
    <hkern u1="S" u2="z" k="5" />
    <hkern u1="S" u2="y" k="15" />
    <hkern u1="S" u2="x" k="15" />
    <hkern u1="S" u2="w" k="10" />
    <hkern u1="S" u2="v" k="15" />
    <hkern u1="S" u2="t" k="5" />
    <hkern u1="S" u2="f" k="5" />
    <hkern u1="S" u2="\" k="20" />
    <hkern u1="S" u2="Z" k="10" />
    <hkern u1="S" u2="Y" k="30" />
    <hkern u1="S" u2="X" k="25" />
    <hkern u1="S" u2="W" k="25" />
    <hkern u1="S" u2="V" k="30" />
    <hkern u1="S" u2="T" k="15" />
    <hkern u1="S" u2="S" k="10" />
    <hkern u1="S" u2="A" k="15" />
    <hkern u1="S" u2="&#x3f;" k="10" />
    <hkern u1="T" u2="&#xfb02;" k="45" />
    <hkern u1="T" u2="&#xfb01;" k="45" />
    <hkern u1="T" u2="&#x2248;" k="45" />
    <hkern u1="T" u2="&#x221a;" k="45" />
    <hkern u1="T" u2="&#x203a;" k="70" />
    <hkern u1="T" u2="&#x2026;" k="100" />
    <hkern u1="T" u2="&#x2014;" k="90" />
    <hkern u1="T" u2="&#x2013;" k="90" />
    <hkern u1="T" u2="&#x3a9;" k="45" />
    <hkern u1="T" u2="&#x153;" k="127" />
    <hkern u1="T" u2="&#x152;" k="48" />
    <hkern u1="T" u2="&#xe6;" k="127" />
    <hkern u1="T" u2="&#xc6;" k="90" />
    <hkern u1="T" u2="&#xbb;" k="70" />
    <hkern u1="T" u2="&#xb5;" k="48" />
    <hkern u1="T" u2="&#xab;" k="90" />
    <hkern u1="T" u2="z" k="102" />
    <hkern u1="T" u2="y" k="87" />
    <hkern u1="T" u2="x" k="87" />
    <hkern u1="T" u2="w" k="84" />
    <hkern u1="T" u2="v" k="87" />
    <hkern u1="T" u2="u" k="87" />
    <hkern u1="T" u2="t" k="42" />
    <hkern u1="T" u2="s" k="108" />
    <hkern u1="T" u2="r" k="89" />
    <hkern u1="T" u2="q" k="117" />
    <hkern u1="T" u2="p" k="89" />
    <hkern u1="T" u2="o" k="127" />
    <hkern u1="T" u2="n" k="89" />
    <hkern u1="T" u2="m" k="89" />
    <hkern u1="T" u2="l" k="12" />
    <hkern u1="T" u2="j" k="32" />
    <hkern u1="T" u2="i" k="32" />
    <hkern u1="T" u2="h" k="15" />
    <hkern u1="T" u2="g" k="117" />
    <hkern u1="T" u2="f" k="45" />
    <hkern u1="T" u2="e" k="127" />
    <hkern u1="T" u2="d" k="117" />
    <hkern u1="T" u2="c" k="127" />
    <hkern u1="T" u2="a" k="127" />
    <hkern u1="T" u2="Z" k="17" />
    <hkern u1="T" u2="S" k="15" />
    <hkern u1="T" u2="Q" k="48" />
    <hkern u1="T" u2="O" k="48" />
    <hkern u1="T" u2="J" k="110" />
    <hkern u1="T" u2="G" k="48" />
    <hkern u1="T" u2="C" k="48" />
    <hkern u1="T" u2="A" k="90" />
    <hkern u1="T" u2="&#x3b;" k="39" />
    <hkern u1="T" u2="&#x3a;" k="39" />
    <hkern u1="T" u2="&#x2f;" k="90" />
    <hkern u1="T" u2="&#x2e;" k="100" />
    <hkern u1="T" u2="&#x2d;" k="90" />
    <hkern u1="T" u2="&#x2c;" k="100" />
    <hkern u1="T" u2="&#x26;" k="63" />
    <hkern u1="U" u2="&#x2026;" k="15" />
    <hkern u1="U" u2="&#xc6;" k="25" />
    <hkern u1="U" u2="x" k="5" />
    <hkern u1="U" u2="X" k="10" />
    <hkern u1="U" u2="J" k="20" />
    <hkern u1="U" u2="A" k="25" />
    <hkern u1="U" u2="&#x2f;" k="15" />
    <hkern u1="U" u2="&#x2e;" k="15" />
    <hkern u1="U" u2="&#x2c;" k="15" />
    <hkern u1="V" u2="&#xfb02;" k="25" />
    <hkern u1="V" u2="&#xfb01;" k="25" />
    <hkern u1="V" u2="&#x2248;" k="25" />
    <hkern u1="V" u2="&#x221a;" k="25" />
    <hkern u1="V" u2="&#x203a;" k="40" />
    <hkern u1="V" u2="&#x2026;" k="120" />
    <hkern u1="V" u2="&#x2014;" k="40" />
    <hkern u1="V" u2="&#x2013;" k="40" />
    <hkern u1="V" u2="&#x3a9;" k="25" />
    <hkern u1="V" u2="&#x153;" k="70" />
    <hkern u1="V" u2="&#x152;" k="41" />
    <hkern u1="V" u2="&#xe6;" k="70" />
    <hkern u1="V" u2="&#xc6;" k="100" />
    <hkern u1="V" u2="&#xbb;" k="40" />
    <hkern u1="V" u2="&#xb5;" k="41" />
    <hkern u1="V" u2="&#xab;" k="60" />
    <hkern u1="V" u2="z" k="55" />
    <hkern u1="V" u2="y" k="40" />
    <hkern u1="V" u2="x" k="50" />
    <hkern u1="V" u2="w" k="35" />
    <hkern u1="V" u2="v" k="40" />
    <hkern u1="V" u2="u" k="40" />
    <hkern u1="V" u2="t" k="20" />
    <hkern u1="V" u2="s" k="60" />
    <hkern u1="V" u2="r" k="40" />
    <hkern u1="V" u2="q" k="65" />
    <hkern u1="V" u2="p" k="40" />
    <hkern u1="V" u2="o" k="70" />
    <hkern u1="V" u2="n" k="40" />
    <hkern u1="V" u2="m" k="40" />
    <hkern u1="V" u2="l" k="10" />
    <hkern u1="V" u2="j" k="20" />
    <hkern u1="V" u2="i" k="20" />
    <hkern u1="V" u2="g" k="65" />
    <hkern u1="V" u2="f" k="25" />
    <hkern u1="V" u2="e" k="70" />
    <hkern u1="V" u2="d" k="65" />
    <hkern u1="V" u2="c" k="70" />
    <hkern u1="V" u2="a" k="70" />
    <hkern u1="V" u2="Z" k="10" />
    <hkern u1="V" u2="Y" k="20" />
    <hkern u1="V" u2="X" k="20" />
    <hkern u1="V" u2="W" k="10" />
    <hkern u1="V" u2="V" k="10" />
    <hkern u1="V" u2="S" k="25" />
    <hkern u1="V" u2="Q" k="41" />
    <hkern u1="V" u2="O" k="41" />
    <hkern u1="V" u2="J" k="120" />
    <hkern u1="V" u2="G" k="41" />
    <hkern u1="V" u2="C" k="41" />
    <hkern u1="V" u2="A" k="100" />
    <hkern u1="V" u2="&#x3b;" k="20" />
    <hkern u1="V" u2="&#x3a;" k="20" />
    <hkern u1="V" u2="&#x2f;" k="120" />
    <hkern u1="V" u2="&#x2e;" k="120" />
    <hkern u1="V" u2="&#x2d;" k="40" />
    <hkern u1="V" u2="&#x2c;" k="120" />
    <hkern u1="V" u2="&#x26;" k="51" />
    <hkern u1="W" u2="&#xfb02;" k="30" />
    <hkern u1="W" u2="&#xfb01;" k="30" />
    <hkern u1="W" u2="&#x2248;" k="30" />
    <hkern u1="W" u2="&#x221a;" k="30" />
    <hkern u1="W" u2="&#x203a;" k="35" />
    <hkern u1="W" u2="&#x2026;" k="100" />
    <hkern u1="W" u2="&#x2014;" k="35" />
    <hkern u1="W" u2="&#x2013;" k="35" />
    <hkern u1="W" u2="&#x3a9;" k="30" />
    <hkern u1="W" u2="&#x153;" k="65" />
    <hkern u1="W" u2="&#x152;" k="36" />
    <hkern u1="W" u2="&#xe6;" k="70" />
    <hkern u1="W" u2="&#xc6;" k="90" />
    <hkern u1="W" u2="&#xbb;" k="35" />
    <hkern u1="W" u2="&#xb5;" k="36" />
    <hkern u1="W" u2="&#xab;" k="50" />
    <hkern u1="W" u2="z" k="55" />
    <hkern u1="W" u2="y" k="35" />
    <hkern u1="W" u2="x" k="40" />
    <hkern u1="W" u2="w" k="35" />
    <hkern u1="W" u2="v" k="35" />
    <hkern u1="W" u2="u" k="35" />
    <hkern u1="W" u2="t" k="25" />
    <hkern u1="W" u2="s" k="60" />
    <hkern u1="W" u2="r" k="35" />
    <hkern u1="W" u2="q" k="60" />
    <hkern u1="W" u2="p" k="35" />
    <hkern u1="W" u2="o" k="65" />
    <hkern u1="W" u2="n" k="35" />
    <hkern u1="W" u2="m" k="35" />
    <hkern u1="W" u2="l" k="10" />
    <hkern u1="W" u2="j" k="15" />
    <hkern u1="W" u2="i" k="15" />
    <hkern u1="W" u2="g" k="60" />
    <hkern u1="W" u2="f" k="30" />
    <hkern u1="W" u2="e" k="65" />
    <hkern u1="W" u2="d" k="60" />
    <hkern u1="W" u2="c" k="65" />
    <hkern u1="W" u2="a" k="70" />
    <hkern u1="W" u2="Z" k="10" />
    <hkern u1="W" u2="Y" k="20" />
    <hkern u1="W" u2="X" k="15" />
    <hkern u1="W" u2="W" k="10" />
    <hkern u1="W" u2="V" k="10" />
    <hkern u1="W" u2="S" k="20" />
    <hkern u1="W" u2="Q" k="36" />
    <hkern u1="W" u2="O" k="36" />
    <hkern u1="W" u2="J" k="105" />
    <hkern u1="W" u2="G" k="36" />
    <hkern u1="W" u2="C" k="36" />
    <hkern u1="W" u2="A" k="90" />
    <hkern u1="W" u2="&#x3b;" k="15" />
    <hkern u1="W" u2="&#x3a;" k="15" />
    <hkern u1="W" u2="&#x2f;" k="100" />
    <hkern u1="W" u2="&#x2e;" k="100" />
    <hkern u1="W" u2="&#x2d;" k="35" />
    <hkern u1="W" u2="&#x2c;" k="100" />
    <hkern u1="W" u2="&#x26;" k="41" />
    <hkern u1="X" u2="&#xfb02;" k="20" />
    <hkern u1="X" u2="&#xfb01;" k="20" />
    <hkern u1="X" u2="&#x2248;" k="20" />
    <hkern u1="X" u2="&#x221a;" k="20" />
    <hkern u1="X" u2="&#x203a;" k="20" />
    <hkern u1="X" u2="&#x2014;" k="50" />
    <hkern u1="X" u2="&#x2013;" k="50" />
    <hkern u1="X" u2="&#x3a9;" k="20" />
    <hkern u1="X" u2="&#x153;" k="45" />
    <hkern u1="X" u2="&#x152;" k="50" />
    <hkern u1="X" u2="&#xe6;" k="10" />
    <hkern u1="X" u2="&#xc6;" k="5" />
    <hkern u1="X" u2="&#xbb;" k="20" />
    <hkern u1="X" u2="&#xb5;" k="50" />
    <hkern u1="X" u2="&#xab;" k="50" />
    <hkern u1="X" u2="y" k="40" />
    <hkern u1="X" u2="w" k="40" />
    <hkern u1="X" u2="v" k="50" />
    <hkern u1="X" u2="u" k="20" />
    <hkern u1="X" u2="t" k="20" />
    <hkern u1="X" u2="q" k="40" />
    <hkern u1="X" u2="o" k="45" />
    <hkern u1="X" u2="l" k="10" />
    <hkern u1="X" u2="j" k="10" />
    <hkern u1="X" u2="i" k="10" />
    <hkern u1="X" u2="g" k="40" />
    <hkern u1="X" u2="f" k="20" />
    <hkern u1="X" u2="e" k="45" />
    <hkern u1="X" u2="d" k="40" />
    <hkern u1="X" u2="c" k="45" />
    <hkern u1="X" u2="a" k="10" />
    <hkern u1="X" u2="Y" k="23" />
    <hkern u1="X" u2="W" k="15" />
    <hkern u1="X" u2="V" k="20" />
    <hkern u1="X" u2="U" k="10" />
    <hkern u1="X" u2="S" k="30" />
    <hkern u1="X" u2="Q" k="50" />
    <hkern u1="X" u2="O" k="50" />
    <hkern u1="X" u2="J" k="10" />
    <hkern u1="X" u2="G" k="50" />
    <hkern u1="X" u2="C" k="50" />
    <hkern u1="X" u2="A" k="5" />
    <hkern u1="X" u2="&#x3f;" k="15" />
    <hkern u1="X" u2="&#x2d;" k="50" />
    <hkern u1="X" u2="&#x26;" k="10" />
    <hkern u1="Y" u2="&#xfb02;" k="40" />
    <hkern u1="Y" u2="&#xfb01;" k="40" />
    <hkern u1="Y" u2="&#x2248;" k="40" />
    <hkern u1="Y" u2="&#x221a;" k="40" />
    <hkern u1="Y" u2="&#x203a;" k="75" />
    <hkern u1="Y" u2="&#x2026;" k="130" />
    <hkern u1="Y" u2="&#x2014;" k="80" />
    <hkern u1="Y" u2="&#x2013;" k="80" />
    <hkern u1="Y" u2="&#x3a9;" k="40" />
    <hkern u1="Y" u2="&#x153;" k="110" />
    <hkern u1="Y" u2="&#x152;" k="60" />
    <hkern u1="Y" u2="&#xe6;" k="100" />
    <hkern u1="Y" u2="&#xc6;" k="110" />
    <hkern u1="Y" u2="&#xbb;" k="75" />
    <hkern u1="Y" u2="&#xb5;" k="60" />
    <hkern u1="Y" u2="&#xab;" k="100" />
    <hkern u1="Y" u2="z" k="80" />
    <hkern u1="Y" u2="y" k="60" />
    <hkern u1="Y" u2="x" k="70" />
    <hkern u1="Y" u2="w" k="55" />
    <hkern u1="Y" u2="v" k="60" />
    <hkern u1="Y" u2="u" k="75" />
    <hkern u1="Y" u2="t" k="30" />
    <hkern u1="Y" u2="s" k="100" />
    <hkern u1="Y" u2="r" k="75" />
    <hkern u1="Y" u2="q" k="105" />
    <hkern u1="Y" u2="p" k="75" />
    <hkern u1="Y" u2="o" k="110" />
    <hkern u1="Y" u2="n" k="75" />
    <hkern u1="Y" u2="m" k="75" />
    <hkern u1="Y" u2="l" k="10" />
    <hkern u1="Y" u2="j" k="20" />
    <hkern u1="Y" u2="i" k="20" />
    <hkern u1="Y" u2="g" k="105" />
    <hkern u1="Y" u2="f" k="40" />
    <hkern u1="Y" u2="e" k="110" />
    <hkern u1="Y" u2="d" k="105" />
    <hkern u1="Y" u2="c" k="110" />
    <hkern u1="Y" u2="a" k="100" />
    <hkern u1="Y" u2="Z" k="10" />
    <hkern u1="Y" u2="Y" k="5" />
    <hkern u1="Y" u2="X" k="23" />
    <hkern u1="Y" u2="W" k="20" />
    <hkern u1="Y" u2="V" k="20" />
    <hkern u1="Y" u2="S" k="35" />
    <hkern u1="Y" u2="Q" k="60" />
    <hkern u1="Y" u2="O" k="60" />
    <hkern u1="Y" u2="J" k="130" />
    <hkern u1="Y" u2="G" k="60" />
    <hkern u1="Y" u2="C" k="60" />
    <hkern u1="Y" u2="A" k="110" />
    <hkern u1="Y" u2="&#x3b;" k="40" />
    <hkern u1="Y" u2="&#x3a;" k="40" />
    <hkern u1="Y" u2="&#x2f;" k="110" />
    <hkern u1="Y" u2="&#x2e;" k="130" />
    <hkern u1="Y" u2="&#x2d;" k="80" />
    <hkern u1="Y" u2="&#x2c;" k="130" />
    <hkern u1="Y" u2="&#x26;" k="66" />
    <hkern u1="Z" u2="&#xfb02;" k="10" />
    <hkern u1="Z" u2="&#xfb01;" k="10" />
    <hkern u1="Z" u2="&#x2248;" k="10" />
    <hkern u1="Z" u2="&#x221a;" k="10" />
    <hkern u1="Z" u2="&#x2014;" k="30" />
    <hkern u1="Z" u2="&#x2013;" k="30" />
    <hkern u1="Z" u2="&#x3a9;" k="10" />
    <hkern u1="Z" u2="&#x153;" k="25" />
    <hkern u1="Z" u2="&#x152;" k="39" />
    <hkern u1="Z" u2="&#xb5;" k="39" />
    <hkern u1="Z" u2="&#xab;" k="20" />
    <hkern u1="Z" u2="y" k="15" />
    <hkern u1="Z" u2="w" k="15" />
    <hkern u1="Z" u2="v" k="20" />
    <hkern u1="Z" u2="q" k="20" />
    <hkern u1="Z" u2="o" k="25" />
    <hkern u1="Z" u2="g" k="20" />
    <hkern u1="Z" u2="f" k="10" />
    <hkern u1="Z" u2="e" k="25" />
    <hkern u1="Z" u2="d" k="20" />
    <hkern u1="Z" u2="c" k="25" />
    <hkern u1="Z" u2="Z" k="10" />
    <hkern u1="Z" u2="S" k="10" />
    <hkern u1="Z" u2="Q" k="39" />
    <hkern u1="Z" u2="O" k="39" />
    <hkern u1="Z" u2="G" k="39" />
    <hkern u1="Z" u2="C" k="39" />
    <hkern u1="Z" u2="&#x2d;" k="30" />
    <hkern u1="Z" u2="&#x26;" k="11" />
    <hkern u1="[" u2="&#x153;" k="20" />
    <hkern u1="[" u2="&#x152;" k="20" />
    <hkern u1="[" u2="&#xe6;" k="10" />
    <hkern u1="[" u2="&#xb5;" k="20" />
    <hkern u1="[" u2="y" k="10" />
    <hkern u1="[" u2="x" k="10" />
    <hkern u1="[" u2="w" k="20" />
    <hkern u1="[" u2="v" k="20" />
    <hkern u1="[" u2="s" k="15" />
    <hkern u1="[" u2="q" k="20" />
    <hkern u1="[" u2="o" k="20" />
    <hkern u1="[" u2="j" k="-30" />
    <hkern u1="[" u2="e" k="20" />
    <hkern u1="[" u2="d" k="20" />
    <hkern u1="[" u2="c" k="20" />
    <hkern u1="[" u2="a" k="10" />
    <hkern u1="[" u2="Q" k="20" />
    <hkern u1="[" u2="O" k="20" />
    <hkern u1="[" u2="J" k="10" />
    <hkern u1="[" u2="G" k="20" />
    <hkern u1="[" u2="C" k="20" />
    <hkern u1="\" u2="&#xfb02;" k="10" />
    <hkern u1="\" u2="&#xfb01;" k="10" />
    <hkern u1="\" u2="&#x2248;" k="10" />
    <hkern u1="\" u2="&#x221a;" k="10" />
    <hkern u1="\" u2="&#x3a9;" k="10" />
    <hkern u1="\" u2="&#x152;" k="40" />
    <hkern u1="\" u2="&#xb5;" k="40" />
    <hkern u1="\" u2="y" k="60" />
    <hkern u1="\" u2="w" k="60" />
    <hkern u1="\" u2="v" k="70" />
    <hkern u1="\" u2="t" k="30" />
    <hkern u1="\" u2="j" k="-30" />
    <hkern u1="\" u2="f" k="10" />
    <hkern u1="\" u2="Y" k="110" />
    <hkern u1="\" u2="W" k="100" />
    <hkern u1="\" u2="V" k="120" />
    <hkern u1="\" u2="U" k="15" />
    <hkern u1="\" u2="T" k="90" />
    <hkern u1="\" u2="Q" k="40" />
    <hkern u1="\" u2="O" k="40" />
    <hkern u1="\" u2="G" k="40" />
    <hkern u1="\" u2="C" k="40" />
    <hkern u1="a" u2="y" k="20" />
    <hkern u1="a" u2="w" k="20" />
    <hkern u1="a" u2="v" k="20" />
    <hkern u1="a" u2="t" k="5" />
    <hkern u1="a" u2="\" k="75" />
    <hkern u1="a" u2="&#x3f;" k="35" />
    <hkern u1="a" u2="&#x2a;" k="15" />
    <hkern u1="b" u2="&#x203a;" k="5" />
    <hkern u1="b" u2="&#x2026;" k="10" />
    <hkern u1="b" u2="&#x201c;" k="10" />
    <hkern u1="b" u2="&#x2018;" k="10" />
    <hkern u1="b" u2="&#xbb;" k="5" />
    <hkern u1="b" u2="&#x7d;" k="15" />
    <hkern u1="b" u2="z" k="15" />
    <hkern u1="b" u2="y" k="25" />
    <hkern u1="b" u2="x" k="30" />
    <hkern u1="b" u2="w" k="20" />
    <hkern u1="b" u2="v" k="25" />
    <hkern u1="b" u2="]" k="20" />
    <hkern u1="b" u2="\" k="70" />
    <hkern u1="b" u2="&#x3f;" k="35" />
    <hkern u1="b" u2="&#x2e;" k="10" />
    <hkern u1="b" u2="&#x2c;" k="10" />
    <hkern u1="b" u2="&#x2a;" k="15" />
    <hkern u1="b" u2="&#x29;" k="30" />
    <hkern u1="c" u2="&#x203a;" k="-10" />
    <hkern u1="c" u2="&#x2039;" k="10" />
    <hkern u1="c" u2="&#x201d;" k="-15" />
    <hkern u1="c" u2="&#x201c;" k="-10" />
    <hkern u1="c" u2="&#x2019;" k="-15" />
    <hkern u1="c" u2="&#x2018;" k="-10" />
    <hkern u1="c" u2="&#x153;" k="15" />
    <hkern u1="c" u2="&#xbb;" k="-10" />
    <hkern u1="c" u2="&#xab;" k="10" />
    <hkern u1="c" u2="y" k="5" />
    <hkern u1="c" u2="x" k="10" />
    <hkern u1="c" u2="w" k="5" />
    <hkern u1="c" u2="v" k="5" />
    <hkern u1="c" u2="q" k="10" />
    <hkern u1="c" u2="o" k="15" />
    <hkern u1="c" u2="g" k="10" />
    <hkern u1="c" u2="e" k="15" />
    <hkern u1="c" u2="d" k="10" />
    <hkern u1="c" u2="c" k="15" />
    <hkern u1="c" u2="\" k="40" />
    <hkern u1="c" u2="&#x3f;" k="15" />
    <hkern u1="c" u2="&#x29;" k="15" />
    <hkern u1="e" u2="&#x2026;" k="10" />
    <hkern u1="e" u2="&#x7d;" k="10" />
    <hkern u1="e" u2="z" k="15" />
    <hkern u1="e" u2="y" k="25" />
    <hkern u1="e" u2="x" k="30" />
    <hkern u1="e" u2="w" k="25" />
    <hkern u1="e" u2="v" k="25" />
    <hkern u1="e" u2="]" k="20" />
    <hkern u1="e" u2="\" k="80" />
    <hkern u1="e" u2="&#x3f;" k="40" />
    <hkern u1="e" u2="&#x2e;" k="10" />
    <hkern u1="e" u2="&#x2c;" k="10" />
    <hkern u1="e" u2="&#x2a;" k="20" />
    <hkern u1="e" u2="&#x29;" k="30" />
    <hkern u1="f" u2="&#x2122;" k="-51" />
    <hkern u1="f" u2="&#x2039;" k="15" />
    <hkern u1="f" u2="&#x2026;" k="45" />
    <hkern u1="f" u2="&#x201d;" k="-35" />
    <hkern u1="f" u2="&#x201c;" k="-30" />
    <hkern u1="f" u2="&#x2019;" k="-35" />
    <hkern u1="f" u2="&#x2018;" k="-30" />
    <hkern u1="f" u2="&#x153;" k="10" />
    <hkern u1="f" u2="&#xe6;" k="15" />
    <hkern u1="f" u2="&#xab;" k="15" />
    <hkern u1="f" u2="&#x7d;" k="-30" />
    <hkern u1="f" u2="z" k="10" />
    <hkern u1="f" u2="q" k="10" />
    <hkern u1="f" u2="o" k="10" />
    <hkern u1="f" u2="g" k="10" />
    <hkern u1="f" u2="e" k="10" />
    <hkern u1="f" u2="d" k="10" />
    <hkern u1="f" u2="c" k="10" />
    <hkern u1="f" u2="a" k="15" />
    <hkern u1="f" u2="]" k="-20" />
    <hkern u1="f" u2="\" k="-30" />
    <hkern u1="f" u2="&#x3f;" k="-35" />
    <hkern u1="f" u2="&#x2f;" k="45" />
    <hkern u1="f" u2="&#x2e;" k="45" />
    <hkern u1="f" u2="&#x2c;" k="45" />
    <hkern u1="f" u2="&#x2a;" k="-30" />
    <hkern u1="f" u2="&#x29;" k="-30" />
    <hkern u1="g" u2="\" k="50" />
    <hkern u1="h" u2="y" k="15" />
    <hkern u1="h" u2="w" k="15" />
    <hkern u1="h" u2="v" k="20" />
    <hkern u1="h" u2="\" k="75" />
    <hkern u1="h" u2="&#x3f;" k="30" />
    <hkern u1="h" u2="&#x2a;" k="15" />
    <hkern u1="k" u2="&#x203a;" k="10" />
    <hkern u1="k" u2="&#x2039;" k="20" />
    <hkern u1="k" u2="&#x2014;" k="20" />
    <hkern u1="k" u2="&#x2013;" k="20" />
    <hkern u1="k" u2="&#x153;" k="25" />
    <hkern u1="k" u2="&#xe6;" k="10" />
    <hkern u1="k" u2="&#xbb;" k="10" />
    <hkern u1="k" u2="&#xab;" k="20" />
    <hkern u1="k" u2="y" k="15" />
    <hkern u1="k" u2="w" k="20" />
    <hkern u1="k" u2="v" k="20" />
    <hkern u1="k" u2="u" k="10" />
    <hkern u1="k" u2="t" k="10" />
    <hkern u1="k" u2="q" k="25" />
    <hkern u1="k" u2="o" k="25" />
    <hkern u1="k" u2="g" k="25" />
    <hkern u1="k" u2="e" k="25" />
    <hkern u1="k" u2="d" k="25" />
    <hkern u1="k" u2="c" k="25" />
    <hkern u1="k" u2="a" k="10" />
    <hkern u1="k" u2="\" k="40" />
    <hkern u1="k" u2="&#x2d;" k="20" />
    <hkern u1="m" u2="y" k="15" />
    <hkern u1="m" u2="w" k="15" />
    <hkern u1="m" u2="v" k="20" />
    <hkern u1="m" u2="\" k="75" />
    <hkern u1="m" u2="&#x3f;" k="30" />
    <hkern u1="m" u2="&#x2a;" k="15" />
    <hkern u1="n" u2="y" k="15" />
    <hkern u1="n" u2="w" k="15" />
    <hkern u1="n" u2="v" k="20" />
    <hkern u1="n" u2="\" k="75" />
    <hkern u1="n" u2="&#x3f;" k="30" />
    <hkern u1="n" u2="&#x2a;" k="15" />
    <hkern u1="o" u2="&#x203a;" k="10" />
    <hkern u1="o" u2="&#x2026;" k="20" />
    <hkern u1="o" u2="&#x201c;" k="20" />
    <hkern u1="o" u2="&#x2018;" k="20" />
    <hkern u1="o" u2="&#xbb;" k="10" />
    <hkern u1="o" u2="&#x7d;" k="15" />
    <hkern u1="o" u2="z" k="20" />
    <hkern u1="o" u2="y" k="30" />
    <hkern u1="o" u2="x" k="35" />
    <hkern u1="o" u2="w" k="25" />
    <hkern u1="o" u2="v" k="30" />
    <hkern u1="o" u2="]" k="20" />
    <hkern u1="o" u2="\" k="80" />
    <hkern u1="o" u2="&#x3f;" k="50" />
    <hkern u1="o" u2="&#x2e;" k="20" />
    <hkern u1="o" u2="&#x2c;" k="20" />
    <hkern u1="o" u2="&#x2a;" k="20" />
    <hkern u1="o" u2="&#x29;" k="30" />
    <hkern u1="p" u2="&#x203a;" k="5" />
    <hkern u1="p" u2="&#x2026;" k="10" />
    <hkern u1="p" u2="&#x201c;" k="10" />
    <hkern u1="p" u2="&#x2018;" k="10" />
    <hkern u1="p" u2="&#xbb;" k="5" />
    <hkern u1="p" u2="&#x7d;" k="15" />
    <hkern u1="p" u2="z" k="15" />
    <hkern u1="p" u2="y" k="25" />
    <hkern u1="p" u2="x" k="30" />
    <hkern u1="p" u2="w" k="20" />
    <hkern u1="p" u2="v" k="25" />
    <hkern u1="p" u2="]" k="20" />
    <hkern u1="p" u2="\" k="70" />
    <hkern u1="p" u2="&#x3f;" k="35" />
    <hkern u1="p" u2="&#x2e;" k="10" />
    <hkern u1="p" u2="&#x2c;" k="10" />
    <hkern u1="p" u2="&#x2a;" k="15" />
    <hkern u1="p" u2="&#x29;" k="30" />
    <hkern u1="q" u2="\" k="50" />
    <hkern u1="r" u2="&#x2039;" k="10" />
    <hkern u1="r" u2="&#x2026;" k="90" />
    <hkern u1="r" u2="&#x201d;" k="-35" />
    <hkern u1="r" u2="&#x201c;" k="-20" />
    <hkern u1="r" u2="&#x2019;" k="-35" />
    <hkern u1="r" u2="&#x2018;" k="-20" />
    <hkern u1="r" u2="&#x153;" k="22" />
    <hkern u1="r" u2="&#xe6;" k="25" />
    <hkern u1="r" u2="&#xab;" k="10" />
    <hkern u1="r" u2="z" k="10" />
    <hkern u1="r" u2="q" k="21" />
    <hkern u1="r" u2="o" k="22" />
    <hkern u1="r" u2="g" k="21" />
    <hkern u1="r" u2="e" k="22" />
    <hkern u1="r" u2="d" k="21" />
    <hkern u1="r" u2="c" k="22" />
    <hkern u1="r" u2="a" k="25" />
    <hkern u1="r" u2="\" k="30" />
    <hkern u1="r" u2="&#x2f;" k="75" />
    <hkern u1="r" u2="&#x2e;" k="90" />
    <hkern u1="r" u2="&#x2c;" k="90" />
    <hkern u1="r" u2="&#x2a;" k="-20" />
    <hkern u1="s" u2="&#x2039;" k="10" />
    <hkern u1="s" u2="&#x201c;" k="10" />
    <hkern u1="s" u2="&#x2018;" k="10" />
    <hkern u1="s" u2="&#xab;" k="10" />
    <hkern u1="s" u2="&#x7d;" k="10" />
    <hkern u1="s" u2="z" k="10" />
    <hkern u1="s" u2="y" k="15" />
    <hkern u1="s" u2="x" k="25" />
    <hkern u1="s" u2="w" k="15" />
    <hkern u1="s" u2="v" k="20" />
    <hkern u1="s" u2="t" k="10" />
    <hkern u1="s" u2="s" k="10" />
    <hkern u1="s" u2="]" k="15" />
    <hkern u1="s" u2="\" k="75" />
    <hkern u1="s" u2="&#x3f;" k="35" />
    <hkern u1="s" u2="&#x29;" k="20" />
    <hkern u1="t" u2="&#x2039;" k="10" />
    <hkern u1="t" u2="&#x201d;" k="-10" />
    <hkern u1="t" u2="&#x2019;" k="-10" />
    <hkern u1="t" u2="&#x153;" k="15" />
    <hkern u1="t" u2="&#xab;" k="10" />
    <hkern u1="t" u2="q" k="15" />
    <hkern u1="t" u2="o" k="15" />
    <hkern u1="t" u2="g" k="15" />
    <hkern u1="t" u2="e" k="15" />
    <hkern u1="t" u2="d" k="15" />
    <hkern u1="t" u2="c" k="15" />
    <hkern u1="t" u2="\" k="40" />
    <hkern u1="u" u2="\" k="50" />
    <hkern u1="v" u2="&#x203a;" k="15" />
    <hkern u1="v" u2="&#x2039;" k="30" />
    <hkern u1="v" u2="&#x2026;" k="85" />
    <hkern u1="v" u2="&#x2014;" k="15" />
    <hkern u1="v" u2="&#x2013;" k="15" />
    <hkern u1="v" u2="&#x153;" k="30" />
    <hkern u1="v" u2="&#xe6;" k="25" />
    <hkern u1="v" u2="&#xbb;" k="15" />
    <hkern u1="v" u2="&#xab;" k="30" />
    <hkern u1="v" u2="&#x7d;" k="10" />
    <hkern u1="v" u2="z" k="5" />
    <hkern u1="v" u2="y" k="15" />
    <hkern u1="v" u2="x" k="10" />
    <hkern u1="v" u2="w" k="15" />
    <hkern u1="v" u2="v" k="15" />
    <hkern u1="v" u2="s" k="20" />
    <hkern u1="v" u2="q" k="25" />
    <hkern u1="v" u2="o" k="30" />
    <hkern u1="v" u2="g" k="25" />
    <hkern u1="v" u2="e" k="30" />
    <hkern u1="v" u2="d" k="25" />
    <hkern u1="v" u2="c" k="30" />
    <hkern u1="v" u2="a" k="25" />
    <hkern u1="v" u2="]" k="20" />
    <hkern u1="v" u2="\" k="50" />
    <hkern u1="v" u2="&#x3f;" k="10" />
    <hkern u1="v" u2="&#x2f;" k="70" />
    <hkern u1="v" u2="&#x2e;" k="85" />
    <hkern u1="v" u2="&#x2d;" k="15" />
    <hkern u1="v" u2="&#x2c;" k="85" />
    <hkern u1="w" u2="&#x203a;" k="10" />
    <hkern u1="w" u2="&#x2039;" k="20" />
    <hkern u1="w" u2="&#x2026;" k="70" />
    <hkern u1="w" u2="&#x2014;" k="10" />
    <hkern u1="w" u2="&#x2013;" k="10" />
    <hkern u1="w" u2="&#x153;" k="25" />
    <hkern u1="w" u2="&#xe6;" k="20" />
    <hkern u1="w" u2="&#xbb;" k="10" />
    <hkern u1="w" u2="&#xab;" k="20" />
    <hkern u1="w" u2="&#x7d;" k="10" />
    <hkern u1="w" u2="z" k="5" />
    <hkern u1="w" u2="y" k="10" />
    <hkern u1="w" u2="x" k="10" />
    <hkern u1="w" u2="w" k="10" />
    <hkern u1="w" u2="v" k="15" />
    <hkern u1="w" u2="s" k="15" />
    <hkern u1="w" u2="q" k="20" />
    <hkern u1="w" u2="o" k="25" />
    <hkern u1="w" u2="g" k="20" />
    <hkern u1="w" u2="e" k="25" />
    <hkern u1="w" u2="d" k="20" />
    <hkern u1="w" u2="c" k="25" />
    <hkern u1="w" u2="a" k="20" />
    <hkern u1="w" u2="]" k="20" />
    <hkern u1="w" u2="\" k="50" />
    <hkern u1="w" u2="&#x3f;" k="10" />
    <hkern u1="w" u2="&#x2f;" k="60" />
    <hkern u1="w" u2="&#x2e;" k="70" />
    <hkern u1="w" u2="&#x2d;" k="10" />
    <hkern u1="w" u2="&#x2c;" k="70" />
    <hkern u1="x" u2="&#x203a;" k="15" />
    <hkern u1="x" u2="&#x2039;" k="45" />
    <hkern u1="x" u2="&#x2014;" k="30" />
    <hkern u1="x" u2="&#x2013;" k="30" />
    <hkern u1="x" u2="&#x153;" k="35" />
    <hkern u1="x" u2="&#xe6;" k="15" />
    <hkern u1="x" u2="&#xbb;" k="15" />
    <hkern u1="x" u2="&#xab;" k="45" />
    <hkern u1="x" u2="&#x7d;" k="10" />
    <hkern u1="x" u2="y" k="10" />
    <hkern u1="x" u2="w" k="10" />
    <hkern u1="x" u2="v" k="10" />
    <hkern u1="x" u2="s" k="20" />
    <hkern u1="x" u2="q" k="30" />
    <hkern u1="x" u2="o" k="35" />
    <hkern u1="x" u2="g" k="30" />
    <hkern u1="x" u2="e" k="35" />
    <hkern u1="x" u2="d" k="30" />
    <hkern u1="x" u2="c" k="35" />
    <hkern u1="x" u2="a" k="15" />
    <hkern u1="x" u2="]" k="10" />
    <hkern u1="x" u2="\" k="50" />
    <hkern u1="x" u2="&#x3f;" k="15" />
    <hkern u1="x" u2="&#x2d;" k="30" />
    <hkern u1="y" u2="&#x203a;" k="15" />
    <hkern u1="y" u2="&#x2039;" k="30" />
    <hkern u1="y" u2="&#x2026;" k="85" />
    <hkern u1="y" u2="&#x2014;" k="15" />
    <hkern u1="y" u2="&#x2013;" k="15" />
    <hkern u1="y" u2="&#x153;" k="30" />
    <hkern u1="y" u2="&#xe6;" k="25" />
    <hkern u1="y" u2="&#xbb;" k="15" />
    <hkern u1="y" u2="&#xab;" k="30" />
    <hkern u1="y" u2="&#x7d;" k="10" />
    <hkern u1="y" u2="z" k="5" />
    <hkern u1="y" u2="y" k="10" />
    <hkern u1="y" u2="x" k="10" />
    <hkern u1="y" u2="w" k="10" />
    <hkern u1="y" u2="v" k="15" />
    <hkern u1="y" u2="s" k="20" />
    <hkern u1="y" u2="q" k="25" />
    <hkern u1="y" u2="o" k="30" />
    <hkern u1="y" u2="g" k="25" />
    <hkern u1="y" u2="e" k="30" />
    <hkern u1="y" u2="d" k="25" />
    <hkern u1="y" u2="c" k="30" />
    <hkern u1="y" u2="a" k="25" />
    <hkern u1="y" u2="]" k="20" />
    <hkern u1="y" u2="\" k="50" />
    <hkern u1="y" u2="&#x3f;" k="10" />
    <hkern u1="y" u2="&#x2f;" k="70" />
    <hkern u1="y" u2="&#x2e;" k="85" />
    <hkern u1="y" u2="&#x2d;" k="15" />
    <hkern u1="y" u2="&#x2c;" k="85" />
    <hkern u1="z" u2="&#x2039;" k="15" />
    <hkern u1="z" u2="&#x153;" k="15" />
    <hkern u1="z" u2="&#xab;" k="15" />
    <hkern u1="z" u2="q" k="15" />
    <hkern u1="z" u2="o" k="15" />
    <hkern u1="z" u2="g" k="15" />
    <hkern u1="z" u2="e" k="15" />
    <hkern u1="z" u2="d" k="15" />
    <hkern u1="z" u2="c" k="15" />
    <hkern u1="z" u2="\" k="45" />
    <hkern u1="&#x7b;" u2="&#x153;" k="15" />
    <hkern u1="&#x7b;" u2="&#x152;" k="20" />
    <hkern u1="&#x7b;" u2="&#xb5;" k="20" />
    <hkern u1="&#x7b;" u2="z" k="10" />
    <hkern u1="&#x7b;" u2="y" k="10" />
    <hkern u1="&#x7b;" u2="x" k="10" />
    <hkern u1="&#x7b;" u2="w" k="10" />
    <hkern u1="&#x7b;" u2="v" k="10" />
    <hkern u1="&#x7b;" u2="s" k="10" />
    <hkern u1="&#x7b;" u2="q" k="15" />
    <hkern u1="&#x7b;" u2="o" k="15" />
    <hkern u1="&#x7b;" u2="j" k="-35" />
    <hkern u1="&#x7b;" u2="g" k="10" />
    <hkern u1="&#x7b;" u2="e" k="15" />
    <hkern u1="&#x7b;" u2="d" k="15" />
    <hkern u1="&#x7b;" u2="c" k="15" />
    <hkern u1="&#x7b;" u2="Q" k="20" />
    <hkern u1="&#x7b;" u2="O" k="20" />
    <hkern u1="&#x7b;" u2="J" k="10" />
    <hkern u1="&#x7b;" u2="G" k="20" />
    <hkern u1="&#x7b;" u2="C" k="20" />
    <hkern u1="&#xa3;" u2="&#x34;" k="15" />
    <hkern u1="&#xa4;" u2="&#x31;" k="-20" />
    <hkern u1="&#xa5;" u2="&#x34;" k="20" />
    <hkern u1="&#xa7;" u2="&#x37;" k="15" />
    <hkern u1="&#xab;" u2="&#x153;" k="10" />
    <hkern u1="&#xab;" u2="y" k="15" />
    <hkern u1="&#xab;" u2="x" k="15" />
    <hkern u1="&#xab;" u2="w" k="10" />
    <hkern u1="&#xab;" u2="v" k="15" />
    <hkern u1="&#xab;" u2="q" k="5" />
    <hkern u1="&#xab;" u2="o" k="10" />
    <hkern u1="&#xab;" u2="g" k="5" />
    <hkern u1="&#xab;" u2="e" k="10" />
    <hkern u1="&#xab;" u2="d" k="5" />
    <hkern u1="&#xab;" u2="c" k="10" />
    <hkern u1="&#xab;" u2="Y" k="75" />
    <hkern u1="&#xab;" u2="X" k="20" />
    <hkern u1="&#xab;" u2="W" k="35" />
    <hkern u1="&#xab;" u2="V" k="40" />
    <hkern u1="&#xab;" u2="T" k="70" />
    <hkern u1="&#xb1;" u2="&#x2215;" k="-30" />
    <hkern u1="&#xb1;" u2="&#x37;" k="20" />
    <hkern u1="&#xb5;" u2="&#x2026;" k="40" />
    <hkern u1="&#xb5;" u2="&#xc6;" k="41" />
    <hkern u1="&#xb5;" u2="&#x7d;" k="20" />
    <hkern u1="&#xb5;" u2="x" k="5" />
    <hkern u1="&#xb5;" u2="]" k="20" />
    <hkern u1="&#xb5;" u2="\" k="40" />
    <hkern u1="&#xb5;" u2="Z" k="39" />
    <hkern u1="&#xb5;" u2="Y" k="60" />
    <hkern u1="&#xb5;" u2="X" k="50" />
    <hkern u1="&#xb5;" u2="W" k="36" />
    <hkern u1="&#xb5;" u2="V" k="41" />
    <hkern u1="&#xb5;" u2="T" k="48" />
    <hkern u1="&#xb5;" u2="S" k="5" />
    <hkern u1="&#xb5;" u2="J" k="30" />
    <hkern u1="&#xb5;" u2="A" k="41" />
    <hkern u1="&#xb5;" u2="&#x3f;" k="20" />
    <hkern u1="&#xb5;" u2="&#x2f;" k="40" />
    <hkern u1="&#xb5;" u2="&#x2e;" k="40" />
    <hkern u1="&#xb5;" u2="&#x2c;" k="40" />
    <hkern u1="&#xb5;" u2="&#x29;" k="30" />
    <hkern u1="&#xbb;" u2="&#xfb02;" k="10" />
    <hkern u1="&#xbb;" u2="&#xfb01;" k="10" />
    <hkern u1="&#xbb;" u2="&#x2248;" k="10" />
    <hkern u1="&#xbb;" u2="&#x221a;" k="10" />
    <hkern u1="&#xbb;" u2="&#x3a9;" k="10" />
    <hkern u1="&#xbb;" u2="&#xc6;" k="20" />
    <hkern u1="&#xbb;" u2="z" k="20" />
    <hkern u1="&#xbb;" u2="y" k="30" />
    <hkern u1="&#xbb;" u2="x" k="45" />
    <hkern u1="&#xbb;" u2="w" k="20" />
    <hkern u1="&#xbb;" u2="v" k="30" />
    <hkern u1="&#xbb;" u2="t" k="10" />
    <hkern u1="&#xbb;" u2="s" k="10" />
    <hkern u1="&#xbb;" u2="f" k="10" />
    <hkern u1="&#xbb;" u2="Z" k="15" />
    <hkern u1="&#xbb;" u2="Y" k="100" />
    <hkern u1="&#xbb;" u2="X" k="50" />
    <hkern u1="&#xbb;" u2="W" k="50" />
    <hkern u1="&#xbb;" u2="V" k="60" />
    <hkern u1="&#xbb;" u2="T" k="90" />
    <hkern u1="&#xbb;" u2="S" k="10" />
    <hkern u1="&#xbb;" u2="A" k="20" />
    <hkern u1="&#xbf;" u2="&#xfb02;" k="10" />
    <hkern u1="&#xbf;" u2="&#xfb01;" k="10" />
    <hkern u1="&#xbf;" u2="&#x2248;" k="10" />
    <hkern u1="&#xbf;" u2="&#x221a;" k="10" />
    <hkern u1="&#xbf;" u2="&#x3a9;" k="10" />
    <hkern u1="&#xbf;" u2="&#x152;" k="20" />
    <hkern u1="&#xbf;" u2="&#xe6;" k="-10" />
    <hkern u1="&#xbf;" u2="&#xb5;" k="20" />
    <hkern u1="&#xbf;" u2="y" k="35" />
    <hkern u1="&#xbf;" u2="w" k="35" />
    <hkern u1="&#xbf;" u2="v" k="45" />
    <hkern u1="&#xbf;" u2="t" k="15" />
    <hkern u1="&#xbf;" u2="f" k="10" />
    <hkern u1="&#xbf;" u2="a" k="-10" />
    <hkern u1="&#xbf;" u2="Y" k="70" />
    <hkern u1="&#xbf;" u2="X" k="10" />
    <hkern u1="&#xbf;" u2="W" k="50" />
    <hkern u1="&#xbf;" u2="V" k="60" />
    <hkern u1="&#xbf;" u2="U" k="15" />
    <hkern u1="&#xbf;" u2="T" k="60" />
    <hkern u1="&#xbf;" u2="Q" k="20" />
    <hkern u1="&#xbf;" u2="O" k="20" />
    <hkern u1="&#xbf;" u2="G" k="20" />
    <hkern u1="&#xbf;" u2="C" k="20" />
    <hkern u1="&#xc6;" u2="&#x153;" k="10" />
    <hkern u1="&#xc6;" u2="y" k="10" />
    <hkern u1="&#xc6;" u2="w" k="10" />
    <hkern u1="&#xc6;" u2="v" k="10" />
    <hkern u1="&#xc6;" u2="o" k="10" />
    <hkern u1="&#xc6;" u2="e" k="10" />
    <hkern u1="&#xc6;" u2="d" k="10" />
    <hkern u1="&#xc6;" u2="c" k="10" />
    <hkern u1="&#xdf;" u2="y" k="10" />
    <hkern u1="&#xdf;" u2="x" k="10" />
    <hkern u1="&#xdf;" u2="w" k="5" />
    <hkern u1="&#xdf;" u2="v" k="10" />
    <hkern u1="&#xe6;" u2="&#x2026;" k="10" />
    <hkern u1="&#xe6;" u2="&#x7d;" k="10" />
    <hkern u1="&#xe6;" u2="z" k="15" />
    <hkern u1="&#xe6;" u2="y" k="25" />
    <hkern u1="&#xe6;" u2="x" k="30" />
    <hkern u1="&#xe6;" u2="w" k="25" />
    <hkern u1="&#xe6;" u2="v" k="25" />
    <hkern u1="&#xe6;" u2="]" k="20" />
    <hkern u1="&#xe6;" u2="\" k="80" />
    <hkern u1="&#xe6;" u2="&#x3f;" k="40" />
    <hkern u1="&#xe6;" u2="&#x2e;" k="10" />
    <hkern u1="&#xe6;" u2="&#x2c;" k="10" />
    <hkern u1="&#xe6;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe6;" u2="&#x29;" k="30" />
    <hkern u1="&#x152;" u2="&#x153;" k="10" />
    <hkern u1="&#x152;" u2="y" k="10" />
    <hkern u1="&#x152;" u2="w" k="10" />
    <hkern u1="&#x152;" u2="v" k="10" />
    <hkern u1="&#x152;" u2="o" k="10" />
    <hkern u1="&#x152;" u2="e" k="10" />
    <hkern u1="&#x152;" u2="d" k="10" />
    <hkern u1="&#x152;" u2="c" k="10" />
    <hkern u1="&#x153;" u2="&#x2026;" k="10" />
    <hkern u1="&#x153;" u2="&#x7d;" k="10" />
    <hkern u1="&#x153;" u2="z" k="15" />
    <hkern u1="&#x153;" u2="y" k="25" />
    <hkern u1="&#x153;" u2="x" k="30" />
    <hkern u1="&#x153;" u2="w" k="25" />
    <hkern u1="&#x153;" u2="v" k="25" />
    <hkern u1="&#x153;" u2="]" k="20" />
    <hkern u1="&#x153;" u2="\" k="80" />
    <hkern u1="&#x153;" u2="&#x3f;" k="40" />
    <hkern u1="&#x153;" u2="&#x2e;" k="10" />
    <hkern u1="&#x153;" u2="&#x2c;" k="10" />
    <hkern u1="&#x153;" u2="&#x2a;" k="20" />
    <hkern u1="&#x153;" u2="&#x29;" k="30" />
    <hkern u1="&#x192;" u2="&#x35;" k="10" />
    <hkern u1="&#x192;" u2="&#x34;" k="30" />
    <hkern u1="&#x192;" u2="&#x31;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x2122;" k="-51" />
    <hkern u1="&#x3a9;" u2="&#x2039;" k="15" />
    <hkern u1="&#x3a9;" u2="&#x2026;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x201d;" k="-35" />
    <hkern u1="&#x3a9;" u2="&#x201c;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x2019;" k="-35" />
    <hkern u1="&#x3a9;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x153;" k="10" />
    <hkern u1="&#x3a9;" u2="&#xe6;" k="15" />
    <hkern u1="&#x3a9;" u2="&#xab;" k="15" />
    <hkern u1="&#x3a9;" u2="&#x7d;" k="-30" />
    <hkern u1="&#x3a9;" u2="z" k="10" />
    <hkern u1="&#x3a9;" u2="q" k="10" />
    <hkern u1="&#x3a9;" u2="o" k="10" />
    <hkern u1="&#x3a9;" u2="g" k="10" />
    <hkern u1="&#x3a9;" u2="e" k="10" />
    <hkern u1="&#x3a9;" u2="d" k="10" />
    <hkern u1="&#x3a9;" u2="c" k="10" />
    <hkern u1="&#x3a9;" u2="a" k="15" />
    <hkern u1="&#x3a9;" u2="]" k="-20" />
    <hkern u1="&#x3a9;" u2="\" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x3f;" k="-35" />
    <hkern u1="&#x3a9;" u2="&#x2f;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x2e;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x2c;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x2a;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x29;" k="-30" />
    <hkern u1="&#x2013;" u2="&#xc6;" k="40" />
    <hkern u1="&#x2013;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2013;" u2="z" k="10" />
    <hkern u1="&#x2013;" u2="y" k="15" />
    <hkern u1="&#x2013;" u2="x" k="30" />
    <hkern u1="&#x2013;" u2="w" k="10" />
    <hkern u1="&#x2013;" u2="v" k="15" />
    <hkern u1="&#x2013;" u2="Z" k="30" />
    <hkern u1="&#x2013;" u2="Y" k="80" />
    <hkern u1="&#x2013;" u2="X" k="50" />
    <hkern u1="&#x2013;" u2="W" k="35" />
    <hkern u1="&#x2013;" u2="V" k="40" />
    <hkern u1="&#x2013;" u2="T" k="90" />
    <hkern u1="&#x2013;" u2="A" k="40" />
    <hkern u1="&#x2013;" u2="&#x37;" k="40" />
    <hkern u1="&#x2013;" u2="&#x33;" k="10" />
    <hkern u1="&#x2013;" u2="&#x31;" k="30" />
    <hkern u1="&#x2014;" u2="&#xc6;" k="40" />
    <hkern u1="&#x2014;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2014;" u2="z" k="10" />
    <hkern u1="&#x2014;" u2="y" k="15" />
    <hkern u1="&#x2014;" u2="x" k="30" />
    <hkern u1="&#x2014;" u2="w" k="10" />
    <hkern u1="&#x2014;" u2="v" k="15" />
    <hkern u1="&#x2014;" u2="Z" k="30" />
    <hkern u1="&#x2014;" u2="Y" k="80" />
    <hkern u1="&#x2014;" u2="X" k="50" />
    <hkern u1="&#x2014;" u2="W" k="35" />
    <hkern u1="&#x2014;" u2="V" k="40" />
    <hkern u1="&#x2014;" u2="T" k="90" />
    <hkern u1="&#x2014;" u2="A" k="40" />
    <hkern u1="&#x2014;" u2="&#x37;" k="40" />
    <hkern u1="&#x2014;" u2="&#x33;" k="10" />
    <hkern u1="&#x2014;" u2="&#x31;" k="30" />
    <hkern u1="&#x2018;" u2="&#x153;" k="15" />
    <hkern u1="&#x2018;" u2="&#xe6;" k="10" />
    <hkern u1="&#x2018;" u2="&#xc6;" k="90" />
    <hkern u1="&#x2018;" u2="&#xbf;" k="35" />
    <hkern u1="&#x2018;" u2="z" k="10" />
    <hkern u1="&#x2018;" u2="t" k="-15" />
    <hkern u1="&#x2018;" u2="s" k="10" />
    <hkern u1="&#x2018;" u2="q" k="15" />
    <hkern u1="&#x2018;" u2="o" k="15" />
    <hkern u1="&#x2018;" u2="g" k="15" />
    <hkern u1="&#x2018;" u2="e" k="15" />
    <hkern u1="&#x2018;" u2="d" k="15" />
    <hkern u1="&#x2018;" u2="c" k="15" />
    <hkern u1="&#x2018;" u2="a" k="10" />
    <hkern u1="&#x2018;" u2="J" k="80" />
    <hkern u1="&#x2018;" u2="A" k="90" />
    <hkern u1="&#x2019;" u2="&#x153;" k="42" />
    <hkern u1="&#x2019;" u2="&#xe6;" k="20" />
    <hkern u1="&#x2019;" u2="&#xc6;" k="100" />
    <hkern u1="&#x2019;" u2="s" k="26" />
    <hkern u1="&#x2019;" u2="q" k="37" />
    <hkern u1="&#x2019;" u2="o" k="42" />
    <hkern u1="&#x2019;" u2="g" k="37" />
    <hkern u1="&#x2019;" u2="e" k="42" />
    <hkern u1="&#x2019;" u2="d" k="37" />
    <hkern u1="&#x2019;" u2="c" k="42" />
    <hkern u1="&#x2019;" u2="a" k="20" />
    <hkern u1="&#x2019;" u2="J" k="100" />
    <hkern u1="&#x2019;" u2="A" k="100" />
    <hkern u1="&#x201a;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x201a;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x201a;" u2="&#x2248;" k="15" />
    <hkern u1="&#x201a;" u2="&#x221a;" k="15" />
    <hkern u1="&#x201a;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x201a;" u2="&#x153;" k="20" />
    <hkern u1="&#x201a;" u2="&#x152;" k="40" />
    <hkern u1="&#x201a;" u2="&#xb5;" k="40" />
    <hkern u1="&#x201a;" u2="y" k="45" />
    <hkern u1="&#x201a;" u2="w" k="70" />
    <hkern u1="&#x201a;" u2="v" k="85" />
    <hkern u1="&#x201a;" u2="t" k="25" />
    <hkern u1="&#x201a;" u2="q" k="10" />
    <hkern u1="&#x201a;" u2="o" k="20" />
    <hkern u1="&#x201a;" u2="j" k="-15" />
    <hkern u1="&#x201a;" u2="g" k="10" />
    <hkern u1="&#x201a;" u2="f" k="15" />
    <hkern u1="&#x201a;" u2="e" k="20" />
    <hkern u1="&#x201a;" u2="d" k="10" />
    <hkern u1="&#x201a;" u2="c" k="20" />
    <hkern u1="&#x201a;" u2="Y" k="130" />
    <hkern u1="&#x201a;" u2="W" k="100" />
    <hkern u1="&#x201a;" u2="V" k="120" />
    <hkern u1="&#x201a;" u2="U" k="15" />
    <hkern u1="&#x201a;" u2="T" k="100" />
    <hkern u1="&#x201a;" u2="Q" k="40" />
    <hkern u1="&#x201a;" u2="O" k="40" />
    <hkern u1="&#x201a;" u2="G" k="40" />
    <hkern u1="&#x201a;" u2="C" k="40" />
    <hkern u1="&#x201c;" u2="&#x153;" k="15" />
    <hkern u1="&#x201c;" u2="&#xe6;" k="10" />
    <hkern u1="&#x201c;" u2="&#xc6;" k="90" />
    <hkern u1="&#x201c;" u2="&#xbf;" k="35" />
    <hkern u1="&#x201c;" u2="z" k="10" />
    <hkern u1="&#x201c;" u2="t" k="-15" />
    <hkern u1="&#x201c;" u2="s" k="10" />
    <hkern u1="&#x201c;" u2="q" k="15" />
    <hkern u1="&#x201c;" u2="o" k="15" />
    <hkern u1="&#x201c;" u2="g" k="15" />
    <hkern u1="&#x201c;" u2="e" k="15" />
    <hkern u1="&#x201c;" u2="d" k="15" />
    <hkern u1="&#x201c;" u2="c" k="15" />
    <hkern u1="&#x201c;" u2="a" k="10" />
    <hkern u1="&#x201c;" u2="J" k="80" />
    <hkern u1="&#x201c;" u2="A" k="90" />
    <hkern u1="&#x201e;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x201e;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x201e;" u2="&#x2248;" k="15" />
    <hkern u1="&#x201e;" u2="&#x221a;" k="15" />
    <hkern u1="&#x201e;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x201e;" u2="&#x153;" k="20" />
    <hkern u1="&#x201e;" u2="&#x152;" k="40" />
    <hkern u1="&#x201e;" u2="&#xb5;" k="40" />
    <hkern u1="&#x201e;" u2="y" k="45" />
    <hkern u1="&#x201e;" u2="w" k="70" />
    <hkern u1="&#x201e;" u2="v" k="85" />
    <hkern u1="&#x201e;" u2="t" k="25" />
    <hkern u1="&#x201e;" u2="q" k="10" />
    <hkern u1="&#x201e;" u2="o" k="20" />
    <hkern u1="&#x201e;" u2="j" k="-15" />
    <hkern u1="&#x201e;" u2="g" k="10" />
    <hkern u1="&#x201e;" u2="f" k="15" />
    <hkern u1="&#x201e;" u2="e" k="20" />
    <hkern u1="&#x201e;" u2="d" k="10" />
    <hkern u1="&#x201e;" u2="c" k="20" />
    <hkern u1="&#x201e;" u2="Y" k="130" />
    <hkern u1="&#x201e;" u2="W" k="100" />
    <hkern u1="&#x201e;" u2="V" k="120" />
    <hkern u1="&#x201e;" u2="U" k="15" />
    <hkern u1="&#x201e;" u2="T" k="100" />
    <hkern u1="&#x201e;" u2="Q" k="40" />
    <hkern u1="&#x201e;" u2="O" k="40" />
    <hkern u1="&#x201e;" u2="G" k="40" />
    <hkern u1="&#x201e;" u2="C" k="40" />
    <hkern u1="&#x2026;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x2026;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x2026;" u2="&#x2248;" k="15" />
    <hkern u1="&#x2026;" u2="&#x221a;" k="15" />
    <hkern u1="&#x2026;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x2026;" u2="&#x153;" k="20" />
    <hkern u1="&#x2026;" u2="&#x152;" k="40" />
    <hkern u1="&#x2026;" u2="&#xb5;" k="40" />
    <hkern u1="&#x2026;" u2="y" k="60" />
    <hkern u1="&#x2026;" u2="w" k="70" />
    <hkern u1="&#x2026;" u2="v" k="85" />
    <hkern u1="&#x2026;" u2="t" k="25" />
    <hkern u1="&#x2026;" u2="q" k="10" />
    <hkern u1="&#x2026;" u2="o" k="20" />
    <hkern u1="&#x2026;" u2="g" k="10" />
    <hkern u1="&#x2026;" u2="f" k="15" />
    <hkern u1="&#x2026;" u2="e" k="20" />
    <hkern u1="&#x2026;" u2="d" k="10" />
    <hkern u1="&#x2026;" u2="c" k="20" />
    <hkern u1="&#x2026;" u2="Y" k="130" />
    <hkern u1="&#x2026;" u2="W" k="100" />
    <hkern u1="&#x2026;" u2="V" k="120" />
    <hkern u1="&#x2026;" u2="U" k="15" />
    <hkern u1="&#x2026;" u2="T" k="100" />
    <hkern u1="&#x2026;" u2="Q" k="40" />
    <hkern u1="&#x2026;" u2="O" k="40" />
    <hkern u1="&#x2026;" u2="G" k="40" />
    <hkern u1="&#x2026;" u2="C" k="40" />
    <hkern u1="&#x2039;" u2="&#x153;" k="10" />
    <hkern u1="&#x2039;" u2="y" k="15" />
    <hkern u1="&#x2039;" u2="x" k="15" />
    <hkern u1="&#x2039;" u2="w" k="10" />
    <hkern u1="&#x2039;" u2="v" k="15" />
    <hkern u1="&#x2039;" u2="q" k="5" />
    <hkern u1="&#x2039;" u2="o" k="10" />
    <hkern u1="&#x2039;" u2="g" k="5" />
    <hkern u1="&#x2039;" u2="e" k="10" />
    <hkern u1="&#x2039;" u2="d" k="5" />
    <hkern u1="&#x2039;" u2="c" k="10" />
    <hkern u1="&#x2039;" u2="Y" k="75" />
    <hkern u1="&#x2039;" u2="X" k="20" />
    <hkern u1="&#x2039;" u2="W" k="35" />
    <hkern u1="&#x2039;" u2="V" k="40" />
    <hkern u1="&#x2039;" u2="T" k="70" />
    <hkern u1="&#x203a;" u2="&#xfb02;" k="10" />
    <hkern u1="&#x203a;" u2="&#xfb01;" k="10" />
    <hkern u1="&#x203a;" u2="&#x2248;" k="10" />
    <hkern u1="&#x203a;" u2="&#x221a;" k="10" />
    <hkern u1="&#x203a;" u2="&#x3a9;" k="10" />
    <hkern u1="&#x203a;" u2="z" k="20" />
    <hkern u1="&#x203a;" u2="y" k="30" />
    <hkern u1="&#x203a;" u2="x" k="45" />
    <hkern u1="&#x203a;" u2="w" k="20" />
    <hkern u1="&#x203a;" u2="v" k="30" />
    <hkern u1="&#x203a;" u2="t" k="10" />
    <hkern u1="&#x203a;" u2="s" k="10" />
    <hkern u1="&#x203a;" u2="f" k="10" />
    <hkern u1="&#x2215;" u2="&#xb1;" k="-15" />
    <hkern u1="&#x2215;" u2="&#x39;" k="-20" />
    <hkern u1="&#x2215;" u2="&#x38;" k="-20" />
    <hkern u1="&#x2215;" u2="&#x37;" k="-30" />
    <hkern u1="&#x2215;" u2="&#x35;" k="-20" />
    <hkern u1="&#x2215;" u2="&#x34;" k="75" />
    <hkern u1="&#x2215;" u2="&#x33;" k="-40" />
    <hkern u1="&#x2215;" u2="&#x32;" k="-10" />
    <hkern u1="&#x2215;" u2="&#x31;" k="-60" />
  </font>
</defs></svg>
