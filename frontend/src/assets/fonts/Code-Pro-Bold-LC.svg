<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Wed Jun 21 07:19:22 2023
 By Aleksey,,,
Copyright (c) 2011 By <PERSON><PERSON><PERSON><PERSON> (c) Fontfabric, LLC. All rights reserved.
</metadata>
<defs>
<font id="Code-Pro-Bold-LC" horiz-adv-x="1310" >
  <font-face 
    font-family="Code Pro Bold LC"
    font-weight="400"
    font-stretch="normal"
    units-per-em="2048"
    panose-1="2 0 0 0 0 0 0 0 0 0"
    ascent="1536"
    descent="-512"
    x-height="1108"
    cap-height="1434"
    bbox="-319 -518 3463 1980"
    underline-thickness="102"
    underline-position="-307"
    unicode-range="U+0020-FB04"
  />
<missing-glyph horiz-adv-x="1220" 
d="M-2 0v1434h1079v-1434h-1079zM537 946l163 242h-325q120 -177 162 -242zM688 715l133 -195v395q-71 -100 -133 -200zM254 518l131 199q-29 44 -54.5 81t-76.5 115v-395zM375 246h323l-161 239q-141 -208 -162 -239z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="1466" 
d="M485 1100h326q0 448 563 350l-10 -242q-231 52 -231 -108h229v-271h-234v-829h-321v829h-326v-829h-321v829h-166v271h170q0 448 563 350l-10 -242q-232 52 -232 -108z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="1239" 
d="M1094 1102h-609q0 149 199 112l2 259q-255 39 -388.5 -52t-133.5 -319h-170v-262h166v-840h321v840h285v-840h328v1102zM735 1327q0 52 21.5 92.5t55.5 61.5t75 31t82.5 0t75.5 -31t55.5 -61.5t21.5 -92.5q0 -50 -21.5 -89t-55.5 -59t-75 -29.5t-82.5 0t-75.5 29.5
t-55.5 59t-21.5 89z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="1236" 
d="M686 1473l-2 -259q-199 37 -199 -112h199v-262h-203v-840h-321v840h-166v262h170q0 228 133.5 319t388.5 52zM766 1436h328v-1436h-328v1436z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="1884" 
d="M1327 1217l8 241q-259 39 -391.5 -45.5t-132.5 -310.5h-326q0 158 232 106l10 242q-563 98 -563 -348h-170v-266h166v-836h321v836h326v-836h321v836h285v-836h328v1102h-608q0 57 25 87t68.5 33.5t100.5 -5.5zM1382 1327q0 52 21.5 92.5t55.5 61.5t75 31t82.5 0
t75.5 -31t55.5 -61.5t21.5 -92.5q0 -50 -21.5 -89t-55.5 -59t-75 -29.5t-82.5 0t-75.5 29.5t-55.5 59t-21.5 89z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="1884" 
d="M1413 0v1436h328v-1436h-328zM1315 1102v-266h-187v-836h-321v836h-326v-836h-321v836h-166v266h170q0 446 563 348l-10 -242q-232 52 -232 -106h326q0 226 132.5 310.5t391.5 45.5l-8 -241q-57 9 -100.5 5.5t-68.5 -33.5t-25 -87h182z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="1726" 
d="M338 526v-526h-336v1436h832v-312h-496v-286h485v-312h-485zM1569 1434v-828q0 -212 -80 -343t-260.5 -197t-478.5 -66v315q183 0 290 33t150 95t43 163v518h-342v310h678z" />
    <glyph glyph-name=".notdef" horiz-adv-x="1220" 
d="M-2 0v1434h1079v-1434h-1079zM537 946l163 242h-325q120 -177 162 -242zM688 715l133 -195v395q-71 -100 -133 -200zM254 518l131 199q-29 44 -54.5 81t-76.5 115v-395zM375 246h323l-161 239q-141 -208 -162 -239z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="682" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="514" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="569" 
d="M72 1434h342v-1041h-342v1041zM240 352q84 0 137 -50.5t53 -131.5q0 -70 -55 -126t-135 -56q-75 0 -130 56.5t-55 125.5q0 80 53.5 131t131.5 51z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="909" 
d="M63 1442h291v-584h-291v584zM444 1442h291v-584h-291v584z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="1204" 
d="M1087 1126v-315h-172l-2 -10l-20 -129h123v-314h-170l-55 -356h-320l53 356h-73l-60 -356h-321l57 356h-139v314h186l16 125v2v12h-135v315h185l51 308h325l-49 -308h72l53 308h320l-47 -308h122zM606 819h-100l-23 -155h101z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="1241" 
d="M422 1444v131h248v-133q83 -15 154 -50.5t129 -90.5t91 -137.5t33 -182.5h-352q0 72 -55 111v-218q59 -11 108.5 -25t102 -39.5t92 -59t72 -84t47.5 -113.5q10 -55 10 -100q0 -121 -61.5 -219.5t-157.5 -157t-213 -80.5v-125h-248v123q-66 12 -126.5 36.5t-117.5 66.5
t-98.5 96t-66.5 130.5t-25 165.5h354q0 -99 80 -143v215q-67 11 -120.5 26t-109.5 45t-94 70.5t-65.5 104.5t-34.5 145v-2q-10 196 110 327.5t314 166.5zM754 436q0 74 -90 103v-213q90 33 90 110zM348 1004q0 -69 76 -97v207q-76 -37 -76 -110z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1658" 
d="M709 1116q0 -146 -105.5 -248t-255.5 -102q-147 0 -252.5 102t-105.5 248q0 147 105.5 249.5t252.5 102.5q150 0 255.5 -102.5t105.5 -249.5zM1020 1436h387l-940 -1452h-387zM348 1044q33 0 51.5 23t19 50t-18.5 50t-52 23q-44 0 -63 -36.5t-0.5 -73t63.5 -36.5z
M1530 344q0 -146 -105.5 -248t-255.5 -102q-147 0 -252.5 102t-105.5 248q0 147 105.5 249.5t252.5 102.5q150 0 255.5 -102.5t105.5 -249.5zM1169 272q43 0 61.5 36.5t0.5 73t-62 36.5t-63 -36.5t-0.5 -73t63.5 -36.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1431" 
d="M911 752l291 -187l-123 -157l185 -211v-199h-301l-101 121q-74 -72 -156.5 -104.5t-201.5 -32.5q-136 0 -246 55t-176 161.5t-66 245.5q0 126 54.5 213t154.5 160q-84 123 -84 250q0 118 61.5 208.5t157 135.5t207.5 45q102 0 192 -47t147 -134t57 -194
q0 -116 -50 -195.5t-143 -142.5l78 -77zM653 371l-202 211q-90 -70 -93 -144q-3 -47 35 -88q45 -53 123.5 -48t136.5 69zM635 1087q2 47 -41.5 67.5t-87.5 -1.5t-43 -78q0 -27 16 -54t51 -67q103 70 105 133z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="458" 
d="M4 1438h299v-635h-299v635z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="888" 
d="M729 1348q-107 -55 -180 -132.5t-109 -168.5t-50.5 -176.5t-14.5 -184.5q0 -92 18 -178.5t57 -172.5t110.5 -160t168.5 -126v-371q-124 50 -228 120t-178 151t-130 170.5t-89 185t-49 188.5t-16 187q0 113 19.5 222t69 228t126 220t198.5 191.5t277 148.5v-342z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="909" 
d="M18 1348v342q130 -49 237 -119.5t181 -153.5t128.5 -174t85 -189t45 -189.5t14.5 -184.5q0 -114 -22.5 -225t-74.5 -226t-130 -214.5t-197 -188t-267 -148.5v371q97 52 168.5 126t111 160t57.5 172.5t18 178.5q0 99 -14.5 184.5t-51 176.5t-109.5 168.5t-180 132.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="839" 
d="M729 1081l-166 -51l105 -139l-224 -162l-94 145l-100 -143l-221 158l100 141l-162 49l80 254l162 -53v170h278l-2 -172q22 8 85 27.5t79 25.5z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="1161" 
d="M0 561v313h354v345h328v-345h340v-313h-340v-348h-328v348h-354z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="583" 
d="M440 242v-99l-207 -372h-307l187 471h327z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="792" 
d="M10 563v316h631v-316h-631z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="493" 
d="M176 358q84 0 137.5 -50.5t53.5 -131.5q0 -70 -55 -126t-136 -56q-75 0 -129.5 56.5t-54.5 125.5q0 80 53 131t131 51z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="948" 
d="M852 1513l-555 -1628h-338l555 1628h338z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="1302" 
d="M578 1108q-110 0 -167 -105.5t-57 -295.5q0 -174 58 -286t166 -112q74 0 126.5 54t76.5 142.5t24 201.5q0 191 -57 296t-170 105zM578 -29q-113 0 -208 39t-162.5 107.5t-114.5 161.5t-70 201.5t-23 226.5q0 122 23 232t70 203.5t115 162t162.5 107t207.5 38.5t208 -38.5
t163 -107t116 -162t71 -203.5t23 -232q0 -118 -23 -226.5t-71 -201.5t-116 -161.5t-163 -107.5t-208 -39z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="806" 
d="M645 0h-334v827l-118 -114l-236 233l457 498h231v-1444z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="1218" 
d="M352 905h-342q0 156 62.5 280.5t182.5 197.5t277 73q140 0 246.5 -41t168 -111.5t92 -155.5t30.5 -183q0 -111 -56.5 -218.5t-160.5 -193.5l-287 -225h498v-328h-1051v252l602 549q103 93 103 170q0 62 -49 105.5t-136 43.5q-74 0 -127 -60.5t-53 -154.5z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="1173" 
d="M-8 475h317q0 -106 95 -147q81 -35 175 -8q89 25 115 87q8 21 8 42q0 51 -38 93t-113 42h-213v297h213q47 0 79 35t32 79q0 60 -49 93.5t-119 35.5q-76 2 -131.5 -37t-55.5 -112h-303q0 117 43.5 210.5t116.5 151t162.5 87t187.5 28.5q75 -1 145.5 -19t132.5 -55
t108.5 -89t73.5 -125t27 -158q0 -81 -32 -153t-107 -110q186 -96 182 -315q-1 -85 -37 -164t-102.5 -144t-173 -104t-237.5 -39q-102 0 -185.5 26.5t-141.5 73t-98 110t-58.5 136t-18.5 152.5z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="1210" 
d="M-4 522l715 920h262v-828h102v-323h-102v-291h-344v291h-633v231zM428 616h207v261z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="1161" 
d="M-8 424l311 51q7 -52 39 -88.5t75 -51.5t91 -12t87.5 22t65.5 58.5t25 90.5q0 63 -36.5 107t-88.5 62.5t-112 18.5q-101 0 -252 -88l-185 82v776h942v-315h-614v-189q106 29 192 29q135 -1 245.5 -56.5t178.5 -166t68 -258.5q0 -96 -34 -186t-97.5 -164t-166.5 -119
t-230 -45q-75 0 -144.5 15t-134.5 49.5t-113.5 84.5t-78.5 125.5t-33 167.5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="1208" 
d="M332 492q0 -57 29 -99t72.5 -61.5t94 -20t94 18.5t72.5 61.5t29 100.5q0 57 -29 99.5t-72.5 61.5t-94 19t-94 -18.5t-72.5 -61t-29 -100.5zM111 829l428 605h417l-346 -488q109 1 197 -36t144 -100.5t86 -145t30 -172.5q0 -97 -34.5 -187t-100 -162t-170.5 -115.5
t-234 -43.5q-263 0 -401.5 135.5t-138.5 372.5q0 154 123 337z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="1189" 
d="M-8 1446h1067v-211l-670 -1235h-381l608 1124h-624v322z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="1198" 
d="M336 440q0 -77 60 -119t141 -40q78 2 135 44.5t57 114.5q0 71 -55 111.5t-131 42.5q-83 2 -145 -39t-62 -115zM688 1036q0 61 -47.5 95t-110.5 32q-62 -2 -106.5 -36t-44.5 -93q0 -62 45 -97.5t106 -35.5q64 0 111 35t47 100zM1022 1028q0 -70 -43 -153t-115 -117
q77 -36 126.5 -92t68 -111t18.5 -115q0 -113 -47 -204.5t-125 -148t-174.5 -86.5t-200.5 -30q-103 0 -198.5 30t-172.5 86.5t-123 148t-46 204.5q0 92 53.5 182.5t155.5 131.5q-48 21 -82 58.5t-49 80t-21 75.5t-6 62q1 107 44 193t114 138.5t158.5 80t181.5 26.5
q93 -1 178.5 -29.5t154 -81.5t109.5 -138.5t41 -190.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="1200" 
d="M721 952q0 82 -54 130t-130 51q-82 5 -144.5 -43t-62.5 -138q0 -88 62 -135.5t145 -44.5q76 3 130 50t54 130zM942 614l-428 -614h-418l346 498q-109 -1 -197 36t-143.5 100.5t-85.5 145t-30 172.5q0 97 34.5 187t99.5 162t170 115.5t234 43.5q263 0 402 -135.5
t139 -372.5q0 -155 -123 -338z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="491" 
d="M170 344q82 0 133 -48.5t51 -127.5q0 -68 -52.5 -121t-131.5 -53q-72 0 -124 54t-52 120q0 78 50.5 127t125.5 49zM170 1028q82 0 133 -49t51 -127q0 -69 -52.5 -121.5t-131.5 -52.5q-72 0 -124 53t-52 121q0 77 50.5 126.5t125.5 49.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="567" 
d="M217 1028q82 0 133 -49t51 -127q0 -69 -52.5 -121.5t-131.5 -52.5q-72 0 -124 53t-52 121q0 77 50.5 126.5t125.5 49.5zM420 143v99h-328l-170 -471h291z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="944" 
d="M-4 569v308l780 415v-366l-405 -201l405 -205v-356z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="1007" 
d="M0 721v303h858v-303h-858zM0 354v299h858v-299h-858z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="903" 
d="M756 899v-332l-758 -391v363l403 198l-403 199v369z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="1169" 
d="M311 981h-315q0 123 42.5 218.5t114 151t157.5 83.5t182 28q252 0 393 -128t141 -337q0 -71 -20 -126t-51.5 -88.5t-70 -60t-77 -47t-70 -41.5t-51.5 -51t-20 -69v-154h-347v170q0 42 8.5 78.5t20 63.5t34.5 53.5t40.5 42.5t50.5 36.5t51.5 30.5t55.5 29q28 16 44 27
t33 35.5t17 52.5q0 58 -37.5 97.5t-91 48.5t-106.5 -0.5t-90.5 -48.5t-37.5 -95zM309 143q0 46 20.5 82t54 55t73.5 28t79.5 0.5t73 -27t54 -55t20.5 -83.5q0 -45 -20.5 -80t-54 -53.5t-73.5 -27.5t-79.5 -1t-73 26.5t-54 54t-20.5 81.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1601" 
d="M797 238l88 -222q-168 -53 -312 -53q-141 0 -250.5 51t-176.5 140t-101.5 201.5t-34.5 242.5q0 66 19 178q23 123 84 235.5t152.5 203t222 144.5t278.5 54q143 0 269.5 -46t216.5 -126t142 -191t52 -237q0 -148 -40.5 -264t-108.5 -185.5t-151 -105t-175 -35.5
q-125 0 -156 96q-77 -69 -199 -69q-108 0 -186 65.5t-106 166.5t-2 206q23 94 62 172t96 140.5t137 97.5t176 35q46 0 99 -25.5t81 -67.5l18 31l123 -12q-60 65 -150.5 99t-193.5 34q-101 0 -190 -41.5t-152.5 -110t-107 -151.5t-64.5 -172q-11 -46 -14 -103
q-10 -189 93.5 -308.5t266.5 -117.5q92 0 195 50zM1208 801q0 108 -43 184l-133 -487q-14 -43 -33 -43q86 -1 147.5 105.5t61.5 240.5zM1034 500l-2 -2zM561 655v-2q-8 -54 -8 -78q0 -108 78 -108q60 0 114.5 53.5t84.5 127.5t30 140q0 56 -20 87.5t-56 31.5
q-87 1 -143.5 -72t-79.5 -180z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="1433" 
d="M1040 0l-79 211h-490l-80 -211h-375l574 1450h250l596 -1450h-396zM717 872l-107 -303l-12 -32h238l-9 24z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1302" 
d="M451 1116v-201h264q43 0 66.5 30.5t23.5 72.5q0 39 -23.5 68.5t-62.5 29.5h-268zM104 1434h664q108 0 196.5 -56t135 -141.5t47.5 -179.5q2 -78 -32.5 -150t-104.5 -121q109 -56 161 -141t52 -182q0 -88 -30 -170t-86 -148t-145 -105.5t-198 -39.5h-660v1434zM451 592
v-260h313q53 0 81.5 38t28.5 91q0 54 -29 92.5t-81 38.5h-313z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1611" 
d="M420 715q0 -98 34 -177.5t90.5 -128.5t126.5 -75t146 -25q132 2 237.5 78.5t137.5 214.5h354q-26 -184 -129.5 -326.5t-261 -219t-342.5 -76.5q-203 0 -372.5 95t-269 264t-99.5 376t99.5 377.5t269 267t372.5 96.5q137 0 263.5 -45t224 -125t162 -196t81.5 -252h-354
q-34 134 -139 208t-234 76q-76 1 -146 -24.5t-126.5 -74.5t-90.5 -129t-34 -179z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1441" 
d="M449 334h221q87 0 158 33t115 87.5t67.5 121.5t23.5 139t-23.5 140t-67.5 123.5t-115 89.5t-158 34h-221v-768zM100 1434h570q138 0 255 -40.5t199.5 -109.5t141 -161t86.5 -195.5t28 -212.5t-28 -212t-86.5 -194.5t-141 -160t-199.5 -108.5t-255 -40h-570v1434z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="1142" 
d="M455 567v-229h594v-338h-947v1446h945v-338h-592v-201h579v-340h-579z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="1120" 
d="M451 518v-518h-349v1446h928v-334h-579v-268h546v-326h-546z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="1601" 
d="M799 1112q-159 0 -265 -112.5t-106 -284.5q0 -181 105 -292.5t266 -111.5q183 0 282 133h-438v314h877q0 -204 -52 -324q-49 -133 -157 -239t-243 -162t-269 -56q-153 0 -287.5 55.5t-230.5 152.5t-151.5 234.5t-55.5 295.5t56.5 296.5t153.5 235t231 152t284 55.5
q73 0 146.5 -14t150 -42t145 -77t125 -113.5t96.5 -156.5t54 -201h-369q-18 73 -56.5 126.5t-88.5 82t-100 41t-103 12.5z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1343" 
d="M453 1434v-541h417v541h351v-1434h-351v555h-417v-555h-351v1434h351z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="991" 
d="M913 1442v-320h-237v-809h237v-313h-825v313h238v809h-238v320h825z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="983" 
d="M877 1444v-813q0 -231 -83.5 -368t-266 -200t-490.5 -63v328q190 0 296.5 30.5t150.5 94.5t44 178v491h-346v322h695z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="1347" 
d="M1270 1434v-21l-508 -692l555 -703v-18h-434l-426 555v-555h-351v1434h351v-557l397 557h416z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="1128" 
d="M453 1434v-1096h602v-338h-953v1434h351z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1519" 
d="M453 713v-713h-351v1434h279l377 -555l377 555h274v-1434h-348v709l-207 -306h-195z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1323" 
d="M451 0h-349v1434h271l495 -717v717h346v-1434h-274l-489 698v-698z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="1693" 
d="M104 715q0 207 99.5 377.5t269.5 267t373 96.5t373 -96.5t270 -267t100 -377.5q0 -153 -59 -290t-158.5 -234t-236.5 -154t-289 -57q-203 0 -373 95t-269.5 264t-99.5 376zM453 715q0 -97 32.5 -175.5t87 -127t122.5 -74.5t143 -27q76 -1 146.5 25t127.5 74.5t91 127.5
t34 177q0 99 -34 179t-91 128.5t-127.5 74t-146.5 23.5q-75 -2 -143 -28t-122.5 -75t-87 -127t-32.5 -175z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="1249" 
d="M102 1446h580q123 0 222 -45t159.5 -120t92.5 -166.5t30 -188.5q-1 -76 -21.5 -148t-62.5 -136t-100 -112t-140.5 -76t-179.5 -28h-229v-426h-351v1446zM453 756h229q77 0 119.5 51.5t42.5 120.5q0 71 -42.5 124.5t-119.5 53.5h-229v-350z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="1703" 
d="M662 600v107h372l162 -148q27 70 27 158q0 162 -113 278q-112 115 -270 115q-157 0 -271 -117v2q-114 -114 -114 -278q0 -167 114 -281v2q58 -58 130.5 -89.5t146.5 -31.5q44 0 110 17zM1632 156v-156h-307l-90 86q-186 -100 -395 -100q-201 0 -369 93.5t-266.5 261.5
t-98.5 376q0 159 60.5 297.5t161.5 233t234.5 148.5t277.5 54q150 0 284 -57.5t236 -159.5q101 -102 158 -236t57 -280q0 -211 -119 -393z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1245" 
d="M508 485h-57v-485h-349v1446h557q120 0 217 -41.5t156.5 -111t91 -155t29.5 -177.5q-3 -136 -76 -257t-205 -184l308 -454v-66h-373zM451 1110v-307h200q72 0 115 44.5t45 102.5q2 63 -40.5 111.5t-119.5 48.5h-200z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1282" 
d="M1165 981h-352q0 70 -51 108.5t-135 38.5q-83 0 -134 -33t-51 -91q0 -17 5.5 -31.5t17 -25t23 -19t30 -14t31.5 -9.5t33.5 -6.5t30 -3.5t27.5 -2.5t19 -1.5q60 -8 106 -17t105 -25t104 -40.5t88 -58.5t73 -84.5t45 -112.5q10 -55 10 -100q0 -87 -32.5 -163.5t-87.5 -132
t-127 -95.5t-153 -60t-163 -20q-73 0 -147 17t-147.5 56.5t-130 96.5t-91.5 144.5t-35 192.5h354q0 -50 19 -85t51 -51.5t63 -23.5t66 -7q93 0 150 28t57 86q0 18 -7.5 33.5t-18.5 26t-29.5 19.5t-33 13.5t-37.5 9t-35 6t-33 3.5t-24 2q-58 6 -101.5 12.5t-99.5 20t-97.5 32
t-85 49t-72.5 69.5t-49.5 95t-26.5 125v-2q-8 152 66.5 269t200.5 176t280 59q79 0 154 -17t144 -54.5t120.5 -92t82.5 -134t31 -175.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="1284" 
d="M805 0h-350v1122h-387v328h1122v-328h-385v-1122z" />
    <glyph glyph-name="U" unicode="U" 
d="M653 -20q-106 0 -203 30.5t-175.5 88t-125.5 151.5t-47 211v973h351v-971q0 -51 30 -88.5t75 -54.5t97.5 -16.5t97.5 17.5t75 54.5t30 87.5v971h350v-973q0 -94 -31 -174t-84.5 -136t-125 -95.5t-151 -58t-163.5 -17.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1398" 
d="M1393 1434v-52l-553 -1386h-254l-551 1372v66h358l238 -598l84 -277l84 277l237 598h357z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1998" 
d="M1137 1106l178 -502l274 830h350v-43l-464 -1399h-310l-180 497l-176 -497h-309l-465 1399v43h350l274 -830l181 502h297z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1400" 
d="M1346 1434v-33l-459 -674l461 -698v-29h-377l-283 436l-278 -436h-377v29l459 698l-453 666v41h369l280 -408l285 408h373z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="1435" 
d="M1403 1434v-37l-514 -817v-580h-350v578l-514 823v33h385l143 -230l162 -293l170 312l131 211h387z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="1220" 
d="M1128 1446v-256l-604 -866h613v-324h-1076v246l619 878h-619v322h1067z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="622" 
d="M485 1550v-297h-145q0 -27 -1 -348.5t-1 -775.5h145v-295h-481v1716h483z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="964" 
d="M520 -111l-547 1608h351l546 -1608h-350z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="647" 
d="M10 1247v301h482l2 -1716h-482v297h146v1118h-148z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="1169" 
d="M1032 725v-45h-346l-182 342l-180 -342h-344v41l393 721h268z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="1204" 
d="M6 -6h1057v-309h-1057v309z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="681" 
d="M4 1421v23h371l129 -227v-48h-264z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="1316" 
d="M565 1126q183 0 287 -133v115h322v-1108h-322v111q-101 -125 -291 -125q-128 0 -231.5 48.5t-168 131t-99 187t-33.5 220.5q2 116 36 215t98.5 175t168 119.5t233.5 43.5zM600 295q104 0 178 78t74 188t-74 189t-178 79q-116 0 -181 -77.5t-67 -186.5q-1 -69 25.5 -129
t85 -100.5t137.5 -40.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="1312" 
d="M668 1126q130 0 233.5 -43.5t168 -119.5t98.5 -175t36 -215q1 -116 -33.5 -220.5t-99 -187t-168 -131t-231.5 -48.5q-192 0 -293 125v-111h-320v1458h320v-450q104 118 289 118zM633 295q79 0 137.5 40.5t85 100.5t25.5 129q-2 109 -67 186.5t-181 77.5q-104 0 -179 -79
t-75 -189t75 -188t179 -78z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="1257" 
d="M823 471h320q-13 -99 -53.5 -182t-98 -139.5t-130 -95t-151 -54.5t-160 -11t-157 30.5t-142.5 73t-116.5 112.5t-78.5 155t-29 195t29 195t78.5 155t116.5 112.5t143 72.5t157.5 30t160 -10.5t151 -54t130 -96t97.5 -140.5t53 -182h-320q-20 66 -64 110.5t-97 60.5
t-110 7.5t-103.5 -39t-76.5 -88.5t-30 -133t30 -133.5t76 -89t103 -39t110 7.5t97.5 60t64.5 110z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="1316" 
d="M565 1126q183 0 287 -118v436h322v-1444h-322v111q-101 -125 -291 -125q-128 0 -231.5 48.5t-168 131t-99 187t-33.5 220.5q2 116 36 215t98.5 175t168 119.5t233.5 43.5zM600 295q104 0 178 78t74 188t-74 189t-178 79q-116 0 -181 -77.5t-67 -186.5q-1 -69 25.5 -129
t85 -100.5t137.5 -40.5z" />
    <glyph glyph-name="e" unicode="e" 
d="M1171 557q0 -65 -12 -127h-794q28 -73 93.5 -113.5t143.5 -42.5q116 0 186 76h351q-62 -177 -215.5 -271t-337.5 -91q-108 2 -206.5 39.5t-178 106t-127 178t-47.5 245.5q0 111 32.5 205t87.5 160t129 112.5t157 68.5t171 21q87 -1 168.5 -23.5t154 -69t127 -112.5
t86 -159t31.5 -203zM377 682h442q-22 80 -82.5 121t-142.5 41q-75 0 -134 -41.5t-83 -120.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="821" 
d="M-6 1096h170q-7 99 24 173.5t86.5 116.5t131.5 62.5t156.5 20t164.5 -18.5l-10 -242q-40 10 -78.5 12t-75.5 -7t-58.5 -38.5t-19.5 -78.5h230v-281h-234v-815h-321v815h-166v281z" />
    <glyph glyph-name="g" unicode="g" 
d="M559 4q-130 0 -233 41.5t-168 115t-99 171t-34 213.5q0 121 34 225t99 182.5t167.5 123.5t231.5 44q192 0 289 -123v111h321v-1045q0 -119 -31.5 -216.5t-85.5 -163t-126.5 -110.5t-152.5 -65t-167 -20q-139 0 -263 51.5t-204 152.5t-80 230h338q0 -50 60.5 -86.5
t138.5 -34.5q47 1 88 14.5t77.5 42.5t59.5 81t27 122v58q-99 -115 -287 -115zM596 813q-80 0 -138.5 -41t-84.5 -101.5t-25 -129.5q1 -110 74.5 -171t173.5 -61q101 2 175.5 64.5t74.5 171.5q0 110 -73.5 189t-176.5 79z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="1243" 
d="M49 1444h326v-447q104 127 325 127q407 -5 400 -514v-610h-326v608q0 102 -48 155.5t-118 55.5q-80 2 -148.5 -59.5t-68.5 -157.5v-602h-342v1444z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="548" 
d="M403 1108h-327v-1108h327v1108zM45 1352q0 87 55 136.5t131 51.5q80 2 140.5 -47.5t60.5 -140.5q0 -84 -55 -131.5t-131 -49.5q-80 -2 -140.5 46t-60.5 135z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="544" 
d="M401 -47v1155h-321v-1149q0 -48 -22 -80.5t-56.5 -45t-76 -16t-81.5 8.5l-12 -266q92 -17 176 -16t156 30t124.5 76.5t82.5 125.5t30 177zM49 1348q0 91 58.5 141.5t138.5 48.5q77 -2 132.5 -52t55.5 -138t-54.5 -135.5t-131.5 -49.5q-81 -2 -140 46t-59 139z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="1144" 
d="M383 709v-37l305 436h352v-16l-374 -535l411 -543v-14h-377l-317 432v-31v-401h-322v1434h322v-725z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="509" 
d="M49 1434h328v-1434h-328v1434z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1871" 
d="M74 1108h325v-111q55 69 142 99t186 28q176 -8 260 -147q54 74 158.5 112t208.5 33q81 -3 147.5 -32.5t118 -87t79.5 -154t27 -223.5v-625h-325v623q0 94 -38 138t-99 46q-70 2 -135 -45t-66 -131v-631h-14h-312v623q0 94 -38 138t-99 46q-68 2 -126 -45.5t-58 -134.5
v-627h-342v1108z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="1243" 
d="M47 1108h326v-111q101 127 327 127q404 -5 400 -514v-610h-328v608q0 106 -50 161.5t-122 55.5q-79 0 -145 -62t-66 -161v-602h-342v1108z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="1296" 
d="M1190 553q0 -142 -51 -257.5t-134 -186t-189 -108.5t-217 -38t-217 38.5t-189 109t-134 185.5t-51 257q0 143 51 258.5t134 186.5t189 109.5t217 38.5t217 -39t189 -110t134 -186.5t51 -257.5zM332 553q0 -67 23 -121.5t60.5 -88t85.5 -51.5t98 -18t98 18t85.5 51.5
t60.5 88t23 121.5q0 68 -23 123t-60.5 89t-85.5 52t-98 18t-98 -18.5t-85.5 -52t-60.5 -88.5t-23 -123z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="1331" 
d="M668 -18q-181 0 -287 120v-426h-320v1432h320v-111q101 125 291 125q128 0 231.5 -48.5t168 -131t99 -187t33.5 -220.5q-2 -116 -36 -215t-98.5 -175t-168 -119.5t-233.5 -43.5zM633 813q-104 0 -178 -78t-74 -188t74 -189t178 -79q116 0 181 78t67 186q1 69 -25.5 129
t-85 100.5t-137.5 40.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="1312" 
d="M561 -18q-105 0 -192.5 28.5t-150 79t-105.5 120t-64 151.5t-22 174t19.5 178t64.5 160.5t106 130t149.5 87t190.5 31.5q190 0 291 -125v111h321v-1432h-321v426q-106 -120 -287 -120zM596 813q-117 0 -182.5 -80.5t-65.5 -189.5q2 -108 67.5 -186t180.5 -78
q104 0 178 79t74 189t-74 188t-178 78z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="839" 
d="M383 614v-614h-322v1108h291l13 -74q102 96 241 96q56 0 125 -16l-4 -303q-53 16 -109 16q-98 -2 -166.5 -55t-68.5 -158z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="1054" 
d="M41 752q0 97 36 172t100 120.5t145 68t176 22.5q91 0 170 -23.5t141 -69t98 -119t37 -167.5h-309q-1 31 -15 50.5t-39.5 28t-47.5 11t-54 2.5q-43 0 -77.5 -18t-34.5 -58q0 -20 12.5 -33t38 -18.5t40 -7t42.5 -2.5q72 -2 137.5 -15t126.5 -41.5t105.5 -69.5t71 -103
t26.5 -138q0 -81 -26 -145.5t-69.5 -106.5t-105 -70t-128.5 -39.5t-144 -11.5q-137 0 -239 40.5t-165 132.5t-65 229h323q1 -52 46.5 -78.5t107.5 -26.5q62 -1 101.5 18.5t39.5 49.5q0 23 -12 38t-36.5 21.5t-41.5 9.5t-49 4t-37 1q-67 6 -125 19t-115 39t-97 63t-64.5 93.5
t-24.5 127.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="849" 
d="M737 -2q-101 -16 -176 -16q-193 -2 -295 94.5t-102 306.5v436h-146v277h146v211l321 28v-239h232v-277h-232v-436q0 -68 42 -98.5t108 -30.5q44 0 92 10z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="1236" 
d="M1094 0h-326v113q-102 -131 -313 -127q-404 3 -400 512v610h326v-608q0 -66 20.5 -115t53 -72.5t72 -33t79.5 5t72.5 41t53 74.5t20.5 106v602h342v-1108z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="1161" 
d="M539 471l77 283l140 354h334v-51l-420 -1061h-264l-420 1061v51h336l137 -354z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1662" 
d="M784 438l-96 -311l-47 -127h-309l-352 1059v49h329l111 -334l74 -303l174 573h231l174 -573l74 303l110 334h330v-49l-352 -1059h-309l-49 131z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="1163" 
d="M541 795l98 190l82 125h358v-33l-327 -514l331 -540v-23h-358l-80 133l-104 205l-105 -205l-78 -133h-358v23l332 540l-328 514v33h358l80 -125z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="1177" 
d="M539 473l82 281l137 354h334v-51q-526 -1333 -545 -1381h-334v50l143 364l-368 967v51h336l137 -354z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="989" 
d="M25 0v195l407 620h-403v293h848v-178l-422 -625h426v-305h-856z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="710" 
d="M565 1561v-304h-123v-323q0 -33 -5.5 -66t-18 -71t-40.5 -69t-67 -48q40 -17 68 -48.5t40.5 -69t17.5 -69.5t5 -65v-317h123v-304h-168q-134 0 -208 78.5t-74 208.5v303q0 129 -113 129v308q113 0 113 131v307q0 132 74 210.5t208 78.5h168z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="477" 
d="M2 -268v1812h328v-1812h-328z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="706" 
d="M-4 1255v301h170q133 0 206.5 -78.5t73.5 -209.5v-303q0 -131 115 -131v-308q-115 0 -115 -129v-286q0 -130 -74 -208.5t-206 -78.5h-170v303h123v301q0 73 25 144t89 108q-64 37 -89 108t-25 146v321h-123z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="1165" 
d="M854 850l188 -174q-86 -128 -184.5 -192t-214 -55t-233.5 99q-132 112 -252 -73l-187 182q81 132 182 196.5t221.5 51.5t243.5 -113q32 -28 63.5 -34t61 8.5t56 39.5t55.5 64z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="526" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="512" 
d="M12 -377h342v1041h-342v-1041zM186 705q-84 0 -137 50.5t-53 131.5q0 49 20.5 87t54 58t73.5 29t79.5 0t73 -28.5t54 -58t20.5 -87.5q0 -80 -53.5 -131t-131.5 -51z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="1607" 
d="M578 -154v148q-257 50 -423.5 249t-166.5 472t166.5 474t423.5 253v135h317v-139q224 -47 380 -209t185 -391h-354q-47 177 -211 249v-743q78 34 134.5 99.5t78.5 158.5h354q-33 -235 -188 -397t-379 -207v-152h-317zM336 715q0 -144 67 -241t175 -138v758
q-108 -43 -175 -139.5t-67 -239.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="1196" 
d="M1051 1016h-314q0 92 -70 128.5t-140 2t-70 -114.5v-168h372v-291h-372v-270h592v-303h-1047v254l119 27v292h-121v291h121v172q0 105 39 186t106.5 130t150.5 73.5t177 24.5q93 0 173 -25.5t145 -76t102 -136t37 -196.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="1478" 
d="M227 190v263h256v43h-256v270h138l-396 635v33h385l144 -230l161 -307l170 326l132 211h387v-37l-398 -631h135v-270h-251v-43h251v-263h-251v-190h-351v190h-256z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="479" 
d="M8 -233v825h334v-825h-334zM342 1513v-851h-334v851h334z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="1013" 
d="M340 795q-24 -38 18 -60t118 -35.5t91 -21.5q11 36 -17 59.5t-67 31.5t-85.5 15t-57.5 11zM8 672q-5 77 24 139.5t97 91.5q-53 28 -81 74.5t-30 99.5q-3 85 32 157t95.5 119.5t138.5 75.5t162 31q104 3 215.5 -37t196.5 -133l-170 -227q-109 88 -227 88q-81 -3 -78 -49
q0 -25 19 -36.5t65 -21.5q69 -13 120.5 -27.5t110.5 -41t97.5 -59.5t65 -85t28.5 -116q3 -63 -25.5 -114t-85.5 -77q91 -63 94 -164q3 -74 -22.5 -136.5t-69.5 -105t-102.5 -73.5t-119.5 -46t-122 -17q-126 -5 -249.5 53t-223.5 176l193 203q47 -56 129 -87.5t153 -33.5
q38 0 59.5 10.5t20.5 28.5q0 25 -26 36t-89 19q-58 7 -110.5 20.5t-104.5 36.5t-90 53.5t-63 75.5t-27 99z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="964" 
d="M180 1651q81 0 130.5 -47.5t49.5 -124.5q0 -66 -51 -118t-129 -52q-72 0 -123 49t-51 121q0 79 49.5 125.5t124.5 46.5zM631 1651q81 0 130.5 -47.5t49.5 -124.5q0 -66 -51 -118t-129 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="1642" 
d="M-4 715q0 178 65.5 321.5t172 231.5t243.5 135.5t279 47.5t279 -48t243.5 -136.5t172 -231.5t65.5 -320t-65.5 -320t-172 -231.5t-243.5 -136.5t-279 -48t-279 47.5t-243.5 135.5t-172 231.5t-65.5 321.5zM301 715q0 -113 39 -204t103 -147t146 -86t167.5 -29.5t167.5 31
t146 86.5t103 146.5t39 202.5q0 147 -67.5 254t-168.5 155t-219.5 47.5t-219.5 -49t-168.5 -155.5t-67.5 -252zM874 676h263q-9 -108 -70 -186.5t-145.5 -112.5t-181 -30.5t-178 43t-135.5 125t-54 200.5q0 88 31.5 159.5t83.5 116.5t119 70.5t137 27.5t138.5 -18.5t123 -60
t90.5 -105t41 -147.5h-260q-12 49 -52 72.5t-82.5 17.5t-76 -41.5t-33.5 -91.5q0 -45 21.5 -76.5t53 -42t65.5 -7.5t62 26.5t39 60.5z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="952" 
d="M367 1446q113 0 182 -84v72h256v-705h-256v72q-64 -80 -184 -80q-82 0 -147.5 31t-107 83.5t-63 119t-20.5 139.5q2 154 89.5 253t250.5 99zM408 967q57 0 88.5 37.5t30.5 82t-32.5 82t-86.5 37.5q-41 0 -69 -24.5t-34.5 -60t0 -70.5t34.5 -59.5t69 -24.5zM49 406v268
h758v-268h-758z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="1327" 
d="M692 299v-47h-364l-353 438l353 438h366v-43l-313 -395zM1186 299v-47h-365l-352 438l352 438h367v-43l-314 -395z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="1163" 
d="M2 580v307h1006v-633h-324v326h-682z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="811" 
d="M-6 512v334h692v-334h-692z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="1648" 
d="M733 465h-14v-156h-277v785h338q150 0 236 -89.5t86 -211.5q0 -71 -32 -133.5t-93 -102.5l197 -250h-343zM702 850v-102h101q22 0 36.5 15.5t14.5 35.5t-14.5 35.5t-36.5 15.5h-101zM-4 715q0 178 65.5 321.5t172 231.5t243.5 135.5t279 47.5t279 -48t243.5 -136.5
t172 -231.5t65.5 -320t-65.5 -320t-172 -231.5t-243.5 -136.5t-279 -48t-279 47.5t-243.5 135.5t-172 231.5t-65.5 321.5zM301 715q0 -113 39 -204t103 -147t146 -86t167.5 -29.5t167.5 31t146 86.5t103 146.5t39 202.5q0 147 -67.5 254t-168.5 155t-219.5 47.5t-219.5 -49
t-168.5 -155.5t-67.5 -252z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="815" 
d="M14 1165v297h629v-297h-629z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="817" 
d="M356 834q-134 0 -228.5 91t-94.5 220q0 128 95 219.5t228 91.5q136 0 232 -91.5t96 -219.5q0 -129 -96 -220t-232 -91zM358 1036q49 0 81 31.5t32 77.5t-32 77t-81 31t-80.5 -31t-31.5 -77t31.5 -77.5t80.5 -31.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="1161" 
d="M-18 283h1038v-283h-1038v283zM686 313h-332v273h-352v313h352v344h332v-344h338v-313h-338v-273z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="874" 
d="M276 1126h-268q-6 154 96 250.5t263 96.5q172 0 270 -92.5t98 -221.5q0 -72 -39.5 -139.5t-115.5 -120.5l-148 -96h305v-238h-733v178l397 326q52 35 52 92q0 45 -48 64q-50 20 -91 -6q-45 -28 -38 -93z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="886" 
d="M406 907h-119v223h100q27 0 43 18t16 42q0 27 -20 43t-49 18q-41 3 -73 -20.5t-32 -69.5h-256q0 64 21.5 117.5t56.5 89.5t81.5 61t95.5 36.5t98 11.5q143 0 245 -64t115 -190q6 -62 -24 -113.5t-89 -71.5q74 -25 111 -80t35 -116q-2 -128 -104 -214.5t-289 -86.5
q-115 0 -198 47.5t-122 123.5t-39 169h256q0 -52 38 -77.5t79 -25.5q39 0 67 18t29 48q2 26 -17 44.5t-56 18.5z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="636" 
d="M512 1444v-23l-236 -252h-264v48l129 227h371z" />
    <glyph glyph-name="_181" unicode="&#xb5;" horiz-adv-x="1470" 
d="M350 713v-713h-350v1434h279l376 -555l377 555h275v-1434h-349v709l-206 -306h-195z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="1351" 
d="M383 -135v680q-162 41 -258 164t-96 268q0 70 19.5 136t60.5 124.5t98.5 102.5t139.5 70t179 26h617v-1571h-332v1272h-94v-1272h-334z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="493" 
d="M180 891q84 0 137.5 -50.5t53.5 -131.5q0 -70 -55.5 -126.5t-135.5 -56.5q-75 0 -129.5 57t-54.5 126q0 80 53 131t131 51z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="659" 
d="M92 -160l-20 31l84 201h245l-32 -88q103 -16 153.5 -77.5t28.5 -164.5q-13 -62 -48.5 -106.5t-84.5 -68t-99.5 -34t-105.5 -10.5q-93 0 -166.5 40t-107.5 130l174 96q13 -28 45.5 -44.5t66.5 -14.5q77 0 88 49q7 34 -18 55t-63.5 25.5t-78 -0.5t-61.5 -19z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="655" 
d="M512 604h-293v463l-104 -70l-144 209l301 275h240v-877z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="897" 
d="M387 717q-165 0 -275 104t-110 266q0 156 110.5 260.5t272.5 104.5t273.5 -104.5t111.5 -260.5q0 -162 -110.5 -266t-272.5 -104zM387 1001q37 0 58.5 26t22 57t-20.5 57t-60 26q-53 0 -75 -41.5t0 -83t75 -41.5zM12 369v303h742v-303h-742z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" 
d="M479 252v47l312 391l-314 395v43h367l352 -438l-352 -438h-365zM-14 252v47l311 391l-313 395v43h366l352 -438l-352 -438h-364z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1794" 
d="M537 -59h-328l819 1591h342zM219 604h293v877h-240l-301 -275l144 -209l104 70v-463zM1561 377v555h-310l-452 -637l112 -186h367v-109h283v109h84v268h-84zM1286 385h-127l127 168v-168z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1843" 
d="M537 -59h-328l819 1591h342zM219 604h293v877h-240l-301 -275l144 -209l104 70v-463zM975 561h268q-8 74 49 99q45 19 90 -5q36 -21 37 -59q0 -58 -51 -92l-397 -326v-178h733v238h-305l147 96q77 53 116.5 120.5t39.5 139.5q0 129 -98.5 221t-270.5 92
q-161 0 -262.5 -96t-95.5 -250z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1943" 
d="M659 -59h-327l932 1591h342zM1714 377v555h-309l-453 -637l113 -186h367v-109h282v109h84v268h-84zM1440 385h-127l127 168v-168zM401 907h-118v223h100q27 0 43 18t16 42q0 41 -43.5 56.5t-87 -8t-43.5 -77.5h-256q0 64 21.5 117.5t56.5 89.5t81.5 61t95.5 36.5t98 11.5
q143 0 245 -64t115 -190q6 -62 -24 -113.5t-89 -71.5q74 -25 111 -80t35 -116q-2 -128 -104 -214.5t-289 -86.5q-115 0 -198 47.5t-122 123.5t-39 169h256q0 -52 38 -77.5t79 -25.5q39 0 67 18t29 48q2 25 -17.5 44t-56.5 19z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="1187" 
d="M1032 70h-315q0 -56 -37.5 -95t-91 -48.5t-106.5 -0.5t-90.5 48.5t-37.5 97.5q0 22 8 39.5t25 32.5t28.5 22.5t33.5 19.5q37 19 56 29t51.5 30.5t50 36.5t40 42.5t34 53.5t20 63.5t8.5 78.5v170h-347v-153q0 -39 -20 -69t-51.5 -51t-70 -41.5t-77 -47t-70 -60.5
t-51.5 -89t-20 -126q0 -209 141 -337t394 -128q76 0 146 17t134 54.5t111.5 92.5t75.5 137t28 181zM719 907q0 -46 -20.5 -82t-54 -55t-73.5 -28t-79.5 -0.5t-73 27t-54 55t-20.5 83.5q0 45 20.5 80t54 53.5t73.5 27.5t79.5 1t73 -26.5t54 -54t20.5 -81.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="1523" 
d="M1014 0l-80 211h-490l-79 -211h-375l573 1450h250l596 -1450h-395zM690 872l-106 -303l-13 -32h238l-8 24zM346 1808v23h371l129 -227v-48h-264z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="1523" 
d="M1014 0l-80 211h-490l-79 -211h-375l573 1450h250l596 -1450h-395zM690 872l-106 -303l-13 -32h238l-8 24zM1065 1837v-22l-236 -252h-264v47l129 227h371z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="1523" 
d="M1014 0l-80 211h-490l-79 -211h-375l573 1450h250l596 -1450h-395zM690 872l-106 -303l-13 -32h238l-8 24zM573 1864h242l283 -258v-29h-320l-90 82l-86 -82h-323v22z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="1523" 
d="M1014 0l-80 211h-490l-79 -211h-375l573 1450h250l596 -1450h-395zM690 872l-106 -303l-13 -32h238l-8 24zM911 1915l179 -156q-62 -106 -139 -154.5t-160 -39.5q-34 5 -71 23t-62 33t-52 23t-54.5 -6.5t-57.5 -56.5l-185 145q64 108 143.5 157t161 39.5t162.5 -69.5
q18 -13 35.5 -13.5t32.5 9.5t28.5 23.5t29.5 31.5q6 7 9 11z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="1523" 
d="M1014 0l-80 211h-490l-79 -211h-375l573 1450h250l596 -1450h-395zM690 872l-106 -303l-13 -32h238l-8 24zM459 1892q81 0 130.5 -47.5t49.5 -124.5q0 -66 -51 -118t-129 -52q-72 0 -123 49t-51 121q0 79 49.5 125.5t124.5 46.5zM909 1892q81 0 131 -47.5t50 -124.5
q0 -66 -51 -118t-130 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="1523" 
d="M1014 0l-80 211h-490l-79 -211h-375l573 1450h250l596 -1450h-395zM690 872l-106 -303l-13 -32h238l-8 24zM686 1413q-101 0 -172.5 60.5t-71.5 158.5q0 99 71.5 160t172.5 61q100 0 173 -61t73 -160q0 -98 -73 -158.5t-173 -60.5zM686 1683q-25 0 -42 -14t-17 -37
q0 -22 16.5 -34.5t42.5 -12.5q28 0 45 12t17 35t-17 37t-45 14z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1953" 
d="M-6 49l760 1403h1044v-330h-592v-260h580v-319h-580v-215h596v-328h-948v244h-360l-134 -244h-366v49zM670 571h184v410z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1628" 
d="M567 -201l-20 31l67 162q-129 21 -241 84.5t-193 157.5t-127.5 218.5t-46.5 262.5q0 207 99.5 377.5t269.5 267t373 96.5q137 0 263.5 -45t224 -125t162 -196t81.5 -252h-355q-34 135 -138.5 208.5t-233.5 75.5q-76 1 -146.5 -24.5t-127 -74.5t-90.5 -129t-34 -179
q0 -98 34 -177.5t90.5 -128.5t127 -75t146.5 -25q85 1 160.5 33t133.5 99.5t80 160.5h355q-36 -249 -207.5 -415t-413.5 -199l-16 -45q103 -16 153.5 -77.5t28.5 -164.5q-13 -62 -48.5 -106.5t-84.5 -68t-99.5 -34t-105.5 -10.5q-93 0 -166.5 40t-107.5 130l174 96
q13 -28 45.5 -44.5t66.5 -14.5q77 0 88 49q7 34 -18 55t-63.5 25.5t-78 -0.5t-61.5 -19z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="1122" 
d="M373 567v-229h594v-338h-947v1446h945v-338h-592v-201h579v-340h-579zM197 1800v23h370l129 -228v-47h-264z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="1122" 
d="M373 567v-229h594v-338h-947v1446h945v-338h-592v-201h579v-340h-579zM881 1829v-23l-236 -252h-264v48l129 227h371z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="1122" 
d="M373 567v-229h594v-338h-947v1446h945v-338h-592v-201h579v-340h-579zM381 1858h242l282 -259v-28h-319l-90 82l-86 -82h-324v22z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="1122" 
d="M373 567v-229h594v-338h-947v1446h945v-338h-592v-201h579v-340h-579zM293 1905q81 0 130.5 -47.5t49.5 -124.5q0 -66 -51 -118t-129 -52q-72 0 -123 49t-51 121q0 79 49.5 125.5t124.5 46.5zM743 1905q81 0 131 -47.5t50 -124.5q0 -66 -51 -118t-130 -52
q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="1009" 
d="M852 1442v-320h-238v-809h238v-313h-825v313h237v809h-237v320h825zM106 1788v22h371l129 -227v-47h-264z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="1009" 
d="M852 1442v-320h-238v-809h238v-313h-825v313h237v809h-237v320h825zM795 1833v-23l-236 -251h-264v47l129 227h371z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="1009" 
d="M852 1442v-320h-238v-809h238v-313h-825v313h237v809h-237v320h825zM338 1849h242l282 -258v-28h-319l-90 82l-86 -82h-324v22z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="1009" 
d="M852 1442v-320h-238v-809h238v-313h-825v313h237v809h-237v320h825zM215 1876q81 0 130.5 -47.5t49.5 -124.5q0 -66 -51 -118t-129 -52q-72 0 -123 49t-51 121q0 79 49.5 125.5t124.5 46.5zM666 1876q81 0 130.5 -47.5t49.5 -124.5q0 -66 -51 -118t-129 -52
q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1499" 
d="M8 553v315h92v580h541q137 0 255 -42t200.5 -113t141 -165.5t86.5 -199t28 -213.5t-28 -212t-86.5 -194.5t-141.5 -160t-200 -108.5t-255 -40h-541v553h-92zM449 334h192q87 0 158.5 33t115.5 87.5t67.5 121.5t23.5 139t-23.5 142t-67.5 128.5t-115.5 94.5t-158.5 36
h-192v-248h147v-315h-147v-219z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1296" 
d="M369 0h-349v1434h271l495 -717v717h347v-1434h-275l-489 698v-698zM764 1894l178 -155q-62 -106 -139 -155t-160 -40q-34 5 -71 23t-62 33t-52 23.5t-54.5 -6t-57.5 -56.5l-184 145q64 108 143.5 157t161 39.5t162.5 -69.5q18 -13 34.5 -15t35 11.5t27 21.5t29.5 33
q6 7 9 10z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="1642" 
d="M10 715q0 207 99.5 377.5t269.5 267t373 96.5t373 -96.5t270 -267t100 -377.5q0 -153 -59 -290t-158.5 -234t-236.5 -154t-289 -57q-203 0 -373 95t-269.5 264t-99.5 376zM358 715q0 -97 32.5 -175.5t87 -127t122.5 -74.5t143 -27q76 -1 146.5 25t127.5 74.5t91.5 127.5
t34.5 177q0 99 -34.5 179t-91.5 128.5t-127.5 74t-146.5 23.5q-75 -2 -143 -28t-122.5 -75t-87 -127t-32.5 -175zM428 1827v22h371l129 -227v-47h-264z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="1642" 
d="M10 715q0 207 99.5 377.5t269.5 267t373 96.5t373 -96.5t270 -267t100 -377.5q0 -153 -59 -290t-158.5 -234t-236.5 -154t-289 -57q-203 0 -373 95t-269.5 264t-99.5 376zM358 715q0 -97 32.5 -175.5t87 -127t122.5 -74.5t143 -27q76 -1 146.5 25t127.5 74.5t91.5 127.5
t34.5 177q0 99 -34.5 179t-91.5 128.5t-127.5 74t-146.5 23.5q-75 -2 -143 -28t-122.5 -75t-87 -127t-32.5 -175zM1137 1839v-22l-236 -252h-264v47l129 227h371z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="1642" 
d="M10 715q0 207 99.5 377.5t269.5 267t373 96.5t373 -96.5t270 -267t100 -377.5q0 -153 -59 -290t-158.5 -234t-236.5 -154t-289 -57q-203 0 -373 95t-269.5 264t-99.5 376zM358 715q0 -97 32.5 -175.5t87 -127t122.5 -74.5t143 -27q76 -1 146.5 25t127.5 74.5t91.5 127.5
t34.5 177q0 99 -34.5 179t-91.5 128.5t-127.5 74t-146.5 23.5q-75 -2 -143 -28t-122.5 -75t-87 -127t-32.5 -175zM653 1864h242l283 -258v-29h-320l-90 82l-86 -82h-324v22z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="1642" 
d="M10 715q0 207 99.5 377.5t269.5 267t373 96.5t373 -96.5t270 -267t100 -377.5q0 -153 -59 -290t-158.5 -234t-236.5 -154t-289 -57q-203 0 -373 95t-269.5 264t-99.5 376zM358 715q0 -97 32.5 -175.5t87 -127t122.5 -74.5t143 -27q76 -1 146.5 25t127.5 74.5t91.5 127.5
t34.5 177q0 99 -34.5 179t-91.5 128.5t-127.5 74t-146.5 23.5q-75 -2 -143 -28t-122.5 -75t-87 -127t-32.5 -175zM967 1923l178 -156q-62 -106 -139 -154.5t-160 -39.5q-34 5 -71 23t-62 33t-52 23t-54.5 -6.5t-57.5 -56.5l-184 146q64 108 143 157t160.5 39.5t162.5 -69.5
q18 -13 35 -14.5t35.5 11.5t28 22.5t29.5 32.5q5 6 8 9z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="1642" 
d="M10 715q0 207 99.5 377.5t269.5 267t373 96.5t373 -96.5t270 -267t100 -377.5q0 -153 -59 -290t-158.5 -234t-236.5 -154t-289 -57q-203 0 -373 95t-269.5 264t-99.5 376zM358 715q0 -97 32.5 -175.5t87 -127t122.5 -74.5t143 -27q76 -1 146.5 25t127.5 74.5t91.5 127.5
t34.5 177q0 99 -34.5 179t-91.5 128.5t-127.5 74t-146.5 23.5q-75 -2 -143 -28t-122.5 -75t-87 -127t-32.5 -175zM522 1884q81 0 130.5 -47.5t49.5 -124.5q0 -66 -51 -118t-129 -52q-72 0 -123 49t-51 121q0 79 49.5 125.5t124.5 46.5zM973 1884q81 0 130.5 -47.5
t49.5 -124.5q0 -66 -51 -118t-129 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="1103" 
d="M10 872l230 230l249 -252l244 244l228 -226l-246 -243l241 -242l-227 -227l-242 239l-245 -245l-228 227l248 246z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="1642" 
d="M10 715q0 207 99.5 377.5t269.5 267t373 96.5q122 0 241 -39l97 158h337l-184 -301q118 -104 185 -248t67 -311q0 -153 -59 -290t-158.5 -234t-236.5 -154t-289 -57q-143 0 -267 47l-102 -168h-338l195 319q-108 101 -169 239.5t-61 297.5zM1143 715q0 157 -82 258
l-397 -654q33 -8 79 -8q76 -1 146.5 25t127.5 74.5t91.5 127.5t34.5 177zM358 715q0 -136 64 -236l387 637q-40 4 -66 4q-75 -2 -143 -28t-122.5 -75t-87 -127t-32.5 -175z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1290" 
d="M571 -20q-106 0 -203 30.5t-175.5 88t-125.5 151.5t-47 211v973h351v-971q0 -51 30 -88.5t75 -54.5t97.5 -16.5t97.5 17.5t75 54.5t30 87.5v971h350v-973q0 -94 -31 -174t-84.5 -136t-125 -95.5t-151 -58t-163.5 -17.5zM231 1800v23h371l129 -228v-47h-264z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1290" 
d="M571 -20q-106 0 -203 30.5t-175.5 88t-125.5 151.5t-47 211v973h351v-971q0 -51 30 -88.5t75 -54.5t97.5 -16.5t97.5 17.5t75 54.5t30 87.5v971h350v-973q0 -94 -31 -174t-84.5 -136t-125 -95.5t-151 -58t-163.5 -17.5zM934 1841v-22l-236 -252h-264v47l129 227h371z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1290" 
d="M571 -20q-106 0 -203 30.5t-175.5 88t-125.5 151.5t-47 211v973h351v-971q0 -51 30 -88.5t75 -54.5t97.5 -16.5t97.5 17.5t75 54.5t30 87.5v971h350v-973q0 -94 -31 -174t-84.5 -136t-125 -95.5t-151 -58t-163.5 -17.5zM463 1864h242l282 -258v-29h-319l-90 82l-86 -82
h-324v22z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1290" 
d="M571 -20q-106 0 -203 30.5t-175.5 88t-125.5 151.5t-47 211v973h351v-971q0 -51 30 -88.5t75 -54.5t97.5 -16.5t97.5 17.5t75 54.5t30 87.5v971h350v-973q0 -94 -31 -174t-84.5 -136t-125 -95.5t-151 -58t-163.5 -17.5zM348 1878q81 0 130.5 -47.5t49.5 -124.5
q0 -66 -51 -118t-129 -52q-72 0 -123 49t-51 121q0 79 49.5 125.5t124.5 46.5zM799 1878q81 0 130.5 -47.5t49.5 -124.5q0 -66 -51 -118t-129 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="1501" 
d="M1368 1434v-37l-514 -817v-580h-350v578l-514 823v33h385l143 -230l162 -293l170 312l131 211h387zM1028 1831v-23l-235 -252h-265v48l129 227h371z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="1189" 
d="M6 0v1434h350v-215h201q119 0 216 -43.5t157.5 -115.5t92.5 -161.5t32 -185.5t-32 -185t-92.5 -160.5t-157.5 -114t-216 -42.5h-201v-211h-350zM356 537h201q51 0 89 26t55 65t17.5 84t-16.5 84t-55 65t-90 26h-201v-350z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="1183" 
d="M365 1038v-1038h-365v1038q0 127 69.5 224.5t179.5 147.5t240 50t239 -48t177 -144.5t68 -223.5q0 -67 -39.5 -143.5t-106.5 -107.5q232 -93 228 -340q-1 -102 -35 -187t-93.5 -141.5t-140 -92.5t-173 -39.5t-193.5 17.5v312q34 -16 80.5 -17.5t89.5 12t72.5 50
t29.5 88.5q0 62 -51 106.5t-139 44.5h-86v330h86q54 0 83 31.5t29 74.5q0 37 -25.5 62.5t-62 31.5t-73.5 0t-62.5 -32.5t-25.5 -65.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="1316" 
d="M565 1126q183 0 287 -133v115h322v-1108h-322v111q-101 -125 -291 -125q-128 0 -231.5 48.5t-168 131t-99 187t-33.5 220.5q2 116 36 215t98.5 175t168 119.5t233.5 43.5zM600 295q104 0 178 78t74 188t-74 189t-178 79q-116 0 -181 -77.5t-67 -186.5q-1 -69 25.5 -129
t85 -100.5t137.5 -40.5zM328 1509v23h370l129 -227v-48h-264z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="1316" 
d="M565 1126q183 0 287 -133v115h322v-1108h-322v111q-101 -125 -291 -125q-128 0 -231.5 48.5t-168 131t-99 187t-33.5 220.5q2 116 36 215t98.5 175t168 119.5t233.5 43.5zM600 295q104 0 178 78t74 188t-74 189t-178 79q-116 0 -181 -77.5t-67 -186.5q-1 -69 25.5 -129
t85 -100.5t137.5 -40.5zM995 1544v-22l-235 -252h-264v47l129 227h370z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="1316" 
d="M565 1126q183 0 287 -133v115h322v-1108h-322v111q-101 -125 -291 -125q-128 0 -231.5 48.5t-168 131t-99 187t-33.5 220.5q2 116 36 215t98.5 175t168 119.5t233.5 43.5zM600 295q104 0 178 78t74 188t-74 189t-178 79q-116 0 -181 -77.5t-67 -186.5q-1 -69 25.5 -129
t85 -100.5t137.5 -40.5zM549 1544h242l282 -258v-29h-319l-90 82l-86 -82h-324v23z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="1316" 
d="M565 1126q183 0 287 -133v115h322v-1108h-322v111q-101 -125 -291 -125q-128 0 -231.5 48.5t-168 131t-99 187t-33.5 220.5q2 116 36 215t98.5 175t168 119.5t233.5 43.5zM600 295q104 0 178 78t74 188t-74 189t-178 79q-116 0 -181 -77.5t-67 -186.5q-1 -69 25.5 -129
t85 -100.5t137.5 -40.5zM858 1593l178 -155q-62 -106 -139 -155t-160 -40q-34 5 -71 23t-62 33t-52 23.5t-54.5 -6t-57.5 -56.5l-184 145q64 108 143.5 157t161 39.5t162.5 -69.5q18 -13 34.5 -15t35 11.5t27 21.5t29.5 33q6 7 9 10z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="1316" 
d="M565 1126q183 0 287 -133v115h322v-1108h-322v111q-101 -125 -291 -125q-128 0 -231.5 48.5t-168 131t-99 187t-33.5 220.5q2 116 36 215t98.5 175t168 119.5t233.5 43.5zM600 295q104 0 178 78t74 188t-74 189t-178 79q-116 0 -181 -77.5t-67 -186.5q-1 -69 25.5 -129
t85 -100.5t137.5 -40.5zM451 1593q81 0 130.5 -47.5t49.5 -124.5q0 -66 -51 -118t-129 -52q-72 0 -123.5 49t-51.5 121q0 79 50 125.5t125 46.5zM901 1593q81 0 130.5 -47.5t49.5 -124.5q0 -66 -51 -118t-129 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5z
" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="1316" 
d="M643 1200q-101 0 -172.5 60.5t-71.5 158.5q0 99 71.5 160t172.5 61q100 0 173 -61t73 -160q0 -98 -73 -158.5t-173 -60.5zM643 1503q-42 0 -70 -24t-28 -62q0 -37 27.5 -57.5t70.5 -20.5q47 0 74.5 20.5t27.5 57.5q0 38 -28 62t-74 24zM565 1126q183 0 287 -133v115h322
v-1108h-322v111q-101 -125 -291 -125q-128 0 -231.5 48.5t-168 131t-99 187t-33.5 220.5q2 116 36 215t98.5 175t168 119.5t233.5 43.5zM600 295q104 0 178 78t74 188t-74 189t-178 79q-116 0 -181 -77.5t-67 -186.5q-1 -69 25.5 -129t85 -100.5t137.5 -40.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="2109" 
d="M1970 557q0 -65 -12 -127h-787q26 -89 105.5 -130t167.5 -25.5t143 75.5h350q-45 -130 -143 -220t-214.5 -124.5t-243.5 -18.5t-232 85v-72h-252v111q-101 -125 -291 -125q-128 0 -231.5 48.5t-168 131t-99 187t-33.5 220.5q2 116 36 215t98.5 175t168 119.5t233.5 43.5
q183 0 287 -133v115h252v-80q74 53 168 75.5t189.5 15t187.5 -49t163 -109t114.5 -172.5t43.5 -231zM600 295q104 0 179 78t75 188t-75 189t-179 79q-116 0 -181 -77.5t-67 -186.5q-1 -69 25.5 -129t85 -100.5t137.5 -40.5zM1171 682h447q-22 80 -82.5 121t-142.5 41
q-76 0 -137 -41t-85 -121z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="1257" 
d="M430 -188l-20 30l65 156q-92 16 -171.5 60t-142 112t-98.5 167t-36 218q0 105 29 195t78.5 155t116.5 112.5t143 72.5t157.5 30t160 -10.5t151 -54t130 -96t97.5 -140.5t53 -182h-320q-20 66 -64 110.5t-97 60.5t-110 7.5t-103.5 -39t-76.5 -88.5t-30 -133t30 -133.5
t76 -89t103 -39t110 7.5t97.5 60t64.5 110h320q-26 -190 -141 -310t-277 -157l-18 -49q103 -16 153.5 -77.5t28.5 -164.5q-13 -62 -48.5 -106.5t-84.5 -68t-99.5 -34t-105.5 -10.5q-93 0 -167 40t-108 130l175 96q13 -28 45.5 -44.5t66.5 -14.5q77 0 88 49q9 44 -35 66
t-100 18.5t-86 -22.5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" 
d="M1171 557q0 -65 -12 -127h-794q28 -73 93.5 -113.5t143.5 -42.5q116 0 186 76h351q-62 -177 -215.5 -271t-337.5 -91q-108 2 -206.5 39.5t-178 106t-127 178t-47.5 245.5q0 111 32.5 205t87.5 160t129 112.5t157 68.5t171 21q87 -1 168.5 -23.5t154 -69t127 -112.5
t86 -159t31.5 -203zM377 682h442q-22 80 -82.5 121t-142.5 41q-75 0 -134 -41.5t-83 -120.5zM293 1516v22h371l129 -227v-47h-265z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" 
d="M1171 557q0 -65 -12 -127h-794q28 -73 93.5 -113.5t143.5 -42.5q116 0 186 76h351q-62 -177 -215.5 -271t-337.5 -91q-108 2 -206.5 39.5t-178 106t-127 178t-47.5 245.5q0 111 32.5 205t87.5 160t129 112.5t157 68.5t171 21q87 -1 168.5 -23.5t154 -69t127 -112.5
t86 -159t31.5 -203zM377 682h442q-22 80 -82.5 121t-142.5 41q-75 0 -134 -41.5t-83 -120.5zM950 1530v-23l-235 -252h-264v48l129 227h370z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" 
d="M1171 557q0 -65 -12 -127h-794q28 -73 93.5 -113.5t143.5 -42.5q116 0 186 76h351q-62 -177 -215.5 -271t-337.5 -91q-108 2 -206.5 39.5t-178 106t-127 178t-47.5 245.5q0 111 32.5 205t87.5 160t129 112.5t157 68.5t171 21q87 -1 168.5 -23.5t154 -69t127 -112.5
t86 -159t31.5 -203zM377 682h442q-22 80 -82.5 121t-142.5 41q-75 0 -134 -41.5t-83 -120.5zM483 1534h242l283 -258v-29h-320l-90 82l-86 -82h-324v23z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" 
d="M1171 557q0 -65 -12 -127h-794q28 -73 93.5 -113.5t143.5 -42.5q116 0 186 76h351q-62 -177 -215.5 -271t-337.5 -91q-108 2 -206.5 39.5t-178 106t-127 178t-47.5 245.5q0 111 32.5 205t87.5 160t129 112.5t157 68.5t171 21q87 -1 168.5 -23.5t154 -69t127 -112.5
t86 -159t31.5 -203zM377 682h442q-22 80 -82.5 121t-142.5 41q-75 0 -134 -41.5t-83 -120.5zM373 1563q81 0 130.5 -47.5t49.5 -124.5q0 -66 -51 -118t-129 -52q-72 0 -123 49t-51 121q0 79 49.5 125.5t124.5 46.5zM823 1563q81 0 131 -47.5t50 -124.5q0 -66 -51 -118
t-130 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="548" 
d="M74 1108h327l2 -1108h-327zM-94 1516v22h370l130 -227v-47h-265z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="548" 
d="M74 1108h327l2 -1108h-327zM578 1544v-22l-236 -252h-264v47l129 227h371z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="548" 
d="M74 1108h327l2 -1108h-327zM141 1559h242l283 -259v-28h-320l-90 82l-86 -82h-324v22z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="548" 
d="M74 1108h327l2 -1108h-327zM14 1561q81 0 131 -47.5t50 -124.5q0 -66 -51 -118t-130 -52q-72 0 -123 49t-51 121q0 79 49.5 125.5t124.5 46.5zM465 1561q81 0 130.5 -47.5t49.5 -124.5q0 -66 -51 -118t-129 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5z
" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="1296" 
d="M1171 553q0 -111 -32 -206t-87.5 -161.5t-129 -113.5t-156.5 -69t-170 -21t-169 23.5t-154.5 69.5t-127 113.5t-86.5 160.5t-32 204q0 123 44 224t119 166t168 102.5t197 44.5l-51 67l-187 -35l-2 4v162l82 17l-102 141h383l53 -78l174 35l2 -4v-162l-71 -14l239 -334
q96 -156 96 -336zM348 553q0 -126 75.5 -196t178.5 -68q101 2 174.5 71.5t73.5 192.5q0 117 -73 182.5t-175 67.5q-103 2 -178.5 -64.5t-75.5 -185.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="1243" 
d="M47 1108h326v-111q101 127 327 127q404 -5 400 -514v-610h-328v608q0 106 -50 161.5t-122 55.5q-79 0 -145 -62t-66 -161v-602h-342v1108zM786 1595l179 -155q-62 -106 -139 -155t-160 -40q-34 5 -71 23t-62 33t-52 23.5t-54.5 -6t-57.5 -56.5l-185 145q64 108 143.5 157
t161 39.5t162.5 -69.5q18 -13 34.5 -15t35 11.5t27 21.5t29.5 33q6 7 9 10z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="1296" 
d="M1190 553q0 -142 -51 -257.5t-134 -186t-189 -108.5t-217 -38t-217 38.5t-189 109t-134 185.5t-51 257q0 143 51 258.5t134 186.5t189 109.5t217 38.5t217 -39t189 -110t134 -186.5t51 -257.5zM332 553q0 -67 23 -121.5t60.5 -88t85.5 -51.5t98 -18t98 18t85.5 51.5
t60.5 88t23 121.5q0 68 -23 123t-60.5 89t-85.5 52t-98 18t-98 -18.5t-85.5 -52t-60.5 -88.5t-23 -123zM295 1526v22h371l129 -227v-47h-265z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="1296" 
d="M1190 553q0 -142 -51 -257.5t-134 -186t-189 -108.5t-217 -38t-217 38.5t-189 109t-134 185.5t-51 257q0 143 51 258.5t134 186.5t189 109.5t217 38.5t217 -39t189 -110t134 -186.5t51 -257.5zM332 553q0 -67 23 -121.5t60.5 -88t85.5 -51.5t98 -18t98 18t85.5 51.5
t60.5 88t23 121.5q0 68 -23 123t-60.5 89t-85.5 52t-98 18t-98 -18.5t-85.5 -52t-60.5 -88.5t-23 -123zM958 1548v-22l-235 -252h-264v47l129 227h370z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="1296" 
d="M1190 553q0 -142 -51 -257.5t-134 -186t-189 -108.5t-217 -38t-217 38.5t-189 109t-134 185.5t-51 257q0 143 51 258.5t134 186.5t189 109.5t217 38.5t217 -39t189 -110t134 -186.5t51 -257.5zM332 553q0 -67 23 -121.5t60.5 -88t85.5 -51.5t98 -18t98 18t85.5 51.5
t60.5 88t23 121.5q0 68 -23 123t-60.5 89t-85.5 52t-98 18t-98 -18.5t-85.5 -52t-60.5 -88.5t-23 -123zM502 1552h241l283 -258v-28h-319l-91 82l-86 -82h-323v22z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="1296" 
d="M1190 553q0 -142 -51 -257.5t-134 -186t-189 -108.5t-217 -38t-217 38.5t-189 109t-134 185.5t-51 257q0 143 51 258.5t134 186.5t189 109.5t217 38.5t217 -39t189 -110t134 -186.5t51 -257.5zM332 553q0 -67 23 -121.5t60.5 -88t85.5 -51.5t98 -18t98 18t85.5 51.5
t60.5 88t23 121.5q0 68 -23 123t-60.5 89t-85.5 52t-98 18t-98 -18.5t-85.5 -52t-60.5 -88.5t-23 -123zM829 1608l179 -156q-62 -106 -139 -155t-160 -40q-34 5 -71 23t-62 33t-52 23.5t-54.5 -6t-57.5 -56.5l-185 145q64 108 143.5 157t161 39.5t162.5 -69.5
q18 -13 35.5 -13.5t32.5 9.5t28.5 23.5t29.5 31.5q6 7 9 11z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="1296" 
d="M1190 553q0 -142 -51 -257.5t-134 -186t-189 -108.5t-217 -38t-217 38.5t-189 109t-134 185.5t-51 257q0 143 51 258.5t134 186.5t189 109.5t217 38.5t217 -39t189 -110t134 -186.5t51 -257.5zM332 553q0 -67 23 -121.5t60.5 -88t85.5 -51.5t98 -18t98 18t85.5 51.5
t60.5 88t23 121.5q0 68 -23 123t-60.5 89t-85.5 52t-98 18t-98 -18.5t-85.5 -52t-60.5 -88.5t-23 -123zM381 1585q81 0 130.5 -47.5t49.5 -124.5q0 -66 -51 -118t-129 -52q-72 0 -123 49t-51 121q0 79 49.5 125.5t124.5 46.5zM831 1585q81 0 131 -47.5t50 -124.5
q0 -66 -51 -118t-130 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="1114" 
d="M0 557v313h987v-313h-987zM481 436q83 0 136 -51t53 -133q0 -69 -54.5 -124.5t-134.5 -55.5q-75 0 -129.5 56t-54.5 124q0 81 53 132.5t131 51.5zM479 1323q84 0 136.5 -50.5t52.5 -131.5q0 -57 -27.5 -98.5t-69 -60t-90 -18.5t-90 18.5t-69 60.5t-27.5 98q0 80 53 131
t131 51z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="1296" 
d="M1190 553q0 -98 -24 -183.5t-67 -150t-100.5 -115t-125.5 -81t-142 -46t-150 -13t-149 21.5l-74 -136h-307l144 259q-187 162 -187 444q0 98 24 184t67 150.5t100.5 115t125.5 81.5t142 46.5t150 13t149 -21.5l74 133h307l-146 -258q189 -164 189 -444zM332 553
q0 -79 33 -143l237 424q-109 2 -189.5 -73t-80.5 -208zM866 553q0 81 -32 143l-238 -422q108 -2 189 72t81 207z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="1236" 
d="M1094 0h-326v113q-102 -131 -313 -127q-404 3 -400 512v610h326v-608q0 -66 20.5 -115t53 -72.5t72 -33t79.5 5t72.5 41t53 74.5t20.5 106v602h342v-1108zM231 1511v23h371l129 -227v-47h-264z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="1236" 
d="M1094 0h-326v113q-102 -131 -313 -127q-404 3 -400 512v610h326v-608q0 -66 20.5 -115t53 -72.5t72 -33t79.5 5t72.5 41t53 74.5t20.5 106v602h342v-1108zM891 1538v-22l-236 -252h-264v47l129 227h371z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="1236" 
d="M1094 0h-326v113q-102 -131 -313 -127q-404 3 -400 512v610h326v-608q0 -66 20.5 -115t53 -72.5t72 -33t79.5 5t72.5 41t53 74.5t20.5 106v602h342v-1108zM440 1548h242l283 -258v-28h-320l-90 81l-86 -81h-324v22z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="1236" 
d="M330 1577q81 0 130.5 -47.5t49.5 -124.5q0 -66 -51 -118t-129 -52q-72 0 -123 49t-51 121q0 79 49.5 125.5t124.5 46.5zM846 1577q81 0 130.5 -47.5t49.5 -124.5q0 -66 -51 -118t-129 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5zM1094 0h-326v113
q-102 -131 -313 -127q-404 3 -400 512v610h326v-608q0 -66 20.5 -115t53 -72.5t72 -33t79.5 5t72.5 41t53 74.5t20.5 106v602h342v-1108z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="1177" 
d="M539 473l82 281l137 354h334v-51q-526 -1333 -545 -1381h-334v50l143 364l-368 967v51h336l137 -354zM866 1559v-23l-235 -252h-264v47l129 228h370z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="1425" 
d="M729 -18q-181 0 -287 120v-426h-319v1762h319v-441q101 125 291 125q128 0 231.5 -48.5t168.5 -131t99.5 -187t33.5 -220.5q-2 -116 -36 -215t-99 -175t-168 -119.5t-234 -43.5zM694 813q-104 0 -178 -78t-74 -188t74 -189t178 -79q116 0 181 78t67 186q1 69 -25.5 129
t-85 100.5t-137.5 40.5z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="1177" 
d="M268 1583q81 0 131 -47.5t50 -124.5q0 -66 -51 -118t-130 -52q-72 0 -123 49t-51 121q0 79 49.5 125.5t124.5 46.5zM827 1583q81 0 131 -47.5t50 -124.5q0 -66 -51 -118t-130 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5zM539 473l82 281l137 354h334v-51
q-526 -1333 -545 -1381h-334v50l143 364l-368 967v51h336l137 -354z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="1523" 
d="M1014 0l-80 211h-490l-79 -211h-375l573 1450h250l596 -1450h-395zM690 872l-106 -303l-13 -32h238l-8 24zM371 1573v256h624v-256h-624z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="1316" 
d="M565 1126q183 0 287 -133v115h322v-1108h-322v111q-101 -125 -291 -125q-128 0 -231.5 48.5t-168 131t-99 187t-33.5 220.5q2 116 36 215t98.5 175t168 119.5t233.5 43.5zM600 295q104 0 178 78t74 188t-74 189t-178 79q-116 0 -181 -77.5t-67 -186.5q-1 -69 25.5 -129
t85 -100.5t137.5 -40.5zM342 1264v274h625v-274h-625z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="1523" 
d="M1014 0l-80 211h-490l-79 -211h-375l573 1450h250l596 -1450h-395zM690 872l-106 -303l-13 -32h238l-8 24zM766 1841h262q0 -94 -49 -164t-122 -102.5t-158.5 -33t-158.5 30t-122 101.5t-49 168h252q1 -41 37.5 -57t72.5 1t35 56z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="1316" 
d="M565 1126q183 0 287 -133v115h322v-1108h-322v111q-101 -125 -291 -125q-128 0 -231.5 48.5t-168 131t-99 187t-33.5 220.5q2 116 36 215t98.5 175t168 119.5t233.5 43.5zM600 295q104 0 178 78t74 188t-74 189t-178 79q-116 0 -181 -77.5t-67 -186.5q-1 -69 25.5 -129
t85 -100.5t137.5 -40.5zM705 1559h262q0 -71 -28.5 -129t-75 -94.5t-105.5 -56.5t-121 -21t-121 18t-105.5 55t-75 95t-28.5 133h252q1 -25 16 -41.5t37 -20t43 1t36 21t14 39.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="1509" 
d="M993 0l-80 211h-489l-80 -211h-375l574 1450h250l591 -1440l5 -10q-51 -9 -71.5 -47t1 -75t76.5 -44l-127 -188q-71 -12 -132.5 5t-99 56t-54.5 87.5t-2.5 104t60.5 101.5h-47zM670 872l-107 -303l-12 -32h237l-8 24z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="1314" 
d="M563 1126q183 0 287 -133v115h321v-1108h-2l2 -2q-51 -9 -71.5 -47t1.5 -75t77 -44l-127 -188q-71 -12 -134.5 5t-102 55.5t-54.5 87t7 104.5t83 102v113q-101 -125 -291 -125q-128 0 -231.5 48.5t-168 131t-99 187t-33.5 220.5q2 116 36 215t98.5 175t168 119.5
t233.5 43.5zM598 295q104 0 178 78t74 188t-74 189t-178 79q-116 0 -181 -77.5t-67 -186.5q-1 -69 25.5 -129t85 -100.5t137.5 -40.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="1628" 
d="M356 715q0 -98 34 -177.5t90.5 -128.5t127 -75t146.5 -25q85 1 160.5 33t133.5 99.5t80 160.5h355q-26 -184 -129.5 -326.5t-261 -219t-342.5 -76.5q-203 0 -373 95t-269.5 264t-99.5 376t99.5 377.5t269.5 267t373 96.5q137 0 263.5 -45t224 -125t162 -196t81.5 -252
h-355q-34 135 -138.5 208.5t-233.5 75.5q-76 1 -146.5 -24.5t-127 -74.5t-90.5 -129t-34 -179zM1104 1827v-23l-236 -252h-264v47l129 228h371z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="1257" 
d="M823 471h320q-13 -99 -53.5 -182t-98 -139.5t-130 -95t-151 -54.5t-160 -11t-157 30.5t-142.5 73t-116.5 112.5t-78.5 155t-29 195t29 195t78.5 155t116.5 112.5t143 72.5t157.5 30t160 -10.5t151 -54t130 -96t97.5 -140.5t53 -182h-320q-20 66 -64 110.5t-97 60.5
t-110 7.5t-103.5 -39t-76.5 -88.5t-30 -133t30 -133.5t76 -89t103 -39t110 7.5t97.5 60t64.5 110zM930 1538v-22l-236 -252h-264v47l129 227h371z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="1628" 
d="M356 715q0 -98 34 -177.5t90.5 -128.5t127 -75t146.5 -25q85 1 160.5 33t133.5 99.5t80 160.5h355q-26 -184 -129.5 -326.5t-261 -219t-342.5 -76.5q-203 0 -373 95t-269.5 264t-99.5 376t99.5 377.5t269.5 267t373 96.5q137 0 263.5 -45t224 -125t162 -196t81.5 -252
h-355q-34 135 -138.5 208.5t-233.5 75.5q-76 1 -146.5 -24.5t-127 -74.5t-90.5 -129t-34 -179zM629 1864h241l283 -258v-29h-319l-91 82l-86 -82h-323v22z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="1257" 
d="M823 471h320q-13 -99 -53.5 -182t-98 -139.5t-130 -95t-151 -54.5t-160 -11t-157 30.5t-142.5 73t-116.5 112.5t-78.5 155t-29 195t29 195t78.5 155t116.5 112.5t143 72.5t157.5 30t160 -10.5t151 -54t130 -96t97.5 -140.5t53 -182h-320q-20 66 -64 110.5t-97 60.5
t-110 7.5t-103.5 -39t-76.5 -88.5t-30 -133t30 -133.5t76 -89t103 -39t110 7.5t97.5 60t64.5 110zM483 1552h242l283 -258v-28h-320l-90 82l-86 -82h-324v22z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="1628" 
d="M356 715q0 -98 34 -177.5t90.5 -128.5t127 -75t146.5 -25q85 1 160.5 33t133.5 99.5t80 160.5h355q-26 -184 -129.5 -326.5t-261 -219t-342.5 -76.5q-203 0 -373 95t-269.5 264t-99.5 376t99.5 377.5t269.5 267t373 96.5q137 0 263.5 -45t224 -125t162 -196t81.5 -252
h-355q-34 135 -138.5 208.5t-233.5 75.5q-76 1 -146.5 -24.5t-127 -74.5t-90.5 -129t-34 -179zM557 1753q0 84 56 131t132 47t131.5 -46t55.5 -132q0 -84 -56 -131t-133 -47q-74 0 -130 46.5t-56 131.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="1257" 
d="M823 471h320q-13 -99 -53.5 -182t-98 -139.5t-130 -95t-151 -54.5t-160 -11t-157 30.5t-142.5 73t-116.5 112.5t-78.5 155t-29 195t29 195t78.5 155t116.5 112.5t143 72.5t157.5 30t160 -10.5t151 -54t130 -96t97.5 -140.5t53 -182h-320q-20 66 -64 110.5t-97 60.5
t-110 7.5t-103.5 -39t-76.5 -88.5t-30 -133t30 -133.5t76 -89t103 -39t110 7.5t97.5 60t64.5 110zM406 1425q0 84 56 131.5t132 47.5t131 -46t55 -133q0 -84 -56 -131t-132 -47q-74 0 -130 46.5t-56 131.5z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="1628" 
d="M356 715q0 -98 34 -177.5t90.5 -128.5t127 -75t146.5 -25q85 1 160.5 33t133.5 99.5t80 160.5h355q-26 -184 -129.5 -326.5t-261 -219t-342.5 -76.5q-203 0 -373 95t-269.5 264t-99.5 376t99.5 377.5t269.5 267t373 96.5q137 0 263.5 -45t224 -125t162 -196t81.5 -252
h-355q-34 135 -138.5 208.5t-233.5 75.5q-76 1 -146.5 -24.5t-127 -74.5t-90.5 -129t-34 -179zM881 1595h-242l-283 258v29h320l90 -82l86 82h324v-22z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="1257" 
d="M823 471h320q-13 -99 -53.5 -182t-98 -139.5t-130 -95t-151 -54.5t-160 -11t-157 30.5t-142.5 73t-116.5 112.5t-78.5 155t-29 195t29 195t78.5 155t116.5 112.5t143 72.5t157.5 30t160 -10.5t151 -54t130 -96t97.5 -140.5t53 -182h-320q-20 66 -64 110.5t-97 60.5
t-110 7.5t-103.5 -39t-76.5 -88.5t-30 -133t30 -133.5t76 -89t103 -39t110 7.5t97.5 60t64.5 110zM709 1260h-242l-283 258v28h320l90 -82l86 82h324v-22z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="1417" 
d="M369 334h221q87 0 158 33t115 87.5t67.5 121.5t23.5 139t-23.5 140t-67.5 123.5t-115 89.5t-158 34h-221v-768zM20 1434h570q138 0 255 -40.5t199.5 -109.5t141 -161t86.5 -195.5t28 -212.5t-28 -212t-86.5 -194.5t-141 -160t-199.5 -108.5t-255 -40h-570v1434zM670 1575
h-242l-283 258v29h320l90 -82l86 82h324v-23z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="1822" 
d="M565 1126q183 0 287 -118v436h322v-1444h-322v111q-101 -125 -291 -125q-128 0 -231.5 48.5t-168 131t-99 187t-33.5 220.5q2 116 36 215t98.5 175t168 119.5t233.5 43.5zM600 295q104 0 178 78t74 188t-74 189t-178 79q-116 0 -181 -77.5t-67 -186.5q-1 -69 25.5 -129
t85 -100.5t137.5 -40.5zM1731 1448v-94l-187 -357h-278l168 451h297z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="1564" 
d="M74 551v319h90v562h543q138 0 255 -40t199.5 -109t141 -161t86.5 -195t28 -212t-28 -212t-86.5 -194.5t-141 -160t-199.5 -108.5t-255 -40h-543v551h-90zM514 338h193q87 0 157.5 32t114 86t67 120.5t23.5 138.5t-23.5 139t-67 121t-114 86.5t-157.5 32.5h-193v-224h242
v-319h-242v-213z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="1316" 
d="M721 1176v172h131v96h322v-96h131v-172h-131v-1176h-322v111q-101 -125 -291 -125q-128 0 -231.5 48.5t-168 131t-99 187t-33.5 220.5q2 116 36 215t98.5 175t168 119.5t233.5 43.5q183 0 287 -118v168h-131zM600 295q104 0 178 78t74 188t-74 189t-178 79
q-116 0 -181 -77.5t-67 -186.5q-1 -69 25.5 -129t85 -100.5t137.5 -40.5z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="1122" 
d="M373 567v-229h594v-338h-947v1446h945v-338h-592v-201h579v-340h-579zM190 1559v258h644v-258h-644z" />
    <glyph glyph-name="emacron" unicode="&#x113;" 
d="M1171 557q0 -65 -12 -127h-794q28 -73 93.5 -113.5t143.5 -42.5q116 0 186 76h351q-62 -177 -215.5 -271t-337.5 -91q-108 2 -206.5 39.5t-178 106t-127 178t-47.5 245.5q0 111 32.5 205t87.5 160t129 112.5t157 68.5t171 21q87 -1 168.5 -23.5t154 -69t127 -112.5
t86 -159t31.5 -203zM377 682h442q-22 80 -82.5 121t-142.5 41q-75 0 -134 -41.5t-83 -120.5zM283 1282v260h626v-260h-626z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="1122" 
d="M373 567v-229h594v-338h-947v1446h945v-338h-592v-201h579v-340h-579zM584 1858h262q0 -71 -28.5 -129t-75 -94.5t-105.5 -56.5t-121 -21t-121 18t-105.5 55t-75 95t-28.5 133h252q1 -25 16 -41.5t37 -20t43 1t36 21t14 39.5z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" 
d="M1171 557q0 -65 -12 -127h-794q28 -73 93.5 -113.5t143.5 -42.5q116 0 186 76h351q-62 -177 -215.5 -271t-337.5 -91q-108 2 -206.5 39.5t-178 106t-127 178t-47.5 245.5q0 111 32.5 205t87.5 160t129 112.5t157 68.5t171 21q87 -1 168.5 -23.5t154 -69t127 -112.5
t86 -159t31.5 -203zM377 682h442q-22 80 -82.5 121t-142.5 41q-75 0 -134 -41.5t-83 -120.5zM678 1550h262q0 -94 -49 -164t-122 -102.5t-158.5 -33t-158.5 30t-122 101.5t-49 168h251q1 -25 16 -41.5t37 -20t43 1t36 21t14 39.5z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="1122" 
d="M373 567v-229h594v-338h-947v1446h945v-338h-592v-201h579v-340h-579zM340 1737q0 84 56 131t132 47t131.5 -46t55.5 -132q0 -84 -56 -131t-133 -47q-74 0 -130 46.5t-56 131.5z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" 
d="M1171 557q0 -65 -12 -127h-794q28 -73 93.5 -113.5t143.5 -42.5q116 0 186 76h351q-62 -177 -215.5 -271t-337.5 -91q-108 2 -206.5 39.5t-178 106t-127 178t-47.5 245.5q0 111 32.5 205t87.5 160t129 112.5t157 68.5t171 21q87 -1 168.5 -23.5t154 -69t127 -112.5
t86 -159t31.5 -203zM377 682h442q-22 80 -82.5 121t-142.5 41q-75 0 -134 -41.5t-83 -120.5zM428 1401q0 84 56 131t132 47t131.5 -46t55.5 -132q0 -84 -56 -131t-133 -47q-74 0 -130 46.5t-56 131.5z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="1124" 
d="M352 567v-229h594v-338h-6q-30 -11 -45.5 -36t-13 -50.5t25.5 -47t62 -26.5l-127 -188q-62 -12 -112 0t-89 36t-62 62.5t-29.5 79.5t9 86.5t51.5 83.5h-610v1454h944v-338h-592v-209h580v-340h-580z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" 
d="M1171 557q0 -65 -12 -127h-794q29 -78 100 -117.5t153 -36.5q105 3 170 74h351q-62 -179 -226 -276q-64 -49 -68.5 -106t42 -92t117.5 -28l-103 -200q-81 -17 -148 -5.5t-113 50.5t-70 87t-18 104.5t44 103.5q-87 0 -169 22t-154.5 69t-127 113t-86.5 160t-32 205
q0 109 31.5 202t86 159t126.5 112.5t153.5 69.5t167.5 24q111 2 213 -33.5t184.5 -104t132 -180t49.5 -249.5zM377 682h442q-22 80 -82.5 121t-142.5 41q-75 0 -134 -41.5t-83 -120.5z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="1122" 
d="M373 567v-229h594v-338h-947v1446h945v-338h-592v-201h579v-340h-579zM637 1561h-242l-282 258v28h319l90 -82l86 82h324v-22z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" 
d="M1171 557q0 -65 -12 -127h-794q28 -73 93.5 -113.5t143.5 -42.5q116 0 186 76h351q-62 -177 -215.5 -271t-337.5 -91q-108 2 -206.5 39.5t-178 106t-127 178t-47.5 245.5q0 111 32.5 205t87.5 160t129 112.5t157 68.5t171 21q87 -1 168.5 -23.5t154 -69t127 -112.5
t86 -159t31.5 -203zM377 682h442q-22 80 -82.5 121t-142.5 41q-75 0 -134 -41.5t-83 -120.5zM729 1257h-242l-282 259v28h319l90 -82l86 82h324v-22z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="1615" 
d="M739 1112q-159 0 -264.5 -112.5t-105.5 -284.5q0 -181 105 -292.5t265 -111.5q184 0 283 133h-438v314h876q0 -206 -51 -324q-49 -133 -157.5 -239t-243.5 -162t-269 -56q-153 0 -287.5 55.5t-230.5 152.5t-151.5 234.5t-55.5 295.5t56.5 296.5t153.5 235t231 152
t284 55.5q73 0 146.5 -14t150 -42t145 -77t125 -113.5t96.5 -156.5t54 -201h-369q-18 73 -56.5 126.5t-88.5 82t-100 41t-103 12.5zM631 1849h241l283 -258v-28h-319l-91 82l-86 -82h-323v22z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" 
d="M559 4q-130 0 -233 41.5t-168 115t-99 171t-34 213.5q0 121 34 225t99 182.5t167.5 123.5t231.5 44q192 0 289 -123v111h321v-1045q0 -119 -31.5 -216.5t-85.5 -163t-126.5 -110.5t-152.5 -65t-167 -20q-139 0 -263 51.5t-204 152.5t-80 230h338q0 -50 60.5 -86.5
t138.5 -34.5q47 1 88 14.5t77.5 42.5t59.5 81t27 122v58q-99 -115 -287 -115zM596 813q-80 0 -138.5 -41t-84.5 -101.5t-25 -129.5q1 -110 74.5 -171t173.5 -61q101 2 175.5 64.5t74.5 171.5q0 110 -73.5 189t-176.5 79zM535 1548h241l283 -258v-28h-320l-90 81l-86 -81
h-323v22z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="1615" 
d="M739 1112q-159 0 -264.5 -112.5t-105.5 -284.5q0 -181 105 -292.5t265 -111.5q184 0 283 133h-438v314h876q0 -206 -51 -324q-49 -133 -157.5 -239t-243.5 -162t-269 -56q-153 0 -287.5 55.5t-230.5 152.5t-151.5 234.5t-55.5 295.5t56.5 296.5t153.5 235t231 152
t284 55.5q73 0 146.5 -14t150 -42t145 -77t125 -113.5t96.5 -156.5t54 -201h-369q-18 73 -56.5 126.5t-88.5 82t-100 41t-103 12.5zM815 1860h262q0 -94 -49 -164t-122 -102.5t-158.5 -33t-158.5 30t-122 101.5t-49 168h252q1 -41 37.5 -57t72.5 1t35 56z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" 
d="M559 4q-130 0 -233 41.5t-168 115t-99 171t-34 213.5q0 121 34 225t99 182.5t167.5 123.5t231.5 44q192 0 289 -123v111h321v-1045q0 -119 -31.5 -216.5t-85.5 -163t-126.5 -110.5t-152.5 -65t-167 -20q-139 0 -263 51.5t-204 152.5t-80 230h338q0 -50 60.5 -86.5
t138.5 -34.5q47 1 88 14.5t77.5 42.5t59.5 81t27 122v58q-99 -115 -287 -115zM596 813q-80 0 -138.5 -41t-84.5 -101.5t-25 -129.5q1 -110 74.5 -171t173.5 -61q101 2 175.5 64.5t74.5 171.5q0 110 -73.5 189t-176.5 79zM696 1550h262q0 -94 -49 -164t-122 -102.5
t-158.5 -33t-158.5 30t-122 101.5t-49 168h252q1 -41 37.5 -57t72.5 1t35 56z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="1615" 
d="M739 1112q-159 0 -264.5 -112.5t-105.5 -284.5q0 -181 105 -292.5t265 -111.5q184 0 283 133h-438v314h876q0 -206 -51 -324q-49 -133 -157.5 -239t-243.5 -162t-269 -56q-153 0 -287.5 55.5t-230.5 152.5t-151.5 234.5t-55.5 295.5t56.5 296.5t153.5 235t231 152
t284 55.5q73 0 146.5 -14t150 -42t145 -77t125 -113.5t96.5 -156.5t54 -201h-369q-18 73 -56.5 126.5t-88.5 82t-100 41t-103 12.5zM543 1753q0 84 56 131t132 47t131.5 -46t55.5 -132q0 -84 -56 -131t-133 -47q-74 0 -130 46.5t-56 131.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" 
d="M559 4q-130 0 -233 41.5t-168 115t-99 171t-34 213.5q0 121 34 225t99 182.5t167.5 123.5t231.5 44q192 0 289 -123v111h321v-1045q0 -119 -31.5 -216.5t-85.5 -163t-126.5 -110.5t-152.5 -65t-167 -20q-139 0 -263 51.5t-204 152.5t-80 230h338q0 -50 60.5 -86.5
t138.5 -34.5q47 1 88 14.5t77.5 42.5t59.5 81t27 122v58q-99 -115 -287 -115zM596 813q-80 0 -138.5 -41t-84.5 -101.5t-25 -129.5q1 -110 74.5 -171t173.5 -61q101 2 175.5 64.5t74.5 171.5q0 110 -73.5 189t-176.5 79zM426 1425q0 84 56 131.5t132 47.5t131.5 -46.5
t55.5 -132.5q0 -84 -56 -131t-133 -47q-74 0 -130 46.5t-56 131.5z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="1615" 
d="M584 -182h327q56 -189 -55 -320h-289q24 69 33.5 111.5t7.5 97t-24 111.5zM739 1112q-159 0 -264.5 -112.5t-105.5 -284.5q0 -181 105 -292.5t265 -111.5q184 0 283 133h-438v314h876q0 -206 -51 -324q-49 -133 -157.5 -239t-243.5 -162t-269 -56q-153 0 -287.5 55.5
t-230.5 152.5t-151.5 234.5t-55.5 295.5t56.5 296.5t153.5 235t231 152t284 55.5q73 0 146.5 -14t150 -42t145 -77t125 -113.5t96.5 -156.5t54 -201h-369q-18 73 -56.5 126.5t-88.5 82t-100 41t-103 12.5z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" 
d="M797 1255h-328q-56 189 55 320h289q-23 -68 -33 -111.5t-7.5 -97.5t24.5 -111zM559 4q-130 0 -233 41.5t-168 115t-99 171t-34 213.5q0 121 34 225t99 182.5t167.5 123.5t231.5 44q192 0 289 -123v111h321v-1045q0 -119 -31.5 -216.5t-85.5 -163t-126.5 -110.5
t-152.5 -65t-167 -20q-139 0 -263 51.5t-204 152.5t-80 230h338q0 -50 60.5 -86.5t138.5 -34.5q47 1 88 14.5t77.5 42.5t59.5 81t27 122v58q-99 -115 -287 -115zM596 813q-80 0 -138.5 -41t-84.5 -101.5t-25 -129.5q1 -110 74.5 -171t173.5 -61q101 2 175.5 64.5t74.5 171.5
q0 110 -73.5 189t-176.5 79z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="1261" 
d="M371 1434v-541h417v541h351v-1434h-351v555h-417v-555h-351v1434h351zM457 1847h241l283 -258v-28h-319l-91 81l-86 -81h-323v22z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="1243" 
d="M49 1444h326v-447q104 127 325 127q407 -5 400 -514v-610h-326v608q0 102 -48 155.5t-118 55.5q-80 2 -148.5 -59.5t-68.5 -157.5v-602h-342v1444zM305 1851h242l282 -258v-28h-319l-90 82l-86 -82h-324v22z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="1370" 
d="M-10 1038v228h84v168h350v-168h418v168h350v-168h111v-228h-111v-1038h-350v555h-418v-555h-350v1038h-84zM424 893h418v145h-418v-145z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="1243" 
d="M-61 1169v164h110v111h326v-111h121v-164h-121v-172q104 127 325 127q407 -5 400 -514v-610h-326v608q0 102 -48 155.5t-118 55.5q-80 2 -148.5 -59.5t-68.5 -157.5v-602h-342v1169h-110z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="1009" 
d="M852 1442v-320h-238v-809h238v-313h-825v313h237v809h-237v320h825zM643 1890l178 -155q-62 -106 -139 -155t-160 -40q-34 5 -71 23t-62 33t-52 23t-54.5 -6.5t-57.5 -56.5l-184 146q64 108 143.5 157t161 39.5t162.5 -69.5q18 -13 34.5 -15t35 11.5t27 21.5t29.5 33
q6 7 9 10z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="548" 
d="M74 1108h327l2 -1108h-327zM420 1565l164 -152q-115 -208 -275 -192q-37 5 -79 29.5t-67 40.5t-58 6.5t-66 -60.5l-168 143q58 106 130.5 154.5t147 39t148.5 -68.5q16 -13 31 -14.5t31 10.5t25.5 22t26.5 31q6 8 9 11z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="1009" 
d="M852 1442v-320h-238v-809h238v-313h-825v313h237v809h-237v320h825zM127 1546v260h618v-260h-618z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="548" 
d="M74 1108h327l2 -1108h-327zM-59 1237v252h587v-252h-587z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="1009" 
d="M852 1442v-320h-238v-809h238v-313h-825v313h237v809h-237v320h825zM512 1845h262q0 -94 -49 -164t-122 -102.5t-158.5 -33t-158.5 30t-122 101.5t-49 168h252q1 -41 37.5 -57t72.5 1t35 56z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="548" 
d="M74 1108h327l2 -1108h-327zM311 1542h262q0 -94 -49 -164t-122 -102.5t-158.5 -33t-158.5 30t-122 101.5t-49 168h252q1 -41 37.5 -57t72.5 1t35 56z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="1009" 
d="M733 -207l4 -6l-192 -205q-12 -1 -32.5 -2t-73 5t-95 22t-81.5 56.5t-48 100.5h2q-19 134 105 236h-295v313h237v809h-237v320h825v-320h-238v-809h238v-313h-219q-34 -21 -50.5 -51.5t-11.5 -59.5t23.5 -53t55.5 -36.5t83 -6.5z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="548" 
d="M489 -190l5 -7l-181 -192q-6 -1 -16 -1.5t-39 0.5t-56.5 5t-61.5 15.5t-60 29.5t-47 49t-27 72h2q-17 123 96 219h-28v1108h327v-1108h-12q-51 -34 -55.5 -83.5t39 -83t114.5 -23.5z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="1009" 
d="M852 1442v-320h-238v-809h238v-313h-825v313h237v809h-237v320h825zM256 1720q0 84 56 131t132 47t131.5 -46t55.5 -132q0 -84 -56 -131t-133 -47q-74 0 -130 46.5t-56 131.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="548" 
d="M74 1108h327l2 -1108h-327z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="1820" 
d="M831 1434v-320h-237v-801h237v-313h-825v313h238v801h-238v320h825zM950 0v330q94 0 158.5 12t104 33.5t61 61t28.5 83t7 111.5v481h-346v322h694v-803q0 -137 -19.5 -234.5t-67 -176t-128.5 -125t-202 -71t-290 -24.5z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="1132" 
d="M436 1108h-327v-1108h327v1108zM946 72v1036h-321v-1030q0 -99 -62 -145.5t-174 -18.5l-12 -266q271 -60 420 56t149 368zM594 1343q0 61 28.5 105.5t71.5 64.5t92.5 20t92.5 -20t71.5 -64.5t28.5 -105.5t-28.5 -105.5t-71.5 -64.5t-92.5 -20t-92.5 20t-71.5 64.5
t-28.5 105.5zM88 1343q0 46 16.5 83t43.5 60t61.5 35.5t71 12.5t71 -12.5t61.5 -35.5t43.5 -60t16.5 -83q0 -61 -28.5 -105.5t-71.5 -64.5t-92.5 -20t-92.5 20t-71.5 64.5t-28.5 105.5z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="1019" 
d="M856 1444v-813q0 -231 -83.5 -368t-266 -200t-490.5 -63v328q190 0 296.5 30.5t151 94.5t44.5 178v491h-346v322h694zM354 1855h242l283 -258v-28h-320l-90 82l-86 -82h-324v22z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="544" 
d="M401 -47v1155h-321v-1149q0 -48 -22 -80.5t-56.5 -45t-76 -16t-81.5 8.5l-12 -266q92 -17 176 -16t156 30t124.5 76.5t82.5 125.5t30 177zM111 1565h241l283 -258v-29h-320l-90 82l-86 -82h-323v22z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="1341" 
d="M428 -154h328q56 -188 -56 -319h-288q24 68 33.5 111t7 97t-24.5 111zM1184 1434v-21l-508 -692l555 -703v-18h-434l-426 555v-555h-351v1434h351v-557l397 557h416z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="1144" 
d="M428 -154h328q56 -188 -56 -319h-288q24 68 33.5 111t7 97t-24.5 111zM383 709v-37l305 436h352v-16l-374 -535l411 -543v-14h-377l-317 432v-31v-401h-322v1434h322v-725z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="1144" 
d="M383 709v-37l305 436h352v-16l-374 -535l411 -543v-14h-377l-317 432v-31v-401h-322v1108h322v-399z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="1116" 
d="M371 1434v-1096h602v-338h-953v1434h351zM567 1810v-22l-235 -252h-264v47l129 227h370z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="509" 
d="M49 1434h328v-1434h-328v1434zM551 1821v-23l-236 -252h-264v47l129 228h371z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="1116" 
d="M319 -109h328q56 -189 -55 -319h-289q24 68 33.5 111t7 97t-24.5 111zM371 1434v-1096h602v-338h-953v1434h351z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="509" 
d="M47 -111h328q56 -188 -56 -319h-288q24 68 33.5 111t7 97t-24.5 111zM49 1434h328v-1434h-328v1434z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="1116" 
d="M629 1438h327q56 -189 -55 -320h-289q24 69 33.5 111.5t7.5 97t-24 111.5zM371 1434v-1096h602v-338h-953v1434h351z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="985" 
d="M551 1432h283q50 -217 -48 -365h-249q20 78 28.5 127t6.5 111t-21 127zM49 1434h328v-1434h-328v1434z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="1116" 
d="M371 1434v-1096h602v-338h-953v1434h351zM727 1040q84 0 137.5 -50.5t53.5 -131.5q0 -70 -55 -126t-136 -56q-75 0 -129.5 56.5t-54.5 125.5q0 80 53 131t131 51z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="1003" 
d="M49 1434h328v-1434h-328v1434zM690 891q84 0 137.5 -50.5t53.5 -131.5q0 -70 -55.5 -126.5t-135.5 -56.5q-75 0 -129.5 57t-54.5 126q0 80 53 131t131 51z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="1202" 
d="M457 655v-317h602v-338h-953v457l-106 -60v346l106 62v629h351v-428l184 106v-352z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="567" 
d="M72 1434h327v-480l78 45v-352l-78 -43v-604h-327v422l-78 -43v346l78 43v666z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="1296" 
d="M369 0h-349v1434h271l495 -717v717h347v-1434h-275l-489 698v-698zM932 1817v-23l-236 -252h-264v47l129 228h371z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="1243" 
d="M47 1108h326v-111q101 127 327 127q404 -5 400 -514v-610h-328v608q0 106 -50 161.5t-122 55.5q-79 0 -145 -62t-66 -161v-602h-342v1108zM915 1550v-22l-235 -252h-264v47l129 227h370z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="1296" 
d="M449 -150h327q56 -189 -55 -319h-289q24 69 33.5 111.5t7.5 96.5t-24 111zM369 0h-349v1434h271l495 -717v717h347v-1434h-275l-489 698v-698z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="1243" 
d="M428 -133h328q56 -188 -56 -320h-288q23 68 33 111.5t7.5 97.5t-24.5 111zM47 1108h326v-111q101 127 327 127q404 -5 400 -514v-610h-328v608q0 106 -50 161.5t-122 55.5q-79 0 -145 -62t-66 -161v-602h-342v1108z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="1296" 
d="M369 0h-349v1434h271l495 -717v717h347v-1434h-275l-489 698v-698zM672 1556h-242l-283 259v28h320l90 -82l86 82h324v-22z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="1243" 
d="M47 1108h326v-111q101 127 327 127q404 -5 400 -514v-610h-328v608q0 106 -50 161.5t-122 55.5q-79 0 -145 -62t-66 -161v-602h-342v1108zM711 1274h-242l-283 258v29h320l90 -82l86 82h324v-23z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="1519" 
d="M8 1432h311q17 -57 18 -121t-18 -128.5t-57 -109.5h-272q73 213 18 359zM338 1108v-1108h342v602q0 73 38 128.5t92.5 74t108.5 11t92 -63t38 -144.5v-608h327v610q4 509 -399 514q-212 0 -313 -127v111h-326z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="1275" 
d="M348 0h-348v1434h270l496 -717v717h346v-1446q0 -410 -496 -375v278q54 -7 91 0t54 26.5t23.5 41t6.5 49.5v60l-443 630v-698z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="1243" 
d="M61 0v1108h326v-111q101 127 313 127q404 -5 400 -514v-626q0 -201 -124 -302t-378 -73v278q52 -7 88 0t53.5 26t24.5 39.5t8 47.5v608q0 89 -38 144.5t-92.5 63t-108.5 -11t-92 -74t-38 -128.5v-602h-342z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="1642" 
d="M10 715q0 207 99.5 377.5t269.5 267t373 96.5t373 -96.5t270 -267t100 -377.5q0 -153 -59 -290t-158.5 -234t-236.5 -154t-289 -57q-203 0 -373 95t-269.5 264t-99.5 376zM358 715q0 -97 32.5 -175.5t87 -127t122.5 -74.5t143 -27q76 -1 146.5 25t127.5 74.5t91.5 127.5
t34.5 177q0 99 -34.5 179t-91.5 128.5t-127.5 74t-146.5 23.5q-75 -2 -143 -28t-122.5 -75t-87 -127t-32.5 -175zM438 1575v254h627v-254h-627z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="1296" 
d="M1190 553q0 -142 -51 -257.5t-134 -186t-189 -108.5t-217 -38t-217 38.5t-189 109t-134 185.5t-51 257q0 143 51 258.5t134 186.5t189 109.5t217 38.5t217 -39t189 -110t134 -186.5t51 -257.5zM332 553q0 -67 23 -121.5t60.5 -88t85.5 -51.5t98 -18t98 18t85.5 51.5
t60.5 88t23 121.5q0 68 -23 123t-60.5 89t-85.5 52t-98 18t-98 -18.5t-85.5 -52t-60.5 -88.5t-23 -123zM289 1257v261h631v-261h-631z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="1642" 
d="M10 715q0 207 99.5 377.5t269.5 267t373 96.5t373 -96.5t270 -267t100 -377.5q0 -153 -59 -290t-158.5 -234t-236.5 -154t-289 -57q-203 0 -373 95t-269.5 264t-99.5 376zM358 715q0 -97 32.5 -175.5t87 -127t122.5 -74.5t143 -27q76 -1 146.5 25t127.5 74.5t91.5 127.5
t34.5 177q0 99 -34.5 179t-91.5 128.5t-127.5 74t-146.5 23.5q-75 -2 -143 -28t-122.5 -75t-87 -127t-32.5 -175zM834 1866h262q0 -71 -28.5 -129t-75 -94.5t-105.5 -56.5t-121 -21t-121 18t-105.5 55t-75 95t-28.5 133h252q1 -25 16 -41.5t37 -20t43 1t36 21t14 39.5z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="1296" 
d="M1190 553q0 -142 -51 -257.5t-134 -186t-189 -108.5t-217 -38t-217 38.5t-189 109t-134 185.5t-51 257q0 143 51 258.5t134 186.5t189 109.5t217 38.5t217 -39t189 -110t134 -186.5t51 -257.5zM332 553q0 -67 23 -121.5t60.5 -88t85.5 -51.5t98 -18t98 18t85.5 51.5
t60.5 88t23 121.5q0 68 -23 123t-60.5 89t-85.5 52t-98 18t-98 -18.5t-85.5 -52t-60.5 -88.5t-23 -123zM684 1556h262q0 -94 -49 -164t-122 -102.5t-158.5 -33t-158.5 30t-122 101.5t-49 168h252q1 -41 37.5 -57t72.5 1t35 56z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="1642" 
d="M10 715q0 207 99.5 377.5t269.5 267t373 96.5t373 -96.5t270 -267t100 -377.5q0 -153 -59 -290t-158.5 -234t-236.5 -154t-289 -57q-203 0 -373 95t-269.5 264t-99.5 376zM358 715q0 -97 32.5 -175.5t87 -127t122.5 -74.5t143 -27q76 -1 146.5 25t127.5 74.5t91.5 127.5
t34.5 177q0 99 -34.5 179t-91.5 128.5t-127.5 74t-146.5 23.5q-75 -2 -143 -28t-122.5 -75t-87 -127t-32.5 -175zM778 1561l191 292h348l-242 -292h-297zM397 1561l176 292h326l-209 -292h-293z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="1296" 
d="M1190 553q0 -142 -51 -257.5t-134 -186t-189 -108.5t-217 -38t-217 38.5t-189 109t-134 185.5t-51 257q0 143 51 258.5t134 186.5t189 109.5t217 38.5t217 -39t189 -110t134 -186.5t51 -257.5zM332 553q0 -67 23 -121.5t60.5 -88t85.5 -51.5t98 -18t98 18t85.5 51.5
t60.5 88t23 121.5q0 68 -23 123t-60.5 89t-85.5 52t-98 18t-98 -18.5t-85.5 -52t-60.5 -88.5t-23 -123zM604 1257l191 293h348l-242 -293h-297zM223 1257l176 293h326l-209 -293h-293z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1892" 
d="M1130 547v-209h599v-338h-826q-82 -16 -160 -16q-334 -2 -534.5 195.5t-200.5 537.5q0 215 96.5 383.5t263.5 259.5t375 90q102 0 170 -18h813v-338h-596v-207h584v-340h-584zM360 717q0 -149 60.5 -249.5t160.5 -140t226 -20.5v823q-132 22 -236 -32.5t-157.5 -155.5
t-53.5 -225z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="2101" 
d="M1962 557q0 -65 -12 -127h-787q19 -65 71.5 -105.5t114.5 -49t125.5 11t104.5 63.5h350q-61 -173 -203 -266.5t-313 -95.5q-120 -2 -229 43.5t-187 134.5q-78 -90 -190.5 -138t-233.5 -46q-83 1 -162 23.5t-148.5 69.5t-122 113.5t-83 160.5t-30.5 204q0 111 31 206
t84 162t124 114t150.5 69.5t163.5 21.5q119 0 228.5 -47.5t186.5 -136.5q76 88 184.5 134t227.5 48q106 1 203.5 -35t177 -104t127 -179t47.5 -249zM348 553q0 -72 27.5 -129t72 -86.5t98 -43t107 0t98 43t72 86.5t27.5 129q0 85 -37 147t-93 90t-121 28t-121 -28t-93 -90
t-37 -147zM1165 682h445q-22 80 -83 121t-143 41q-75 0 -135 -41.5t-84 -120.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="1247" 
d="M426 485h-57v-485h-349v1446h558q97 0 179 -27.5t139.5 -74t98 -109t59 -132t17.5 -142.5q-3 -136 -75.5 -257t-204.5 -184l307 -454v-66h-373zM369 1110v-307h200q72 0 115 44.5t45 102.5q2 63 -40.5 111.5t-119.5 48.5h-200zM797 1825v-23l-236 -252h-264v47l129 228
h371z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="839" 
d="M383 614v-614h-322v1108h291l13 -74q102 96 241 96q56 0 125 -16l-4 -303q-53 16 -109 16q-98 -2 -166.5 -55t-68.5 -158zM676 1524v-23l-236 -252h-264v47l129 228h371z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="1247" 
d="M381 -129h328q56 -188 -56 -320h-288q23 68 33 111.5t7.5 97.5t-24.5 111zM426 485h-57v-485h-349v1446h558q97 0 179 -27.5t139.5 -74t98 -109t59 -132t17.5 -142.5q-3 -136 -75.5 -257t-204.5 -184l307 -454v-66h-373zM369 1110v-307h200q72 0 115 44.5t45 102.5
q2 63 -40.5 111.5t-119.5 48.5h-200z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="839" 
d="M66 -131h327q56 -189 -55 -320h-289q24 69 33.5 111.5t7.5 97t-24 111.5zM383 614v-614h-322v1108h291l13 -74q102 96 241 96q56 0 125 -16l-4 -303q-53 16 -109 16q-98 -2 -166.5 -55t-68.5 -158z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="1247" 
d="M426 485h-57v-485h-349v1446h558q97 0 179 -27.5t139.5 -74t98 -109t59 -132t17.5 -142.5q-3 -136 -75.5 -257t-204.5 -184l307 -454v-66h-373zM369 1110v-307h200q72 0 115 44.5t45 102.5q2 63 -40.5 111.5t-119.5 48.5h-200zM592 1550h-242l-282 258v29h319l90 -82
l86 82h324v-22z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="839" 
d="M383 614v-614h-322v1108h291l13 -74q102 96 241 96q56 0 125 -16l-4 -303q-53 16 -109 16q-98 -2 -166.5 -55t-68.5 -158zM535 1249h-242l-283 258v29h320l90 -82l86 82h323v-23z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="1261" 
d="M1098 981h-353q0 70 -51 108.5t-135 38.5q-83 0 -133.5 -33t-50.5 -91q0 -17 5.5 -31.5t17 -25t23 -19t30 -14t31.5 -9.5t33.5 -6.5t30 -3.5t27.5 -2.5t19 -1.5q60 -8 106 -17t104.5 -25t103.5 -40.5t88 -58.5t73 -84.5t45 -112.5q10 -55 10 -100q0 -87 -32.5 -163.5
t-87.5 -132t-127 -95.5t-153 -60t-163 -20q-73 0 -147 17t-147.5 56.5t-130 96.5t-91.5 144.5t-35 192.5h354q0 -50 19 -85t51 -51.5t63 -23.5t66 -7q93 0 150 28t57 86q0 18 -7.5 33.5t-18.5 26t-29.5 19.5t-33 13.5t-37 9t-34.5 6t-33 3.5t-24 2q-58 6 -101.5 12.5
t-100 20t-97.5 32t-85 49t-73 69.5t-49.5 95t-26.5 125v-2q-8 152 66.5 269t200.5 176t280 59q79 0 154 -17t144 -54.5t121 -92t83 -134t31 -175.5zM895 1829v-23l-236 -252h-264v48l129 227h371z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="1054" 
d="M41 752q0 97 36 172t100 120.5t145 68t176 22.5q91 0 170 -23.5t141 -69t98 -119t37 -167.5h-309q-1 31 -15 50.5t-39.5 28t-47.5 11t-54 2.5q-43 0 -77.5 -18t-34.5 -58q0 -20 12.5 -33t38 -18.5t40 -7t42.5 -2.5q72 -2 137.5 -15t126.5 -41.5t105.5 -69.5t71 -103
t26.5 -138q0 -81 -26 -145.5t-69.5 -106.5t-105 -70t-128.5 -39.5t-144 -11.5q-137 0 -239 40.5t-165 132.5t-65 229h323q1 -52 46.5 -78.5t107.5 -26.5q62 -1 101.5 18.5t39.5 49.5q0 23 -12 38t-36.5 21.5t-41.5 9.5t-49 4t-37 1q-67 6 -125 19t-115 39t-97 63t-64.5 93.5
t-24.5 127.5zM850 1532v-23l-236 -252h-264v48l129 227h371z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="1261" 
d="M1098 981h-353q0 70 -51 108.5t-135 38.5q-83 0 -133.5 -33t-50.5 -91q0 -17 5.5 -31.5t17 -25t23 -19t30 -14t31.5 -9.5t33.5 -6.5t30 -3.5t27.5 -2.5t19 -1.5q60 -8 106 -17t104.5 -25t103.5 -40.5t88 -58.5t73 -84.5t45 -112.5q10 -55 10 -100q0 -87 -32.5 -163.5
t-87.5 -132t-127 -95.5t-153 -60t-163 -20q-73 0 -147 17t-147.5 56.5t-130 96.5t-91.5 144.5t-35 192.5h354q0 -50 19 -85t51 -51.5t63 -23.5t66 -7q93 0 150 28t57 86q0 18 -7.5 33.5t-18.5 26t-29.5 19.5t-33 13.5t-37 9t-34.5 6t-33 3.5t-24 2q-58 6 -101.5 12.5
t-100 20t-97.5 32t-85 49t-73 69.5t-49.5 95t-26.5 125v-2q-8 152 66.5 269t200.5 176t280 59q79 0 154 -17t144 -54.5t121 -92t83 -134t31 -175.5zM451 1837h241l283 -258v-29h-320l-90 82l-86 -82h-323v23z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="1054" 
d="M41 752q0 97 36 172t100 120.5t145 68t176 22.5q91 0 170 -23.5t141 -69t98 -119t37 -167.5h-309q-1 31 -15 50.5t-39.5 28t-47.5 11t-54 2.5q-43 0 -77.5 -18t-34.5 -58q0 -20 12.5 -33t38 -18.5t40 -7t42.5 -2.5q72 -2 137.5 -15t126.5 -41.5t105.5 -69.5t71 -103
t26.5 -138q0 -81 -26 -145.5t-69.5 -106.5t-105 -70t-128.5 -39.5t-144 -11.5q-137 0 -239 40.5t-165 132.5t-65 229h323q1 -52 46.5 -78.5t107.5 -26.5q62 -1 101.5 18.5t39.5 49.5q0 23 -12 38t-36.5 21.5t-41.5 9.5t-49 4t-37 1q-67 6 -125 19t-115 39t-97 63t-64.5 93.5
t-24.5 127.5zM403 1559h242l283 -259v-28h-320l-90 82l-86 -82h-323v22z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="1261" 
d="M424 -195l-21 31l64 154q-86 12 -164 47t-146 93.5t-108.5 151.5t-40.5 207h354q0 -50 19 -85t51 -51.5t63 -23.5t66 -7q93 0 150 28t57 86q0 18 -7.5 33.5t-18.5 26t-29.5 19.5t-33 13.5t-37 9t-34.5 6t-33 3.5t-24 2q-58 6 -101.5 12.5t-100 20t-97.5 32t-85 49
t-73 69.5t-49.5 95t-26.5 125v-2q-8 152 66.5 269t200.5 176t280 59q79 0 154 -17t144 -54.5t121 -92t83 -134t31 -175.5h-353q0 70 -51 108.5t-135 38.5q-83 0 -133.5 -33t-50.5 -91q0 -17 5.5 -31.5t17 -25t23 -19t30 -14t31.5 -9.5t33.5 -6.5t30 -3.5t27.5 -2.5t19 -1.5
q60 -8 106 -17t104.5 -25t103.5 -40.5t88 -58.5t73 -84.5t45 -112.5q10 -55 10 -100q0 -172 -115 -291t-288 -162l-19 -51q103 -16 154 -77.5t29 -164.5q-13 -62 -48.5 -106.5t-84.5 -68t-99.5 -34t-105.5 -10.5q-93 0 -167 40t-108 130l174 96q13 -28 46 -44.5t67 -14.5
q77 0 88 49q7 34 -18 55t-63.5 25.5t-78 -0.5t-61.5 -19z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="1054" 
d="M348 -190l-20 30l57 140q-165 23 -262 120.5t-98 272.5h323q1 -52 46.5 -78.5t107.5 -26.5q62 -1 101.5 18.5t39.5 49.5q0 23 -12 38t-36.5 21.5t-41.5 9.5t-49 4t-37 1q-67 6 -125 19t-115 39t-97 63t-64.5 93.5t-24.5 127.5q0 97 36 172t100 120.5t145 68t176 22.5
q91 0 170 -23.5t141 -69t98 -119t37 -167.5h-309q-1 31 -15 50.5t-39.5 28t-47.5 11t-54 2.5q-43 0 -77.5 -18t-34.5 -58q0 -20 12.5 -33t38 -18.5t40 -7t42.5 -2.5q72 -2 137.5 -15t126.5 -41.5t105.5 -69.5t71 -103t26.5 -138q0 -157 -91 -245t-241 -115l-10 -31
q103 -16 153.5 -77.5t28.5 -164.5q-13 -62 -48.5 -106.5t-84.5 -68t-99.5 -34t-105.5 -10.5q-93 0 -166.5 40t-107.5 130l174 96q13 -28 45.5 -44.5t66.5 -14.5q77 0 88 49q9 44 -35 66t-100 18.5t-86 -22.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="1261" 
d="M1098 981h-353q0 70 -51 108.5t-135 38.5q-83 0 -133.5 -33t-50.5 -91q0 -17 5.5 -31.5t17 -25t23 -19t30 -14t31.5 -9.5t33.5 -6.5t30 -3.5t27.5 -2.5t19 -1.5q60 -8 106 -17t104.5 -25t103.5 -40.5t88 -58.5t73 -84.5t45 -112.5q10 -55 10 -100q0 -87 -32.5 -163.5
t-87.5 -132t-127 -95.5t-153 -60t-163 -20q-73 0 -147 17t-147.5 56.5t-130 96.5t-91.5 144.5t-35 192.5h354q0 -50 19 -85t51 -51.5t63 -23.5t66 -7q93 0 150 28t57 86q0 18 -7.5 33.5t-18.5 26t-29.5 19.5t-33 13.5t-37 9t-34.5 6t-33 3.5t-24 2q-58 6 -101.5 12.5
t-100 20t-97.5 32t-85 49t-73 69.5t-49.5 95t-26.5 125v-2q-8 152 66.5 269t200.5 176t280 59q79 0 154 -17t144 -54.5t121 -92t83 -134t31 -175.5zM711 1567h-242l-283 258v28h320l90 -81l86 81h324v-22z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="1054" 
d="M41 752q0 97 36 172t100 120.5t145 68t176 22.5q91 0 170 -23.5t141 -69t98 -119t37 -167.5h-309q-1 31 -15 50.5t-39.5 28t-47.5 11t-54 2.5q-43 0 -77.5 -18t-34.5 -58q0 -20 12.5 -33t38 -18.5t40 -7t42.5 -2.5q72 -2 137.5 -15t126.5 -41.5t105.5 -69.5t71 -103
t26.5 -138q0 -81 -26 -145.5t-69.5 -106.5t-105 -70t-128.5 -39.5t-144 -11.5q-137 0 -239 40.5t-165 132.5t-65 229h323q1 -52 46.5 -78.5t107.5 -26.5q62 -1 101.5 18.5t39.5 49.5q0 23 -12 38t-36.5 21.5t-41.5 9.5t-49 4t-37 1q-67 6 -125 19t-115 39t-97 63t-64.5 93.5
t-24.5 127.5zM639 1262h-242l-282 258v28h319l90 -82l86 82h324v-22z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="1298" 
d="M752 0h-62l-16 -47q103 -16 153.5 -77.5t28.5 -164.5q-13 -62 -48.5 -106.5t-84.5 -68t-99.5 -34t-105.5 -10.5q-93 0 -166.5 40t-107.5 130l174 96q13 -28 45.5 -44.5t66.5 -14.5q77 0 88 49q9 44 -35 66t-100 18.5t-86 -22.5l-20 30l65 160h-41v1122h-387v328h1123
v-328h-385v-1122z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="849" 
d="M283 -217l-21 29l82 211q-180 85 -180 360v436h-146v277h146v211l321 28v-239h232v-277h-232v-436q0 -68 42 -98.5t108 -30.5q44 0 92 10l10 -266q-95 -13 -157 -16l-25 -66q102 -14 151 -70.5t27 -152.5q-13 -57 -47.5 -98.5t-82.5 -63.5t-98 -31.5t-104 -9.5
q-91 0 -163.5 37.5t-106.5 120.5l172 88q13 -25 45 -40t66 -15q37 0 59 13.5t27 33.5q9 41 -34 60.5t-98.5 16t-84.5 -21.5z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="1298" 
d="M752 0h-351v1122h-387v328h1123v-328h-385v-1122zM690 1548h-241l-283 258v29h319l90 -82l87 82h323v-23z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="970" 
d="M553 1479h272q32 -173 -51 -289h-217q15 51 20.5 93t0.5 93t-25 103zM737 -2q-101 -16 -176 -16q-193 -2 -295 94.5t-102 306.5v436h-146v277h146v211l321 28v-239h232v-277h-232v-436q0 -68 42 -98.5t108 -30.5q44 0 92 10z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="1298" 
d="M188 526v279h213v317h-387v328h1123v-328h-385v-317h239v-279h-239v-526h-351v526h-213z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="849" 
d="M23 416v227h141v176h-146v277h146v211l321 28v-239h232v-277h-232v-176h187v-227h-187v-33q0 -68 42 -98.5t108 -30.5q44 0 92 10l10 -266q-101 -16 -176 -16q-193 -2 -295 94.5t-102 306.5v33h-141z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="1290" 
d="M571 -20q-106 0 -203 30.5t-175.5 88t-125.5 151.5t-47 211v973h351v-971q0 -51 30 -88.5t75 -54.5t97.5 -16.5t97.5 17.5t75 54.5t30 87.5v971h350v-973q0 -94 -31 -174t-84.5 -136t-125 -95.5t-151 -58t-163.5 -17.5zM793 1898l178 -155q-62 -106 -139 -155t-160 -40
q-34 5 -71 23t-62 33t-52 23.5t-54.5 -6t-57.5 -56.5l-185 145q64 108 143.5 157t161 39.5t162.5 -69.5q18 -13 35 -14.5t35.5 11.5t28 22.5t29.5 32.5q5 6 8 9z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="1236" 
d="M1094 0h-326v113q-102 -131 -313 -127q-404 3 -400 512v610h326v-608q0 -66 20.5 -115t53 -72.5t72 -33t79.5 5t72.5 41t53 74.5t20.5 106v602h342v-1108zM782 1563l179 -156q-62 -106 -139 -155t-160 -40q-34 5 -71 23t-62 33t-52 23.5t-54.5 -6t-57.5 -56.5l-185 145
q64 108 143.5 157t161 39.5t162.5 -69.5q18 -13 35.5 -13.5t32.5 9.5t28.5 23.5t29.5 31.5q6 7 9 11z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="1290" 
d="M571 -20q-106 0 -203 30.5t-175.5 88t-125.5 151.5t-47 211v973h351v-971q0 -51 30 -88.5t75 -54.5t97.5 -16.5t97.5 17.5t75 54.5t30 87.5v971h350v-973q0 -94 -31 -174t-84.5 -136t-125 -95.5t-151 -58t-163.5 -17.5zM248 1559v258h641v-258h-641z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="1236" 
d="M1094 0h-326v113q-102 -131 -313 -127q-404 3 -400 512v610h326v-608q0 -66 20.5 -115t53 -72.5t72 -33t79.5 5t72.5 41t53 74.5t20.5 106v602h342v-1108zM283 1255v273h628v-273h-628z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="1290" 
d="M571 -20q-106 0 -203 30.5t-175.5 88t-125.5 151.5t-47 211v973h351v-971q0 -51 30 -88.5t75 -54.5t97.5 -16.5t97.5 17.5t75 54.5t30 87.5v971h350v-973q0 -94 -31 -174t-84.5 -136t-125 -95.5t-151 -58t-163.5 -17.5zM631 1837h262q0 -71 -28.5 -129t-75 -94.5
t-105.5 -56.5t-121 -21t-121 18t-105.5 55t-75 95t-28.5 133h252q1 -25 16 -41.5t37 -20t43 1t36 21t14 39.5z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="1236" 
d="M1094 0h-326v113q-102 -131 -313 -127q-404 3 -400 512v610h326v-608q0 -66 20.5 -115t53 -72.5t72 -33t79.5 5t72.5 41t53 74.5t20.5 106v602h342v-1108zM639 1546h262q0 -94 -49 -164t-122 -102.5t-158.5 -33t-158.5 30t-122 101.5t-49 168h252q1 -41 37.5 -57t72.5 1
t35 56z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="1290" 
d="M580 1540q-101 0 -172.5 60.5t-71.5 158.5q0 99 71.5 160t172.5 61q100 0 172.5 -61t72.5 -160q0 -98 -72.5 -158.5t-172.5 -60.5zM580 1841q-41 0 -70 -23t-29 -59q0 -37 28.5 -57.5t70.5 -20.5q45 0 72.5 20.5t27.5 57.5t-28 59.5t-72 22.5zM571 -20q-106 0 -203 30.5
t-175.5 88t-125.5 151.5t-47 211v973h351v-971q0 -51 30 -88.5t75 -54.5t97.5 -16.5t97.5 17.5t75 54.5t30 87.5v971h350v-973q0 -94 -31 -174t-84.5 -136t-125 -95.5t-151 -58t-163.5 -17.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="1236" 
d="M582 1167q-101 0 -172.5 60.5t-71.5 158.5q0 99 71.5 160.5t172.5 61.5q100 0 172.5 -61.5t72.5 -160.5q0 -98 -72.5 -158.5t-172.5 -60.5zM582 1470q-42 0 -70.5 -24t-28.5 -62q0 -37 28 -57t71 -20q47 0 74.5 20t27.5 57q0 38 -28 62t-74 24zM1094 0h-326v113
q-102 -131 -313 -127q-404 3 -400 512v610h326v-608q0 -66 20.5 -115t53 -72.5t72 -33t79.5 5t72.5 41t53 74.5t20.5 106v602h342v-1108z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="1290" 
d="M571 -20q-106 0 -203 30.5t-175.5 88t-125.5 151.5t-47 211v973h351v-971q0 -51 30 -88.5t75 -54.5t97.5 -16.5t97.5 17.5t75 54.5t30 87.5v971h350v-973q0 -94 -31 -174t-84.5 -136t-125 -95.5t-151 -58t-163.5 -17.5zM588 1563l190 292h348l-241 -292h-297zM207 1563
l176 292h326l-209 -292h-293z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="1236" 
d="M1094 0h-326v113q-102 -131 -313 -127q-404 3 -400 512v610h326v-608q0 -66 20.5 -115t53 -72.5t72 -33t79.5 5t72.5 41t53 74.5t20.5 106v602h342v-1108zM567 1264l191 292h348l-242 -292h-297zM186 1264l176 292h326l-209 -292h-293z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="1329" 
d="M963 -168l2 -6l-160 -201q-5 -1 -14 -1.5t-35.5 0.5t-51 5t-55 16.5t-54 31t-42.5 51t-25 75.5h2q-13 100 41 179q-101 5 -191 38t-163 90.5t-115.5 148t-42.5 202.5v973h351v-971q0 -51 30 -88.5t75 -54.5t97.5 -16.5t97.5 17.5t75 54.5t30 87.5v971h350v-973
q0 -157 -80.5 -266.5t-214.5 -165.5q-33 -27 -41 -64.5t5 -69t48 -50.5t81 -13z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="1236" 
d="M1135 -186l4 -7l-164 -194q-5 -1 -14.5 -1.5t-36 0.5t-51.5 5t-56 15.5t-55 29.5t-43 50t-25 73h2q-18 123 78 215h-6v113q-102 -131 -313 -127q-404 3 -400 512v610h326v-608q0 -66 20.5 -115t53 -72.5t72 -33t79.5 5t72.5 41t53 74.5t20.5 106v602h342v-1108h-58
q-30 -27 -37.5 -62.5t6.5 -65t49 -47t81 -11.5z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="2052" 
d="M1110 1106l178 -502l275 830h350v-43l-465 -1399h-309l-181 497l-176 -497h-309l-465 1399v43h350l275 -830l180 502h297zM844 1841h241l283 -258v-29h-319l-91 82l-86 -82h-323v23z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="1662" 
d="M784 438l-96 -311l-47 -127h-309l-352 1059v49h329l111 -334l74 -303l174 573h231l174 -573l74 303l110 334h330v-49l-352 -1059h-309l-49 131zM672 1544h241l283 -258v-29h-319l-91 82l-86 -82h-323v23z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="1501" 
d="M1368 1434v-37l-514 -817v-580h-350v578l-514 823v33h385l143 -230l162 -293l170 312l131 211h387zM543 1849h241l283 -258v-28h-319l-91 82l-86 -82h-323v22z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="1177" 
d="M539 473l82 281l137 354h334v-51q-526 -1333 -545 -1381h-334v50l143 364l-368 967v51h336l137 -354zM422 1550h242l282 -258v-28h-319l-90 82l-86 -82h-324v22z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="1480" 
d="M1348 1397v37h-387l-132 -211l-170 -312l-161 293l-144 230h-385v-33l514 -823v-578h351v580zM365 1870q81 0 130.5 -45t49.5 -119q0 -65 -50.5 -114.5t-129.5 -49.5q-71 0 -121.5 47.5t-50.5 116.5q0 76 49.5 120t122.5 44zM950 1870q80 0 129 -45.5t49 -118.5
q0 -64 -50.5 -114t-127.5 -50q-70 0 -122 50.5t-52 113.5q0 72 50.5 118t123.5 46z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="1333" 
d="M1128 1446v-256l-604 -866h613v-324h-1076v246l619 878h-619v322h1067zM965 1825v-23l-236 -252h-264v47l129 228h371z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="989" 
d="M25 0v195l407 620h-403v293h848v-178l-422 -625h426v-305h-856zM834 1481v-23l-236 -252h-264v47l129 228h371z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="1333" 
d="M1128 1446v-256l-604 -866h613v-324h-1076v246l619 878h-619v322h1067zM420 1741q0 84 56 131t132 47t131.5 -46t55.5 -132q0 -84 -56 -131t-133 -47q-74 0 -130 46.5t-56 131.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="989" 
d="M25 0v195l407 620h-403v293h848v-178l-422 -625h426v-305h-856zM291 1376q0 84 56 131t132 47t131.5 -46t55.5 -132q0 -84 -56 -131t-133 -47q-74 0 -130 46.5t-56 131.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="1333" 
d="M1128 1446v-256l-604 -866h613v-324h-1076v246l619 878h-619v322h1067zM750 1563h-242l-283 258v28h320l90 -82l86 82h323v-22z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="989" 
d="M25 0v195l407 620h-403v293h848v-178l-422 -625h426v-305h-856zM612 1225h-241l-283 258v28h320l90 -81l86 81h323v-22z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="1056" 
d="M350 1110v-1110h-350v1434h905v-324h-555z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="1112" 
d="M975 1438l-45 -283q-91 27 -160.5 -1.5t-79.5 -102.5h176l-43 -275h-182l-149 -782q-18 -101 -64.5 -176.5t-111 -117.5t-141.5 -59.5t-157.5 -8.5t-158.5 40l57 269q105 -36 160.5 -14.5t79.5 128.5l139 721h-160l43 275h168q18 124 73.5 214t138 135.5t189.5 57.5
t228 -20z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="1241" 
d="M410 -80h327q56 -189 -55 -319h-289q24 69 33.5 111.5t7.5 96.5t-24 111zM1077 981h-352q0 70 -51 108.5t-135 38.5q-83 0 -134 -33t-51 -91q0 -17 5.5 -31.5t17 -25t23 -19t30 -14t31.5 -9.5t33.5 -6.5t30 -3.5t27.5 -2.5t19 -1.5q60 -8 106 -17t105 -25t104 -40.5
t88 -58.5t73 -84.5t45 -112.5q10 -55 10 -100q0 -87 -32.5 -163.5t-87.5 -132t-127 -95.5t-153 -60t-163 -20q-73 0 -147 17t-147.5 56.5t-130 96.5t-91.5 144.5t-35 192.5h354q0 -50 19 -85t51 -51.5t63 -23.5t66 -7q93 0 150 28t57 86q0 18 -7.5 33.5t-18.5 26t-29.5 19.5
t-33 13.5t-37.5 9t-35 6t-33 3.5t-24 2q-58 6 -101.5 12.5t-99.5 20t-97.5 32t-85 49t-72.5 69.5t-49.5 95t-26.5 125v-2q-8 152 66.5 269t200.5 176t280 59q79 0 154 -17t144 -54.5t120.5 -92t82.5 -134t31 -175.5z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="1054" 
d="M328 -80h307q56 -189 -55 -319h-269q24 69 33.5 111.5t7.5 96.5t-24 111zM41 752q0 97 35.5 172t97.5 120.5t141.5 68t173.5 22.5q90 0 168.5 -23.5t139.5 -69t96.5 -119t36.5 -167.5h-305q-1 31 -14.5 50.5t-38.5 28t-46.5 11t-52.5 2.5q-44 0 -78.5 -18t-34.5 -58
q0 -20 12.5 -33t38.5 -18.5t40 -7t41 -2.5q90 -2 169 -23.5t146 -63.5t106 -114t39 -166q0 -81 -25.5 -145.5t-68.5 -106.5t-103 -70t-126.5 -39.5t-141.5 -11.5q-208 0 -333.5 99.5t-128.5 302.5h319q1 -52 45.5 -78.5t104.5 -26.5q62 -1 100.5 18.5t38.5 49.5q0 19 -10 33
t-23 21t-34.5 11.5t-36.5 5.5t-38 1.5t-30 1.5q-66 6 -123.5 19t-113.5 39t-95.5 63t-63.5 93.5t-24 127.5z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="1277" 
d="M387 -80h328q56 -188 -56 -319h-288q24 68 33.5 111t7 97t-24.5 111zM731 0h-350v1106h-387v328h1122v-328h-385v-1106z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="849" 
d="M305 -68h324q17 -57 18.5 -116.5t-17 -120t-57.5 -105.5h-284q23 66 33 116.5t7.5 109.5t-24.5 116zM737 -2q-573 -99 -573 385v436h-146v277h146v211l321 28v-239h232v-277h-232v-436q0 -49 23 -80t61 -40.5t77 -9t81 10.5z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="976" 
d="M317 1538h242l283 -258v-29h-320l-90 82l-86 -82h-323v23z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="1011" 
d="M549 1249h-242l-282 258v29h319l90 -82l86 82h324v-23z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="759" 
d="M377 1522h262q0 -94 -49 -164t-122 -102.5t-158.5 -33t-158.5 30t-122 101.5t-49 168h251q1 -25 16 -41.5t37 -20t43 1t36 21t14 39.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="645" 
d="M84 1489q0 84 56 131t132 47t131.5 -46t55.5 -132q0 -84 -56 -131t-133 -47q-74 0 -130 46.5t-56 131.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="622" 
d="M252 1290q-101 0 -172.5 60.5t-71.5 158.5q0 99 71.5 160.5t172.5 61.5q100 0 173 -61.5t73 -160.5q0 -98 -73 -158.5t-173 -60.5zM252 1561q-25 0 -42 -14.5t-17 -37.5q0 -22 16.5 -34.5t42.5 -12.5q61 0 61 47q0 23 -16.5 37.5t-44.5 14.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="1103" 
d="M764 -109l4 -6l-193 -204q-12 -1 -32 -2t-73 5t-95 22t-81 56.5t-48 100.5h2q-8 53 8 102.5t45 82t59 57t52 35.5l22 12l230 -54q-34 -21 -50.5 -51.5t-11.5 -59.5t23.5 -53t55.5 -36.5t83 -6.5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="886" 
d="M590 1769l178 -155q-62 -106 -139 -155t-160 -40q-34 5 -71 23t-62 33t-52 23.5t-54.5 -6t-57.5 -56.5l-184 145q64 108 143.5 157t161 39.5t162.5 -69.5q18 -13 34.5 -15t35 11.5t27 21.5t29.5 33q6 7 9 10z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="913" 
d="M387 1454l191 293h348l-242 -293h-297zM6 1454l176 293h326l-209 -293h-293z" />
    <glyph glyph-name="tonos" unicode="&#x384;" horiz-adv-x="739" 
d="M594 1634v-22l-244 -289h-264v47l137 264h371z" />
    <glyph glyph-name="dieresistonos" unicode="&#x385;" horiz-adv-x="1372" 
d="M57 1536q0 75 52 119.5t120 44.5q69 0 119.5 -43t50.5 -121q0 -77 -51 -119.5t-121 -42.5t-120 42t-50 120zM854 1536q0 75 52 119.5t120 44.5q69 0 119.5 -43t50.5 -121q0 -77 -51.5 -119.5t-120.5 -42.5q-70 0 -120 42t-50 120zM862 1671v23h-334l-110 -279v-47h264z
" />
    <glyph glyph-name="Alphatonos" unicode="&#x386;" horiz-adv-x="1521" 
d="M444 1411v23h-333l-111 -299v-48h264zM1012 0l-80 211h-490l-80 -211h-374l573 1450h250l596 -1450h-395zM688 872l-106 -303l-13 -32h238l-8 24z" />
    <glyph glyph-name="Epsilontonos" unicode="&#x388;" horiz-adv-x="1570" 
d="M426 1434v-23l-180 -324h-264v48l110 299h334zM821 547v-209h594v-338h-946v1434h944v-338h-592v-209h580v-340h-580z" />
    <glyph glyph-name="Etatonos" unicode="&#x389;" horiz-adv-x="1695" 
d="M424 1434v-23l-180 -324h-264v48l110 299h334zM805 1434v-541h377v541h350v-1434h-350v555h-377v-555h-350v1434h350z" />
    <glyph glyph-name="Iotatonos" unicode="&#x38a;" horiz-adv-x="1452" 
d="M434 1434v-23l-180 -324h-264v48l110 299h334zM1294 1434v-320h-237v-801h237v-313h-825v313h238v801h-238v320h825z" />
    <glyph glyph-name="Omicrontonos" unicode="&#x38c;" horiz-adv-x="1871" 
d="M434 1411v23h-334l-110 -299v-48h264zM240 715q0 207 99.5 377.5t269 267t372.5 96.5t373 -96.5t270 -267t100 -377.5q0 -153 -59 -290t-158.5 -234t-236.5 -154t-289 -57q-203 0 -372.5 95t-269 264t-99.5 376zM588 715q0 -98 33.5 -177t89 -127.5t125.5 -74.5t144 -26
t144 26.5t125.5 75t89 127.5t33.5 176q0 98 -33.5 177t-89 128t-125.5 75.5t-144 26.5t-144 -26.5t-125.5 -75t-89 -128t-33.5 -177.5z" />
    <glyph glyph-name="Upsilontonos" unicode="&#x38e;" horiz-adv-x="1964" 
d="M424 1434v-23l-180 -324h-264v48l110 299h334zM1831 1434v-37l-514 -817v-580h-350v578l-514 823v33h385l143 -230l162 -293l170 312l131 211h387z" />
    <glyph glyph-name="Omegatonos" unicode="&#x38f;" horiz-adv-x="1916" 
d="M430 1434v-23l-180 -324h-264v48l110 299h334zM989 1126q-167 0 -275 -114.5t-108 -284.5q0 -323 338 -459l-67 -268h-660v315h229q-90 72 -141 185t-51 227q0 212 93.5 378t261 257.5t380.5 91.5q161 0 298.5 -55.5t233 -152t149.5 -231t54 -288.5q0 -118 -47 -233
t-139 -181h225v-313h-653l-51 270q164 75 239.5 184.5t75.5 272.5q0 169 -108.5 284t-276.5 115z" />
    <glyph glyph-name="iotadieresistonos" unicode="&#x390;" horiz-adv-x="989" 
d="M-86 1671q0 75 52 119.5t120 44.5q69 0 119.5 -43t50.5 -121q0 -77 -51 -119.5t-121 -42.5t-120 42t-50 120zM688 1671q0 75 52 119.5t120 44.5q69 0 119.5 -43t50.5 -121q0 -77 -51.5 -119.5t-120.5 -42.5q-70 0 -120 42t-50 120zM696 1806v23h-311l-111 -279v-47h242z
M831 1114v320h-825v-320h238v-801h-238v-313h825v313h-237v801h237z" />
    <glyph glyph-name="Alpha" unicode="&#x391;" horiz-adv-x="1503" 
d="M993 0l-80 211h-489l-80 -211h-375l574 1450h250l596 -1450h-396zM670 872l-107 -303l-12 -32h237l-8 24z" />
    <glyph glyph-name="Beta" unicode="&#x392;" horiz-adv-x="1269" 
d="M346 1116v-201h264q34 0 57.5 20.5t29 50.5t0 59.5t-28 50t-54.5 20.5h-268zM0 1434h664q84 0 157 -34.5t121.5 -90t75 -126.5t25 -142t-36.5 -139.5t-101 -115.5q108 -55 160.5 -144t52.5 -187q0 -88 -30 -169t-86 -145t-145 -102.5t-198 -38.5h-659v1434zM346 592v-260
h313q35 0 61 19.5t37.5 48t11.5 62.5t-11.5 62.5t-37.5 48t-61 19.5h-313z" />
    <glyph glyph-name="Gamma" unicode="&#x393;" horiz-adv-x="1120" 
d="M369 1100v-1100h-351v1434h943v-334h-592z" />
    <glyph glyph-name="Delta" unicode="&#x394;" horiz-adv-x="1503" 
d="M1389 0h-1420l574 1450h250zM563 569l-98 -252h407l-92 244l-110 311z" />
    <glyph glyph-name="Epsilon" unicode="&#x395;" horiz-adv-x="1101" 
d="M352 547v-209h594v-338h-946v1434h944v-338h-592v-209h580v-340h-580z" />
    <glyph glyph-name="Zeta" unicode="&#x396;" horiz-adv-x="1300" 
d="M1118 1434v-256l-604 -854h612v-324h-1075v246l619 866h-619v322h1067z" />
    <glyph glyph-name="Eta" unicode="&#x397;" horiz-adv-x="1241" 
d="M350 1434v-541h377v541h350v-1434h-350v555h-377v-555h-350v1434h350z" />
    <glyph glyph-name="Theta" unicode="&#x398;" horiz-adv-x="1622" 
d="M-10 715q0 207 99.5 377.5t269 267t372.5 96.5t373.5 -96.5t270.5 -267t100 -377.5q0 -153 -59 -290t-159 -234t-237 -154t-289 -57q-203 0 -372.5 95t-269 264t-99.5 376zM338 715q0 -98 33.5 -177t89 -127.5t125.5 -74.5t144 -26t144 26.5t125.5 75t89 127.5t33.5 176
q0 98 -33.5 177t-89 128t-125.5 75.5t-144 26.5t-144 -26.5t-125.5 -75t-89 -128t-33.5 -177.5zM446 569v285h574v-285h-574z" />
    <glyph glyph-name="Iota" unicode="&#x399;" horiz-adv-x="989" 
d="M831 1434v-320h-237v-801h237v-313h-825v313h238v801h-238v320h825z" />
    <glyph glyph-name="Kappa" unicode="&#x39a;" horiz-adv-x="1333" 
d="M1163 1434v-21l-508 -692l555 -703v-18h-434l-426 555v-555h-350v1434h350v-557l398 557h415z" />
    <glyph glyph-name="Lambda" unicode="&#x39b;" horiz-adv-x="1503" 
d="M993 0l-213 561l-110 311l-107 -303l-219 -569h-375l574 1450h250l596 -1450h-396z" />
    <glyph glyph-name="Mu" unicode="&#x39c;" horiz-adv-x="1470" 
d="M350 713v-713h-350v1434h279l376 -555l377 555h275v-1434h-349v709l-206 -306h-195z" />
    <glyph glyph-name="Nu" unicode="&#x39d;" horiz-adv-x="1275" 
d="M348 0h-348v1434h270l496 -717v717h346v-1434h-274l-490 698v-698z" />
    <glyph glyph-name="Xi" unicode="&#x39e;" horiz-adv-x="1126" 
d="M967 1098h-963v336h963v-336zM4 334h963v-334h-963v334zM932 551h-903v332h903v-332z" />
    <glyph glyph-name="Omicron" unicode="&#x39f;" horiz-adv-x="1622" 
d="M-10 715q0 207 99.5 377.5t269 267t372.5 96.5t373.5 -96.5t270.5 -267t100 -377.5q0 -153 -59 -290t-159 -234t-237 -154t-289 -57q-203 0 -372.5 95t-269 264t-99.5 376zM338 715q0 -98 33.5 -177t89 -127.5t125.5 -74.5t144 -26t144 26.5t125.5 75t89 127.5t33.5 176
q0 98 -33.5 177t-89 128t-125.5 75.5t-144 26.5t-144 -26.5t-125.5 -75t-89 -128t-33.5 -177.5z" />
    <glyph glyph-name="Pi" unicode="&#x3a0;" horiz-adv-x="1241" 
d="M0 1434h1077v-1434h-350v1096h-377v-1096h-350v1434z" />
    <glyph glyph-name="Rho" unicode="&#x3a1;" horiz-adv-x="1196" 
d="M0 1434h549q121 0 219.5 -43.5t159 -114t93 -161.5t32.5 -185t-33 -185t-93.5 -161.5t-158.5 -114t-219 -43.5h-199v-426h-350v1434zM350 756h199q45 0 80 18.5t53 48.5t26.5 66t0 72t-26.5 66t-53 48.5t-80 18.5h-199v-338z" />
    <glyph glyph-name="Sigma" unicode="&#x3a3;" horiz-adv-x="1284" 
d="M743 799v-103l-288 -381h639v-315h-990l-129 178l394 567l-379 482l104 207h993v-320h-604z" />
    <glyph glyph-name="Tau" unicode="&#x3a4;" horiz-adv-x="1277" 
d="M731 0h-350v1106h-387v328h1122v-328h-385v-1106z" />
    <glyph glyph-name="Upsilon" unicode="&#x3a5;" horiz-adv-x="1480" 
d="M1348 1434v-37l-514 -817v-580h-351v578l-514 823v33h385l144 -230l161 -293l170 312l132 211h387z" />
    <glyph glyph-name="Phi" unicode="&#x3a6;" horiz-adv-x="1589" 
d="M549 1311v129h350v-127q105 -14 194.5 -53t153.5 -96t108.5 -128.5t66.5 -150.5t22 -162q0 -105 -35.5 -203t-102.5 -179t-172 -138t-235 -76v-127h-350v127q-132 18 -237.5 74t-172.5 137t-102 178.5t-35 202.5t35.5 202t102.5 178t172.5 137t236.5 75zM895 999v-557
q98 29 148.5 101t50.5 174q0 238 -199 282zM352 721q0 -106 49.5 -180t151.5 -99v559q-101 -30 -151 -103t-50 -177z" />
    <glyph glyph-name="Chi" unicode="&#x3a7;" horiz-adv-x="1427" 
d="M1284 1434v-33l-459 -674l461 -698v-29h-377l-282 436l-279 -436h-377v29l459 698l-453 666v41h369l281 -408l284 408h373z" />
    <glyph glyph-name="Psi" unicode="&#x3a8;" horiz-adv-x="1562" 
d="M530 602v832h351v-838q170 0 170 266v572h354v-572q0 -89 -18.5 -173t-59 -162t-100.5 -136t-149 -92.5t-197 -34.5v-264h-351v264q-109 0 -198 35t-148 92.5t-99.5 135.5t-58.5 161.5t-18 171.5v574h350v-572q0 -68 15 -123.5t55.5 -96t101.5 -40.5z" />
    <glyph glyph-name="Omega" unicode="&#x3a9;" horiz-adv-x="1691" 
d="M764 1126q-167 0 -275 -114.5t-108 -284.5q0 -323 338 -459l-68 -268h-659v315h229q-90 72 -141 185t-51 227q0 212 93.5 378t261 257.5t380.5 91.5q161 0 298.5 -55.5t233 -152t149.5 -231t54 -288.5q0 -118 -47 -233t-139 -181h225v-313h-653l-51 270
q164 75 239.5 184.5t75.5 272.5q0 169 -108.5 284t-276.5 115z" />
    <glyph glyph-name="Iotadieresis" unicode="&#x3aa;" horiz-adv-x="989" 
d="M35 1671q0 75 52 119.5t120 44.5q69 0 119.5 -43t50.5 -121q0 -77 -51 -119.5t-121 -42.5t-120 42t-50 120zM498 1671q0 75 52 119.5t120 44.5q69 0 119.5 -43t50.5 -121q0 -77 -51.5 -119.5t-120.5 -42.5q-70 0 -120 42t-50 120zM831 1114v320h-825v-320h238v-801h-238
v-313h825v313h-237v801h237z" />
    <glyph glyph-name="Upsilondieresis" unicode="&#x3ab;" horiz-adv-x="1480" 
d="M256 1671q0 75 52 119.5t120 44.5q69 0 119.5 -43t50.5 -121q0 -77 -51 -119.5t-121 -42.5t-120 42t-50 120zM719 1671q0 75 52 119.5t120 44.5q69 0 119.5 -43t50.5 -121q0 -77 -51.5 -119.5t-120.5 -42.5q-70 0 -120 42t-50 120zM1348 1397v37h-387l-132 -211
l-170 -312l-161 293l-144 230h-385v-33l514 -823v-578h351v580z" />
    <glyph glyph-name="alphatonos" unicode="&#x3ac;" horiz-adv-x="1523" 
d="M442 1411v23h-333l-111 -299v-48h264zM1014 0l-80 211h-490l-79 -211h-375l573 1450h250l596 -1450h-395zM690 872l-106 -303l-13 -32h238l-8 24z" />
    <glyph glyph-name="epsilontonos" unicode="&#x3ad;" horiz-adv-x="1556" 
d="M422 1434v-23l-180 -324h-265v48l111 299h334zM807 547v-209h594v-338h-946v1434h944v-338h-592v-209h579v-340h-579z" />
    <glyph glyph-name="etatonos" unicode="&#x3ae;" horiz-adv-x="1716" 
d="M430 1434v-23l-180 -324h-264v48l110 299h334zM825 1434v-541h377v541h350v-1434h-350v555h-377v-555h-350v1434h350z" />
    <glyph glyph-name="iotatonos" unicode="&#x3af;" horiz-adv-x="1476" 
d="M463 1434v-23l-180 -324h-265v48l111 299h334zM1319 1434v-320h-238v-801h238v-313h-825v313h237v801h-237v320h825z" />
    <glyph glyph-name="upsilondieresistonos" unicode="&#x3b0;" horiz-adv-x="1480" 
d="M127 1671q0 75 52 119.5t120 44.5q69 0 119.5 -43t50.5 -121q0 -77 -51 -119.5t-121 -42.5t-120 42t-50 120zM924 1671q0 75 52 119.5t120 44.5q69 0 119.5 -43t50.5 -121q0 -77 -51.5 -119.5t-120.5 -42.5q-70 0 -120 42t-50 120zM932 1806v23h-334l-111 -279v-47h265z
M1348 1397v37h-387l-132 -211l-170 -312l-161 293l-144 230h-385v-33l514 -823v-578h351v580z" />
    <glyph glyph-name="alpha" unicode="&#x3b1;" horiz-adv-x="1503" 
d="M993 0l-80 211h-489l-80 -211h-375l574 1450h250l596 -1450h-396zM670 872l-107 -303l-12 -32h237l-8 24z" />
    <glyph glyph-name="beta" unicode="&#x3b2;" horiz-adv-x="1269" 
d="M346 1116v-201h264q34 0 57.5 20.5t29 50.5t0 59.5t-28 50t-54.5 20.5h-268zM0 1434h664q84 0 157 -34.5t121.5 -90t75 -126.5t25 -142t-36.5 -139.5t-101 -115.5q108 -55 160.5 -144t52.5 -187q0 -88 -30 -169t-86 -145t-145 -102.5t-198 -38.5h-659v1434zM346 592v-260
h313q35 0 61 19.5t37.5 48t11.5 62.5t-11.5 62.5t-37.5 48t-61 19.5h-313z" />
    <glyph glyph-name="gamma" unicode="&#x3b3;" horiz-adv-x="1120" 
d="M369 1100v-1100h-351v1434h943v-334h-592z" />
    <glyph glyph-name="delta" unicode="&#x3b4;" horiz-adv-x="1503" 
d="M1389 0h-1420l574 1450h250zM563 569l-98 -252h407l-92 244l-110 311z" />
    <glyph glyph-name="epsilon" unicode="&#x3b5;" horiz-adv-x="1101" 
d="M352 547v-209h594v-338h-946v1434h944v-338h-592v-209h580v-340h-580z" />
    <glyph glyph-name="zeta" unicode="&#x3b6;" horiz-adv-x="1300" 
d="M1118 1434v-256l-604 -854h612v-324h-1075v246l619 866h-619v322h1067z" />
    <glyph glyph-name="eta" unicode="&#x3b7;" horiz-adv-x="1241" 
d="M350 1434v-541h377v541h350v-1434h-350v555h-377v-555h-350v1434h350z" />
    <glyph glyph-name="theta" unicode="&#x3b8;" horiz-adv-x="1622" 
d="M-10 715q0 207 99.5 377.5t269 267t372.5 96.5t373.5 -96.5t270.5 -267t100 -377.5q0 -153 -59 -290t-159 -234t-237 -154t-289 -57q-203 0 -372.5 95t-269 264t-99.5 376zM338 715q0 -98 33.5 -177t89 -127.5t125.5 -74.5t144 -26t144 26.5t125.5 75t89 127.5t33.5 176
q0 98 -33.5 177t-89 128t-125.5 75.5t-144 26.5t-144 -26.5t-125.5 -75t-89 -128t-33.5 -177.5zM446 569v285h574v-285h-574z" />
    <glyph glyph-name="iota" unicode="&#x3b9;" horiz-adv-x="989" 
d="M831 1434v-320h-237v-801h237v-313h-825v313h238v801h-238v320h825z" />
    <glyph glyph-name="kappa" unicode="&#x3ba;" horiz-adv-x="1333" 
d="M1163 1434v-21l-508 -692l555 -703v-18h-434l-426 555v-555h-350v1434h350v-557l398 557h415z" />
    <glyph glyph-name="lambda" unicode="&#x3bb;" horiz-adv-x="1503" 
d="M993 0l-213 561l-110 311l-107 -303l-219 -569h-375l574 1450h250l596 -1450h-396z" />
    <glyph glyph-name="mu" unicode="&#x3bc;" horiz-adv-x="1470" 
d="M350 713v-713h-350v1434h279l376 -555l377 555h275v-1434h-349v709l-206 -306h-195z" />
    <glyph glyph-name="nu" unicode="&#x3bd;" horiz-adv-x="1275" 
d="M348 0h-348v1434h270l496 -717v717h346v-1434h-274l-490 698v-698z" />
    <glyph glyph-name="xi" unicode="&#x3be;" horiz-adv-x="1126" 
d="M967 1098h-963v336h963v-336zM4 334h963v-334h-963v334zM932 551h-903v332h903v-332z" />
    <glyph glyph-name="omicron" unicode="&#x3bf;" horiz-adv-x="1622" 
d="M-10 715q0 207 99.5 377.5t269 267t372.5 96.5t373.5 -96.5t270.5 -267t100 -377.5q0 -153 -59 -290t-159 -234t-237 -154t-289 -57q-203 0 -372.5 95t-269 264t-99.5 376zM338 715q0 -98 33.5 -177t89 -127.5t125.5 -74.5t144 -26t144 26.5t125.5 75t89 127.5t33.5 176
q0 98 -33.5 177t-89 128t-125.5 75.5t-144 26.5t-144 -26.5t-125.5 -75t-89 -128t-33.5 -177.5z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="1241" 
d="M0 1434h1077v-1434h-350v1096h-377v-1096h-350v1434z" />
    <glyph glyph-name="rho" unicode="&#x3c1;" horiz-adv-x="1196" 
d="M0 1434h549q121 0 219.5 -43.5t159 -114t93 -161.5t32.5 -185t-33 -185t-93.5 -161.5t-158.5 -114t-219 -43.5h-199v-426h-350v1434zM350 756h199q45 0 80 18.5t53 48.5t26.5 66t0 72t-26.5 66t-53 48.5t-80 18.5h-199v-338z" />
    <glyph glyph-name="sigma1" unicode="&#x3c2;" horiz-adv-x="1607" 
d="M336 715q0 -91 29 -166t77.5 -123.5t111.5 -78.5t130.5 -36.5t135.5 9t125 50.5t101 95t62 137h354q-26 -184 -129.5 -326.5t-261 -219t-342.5 -76.5q-203 0 -372.5 95t-269 264t-99.5 376t99.5 377.5t269 267t372.5 96.5q137 0 263.5 -45t224 -125t162 -196t81.5 -252
h-354q-19 76 -63.5 135t-101.5 93t-125 48t-135 7t-129.5 -37.5t-110 -79t-76.5 -123.5t-29 -166z" />
    <glyph glyph-name="sigma" unicode="&#x3c3;" horiz-adv-x="1284" 
d="M743 799v-103l-288 -381h639v-315h-990l-129 178l394 567l-379 482l104 207h993v-320h-604z" />
    <glyph glyph-name="tau" unicode="&#x3c4;" horiz-adv-x="1277" 
d="M731 0h-350v1106h-387v328h1122v-328h-385v-1106z" />
    <glyph glyph-name="upsilon" unicode="&#x3c5;" horiz-adv-x="1480" 
d="M1348 1434v-37l-514 -817v-580h-351v578l-514 823v33h385l144 -230l161 -293l170 312l132 211h387z" />
    <glyph glyph-name="phi" unicode="&#x3c6;" horiz-adv-x="1589" 
d="M549 1311v129h350v-127q105 -14 194.5 -53t153.5 -96t108.5 -128.5t66.5 -150.5t22 -162q0 -105 -35.5 -203t-102.5 -179t-172 -138t-235 -76v-127h-350v127q-132 18 -237.5 74t-172.5 137t-102 178.5t-35 202.5t35.5 202t102.5 178t172.5 137t236.5 75zM895 999v-557
q98 29 148.5 101t50.5 174q0 238 -199 282zM352 721q0 -106 49.5 -180t151.5 -99v559q-101 -30 -151 -103t-50 -177z" />
    <glyph glyph-name="chi" unicode="&#x3c7;" horiz-adv-x="1427" 
d="M1284 1434v-33l-459 -674l461 -698v-29h-377l-282 436l-279 -436h-377v29l459 698l-453 666v41h369l281 -408l284 408h373z" />
    <glyph glyph-name="psi" unicode="&#x3c8;" horiz-adv-x="1562" 
d="M530 602v832h351v-838q170 0 170 266v572h354v-572q0 -89 -18.5 -173t-59 -162t-100.5 -136t-149 -92.5t-197 -34.5v-264h-351v264q-109 0 -198 35t-148 92.5t-99.5 135.5t-58.5 161.5t-18 171.5v574h350v-572q0 -68 15 -123.5t55.5 -96t101.5 -40.5z" />
    <glyph glyph-name="omega" unicode="&#x3c9;" horiz-adv-x="1691" 
d="M764 1126q-167 0 -275 -114.5t-108 -284.5q0 -323 338 -459l-68 -268h-659v315h229q-90 72 -141 185t-51 227q0 212 93.5 378t261 257.5t380.5 91.5q161 0 298.5 -55.5t233 -152t149.5 -231t54 -288.5q0 -118 -47 -233t-139 -181h225v-313h-653l-51 270
q164 75 239.5 184.5t75.5 272.5q0 169 -108.5 284t-276.5 115z" />
    <glyph glyph-name="iotadieresis" unicode="&#x3ca;" horiz-adv-x="989" 
d="M203 1843q79 0 128.5 -47.5t49.5 -124.5q0 -66 -50.5 -118t-127.5 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5zM664 1843q79 0 128.5 -47.5t49.5 -124.5q0 -66 -50.5 -118t-127.5 -52q-70 0 -122.5 52.5t-52.5 117.5q0 75 51 123.5t124 48.5zM831 1114
v320h-825v-320h238v-801h-238v-313h825v313h-237v801h237z" />
    <glyph glyph-name="upsilondieresis" unicode="&#x3cb;" horiz-adv-x="1480" 
d="M434 1837q79 0 128.5 -47.5t49.5 -124.5q0 -66 -50.5 -118t-127.5 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5zM879 1837q79 0 128.5 -47.5t49.5 -124.5q0 -66 -50.5 -118t-127.5 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5zM1348 1397
v37h-387l-132 -211l-170 -312l-161 293l-144 230h-385v-33l514 -823v-578h351v580z" />
    <glyph glyph-name="omicrontonos" unicode="&#x3cc;" horiz-adv-x="1622" 
d="M-10 715q0 207 99.5 377.5t269 267t372.5 96.5t373.5 -96.5t270.5 -267t100 -377.5q0 -153 -59 -290t-159 -234t-237 -154t-289 -57q-203 0 -372.5 95t-269 264t-99.5 376zM338 715q0 -98 33.5 -177t89 -127.5t125.5 -74.5t144 -26t144 26.5t125.5 75t89 127.5t33.5 176
q0 98 -33.5 177t-89 128t-125.5 75.5t-144 26.5t-144 -26.5t-125.5 -75t-89 -128t-33.5 -177.5zM1077 1808v-22l-235 -252h-264v47l129 227h370z" />
    <glyph glyph-name="upsilontonos" unicode="&#x3cd;" horiz-adv-x="1480" 
d="M1010 1808v-22l-236 -252h-264v47l129 227h371zM1348 1434v-37l-514 -817v-580h-351v578l-514 823v33h385l144 -230l161 -293l170 312l132 211h387z" />
    <glyph glyph-name="omegatonos" unicode="&#x3ce;" horiz-adv-x="1691" 
d="M1126 1808v-22l-235 -252h-264v47l129 227h370zM764 1126q-167 0 -275 -114.5t-108 -284.5q0 -323 338 -459l-68 -268h-659v315h229q-90 72 -141 185t-51 227q0 212 93.5 378t261 257.5t380.5 91.5q161 0 298.5 -55.5t233 -152t149.5 -231t54 -288.5q0 -118 -47 -233
t-139 -181h225v-313h-653l-51 270q164 75 239.5 184.5t75.5 272.5q0 169 -108.5 284t-276.5 115z" />
    <glyph glyph-name="afii10023" unicode="&#x401;" horiz-adv-x="1101" 
d="M274 1839q80 0 129.5 -48t49.5 -124q0 -66 -51 -118t-128 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5zM719 1839q79 0 128.5 -47.5t49.5 -124.5q0 -66 -50.5 -118t-127.5 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5zM352 338v209h580
v340h-580v209h592v338h-944v-1434h946v338h-594z" />
    <glyph glyph-name="afii10051" unicode="&#x402;" horiz-adv-x="1497" 
d="M711 0h-351v1106h-387v328h1123v-328h-385v-117q73 20 147.5 16t144.5 -22t131 -59.5t106.5 -95t72 -129t26.5 -162.5v-86q2 -245 -129 -349t-442 -104v295q221 0 221 158v88q0 55 -29.5 93t-72.5 52.5t-91 12.5t-85 -21v-676z" />
    <glyph glyph-name="afii10052" unicode="&#x403;" horiz-adv-x="1107" 
d="M807 1798v-22l-236 -252h-264v47l129 227h371zM369 1100v-1100h-351v1434h943v-334h-592z" />
    <glyph glyph-name="afii10053" unicode="&#x404;" horiz-adv-x="1617" 
d="M936 870v-299h-569q28 -87 89.5 -148.5t136.5 -88.5t159 -24t157 35t130 99t79 158h355q-26 -184 -129.5 -326.5t-261 -219t-343.5 -76.5q-203 0 -372.5 95t-269 264t-99.5 376t99.5 377.5t269 267t372.5 96.5q137 0 263.5 -45t224 -125t162 -196t81.5 -252h-354
q-23 89 -79.5 154t-128 96t-154 33.5t-156 -23.5t-135 -85t-90.5 -143h563z" />
    <glyph glyph-name="afii10054" unicode="&#x405;" horiz-adv-x="1241" 
d="M1077 981h-352q0 70 -51 108.5t-135 38.5q-83 0 -134 -33t-51 -91q0 -17 5.5 -31.5t17 -25t23 -19t30 -14t31.5 -9.5t33.5 -6.5t30 -3.5t27.5 -2.5t19 -1.5q60 -8 106 -17t105 -25t104 -40.5t88 -58.5t73 -84.5t45 -112.5q10 -55 10 -100q0 -87 -32.5 -163.5t-87.5 -132
t-127 -95.5t-153 -60t-163 -20q-73 0 -147 17t-147.5 56.5t-130 96.5t-91.5 144.5t-35 192.5h354q0 -50 19 -85t51 -51.5t63 -23.5t66 -7q93 0 150 28t57 86q0 18 -7.5 33.5t-18.5 26t-29.5 19.5t-33 13.5t-37.5 9t-35 6t-33 3.5t-24 2q-58 6 -101.5 12.5t-99.5 20t-97.5 32
t-85 49t-72.5 69.5t-49.5 95t-26.5 125v-2q-8 152 66.5 269t200.5 176t280 59q79 0 154 -17t144 -54.5t120.5 -92t82.5 -134t31 -175.5z" />
    <glyph glyph-name="afii10055" unicode="&#x406;" horiz-adv-x="989" 
d="M831 1434v-320h-237v-801h237v-313h-825v313h238v801h-238v320h825z" />
    <glyph glyph-name="afii10056" unicode="&#x407;" horiz-adv-x="989" 
d="M190 1851q80 0 130.5 -49t50.5 -125q0 -67 -52 -118.5t-129 -51.5q-71 0 -123.5 52t-52.5 118q0 75 51.5 124.5t124.5 49.5zM645 1851q81 0 130.5 -48.5t49.5 -125.5q0 -68 -50.5 -119t-129.5 -51q-71 0 -122.5 52t-51.5 118q0 75 50.5 124.5t123.5 49.5zM831 1114v320
h-825v-320h238v-801h-238v-313h825v313h-237v801h237z" />
    <glyph glyph-name="afii10057" unicode="&#x408;" horiz-adv-x="999" 
d="M836 1434v-803q0 -231 -83.5 -368t-266 -200t-490.5 -63v328q190 0 296.5 30.5t150.5 94.5t44 178v481h-346v322h695z" />
    <glyph glyph-name="afii10058" unicode="&#x409;" horiz-adv-x="1906" 
d="M1253 0h-510v821l-378 -821h-388l672 1434h447v-490h157q95 0 176.5 -26.5t138 -71.5t97 -105.5t59.5 -128t19 -139.5q0 -90 -31.5 -173.5t-90.5 -151t-154.5 -108t-213.5 -40.5zM1253 606h-157v-270h157q64 0 101 40t37 95t-37 95t-101 40z" />
    <glyph glyph-name="afii10059" unicode="&#x40a;" horiz-adv-x="1847" 
d="M1198 0h-510v606h-303v-606h-348v1434h348v-490h303v490h352v-490h158q95 0 176 -26.5t138 -71.5t97 -105t59.5 -128t19.5 -140q0 -90 -31.5 -173.5t-90.5 -151t-154.5 -108t-213.5 -40.5zM1198 606h-158v-270h158q64 0 100.5 40t36.5 95t-36.5 95t-100.5 40z" />
    <glyph glyph-name="afii10060" unicode="&#x40b;" horiz-adv-x="1519" 
d="M1358 0h-350v510q0 51 -29 89t-71.5 54t-91 15t-87.5 -21v-647h-350v1106h-387v328h1122v-328h-385v-133q78 20 154.5 14.5t146.5 -26t130 -64t104 -99t69 -131t25 -159.5v-508z" />
    <glyph glyph-name="afii10061" unicode="&#x40c;" horiz-adv-x="1333" 
d="M893 1788v-23l-236 -252h-264v48l129 227h371zM1163 1434v-21l-508 -692l555 -703v-18h-434l-426 555v-555h-350v1434h350v-557l398 557h415z" />
    <glyph glyph-name="afii10062" unicode="&#x40e;" horiz-adv-x="1339" 
d="M588 -2h-379l213 487q-79 165 -461 949h389l148 -301l114 -297l101 294l131 304h389zM307 1776h588v-271h-588v271z" />
    <glyph glyph-name="afii10145" unicode="&#x40f;" horiz-adv-x="1241" 
d="M369 -211v211h-369v1434h352v-1100h373v1100h352v-1434h-362v-211h-346z" />
    <glyph glyph-name="bgA" unicode="&#x410;" horiz-adv-x="1503" 
d="M993 0l-80 211h-489l-80 -211h-375l574 1450h250l596 -1450h-396zM670 872l-107 -303l-12 -32h237l-8 24z" />
    <glyph glyph-name="afii10018" unicode="&#x411;" horiz-adv-x="1280" 
d="M1038 1434v-334h-665v-178h313q136 0 241 -64t158.5 -167.5t53.5 -227.5q0 -88 -29.5 -170t-84.5 -148t-143 -105.5t-196 -39.5h-659v1434h1011zM373 594v-262h313q44 0 74.5 27t37 65.5t-1 77t-37.5 65.5t-73 27h-313z" />
    <glyph glyph-name="afii10019" unicode="&#x412;" horiz-adv-x="1269" 
d="M346 1116v-201h264q34 0 57.5 20.5t29 50.5t0 59.5t-28 50t-54.5 20.5h-268zM0 1434h664q84 0 157 -34.5t121.5 -90t75 -126.5t25 -142t-36.5 -139.5t-101 -115.5q109 -56 161 -141t52 -182q0 -88 -30 -170t-86 -148t-145 -105.5t-198 -39.5h-659v1434zM346 592v-260h313
q35 0 61 19.5t37.5 48t11.5 62.5t-11.5 62.5t-37.5 48t-61 19.5h-313z" />
    <glyph glyph-name="afii10020" unicode="&#x413;" horiz-adv-x="1107" 
d="M369 1100v-1100h-351v1434h943v-334h-592z" />
    <glyph glyph-name="afii10021" unicode="&#x414;" horiz-adv-x="1427" 
d="M1176 328h104v-533h-330v205h-618v-205h-344v494l141 33q56 79 74 329l53 783h920v-1106zM489 328h334v774h-248q-27 -487 -36 -573q-15 -131 -50 -201z" />
    <glyph glyph-name="afii10022" unicode="&#x415;" horiz-adv-x="1101" 
d="M352 547v-209h594v-338h-946v1434h944v-338h-592v-209h580v-340h-580z" />
    <glyph glyph-name="afii10024" unicode="&#x416;" horiz-adv-x="2009" 
d="M14 1413v21h408l330 -557v557h350v-557l330 557h407v-21l-444 -692l485 -703v-18h-420l-358 555v-555h-350v555l-359 -555h-420v18l486 703z" />
    <glyph glyph-name="afii10025" unicode="&#x417;" horiz-adv-x="1216" 
d="M-4 479h323q0 -106 98 -149q83 -35 179 -8q91 26 117 89q8 19 8 40q0 52 -40 94.5t-116 42.5h-217v295h217q49 0 81 37t32 81q0 50 -37 84.5t-90 42t-106.5 -1.5t-90.5 -48t-37 -97h-309q0 231 164 364q135 109 328 116q190 7 334 -87q162 -106 188 -296q4 -32 4 -66
q0 -80 -33.5 -152.5t-109.5 -111.5q190 -95 186 -318q-1 -85 -37.5 -164.5t-104.5 -144.5t-177 -104.5t-242 -39.5q-129 0 -229.5 41t-161 111.5t-91 159t-30.5 190.5z" />
    <glyph glyph-name="afii10026" unicode="&#x418;" horiz-adv-x="1275" 
d="M1116 0h-348v727l-530 -727h-234v1434h346v-725l522 725h244v-1434z" />
    <glyph glyph-name="afii10027" unicode="&#x419;" horiz-adv-x="1275" 
d="M332 1788h590v-287h-590v287zM1116 0h-348v727l-530 -727h-234v1434h346v-725l522 725h244v-1434z" />
    <glyph glyph-name="afii10028" unicode="&#x41a;" horiz-adv-x="1372" 
d="M1202 1434v-21l-508 -692l555 -703v-18h-434l-426 555v-555h-350v1434h350v-557l397 557h416z" />
    <glyph glyph-name="afii10029" unicode="&#x41b;" horiz-adv-x="1503" 
d="M993 0l-213 561l-110 311l-107 -303l-219 -569h-375l574 1450h250l596 -1450h-396z" />
    <glyph glyph-name="afii10030" unicode="&#x41c;" horiz-adv-x="1470" 
d="M350 713v-713h-350v1434h279l376 -555l377 555h275v-1434h-349v709l-206 -306h-195z" />
    <glyph glyph-name="afii10031" unicode="&#x41d;" horiz-adv-x="1241" 
d="M350 1434v-541h377v541h350v-1434h-350v555h-377v-555h-350v1434h350z" />
    <glyph glyph-name="afii10032" unicode="&#x41e;" horiz-adv-x="1622" 
d="M-10 715q0 207 99.5 377.5t269 267t372.5 96.5t373.5 -96.5t270.5 -267t100 -377.5q0 -153 -59 -290t-159 -234t-237 -154t-289 -57q-203 0 -372.5 95t-269 264t-99.5 376zM338 715q0 -98 33.5 -177t89 -127.5t125.5 -74.5t144 -26t144 26.5t125.5 75t89 127.5t33.5 176
q0 98 -33.5 177t-89 128t-125.5 75.5t-144 26.5t-144 -26.5t-125.5 -75t-89 -128t-33.5 -177.5z" />
    <glyph glyph-name="afii10033" unicode="&#x41f;" horiz-adv-x="1241" 
d="M0 1434h1077v-1434h-350v1096h-377v-1096h-350v1434z" />
    <glyph glyph-name="afii10034" unicode="&#x420;" horiz-adv-x="1196" 
d="M0 1434h549q121 0 219.5 -43.5t159 -114t93 -161.5t32.5 -185t-33 -185t-93.5 -161.5t-158.5 -114t-219 -43.5h-199v-426h-350v1434zM350 756h199q45 0 80 18.5t53 48.5t26.5 66t0 72t-26.5 66t-53 48.5t-80 18.5h-199v-338z" />
    <glyph glyph-name="afii10035" unicode="&#x421;" horiz-adv-x="1607" 
d="M336 715q0 -91 29 -166t77.5 -123.5t111.5 -78.5t130.5 -36.5t135.5 9t125 50.5t101 95t62 137h354q-26 -184 -129.5 -326.5t-261 -219t-342.5 -76.5q-203 0 -372.5 95t-269 264t-99.5 376t99.5 377.5t269 267t372.5 96.5q137 0 263.5 -45t224 -125t162 -196t81.5 -252
h-354q-19 76 -63.5 135t-101.5 93t-125 48t-135 7t-129.5 -37.5t-110 -79t-76.5 -123.5t-29 -166z" />
    <glyph glyph-name="afii10036" unicode="&#x422;" horiz-adv-x="1277" 
d="M731 0h-350v1106h-387v328h1122v-328h-385v-1106z" />
    <glyph glyph-name="afii10037" unicode="&#x423;" horiz-adv-x="1366" 
d="M596 -2h-379l213 487l-461 949h389l146 -299l117 -279l100 274l131 304h389z" />
    <glyph glyph-name="afii10038" unicode="&#x424;" horiz-adv-x="1589" 
d="M549 1311v129h350v-127q105 -14 194.5 -53t153.5 -96t108.5 -128.5t66.5 -150.5t22 -162q0 -105 -35.5 -203t-102.5 -179t-172 -138t-235 -76v-127h-350v127q-132 18 -237.5 74t-172.5 137t-102 178.5t-35 202.5t35.5 202t102.5 178t172.5 137t236.5 75zM895 999v-557
q98 29 148.5 101t50.5 174q0 238 -199 282zM352 721q0 -106 49.5 -180t151.5 -99v559q-101 -30 -151 -103t-50 -177z" />
    <glyph glyph-name="afii10039" unicode="&#x425;" horiz-adv-x="1427" 
d="M1284 1434v-33l-459 -674l461 -698v-29h-377l-282 436l-279 -436h-377v29l459 698l-453 666v41h369l281 -408l284 408h373z" />
    <glyph glyph-name="afii10040" unicode="&#x426;" horiz-adv-x="1337" 
d="M1085 317h105v-577h-330v260h-837v1434h352v-1117h356v1117h354v-1117z" />
    <glyph glyph-name="afii10041" unicode="&#x427;" horiz-adv-x="1212" 
d="M1042 1434v-1434h-356v442q-33 -6 -94 -6q-90 0 -172 17.5t-155.5 56.5t-127.5 97t-85.5 144t-31.5 191v492h359v-492q0 -118 88 -164.5t219 -21.5v678h356z" />
    <glyph glyph-name="afii10042" unicode="&#x428;" horiz-adv-x="1824" 
d="M35 1434h360v-1112h260v1112h359v-1112h258v1112h360v-1434h-1597v1434z" />
    <glyph glyph-name="afii10043" unicode="&#x429;" horiz-adv-x="1908" 
d="M39 1434h358v-1115h256v1115h361v-1115h260v1115h360v-1115h111v-579h-336v260h-1370v1434z" />
    <glyph glyph-name="afii10044" unicode="&#x42a;" horiz-adv-x="1378" 
d="M4 1100v334h524v-449h199q121 0 217.5 -39t156.5 -106.5t92 -153t32 -182.5q0 -96 -32 -185t-92 -161t-157 -115t-217 -43h-549v1100h-174zM727 674h-199v-342h199q71 0 111.5 52.5t40.5 121.5q0 67 -40.5 117.5t-111.5 50.5z" />
    <glyph glyph-name="afii10045" unicode="&#x42b;" horiz-adv-x="1650" 
d="M551 0h-551v1434h352v-426h199q120 0 217 -42.5t157 -114.5t92 -161t32 -186q0 -96 -32 -185t-92 -161t-157 -115t-217 -43zM551 672h-199v-336h199q71 0 110 50.5t39 119.5q0 67 -39 116.5t-110 49.5zM1135 0v1434h352v-1434h-352z" />
    <glyph glyph-name="afii10046" unicode="&#x42c;" horiz-adv-x="1198" 
d="M563 0h-551v1434h350v-426h201q119 0 216 -42.5t157.5 -114.5t92.5 -161t32 -186q0 -96 -32 -185t-92.5 -161t-157.5 -115t-216 -43zM563 672h-201v-336h201q71 0 110.5 51t39.5 119q0 67 -39.5 116.5t-110.5 49.5z" />
    <glyph glyph-name="afii10047" unicode="&#x42d;" horiz-adv-x="1611" 
d="M526 571v299h564q-29 84 -90.5 143t-135.5 85t-156.5 23.5t-154 -33.5t-128 -96t-79.5 -154h-354q17 136 81.5 252t162 196t224 125t263.5 45q203 0 372.5 -96.5t269 -267t99.5 -377.5t-99.5 -376t-269 -264t-372.5 -95q-185 0 -342.5 76.5t-261 219t-129.5 326.5h354
q22 -91 79 -158t130.5 -99t157.5 -35t159 24t136.5 88.5t89.5 148.5h-570z" />
    <glyph glyph-name="afii10048" unicode="&#x42e;" horiz-adv-x="2072" 
d="M1190 1450q155 0 293 -60.5t234 -161t152 -234t56 -277.5q0 -200 -99.5 -369t-268.5 -266.5t-367 -97.5q-260 0 -454.5 162t-254.5 409h-133v-555h-348v1434h348v-541h135q68 248 262.5 402.5t444.5 154.5zM1573 717q0 161 -113.5 277t-269.5 116q-158 0 -270.5 -119
t-112.5 -274q0 -163 111 -279.5t272 -113.5q107 2 195.5 57t138 144.5t49.5 191.5z" />
    <glyph glyph-name="bgQ" unicode="&#x42f;" horiz-adv-x="1228" 
d="M735 485h-57l-299 -485h-373v66l307 454q-80 38 -140 99.5t-93 132.5t-43 150t3.5 154t52.5 144t97.5 121t145.5 82.5t190 30.5h557v-1434h-348v485zM735 803v295h-200q-44 0 -78 -16.5t-52 -42.5t-26 -57.5t0.5 -62.5t26 -57t52 -42.5t77.5 -16.5h200z" />
    <glyph glyph-name="bga" unicode="&#x430;" horiz-adv-x="1503" 
d="M993 0l-80 211h-489l-80 -211h-375l574 1450h250l596 -1450h-396zM670 872l-107 -303l-12 -32h237l-8 24z" />
    <glyph glyph-name="afii10066" unicode="&#x431;" horiz-adv-x="1280" 
d="M1038 1434v-334h-665v-178h313q136 0 241 -64t158.5 -167.5t53.5 -227.5q0 -88 -29.5 -170t-84.5 -148t-143 -105.5t-196 -39.5h-659v1434h1011zM373 594v-262h313q44 0 74.5 27t37 65.5t-1 77t-37.5 65.5t-73 27h-313z" />
    <glyph glyph-name="afii10067" unicode="&#x432;" horiz-adv-x="1269" 
d="M346 1116v-201h264q34 0 57.5 20.5t29 50.5t0 59.5t-28 50t-54.5 20.5h-268zM0 1434h664q84 0 157 -34.5t121.5 -90t75 -126.5t25 -142t-36.5 -139.5t-101 -115.5q109 -56 161 -141t52 -182q0 -88 -30 -170t-86 -148t-145 -105.5t-198 -39.5h-659v1434zM346 592v-260h313
q35 0 61 19.5t37.5 48t11.5 62.5t-11.5 62.5t-37.5 48t-61 19.5h-313z" />
    <glyph glyph-name="afii10068" unicode="&#x433;" horiz-adv-x="1107" 
d="M369 1100v-1100h-351v1434h943v-334h-592z" />
    <glyph glyph-name="afii10069" unicode="&#x434;" horiz-adv-x="1427" 
d="M1176 328h104v-533h-330v205h-618v-205h-344v494l141 33q56 79 74 329l53 783h920v-1106zM489 328h334v774h-248q-27 -487 -36 -573q-15 -131 -50 -201z" />
    <glyph glyph-name="afii10070" unicode="&#x435;" horiz-adv-x="1101" 
d="M352 547v-209h594v-338h-946v1434h944v-338h-592v-209h580v-340h-580z" />
    <glyph glyph-name="afii10072" unicode="&#x436;" horiz-adv-x="2009" 
d="M14 1413v21h408l330 -557v557h350v-557l330 557h407v-21l-444 -692l485 -703v-18h-420l-358 555v-555h-350v555l-359 -555h-420v18l486 703z" />
    <glyph glyph-name="afii10073" unicode="&#x437;" horiz-adv-x="1216" 
d="M-4 479h323q0 -106 98 -149q83 -35 179 -8q91 26 117 89q8 19 8 40q0 52 -40 94.5t-116 42.5h-217v295h217q49 0 81 37t32 81q0 50 -37 84.5t-90 42t-106.5 -1.5t-90.5 -48t-37 -97h-309q0 231 164 364q135 109 328 116q190 7 334 -87q162 -106 188 -296q4 -32 4 -66
q0 -80 -33.5 -152.5t-109.5 -111.5q190 -95 186 -318q-1 -85 -37.5 -164.5t-104.5 -144.5t-177 -104.5t-242 -39.5q-129 0 -229.5 41t-161 111.5t-91 159t-30.5 190.5z" />
    <glyph glyph-name="afii10074" unicode="&#x438;" horiz-adv-x="1275" 
d="M1116 0h-348v727l-530 -727h-234v1434h346v-725l522 725h244v-1434z" />
    <glyph glyph-name="afii10075" unicode="&#x439;" horiz-adv-x="1275" 
d="M332 1788h590v-287h-590v287zM1116 0h-348v727l-530 -727h-234v1434h346v-725l522 725h244v-1434z" />
    <glyph glyph-name="afii10076" unicode="&#x43a;" horiz-adv-x="1372" 
d="M1202 1434v-21l-508 -692l555 -703v-18h-434l-426 555v-555h-350v1434h350v-557l397 557h416z" />
    <glyph glyph-name="afii10077" unicode="&#x43b;" horiz-adv-x="1503" 
d="M993 0l-213 561l-110 311l-107 -303l-219 -569h-375l574 1450h250l596 -1450h-396z" />
    <glyph glyph-name="afii10078" unicode="&#x43c;" horiz-adv-x="1470" 
d="M350 713v-713h-350v1434h279l376 -555l377 555h275v-1434h-349v709l-206 -306h-195z" />
    <glyph glyph-name="afii10079" unicode="&#x43d;" horiz-adv-x="1241" 
d="M350 1434v-541h377v541h350v-1434h-350v555h-377v-555h-350v1434h350z" />
    <glyph glyph-name="afii10080" unicode="&#x43e;" horiz-adv-x="1622" 
d="M-10 715q0 207 99.5 377.5t269 267t372.5 96.5t373.5 -96.5t270.5 -267t100 -377.5q0 -153 -59 -290t-159 -234t-237 -154t-289 -57q-203 0 -372.5 95t-269 264t-99.5 376zM338 715q0 -98 33.5 -177t89 -127.5t125.5 -74.5t144 -26t144 26.5t125.5 75t89 127.5t33.5 176
q0 98 -33.5 177t-89 128t-125.5 75.5t-144 26.5t-144 -26.5t-125.5 -75t-89 -128t-33.5 -177.5z" />
    <glyph glyph-name="afii10081" unicode="&#x43f;" horiz-adv-x="1241" 
d="M0 1434h1077v-1434h-350v1096h-377v-1096h-350v1434z" />
    <glyph glyph-name="afii10082" unicode="&#x440;" horiz-adv-x="1196" 
d="M0 1434h549q121 0 219.5 -43.5t159 -114t93 -161.5t32.5 -185t-33 -185t-93.5 -161.5t-158.5 -114t-219 -43.5h-199v-426h-350v1434zM350 756h199q45 0 80 18.5t53 48.5t26.5 66t0 72t-26.5 66t-53 48.5t-80 18.5h-199v-338z" />
    <glyph glyph-name="afii10083" unicode="&#x441;" horiz-adv-x="1607" 
d="M336 715q0 -91 29 -166t77.5 -123.5t111.5 -78.5t130.5 -36.5t135.5 9t125 50.5t101 95t62 137h354q-26 -184 -129.5 -326.5t-261 -219t-342.5 -76.5q-203 0 -372.5 95t-269 264t-99.5 376t99.5 377.5t269 267t372.5 96.5q137 0 263.5 -45t224 -125t162 -196t81.5 -252
h-354q-19 76 -63.5 135t-101.5 93t-125 48t-135 7t-129.5 -37.5t-110 -79t-76.5 -123.5t-29 -166z" />
    <glyph glyph-name="afii10084" unicode="&#x442;" horiz-adv-x="1277" 
d="M731 0h-350v1106h-387v328h1122v-328h-385v-1106z" />
    <glyph glyph-name="afii10085" unicode="&#x443;" horiz-adv-x="1366" 
d="M596 -2h-379l213 487l-461 949h389l146 -299l117 -279l100 274l131 304h389z" />
    <glyph glyph-name="afii10086" unicode="&#x444;" horiz-adv-x="1589" 
d="M549 1311v129h350v-127q105 -14 194.5 -53t153.5 -96t108.5 -128.5t66.5 -150.5t22 -162q0 -105 -35.5 -203t-102.5 -179t-172 -138t-235 -76v-127h-350v127q-132 18 -237.5 74t-172.5 137t-102 178.5t-35 202.5t35.5 202t102.5 178t172.5 137t236.5 75zM895 999v-557
q98 29 148.5 101t50.5 174q0 238 -199 282zM352 721q0 -106 49.5 -180t151.5 -99v559q-101 -30 -151 -103t-50 -177z" />
    <glyph glyph-name="afii10087" unicode="&#x445;" horiz-adv-x="1427" 
d="M1284 1434v-33l-459 -674l461 -698v-29h-377l-282 436l-279 -436h-377v29l459 698l-453 666v41h369l281 -408l284 408h373z" />
    <glyph glyph-name="afii10088" unicode="&#x446;" horiz-adv-x="1337" 
d="M1085 317h105v-577h-330v260h-837v1434h352v-1117h356v1117h354v-1117z" />
    <glyph glyph-name="afii10089" unicode="&#x447;" horiz-adv-x="1212" 
d="M1042 1434v-1434h-356v442q-33 -6 -94 -6q-90 0 -172 17.5t-155.5 56.5t-127.5 97t-85.5 144t-31.5 191v492h359v-492q0 -118 88 -164.5t219 -21.5v678h356z" />
    <glyph glyph-name="afii10090" unicode="&#x448;" horiz-adv-x="1824" 
d="M35 1434h360v-1112h260v1112h359v-1112h258v1112h360v-1434h-1597v1434z" />
    <glyph glyph-name="afii10091" unicode="&#x449;" horiz-adv-x="1908" 
d="M39 1434h358v-1115h256v1115h361v-1115h260v1115h360v-1115h111v-579h-336v260h-1370v1434z" />
    <glyph glyph-name="afii10092" unicode="&#x44a;" horiz-adv-x="1378" 
d="M4 1100v334h524v-449h199q121 0 217.5 -39t156.5 -106.5t92 -153t32 -182.5q0 -96 -32 -185t-92 -161t-157 -115t-217 -43h-549v1100h-174zM727 674h-199v-342h199q71 0 111.5 52.5t40.5 121.5q0 67 -40.5 117.5t-111.5 50.5z" />
    <glyph glyph-name="afii10093" unicode="&#x44b;" horiz-adv-x="1650" 
d="M551 0h-551v1434h352v-426h199q120 0 217 -42.5t157 -114.5t92 -161t32 -186q0 -96 -32 -185t-92 -161t-157 -115t-217 -43zM551 672h-199v-336h199q71 0 110 50.5t39 119.5q0 67 -39 116.5t-110 49.5zM1135 0v1434h352v-1434h-352z" />
    <glyph glyph-name="afii10094" unicode="&#x44c;" horiz-adv-x="1198" 
d="M563 0h-551v1434h350v-426h201q119 0 216 -42.5t157.5 -114.5t92.5 -161t32 -186q0 -96 -32 -185t-92.5 -161t-157.5 -115t-216 -43zM563 672h-201v-336h201q71 0 110.5 51t39.5 119q0 67 -39.5 116.5t-110.5 49.5z" />
    <glyph glyph-name="afii10095" unicode="&#x44d;" horiz-adv-x="1611" 
d="M526 571v299h564q-29 84 -90.5 143t-135.5 85t-156.5 23.5t-154 -33.5t-128 -96t-79.5 -154h-354q17 136 81.5 252t162 196t224 125t263.5 45q203 0 372.5 -96.5t269 -267t99.5 -377.5t-99.5 -376t-269 -264t-372.5 -95q-185 0 -342.5 76.5t-261 219t-129.5 326.5h354
q22 -91 79 -158t130.5 -99t157.5 -35t159 24t136.5 88.5t89.5 148.5h-570z" />
    <glyph glyph-name="afii10096" unicode="&#x44e;" horiz-adv-x="2072" 
d="M1190 1450q155 0 293 -60.5t234 -161t152 -234t56 -277.5q0 -200 -99.5 -369t-268.5 -266.5t-367 -97.5q-260 0 -454.5 162t-254.5 409h-133v-555h-348v1434h348v-541h135q68 248 262.5 402.5t444.5 154.5zM1573 717q0 161 -113.5 277t-269.5 116q-158 0 -270.5 -119
t-112.5 -274q0 -163 111 -279.5t272 -113.5q107 2 195.5 57t138 144.5t49.5 191.5z" />
    <glyph glyph-name="bgq" unicode="&#x44f;" horiz-adv-x="1228" 
d="M735 485h-57l-299 -485h-373v66l307 454q-80 38 -140 99.5t-93 132.5t-43 150t3.5 154t52.5 144t97.5 121t145.5 82.5t190 30.5h557v-1434h-348v485zM735 803v295h-200q-44 0 -78 -16.5t-52 -42.5t-26 -57.5t0.5 -62.5t26 -57t52 -42.5t77.5 -16.5h200z" />
    <glyph glyph-name="afii10071" unicode="&#x451;" horiz-adv-x="1101" 
d="M274 1839q80 0 129.5 -48t49.5 -124q0 -66 -51 -118t-128 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5zM719 1839q79 0 128.5 -47.5t49.5 -124.5q0 -66 -50.5 -118t-127.5 -52q-70 0 -122 52.5t-52 117.5q0 75 50.5 123.5t123.5 48.5zM352 338v209h580
v340h-580v209h592v338h-944v-1434h946v338h-594z" />
    <glyph glyph-name="afii10099" unicode="&#x452;" horiz-adv-x="1497" 
d="M711 0h-351v1106h-387v328h1123v-328h-385v-117q73 20 147.5 16t144.5 -22t131 -59.5t106.5 -95t72 -129t26.5 -162.5v-86q2 -245 -129 -349t-442 -104v295q221 0 221 158v88q0 55 -29.5 93t-72.5 52.5t-91 12.5t-85 -21v-676z" />
    <glyph glyph-name="afii10100" unicode="&#x453;" horiz-adv-x="1107" 
d="M807 1798v-22l-236 -252h-264v47l129 227h371zM369 1100v-1100h-351v1434h943v-334h-592z" />
    <glyph glyph-name="afii10101" unicode="&#x454;" horiz-adv-x="1617" 
d="M936 870v-299h-569q28 -87 89.5 -148.5t136.5 -88.5t159 -24t157 35t130 99t79 158h355q-26 -184 -129.5 -326.5t-261 -219t-343.5 -76.5q-203 0 -372.5 95t-269 264t-99.5 376t99.5 377.5t269 267t372.5 96.5q137 0 263.5 -45t224 -125t162 -196t81.5 -252h-354
q-23 89 -79.5 154t-128 96t-154 33.5t-156 -23.5t-135 -85t-90.5 -143h563z" />
    <glyph glyph-name="afii10102" unicode="&#x455;" horiz-adv-x="1241" 
d="M1077 981h-352q0 70 -51 108.5t-135 38.5q-83 0 -134 -33t-51 -91q0 -17 5.5 -31.5t17 -25t23 -19t30 -14t31.5 -9.5t33.5 -6.5t30 -3.5t27.5 -2.5t19 -1.5q60 -8 106 -17t105 -25t104 -40.5t88 -58.5t73 -84.5t45 -112.5q10 -55 10 -100q0 -87 -32.5 -163.5t-87.5 -132
t-127 -95.5t-153 -60t-163 -20q-73 0 -147 17t-147.5 56.5t-130 96.5t-91.5 144.5t-35 192.5h354q0 -50 19 -85t51 -51.5t63 -23.5t66 -7q93 0 150 28t57 86q0 18 -7.5 33.5t-18.5 26t-29.5 19.5t-33 13.5t-37.5 9t-35 6t-33 3.5t-24 2q-58 6 -101.5 12.5t-99.5 20t-97.5 32
t-85 49t-72.5 69.5t-49.5 95t-26.5 125v-2q-8 152 66.5 269t200.5 176t280 59q79 0 154 -17t144 -54.5t120.5 -92t82.5 -134t31 -175.5z" />
    <glyph glyph-name="afii10103" unicode="&#x456;" horiz-adv-x="989" 
d="M831 1434v-320h-237v-801h237v-313h-825v313h238v801h-238v320h825z" />
    <glyph glyph-name="afii10104" unicode="&#x457;" horiz-adv-x="989" 
d="M190 1851q80 0 130.5 -49t50.5 -125q0 -67 -52 -118.5t-129 -51.5q-71 0 -123.5 52t-52.5 118q0 75 51.5 124.5t124.5 49.5zM645 1851q81 0 130.5 -48.5t49.5 -125.5q0 -68 -50.5 -119t-129.5 -51q-71 0 -122.5 52t-51.5 118q0 75 50.5 124.5t123.5 49.5zM831 1114v320
h-825v-320h238v-801h-238v-313h825v313h-237v801h237z" />
    <glyph glyph-name="afii10105" unicode="&#x458;" horiz-adv-x="999" 
d="M836 1434v-803q0 -231 -83.5 -368t-266 -200t-490.5 -63v328q190 0 296.5 30.5t150.5 94.5t44 178v481h-346v322h695z" />
    <glyph glyph-name="afii10106" unicode="&#x459;" horiz-adv-x="1857" 
d="M1174 0h-488v788l-367 -788h-370l645 1378h428v-471h152q114 0 206 -38.5t148.5 -103t86.5 -144t30 -166.5q0 -86 -30.5 -166t-87 -145.5t-148.5 -104.5t-205 -39zM1174 584h-152v-260h152q62 0 97.5 39t35.5 90q0 53 -35.5 92t-97.5 39z" />
    <glyph glyph-name="afii10107" unicode="&#x45a;" horiz-adv-x="1847" 
d="M1169 0h-489v584h-291v-584h-336v1378h336v-471h291v471h338v-471h151q113 0 205 -38.5t148.5 -102.5t87 -144t30.5 -167q0 -68 -19 -133.5t-58 -124t-93.5 -102.5t-132.5 -69.5t-168 -25.5zM1169 584h-151v-260h151q62 0 96.5 38.5t34.5 90.5q0 54 -34.5 92.5
t-96.5 38.5z" />
    <glyph glyph-name="afii10108" unicode="&#x45b;" horiz-adv-x="1501" 
d="M1300 0h-335v563q0 77 -57 114.5t-134 37.5q-41 0 -78 -19v-696h-336v1063h-372v315h1079v-315h-371v-53q44 10 97 10q76 0 148.5 -17.5t138 -55t114.5 -90.5t77.5 -129t28.5 -165v-563z" />
    <glyph glyph-name="afii10109" unicode="&#x45c;" horiz-adv-x="1333" 
d="M893 1788v-23l-236 -252h-264v48l129 227h371zM1163 1434v-21l-508 -692l555 -703v-18h-434l-426 555v-555h-350v1434h350v-557l398 557h415z" />
    <glyph glyph-name="afii10110" unicode="&#x45e;" horiz-adv-x="1339" 
d="M588 -2h-379l213 487q-79 165 -461 949h389l148 -301l114 -297l101 294l131 304h389zM307 1776h588v-271h-588v271z" />
    <glyph glyph-name="afii10193" unicode="&#x45f;" horiz-adv-x="1241" 
d="M369 -211v211h-369v1434h352v-1100h373v1100h352v-1434h-362v-211h-346z" />
    <glyph glyph-name="afii10050" unicode="&#x490;" horiz-adv-x="1105" 
d="M621 1434v168h327v-502h-592v-1100h-350v1434h615z" />
    <glyph glyph-name="afii10098" unicode="&#x491;" horiz-adv-x="1105" 
d="M621 1434v168h327v-502h-592v-1100h-350v1434h615z" />
    <glyph glyph-name="uni1E9E" unicode="&#x1e9e;" horiz-adv-x="1183" 
d="M365 1038v-1038h-365v1038q0 127 69.5 224.5t179.5 147.5t240 50t239 -48t177 -144.5t68 -223.5q0 -67 -39.5 -143.5t-106.5 -107.5q232 -93 228 -340q-1 -102 -35 -187t-93.5 -141.5t-140 -92.5t-173 -39.5t-193.5 17.5v312q34 -16 80.5 -17.5t89.5 12t72.5 50
t29.5 88.5q0 62 -51 106.5t-139 44.5h-86v330h86q54 0 83 31.5t29 74.5q0 37 -25.5 62.5t-62 31.5t-73.5 0t-62.5 -32.5t-25.5 -65.5z" />
    <glyph glyph-name="thinspace" unicode="&#x2009;" horiz-adv-x="28" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="1196" 
d="M-2 578v321h1059v-321h-1059z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1505" 
d="M0 569v312h1362v-312h-1362z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="622" 
d="M-6 975v98l207 373h307l-186 -471h-328z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="636" 
d="M492 1436v-99l-207 -372h-308l187 471h328z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="583" 
d="M440 242v-99l-207 -372h-307l187 471h327z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="1040" 
d="M401 965l2 98l205 373h307l-186 -471h-328zM-12 965v98l207 373h307l-187 -471h-327z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="1030" 
d="M492 1436l-3 -99l-204 -372h-308l187 471h328zM905 1436v-99l-207 -372h-307l187 471h327z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="1026" 
d="M440 242v-99l-207 -372h-307l187 471h327zM874 242v-99l-206 -372h-308l187 471h327z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="1058" 
d="M291 -84v881h-299v301h299v338h321v-338h301v-301h-301v-881h-321z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="1062" 
d="M0 291v291h299v223h-299v297h299v338h319v-338h302v-297h-302v-223h302v-291h-302v-375h-319v375h-299z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="702" 
d="M281 440q-117 0 -198 80t-81 195q0 114 81 193t198 79q116 0 196 -79t80 -193q0 -115 -80 -195t-196 -80z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1384" 
d="M188 358q84 0 136.5 -50.5t52.5 -131.5q0 -70 -54.5 -126t-134.5 -56q-75 0 -129.5 56.5t-54.5 125.5q0 80 53 131t131 51zM608 358q84 0 137.5 -50.5t53.5 -131.5q0 -70 -55 -126t-136 -56q-75 0 -129.5 56.5t-54.5 125.5q0 80 53 131t131 51zM1034 358
q84 0 137.5 -50.5t53.5 -131.5q0 -70 -55 -126t-136 -56q-75 0 -129.5 56.5t-54.5 125.5q0 80 53 131t131 51z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="2439" 
d="M709 1116q0 -146 -105.5 -248t-255.5 -102q-147 0 -252.5 102t-105.5 248q0 147 105.5 249.5t252.5 102.5q150 0 255.5 -102.5t105.5 -249.5zM1020 1436h387l-940 -1452h-387zM348 1044q33 0 51.5 23t19 50t-18.5 50t-52 23q-44 0 -63 -36.5t-0.5 -73t63.5 -36.5z
M1530 344q0 -146 -105.5 -248t-255.5 -102q-147 0 -252.5 102t-105.5 248q0 147 105.5 249.5t252.5 102.5q150 0 255.5 -102.5t105.5 -249.5zM1169 272q43 0 61.5 36.5t0.5 73t-62 36.5t-63 -36.5t-0.5 -73t63.5 -36.5zM2294 344q0 -146 -105.5 -248t-255.5 -102
q-147 0 -252.5 102t-105.5 248q0 147 105.5 249.5t252.5 102.5q150 0 255.5 -102.5t105.5 -249.5zM1933 272q33 0 51.5 23t19 50t-18.5 50t-52 23q-44 0 -63 -36.5t-0.5 -73t63.5 -36.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="821" 
d="M692 299v-47h-364l-353 438l353 438h366v-43l-313 -395z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="813" 
d="M-14 268v27l325 410l-319 401v39h370l316 -397v-82l-316 -398h-376z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="491" 
d="M29 -82h-348l706 1485h350z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="1302" 
d="M578 1108q-110 0 -167 -105.5t-57 -295.5q0 -174 58 -286t166 -112q74 0 126.5 54t76.5 142.5t24 201.5q0 191 -57 296t-170 105zM578 -29q-113 0 -208 39t-162.5 107.5t-114.5 161.5t-70 201.5t-23 226.5q0 122 23 232t70 203.5t115 162t162.5 107t207.5 38.5t208 -38.5
t163 -107t116 -162t71 -203.5t23 -232q0 -118 -23 -226.5t-71 -201.5t-116 -161.5t-163 -107.5t-208 -39z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="1210" 
d="M-4 522l715 920h262v-828h102v-323h-102v-291h-344v291h-633v231zM428 616h207v261z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="1161" 
d="M-8 424l311 51q7 -52 39 -88.5t75 -51.5t91 -12t87.5 22t65.5 58.5t25 90.5q0 63 -36.5 107t-88.5 62.5t-112 18.5q-101 0 -252 -88l-185 82v760h942v-316h-614v-172q85 25 169.5 27.5t160 -16t140.5 -59t112.5 -97.5t74.5 -136t27 -171q0 -96 -34 -186t-97.5 -164
t-166.5 -119t-230 -45q-75 0 -144.5 15t-134.5 49.5t-113.5 84.5t-78.5 125.5t-33 167.5z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="1208" 
d="M332 492q0 -57 29 -99t72.5 -61.5t94 -20t94 18.5t72.5 61.5t29 100.5q0 57 -29 99.5t-72.5 61.5t-94 19t-94 -18.5t-72.5 -61t-29 -100.5zM111 829l428 605h417l-346 -488q109 1 197 -36t144 -100.5t86 -145t30 -172.5q0 -97 -34.5 -187t-100 -162t-170.5 -115.5
t-234 -43.5q-263 0 -401.5 135.5t-138.5 372.5q0 154 123 337z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="1189" 
d="M-8 1434h1067v-211l-670 -1223h-381l608 1112h-624v322z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="1198" 
d="M336 440q0 -44 21.5 -78.5t56.5 -52t76.5 -25.5t83.5 0.5t77 26.5t56.5 52t21.5 77q0 50 -29 86t-73 52t-94.5 15.5t-94.5 -17t-73 -52.5t-29 -84zM688 1036q0 48 -32 81t-77 40.5t-90.5 0t-77.5 -41t-32 -82.5q0 -37 17 -65.5t44.5 -43.5t60.5 -22t65.5 0t60 22
t44.5 44.5t17 66.5zM1022 1028q0 -70 -43 -153t-115 -117q77 -36 126.5 -92t68 -111t18.5 -115q0 -149 -80.5 -258.5t-201.5 -159.5t-261.5 -50t-261.5 49t-201.5 159t-80.5 260q0 92 53.5 182.5t155.5 131.5q-48 21 -82 58.5t-49 80t-21 75.5t-6 62q1 86 28.5 158.5
t75 123.5t110 87.5t133.5 53t145 16.5t144.5 -17.5t133 -54t109.5 -87.5t74.5 -124t27.5 -158z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="1200" 
d="M721 952q0 57 -29 99t-72.5 61.5t-94 20t-94 -18.5t-72.5 -61.5t-29 -100.5q0 -57 29 -99.5t72.5 -61.5t94 -19t94 18.5t72.5 61t29 100.5zM942 614l-428 -614h-418l346 498q-109 -1 -197 36t-143.5 100.5t-85.5 145t-30 172.5q0 97 34.5 187t99.5 162t170 115.5
t234 43.5q263 0 402 -135.5t139 -372.5q0 -155 -123 -338z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="1302" 
d="M578 1108q-110 0 -167 -105.5t-57 -295.5q0 -174 58 -286t166 -112q74 0 126.5 54t76.5 142.5t24 201.5q0 191 -57 296t-170 105zM578 -29q-113 0 -208 39t-162.5 107.5t-114.5 161.5t-70 201.5t-23 226.5q0 122 23 232t70 203.5t115 162t162.5 107t207.5 38.5t208 -38.5
t163 -107t116 -162t71 -203.5t23 -232q0 -118 -23 -226.5t-71 -201.5t-116 -161.5t-163 -107.5t-208 -39z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="806" 
d="M645 0h-334v827l-118 -114l-236 233l457 498h231v-1444z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="1218" 
d="M352 905h-342q0 156 62.5 280.5t182.5 197.5t277 73q140 0 246.5 -41t168 -111.5t92 -155.5t30.5 -183q0 -111 -56.5 -218.5t-160.5 -193.5l-287 -225h498v-328h-1051v252l602 549q103 93 103 170q0 62 -49 105.5t-136 43.5q-74 0 -127 -60.5t-53 -154.5z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="1173" 
d="M-8 475h317q0 -106 95 -147q81 -35 175 -8q89 25 115 87q8 21 8 42q0 51 -38 93t-113 42h-213v297h213q47 0 79 35t32 79q0 49 -36.5 83t-89 42t-104.5 -1.5t-88.5 -48t-36.5 -95.5h-303q0 230 162 363q133 108 323 115q186 6 327 -89q159 -108 182 -299l3 -59
q0 -81 -32 -153t-107 -110q186 -96 182 -315q-1 -85 -37 -164t-102.5 -144t-173 -104t-237.5 -39q-102 0 -185.5 26.5t-141.5 73t-98 110t-58.5 136t-18.5 152.5z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="1210" 
d="M-4 522l715 920h262v-828h102v-323h-102v-291h-344v291h-633v231zM428 616h207v261z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="1161" 
d="M-8 424l311 51q7 -52 39 -88.5t75 -51.5t91 -12t87.5 22t65.5 58.5t25 90.5q0 63 -36.5 107t-88.5 62.5t-112 18.5q-101 0 -252 -88l-185 82v760h942v-316h-614v-172q85 25 169.5 27.5t160 -16t140.5 -59t112.5 -97.5t74.5 -136t27 -171q0 -96 -34 -186t-97.5 -164
t-166.5 -119t-230 -45q-75 0 -144.5 15t-134.5 49.5t-113.5 84.5t-78.5 125.5t-33 167.5z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="1208" 
d="M332 492q0 -57 29 -99t72.5 -61.5t94 -20t94 18.5t72.5 61.5t29 100.5q0 57 -29 99.5t-72.5 61.5t-94 19t-94 -18.5t-72.5 -61t-29 -100.5zM111 829l428 605h417l-346 -488q109 1 197 -36t144 -100.5t86 -145t30 -172.5q0 -97 -34.5 -187t-100 -162t-170.5 -115.5
t-234 -43.5q-263 0 -401.5 135.5t-138.5 372.5q0 154 123 337z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="1189" 
d="M-8 1434h1067v-211l-670 -1223h-381l608 1112h-624v322z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="1198" 
d="M336 440q0 -44 21.5 -78.5t56.5 -52t76.5 -25.5t83.5 0.5t77 26.5t56.5 52t21.5 77q0 50 -29 86t-73 52t-94.5 15.5t-94.5 -17t-73 -52.5t-29 -84zM688 1036q0 48 -32 81t-77 40.5t-90.5 0t-77.5 -41t-32 -82.5q0 -37 17 -65.5t44.5 -43.5t60.5 -22t65.5 0t60 22
t44.5 44.5t17 66.5zM1022 1028q0 -70 -43 -153t-115 -117q77 -36 126.5 -92t68 -111t18.5 -115q0 -149 -80.5 -258.5t-201.5 -159.5t-261.5 -50t-261.5 49t-201.5 159t-80.5 260q0 92 53.5 182.5t155.5 131.5q-48 21 -82 58.5t-49 80t-21 75.5t-6 62q1 86 28.5 158.5
t75 123.5t110 87.5t133.5 53t145 16.5t144.5 -17.5t133 -54t109.5 -87.5t74.5 -124t27.5 -158z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="1200" 
d="M721 952q0 57 -29 99t-72.5 61.5t-94 20t-94 -18.5t-72.5 -61.5t-29 -100.5q0 -57 29 -99.5t72.5 -61.5t94 -19t94 18.5t72.5 61t29 100.5zM942 614l-428 -614h-418l346 498q-109 -1 -197 36t-143.5 100.5t-85.5 145t-30 172.5q0 97 34.5 187t99.5 162t170 115.5
t234 43.5q263 0 402 -135.5t139 -372.5q0 -155 -123 -338z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="1710" 
d="M909 737h-909l49 252h90q82 211 270.5 339t421.5 128q184 0 342.5 -76.5t263 -218.5t126.5 -323h-355q-24 93 -84 160.5t-138 96t-161.5 29.5t-163 -34.5t-133.5 -100.5h430zM27 688h899l-43 -233h-365q44 -58 108.5 -94.5t134 -47.5t142 2t133 48t108 97t66.5 142h355
q-26 -184 -129.5 -326.5t-261 -219t-343.5 -76.5q-238 0 -428 130t-268 345h-158z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="2820" 
d="M0 0h348v698l490 -698h274v1434h-346v-717l-496 717h-270v-1434zM1186 715q0 207 99.5 377.5t269 267t372.5 96.5t373.5 -96.5t270.5 -267t100 -377.5q0 -153 -59 -290t-159 -234t-237 -154t-289 -57q-203 0 -372.5 95t-269 264t-99.5 376zM1534 715q0 -98 33.5 -177
t89 -127.5t125.5 -74.5t144 -26t144 26.5t125.5 75t89 127.5t33.5 176q0 98 -33.5 177t-89 128t-125.5 75.5t-144 26.5t-144 -26.5t-125.5 -75t-89 -128t-33.5 -177.5z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1835" 
d="M674 1153h-189v-537h-313v537h-188v285h690v-285zM1038 1436l181 -314l180 314h317v-820h-315v222l-74 -142h-217l-74 142v-222h-315v820h317z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="1083" 
d="M-6 537v321h950v-321h-950z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="897" 
d="M369 324q-152 0 -260.5 106.5t-108.5 255.5q0 153 108 258t261 105q156 0 263 -105t107 -258q0 -150 -107.5 -256t-262.5 -106z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="1173" 
d="M889 668l170 -179q-85 -128 -188 -193.5t-222 -58.5t-237 97q-131 112 -252 -74l-187 182q59 96 115.5 157.5t122 94.5t140 21.5t157.5 -68.5q108 -72 216.5 -63t164.5 84zM856 1090l188 -175q-50 -76 -94.5 -128.5t-95.5 -92t-102 -52.5t-110.5 -1.5t-123.5 53.5
q-65 53 -138.5 70.5t-134 1t-100.5 -58.5l-172 170q81 132 182.5 196.5t222 51.5t243.5 -113q32 -28 63 -34t61 9t56 39.5t55 63.5z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="1089" 
d="M70 211l55 80h-129v297h338l28 39h-366v303h577l148 207l203 -152l-39 -55h65v-303h-274l-29 -39h303v-297h-512l-162 -230z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="1019" 
d="M0 319h885v-317h-885v317zM872 1432v-351l-407 -192l407 -199v-338l-880 432v201z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="1021" 
d="M-4 319h885v-317h-885v317zM8 1081v351l881 -447v-201l-881 -432v338l408 199z" />
    <glyph glyph-name="fontfabric" horiz-adv-x="3624" 
d="M2746 1432h717v-361h-29q-22 79 -51 137.5t-72 104t-104.5 69t-140.5 23.5h-121v-1380h225v-27h-424v1434zM1526 25h479v-27h-479v27zM2429 1432h252v-27v-1407h-66l-876 1407h-213v27h440l676 -1100h12v1073h-225v27zM1620 1006q53 -128 53 -289q0 -140 -53 -268
q-29 -58 -41.5 -82t-42 -68t-61.5 -76q-119 -119 -232.5 -172t-271.5 -53h-13v27h13q258 0 372 172q57 78 78 213q1 9 7 54t8 62.5t5.5 54.5t5 69t1.5 67q0 317 -92 475q-87 213 -385 213h-13v27h13q207 0 385.5 -117.5t263.5 -308.5zM508 899q4 14 22 60t29 88h14v-625h-14
q-7 60 -25 112.5t-48 98t-79.5 71.5t-111.5 25h-133v27h117q93 -2 146 30t83 113zM162 1432h688v-347h-12q-19 72 -45.5 126t-69.5 100t-107.5 70t-150.5 24h-303v27z" />
    <glyph glyph-name="zero.tnum" horiz-adv-x="1302" 
d="M578 1108q-110 0 -167 -105.5t-57 -295.5q0 -174 58 -286t166 -112q74 0 126.5 54t76.5 142.5t24 201.5q0 191 -57 296t-170 105zM578 -29q-113 0 -208 39t-162.5 107.5t-114.5 161.5t-70 201.5t-23 226.5q0 122 23 232t70 203.5t115 162t162.5 107t207.5 38.5t208 -38.5
t163 -107t116 -162t71 -203.5t23 -232q0 -118 -23 -226.5t-71 -201.5t-116 -161.5t-163 -107.5t-208 -39z" />
    <glyph glyph-name="one.tnum" horiz-adv-x="806" 
d="M645 0h-334v827l-118 -114l-236 233l457 498h231v-1444z" />
    <glyph glyph-name="two.tnum" horiz-adv-x="1218" 
d="M352 905h-342q0 156 62.5 280.5t182.5 197.5t277 73q140 0 246.5 -41t168 -111.5t92 -155.5t30.5 -183q0 -111 -56.5 -218.5t-160.5 -193.5l-287 -225h498v-328h-1051v252l602 549q103 93 103 170q0 62 -49 105.5t-136 43.5q-74 0 -127 -60.5t-53 -154.5z" />
    <glyph glyph-name="three.tnum" horiz-adv-x="1173" 
d="M-8 475h317q0 -106 95 -147q81 -35 175 -8q89 25 115 87q8 21 8 42q0 51 -38 93t-113 42h-213v297h213q47 0 79 35t32 79q0 49 -36.5 83t-89 42t-104.5 -1.5t-88.5 -48t-36.5 -95.5h-303q0 230 162 363q133 108 323 115q186 6 327 -89q159 -108 182 -299l3 -59
q0 -81 -32 -153t-107 -110q186 -96 182 -315q-1 -85 -37 -164t-102.5 -144t-173 -104t-237.5 -39q-102 0 -185.5 26.5t-141.5 73t-98 110t-58.5 136t-18.5 152.5z" />
    <glyph glyph-name="four.tnum" horiz-adv-x="1210" 
d="M-4 522l715 920h262v-828h102v-323h-102v-291h-344v291h-633v231zM428 616h207v261z" />
    <glyph glyph-name="five.tnum" horiz-adv-x="1161" 
d="M-8 424l311 51q7 -52 39 -88.5t75 -51.5t91 -12t87.5 22t65.5 58.5t25 90.5q0 63 -36.5 107t-88.5 62.5t-112 18.5q-101 0 -252 -88l-185 82v760h942v-316h-614v-172q85 25 169.5 27.5t160 -16t140.5 -59t112.5 -97.5t74.5 -136t27 -171q0 -96 -34 -186t-97.5 -164
t-166.5 -119t-230 -45q-75 0 -144.5 15t-134.5 49.5t-113.5 84.5t-78.5 125.5t-33 167.5z" />
    <glyph glyph-name="six.tnum" horiz-adv-x="1208" 
d="M332 492q0 -57 29 -99t72.5 -61.5t94 -20t94 18.5t72.5 61.5t29 100.5q0 57 -29 99.5t-72.5 61.5t-94 19t-94 -18.5t-72.5 -61t-29 -100.5zM111 829l428 605h417l-346 -488q109 1 197 -36t144 -100.5t86 -145t30 -172.5q0 -97 -34.5 -187t-100 -162t-170.5 -115.5
t-234 -43.5q-263 0 -401.5 135.5t-138.5 372.5q0 154 123 337z" />
    <glyph glyph-name="seven.tnum" horiz-adv-x="1189" 
d="M-8 1434h1067v-211l-670 -1223h-381l608 1112h-624v322z" />
    <glyph glyph-name="eight.tnum" horiz-adv-x="1198" 
d="M336 440q0 -44 21.5 -78.5t56.5 -52t76.5 -25.5t83.5 0.5t77 26.5t56.5 52t21.5 77q0 50 -29 86t-73 52t-94.5 15.5t-94.5 -17t-73 -52.5t-29 -84zM688 1036q0 48 -32 81t-77 40.5t-90.5 0t-77.5 -41t-32 -82.5q0 -37 17 -65.5t44.5 -43.5t60.5 -22t65.5 0t60 22
t44.5 44.5t17 66.5zM1022 1028q0 -70 -43 -153t-115 -117q77 -36 126.5 -92t68 -111t18.5 -115q0 -149 -80.5 -258.5t-201.5 -159.5t-261.5 -50t-261.5 49t-201.5 159t-80.5 260q0 92 53.5 182.5t155.5 131.5q-48 21 -82 58.5t-49 80t-21 75.5t-6 62q1 86 28.5 158.5
t75 123.5t110 87.5t133.5 53t145 16.5t144.5 -17.5t133 -54t109.5 -87.5t74.5 -124t27.5 -158z" />
    <glyph glyph-name="nine.tnum" horiz-adv-x="1200" 
d="M721 952q0 57 -29 99t-72.5 61.5t-94 20t-94 -18.5t-72.5 -61.5t-29 -100.5q0 -57 29 -99.5t72.5 -61.5t94 -19t94 18.5t72.5 61t29 100.5zM942 614l-428 -614h-418l346 498q-109 -1 -197 36t-143.5 100.5t-85.5 145t-30 172.5q0 97 34.5 187t99.5 162t170 115.5
t234 43.5q263 0 402 -135.5t139 -372.5q0 -155 -123 -338z" />
    <glyph glyph-name="zero.p_osf" horiz-adv-x="1302" 
d="M578 1108q-110 0 -167 -105.5t-57 -295.5q0 -174 58 -286t166 -112q74 0 126.5 54t76.5 142.5t24 201.5q0 191 -57 296t-170 105zM578 -29q-113 0 -208 39t-162.5 107.5t-114.5 161.5t-70 201.5t-23 226.5q0 122 23 232t70 203.5t115 162t162.5 107t207.5 38.5t208 -38.5
t163 -107t116 -162t71 -203.5t23 -232q0 -118 -23 -226.5t-71 -201.5t-116 -161.5t-163 -107.5t-208 -39z" />
    <glyph glyph-name="one.p_osf" horiz-adv-x="806" 
d="M645 0h-334v827l-118 -114l-236 233l457 498h231v-1444z" />
    <glyph glyph-name="two.p_osf" horiz-adv-x="1218" 
d="M352 905h-342q0 156 62.5 280.5t182.5 197.5t277 73q140 0 246.5 -41t168 -111.5t92 -155.5t30.5 -183q0 -111 -56.5 -218.5t-160.5 -193.5l-287 -225h498v-328h-1051v252l602 549q103 93 103 170q0 62 -49 105.5t-136 43.5q-74 0 -127 -60.5t-53 -154.5z" />
    <glyph glyph-name="three.p_osf" horiz-adv-x="1173" 
d="M-8 475h317q0 -106 95 -147q81 -35 175 -8q89 25 115 87q8 21 8 42q0 51 -38 93t-113 42h-213v297h213q47 0 79 35t32 79q0 49 -36.5 83t-89 42t-104.5 -1.5t-88.5 -48t-36.5 -95.5h-303q0 230 162 363q133 108 323 115q186 6 327 -89q159 -108 182 -299l3 -59
q0 -81 -32 -153t-107 -110q186 -96 182 -315q-1 -85 -37 -164t-102.5 -144t-173 -104t-237.5 -39q-102 0 -185.5 26.5t-141.5 73t-98 110t-58.5 136t-18.5 152.5z" />
    <glyph glyph-name="four.p_osf" horiz-adv-x="1210" 
d="M-4 522l715 920h262v-828h102v-323h-102v-291h-344v291h-633v231zM428 616h207v261z" />
    <glyph glyph-name="five.p_osf" horiz-adv-x="1161" 
d="M-8 424l311 51q7 -52 39 -88.5t75 -51.5t91 -12t87.5 22t65.5 58.5t25 90.5q0 63 -36.5 107t-88.5 62.5t-112 18.5q-101 0 -252 -88l-185 82v760h942v-316h-614v-172q85 25 169.5 27.5t160 -16t140.5 -59t112.5 -97.5t74.5 -136t27 -171q0 -96 -34 -186t-97.5 -164
t-166.5 -119t-230 -45q-75 0 -144.5 15t-134.5 49.5t-113.5 84.5t-78.5 125.5t-33 167.5z" />
    <glyph glyph-name="six.p_osf" horiz-adv-x="1208" 
d="M332 492q0 -57 29 -99t72.5 -61.5t94 -20t94 18.5t72.5 61.5t29 100.5q0 57 -29 99.5t-72.5 61.5t-94 19t-94 -18.5t-72.5 -61t-29 -100.5zM111 829l428 605h417l-346 -488q109 1 197 -36t144 -100.5t86 -145t30 -172.5q0 -97 -34.5 -187t-100 -162t-170.5 -115.5
t-234 -43.5q-263 0 -401.5 135.5t-138.5 372.5q0 154 123 337z" />
    <glyph glyph-name="seven.p_osf" horiz-adv-x="1189" 
d="M-8 1434h1067v-211l-670 -1223h-381l608 1112h-624v322z" />
    <glyph glyph-name="eight.p_osf" horiz-adv-x="1198" 
d="M336 440q0 -44 21.5 -78.5t56.5 -52t76.5 -25.5t83.5 0.5t77 26.5t56.5 52t21.5 77q0 50 -29 86t-73 52t-94.5 15.5t-94.5 -17t-73 -52.5t-29 -84zM688 1036q0 48 -32 81t-77 40.5t-90.5 0t-77.5 -41t-32 -82.5q0 -37 17 -65.5t44.5 -43.5t60.5 -22t65.5 0t60 22
t44.5 44.5t17 66.5zM1022 1028q0 -70 -43 -153t-115 -117q77 -36 126.5 -92t68 -111t18.5 -115q0 -149 -80.5 -258.5t-201.5 -159.5t-261.5 -50t-261.5 49t-201.5 159t-80.5 260q0 92 53.5 182.5t155.5 131.5q-48 21 -82 58.5t-49 80t-21 75.5t-6 62q1 86 28.5 158.5
t75 123.5t110 87.5t133.5 53t145 16.5t144.5 -17.5t133 -54t109.5 -87.5t74.5 -124t27.5 -158z" />
    <glyph glyph-name="nine.p_osf" horiz-adv-x="1200" 
d="M721 952q0 57 -29 99t-72.5 61.5t-94 20t-94 -18.5t-72.5 -61.5t-29 -100.5q0 -57 29 -99.5t72.5 -61.5t94 -19t94 18.5t72.5 61t29 100.5zM942 614l-428 -614h-418l346 498q-109 -1 -197 36t-143.5 100.5t-85.5 145t-30 172.5q0 97 34.5 187t99.5 162t170 115.5
t234 43.5q263 0 402 -135.5t139 -372.5q0 -155 -123 -338z" />
    <glyph glyph-name="zero.t_osf" horiz-adv-x="1302" 
d="M578 1108q-110 0 -167 -105.5t-57 -295.5q0 -174 58 -286t166 -112q74 0 126.5 54t76.5 142.5t24 201.5q0 191 -57 296t-170 105zM578 -29q-113 0 -208 39t-162.5 107.5t-114.5 161.5t-70 201.5t-23 226.5q0 122 23 232t70 203.5t115 162t162.5 107t207.5 38.5t208 -38.5
t163 -107t116 -162t71 -203.5t23 -232q0 -118 -23 -226.5t-71 -201.5t-116 -161.5t-163 -107.5t-208 -39z" />
    <glyph glyph-name="one.t_osf" horiz-adv-x="806" 
d="M645 0h-334v827l-118 -114l-236 233l457 498h231v-1444z" />
    <glyph glyph-name="two.t_osf" horiz-adv-x="1218" 
d="M352 905h-342q0 156 62.5 280.5t182.5 197.5t277 73q140 0 246.5 -41t168 -111.5t92 -155.5t30.5 -183q0 -111 -56.5 -218.5t-160.5 -193.5l-287 -225h498v-328h-1051v252l602 549q103 93 103 170q0 62 -49 105.5t-136 43.5q-74 0 -127 -60.5t-53 -154.5z" />
    <glyph glyph-name="three.t_osf" horiz-adv-x="1173" 
d="M-8 475h317q0 -106 95 -147q81 -35 175 -8q89 25 115 87q8 21 8 42q0 51 -38 93t-113 42h-213v297h213q47 0 79 35t32 79q0 49 -36.5 83t-89 42t-104.5 -1.5t-88.5 -48t-36.5 -95.5h-303q0 230 162 363q133 108 323 115q186 6 327 -89q159 -108 182 -299l3 -59
q0 -81 -32 -153t-107 -110q186 -96 182 -315q-1 -85 -37 -164t-102.5 -144t-173 -104t-237.5 -39q-102 0 -185.5 26.5t-141.5 73t-98 110t-58.5 136t-18.5 152.5z" />
    <glyph glyph-name="four.t_osf" horiz-adv-x="1210" 
d="M-4 522l715 920h262v-828h102v-323h-102v-291h-344v291h-633v231zM428 616h207v261z" />
    <glyph glyph-name="five.t_osf" horiz-adv-x="1161" 
d="M-8 424l311 51q7 -52 39 -88.5t75 -51.5t91 -12t87.5 22t65.5 58.5t25 90.5q0 63 -36.5 107t-88.5 62.5t-112 18.5q-101 0 -252 -88l-185 82v760h942v-316h-614v-172q85 25 169.5 27.5t160 -16t140.5 -59t112.5 -97.5t74.5 -136t27 -171q0 -96 -34 -186t-97.5 -164
t-166.5 -119t-230 -45q-75 0 -144.5 15t-134.5 49.5t-113.5 84.5t-78.5 125.5t-33 167.5z" />
    <glyph glyph-name="six.t_osf" horiz-adv-x="1208" 
d="M332 492q0 -57 29 -99t72.5 -61.5t94 -20t94 18.5t72.5 61.5t29 100.5q0 57 -29 99.5t-72.5 61.5t-94 19t-94 -18.5t-72.5 -61t-29 -100.5zM111 829l428 605h417l-346 -488q109 1 197 -36t144 -100.5t86 -145t30 -172.5q0 -97 -34.5 -187t-100 -162t-170.5 -115.5
t-234 -43.5q-263 0 -401.5 135.5t-138.5 372.5q0 154 123 337z" />
    <glyph glyph-name="seven.t_osf" horiz-adv-x="1189" 
d="M-8 1434h1067v-211l-670 -1223h-381l608 1112h-624v322z" />
    <glyph glyph-name="eight.t_osf" horiz-adv-x="1198" 
d="M336 440q0 -44 21.5 -78.5t56.5 -52t76.5 -25.5t83.5 0.5t77 26.5t56.5 52t21.5 77q0 50 -29 86t-73 52t-94.5 15.5t-94.5 -17t-73 -52.5t-29 -84zM688 1036q0 48 -32 81t-77 40.5t-90.5 0t-77.5 -41t-32 -82.5q0 -37 17 -65.5t44.5 -43.5t60.5 -22t65.5 0t60 22
t44.5 44.5t17 66.5zM1022 1028q0 -70 -43 -153t-115 -117q77 -36 126.5 -92t68 -111t18.5 -115q0 -149 -80.5 -258.5t-201.5 -159.5t-261.5 -50t-261.5 49t-201.5 159t-80.5 260q0 92 53.5 182.5t155.5 131.5q-48 21 -82 58.5t-49 80t-21 75.5t-6 62q1 86 28.5 158.5
t75 123.5t110 87.5t133.5 53t145 16.5t144.5 -17.5t133 -54t109.5 -87.5t74.5 -124t27.5 -158z" />
    <glyph glyph-name="nine.t_osf" horiz-adv-x="1200" 
d="M721 952q0 57 -29 99t-72.5 61.5t-94 20t-94 -18.5t-72.5 -61.5t-29 -100.5q0 -57 29 -99.5t72.5 -61.5t94 -19t94 18.5t72.5 61t29 100.5zM942 614l-428 -614h-418l346 498q-109 -1 -197 36t-143.5 100.5t-85.5 145t-30 172.5q0 97 34.5 187t99.5 162t170 115.5
t234 43.5q263 0 402 -135.5t139 -372.5q0 -155 -123 -338z" />
    <glyph glyph-name="zero.numerator" horiz-adv-x="1302" 
d="M578 1108q-110 0 -167 -105.5t-57 -295.5q0 -174 58 -286t166 -112q74 0 126.5 54t76.5 142.5t24 201.5q0 191 -57 296t-170 105zM578 -29q-113 0 -208 39t-162.5 107.5t-114.5 161.5t-70 201.5t-23 226.5q0 122 23 232t70 203.5t115 162t162.5 107t207.5 38.5t208 -38.5
t163 -107t116 -162t71 -203.5t23 -232q0 -118 -23 -226.5t-71 -201.5t-116 -161.5t-163 -107.5t-208 -39z" />
    <glyph glyph-name="one.numerator" horiz-adv-x="806" 
d="M645 0h-334v827l-118 -114l-236 233l457 498h231v-1444z" />
    <glyph glyph-name="two.numerator" horiz-adv-x="1218" 
d="M352 905h-342q0 156 62.5 280.5t182.5 197.5t277 73q140 0 246.5 -41t168 -111.5t92 -155.5t30.5 -183q0 -111 -56.5 -218.5t-160.5 -193.5l-287 -225h498v-328h-1051v252l602 549q103 93 103 170q0 62 -49 105.5t-136 43.5q-74 0 -127 -60.5t-53 -154.5z" />
    <glyph glyph-name="three.numerator" horiz-adv-x="1173" 
d="M-8 475h317q0 -106 95 -147q81 -35 175 -8q89 25 115 87q8 21 8 42q0 51 -38 93t-113 42h-213v297h213q47 0 79 35t32 79q0 49 -36.5 83t-89 42t-104.5 -1.5t-88.5 -48t-36.5 -95.5h-303q0 230 162 363q133 108 323 115q186 6 327 -89q159 -108 182 -299l3 -59
q0 -81 -32 -153t-107 -110q186 -96 182 -315q-1 -85 -37 -164t-102.5 -144t-173 -104t-237.5 -39q-102 0 -185.5 26.5t-141.5 73t-98 110t-58.5 136t-18.5 152.5z" />
    <glyph glyph-name="four.numerator" horiz-adv-x="1210" 
d="M-4 522l715 920h262v-828h102v-323h-102v-291h-344v291h-633v231zM428 616h207v261z" />
    <glyph glyph-name="five.numerator" horiz-adv-x="1161" 
d="M-8 424l311 51q7 -52 39 -88.5t75 -51.5t91 -12t87.5 22t65.5 58.5t25 90.5q0 63 -36.5 107t-88.5 62.5t-112 18.5q-101 0 -252 -88l-185 82v760h942v-316h-614v-172q85 25 169.5 27.5t160 -16t140.5 -59t112.5 -97.5t74.5 -136t27 -171q0 -96 -34 -186t-97.5 -164
t-166.5 -119t-230 -45q-75 0 -144.5 15t-134.5 49.5t-113.5 84.5t-78.5 125.5t-33 167.5z" />
    <glyph glyph-name="six.numerator" horiz-adv-x="1208" 
d="M332 492q0 -57 29 -99t72.5 -61.5t94 -20t94 18.5t72.5 61.5t29 100.5q0 57 -29 99.5t-72.5 61.5t-94 19t-94 -18.5t-72.5 -61t-29 -100.5zM111 829l428 605h417l-346 -488q109 1 197 -36t144 -100.5t86 -145t30 -172.5q0 -97 -34.5 -187t-100 -162t-170.5 -115.5
t-234 -43.5q-263 0 -401.5 135.5t-138.5 372.5q0 154 123 337z" />
    <glyph glyph-name="seven.numerator" horiz-adv-x="1189" 
d="M-8 1434h1067v-211l-670 -1223h-381l608 1112h-624v322z" />
    <glyph glyph-name="eight.numerator" horiz-adv-x="1198" 
d="M336 440q0 -44 21.5 -78.5t56.5 -52t76.5 -25.5t83.5 0.5t77 26.5t56.5 52t21.5 77q0 50 -29 86t-73 52t-94.5 15.5t-94.5 -17t-73 -52.5t-29 -84zM688 1036q0 48 -32 81t-77 40.5t-90.5 0t-77.5 -41t-32 -82.5q0 -37 17 -65.5t44.5 -43.5t60.5 -22t65.5 0t60 22
t44.5 44.5t17 66.5zM1022 1028q0 -70 -43 -153t-115 -117q77 -36 126.5 -92t68 -111t18.5 -115q0 -149 -80.5 -258.5t-201.5 -159.5t-261.5 -50t-261.5 49t-201.5 159t-80.5 260q0 92 53.5 182.5t155.5 131.5q-48 21 -82 58.5t-49 80t-21 75.5t-6 62q1 86 28.5 158.5
t75 123.5t110 87.5t133.5 53t145 16.5t144.5 -17.5t133 -54t109.5 -87.5t74.5 -124t27.5 -158z" />
    <glyph glyph-name="nine.numerator" horiz-adv-x="1200" 
d="M721 952q0 57 -29 99t-72.5 61.5t-94 20t-94 -18.5t-72.5 -61.5t-29 -100.5q0 -57 29 -99.5t72.5 -61.5t94 -19t94 18.5t72.5 61t29 100.5zM942 614l-428 -614h-418l346 498q-109 -1 -197 36t-143.5 100.5t-85.5 145t-30 172.5q0 97 34.5 187t99.5 162t170 115.5
t234 43.5q263 0 402 -135.5t139 -372.5q0 -155 -123 -338z" />
    <glyph glyph-name="zero.denominator" horiz-adv-x="1302" 
d="M578 1108q-110 0 -167 -105.5t-57 -295.5q0 -174 58 -286t166 -112q74 0 126.5 54t76.5 142.5t24 201.5q0 191 -57 296t-170 105zM578 -29q-113 0 -208 39t-162.5 107.5t-114.5 161.5t-70 201.5t-23 226.5q0 122 23 232t70 203.5t115 162t162.5 107t207.5 38.5t208 -38.5
t163 -107t116 -162t71 -203.5t23 -232q0 -118 -23 -226.5t-71 -201.5t-116 -161.5t-163 -107.5t-208 -39z" />
    <glyph glyph-name="one.denominator" horiz-adv-x="806" 
d="M645 0h-334v827l-118 -114l-236 233l457 498h231v-1444z" />
    <glyph glyph-name="two.denominator" horiz-adv-x="1218" 
d="M352 905h-342q0 156 62.5 280.5t182.5 197.5t277 73q140 0 246.5 -41t168 -111.5t92 -155.5t30.5 -183q0 -111 -56.5 -218.5t-160.5 -193.5l-287 -225h498v-328h-1051v252l602 549q103 93 103 170q0 62 -49 105.5t-136 43.5q-74 0 -127 -60.5t-53 -154.5z" />
    <glyph glyph-name="three.denominator" horiz-adv-x="1173" 
d="M-8 475h317q0 -106 95 -147q81 -35 175 -8q89 25 115 87q8 21 8 42q0 51 -38 93t-113 42h-213v297h213q47 0 79 35t32 79q0 49 -36.5 83t-89 42t-104.5 -1.5t-88.5 -48t-36.5 -95.5h-303q0 230 162 363q133 108 323 115q186 6 327 -89q159 -108 182 -299l3 -59
q0 -81 -32 -153t-107 -110q186 -96 182 -315q-1 -85 -37 -164t-102.5 -144t-173 -104t-237.5 -39q-102 0 -185.5 26.5t-141.5 73t-98 110t-58.5 136t-18.5 152.5z" />
    <glyph glyph-name="four.denominator" horiz-adv-x="1210" 
d="M-4 522l715 920h262v-828h102v-323h-102v-291h-344v291h-633v231zM428 616h207v261z" />
    <glyph glyph-name="five.denominator" horiz-adv-x="1161" 
d="M-8 424l311 51q7 -52 39 -88.5t75 -51.5t91 -12t87.5 22t65.5 58.5t25 90.5q0 63 -36.5 107t-88.5 62.5t-112 18.5q-101 0 -252 -88l-185 82v760h942v-316h-614v-172q85 25 169.5 27.5t160 -16t140.5 -59t112.5 -97.5t74.5 -136t27 -171q0 -96 -34 -186t-97.5 -164
t-166.5 -119t-230 -45q-75 0 -144.5 15t-134.5 49.5t-113.5 84.5t-78.5 125.5t-33 167.5z" />
    <glyph glyph-name="six.denominator" horiz-adv-x="1208" 
d="M332 492q0 -57 29 -99t72.5 -61.5t94 -20t94 18.5t72.5 61.5t29 100.5q0 57 -29 99.5t-72.5 61.5t-94 19t-94 -18.5t-72.5 -61t-29 -100.5zM111 829l428 605h417l-346 -488q109 1 197 -36t144 -100.5t86 -145t30 -172.5q0 -97 -34.5 -187t-100 -162t-170.5 -115.5
t-234 -43.5q-263 0 -401.5 135.5t-138.5 372.5q0 154 123 337z" />
    <glyph glyph-name="seven.denominator" horiz-adv-x="1189" 
d="M-8 1434h1067v-211l-670 -1223h-381l608 1112h-624v322z" />
    <glyph glyph-name="eight.denominator" horiz-adv-x="1198" 
d="M336 440q0 -44 21.5 -78.5t56.5 -52t76.5 -25.5t83.5 0.5t77 26.5t56.5 52t21.5 77q0 50 -29 86t-73 52t-94.5 15.5t-94.5 -17t-73 -52.5t-29 -84zM688 1036q0 48 -32 81t-77 40.5t-90.5 0t-77.5 -41t-32 -82.5q0 -37 17 -65.5t44.5 -43.5t60.5 -22t65.5 0t60 22
t44.5 44.5t17 66.5zM1022 1028q0 -70 -43 -153t-115 -117q77 -36 126.5 -92t68 -111t18.5 -115q0 -149 -80.5 -258.5t-201.5 -159.5t-261.5 -50t-261.5 49t-201.5 159t-80.5 260q0 92 53.5 182.5t155.5 131.5q-48 21 -82 58.5t-49 80t-21 75.5t-6 62q1 86 28.5 158.5
t75 123.5t110 87.5t133.5 53t145 16.5t144.5 -17.5t133 -54t109.5 -87.5t74.5 -124t27.5 -158z" />
    <glyph glyph-name="nine.denominator" horiz-adv-x="1200" 
d="M721 952q0 57 -29 99t-72.5 61.5t-94 20t-94 -18.5t-72.5 -61.5t-29 -100.5q0 -57 29 -99.5t72.5 -61.5t94 -19t94 18.5t72.5 61t29 100.5zM942 614l-428 -614h-418l346 498q-109 -1 -197 36t-143.5 100.5t-85.5 145t-30 172.5q0 97 34.5 187t99.5 162t170 115.5
t234 43.5q263 0 402 -135.5t139 -372.5q0 -155 -123 -338z" />
    <glyph glyph-name="dollar.osf" horiz-adv-x="1241" 
d="M422 1444v131h248v-133q83 -15 154 -50.5t129 -90.5t91 -137.5t33 -182.5h-352q0 72 -55 111v-218q59 -11 108.5 -25t102 -39.5t92 -59t72 -84t47.5 -113.5q10 -55 10 -100q0 -121 -61.5 -219.5t-157.5 -157t-213 -80.5v-125h-248v123q-66 12 -126.5 36.5t-117.5 66.5
t-98.5 96t-66.5 130.5t-25 165.5h354q0 -99 80 -143v215q-67 11 -120.5 26t-109.5 45t-94 70.5t-65.5 104.5t-34.5 145v-2q-10 196 110 327.5t314 166.5zM754 436q0 74 -90 103v-213q90 33 90 110zM348 1004q0 -69 76 -97v207q-76 -37 -76 -110z" />
    <glyph glyph-name="cent.osf" horiz-adv-x="1607" 
d="M578 -154v148q-257 50 -423.5 249t-166.5 472t166.5 474t423.5 253v135h317v-139q224 -47 380 -209t185 -391h-354q-47 177 -211 249v-743q78 34 134.5 99.5t78.5 158.5h354q-33 -235 -188 -397t-379 -207v-152h-317zM336 715q0 -144 67 -241t175 -138v758
q-108 -43 -175 -139.5t-67 -239.5z" />
    <glyph glyph-name="sterling.osf" horiz-adv-x="1196" 
d="M1051 1016h-314q0 92 -70 128.5t-140 2t-70 -114.5v-168h372v-291h-372v-270h592v-303h-1047v254l119 27v292h-121v291h121v172q0 105 39 186t106.5 130t150.5 73.5t177 24.5q93 0 173 -25.5t145 -76t102 -136t37 -196.5z" />
    <glyph glyph-name="yen.osf" horiz-adv-x="1478" 
d="M227 190v263h256v43h-256v270h138l-396 635v33h385l144 -230l161 -307l170 326l132 211h387v-37l-398 -631h135v-270h-251v-43h251v-263h-251v-190h-351v190h-256z" />
    <glyph glyph-name="florin.osf" horiz-adv-x="1112" 
d="M975 1438l-45 -283q-91 27 -160.5 -1.5t-79.5 -102.5h176l-43 -275h-182l-149 -782q-18 -101 -64.5 -176.5t-111 -117.5t-141.5 -59.5t-157.5 -8.5t-158.5 40l57 269q105 -36 160.5 -14.5t79.5 128.5l139 721h-160l43 275h168q18 124 73.5 214t138 135.5t189.5 57.5
t228 -20z" />
    <glyph glyph-name="Euro.osf" horiz-adv-x="1710" 
d="M909 737h-909l49 252h90q82 211 270.5 339t421.5 128q184 0 342.5 -76.5t263 -218.5t126.5 -323h-355q-24 93 -84 160.5t-138 96t-161.5 29.5t-163 -34.5t-133.5 -100.5h430zM27 688h899l-43 -233h-365q44 -58 108.5 -94.5t134 -47.5t142 2t133 48t108 97t66.5 142h355
q-26 -184 -129.5 -326.5t-261 -219t-343.5 -76.5q-238 0 -428 130t-268 345h-158z" />
    <hkern u1="&#x22;" u2="&#x21b;" k="-43" />
    <hkern u1="&#x22;" u2="t" k="-43" />
    <hkern u1="&#x22;" u2="r" k="-31" />
    <hkern u1="&#x22;" u2="p" k="-33" />
    <hkern u1="&#x22;" u2="l" k="-31" />
    <hkern u1="&#x22;" u2="h" k="-33" />
    <hkern u1="&#x22;" u2="e" k="-31" />
    <hkern u1="&#x22;" u2="b" k="-31" />
    <hkern u1="&#x22;" u2="M" k="-33" />
    <hkern u1="&#x23;" u2="&#x39;" k="-66" />
    <hkern u1="&#x23;" u2="&#x35;" k="-49" />
    <hkern u1="&#x23;" u2="&#x32;" k="-31" />
    <hkern u1="&#x23;" u2="&#x31;" k="-31" />
    <hkern u1="&#x23;" u2="&#x30;" k="-31" />
    <hkern u1="&#x24;" u2="&#x38;" k="-31" />
    <hkern u1="&#x24;" u2="&#x36;" k="-33" />
    <hkern u1="&#x25;" u2="&#x38;" k="-33" />
    <hkern u1="&#x25;" u2="&#x36;" k="-49" />
    <hkern u1="&#x25;" u2="&#x35;" k="-31" />
    <hkern u1="&#x25;" u2="&#x32;" k="-33" />
    <hkern u1="&#x25;" u2="&#x31;" k="66" />
    <hkern u1="&#x26;" u2="&#x17e;" k="16" />
    <hkern u1="&#x26;" u2="&#x17c;" k="16" />
    <hkern u1="&#x26;" u2="&#x17a;" k="16" />
    <hkern u1="&#x26;" u2="&#x103;" k="-16" />
    <hkern u1="&#x26;" u2="&#x101;" k="-16" />
    <hkern u1="&#x26;" u2="&#xe5;" k="-16" />
    <hkern u1="&#x26;" u2="&#xe4;" k="-16" />
    <hkern u1="&#x26;" u2="&#xe3;" k="-16" />
    <hkern u1="&#x26;" u2="&#xe2;" k="-16" />
    <hkern u1="&#x26;" u2="&#xe1;" k="-16" />
    <hkern u1="&#x26;" u2="&#xe0;" k="-16" />
    <hkern u1="&#x26;" u2="z" k="16" />
    <hkern u1="&#x26;" u2="a" k="-16" />
    <hkern u1="&#x26;" u2="&#x21b;" k="242" />
    <hkern u1="&#x26;" u2="&#x21a;" k="256" />
    <hkern u1="&#x26;" u2="&#x219;" k="31" />
    <hkern u1="&#x26;" u2="&#x178;" k="303" />
    <hkern u1="&#x26;" u2="&#x153;" k="47" />
    <hkern u1="&#x26;" u2="&#x152;" k="66" />
    <hkern u1="&#x26;" u2="&#x14a;" k="-16" />
    <hkern u1="&#x26;" u2="&#x141;" k="-16" />
    <hkern u1="&#x26;" u2="&#x132;" k="-16" />
    <hkern u1="&#x26;" u2="&#x119;" k="47" />
    <hkern u1="&#x26;" u2="&#x118;" k="-16" />
    <hkern u1="&#x26;" u2="&#x110;" k="-16" />
    <hkern u1="&#x26;" u2="&#x105;" k="-16" />
    <hkern u1="&#x26;" u2="&#xe6;" k="-16" />
    <hkern u1="&#x26;" u2="&#xde;" k="-16" />
    <hkern u1="&#x26;" u2="&#xd0;" k="-16" />
    <hkern u1="&#x26;" u2="y" k="289" />
    <hkern u1="&#x26;" u2="x" k="-16" />
    <hkern u1="&#x26;" u2="w" k="207" />
    <hkern u1="&#x26;" u2="v" k="256" />
    <hkern u1="&#x26;" u2="u" k="16" />
    <hkern u1="&#x26;" u2="t" k="242" />
    <hkern u1="&#x26;" u2="s" k="31" />
    <hkern u1="&#x26;" u2="q" k="49" />
    <hkern u1="&#x26;" u2="o" k="66" />
    <hkern u1="&#x26;" u2="g" k="63" />
    <hkern u1="&#x26;" u2="e" k="47" />
    <hkern u1="&#x26;" u2="d" k="-16" />
    <hkern u1="&#x26;" u2="c" k="47" />
    <hkern u1="&#x26;" u2="b" k="-16" />
    <hkern u1="&#x26;" u2="Y" k="303" />
    <hkern u1="&#x26;" u2="W" k="207" />
    <hkern u1="&#x26;" u2="V" k="240" />
    <hkern u1="&#x26;" u2="T" k="256" />
    <hkern u1="&#x26;" u2="R" k="-16" />
    <hkern u1="&#x26;" u2="Q" k="49" />
    <hkern u1="&#x26;" u2="P" k="-16" />
    <hkern u1="&#x26;" u2="O" k="49" />
    <hkern u1="&#x26;" u2="N" k="-16" />
    <hkern u1="&#x26;" u2="L" k="-16" />
    <hkern u1="&#x26;" u2="K" k="-16" />
    <hkern u1="&#x26;" u2="I" k="-16" />
    <hkern u1="&#x26;" u2="H" k="-16" />
    <hkern u1="&#x26;" u2="G" k="66" />
    <hkern u1="&#x26;" u2="F" k="-16" />
    <hkern u1="&#x26;" u2="E" k="-16" />
    <hkern u1="&#x26;" u2="D" k="-16" />
    <hkern u1="&#x26;" u2="C" k="66" />
    <hkern u1="&#x26;" u2="B" k="-16" />
    <hkern u1="&#x26;" u2="&#x39;" k="80" />
    <hkern u1="&#x26;" u2="&#x38;" k="31" />
    <hkern u1="&#x26;" u2="&#x35;" k="16" />
    <hkern u1="&#x26;" u2="&#x34;" k="47" />
    <hkern u1="&#x26;" u2="&#x31;" k="193" />
    <hkern u1="&#x26;" u2="&#x30;" k="47" />
    <hkern u1="&#x27;" u2="&#x21b;" k="-43" />
    <hkern u1="&#x27;" u2="t" k="-43" />
    <hkern u1="&#x27;" u2="r" k="-31" />
    <hkern u1="&#x27;" u2="p" k="-33" />
    <hkern u1="&#x27;" u2="l" k="-31" />
    <hkern u1="&#x27;" u2="h" k="-33" />
    <hkern u1="&#x27;" u2="e" k="-31" />
    <hkern u1="&#x27;" u2="b" k="-31" />
    <hkern u1="&#x27;" u2="M" k="-33" />
    <hkern u1="&#x28;" u2="&#x219;" k="49" />
    <hkern u1="&#x28;" u2="&#x153;" k="96" />
    <hkern u1="&#x28;" u2="&#x152;" k="96" />
    <hkern u1="&#x28;" u2="&#x149;" k="-37" />
    <hkern u1="&#x28;" u2="&#x119;" k="96" />
    <hkern u1="&#x28;" u2="s" k="49" />
    <hkern u1="&#x28;" u2="q" k="96" />
    <hkern u1="&#x28;" u2="o" k="96" />
    <hkern u1="&#x28;" u2="g" k="96" />
    <hkern u1="&#x28;" u2="e" k="-33" />
    <hkern u1="&#x28;" u2="c" k="96" />
    <hkern u1="&#x28;" u2="Q" k="96" />
    <hkern u1="&#x28;" u2="O" k="96" />
    <hkern u1="&#x28;" u2="G" k="96" />
    <hkern u1="&#x28;" u2="C" k="96" />
    <hkern u1="&#x29;" u2="&#x178;" k="113" />
    <hkern u1="&#x29;" u2="&#x14a;" k="-31" />
    <hkern u1="&#x29;" u2="&#x141;" k="-31" />
    <hkern u1="&#x29;" u2="&#x132;" k="-31" />
    <hkern u1="&#x29;" u2="&#x118;" k="-31" />
    <hkern u1="&#x29;" u2="&#x110;" k="-31" />
    <hkern u1="&#x29;" u2="&#xde;" k="-31" />
    <hkern u1="&#x29;" u2="&#xd0;" k="-31" />
    <hkern u1="&#x29;" u2="Y" k="113" />
    <hkern u1="&#x29;" u2="R" k="-31" />
    <hkern u1="&#x29;" u2="P" k="-31" />
    <hkern u1="&#x29;" u2="N" k="-31" />
    <hkern u1="&#x29;" u2="L" k="-31" />
    <hkern u1="&#x29;" u2="K" k="-31" />
    <hkern u1="&#x29;" u2="I" k="-31" />
    <hkern u1="&#x29;" u2="H" k="-31" />
    <hkern u1="&#x29;" u2="F" k="-31" />
    <hkern u1="&#x29;" u2="E" k="-31" />
    <hkern u1="&#x29;" u2="D" k="-31" />
    <hkern u1="&#x29;" u2="B" k="-31" />
    <hkern u1="&#x2a;" u2="&#x39;" k="-49" />
    <hkern u1="&#x2a;" u2="&#x37;" k="-63" />
    <hkern u1="&#x2a;" u2="&#x36;" k="96" />
    <hkern u1="&#x2a;" u2="&#x35;" k="-47" />
    <hkern u1="&#x2a;" u2="&#x34;" k="193" />
    <hkern u1="&#x2b;" u2="&#x39;" k="-47" />
    <hkern u1="&#x2b;" u2="&#x38;" k="-33" />
    <hkern u1="&#x2b;" u2="&#x37;" k="49" />
    <hkern u1="&#x2b;" u2="&#x36;" k="-33" />
    <hkern u1="&#x2b;" u2="&#x35;" k="-31" />
    <hkern u1="&#x2b;" u2="&#x30;" k="-33" />
    <hkern u1="&#x2c;" u2="&#x17e;" k="-49" />
    <hkern u1="&#x2c;" u2="&#x17c;" k="-49" />
    <hkern u1="&#x2c;" u2="&#x17a;" k="-49" />
    <hkern u1="&#x2c;" u2="&#x103;" k="-47" />
    <hkern u1="&#x2c;" u2="&#x101;" k="-47" />
    <hkern u1="&#x2c;" u2="&#xe5;" k="-47" />
    <hkern u1="&#x2c;" u2="&#xe4;" k="-47" />
    <hkern u1="&#x2c;" u2="&#xe3;" k="-47" />
    <hkern u1="&#x2c;" u2="&#xe2;" k="-47" />
    <hkern u1="&#x2c;" u2="&#xe1;" k="-47" />
    <hkern u1="&#x2c;" u2="&#xe0;" k="-47" />
    <hkern u1="&#x2c;" u2="z" k="-49" />
    <hkern u1="&#x2c;" u2="a" k="-47" />
    <hkern u1="&#x2c;" u2="&#x153;" k="33" />
    <hkern u1="&#x2c;" u2="&#x14b;" k="-31" />
    <hkern u1="&#x2c;" u2="&#x149;" k="-31" />
    <hkern u1="&#x2c;" u2="&#x119;" k="33" />
    <hkern u1="&#x2c;" u2="&#x105;" k="-47" />
    <hkern u1="&#x2c;" u2="&#xe6;" k="-47" />
    <hkern u1="&#x2c;" u2="r" k="-31" />
    <hkern u1="&#x2c;" u2="q" k="-47" />
    <hkern u1="&#x2c;" u2="o" k="33" />
    <hkern u1="&#x2c;" u2="n" k="-31" />
    <hkern u1="&#x2c;" u2="m" k="-31" />
    <hkern u1="&#x2c;" u2="l" k="-33" />
    <hkern u1="&#x2c;" u2="k" k="-47" />
    <hkern u1="&#x2c;" u2="j" k="-80" />
    <hkern u1="&#x2c;" u2="i" k="-33" />
    <hkern u1="&#x2c;" u2="h" k="-16" />
    <hkern u1="&#x2c;" u2="e" k="-47" />
    <hkern u1="&#x2c;" u2="d" k="-47" />
    <hkern u1="&#x2c;" u2="c" k="33" />
    <hkern u1="&#x2c;" u2="Z" k="-63" />
    <hkern u1="&#x2c;" u2="H" k="-31" />
    <hkern u1="&#x2d;" u2="&#x1e9e;" k="25" />
    <hkern u1="&#x2d;" u2="x" k="47" />
    <hkern u1="&#x2d;" u2="v" k="80" />
    <hkern u1="&#x2d;" u2="l" k="63" />
    <hkern u1="&#x2d;" u2="i" k="63" />
    <hkern u1="&#x2d;" u2="X" k="49" />
    <hkern u1="&#x2d;" u2="V" k="63" />
    <hkern u1="&#x2d;" u2="Q" k="-33" />
    <hkern u1="&#x2d;" u2="L" k="-31" />
    <hkern u1="&#x2d;" u2="I" k="63" />
    <hkern u1="&#x2d;" u2="G" k="-16" />
    <hkern u1="&#x2e;" u2="&#x17e;" k="-49" />
    <hkern u1="&#x2e;" u2="&#x17c;" k="-49" />
    <hkern u1="&#x2e;" u2="&#x17a;" k="-49" />
    <hkern u1="&#x2e;" u2="&#x103;" k="-49" />
    <hkern u1="&#x2e;" u2="&#x101;" k="-49" />
    <hkern u1="&#x2e;" u2="&#xe5;" k="-49" />
    <hkern u1="&#x2e;" u2="&#xe4;" k="-49" />
    <hkern u1="&#x2e;" u2="&#xe3;" k="-49" />
    <hkern u1="&#x2e;" u2="&#xe2;" k="-49" />
    <hkern u1="&#x2e;" u2="&#xe1;" k="-49" />
    <hkern u1="&#x2e;" u2="&#xe0;" k="-49" />
    <hkern u1="&#x2e;" u2="z" k="-49" />
    <hkern u1="&#x2e;" u2="a" k="-49" />
    <hkern u1="&#x2e;" u2="&#x21b;" k="160" />
    <hkern u1="&#x2e;" u2="&#x21a;" k="143" />
    <hkern u1="&#x2e;" u2="&#x178;" k="240" />
    <hkern u1="&#x2e;" u2="&#x153;" k="31" />
    <hkern u1="&#x2e;" u2="&#x152;" k="47" />
    <hkern u1="&#x2e;" u2="&#x14a;" k="-47" />
    <hkern u1="&#x2e;" u2="&#x141;" k="-47" />
    <hkern u1="&#x2e;" u2="&#x132;" k="-47" />
    <hkern u1="&#x2e;" u2="&#x119;" k="31" />
    <hkern u1="&#x2e;" u2="&#x118;" k="-47" />
    <hkern u1="&#x2e;" u2="&#x110;" k="-47" />
    <hkern u1="&#x2e;" u2="&#x105;" k="-49" />
    <hkern u1="&#x2e;" u2="&#xe6;" k="-49" />
    <hkern u1="&#x2e;" u2="&#xde;" k="-47" />
    <hkern u1="&#x2e;" u2="&#xd0;" k="-47" />
    <hkern u1="&#x2e;" u2="y" k="225" />
    <hkern u1="&#x2e;" u2="x" k="-33" />
    <hkern u1="&#x2e;" u2="w" k="66" />
    <hkern u1="&#x2e;" u2="v" k="176" />
    <hkern u1="&#x2e;" u2="t" k="160" />
    <hkern u1="&#x2e;" u2="q" k="47" />
    <hkern u1="&#x2e;" u2="p" k="-63" />
    <hkern u1="&#x2e;" u2="o" k="63" />
    <hkern u1="&#x2e;" u2="l" k="-66" />
    <hkern u1="&#x2e;" u2="k" k="-47" />
    <hkern u1="&#x2e;" u2="j" k="-63" />
    <hkern u1="&#x2e;" u2="h" k="-47" />
    <hkern u1="&#x2e;" u2="g" k="33" />
    <hkern u1="&#x2e;" u2="e" k="-33" />
    <hkern u1="&#x2e;" u2="d" k="-49" />
    <hkern u1="&#x2e;" u2="c" k="31" />
    <hkern u1="&#x2e;" u2="b" k="-47" />
    <hkern u1="&#x2e;" u2="Z" k="-80" />
    <hkern u1="&#x2e;" u2="Y" k="240" />
    <hkern u1="&#x2e;" u2="T" k="143" />
    <hkern u1="&#x2e;" u2="R" k="-47" />
    <hkern u1="&#x2e;" u2="Q" k="47" />
    <hkern u1="&#x2e;" u2="P" k="-47" />
    <hkern u1="&#x2e;" u2="O" k="47" />
    <hkern u1="&#x2e;" u2="N" k="-47" />
    <hkern u1="&#x2e;" u2="L" k="-47" />
    <hkern u1="&#x2e;" u2="K" k="-47" />
    <hkern u1="&#x2e;" u2="I" k="-47" />
    <hkern u1="&#x2e;" u2="H" k="-47" />
    <hkern u1="&#x2e;" u2="G" k="47" />
    <hkern u1="&#x2e;" u2="F" k="-47" />
    <hkern u1="&#x2e;" u2="E" k="-47" />
    <hkern u1="&#x2e;" u2="D" k="-47" />
    <hkern u1="&#x2e;" u2="C" k="47" />
    <hkern u1="&#x2e;" u2="B" k="-47" />
    <hkern u1="&#x2f;" u2="&#x17f;" k="-47" />
    <hkern u1="&#x2f;" u2="&#x17e;" k="-80" />
    <hkern u1="&#x2f;" u2="&#x17c;" k="-80" />
    <hkern u1="&#x2f;" u2="&#x17a;" k="-80" />
    <hkern u1="&#x2f;" u2="&#x104;" k="129" />
    <hkern u1="&#x2f;" u2="&#x103;" k="-113" />
    <hkern u1="&#x2f;" u2="&#x102;" k="129" />
    <hkern u1="&#x2f;" u2="&#x101;" k="-113" />
    <hkern u1="&#x2f;" u2="&#x100;" k="129" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="-113" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="-113" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="-113" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="-113" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="-113" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="-113" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="129" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="129" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="129" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="129" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="129" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="129" />
    <hkern u1="&#x2f;" u2="z" k="-80" />
    <hkern u1="&#x2f;" u2="a" k="-113" />
    <hkern u1="&#x2f;" g2="f_j" k="-47" />
    <hkern u1="&#x2f;" g2="f_f_l" k="-47" />
    <hkern u1="&#x2f;" g2="f_f_i" k="-47" />
    <hkern u1="&#x2f;" g2="fl" k="-47" />
    <hkern u1="&#x2f;" g2="fi" k="-47" />
    <hkern u1="&#x2f;" g2="f_f" k="-47" />
    <hkern u1="&#x2f;" u2="&#x21b;" k="-113" />
    <hkern u1="&#x2f;" u2="&#x21a;" k="-80" />
    <hkern u1="&#x2f;" u2="&#x178;" k="-96" />
    <hkern u1="&#x2f;" u2="&#x153;" k="16" />
    <hkern u1="&#x2f;" u2="&#x14b;" k="-49" />
    <hkern u1="&#x2f;" u2="&#x14a;" k="-96" />
    <hkern u1="&#x2f;" u2="&#x149;" k="-49" />
    <hkern u1="&#x2f;" u2="&#x141;" k="-96" />
    <hkern u1="&#x2f;" u2="&#x132;" k="-96" />
    <hkern u1="&#x2f;" u2="&#x119;" k="16" />
    <hkern u1="&#x2f;" u2="&#x118;" k="-96" />
    <hkern u1="&#x2f;" u2="&#x110;" k="-96" />
    <hkern u1="&#x2f;" u2="&#x105;" k="-113" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="-113" />
    <hkern u1="&#x2f;" u2="&#xde;" k="-96" />
    <hkern u1="&#x2f;" u2="&#xd0;" k="-96" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="129" />
    <hkern u1="&#x2f;" u2="y" k="-113" />
    <hkern u1="&#x2f;" u2="x" k="-63" />
    <hkern u1="&#x2f;" u2="w" k="-113" />
    <hkern u1="&#x2f;" u2="v" k="-80" />
    <hkern u1="&#x2f;" u2="u" k="-80" />
    <hkern u1="&#x2f;" u2="t" k="-113" />
    <hkern u1="&#x2f;" u2="r" k="-49" />
    <hkern u1="&#x2f;" u2="q" k="33" />
    <hkern u1="&#x2f;" u2="p" k="-63" />
    <hkern u1="&#x2f;" u2="o" k="16" />
    <hkern u1="&#x2f;" u2="n" k="-49" />
    <hkern u1="&#x2f;" u2="m" k="-49" />
    <hkern u1="&#x2f;" u2="l" k="-66" />
    <hkern u1="&#x2f;" u2="k" k="-80" />
    <hkern u1="&#x2f;" u2="i" k="-66" />
    <hkern u1="&#x2f;" u2="h" k="-63" />
    <hkern u1="&#x2f;" u2="g" k="-16" />
    <hkern u1="&#x2f;" u2="f" k="-47" />
    <hkern u1="&#x2f;" u2="e" k="-63" />
    <hkern u1="&#x2f;" u2="d" k="-113" />
    <hkern u1="&#x2f;" u2="c" k="16" />
    <hkern u1="&#x2f;" u2="b" k="-80" />
    <hkern u1="&#x2f;" u2="Z" k="-47" />
    <hkern u1="&#x2f;" u2="Y" k="-96" />
    <hkern u1="&#x2f;" u2="X" k="-80" />
    <hkern u1="&#x2f;" u2="W" k="-80" />
    <hkern u1="&#x2f;" u2="V" k="-96" />
    <hkern u1="&#x2f;" u2="U" k="-96" />
    <hkern u1="&#x2f;" u2="T" k="-80" />
    <hkern u1="&#x2f;" u2="R" k="-96" />
    <hkern u1="&#x2f;" u2="P" k="-96" />
    <hkern u1="&#x2f;" u2="N" k="-96" />
    <hkern u1="&#x2f;" u2="M" k="-96" />
    <hkern u1="&#x2f;" u2="L" k="-96" />
    <hkern u1="&#x2f;" u2="K" k="-96" />
    <hkern u1="&#x2f;" u2="I" k="-96" />
    <hkern u1="&#x2f;" u2="H" k="-96" />
    <hkern u1="&#x2f;" u2="F" k="-96" />
    <hkern u1="&#x2f;" u2="E" k="-96" />
    <hkern u1="&#x2f;" u2="D" k="-96" />
    <hkern u1="&#x2f;" u2="B" k="-96" />
    <hkern u1="&#x2f;" u2="A" k="129" />
    <hkern u1="&#x30;" u2="&#x2089;" k="-20" />
    <hkern u1="&#x30;" u2="&#x2088;" k="-23" />
    <hkern u1="&#x30;" u2="&#x2087;" k="43" />
    <hkern u1="&#x30;" u2="&#x2086;" k="-23" />
    <hkern u1="&#x30;" u2="&#x2085;" k="-20" />
    <hkern u1="&#x30;" u2="&#x2080;" k="-23" />
    <hkern u1="&#x30;" u2="&#x2079;" k="-20" />
    <hkern u1="&#x30;" u2="&#x2078;" k="-23" />
    <hkern u1="&#x30;" u2="&#x2077;" k="43" />
    <hkern u1="&#x30;" u2="&#x2076;" k="-23" />
    <hkern u1="&#x30;" u2="&#x2075;" k="-20" />
    <hkern u1="&#x30;" u2="&#x2070;" k="-23" />
    <hkern u1="&#x30;" u2="&#x3c;" k="-96" />
    <hkern u1="&#x30;" u2="&#x39;" k="-20" />
    <hkern u1="&#x30;" u2="&#x38;" k="-23" />
    <hkern u1="&#x30;" u2="&#x37;" k="43" />
    <hkern u1="&#x30;" u2="&#x36;" k="-23" />
    <hkern u1="&#x30;" u2="&#x35;" k="-33" />
    <hkern u1="&#x30;" u2="&#x31;" k="-31" />
    <hkern u1="&#x30;" u2="&#x30;" k="-23" />
    <hkern u1="&#x30;" u2="&#x23;" k="-33" />
    <hkern u1="&#x31;" u2="&#x2087;" k="-23" />
    <hkern u1="&#x31;" u2="&#x2085;" k="-43" />
    <hkern u1="&#x31;" u2="&#x2080;" k="-43" />
    <hkern u1="&#x31;" u2="&#x2077;" k="-23" />
    <hkern u1="&#x31;" u2="&#x2075;" k="-43" />
    <hkern u1="&#x31;" u2="&#x2070;" k="-43" />
    <hkern u1="&#x31;" u2="&#x3e;" k="-47" />
    <hkern u1="&#x31;" u2="&#x3c;" k="-47" />
    <hkern u1="&#x31;" u2="&#x39;" k="-31" />
    <hkern u1="&#x31;" u2="&#x38;" k="-47" />
    <hkern u1="&#x31;" u2="&#x37;" k="-23" />
    <hkern u1="&#x31;" u2="&#x36;" k="-33" />
    <hkern u1="&#x31;" u2="&#x35;" k="-43" />
    <hkern u1="&#x31;" u2="&#x33;" k="-31" />
    <hkern u1="&#x31;" u2="&#x32;" k="-16" />
    <hkern u1="&#x31;" u2="&#x31;" k="-33" />
    <hkern u1="&#x31;" u2="&#x30;" k="-43" />
    <hkern u1="&#x31;" u2="&#x2a;" k="-49" />
    <hkern u1="&#x31;" u2="&#x26;" k="-33" />
    <hkern u1="&#x31;" u2="&#x24;" k="-33" />
    <hkern u1="&#x31;" u2="&#x23;" k="-31" />
    <hkern u1="&#x32;" u2="&#x2089;" k="-23" />
    <hkern u1="&#x32;" u2="&#x2085;" k="-43" />
    <hkern u1="&#x32;" u2="&#x2084;" k="43" />
    <hkern u1="&#x32;" u2="&#x2081;" k="-43" />
    <hkern u1="&#x32;" u2="&#x2080;" k="-23" />
    <hkern u1="&#x32;" u2="&#x2079;" k="-23" />
    <hkern u1="&#x32;" u2="&#x2075;" k="-43" />
    <hkern u1="&#x32;" u2="&#x2074;" k="43" />
    <hkern u1="&#x32;" u2="&#x2070;" k="-23" />
    <hkern u1="&#x32;" u2="&#x3e;" k="-33" />
    <hkern u1="&#x32;" u2="&#x39;" k="-23" />
    <hkern u1="&#x32;" u2="&#x35;" k="-43" />
    <hkern u1="&#x32;" u2="&#x31;" k="-43" />
    <hkern u1="&#x32;" u2="&#x30;" k="-23" />
    <hkern u1="&#x32;" u2="&#x25;" k="-31" />
    <hkern u1="&#x32;" u2="&#x24;" k="-16" />
    <hkern u1="&#x33;" u2="&#x2084;" k="-20" />
    <hkern u1="&#x33;" u2="&#x2082;" k="20" />
    <hkern u1="&#x33;" u2="&#x2081;" k="43" />
    <hkern u1="&#x33;" u2="&#x2074;" k="-20" />
    <hkern u1="&#x33;" u2="&#x3e;" k="-47" />
    <hkern u1="&#x33;" u2="&#x38;" k="-31" />
    <hkern u1="&#x33;" u2="&#x36;" k="-31" />
    <hkern u1="&#x33;" u2="&#x35;" k="-33" />
    <hkern u1="&#x33;" u2="&#x34;" k="-20" />
    <hkern u1="&#x33;" u2="&#x33;" k="-31" />
    <hkern u1="&#x33;" u2="&#x31;" k="-16" />
    <hkern u1="&#x33;" u2="&#x30;" k="-31" />
    <hkern u1="&#x33;" u2="&#x26;" k="-31" />
    <hkern u1="&#x33;" u2="&#x24;" k="-33" />
    <hkern u1="&#x33;" u2="&#x23;" k="-49" />
    <hkern u1="&#x34;" u2="&#x2088;" k="-43" />
    <hkern u1="&#x34;" u2="&#x2086;" k="-20" />
    <hkern u1="&#x34;" u2="&#x2081;" k="41" />
    <hkern u1="&#x34;" u2="&#x2078;" k="-43" />
    <hkern u1="&#x34;" u2="&#x2076;" k="-20" />
    <hkern u1="&#x34;" u2="&#x3e;" k="-31" />
    <hkern u1="&#x34;" u2="&#x39;" k="-16" />
    <hkern u1="&#x34;" u2="&#x38;" k="-47" />
    <hkern u1="&#x34;" u2="&#x36;" k="-49" />
    <hkern u1="&#x34;" u2="&#x35;" k="-49" />
    <hkern u1="&#x34;" u2="&#x34;" k="-33" />
    <hkern u1="&#x34;" u2="&#x33;" k="-33" />
    <hkern u1="&#x34;" u2="&#x31;" k="41" />
    <hkern u1="&#x34;" u2="&#x30;" k="-31" />
    <hkern u1="&#x34;" u2="&#x26;" k="-33" />
    <hkern u1="&#x34;" u2="&#x24;" k="-33" />
    <hkern u1="&#x34;" u2="&#x23;" k="-47" />
    <hkern u1="&#x35;" u2="&#x2089;" k="20" />
    <hkern u1="&#x35;" u2="&#x2087;" k="86" />
    <hkern u1="&#x35;" u2="&#x2082;" k="20" />
    <hkern u1="&#x35;" u2="&#x2081;" k="41" />
    <hkern u1="&#x35;" u2="&#x2079;" k="20" />
    <hkern u1="&#x35;" u2="&#x2077;" k="86" />
    <hkern u1="&#x35;" u2="&#x39;" k="20" />
    <hkern u1="&#x35;" u2="&#x38;" k="-31" />
    <hkern u1="&#x35;" u2="&#x37;" k="31" />
    <hkern u1="&#x35;" u2="&#x36;" k="-49" />
    <hkern u1="&#x35;" u2="&#x34;" k="-33" />
    <hkern u1="&#x35;" u2="&#x33;" k="-33" />
    <hkern u1="&#x35;" u2="&#x32;" k="20" />
    <hkern u1="&#x35;" u2="&#x31;" k="41" />
    <hkern u1="&#x35;" u2="&#x30;" k="-16" />
    <hkern u1="&#x35;" u2="&#x26;" k="-31" />
    <hkern u1="&#x36;" u2="&#x2087;" k="84" />
    <hkern u1="&#x36;" u2="&#x2084;" k="-20" />
    <hkern u1="&#x36;" u2="&#x2082;" k="43" />
    <hkern u1="&#x36;" u2="&#x2081;" k="63" />
    <hkern u1="&#x36;" u2="&#x2077;" k="84" />
    <hkern u1="&#x36;" u2="&#x2074;" k="-20" />
    <hkern u1="&#x36;" u2="&#x39;" k="14" />
    <hkern u1="&#x36;" u2="&#x38;" k="-31" />
    <hkern u1="&#x36;" u2="&#x37;" k="31" />
    <hkern u1="&#x36;" u2="&#x36;" k="-31" />
    <hkern u1="&#x36;" u2="&#x34;" k="-20" />
    <hkern u1="&#x36;" u2="&#x32;" k="16" />
    <hkern u1="&#x36;" u2="&#x31;" k="96" />
    <hkern u1="&#x36;" u2="&#x30;" k="-16" />
    <hkern u1="&#x36;" u2="&#x2a;" k="113" />
    <hkern u1="&#x36;" u2="&#x25;" k="31" />
    <hkern u1="&#x36;" u2="&#x24;" k="-14" />
    <hkern u1="&#x37;" u2="&#x2089;" k="23" />
    <hkern u1="&#x37;" u2="&#x2088;" k="23" />
    <hkern u1="&#x37;" u2="&#x2087;" k="-43" />
    <hkern u1="&#x37;" u2="&#x2086;" k="172" />
    <hkern u1="&#x37;" u2="&#x2085;" k="-23" />
    <hkern u1="&#x37;" u2="&#x2084;" k="213" />
    <hkern u1="&#x37;" u2="&#x2083;" k="20" />
    <hkern u1="&#x37;" u2="&#x2082;" k="43" />
    <hkern u1="&#x37;" u2="&#x2081;" k="43" />
    <hkern u1="&#x37;" u2="&#x2080;" k="63" />
    <hkern u1="&#x37;" u2="&#x2079;" k="23" />
    <hkern u1="&#x37;" u2="&#x2078;" k="23" />
    <hkern u1="&#x37;" u2="&#x2077;" k="-43" />
    <hkern u1="&#x37;" u2="&#x2076;" k="172" />
    <hkern u1="&#x37;" u2="&#x2075;" k="-23" />
    <hkern u1="&#x37;" u2="&#x2074;" k="213" />
    <hkern u1="&#x37;" u2="&#x2070;" k="63" />
    <hkern u1="&#x37;" u2="&#x3e;" k="-47" />
    <hkern u1="&#x37;" u2="&#x3c;" k="96" />
    <hkern u1="&#x37;" u2="&#x39;" k="23" />
    <hkern u1="&#x37;" u2="&#x38;" k="31" />
    <hkern u1="&#x37;" u2="&#x37;" k="-63" />
    <hkern u1="&#x37;" u2="&#x36;" k="172" />
    <hkern u1="&#x37;" u2="&#x35;" k="-23" />
    <hkern u1="&#x37;" u2="&#x34;" k="213" />
    <hkern u1="&#x37;" u2="&#x33;" k="20" />
    <hkern u1="&#x37;" u2="&#x32;" k="43" />
    <hkern u1="&#x37;" u2="&#x31;" k="43" />
    <hkern u1="&#x37;" u2="&#x30;" k="63" />
    <hkern u1="&#x37;" u2="&#x2b;" k="111" />
    <hkern u1="&#x37;" u2="&#x2a;" k="-49" />
    <hkern u1="&#x37;" u2="&#x26;" k="96" />
    <hkern u1="&#x37;" u2="&#x25;" k="-33" />
    <hkern u1="&#x37;" u2="&#x23;" k="96" />
    <hkern u1="&#x38;" u2="&#x2087;" k="43" />
    <hkern u1="&#x38;" u2="&#x2082;" k="41" />
    <hkern u1="&#x38;" u2="&#x2077;" k="43" />
    <hkern u1="&#x38;" u2="&#x3e;" k="-33" />
    <hkern u1="&#x38;" u2="&#x38;" k="-49" />
    <hkern u1="&#x38;" u2="&#x37;" k="16" />
    <hkern u1="&#x38;" u2="&#x36;" k="-49" />
    <hkern u1="&#x38;" u2="&#x35;" k="-49" />
    <hkern u1="&#x38;" u2="&#x34;" k="-31" />
    <hkern u1="&#x38;" u2="&#x33;" k="-33" />
    <hkern u1="&#x38;" u2="&#x32;" k="-33" />
    <hkern u1="&#x38;" u2="&#x30;" k="-16" />
    <hkern u1="&#x38;" u2="&#x2b;" k="-33" />
    <hkern u1="&#x38;" u2="&#x2a;" k="-16" />
    <hkern u1="&#x38;" u2="&#x26;" k="-33" />
    <hkern u1="&#x38;" u2="&#x24;" k="-63" />
    <hkern u1="&#x38;" u2="&#x23;" k="-63" />
    <hkern u1="&#x39;" u2="&#x2088;" k="20" />
    <hkern u1="&#x39;" u2="&#x2087;" k="41" />
    <hkern u1="&#x39;" u2="&#x2086;" k="43" />
    <hkern u1="&#x39;" u2="&#x2085;" k="-20" />
    <hkern u1="&#x39;" u2="&#x2084;" k="86" />
    <hkern u1="&#x39;" u2="&#x2083;" k="43" />
    <hkern u1="&#x39;" u2="&#x2082;" k="20" />
    <hkern u1="&#x39;" u2="&#x2081;" k="-23" />
    <hkern u1="&#x39;" u2="&#x2080;" k="-20" />
    <hkern u1="&#x39;" u2="&#x2078;" k="20" />
    <hkern u1="&#x39;" u2="&#x2077;" k="41" />
    <hkern u1="&#x39;" u2="&#x2076;" k="43" />
    <hkern u1="&#x39;" u2="&#x2075;" k="-20" />
    <hkern u1="&#x39;" u2="&#x2074;" k="86" />
    <hkern u1="&#x39;" u2="&#x2070;" k="-20" />
    <hkern u1="&#x39;" u2="&#x3e;" k="-47" />
    <hkern u1="&#x39;" u2="&#x3c;" k="-47" />
    <hkern u1="&#x39;" u2="&#x39;" k="-33" />
    <hkern u1="&#x39;" u2="&#x38;" k="20" />
    <hkern u1="&#x39;" u2="&#x37;" k="-16" />
    <hkern u1="&#x39;" u2="&#x36;" k="47" />
    <hkern u1="&#x39;" u2="&#x35;" k="-47" />
    <hkern u1="&#x39;" u2="&#x34;" k="86" />
    <hkern u1="&#x39;" u2="&#x32;" k="-16" />
    <hkern u1="&#x39;" u2="&#x31;" k="-23" />
    <hkern u1="&#x39;" u2="&#x2a;" k="-33" />
    <hkern u1="&#x39;" u2="&#x26;" k="49" />
    <hkern u1="&#x39;" u2="&#x25;" k="-33" />
    <hkern u1="&#x39;" u2="&#x24;" k="-33" />
    <hkern u1="&#x3a;" u2="&#x219;" k="-63" />
    <hkern u1="&#x3a;" u2="s" k="-63" />
    <hkern u1="&#x3a;" u2="Z" k="-63" />
    <hkern u1="&#x3a;" u2="U" k="-63" />
    <hkern u1="&#x3c;" u2="&#x39;" k="-47" />
    <hkern u1="&#x3c;" u2="&#x34;" k="-47" />
    <hkern u1="&#x3c;" u2="&#x33;" k="-80" />
    <hkern u1="&#x3c;" u2="&#x31;" k="-63" />
    <hkern u1="&#x3f;" u2="&#x103;" k="96" />
    <hkern u1="&#x3f;" u2="&#x101;" k="96" />
    <hkern u1="&#x3f;" u2="&#xe5;" k="96" />
    <hkern u1="&#x3f;" u2="&#xe4;" k="96" />
    <hkern u1="&#x3f;" u2="&#xe3;" k="96" />
    <hkern u1="&#x3f;" u2="&#xe2;" k="96" />
    <hkern u1="&#x3f;" u2="&#xe1;" k="96" />
    <hkern u1="&#x3f;" u2="&#xe0;" k="96" />
    <hkern u1="&#x3f;" u2="a" k="96" />
    <hkern u1="&#x3f;" u2="&#x14a;" k="-33" />
    <hkern u1="&#x3f;" u2="&#x141;" k="-33" />
    <hkern u1="&#x3f;" u2="&#x132;" k="-33" />
    <hkern u1="&#x3f;" u2="&#x118;" k="-33" />
    <hkern u1="&#x3f;" u2="&#x110;" k="-33" />
    <hkern u1="&#x3f;" u2="&#x105;" k="96" />
    <hkern u1="&#x3f;" u2="&#xe6;" k="96" />
    <hkern u1="&#x3f;" u2="&#xde;" k="-33" />
    <hkern u1="&#x3f;" u2="&#xd0;" k="-33" />
    <hkern u1="&#x3f;" u2="R" k="-33" />
    <hkern u1="&#x3f;" u2="P" k="-33" />
    <hkern u1="&#x3f;" u2="N" k="-33" />
    <hkern u1="&#x3f;" u2="L" k="-33" />
    <hkern u1="&#x3f;" u2="K" k="-33" />
    <hkern u1="&#x3f;" u2="J" k="80" />
    <hkern u1="&#x3f;" u2="I" k="-33" />
    <hkern u1="&#x3f;" u2="H" k="-33" />
    <hkern u1="&#x3f;" u2="F" k="-33" />
    <hkern u1="&#x3f;" u2="E" k="-33" />
    <hkern u1="&#x3f;" u2="D" k="-33" />
    <hkern u1="&#x3f;" u2="B" k="-33" />
    <hkern u1="&#x40;" u2="&#x103;" k="80" />
    <hkern u1="&#x40;" u2="&#x101;" k="80" />
    <hkern u1="&#x40;" u2="&#xe5;" k="80" />
    <hkern u1="&#x40;" u2="&#xe4;" k="80" />
    <hkern u1="&#x40;" u2="&#xe3;" k="80" />
    <hkern u1="&#x40;" u2="&#xe2;" k="80" />
    <hkern u1="&#x40;" u2="&#xe1;" k="80" />
    <hkern u1="&#x40;" u2="&#xe0;" k="80" />
    <hkern u1="&#x40;" u2="a" k="80" />
    <hkern u1="&#x40;" u2="&#x105;" k="80" />
    <hkern u1="&#x40;" u2="&#xe6;" k="80" />
    <hkern u1="&#x40;" u2="y" k="80" />
    <hkern u1="&#x40;" u2="r" k="-80" />
    <hkern u1="&#x40;" u2="p" k="-47" />
    <hkern u1="&#x40;" u2="n" k="-31" />
    <hkern u1="A" u2="x" k="96" />
    <hkern u1="A" u2="v" k="160" />
    <hkern u1="A" u2="r" k="14" />
    <hkern u1="A" u2="p" k="33" />
    <hkern u1="A" u2="l" k="16" />
    <hkern u1="A" u2="k" k="33" />
    <hkern u1="A" u2="j" k="33" />
    <hkern u1="A" u2="i" k="49" />
    <hkern u1="A" u2="h" k="33" />
    <hkern u1="A" u2="e" k="113" />
    <hkern u1="A" u2="b" k="16" />
    <hkern u1="A" u2="X" k="-33" />
    <hkern u1="A" u2="V" k="193" />
    <hkern u1="A" u2="R" k="-16" />
    <hkern u1="A" u2="Q" k="80" />
    <hkern u1="A" u2="P" k="-47" />
    <hkern u1="A" u2="O" k="63" />
    <hkern u1="A" u2="M" k="-33" />
    <hkern u1="A" u2="L" k="-31" />
    <hkern u1="A" u2="K" k="-16" />
    <hkern u1="A" u2="I" k="-31" />
    <hkern u1="A" u2="H" k="-33" />
    <hkern u1="A" u2="G" k="80" />
    <hkern u1="A" u2="E" k="-31" />
    <hkern u1="A" u2="&#x3f;" k="209" />
    <hkern u1="A" u2="&#x2f;" k="-63" />
    <hkern u1="A" u2="&#x26;" k="49" />
    <hkern u1="B" u2="&#x17f;" k="80" />
    <hkern u1="B" u2="&#x17e;" k="49" />
    <hkern u1="B" u2="&#x17c;" k="49" />
    <hkern u1="B" u2="&#x17a;" k="49" />
    <hkern u1="B" u2="&#x104;" k="66" />
    <hkern u1="B" u2="&#x103;" k="16" />
    <hkern u1="B" u2="&#x102;" k="66" />
    <hkern u1="B" u2="&#x101;" k="16" />
    <hkern u1="B" u2="&#x100;" k="66" />
    <hkern u1="B" u2="&#xe5;" k="16" />
    <hkern u1="B" u2="&#xe4;" k="16" />
    <hkern u1="B" u2="&#xe3;" k="16" />
    <hkern u1="B" u2="&#xe2;" k="16" />
    <hkern u1="B" u2="&#xe1;" k="16" />
    <hkern u1="B" u2="&#xe0;" k="16" />
    <hkern u1="B" u2="&#xc5;" k="66" />
    <hkern u1="B" u2="&#xc4;" k="66" />
    <hkern u1="B" u2="&#xc3;" k="66" />
    <hkern u1="B" u2="&#xc2;" k="66" />
    <hkern u1="B" u2="&#xc1;" k="66" />
    <hkern u1="B" u2="&#xc0;" k="66" />
    <hkern u1="B" u2="z" k="49" />
    <hkern u1="B" u2="a" k="16" />
    <hkern u1="B" g2="f_j" k="80" />
    <hkern u1="B" g2="f_f_l" k="80" />
    <hkern u1="B" g2="f_f_i" k="80" />
    <hkern u1="B" g2="fl" k="80" />
    <hkern u1="B" g2="fi" k="80" />
    <hkern u1="B" g2="f_f" k="80" />
    <hkern u1="B" u2="&#x21b;" k="82" />
    <hkern u1="B" u2="&#x21a;" k="80" />
    <hkern u1="B" u2="&#x219;" k="23" />
    <hkern u1="B" u2="&#x178;" k="127" />
    <hkern u1="B" u2="&#x14b;" k="47" />
    <hkern u1="B" u2="&#x14a;" k="-16" />
    <hkern u1="B" u2="&#x149;" k="47" />
    <hkern u1="B" u2="&#x141;" k="-16" />
    <hkern u1="B" u2="&#x132;" k="-16" />
    <hkern u1="B" u2="&#x118;" k="-16" />
    <hkern u1="B" u2="&#x110;" k="-16" />
    <hkern u1="B" u2="&#x105;" k="16" />
    <hkern u1="B" u2="&#xe6;" k="16" />
    <hkern u1="B" u2="&#xde;" k="-16" />
    <hkern u1="B" u2="&#xd0;" k="-16" />
    <hkern u1="B" u2="&#xc6;" k="66" />
    <hkern u1="B" u2="y" k="63" />
    <hkern u1="B" u2="x" k="43" />
    <hkern u1="B" u2="w" k="49" />
    <hkern u1="B" u2="v" k="49" />
    <hkern u1="B" u2="u" k="20" />
    <hkern u1="B" u2="t" k="82" />
    <hkern u1="B" u2="s" k="23" />
    <hkern u1="B" u2="r" k="33" />
    <hkern u1="B" u2="q" k="16" />
    <hkern u1="B" u2="p" k="16" />
    <hkern u1="B" u2="n" k="43" />
    <hkern u1="B" u2="m" k="47" />
    <hkern u1="B" u2="l" k="33" />
    <hkern u1="B" u2="k" k="33" />
    <hkern u1="B" u2="j" k="33" />
    <hkern u1="B" u2="i" k="33" />
    <hkern u1="B" u2="h" k="33" />
    <hkern u1="B" u2="g" k="33" />
    <hkern u1="B" u2="f" k="80" />
    <hkern u1="B" u2="e" k="33" />
    <hkern u1="B" u2="b" k="33" />
    <hkern u1="B" u2="Z" k="31" />
    <hkern u1="B" u2="Y" k="127" />
    <hkern u1="B" u2="X" k="33" />
    <hkern u1="B" u2="W" k="96" />
    <hkern u1="B" u2="V" k="66" />
    <hkern u1="B" u2="U" k="-16" />
    <hkern u1="B" u2="T" k="80" />
    <hkern u1="B" u2="R" k="-16" />
    <hkern u1="B" u2="P" k="-31" />
    <hkern u1="B" u2="N" k="-16" />
    <hkern u1="B" u2="L" k="-31" />
    <hkern u1="B" u2="K" k="-16" />
    <hkern u1="B" u2="I" k="31" />
    <hkern u1="B" u2="H" k="-16" />
    <hkern u1="B" u2="F" k="-16" />
    <hkern u1="B" u2="E" k="-16" />
    <hkern u1="B" u2="D" k="-16" />
    <hkern u1="B" u2="B" k="-16" />
    <hkern u1="B" u2="A" k="66" />
    <hkern u1="B" u2="&#x3f;" k="47" />
    <hkern u1="B" u2="&#x2c;" k="66" />
    <hkern u1="B" u2="&#x29;" k="80" />
    <hkern u1="C" u2="x" k="43" />
    <hkern u1="C" u2="v" k="84" />
    <hkern u1="C" u2="r" k="43" />
    <hkern u1="C" u2="p" k="16" />
    <hkern u1="C" u2="l" k="33" />
    <hkern u1="C" u2="k" k="49" />
    <hkern u1="C" u2="j" k="49" />
    <hkern u1="C" u2="i" k="47" />
    <hkern u1="C" u2="h" k="31" />
    <hkern u1="C" u2="e" k="31" />
    <hkern u1="C" u2="b" k="31" />
    <hkern u1="C" u2="X" k="80" />
    <hkern u1="C" u2="V" k="33" />
    <hkern u1="C" u2="R" k="-33" />
    <hkern u1="C" u2="P" k="-33" />
    <hkern u1="C" u2="N" k="-33" />
    <hkern u1="C" u2="L" k="-31" />
    <hkern u1="C" u2="K" k="-33" />
    <hkern u1="C" u2="I" k="49" />
    <hkern u1="C" u2="H" k="-16" />
    <hkern u1="C" u2="E" k="-49" />
    <hkern u1="C" u2="&#x3a;" k="-33" />
    <hkern u1="C" u2="&#x2c;" k="129" />
    <hkern u1="C" u2="&#x29;" k="96" />
    <hkern u1="D" u2="x" k="66" />
    <hkern u1="D" u2="v" k="80" />
    <hkern u1="D" u2="n" k="33" />
    <hkern u1="D" u2="l" k="33" />
    <hkern u1="D" u2="k" k="33" />
    <hkern u1="D" u2="j" k="31" />
    <hkern u1="D" u2="i" k="49" />
    <hkern u1="D" u2="h" k="33" />
    <hkern u1="D" u2="X" k="80" />
    <hkern u1="D" u2="V" k="80" />
    <hkern u1="D" u2="P" k="-33" />
    <hkern u1="D" u2="L" k="-16" />
    <hkern u1="D" u2="I" k="33" />
    <hkern u1="D" u2="H" k="-49" />
    <hkern u1="D" u2="D" k="-33" />
    <hkern u1="D" u2="&#x3f;" k="31" />
    <hkern u1="D" u2="&#x2c;" k="129" />
    <hkern u1="D" u2="&#x29;" k="96" />
    <hkern u1="E" u2="p" k="14" />
    <hkern u1="E" u2="l" k="33" />
    <hkern u1="E" u2="j" k="31" />
    <hkern u1="E" u2="i" k="33" />
    <hkern u1="E" u2="h" k="31" />
    <hkern u1="E" u2="e" k="33" />
    <hkern u1="E" u2="X" k="-33" />
    <hkern u1="E" u2="N" k="-33" />
    <hkern u1="E" u2="L" k="-33" />
    <hkern u1="E" u2="D" k="-16" />
    <hkern u1="F" u2="&#x17f;" k="16" />
    <hkern u1="F" u2="&#x17e;" k="66" />
    <hkern u1="F" u2="&#x17c;" k="66" />
    <hkern u1="F" u2="&#x17a;" k="66" />
    <hkern u1="F" u2="&#x104;" k="111" />
    <hkern u1="F" u2="&#x103;" k="31" />
    <hkern u1="F" u2="&#x102;" k="111" />
    <hkern u1="F" u2="&#x101;" k="31" />
    <hkern u1="F" u2="&#x100;" k="111" />
    <hkern u1="F" u2="&#xe5;" k="31" />
    <hkern u1="F" u2="&#xe4;" k="31" />
    <hkern u1="F" u2="&#xe3;" k="31" />
    <hkern u1="F" u2="&#xe2;" k="31" />
    <hkern u1="F" u2="&#xe1;" k="31" />
    <hkern u1="F" u2="&#xe0;" k="31" />
    <hkern u1="F" u2="&#xc5;" k="111" />
    <hkern u1="F" u2="&#xc4;" k="111" />
    <hkern u1="F" u2="&#xc3;" k="111" />
    <hkern u1="F" u2="&#xc2;" k="111" />
    <hkern u1="F" u2="&#xc1;" k="111" />
    <hkern u1="F" u2="&#xc0;" k="111" />
    <hkern u1="F" u2="z" k="66" />
    <hkern u1="F" u2="a" k="31" />
    <hkern u1="F" u2="&#x2e;" k="143" />
    <hkern u1="F" g2="f_j" k="16" />
    <hkern u1="F" g2="f_f_l" k="16" />
    <hkern u1="F" g2="f_f_i" k="16" />
    <hkern u1="F" g2="fl" k="16" />
    <hkern u1="F" g2="fi" k="16" />
    <hkern u1="F" g2="f_f" k="16" />
    <hkern u1="F" u2="&#x2026;" k="143" />
    <hkern u1="F" u2="&#x21b;" k="16" />
    <hkern u1="F" u2="&#x21a;" k="-33" />
    <hkern u1="F" u2="&#x219;" k="49" />
    <hkern u1="F" u2="&#x178;" k="-31" />
    <hkern u1="F" u2="&#x14b;" k="47" />
    <hkern u1="F" u2="&#x149;" k="47" />
    <hkern u1="F" u2="&#x105;" k="31" />
    <hkern u1="F" u2="&#xe6;" k="31" />
    <hkern u1="F" u2="&#xc6;" k="111" />
    <hkern u1="F" u2="y" k="-16" />
    <hkern u1="F" u2="x" k="43" />
    <hkern u1="F" u2="w" k="23" />
    <hkern u1="F" u2="v" k="43" />
    <hkern u1="F" u2="u" k="33" />
    <hkern u1="F" u2="t" k="16" />
    <hkern u1="F" u2="s" k="49" />
    <hkern u1="F" u2="r" k="49" />
    <hkern u1="F" u2="q" k="33" />
    <hkern u1="F" u2="p" k="31" />
    <hkern u1="F" u2="o" k="47" />
    <hkern u1="F" u2="n" k="63" />
    <hkern u1="F" u2="m" k="47" />
    <hkern u1="F" u2="l" k="31" />
    <hkern u1="F" u2="k" k="31" />
    <hkern u1="F" u2="j" k="49" />
    <hkern u1="F" u2="i" k="63" />
    <hkern u1="F" u2="h" k="33" />
    <hkern u1="F" u2="g" k="49" />
    <hkern u1="F" u2="f" k="16" />
    <hkern u1="F" u2="e" k="31" />
    <hkern u1="F" u2="Y" k="-31" />
    <hkern u1="F" u2="V" k="-33" />
    <hkern u1="F" u2="U" k="-16" />
    <hkern u1="F" u2="T" k="-33" />
    <hkern u1="F" u2="R" k="-16" />
    <hkern u1="F" u2="D" k="-31" />
    <hkern u1="F" u2="A" k="111" />
    <hkern u1="F" u2="&#x3b;" k="47" />
    <hkern u1="F" u2="&#x2c;" k="207" />
    <hkern u1="G" u2="x" k="84" />
    <hkern u1="G" u2="v" k="84" />
    <hkern u1="G" u2="r" k="20" />
    <hkern u1="G" u2="q" k="33" />
    <hkern u1="G" u2="p" k="33" />
    <hkern u1="G" u2="o" k="14" />
    <hkern u1="G" u2="n" k="16" />
    <hkern u1="G" u2="l" k="33" />
    <hkern u1="G" u2="k" k="31" />
    <hkern u1="G" u2="j" k="63" />
    <hkern u1="G" u2="i" k="47" />
    <hkern u1="G" u2="h" k="31" />
    <hkern u1="G" u2="X" k="80" />
    <hkern u1="G" u2="V" k="47" />
    <hkern u1="G" u2="P" k="-16" />
    <hkern u1="G" u2="O" k="-14" />
    <hkern u1="G" u2="M" k="-47" />
    <hkern u1="G" u2="L" k="-33" />
    <hkern u1="G" u2="K" k="-33" />
    <hkern u1="G" u2="I" k="63" />
    <hkern u1="G" u2="H" k="-31" />
    <hkern u1="G" u2="&#x2c;" k="160" />
    <hkern u1="G" u2="&#x29;" k="96" />
    <hkern u1="H" u2="r" k="33" />
    <hkern u1="H" u2="n" k="43" />
    <hkern u1="H" u2="l" k="14" />
    <hkern u1="H" u2="j" k="33" />
    <hkern u1="H" u2="i" k="33" />
    <hkern u1="H" u2="h" k="16" />
    <hkern u1="H" u2="R" k="-31" />
    <hkern u1="H" u2="P" k="-47" />
    <hkern u1="H" u2="K" k="-31" />
    <hkern u1="H" u2="F" k="-16" />
    <hkern u1="H" u2="&#x2f;" k="-63" />
    <hkern u1="I" u2="&#x17f;" k="33" />
    <hkern u1="I" u2="&#x103;" k="82" />
    <hkern u1="I" u2="&#x101;" k="82" />
    <hkern u1="I" u2="&#xe5;" k="82" />
    <hkern u1="I" u2="&#xe4;" k="82" />
    <hkern u1="I" u2="&#xe3;" k="82" />
    <hkern u1="I" u2="&#xe2;" k="82" />
    <hkern u1="I" u2="&#xe1;" k="82" />
    <hkern u1="I" u2="&#xe0;" k="82" />
    <hkern u1="I" u2="a" k="82" />
    <hkern u1="I" u2="&#x2d;" k="63" />
    <hkern u1="I" g2="f_j" k="33" />
    <hkern u1="I" g2="f_f_l" k="33" />
    <hkern u1="I" g2="f_f_i" k="33" />
    <hkern u1="I" g2="fl" k="33" />
    <hkern u1="I" g2="fi" k="33" />
    <hkern u1="I" g2="f_f" k="33" />
    <hkern u1="I" u2="&#x2014;" k="63" />
    <hkern u1="I" u2="&#x2013;" k="63" />
    <hkern u1="I" u2="&#x21b;" k="47" />
    <hkern u1="I" u2="&#x21a;" k="-14" />
    <hkern u1="I" u2="&#x219;" k="47" />
    <hkern u1="I" u2="&#x153;" k="49" />
    <hkern u1="I" u2="&#x152;" k="31" />
    <hkern u1="I" u2="&#x14b;" k="33" />
    <hkern u1="I" u2="&#x14a;" k="-33" />
    <hkern u1="I" u2="&#x149;" k="33" />
    <hkern u1="I" u2="&#x141;" k="-33" />
    <hkern u1="I" u2="&#x132;" k="-33" />
    <hkern u1="I" u2="&#x119;" k="49" />
    <hkern u1="I" u2="&#x118;" k="-33" />
    <hkern u1="I" u2="&#x110;" k="-33" />
    <hkern u1="I" u2="&#x105;" k="82" />
    <hkern u1="I" u2="&#xe6;" k="82" />
    <hkern u1="I" u2="&#xde;" k="-33" />
    <hkern u1="I" u2="&#xd0;" k="-33" />
    <hkern u1="I" u2="y" k="33" />
    <hkern u1="I" u2="x" k="16" />
    <hkern u1="I" u2="w" k="16" />
    <hkern u1="I" u2="v" k="33" />
    <hkern u1="I" u2="u" k="49" />
    <hkern u1="I" u2="t" k="47" />
    <hkern u1="I" u2="s" k="47" />
    <hkern u1="I" u2="r" k="31" />
    <hkern u1="I" u2="q" k="96" />
    <hkern u1="I" u2="p" k="33" />
    <hkern u1="I" u2="o" k="96" />
    <hkern u1="I" u2="n" k="31" />
    <hkern u1="I" u2="m" k="33" />
    <hkern u1="I" u2="l" k="14" />
    <hkern u1="I" u2="k" k="33" />
    <hkern u1="I" u2="j" k="31" />
    <hkern u1="I" u2="i" k="47" />
    <hkern u1="I" u2="h" k="31" />
    <hkern u1="I" u2="g" k="63" />
    <hkern u1="I" u2="f" k="33" />
    <hkern u1="I" u2="e" k="94" />
    <hkern u1="I" u2="d" k="80" />
    <hkern u1="I" u2="c" k="49" />
    <hkern u1="I" u2="b" k="31" />
    <hkern u1="I" u2="U" k="-16" />
    <hkern u1="I" u2="T" k="-14" />
    <hkern u1="I" u2="R" k="-31" />
    <hkern u1="I" u2="Q" k="31" />
    <hkern u1="I" u2="P" k="-47" />
    <hkern u1="I" u2="O" k="31" />
    <hkern u1="I" u2="N" k="-33" />
    <hkern u1="I" u2="L" k="-16" />
    <hkern u1="I" u2="K" k="-31" />
    <hkern u1="I" u2="I" k="-33" />
    <hkern u1="I" u2="H" k="-33" />
    <hkern u1="I" u2="G" k="31" />
    <hkern u1="I" u2="F" k="-16" />
    <hkern u1="I" u2="E" k="-33" />
    <hkern u1="I" u2="D" k="-33" />
    <hkern u1="I" u2="C" k="31" />
    <hkern u1="I" u2="B" k="-33" />
    <hkern u1="I" u2="&#x2f;" k="-63" />
    <hkern u1="J" u2="r" k="20" />
    <hkern u1="J" u2="n" k="20" />
    <hkern u1="J" u2="k" k="16" />
    <hkern u1="J" u2="j" k="14" />
    <hkern u1="J" u2="i" k="49" />
    <hkern u1="J" u2="h" k="16" />
    <hkern u1="J" u2="R" k="-16" />
    <hkern u1="J" u2="O" k="-33" />
    <hkern u1="J" u2="L" k="-16" />
    <hkern u1="J" u2="K" k="-33" />
    <hkern u1="J" u2="&#x2c;" k="113" />
    <hkern u1="K" u2="x" k="-47" />
    <hkern u1="K" u2="v" k="106" />
    <hkern u1="K" u2="r" k="33" />
    <hkern u1="K" u2="q" k="143" />
    <hkern u1="K" u2="o" k="127" />
    <hkern u1="K" u2="n" k="43" />
    <hkern u1="K" u2="l" k="16" />
    <hkern u1="K" u2="k" k="33" />
    <hkern u1="K" u2="i" k="63" />
    <hkern u1="K" u2="h" k="33" />
    <hkern u1="K" u2="e" k="145" />
    <hkern u1="K" u2="X" k="-47" />
    <hkern u1="K" u2="R" k="-31" />
    <hkern u1="K" u2="Q" k="96" />
    <hkern u1="K" u2="P" k="-47" />
    <hkern u1="K" u2="O" k="111" />
    <hkern u1="K" u2="M" k="-49" />
    <hkern u1="K" u2="K" k="-33" />
    <hkern u1="K" u2="H" k="-31" />
    <hkern u1="K" u2="G" k="129" />
    <hkern u1="K" u2="F" k="-16" />
    <hkern u1="K" u2="E" k="-47" />
    <hkern u1="K" u2="D" k="-33" />
    <hkern u1="K" u2="&#x2f;" k="-80" />
    <hkern u1="K" u2="&#x26;" k="47" />
    <hkern u1="L" u2="v" k="150" />
    <hkern u1="L" u2="r" k="16" />
    <hkern u1="L" u2="q" k="49" />
    <hkern u1="L" u2="p" k="16" />
    <hkern u1="L" u2="o" k="33" />
    <hkern u1="L" u2="k" k="16" />
    <hkern u1="L" u2="i" k="49" />
    <hkern u1="L" u2="h" k="33" />
    <hkern u1="L" u2="e" k="33" />
    <hkern u1="L" u2="b" k="16" />
    <hkern u1="L" u2="X" k="-33" />
    <hkern u1="L" u2="V" k="209" />
    <hkern u1="L" u2="R" k="-33" />
    <hkern u1="L" u2="Q" k="33" />
    <hkern u1="L" u2="P" k="-33" />
    <hkern u1="L" u2="O" k="31" />
    <hkern u1="L" u2="N" k="-33" />
    <hkern u1="L" u2="L" k="-33" />
    <hkern u1="L" u2="K" k="-31" />
    <hkern u1="L" u2="G" k="49" />
    <hkern u1="L" u2="E" k="-33" />
    <hkern u1="L" u2="D" k="-33" />
    <hkern u1="L" u2="&#x3f;" k="129" />
    <hkern u1="L" u2="&#x2f;" k="-63" />
    <hkern u1="M" u2="&#x17e;" k="23" />
    <hkern u1="M" u2="&#x17c;" k="23" />
    <hkern u1="M" u2="&#x17a;" k="23" />
    <hkern u1="M" u2="&#x103;" k="16" />
    <hkern u1="M" u2="&#x101;" k="16" />
    <hkern u1="M" u2="&#xe5;" k="16" />
    <hkern u1="M" u2="&#xe4;" k="16" />
    <hkern u1="M" u2="&#xe3;" k="16" />
    <hkern u1="M" u2="&#xe2;" k="16" />
    <hkern u1="M" u2="&#xe1;" k="16" />
    <hkern u1="M" u2="&#xe0;" k="16" />
    <hkern u1="M" u2="z" k="23" />
    <hkern u1="M" u2="a" k="16" />
    <hkern u1="M" u2="&#x21b;" k="43" />
    <hkern u1="M" u2="&#x219;" k="16" />
    <hkern u1="M" u2="&#x218;" k="-16" />
    <hkern u1="M" u2="&#x152;" k="-33" />
    <hkern u1="M" u2="&#x105;" k="16" />
    <hkern u1="M" u2="&#xe6;" k="16" />
    <hkern u1="M" u2="y" k="-16" />
    <hkern u1="M" u2="w" k="43" />
    <hkern u1="M" u2="u" k="23" />
    <hkern u1="M" u2="t" k="43" />
    <hkern u1="M" u2="s" k="16" />
    <hkern u1="M" u2="r" k="20" />
    <hkern u1="M" u2="i" k="31" />
    <hkern u1="M" u2="g" k="16" />
    <hkern u1="M" u2="U" k="-31" />
    <hkern u1="M" u2="S" k="-16" />
    <hkern u1="M" u2="Q" k="-33" />
    <hkern u1="M" u2="P" k="-31" />
    <hkern u1="M" u2="O" k="-33" />
    <hkern u1="M" u2="M" k="-31" />
    <hkern u1="M" u2="K" k="-33" />
    <hkern u1="M" u2="G" k="-33" />
    <hkern u1="M" u2="F" k="-16" />
    <hkern u1="M" u2="E" k="-33" />
    <hkern u1="M" u2="C" k="-33" />
    <hkern u1="M" u2="&#x2f;" k="-96" />
    <hkern u1="N" u2="&#x17e;" k="20" />
    <hkern u1="N" u2="&#x17c;" k="20" />
    <hkern u1="N" u2="&#x17a;" k="20" />
    <hkern u1="N" u2="&#x103;" k="33" />
    <hkern u1="N" u2="&#x101;" k="33" />
    <hkern u1="N" u2="&#xe5;" k="33" />
    <hkern u1="N" u2="&#xe4;" k="33" />
    <hkern u1="N" u2="&#xe3;" k="33" />
    <hkern u1="N" u2="&#xe2;" k="33" />
    <hkern u1="N" u2="&#xe1;" k="33" />
    <hkern u1="N" u2="&#xe0;" k="33" />
    <hkern u1="N" u2="z" k="20" />
    <hkern u1="N" u2="a" k="33" />
    <hkern u1="N" u2="&#x21b;" k="20" />
    <hkern u1="N" u2="&#x219;" k="23" />
    <hkern u1="N" u2="&#x218;" k="-47" />
    <hkern u1="N" u2="&#x105;" k="33" />
    <hkern u1="N" u2="&#xe6;" k="33" />
    <hkern u1="N" u2="w" k="43" />
    <hkern u1="N" u2="v" k="23" />
    <hkern u1="N" u2="u" k="16" />
    <hkern u1="N" u2="t" k="20" />
    <hkern u1="N" u2="s" k="23" />
    <hkern u1="N" u2="r" k="23" />
    <hkern u1="N" u2="q" k="16" />
    <hkern u1="N" u2="n" k="43" />
    <hkern u1="N" u2="l" k="14" />
    <hkern u1="N" u2="j" k="33" />
    <hkern u1="N" u2="i" k="31" />
    <hkern u1="N" u2="h" k="16" />
    <hkern u1="N" u2="g" k="16" />
    <hkern u1="N" u2="X" k="-33" />
    <hkern u1="N" u2="W" k="-31" />
    <hkern u1="N" u2="S" k="-47" />
    <hkern u1="N" u2="R" k="-31" />
    <hkern u1="N" u2="P" k="-16" />
    <hkern u1="N" u2="N" k="-33" />
    <hkern u1="N" u2="L" k="-31" />
    <hkern u1="N" u2="K" k="-31" />
    <hkern u1="N" u2="I" k="-16" />
    <hkern u1="N" u2="G" k="-33" />
    <hkern u1="N" u2="F" k="-14" />
    <hkern u1="N" u2="E" k="-31" />
    <hkern u1="N" u2="D" k="-33" />
    <hkern u1="N" u2="&#x2f;" k="-63" />
    <hkern u1="O" u2="x" k="20" />
    <hkern u1="O" u2="v" k="14" />
    <hkern u1="O" u2="r" k="25" />
    <hkern u1="O" u2="q" k="14" />
    <hkern u1="O" u2="p" k="16" />
    <hkern u1="O" u2="n" k="20" />
    <hkern u1="O" u2="l" k="16" />
    <hkern u1="O" u2="k" k="33" />
    <hkern u1="O" u2="j" k="49" />
    <hkern u1="O" u2="i" k="33" />
    <hkern u1="O" u2="h" k="16" />
    <hkern u1="O" u2="e" k="14" />
    <hkern u1="O" u2="b" k="16" />
    <hkern u1="O" u2="X" k="63" />
    <hkern u1="O" u2="V" k="63" />
    <hkern u1="O" u2="P" k="-31" />
    <hkern u1="O" u2="O" k="-47" />
    <hkern u1="O" u2="M" k="-47" />
    <hkern u1="O" u2="L" k="-31" />
    <hkern u1="O" u2="K" k="-31" />
    <hkern u1="O" u2="I" k="31" />
    <hkern u1="O" u2="H" k="-16" />
    <hkern u1="O" u2="G" k="-16" />
    <hkern u1="O" u2="F" k="-33" />
    <hkern u1="O" u2="D" k="-49" />
    <hkern u1="O" u2="&#x2c;" k="129" />
    <hkern u1="O" u2="&#x29;" k="96" />
    <hkern u1="P" u2="&#x17e;" k="16" />
    <hkern u1="P" u2="&#x17c;" k="16" />
    <hkern u1="P" u2="&#x17a;" k="16" />
    <hkern u1="P" u2="&#x104;" k="143" />
    <hkern u1="P" u2="&#x103;" k="63" />
    <hkern u1="P" u2="&#x102;" k="143" />
    <hkern u1="P" u2="&#x101;" k="63" />
    <hkern u1="P" u2="&#x100;" k="143" />
    <hkern u1="P" u2="&#xe5;" k="63" />
    <hkern u1="P" u2="&#xe4;" k="63" />
    <hkern u1="P" u2="&#xe3;" k="63" />
    <hkern u1="P" u2="&#xe2;" k="63" />
    <hkern u1="P" u2="&#xe1;" k="63" />
    <hkern u1="P" u2="&#xe0;" k="63" />
    <hkern u1="P" u2="&#xc5;" k="143" />
    <hkern u1="P" u2="&#xc4;" k="143" />
    <hkern u1="P" u2="&#xc3;" k="143" />
    <hkern u1="P" u2="&#xc2;" k="143" />
    <hkern u1="P" u2="&#xc1;" k="143" />
    <hkern u1="P" u2="&#xc0;" k="143" />
    <hkern u1="P" u2="z" k="16" />
    <hkern u1="P" u2="a" k="63" />
    <hkern u1="P" u2="&#x2e;" k="176" />
    <hkern u1="P" u2="&#x2026;" k="176" />
    <hkern u1="P" u2="&#x219;" k="20" />
    <hkern u1="P" u2="&#x218;" k="-31" />
    <hkern u1="P" u2="&#x178;" k="49" />
    <hkern u1="P" u2="&#x14b;" k="16" />
    <hkern u1="P" u2="&#x14a;" k="-14" />
    <hkern u1="P" u2="&#x149;" k="16" />
    <hkern u1="P" u2="&#x141;" k="-14" />
    <hkern u1="P" u2="&#x132;" k="-14" />
    <hkern u1="P" u2="&#x118;" k="-14" />
    <hkern u1="P" u2="&#x110;" k="-14" />
    <hkern u1="P" u2="&#x105;" k="63" />
    <hkern u1="P" u2="&#xe6;" k="63" />
    <hkern u1="P" u2="&#xde;" k="-14" />
    <hkern u1="P" u2="&#xd0;" k="-14" />
    <hkern u1="P" u2="&#xc6;" k="143" />
    <hkern u1="P" u2="y" k="-33" />
    <hkern u1="P" u2="x" k="47" />
    <hkern u1="P" u2="w" k="-33" />
    <hkern u1="P" u2="v" k="-14" />
    <hkern u1="P" u2="u" k="43" />
    <hkern u1="P" u2="s" k="20" />
    <hkern u1="P" u2="r" k="16" />
    <hkern u1="P" u2="q" k="49" />
    <hkern u1="P" u2="p" k="33" />
    <hkern u1="P" u2="o" k="49" />
    <hkern u1="P" u2="n" k="16" />
    <hkern u1="P" u2="m" k="16" />
    <hkern u1="P" u2="l" k="16" />
    <hkern u1="P" u2="k" k="33" />
    <hkern u1="P" u2="i" k="47" />
    <hkern u1="P" u2="h" k="33" />
    <hkern u1="P" u2="g" k="63" />
    <hkern u1="P" u2="e" k="49" />
    <hkern u1="P" u2="d" k="63" />
    <hkern u1="P" u2="Z" k="16" />
    <hkern u1="P" u2="Y" k="49" />
    <hkern u1="P" u2="X" k="47" />
    <hkern u1="P" u2="W" k="33" />
    <hkern u1="P" u2="V" k="33" />
    <hkern u1="P" u2="U" k="-33" />
    <hkern u1="P" u2="S" k="-31" />
    <hkern u1="P" u2="R" k="-31" />
    <hkern u1="P" u2="P" k="-31" />
    <hkern u1="P" u2="O" k="-33" />
    <hkern u1="P" u2="N" k="-14" />
    <hkern u1="P" u2="M" k="-31" />
    <hkern u1="P" u2="L" k="-33" />
    <hkern u1="P" u2="K" k="-14" />
    <hkern u1="P" u2="J" k="111" />
    <hkern u1="P" u2="I" k="33" />
    <hkern u1="P" u2="H" k="-14" />
    <hkern u1="P" u2="F" k="-14" />
    <hkern u1="P" u2="E" k="-14" />
    <hkern u1="P" u2="D" k="-14" />
    <hkern u1="P" u2="B" k="-14" />
    <hkern u1="P" u2="A" k="143" />
    <hkern u1="P" u2="&#x2f;" k="66" />
    <hkern u1="P" u2="&#x2c;" k="256" />
    <hkern u1="P" u2="&#x29;" k="63" />
    <hkern u1="P" u2="&#x26;" k="33" />
    <hkern u1="Q" u2="&#x17f;" k="66" />
    <hkern u1="Q" u2="&#x103;" k="66" />
    <hkern u1="Q" u2="&#x101;" k="66" />
    <hkern u1="Q" u2="&#xe5;" k="66" />
    <hkern u1="Q" u2="&#xe4;" k="66" />
    <hkern u1="Q" u2="&#xe3;" k="66" />
    <hkern u1="Q" u2="&#xe2;" k="66" />
    <hkern u1="Q" u2="&#xe1;" k="66" />
    <hkern u1="Q" u2="&#xe0;" k="66" />
    <hkern u1="Q" u2="a" k="66" />
    <hkern u1="Q" u2="&#x2d;" k="33" />
    <hkern u1="Q" g2="f_j" k="66" />
    <hkern u1="Q" g2="f_f_l" k="66" />
    <hkern u1="Q" g2="f_f_i" k="66" />
    <hkern u1="Q" g2="fl" k="66" />
    <hkern u1="Q" g2="fi" k="66" />
    <hkern u1="Q" g2="f_f" k="66" />
    <hkern u1="Q" u2="&#x2014;" k="33" />
    <hkern u1="Q" u2="&#x2013;" k="33" />
    <hkern u1="Q" u2="&#x21b;" k="63" />
    <hkern u1="Q" u2="&#x21a;" k="63" />
    <hkern u1="Q" u2="&#x178;" k="113" />
    <hkern u1="Q" u2="&#x152;" k="33" />
    <hkern u1="Q" u2="&#x14b;" k="31" />
    <hkern u1="Q" u2="&#x149;" k="31" />
    <hkern u1="Q" u2="&#x105;" k="66" />
    <hkern u1="Q" u2="&#xe6;" k="66" />
    <hkern u1="Q" u2="y" k="113" />
    <hkern u1="Q" u2="w" k="20" />
    <hkern u1="Q" u2="v" k="96" />
    <hkern u1="Q" u2="t" k="63" />
    <hkern u1="Q" u2="r" k="43" />
    <hkern u1="Q" u2="q" k="80" />
    <hkern u1="Q" u2="p" k="47" />
    <hkern u1="Q" u2="o" k="63" />
    <hkern u1="Q" u2="n" k="31" />
    <hkern u1="Q" u2="m" k="31" />
    <hkern u1="Q" u2="l" k="33" />
    <hkern u1="Q" u2="j" k="49" />
    <hkern u1="Q" u2="i" k="66" />
    <hkern u1="Q" u2="h" k="63" />
    <hkern u1="Q" u2="g" k="63" />
    <hkern u1="Q" u2="f" k="66" />
    <hkern u1="Q" u2="e" k="63" />
    <hkern u1="Q" u2="Y" k="113" />
    <hkern u1="Q" u2="W" k="80" />
    <hkern u1="Q" u2="V" k="96" />
    <hkern u1="Q" u2="T" k="63" />
    <hkern u1="Q" u2="Q" k="33" />
    <hkern u1="Q" u2="P" k="-16" />
    <hkern u1="Q" u2="O" k="33" />
    <hkern u1="Q" u2="G" k="33" />
    <hkern u1="Q" u2="C" k="33" />
    <hkern u1="R" u2="v" k="-20" />
    <hkern u1="R" u2="r" k="16" />
    <hkern u1="R" u2="q" k="63" />
    <hkern u1="R" u2="p" k="31" />
    <hkern u1="R" u2="o" k="66" />
    <hkern u1="R" u2="k" k="47" />
    <hkern u1="R" u2="j" k="47" />
    <hkern u1="R" u2="i" k="63" />
    <hkern u1="R" u2="h" k="33" />
    <hkern u1="R" u2="e" k="80" />
    <hkern u1="R" u2="V" k="80" />
    <hkern u1="R" u2="P" k="-16" />
    <hkern u1="R" u2="&#x26;" k="31" />
    <hkern u1="S" u2="x" k="43" />
    <hkern u1="S" u2="v" k="-20" />
    <hkern u1="S" u2="n" k="14" />
    <hkern u1="S" u2="j" k="49" />
    <hkern u1="S" u2="i" k="33" />
    <hkern u1="S" u2="h" k="16" />
    <hkern u1="S" u2="V" k="49" />
    <hkern u1="S" u2="R" k="-47" />
    <hkern u1="S" u2="P" k="-47" />
    <hkern u1="S" u2="M" k="-47" />
    <hkern u1="S" u2="L" k="-47" />
    <hkern u1="S" u2="K" k="-47" />
    <hkern u1="S" u2="E" k="-49" />
    <hkern u1="S" u2="&#x2c;" k="31" />
    <hkern u1="S" u2="&#x29;" k="63" />
    <hkern u1="S" u2="&#x26;" k="-49" />
    <hkern u1="T" u2="x" k="96" />
    <hkern u1="T" u2="v" k="213" />
    <hkern u1="T" u2="r" k="96" />
    <hkern u1="T" u2="p" k="96" />
    <hkern u1="T" u2="o" k="270" />
    <hkern u1="T" u2="n" k="129" />
    <hkern u1="T" u2="l" k="63" />
    <hkern u1="T" u2="k" k="66" />
    <hkern u1="T" u2="j" k="80" />
    <hkern u1="T" u2="i" k="66" />
    <hkern u1="T" u2="h" k="66" />
    <hkern u1="T" u2="e" k="225" />
    <hkern u1="T" u2="P" k="-16" />
    <hkern u1="T" u2="O" k="63" />
    <hkern u1="T" u2="L" k="-16" />
    <hkern u1="T" u2="G" k="49" />
    <hkern u1="T" u2="&#x3b;" k="143" />
    <hkern u1="T" u2="&#x3a;" k="127" />
    <hkern u1="T" u2="&#x2f;" k="176" />
    <hkern u1="T" u2="&#x2c;" k="225" />
    <hkern u1="T" u2="&#x26;" k="113" />
    <hkern u1="U" u2="x" k="16" />
    <hkern u1="U" u2="n" k="16" />
    <hkern u1="U" u2="k" k="16" />
    <hkern u1="U" u2="j" k="47" />
    <hkern u1="U" u2="i" k="33" />
    <hkern u1="U" u2="R" k="-47" />
    <hkern u1="U" u2="O" k="-33" />
    <hkern u1="U" u2="N" k="-49" />
    <hkern u1="U" u2="M" k="-47" />
    <hkern u1="U" u2="L" k="-47" />
    <hkern u1="U" u2="K" k="-47" />
    <hkern u1="U" u2="I" k="-16" />
    <hkern u1="U" u2="H" k="-31" />
    <hkern u1="U" u2="G" k="-33" />
    <hkern u1="U" u2="D" k="-49" />
    <hkern u1="U" u2="&#x2c;" k="66" />
    <hkern u1="V" u2="&#x17f;" k="113" />
    <hkern u1="V" u2="&#x17e;" k="150" />
    <hkern u1="V" u2="&#x17c;" k="150" />
    <hkern u1="V" u2="&#x17a;" k="150" />
    <hkern u1="V" u2="&#x104;" k="207" />
    <hkern u1="V" u2="&#x103;" k="209" />
    <hkern u1="V" u2="&#x102;" k="207" />
    <hkern u1="V" u2="&#x101;" k="209" />
    <hkern u1="V" u2="&#x100;" k="207" />
    <hkern u1="V" u2="&#xe5;" k="209" />
    <hkern u1="V" u2="&#xe4;" k="209" />
    <hkern u1="V" u2="&#xe3;" k="209" />
    <hkern u1="V" u2="&#xe2;" k="209" />
    <hkern u1="V" u2="&#xe1;" k="209" />
    <hkern u1="V" u2="&#xe0;" k="209" />
    <hkern u1="V" u2="&#xc5;" k="207" />
    <hkern u1="V" u2="&#xc4;" k="207" />
    <hkern u1="V" u2="&#xc3;" k="207" />
    <hkern u1="V" u2="&#xc2;" k="207" />
    <hkern u1="V" u2="&#xc1;" k="207" />
    <hkern u1="V" u2="&#xc0;" k="207" />
    <hkern u1="V" u2="z" k="150" />
    <hkern u1="V" u2="a" k="209" />
    <hkern u1="V" u2="&#x2e;" k="209" />
    <hkern u1="V" u2="&#x2d;" k="66" />
    <hkern u1="V" g2="f_j" k="113" />
    <hkern u1="V" g2="f_f_l" k="113" />
    <hkern u1="V" g2="f_f_i" k="113" />
    <hkern u1="V" g2="fl" k="113" />
    <hkern u1="V" g2="fi" k="113" />
    <hkern u1="V" g2="f_f" k="113" />
    <hkern u1="V" u2="&#x2026;" k="209" />
    <hkern u1="V" u2="&#x2014;" k="66" />
    <hkern u1="V" u2="&#x2013;" k="66" />
    <hkern u1="V" u2="&#x21b;" k="86" />
    <hkern u1="V" u2="&#x219;" k="176" />
    <hkern u1="V" u2="&#x218;" k="33" />
    <hkern u1="V" u2="&#x153;" k="223" />
    <hkern u1="V" u2="&#x152;" k="47" />
    <hkern u1="V" u2="&#x14b;" k="127" />
    <hkern u1="V" u2="&#x14a;" k="-33" />
    <hkern u1="V" u2="&#x149;" k="127" />
    <hkern u1="V" u2="&#x141;" k="-33" />
    <hkern u1="V" u2="&#x132;" k="-33" />
    <hkern u1="V" u2="&#x119;" k="223" />
    <hkern u1="V" u2="&#x118;" k="-33" />
    <hkern u1="V" u2="&#x110;" k="-33" />
    <hkern u1="V" u2="&#x105;" k="209" />
    <hkern u1="V" u2="&#xe6;" k="209" />
    <hkern u1="V" u2="&#xde;" k="-33" />
    <hkern u1="V" u2="&#xd0;" k="-33" />
    <hkern u1="V" u2="&#xc6;" k="207" />
    <hkern u1="V" u2="y" k="63" />
    <hkern u1="V" u2="x" k="63" />
    <hkern u1="V" u2="w" k="63" />
    <hkern u1="V" u2="v" k="86" />
    <hkern u1="V" u2="u" k="150" />
    <hkern u1="V" u2="t" k="86" />
    <hkern u1="V" u2="s" k="176" />
    <hkern u1="V" u2="r" k="113" />
    <hkern u1="V" u2="q" k="209" />
    <hkern u1="V" u2="p" k="111" />
    <hkern u1="V" u2="o" k="209" />
    <hkern u1="V" u2="n" k="150" />
    <hkern u1="V" u2="m" k="127" />
    <hkern u1="V" u2="l" k="49" />
    <hkern u1="V" u2="k" k="47" />
    <hkern u1="V" u2="j" k="63" />
    <hkern u1="V" u2="i" k="47" />
    <hkern u1="V" u2="h" k="49" />
    <hkern u1="V" u2="g" k="209" />
    <hkern u1="V" u2="f" k="113" />
    <hkern u1="V" u2="e" k="209" />
    <hkern u1="V" u2="c" k="223" />
    <hkern u1="V" u2="S" k="33" />
    <hkern u1="V" u2="R" k="-33" />
    <hkern u1="V" u2="Q" k="33" />
    <hkern u1="V" u2="P" k="-33" />
    <hkern u1="V" u2="O" k="80" />
    <hkern u1="V" u2="N" k="-33" />
    <hkern u1="V" u2="M" k="-31" />
    <hkern u1="V" u2="L" k="-33" />
    <hkern u1="V" u2="K" k="-33" />
    <hkern u1="V" u2="J" k="113" />
    <hkern u1="V" u2="I" k="-33" />
    <hkern u1="V" u2="H" k="-33" />
    <hkern u1="V" u2="G" k="63" />
    <hkern u1="V" u2="F" k="-14" />
    <hkern u1="V" u2="E" k="-16" />
    <hkern u1="V" u2="D" k="-33" />
    <hkern u1="V" u2="C" k="47" />
    <hkern u1="V" u2="B" k="-33" />
    <hkern u1="V" u2="A" k="207" />
    <hkern u1="V" u2="&#x3f;" k="63" />
    <hkern u1="V" u2="&#x3b;" k="129" />
    <hkern u1="V" u2="&#x3a;" k="96" />
    <hkern u1="V" u2="&#x2f;" k="143" />
    <hkern u1="V" u2="&#x2c;" k="289" />
    <hkern u1="V" u2="&#x26;" k="127" />
    <hkern u1="W" u2="x" k="84" />
    <hkern u1="W" u2="v" k="86" />
    <hkern u1="W" u2="r" k="96" />
    <hkern u1="W" u2="q" k="176" />
    <hkern u1="W" u2="p" k="96" />
    <hkern u1="W" u2="o" k="193" />
    <hkern u1="W" u2="n" k="127" />
    <hkern u1="W" u2="l" k="31" />
    <hkern u1="W" u2="k" k="49" />
    <hkern u1="W" u2="j" k="63" />
    <hkern u1="W" u2="i" k="63" />
    <hkern u1="W" u2="h" k="47" />
    <hkern u1="W" u2="e" k="190" />
    <hkern u1="W" u2="X" k="-33" />
    <hkern u1="W" u2="Q" k="31" />
    <hkern u1="W" u2="P" k="-49" />
    <hkern u1="W" u2="O" k="63" />
    <hkern u1="W" u2="N" k="-47" />
    <hkern u1="W" u2="L" k="-31" />
    <hkern u1="W" u2="I" k="16" />
    <hkern u1="W" u2="G" k="47" />
    <hkern u1="W" u2="D" k="-31" />
    <hkern u1="W" u2="&#x3f;" k="33" />
    <hkern u1="W" u2="&#x3b;" k="143" />
    <hkern u1="W" u2="&#x3a;" k="80" />
    <hkern u1="W" u2="&#x2f;" k="143" />
    <hkern u1="W" u2="&#x2c;" k="225" />
    <hkern u1="W" u2="&#x26;" k="94" />
    <hkern u1="X" u2="&#x17f;" k="145" />
    <hkern u1="X" u2="&#x104;" k="-16" />
    <hkern u1="X" u2="&#x103;" k="129" />
    <hkern u1="X" u2="&#x102;" k="-16" />
    <hkern u1="X" u2="&#x101;" k="129" />
    <hkern u1="X" u2="&#x100;" k="-16" />
    <hkern u1="X" u2="&#xe5;" k="129" />
    <hkern u1="X" u2="&#xe4;" k="129" />
    <hkern u1="X" u2="&#xe3;" k="129" />
    <hkern u1="X" u2="&#xe2;" k="129" />
    <hkern u1="X" u2="&#xe1;" k="129" />
    <hkern u1="X" u2="&#xe0;" k="129" />
    <hkern u1="X" u2="&#xc5;" k="-16" />
    <hkern u1="X" u2="&#xc4;" k="-16" />
    <hkern u1="X" u2="&#xc3;" k="-16" />
    <hkern u1="X" u2="&#xc2;" k="-16" />
    <hkern u1="X" u2="&#xc1;" k="-16" />
    <hkern u1="X" u2="&#xc0;" k="-16" />
    <hkern u1="X" u2="a" k="129" />
    <hkern u1="X" u2="&#x2d;" k="96" />
    <hkern u1="X" g2="f_j" k="145" />
    <hkern u1="X" g2="f_f_l" k="145" />
    <hkern u1="X" g2="f_f_i" k="145" />
    <hkern u1="X" g2="fl" k="145" />
    <hkern u1="X" g2="fi" k="145" />
    <hkern u1="X" g2="f_f" k="145" />
    <hkern u1="X" u2="&#x2014;" k="96" />
    <hkern u1="X" u2="&#x2013;" k="96" />
    <hkern u1="X" u2="&#x21b;" k="43" />
    <hkern u1="X" u2="&#x219;" k="33" />
    <hkern u1="X" u2="&#x218;" k="33" />
    <hkern u1="X" u2="&#x178;" k="-33" />
    <hkern u1="X" u2="&#x153;" k="145" />
    <hkern u1="X" u2="&#x152;" k="63" />
    <hkern u1="X" u2="&#x14b;" k="33" />
    <hkern u1="X" u2="&#x14a;" k="-33" />
    <hkern u1="X" u2="&#x149;" k="33" />
    <hkern u1="X" u2="&#x141;" k="-33" />
    <hkern u1="X" u2="&#x132;" k="-33" />
    <hkern u1="X" u2="&#x119;" k="145" />
    <hkern u1="X" u2="&#x118;" k="-33" />
    <hkern u1="X" u2="&#x110;" k="-33" />
    <hkern u1="X" u2="&#x105;" k="129" />
    <hkern u1="X" u2="&#xe6;" k="129" />
    <hkern u1="X" u2="&#xde;" k="-33" />
    <hkern u1="X" u2="&#xd0;" k="-33" />
    <hkern u1="X" u2="&#xc6;" k="-16" />
    <hkern u1="X" u2="y" k="63" />
    <hkern u1="X" u2="w" k="43" />
    <hkern u1="X" u2="v" k="41" />
    <hkern u1="X" u2="u" k="66" />
    <hkern u1="X" u2="t" k="43" />
    <hkern u1="X" u2="s" k="33" />
    <hkern u1="X" u2="r" k="33" />
    <hkern u1="X" u2="q" k="111" />
    <hkern u1="X" u2="p" k="16" />
    <hkern u1="X" u2="o" k="127" />
    <hkern u1="X" u2="n" k="31" />
    <hkern u1="X" u2="m" k="33" />
    <hkern u1="X" u2="l" k="16" />
    <hkern u1="X" u2="k" k="49" />
    <hkern u1="X" u2="j" k="49" />
    <hkern u1="X" u2="i" k="47" />
    <hkern u1="X" u2="h" k="33" />
    <hkern u1="X" u2="g" k="96" />
    <hkern u1="X" u2="f" k="145" />
    <hkern u1="X" u2="e" k="160" />
    <hkern u1="X" u2="c" k="145" />
    <hkern u1="X" u2="Y" k="-33" />
    <hkern u1="X" u2="X" k="-47" />
    <hkern u1="X" u2="W" k="-16" />
    <hkern u1="X" u2="S" k="33" />
    <hkern u1="X" u2="R" k="-33" />
    <hkern u1="X" u2="Q" k="63" />
    <hkern u1="X" u2="P" k="-49" />
    <hkern u1="X" u2="O" k="80" />
    <hkern u1="X" u2="N" k="-33" />
    <hkern u1="X" u2="M" k="-31" />
    <hkern u1="X" u2="L" k="-33" />
    <hkern u1="X" u2="K" k="-33" />
    <hkern u1="X" u2="I" k="-33" />
    <hkern u1="X" u2="H" k="-16" />
    <hkern u1="X" u2="G" k="80" />
    <hkern u1="X" u2="F" k="-33" />
    <hkern u1="X" u2="E" k="-33" />
    <hkern u1="X" u2="D" k="-47" />
    <hkern u1="X" u2="C" k="63" />
    <hkern u1="X" u2="B" k="-33" />
    <hkern u1="X" u2="A" k="-16" />
    <hkern u1="X" u2="&#x3f;" k="80" />
    <hkern u1="X" u2="&#x2f;" k="-63" />
    <hkern u1="X" u2="&#x26;" k="63" />
    <hkern u1="Y" u2="x" k="127" />
    <hkern u1="Y" u2="v" k="106" />
    <hkern u1="Y" u2="r" k="172" />
    <hkern u1="Y" u2="q" k="256" />
    <hkern u1="Y" u2="p" k="160" />
    <hkern u1="Y" u2="o" k="272" />
    <hkern u1="Y" u2="n" k="172" />
    <hkern u1="Y" u2="l" k="33" />
    <hkern u1="Y" u2="k" k="49" />
    <hkern u1="Y" u2="j" k="49" />
    <hkern u1="Y" u2="i" k="63" />
    <hkern u1="Y" u2="h" k="31" />
    <hkern u1="Y" u2="e" k="303" />
    <hkern u1="Y" u2="X" k="-31" />
    <hkern u1="Y" u2="V" k="-47" />
    <hkern u1="Y" u2="Q" k="96" />
    <hkern u1="Y" u2="M" k="-33" />
    <hkern u1="Y" u2="G" k="127" />
    <hkern u1="Y" u2="F" k="-31" />
    <hkern u1="Y" u2="E" k="-16" />
    <hkern u1="Y" u2="D" k="-33" />
    <hkern u1="Y" u2="&#x3f;" k="47" />
    <hkern u1="Y" u2="&#x3b;" k="176" />
    <hkern u1="Y" u2="&#x3a;" k="127" />
    <hkern u1="Y" u2="&#x2f;" k="145" />
    <hkern u1="Y" u2="&#x2c;" k="240" />
    <hkern u1="Y" u2="&#x26;" k="143" />
    <hkern u1="Z" u2="x" k="16" />
    <hkern u1="Z" u2="v" k="47" />
    <hkern u1="Z" u2="r" k="49" />
    <hkern u1="Z" u2="p" k="47" />
    <hkern u1="Z" u2="l" k="47" />
    <hkern u1="Z" u2="k" k="47" />
    <hkern u1="Z" u2="j" k="63" />
    <hkern u1="Z" u2="i" k="63" />
    <hkern u1="Z" u2="h" k="49" />
    <hkern u1="Z" u2="e" k="80" />
    <hkern u1="Z" u2="b" k="49" />
    <hkern u1="Z" u2="V" k="16" />
    <hkern u1="Z" u2="R" k="-33" />
    <hkern u1="Z" u2="Q" k="49" />
    <hkern u1="Z" u2="P" k="-33" />
    <hkern u1="Z" u2="L" k="-14" />
    <hkern u1="Z" u2="K" k="-33" />
    <hkern u1="Z" u2="G" k="63" />
    <hkern u1="Z" u2="E" k="-16" />
    <hkern u1="Z" u2="D" k="-31" />
    <hkern u1="Z" u2="&#x3a;" k="-16" />
    <hkern u1="Z" u2="&#x29;" k="16" />
    <hkern u1="[" u2="&#x149;" k="-35" />
    <hkern u1="a" u2="v" k="-16" />
    <hkern u1="a" u2="j" k="-43" />
    <hkern u1="b" u2="y" k="23" />
    <hkern u1="b" u2="x" k="49" />
    <hkern u1="b" u2="v" k="14" />
    <hkern u1="b" u2="N" k="-49" />
    <hkern u1="b" u2="L" k="-80" />
    <hkern u1="c" u2="&#x17e;" k="31" />
    <hkern u1="c" u2="&#x17c;" k="31" />
    <hkern u1="c" u2="&#x17a;" k="31" />
    <hkern u1="c" u2="z" k="31" />
    <hkern u1="c" u2="&#x21b;" k="33" />
    <hkern u1="c" u2="&#x152;" k="-80" />
    <hkern u1="c" u2="y" k="31" />
    <hkern u1="c" u2="x" k="31" />
    <hkern u1="c" u2="v" k="20" />
    <hkern u1="c" u2="t" k="33" />
    <hkern u1="c" u2="k" k="-23" />
    <hkern u1="c" u2="g" k="-23" />
    <hkern u1="c" u2="b" k="16" />
    <hkern u1="c" u2="Q" k="-66" />
    <hkern u1="c" u2="O" k="-80" />
    <hkern u1="c" u2="L" k="-80" />
    <hkern u1="c" u2="G" k="-80" />
    <hkern u1="c" u2="D" k="-49" />
    <hkern u1="c" u2="C" k="-80" />
    <hkern u1="d" u2="y" k="-14" />
    <hkern u1="d" u2="w" k="-16" />
    <hkern u1="d" u2="v" k="-33" />
    <hkern u1="d" u2="u" k="-20" />
    <hkern u1="d" u2="j" k="-43" />
    <hkern u1="d" u2="E" k="-66" />
    <hkern u1="e" u2="&#x17f;" k="20" />
    <hkern u1="e" u2="&#x17e;" k="47" />
    <hkern u1="e" u2="&#x17c;" k="47" />
    <hkern u1="e" u2="&#x17a;" k="47" />
    <hkern u1="e" u2="z" k="47" />
    <hkern u1="e" g2="f_j" k="20" />
    <hkern u1="e" g2="f_f_l" k="20" />
    <hkern u1="e" g2="f_f_i" k="20" />
    <hkern u1="e" g2="fl" k="20" />
    <hkern u1="e" g2="fi" k="20" />
    <hkern u1="e" g2="f_f" k="20" />
    <hkern u1="e" u2="&#x21b;" k="47" />
    <hkern u1="e" u2="&#x219;" k="33" />
    <hkern u1="e" u2="&#x14b;" k="16" />
    <hkern u1="e" u2="&#x149;" k="16" />
    <hkern u1="e" u2="y" k="63" />
    <hkern u1="e" u2="x" k="80" />
    <hkern u1="e" u2="w" k="41" />
    <hkern u1="e" u2="v" k="20" />
    <hkern u1="e" u2="u" k="20" />
    <hkern u1="e" u2="t" k="47" />
    <hkern u1="e" u2="s" k="33" />
    <hkern u1="e" u2="r" k="16" />
    <hkern u1="e" u2="q" k="20" />
    <hkern u1="e" u2="p" k="20" />
    <hkern u1="e" u2="o" k="16" />
    <hkern u1="e" u2="n" k="31" />
    <hkern u1="e" u2="m" k="16" />
    <hkern u1="e" u2="k" k="43" />
    <hkern u1="e" u2="j" k="47" />
    <hkern u1="e" u2="i" k="33" />
    <hkern u1="e" u2="f" k="20" />
    <hkern u1="e" u2="d" k="16" />
    <hkern u1="e" u2="b" k="16" />
    <hkern u1="e" u2="F" k="-31" />
    <hkern u1="f" u2="&#x149;" k="-27" />
    <hkern u1="f" u2="x" k="-16" />
    <hkern u1="f" u2="v" k="-49" />
    <hkern u1="f" u2="r" k="16" />
    <hkern u1="f" u2="q" k="47" />
    <hkern u1="f" u2="o" k="47" />
    <hkern u1="f" u2="j" k="16" />
    <hkern u1="f" u2="i" k="16" />
    <hkern u1="f" u2="e" k="47" />
    <hkern u1="f" u2="R" k="-80" />
    <hkern u1="f" u2="P" k="-63" />
    <hkern u1="f" u2="M" k="-80" />
    <hkern u1="f" u2="L" k="-80" />
    <hkern u1="f" u2="H" k="-113" />
    <hkern u1="f" u2="G" k="-49" />
    <hkern u1="f" u2="D" k="-63" />
    <hkern u1="f" u2="&#x2c;" k="96" />
    <hkern u1="g" u2="y" k="-31" />
    <hkern u1="g" u2="w" k="-23" />
    <hkern u1="g" u2="v" k="-20" />
    <hkern u1="g" u2="q" k="-16" />
    <hkern u1="g" u2="l" k="-20" />
    <hkern u1="g" u2="j" k="-160" />
    <hkern u1="g" u2="d" k="-16" />
    <hkern u1="g" u2="H" k="-66" />
    <hkern u1="h" u2="v" k="49" />
    <hkern u1="h" u2="R" k="-66" />
    <hkern u1="h" u2="I" k="-31" />
    <hkern u1="i" u2="&#x2019;" k="-41" />
    <hkern u1="i" u2="&#x17e;" k="-14" />
    <hkern u1="i" u2="&#x17c;" k="-14" />
    <hkern u1="i" u2="&#x17a;" k="-14" />
    <hkern u1="i" u2="z" k="-14" />
    <hkern u1="i" u2="&#x201d;" k="-41" />
    <hkern u1="i" u2="&#x178;" k="-49" />
    <hkern u1="i" u2="&#x14a;" k="-33" />
    <hkern u1="i" u2="&#x141;" k="-33" />
    <hkern u1="i" u2="&#x132;" k="-33" />
    <hkern u1="i" u2="&#x118;" k="-33" />
    <hkern u1="i" u2="&#x110;" k="-33" />
    <hkern u1="i" u2="&#xde;" k="-33" />
    <hkern u1="i" u2="&#xd0;" k="-33" />
    <hkern u1="i" u2="x" k="-16" />
    <hkern u1="i" u2="w" k="-33" />
    <hkern u1="i" u2="v" k="-43" />
    <hkern u1="i" u2="p" k="-23" />
    <hkern u1="i" u2="n" k="-20" />
    <hkern u1="i" u2="l" k="-23" />
    <hkern u1="i" u2="k" k="-43" />
    <hkern u1="i" u2="e" k="-14" />
    <hkern u1="i" u2="Y" k="-49" />
    <hkern u1="i" u2="X" k="-47" />
    <hkern u1="i" u2="U" k="-63" />
    <hkern u1="i" u2="R" k="-33" />
    <hkern u1="i" u2="P" k="-33" />
    <hkern u1="i" u2="N" k="-63" />
    <hkern u1="i" u2="L" k="-33" />
    <hkern u1="i" u2="K" k="-63" />
    <hkern u1="i" u2="J" k="-49" />
    <hkern u1="i" u2="I" k="-33" />
    <hkern u1="i" u2="H" k="-33" />
    <hkern u1="i" u2="F" k="-33" />
    <hkern u1="i" u2="E" k="-33" />
    <hkern u1="i" u2="D" k="-33" />
    <hkern u1="i" u2="B" k="-33" />
    <hkern u1="j" u2="u" k="-20" />
    <hkern u1="j" u2="j" k="-129" />
    <hkern u1="j" u2="X" k="-80" />
    <hkern u1="j" u2="U" k="-80" />
    <hkern u1="j" u2="K" k="-96" />
    <hkern u1="j" u2="F" k="-63" />
    <hkern u1="j" u2="E" k="-66" />
    <hkern u1="k" u2="x" k="-49" />
    <hkern u1="k" u2="v" k="-43" />
    <hkern u1="k" u2="M" k="-80" />
    <hkern u1="k" u2="L" k="-80" />
    <hkern u1="k" u2="D" k="-80" />
    <hkern u1="l" u2="&#x2019;" k="-41" />
    <hkern u1="l" u2="&#x17e;" k="-23" />
    <hkern u1="l" u2="&#x17c;" k="-23" />
    <hkern u1="l" u2="&#x17a;" k="-23" />
    <hkern u1="l" u2="z" k="-23" />
    <hkern u1="l" u2="&#x201d;" k="-41" />
    <hkern u1="l" u2="&#x21b;" k="-16" />
    <hkern u1="l" u2="&#x219;" k="-16" />
    <hkern u1="l" u2="&#x14a;" k="-49" />
    <hkern u1="l" u2="&#x141;" k="-49" />
    <hkern u1="l" u2="&#x132;" k="-49" />
    <hkern u1="l" u2="&#x118;" k="-49" />
    <hkern u1="l" u2="&#x110;" k="-49" />
    <hkern u1="l" u2="&#xde;" k="-49" />
    <hkern u1="l" u2="&#xd0;" k="-49" />
    <hkern u1="l" u2="y" k="-16" />
    <hkern u1="l" u2="x" k="-43" />
    <hkern u1="l" u2="w" k="-31" />
    <hkern u1="l" u2="v" k="-43" />
    <hkern u1="l" u2="t" k="-16" />
    <hkern u1="l" u2="s" k="-16" />
    <hkern u1="l" u2="r" k="-16" />
    <hkern u1="l" u2="q" k="-16" />
    <hkern u1="l" u2="l" k="-23" />
    <hkern u1="l" u2="g" k="-14" />
    <hkern u1="l" u2="X" k="-49" />
    <hkern u1="l" u2="U" k="-63" />
    <hkern u1="l" u2="R" k="-49" />
    <hkern u1="l" u2="P" k="-49" />
    <hkern u1="l" u2="N" k="-49" />
    <hkern u1="l" u2="M" k="-96" />
    <hkern u1="l" u2="L" k="-49" />
    <hkern u1="l" u2="K" k="-49" />
    <hkern u1="l" u2="I" k="-49" />
    <hkern u1="l" u2="H" k="-49" />
    <hkern u1="l" u2="F" k="-63" />
    <hkern u1="l" u2="E" k="-31" />
    <hkern u1="l" u2="D" k="-80" />
    <hkern u1="l" u2="B" k="-49" />
    <hkern u1="m" u2="&#x21b;" k="43" />
    <hkern u1="m" u2="y" k="33" />
    <hkern u1="m" u2="w" k="31" />
    <hkern u1="m" u2="v" k="33" />
    <hkern u1="m" u2="t" k="43" />
    <hkern u1="m" u2="R" k="-66" />
    <hkern u1="m" u2="N" k="-80" />
    <hkern u1="m" u2="I" k="-31" />
    <hkern u1="n" u2="&#x2019;" k="86" />
    <hkern u1="n" u2="&#x201d;" k="86" />
    <hkern u1="n" u2="&#x21b;" k="43" />
    <hkern u1="n" u2="y" k="43" />
    <hkern u1="n" u2="w" k="43" />
    <hkern u1="n" u2="v" k="16" />
    <hkern u1="n" u2="u" k="14" />
    <hkern u1="n" u2="t" k="43" />
    <hkern u1="n" u2="p" k="20" />
    <hkern u1="n" u2="R" k="-66" />
    <hkern u1="n" u2="P" k="-33" />
    <hkern u1="n" u2="O" k="-47" />
    <hkern u1="n" u2="I" k="-31" />
    <hkern u1="o" u2="x" k="43" />
    <hkern u1="o" u2="v" k="47" />
    <hkern u1="o" u2="r" k="16" />
    <hkern u1="o" u2="p" k="20" />
    <hkern u1="o" u2="n" k="16" />
    <hkern u1="o" u2="i" k="16" />
    <hkern u1="o" u2="h" k="14" />
    <hkern u1="o" u2="b" k="16" />
    <hkern u1="o" u2="P" k="-63" />
    <hkern u1="o" u2="D" k="-31" />
    <hkern u1="p" u2="&#x21b;" k="31" />
    <hkern u1="p" u2="&#x14b;" k="16" />
    <hkern u1="p" u2="&#x149;" k="16" />
    <hkern u1="p" u2="y" k="43" />
    <hkern u1="p" u2="x" k="66" />
    <hkern u1="p" u2="w" k="33" />
    <hkern u1="p" u2="v" k="49" />
    <hkern u1="p" u2="u" k="20" />
    <hkern u1="p" u2="t" k="31" />
    <hkern u1="p" u2="r" k="16" />
    <hkern u1="p" u2="n" k="16" />
    <hkern u1="p" u2="m" k="16" />
    <hkern u1="p" u2="Q" k="-47" />
    <hkern u1="p" u2="N" k="-49" />
    <hkern u1="p" u2="L" k="-80" />
    <hkern u1="q" u2="y" k="-33" />
    <hkern u1="q" u2="w" k="-14" />
    <hkern u1="q" u2="v" k="-16" />
    <hkern u1="q" u2="u" k="-43" />
    <hkern u1="q" u2="l" k="-33" />
    <hkern u1="q" u2="R" k="-80" />
    <hkern u1="q" u2="L" k="-66" />
    <hkern u1="r" u2="x" k="-31" />
    <hkern u1="r" u2="v" k="-47" />
    <hkern u1="r" u2="e" k="33" />
    <hkern u1="r" u2="&#x2c;" k="106" />
    <hkern u1="s" u2="&#x17f;" k="-33" />
    <hkern u1="s" u2="&#x103;" k="-16" />
    <hkern u1="s" u2="&#x101;" k="-16" />
    <hkern u1="s" u2="&#xe5;" k="-16" />
    <hkern u1="s" u2="&#xe4;" k="-16" />
    <hkern u1="s" u2="&#xe3;" k="-16" />
    <hkern u1="s" u2="&#xe2;" k="-16" />
    <hkern u1="s" u2="&#xe1;" k="-16" />
    <hkern u1="s" u2="&#xe0;" k="-16" />
    <hkern u1="s" u2="a" k="-16" />
    <hkern u1="s" g2="f_j" k="-33" />
    <hkern u1="s" g2="f_f_l" k="-33" />
    <hkern u1="s" g2="f_f_i" k="-33" />
    <hkern u1="s" g2="fl" k="-33" />
    <hkern u1="s" g2="fi" k="-33" />
    <hkern u1="s" g2="f_f" k="-33" />
    <hkern u1="s" u2="&#x21a;" k="49" />
    <hkern u1="s" u2="&#x153;" k="-16" />
    <hkern u1="s" u2="&#x14b;" k="-16" />
    <hkern u1="s" u2="&#x14a;" k="-80" />
    <hkern u1="s" u2="&#x149;" k="-16" />
    <hkern u1="s" u2="&#x141;" k="-80" />
    <hkern u1="s" u2="&#x132;" k="-80" />
    <hkern u1="s" u2="&#x119;" k="-16" />
    <hkern u1="s" u2="&#x118;" k="-80" />
    <hkern u1="s" u2="&#x110;" k="-80" />
    <hkern u1="s" u2="&#x105;" k="-16" />
    <hkern u1="s" u2="&#xe6;" k="-16" />
    <hkern u1="s" u2="&#xde;" k="-80" />
    <hkern u1="s" u2="&#xd0;" k="-80" />
    <hkern u1="s" u2="v" k="16" />
    <hkern u1="s" u2="r" k="-16" />
    <hkern u1="s" u2="q" k="-16" />
    <hkern u1="s" u2="o" k="-16" />
    <hkern u1="s" u2="n" k="-16" />
    <hkern u1="s" u2="m" k="-16" />
    <hkern u1="s" u2="l" k="-33" />
    <hkern u1="s" u2="f" k="-33" />
    <hkern u1="s" u2="e" k="-16" />
    <hkern u1="s" u2="d" k="-31" />
    <hkern u1="s" u2="c" k="-16" />
    <hkern u1="s" u2="T" k="49" />
    <hkern u1="s" u2="R" k="-80" />
    <hkern u1="s" u2="P" k="-80" />
    <hkern u1="s" u2="N" k="-80" />
    <hkern u1="s" u2="L" k="-80" />
    <hkern u1="s" u2="K" k="-80" />
    <hkern u1="s" u2="I" k="-80" />
    <hkern u1="s" u2="H" k="-80" />
    <hkern u1="s" u2="F" k="-80" />
    <hkern u1="s" u2="E" k="-80" />
    <hkern u1="s" u2="D" k="-80" />
    <hkern u1="s" u2="B" k="-80" />
    <hkern u1="t" u2="&#x2019;" k="-84" />
    <hkern u1="t" u2="&#x17e;" k="-16" />
    <hkern u1="t" u2="&#x17c;" k="-16" />
    <hkern u1="t" u2="&#x17a;" k="-16" />
    <hkern u1="t" u2="&#x103;" k="47" />
    <hkern u1="t" u2="&#x101;" k="47" />
    <hkern u1="t" u2="&#xe5;" k="47" />
    <hkern u1="t" u2="&#xe4;" k="47" />
    <hkern u1="t" u2="&#xe3;" k="47" />
    <hkern u1="t" u2="&#xe2;" k="47" />
    <hkern u1="t" u2="&#xe1;" k="47" />
    <hkern u1="t" u2="&#xe0;" k="47" />
    <hkern u1="t" u2="z" k="-16" />
    <hkern u1="t" u2="a" k="47" />
    <hkern u1="t" u2="&#x201d;" k="-84" />
    <hkern u1="t" u2="&#x153;" k="49" />
    <hkern u1="t" u2="&#x119;" k="49" />
    <hkern u1="t" u2="&#x105;" k="47" />
    <hkern u1="t" u2="&#xe6;" k="47" />
    <hkern u1="t" u2="y" k="-31" />
    <hkern u1="t" u2="x" k="-16" />
    <hkern u1="t" u2="w" k="-33" />
    <hkern u1="t" u2="v" k="-14" />
    <hkern u1="t" u2="q" k="47" />
    <hkern u1="t" u2="o" k="49" />
    <hkern u1="t" u2="g" k="49" />
    <hkern u1="t" u2="e" k="49" />
    <hkern u1="t" u2="d" k="47" />
    <hkern u1="t" u2="c" k="49" />
    <hkern u1="t" u2="U" k="-63" />
    <hkern u1="u" u2="&#x17f;" k="-16" />
    <hkern u1="u" g2="f_j" k="-16" />
    <hkern u1="u" g2="f_f_l" k="-16" />
    <hkern u1="u" g2="f_f_i" k="-16" />
    <hkern u1="u" g2="fl" k="-16" />
    <hkern u1="u" g2="fi" k="-16" />
    <hkern u1="u" g2="f_f" k="-16" />
    <hkern u1="u" u2="x" k="-23" />
    <hkern u1="u" u2="i" k="-20" />
    <hkern u1="u" u2="f" k="-16" />
    <hkern u1="v" u2="&#x17f;" k="-80" />
    <hkern u1="v" u2="&#x17e;" k="-33" />
    <hkern u1="v" u2="&#x17c;" k="-33" />
    <hkern u1="v" u2="&#x17a;" k="-33" />
    <hkern u1="v" u2="&#x103;" k="33" />
    <hkern u1="v" u2="&#x101;" k="33" />
    <hkern u1="v" u2="&#xe5;" k="33" />
    <hkern u1="v" u2="&#xe4;" k="33" />
    <hkern u1="v" u2="&#xe3;" k="33" />
    <hkern u1="v" u2="&#xe2;" k="33" />
    <hkern u1="v" u2="&#xe1;" k="33" />
    <hkern u1="v" u2="&#xe0;" k="33" />
    <hkern u1="v" u2="z" k="-33" />
    <hkern u1="v" u2="a" k="33" />
    <hkern u1="v" g2="f_j" k="-80" />
    <hkern u1="v" g2="f_f_l" k="-80" />
    <hkern u1="v" g2="f_f_i" k="-80" />
    <hkern u1="v" g2="fl" k="-80" />
    <hkern u1="v" g2="fi" k="-80" />
    <hkern u1="v" g2="f_f" k="-80" />
    <hkern u1="v" u2="&#x21b;" k="-66" />
    <hkern u1="v" u2="&#x153;" k="31" />
    <hkern u1="v" u2="&#x119;" k="31" />
    <hkern u1="v" u2="&#x105;" k="33" />
    <hkern u1="v" u2="&#xe6;" k="33" />
    <hkern u1="v" u2="y" k="-80" />
    <hkern u1="v" u2="x" k="-43" />
    <hkern u1="v" u2="w" k="-96" />
    <hkern u1="v" u2="v" k="-80" />
    <hkern u1="v" u2="u" k="-16" />
    <hkern u1="v" u2="t" k="-66" />
    <hkern u1="v" u2="q" k="49" />
    <hkern u1="v" u2="p" k="-16" />
    <hkern u1="v" u2="o" k="31" />
    <hkern u1="v" u2="i" k="-16" />
    <hkern u1="v" u2="g" k="47" />
    <hkern u1="v" u2="f" k="-80" />
    <hkern u1="v" u2="d" k="33" />
    <hkern u1="v" u2="c" k="31" />
    <hkern u1="w" u2="x" k="-43" />
    <hkern u1="w" u2="v" k="-66" />
    <hkern u1="w" u2="n" k="-20" />
    <hkern u1="w" u2="l" k="-33" />
    <hkern u1="w" u2="b" k="-33" />
    <hkern u1="x" u2="&#x17f;" k="-31" />
    <hkern u1="x" u2="&#x17e;" k="-33" />
    <hkern u1="x" u2="&#x17c;" k="-33" />
    <hkern u1="x" u2="&#x17a;" k="-33" />
    <hkern u1="x" u2="&#x103;" k="63" />
    <hkern u1="x" u2="&#x101;" k="63" />
    <hkern u1="x" u2="&#xe5;" k="63" />
    <hkern u1="x" u2="&#xe4;" k="63" />
    <hkern u1="x" u2="&#xe3;" k="63" />
    <hkern u1="x" u2="&#xe2;" k="63" />
    <hkern u1="x" u2="&#xe1;" k="63" />
    <hkern u1="x" u2="&#xe0;" k="63" />
    <hkern u1="x" u2="z" k="-33" />
    <hkern u1="x" u2="a" k="63" />
    <hkern u1="x" g2="f_j" k="-31" />
    <hkern u1="x" g2="f_f_l" k="-31" />
    <hkern u1="x" g2="f_f_i" k="-31" />
    <hkern u1="x" g2="fl" k="-31" />
    <hkern u1="x" g2="fi" k="-31" />
    <hkern u1="x" g2="f_f" k="-31" />
    <hkern u1="x" u2="&#x21b;" k="-31" />
    <hkern u1="x" u2="&#x219;" k="16" />
    <hkern u1="x" u2="&#x153;" k="47" />
    <hkern u1="x" u2="&#x119;" k="47" />
    <hkern u1="x" u2="&#x105;" k="63" />
    <hkern u1="x" u2="&#xe6;" k="63" />
    <hkern u1="x" u2="y" k="-66" />
    <hkern u1="x" u2="x" k="-63" />
    <hkern u1="x" u2="w" k="-47" />
    <hkern u1="x" u2="v" k="-49" />
    <hkern u1="x" u2="t" k="-31" />
    <hkern u1="x" u2="s" k="16" />
    <hkern u1="x" u2="q" k="63" />
    <hkern u1="x" u2="o" k="66" />
    <hkern u1="x" u2="g" k="31" />
    <hkern u1="x" u2="f" k="-31" />
    <hkern u1="x" u2="e" k="63" />
    <hkern u1="x" u2="d" k="63" />
    <hkern u1="x" u2="c" k="47" />
    <hkern u1="y" u2="x" k="-63" />
    <hkern u1="y" u2="v" k="-63" />
    <hkern u1="y" u2="o" k="66" />
    <hkern u1="y" u2="l" k="-16" />
    <hkern u1="z" u2="x" k="-16" />
    <hkern u1="z" u2="v" k="-33" />
    <hkern u1="z" u2="o" k="33" />
    <hkern u1="z" u2="e" k="33" />
    <hkern u1="&#x7b;" u2="&#x149;" k="-47" />
    <hkern u1="&#x7c;" u2="j" k="-43" />
    <hkern u1="&#xc0;" u2="x" k="96" />
    <hkern u1="&#xc0;" u2="v" k="160" />
    <hkern u1="&#xc0;" u2="r" k="14" />
    <hkern u1="&#xc0;" u2="p" k="33" />
    <hkern u1="&#xc0;" u2="l" k="16" />
    <hkern u1="&#xc0;" u2="k" k="33" />
    <hkern u1="&#xc0;" u2="j" k="33" />
    <hkern u1="&#xc0;" u2="i" k="49" />
    <hkern u1="&#xc0;" u2="h" k="33" />
    <hkern u1="&#xc0;" u2="e" k="113" />
    <hkern u1="&#xc0;" u2="b" k="16" />
    <hkern u1="&#xc0;" u2="X" k="-33" />
    <hkern u1="&#xc0;" u2="V" k="193" />
    <hkern u1="&#xc0;" u2="R" k="-16" />
    <hkern u1="&#xc0;" u2="Q" k="80" />
    <hkern u1="&#xc0;" u2="P" k="-47" />
    <hkern u1="&#xc0;" u2="O" k="63" />
    <hkern u1="&#xc0;" u2="M" k="-33" />
    <hkern u1="&#xc0;" u2="L" k="-31" />
    <hkern u1="&#xc0;" u2="K" k="-16" />
    <hkern u1="&#xc0;" u2="I" k="-31" />
    <hkern u1="&#xc0;" u2="H" k="-33" />
    <hkern u1="&#xc0;" u2="G" k="80" />
    <hkern u1="&#xc0;" u2="E" k="-31" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="209" />
    <hkern u1="&#xc0;" u2="&#x2f;" k="-63" />
    <hkern u1="&#xc0;" u2="&#x26;" k="49" />
    <hkern u1="&#xc1;" u2="x" k="96" />
    <hkern u1="&#xc1;" u2="v" k="160" />
    <hkern u1="&#xc1;" u2="r" k="14" />
    <hkern u1="&#xc1;" u2="p" k="33" />
    <hkern u1="&#xc1;" u2="l" k="16" />
    <hkern u1="&#xc1;" u2="k" k="33" />
    <hkern u1="&#xc1;" u2="j" k="33" />
    <hkern u1="&#xc1;" u2="i" k="49" />
    <hkern u1="&#xc1;" u2="h" k="33" />
    <hkern u1="&#xc1;" u2="e" k="113" />
    <hkern u1="&#xc1;" u2="b" k="16" />
    <hkern u1="&#xc1;" u2="X" k="-33" />
    <hkern u1="&#xc1;" u2="V" k="193" />
    <hkern u1="&#xc1;" u2="R" k="-16" />
    <hkern u1="&#xc1;" u2="Q" k="80" />
    <hkern u1="&#xc1;" u2="P" k="-47" />
    <hkern u1="&#xc1;" u2="O" k="63" />
    <hkern u1="&#xc1;" u2="M" k="-33" />
    <hkern u1="&#xc1;" u2="L" k="-31" />
    <hkern u1="&#xc1;" u2="K" k="-16" />
    <hkern u1="&#xc1;" u2="I" k="-31" />
    <hkern u1="&#xc1;" u2="H" k="-33" />
    <hkern u1="&#xc1;" u2="G" k="80" />
    <hkern u1="&#xc1;" u2="E" k="-31" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="209" />
    <hkern u1="&#xc1;" u2="&#x2f;" k="-63" />
    <hkern u1="&#xc1;" u2="&#x26;" k="49" />
    <hkern u1="&#xc2;" u2="x" k="96" />
    <hkern u1="&#xc2;" u2="v" k="160" />
    <hkern u1="&#xc2;" u2="r" k="14" />
    <hkern u1="&#xc2;" u2="p" k="33" />
    <hkern u1="&#xc2;" u2="l" k="16" />
    <hkern u1="&#xc2;" u2="k" k="33" />
    <hkern u1="&#xc2;" u2="j" k="33" />
    <hkern u1="&#xc2;" u2="i" k="49" />
    <hkern u1="&#xc2;" u2="h" k="33" />
    <hkern u1="&#xc2;" u2="e" k="113" />
    <hkern u1="&#xc2;" u2="b" k="16" />
    <hkern u1="&#xc2;" u2="X" k="-33" />
    <hkern u1="&#xc2;" u2="V" k="193" />
    <hkern u1="&#xc2;" u2="R" k="-16" />
    <hkern u1="&#xc2;" u2="Q" k="80" />
    <hkern u1="&#xc2;" u2="P" k="-47" />
    <hkern u1="&#xc2;" u2="O" k="63" />
    <hkern u1="&#xc2;" u2="M" k="-33" />
    <hkern u1="&#xc2;" u2="L" k="-31" />
    <hkern u1="&#xc2;" u2="K" k="-16" />
    <hkern u1="&#xc2;" u2="I" k="-31" />
    <hkern u1="&#xc2;" u2="H" k="-33" />
    <hkern u1="&#xc2;" u2="G" k="80" />
    <hkern u1="&#xc2;" u2="E" k="-31" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="209" />
    <hkern u1="&#xc2;" u2="&#x2f;" k="-63" />
    <hkern u1="&#xc2;" u2="&#x26;" k="49" />
    <hkern u1="&#xc3;" u2="x" k="96" />
    <hkern u1="&#xc3;" u2="v" k="160" />
    <hkern u1="&#xc3;" u2="r" k="14" />
    <hkern u1="&#xc3;" u2="p" k="33" />
    <hkern u1="&#xc3;" u2="l" k="16" />
    <hkern u1="&#xc3;" u2="k" k="33" />
    <hkern u1="&#xc3;" u2="j" k="33" />
    <hkern u1="&#xc3;" u2="i" k="49" />
    <hkern u1="&#xc3;" u2="h" k="33" />
    <hkern u1="&#xc3;" u2="e" k="113" />
    <hkern u1="&#xc3;" u2="b" k="16" />
    <hkern u1="&#xc3;" u2="X" k="-33" />
    <hkern u1="&#xc3;" u2="V" k="193" />
    <hkern u1="&#xc3;" u2="R" k="-16" />
    <hkern u1="&#xc3;" u2="Q" k="80" />
    <hkern u1="&#xc3;" u2="P" k="-47" />
    <hkern u1="&#xc3;" u2="O" k="63" />
    <hkern u1="&#xc3;" u2="M" k="-33" />
    <hkern u1="&#xc3;" u2="L" k="-31" />
    <hkern u1="&#xc3;" u2="K" k="-16" />
    <hkern u1="&#xc3;" u2="I" k="-31" />
    <hkern u1="&#xc3;" u2="H" k="-33" />
    <hkern u1="&#xc3;" u2="G" k="80" />
    <hkern u1="&#xc3;" u2="E" k="-31" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="209" />
    <hkern u1="&#xc3;" u2="&#x2f;" k="-63" />
    <hkern u1="&#xc3;" u2="&#x26;" k="49" />
    <hkern u1="&#xc4;" u2="x" k="96" />
    <hkern u1="&#xc4;" u2="v" k="160" />
    <hkern u1="&#xc4;" u2="r" k="14" />
    <hkern u1="&#xc4;" u2="p" k="33" />
    <hkern u1="&#xc4;" u2="l" k="16" />
    <hkern u1="&#xc4;" u2="k" k="33" />
    <hkern u1="&#xc4;" u2="j" k="33" />
    <hkern u1="&#xc4;" u2="i" k="49" />
    <hkern u1="&#xc4;" u2="h" k="33" />
    <hkern u1="&#xc4;" u2="e" k="113" />
    <hkern u1="&#xc4;" u2="b" k="16" />
    <hkern u1="&#xc4;" u2="X" k="-33" />
    <hkern u1="&#xc4;" u2="V" k="193" />
    <hkern u1="&#xc4;" u2="R" k="-16" />
    <hkern u1="&#xc4;" u2="Q" k="80" />
    <hkern u1="&#xc4;" u2="P" k="-47" />
    <hkern u1="&#xc4;" u2="O" k="63" />
    <hkern u1="&#xc4;" u2="M" k="-33" />
    <hkern u1="&#xc4;" u2="L" k="-31" />
    <hkern u1="&#xc4;" u2="K" k="-16" />
    <hkern u1="&#xc4;" u2="I" k="-31" />
    <hkern u1="&#xc4;" u2="H" k="-33" />
    <hkern u1="&#xc4;" u2="G" k="80" />
    <hkern u1="&#xc4;" u2="E" k="-31" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="209" />
    <hkern u1="&#xc4;" u2="&#x2f;" k="-63" />
    <hkern u1="&#xc4;" u2="&#x26;" k="49" />
    <hkern u1="&#xc5;" u2="x" k="96" />
    <hkern u1="&#xc5;" u2="v" k="160" />
    <hkern u1="&#xc5;" u2="r" k="14" />
    <hkern u1="&#xc5;" u2="p" k="33" />
    <hkern u1="&#xc5;" u2="l" k="16" />
    <hkern u1="&#xc5;" u2="k" k="33" />
    <hkern u1="&#xc5;" u2="j" k="33" />
    <hkern u1="&#xc5;" u2="i" k="49" />
    <hkern u1="&#xc5;" u2="h" k="33" />
    <hkern u1="&#xc5;" u2="e" k="113" />
    <hkern u1="&#xc5;" u2="b" k="16" />
    <hkern u1="&#xc5;" u2="X" k="-33" />
    <hkern u1="&#xc5;" u2="V" k="193" />
    <hkern u1="&#xc5;" u2="R" k="-16" />
    <hkern u1="&#xc5;" u2="Q" k="80" />
    <hkern u1="&#xc5;" u2="P" k="-47" />
    <hkern u1="&#xc5;" u2="O" k="63" />
    <hkern u1="&#xc5;" u2="M" k="-33" />
    <hkern u1="&#xc5;" u2="L" k="-31" />
    <hkern u1="&#xc5;" u2="K" k="-16" />
    <hkern u1="&#xc5;" u2="I" k="-31" />
    <hkern u1="&#xc5;" u2="H" k="-33" />
    <hkern u1="&#xc5;" u2="G" k="80" />
    <hkern u1="&#xc5;" u2="E" k="-31" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="209" />
    <hkern u1="&#xc5;" u2="&#x2f;" k="-63" />
    <hkern u1="&#xc5;" u2="&#x26;" k="49" />
    <hkern u1="&#xc6;" u2="p" k="14" />
    <hkern u1="&#xc6;" u2="l" k="33" />
    <hkern u1="&#xc6;" u2="j" k="31" />
    <hkern u1="&#xc6;" u2="i" k="33" />
    <hkern u1="&#xc6;" u2="h" k="31" />
    <hkern u1="&#xc6;" u2="e" k="33" />
    <hkern u1="&#xc6;" u2="X" k="-33" />
    <hkern u1="&#xc6;" u2="N" k="-33" />
    <hkern u1="&#xc6;" u2="L" k="-33" />
    <hkern u1="&#xc6;" u2="D" k="-16" />
    <hkern u1="&#xd0;" u2="x" k="66" />
    <hkern u1="&#xd0;" u2="v" k="80" />
    <hkern u1="&#xd0;" u2="n" k="33" />
    <hkern u1="&#xd0;" u2="l" k="33" />
    <hkern u1="&#xd0;" u2="k" k="33" />
    <hkern u1="&#xd0;" u2="j" k="31" />
    <hkern u1="&#xd0;" u2="i" k="49" />
    <hkern u1="&#xd0;" u2="h" k="33" />
    <hkern u1="&#xd0;" u2="X" k="80" />
    <hkern u1="&#xd0;" u2="V" k="80" />
    <hkern u1="&#xd0;" u2="P" k="-33" />
    <hkern u1="&#xd0;" u2="L" k="-16" />
    <hkern u1="&#xd0;" u2="I" k="33" />
    <hkern u1="&#xd0;" u2="H" k="-49" />
    <hkern u1="&#xd0;" u2="D" k="-33" />
    <hkern u1="&#xd0;" u2="&#x3f;" k="31" />
    <hkern u1="&#xd0;" u2="&#x2c;" k="129" />
    <hkern u1="&#xd0;" u2="&#x29;" k="96" />
    <hkern u1="&#xe0;" u2="v" k="-16" />
    <hkern u1="&#xe0;" u2="j" k="-43" />
    <hkern u1="&#xe1;" u2="v" k="-16" />
    <hkern u1="&#xe1;" u2="j" k="-43" />
    <hkern u1="&#xe2;" u2="v" k="-16" />
    <hkern u1="&#xe2;" u2="j" k="-43" />
    <hkern u1="&#xe3;" u2="v" k="-16" />
    <hkern u1="&#xe3;" u2="j" k="-43" />
    <hkern u1="&#xe4;" u2="v" k="-16" />
    <hkern u1="&#xe4;" u2="j" k="-43" />
    <hkern u1="&#xe5;" u2="v" k="-16" />
    <hkern u1="&#xe5;" u2="j" k="-43" />
    <hkern u1="&#xe6;" u2="&#x17f;" k="20" />
    <hkern u1="&#xe6;" u2="&#x17e;" k="47" />
    <hkern u1="&#xe6;" u2="&#x17c;" k="47" />
    <hkern u1="&#xe6;" u2="&#x17a;" k="47" />
    <hkern u1="&#xe6;" u2="z" k="47" />
    <hkern u1="&#xe6;" g2="f_j" k="20" />
    <hkern u1="&#xe6;" g2="f_f_l" k="20" />
    <hkern u1="&#xe6;" g2="f_f_i" k="20" />
    <hkern u1="&#xe6;" g2="fl" k="20" />
    <hkern u1="&#xe6;" g2="fi" k="20" />
    <hkern u1="&#xe6;" g2="f_f" k="20" />
    <hkern u1="&#xe6;" u2="&#x21b;" k="47" />
    <hkern u1="&#xe6;" u2="&#x219;" k="33" />
    <hkern u1="&#xe6;" u2="&#x14b;" k="16" />
    <hkern u1="&#xe6;" u2="&#x149;" k="16" />
    <hkern u1="&#xe6;" u2="y" k="63" />
    <hkern u1="&#xe6;" u2="x" k="80" />
    <hkern u1="&#xe6;" u2="w" k="41" />
    <hkern u1="&#xe6;" u2="v" k="63" />
    <hkern u1="&#xe6;" u2="u" k="20" />
    <hkern u1="&#xe6;" u2="t" k="47" />
    <hkern u1="&#xe6;" u2="s" k="33" />
    <hkern u1="&#xe6;" u2="r" k="16" />
    <hkern u1="&#xe6;" u2="q" k="20" />
    <hkern u1="&#xe6;" u2="p" k="20" />
    <hkern u1="&#xe6;" u2="o" k="16" />
    <hkern u1="&#xe6;" u2="n" k="31" />
    <hkern u1="&#xe6;" u2="m" k="16" />
    <hkern u1="&#xe6;" u2="k" k="43" />
    <hkern u1="&#xe6;" u2="j" k="47" />
    <hkern u1="&#xe6;" u2="i" k="33" />
    <hkern u1="&#xe6;" u2="f" k="20" />
    <hkern u1="&#xe6;" u2="d" k="16" />
    <hkern u1="&#xe6;" u2="b" k="16" />
    <hkern u1="&#xfe;" u2="y" k="23" />
    <hkern u1="&#xfe;" u2="x" k="49" />
    <hkern u1="&#xfe;" u2="v" k="14" />
    <hkern u1="&#xfe;" u2="N" k="-49" />
    <hkern u1="&#xfe;" u2="L" k="-80" />
    <hkern u1="&#x100;" u2="x" k="96" />
    <hkern u1="&#x100;" u2="v" k="160" />
    <hkern u1="&#x100;" u2="r" k="14" />
    <hkern u1="&#x100;" u2="p" k="33" />
    <hkern u1="&#x100;" u2="l" k="16" />
    <hkern u1="&#x100;" u2="k" k="33" />
    <hkern u1="&#x100;" u2="j" k="33" />
    <hkern u1="&#x100;" u2="i" k="49" />
    <hkern u1="&#x100;" u2="h" k="33" />
    <hkern u1="&#x100;" u2="e" k="113" />
    <hkern u1="&#x100;" u2="b" k="16" />
    <hkern u1="&#x100;" u2="X" k="-33" />
    <hkern u1="&#x100;" u2="V" k="193" />
    <hkern u1="&#x100;" u2="R" k="-16" />
    <hkern u1="&#x100;" u2="Q" k="80" />
    <hkern u1="&#x100;" u2="P" k="-47" />
    <hkern u1="&#x100;" u2="O" k="63" />
    <hkern u1="&#x100;" u2="M" k="-33" />
    <hkern u1="&#x100;" u2="L" k="-31" />
    <hkern u1="&#x100;" u2="K" k="-16" />
    <hkern u1="&#x100;" u2="I" k="-31" />
    <hkern u1="&#x100;" u2="H" k="-33" />
    <hkern u1="&#x100;" u2="G" k="80" />
    <hkern u1="&#x100;" u2="E" k="-31" />
    <hkern u1="&#x100;" u2="&#x3f;" k="209" />
    <hkern u1="&#x100;" u2="&#x2f;" k="-63" />
    <hkern u1="&#x100;" u2="&#x26;" k="49" />
    <hkern u1="&#x101;" u2="v" k="-16" />
    <hkern u1="&#x101;" u2="j" k="-43" />
    <hkern u1="&#x102;" u2="x" k="96" />
    <hkern u1="&#x102;" u2="v" k="160" />
    <hkern u1="&#x102;" u2="r" k="14" />
    <hkern u1="&#x102;" u2="p" k="33" />
    <hkern u1="&#x102;" u2="l" k="16" />
    <hkern u1="&#x102;" u2="k" k="33" />
    <hkern u1="&#x102;" u2="j" k="33" />
    <hkern u1="&#x102;" u2="i" k="49" />
    <hkern u1="&#x102;" u2="h" k="33" />
    <hkern u1="&#x102;" u2="e" k="113" />
    <hkern u1="&#x102;" u2="b" k="16" />
    <hkern u1="&#x102;" u2="X" k="-33" />
    <hkern u1="&#x102;" u2="V" k="193" />
    <hkern u1="&#x102;" u2="R" k="-16" />
    <hkern u1="&#x102;" u2="Q" k="80" />
    <hkern u1="&#x102;" u2="P" k="-47" />
    <hkern u1="&#x102;" u2="O" k="63" />
    <hkern u1="&#x102;" u2="M" k="-33" />
    <hkern u1="&#x102;" u2="L" k="-31" />
    <hkern u1="&#x102;" u2="K" k="-16" />
    <hkern u1="&#x102;" u2="I" k="-31" />
    <hkern u1="&#x102;" u2="H" k="-33" />
    <hkern u1="&#x102;" u2="G" k="80" />
    <hkern u1="&#x102;" u2="E" k="-31" />
    <hkern u1="&#x102;" u2="&#x3f;" k="209" />
    <hkern u1="&#x102;" u2="&#x2f;" k="-63" />
    <hkern u1="&#x102;" u2="&#x26;" k="49" />
    <hkern u1="&#x103;" u2="v" k="-16" />
    <hkern u1="&#x103;" u2="j" k="-43" />
    <hkern u1="&#x104;" u2="x" k="96" />
    <hkern u1="&#x104;" u2="v" k="160" />
    <hkern u1="&#x104;" u2="r" k="14" />
    <hkern u1="&#x104;" u2="p" k="33" />
    <hkern u1="&#x104;" u2="l" k="16" />
    <hkern u1="&#x104;" u2="k" k="33" />
    <hkern u1="&#x104;" u2="j" k="33" />
    <hkern u1="&#x104;" u2="i" k="49" />
    <hkern u1="&#x104;" u2="h" k="33" />
    <hkern u1="&#x104;" u2="e" k="113" />
    <hkern u1="&#x104;" u2="b" k="16" />
    <hkern u1="&#x104;" u2="X" k="-33" />
    <hkern u1="&#x104;" u2="V" k="193" />
    <hkern u1="&#x104;" u2="R" k="-16" />
    <hkern u1="&#x104;" u2="Q" k="80" />
    <hkern u1="&#x104;" u2="P" k="-47" />
    <hkern u1="&#x104;" u2="O" k="63" />
    <hkern u1="&#x104;" u2="M" k="-33" />
    <hkern u1="&#x104;" u2="L" k="-31" />
    <hkern u1="&#x104;" u2="K" k="-16" />
    <hkern u1="&#x104;" u2="I" k="-31" />
    <hkern u1="&#x104;" u2="H" k="-33" />
    <hkern u1="&#x104;" u2="G" k="80" />
    <hkern u1="&#x104;" u2="E" k="-31" />
    <hkern u1="&#x104;" u2="&#x3f;" k="209" />
    <hkern u1="&#x104;" u2="&#x2f;" k="-63" />
    <hkern u1="&#x104;" u2="&#x26;" k="49" />
    <hkern u1="&#x105;" u2="v" k="-16" />
    <hkern u1="&#x105;" u2="j" k="-43" />
    <hkern u1="&#x110;" u2="x" k="66" />
    <hkern u1="&#x110;" u2="v" k="80" />
    <hkern u1="&#x110;" u2="n" k="33" />
    <hkern u1="&#x110;" u2="l" k="33" />
    <hkern u1="&#x110;" u2="k" k="33" />
    <hkern u1="&#x110;" u2="j" k="31" />
    <hkern u1="&#x110;" u2="i" k="49" />
    <hkern u1="&#x110;" u2="h" k="33" />
    <hkern u1="&#x110;" u2="X" k="80" />
    <hkern u1="&#x110;" u2="V" k="80" />
    <hkern u1="&#x110;" u2="P" k="-33" />
    <hkern u1="&#x110;" u2="L" k="-16" />
    <hkern u1="&#x110;" u2="I" k="33" />
    <hkern u1="&#x110;" u2="H" k="-49" />
    <hkern u1="&#x110;" u2="D" k="-33" />
    <hkern u1="&#x110;" u2="&#x3f;" k="31" />
    <hkern u1="&#x110;" u2="&#x2c;" k="129" />
    <hkern u1="&#x110;" u2="&#x29;" k="96" />
    <hkern u1="&#x118;" u2="p" k="14" />
    <hkern u1="&#x118;" u2="l" k="33" />
    <hkern u1="&#x118;" u2="j" k="31" />
    <hkern u1="&#x118;" u2="i" k="33" />
    <hkern u1="&#x118;" u2="h" k="31" />
    <hkern u1="&#x118;" u2="e" k="33" />
    <hkern u1="&#x118;" u2="X" k="-33" />
    <hkern u1="&#x118;" u2="N" k="-33" />
    <hkern u1="&#x118;" u2="L" k="-33" />
    <hkern u1="&#x118;" u2="D" k="-16" />
    <hkern u1="&#x132;" u2="r" k="20" />
    <hkern u1="&#x132;" u2="n" k="20" />
    <hkern u1="&#x132;" u2="k" k="16" />
    <hkern u1="&#x132;" u2="j" k="14" />
    <hkern u1="&#x132;" u2="i" k="49" />
    <hkern u1="&#x132;" u2="h" k="16" />
    <hkern u1="&#x132;" u2="R" k="-16" />
    <hkern u1="&#x132;" u2="O" k="-33" />
    <hkern u1="&#x132;" u2="L" k="-16" />
    <hkern u1="&#x132;" u2="K" k="-33" />
    <hkern u1="&#x132;" u2="&#x2c;" k="113" />
    <hkern u1="&#x134;" u2="r" k="20" />
    <hkern u1="&#x134;" u2="n" k="20" />
    <hkern u1="&#x134;" u2="k" k="16" />
    <hkern u1="&#x134;" u2="j" k="14" />
    <hkern u1="&#x134;" u2="i" k="49" />
    <hkern u1="&#x134;" u2="h" k="16" />
    <hkern u1="&#x134;" u2="R" k="-16" />
    <hkern u1="&#x134;" u2="O" k="-33" />
    <hkern u1="&#x134;" u2="L" k="-16" />
    <hkern u1="&#x134;" u2="K" k="-33" />
    <hkern u1="&#x134;" u2="&#x2c;" k="113" />
    <hkern u1="&#x141;" u2="v" k="150" />
    <hkern u1="&#x141;" u2="r" k="16" />
    <hkern u1="&#x141;" u2="q" k="49" />
    <hkern u1="&#x141;" u2="p" k="16" />
    <hkern u1="&#x141;" u2="o" k="33" />
    <hkern u1="&#x141;" u2="k" k="16" />
    <hkern u1="&#x141;" u2="i" k="49" />
    <hkern u1="&#x141;" u2="h" k="33" />
    <hkern u1="&#x141;" u2="e" k="33" />
    <hkern u1="&#x141;" u2="b" k="16" />
    <hkern u1="&#x141;" u2="X" k="-33" />
    <hkern u1="&#x141;" u2="V" k="209" />
    <hkern u1="&#x141;" u2="R" k="-33" />
    <hkern u1="&#x141;" u2="Q" k="33" />
    <hkern u1="&#x141;" u2="P" k="-33" />
    <hkern u1="&#x141;" u2="O" k="31" />
    <hkern u1="&#x141;" u2="N" k="-33" />
    <hkern u1="&#x141;" u2="L" k="-33" />
    <hkern u1="&#x141;" u2="K" k="-31" />
    <hkern u1="&#x141;" u2="G" k="49" />
    <hkern u1="&#x141;" u2="E" k="-33" />
    <hkern u1="&#x141;" u2="D" k="-33" />
    <hkern u1="&#x141;" u2="&#x3f;" k="129" />
    <hkern u1="&#x141;" u2="&#x2f;" k="-63" />
    <hkern u1="&#x149;" u2="v" k="49" />
    <hkern u1="&#x149;" u2="R" k="-66" />
    <hkern u1="&#x149;" u2="I" k="-31" />
    <hkern u1="&#x14a;" u2="r" k="33" />
    <hkern u1="&#x14a;" u2="n" k="43" />
    <hkern u1="&#x14a;" u2="l" k="14" />
    <hkern u1="&#x14a;" u2="j" k="33" />
    <hkern u1="&#x14a;" u2="i" k="33" />
    <hkern u1="&#x14a;" u2="h" k="16" />
    <hkern u1="&#x14a;" u2="R" k="-31" />
    <hkern u1="&#x14a;" u2="P" k="-47" />
    <hkern u1="&#x14a;" u2="K" k="-31" />
    <hkern u1="&#x14a;" u2="F" k="-16" />
    <hkern u1="&#x14a;" u2="&#x2f;" k="-63" />
    <hkern u1="&#x14b;" u2="v" k="49" />
    <hkern u1="&#x14b;" u2="R" k="-66" />
    <hkern u1="&#x14b;" u2="I" k="-31" />
    <hkern u1="&#x152;" u2="p" k="14" />
    <hkern u1="&#x152;" u2="l" k="33" />
    <hkern u1="&#x152;" u2="j" k="31" />
    <hkern u1="&#x152;" u2="i" k="33" />
    <hkern u1="&#x152;" u2="h" k="31" />
    <hkern u1="&#x152;" u2="e" k="33" />
    <hkern u1="&#x152;" u2="X" k="-33" />
    <hkern u1="&#x152;" u2="N" k="-33" />
    <hkern u1="&#x152;" u2="L" k="-33" />
    <hkern u1="&#x152;" u2="D" k="-16" />
    <hkern u1="&#x153;" u2="&#x17f;" k="20" />
    <hkern u1="&#x153;" u2="&#x17e;" k="47" />
    <hkern u1="&#x153;" u2="&#x17c;" k="47" />
    <hkern u1="&#x153;" u2="&#x17a;" k="47" />
    <hkern u1="&#x153;" u2="z" k="47" />
    <hkern u1="&#x153;" g2="f_j" k="20" />
    <hkern u1="&#x153;" g2="f_f_l" k="20" />
    <hkern u1="&#x153;" g2="f_f_i" k="20" />
    <hkern u1="&#x153;" g2="fl" k="20" />
    <hkern u1="&#x153;" g2="fi" k="20" />
    <hkern u1="&#x153;" g2="f_f" k="20" />
    <hkern u1="&#x153;" u2="&#x21b;" k="47" />
    <hkern u1="&#x153;" u2="&#x219;" k="33" />
    <hkern u1="&#x153;" u2="&#x14b;" k="16" />
    <hkern u1="&#x153;" u2="&#x149;" k="16" />
    <hkern u1="&#x153;" u2="y" k="63" />
    <hkern u1="&#x153;" u2="x" k="80" />
    <hkern u1="&#x153;" u2="w" k="41" />
    <hkern u1="&#x153;" u2="v" k="63" />
    <hkern u1="&#x153;" u2="u" k="20" />
    <hkern u1="&#x153;" u2="t" k="47" />
    <hkern u1="&#x153;" u2="s" k="33" />
    <hkern u1="&#x153;" u2="r" k="16" />
    <hkern u1="&#x153;" u2="q" k="20" />
    <hkern u1="&#x153;" u2="p" k="20" />
    <hkern u1="&#x153;" u2="o" k="16" />
    <hkern u1="&#x153;" u2="n" k="31" />
    <hkern u1="&#x153;" u2="m" k="16" />
    <hkern u1="&#x153;" u2="k" k="43" />
    <hkern u1="&#x153;" u2="j" k="47" />
    <hkern u1="&#x153;" u2="i" k="33" />
    <hkern u1="&#x153;" u2="f" k="20" />
    <hkern u1="&#x153;" u2="d" k="16" />
    <hkern u1="&#x153;" u2="b" k="16" />
    <hkern u1="&#x178;" u2="x" k="127" />
    <hkern u1="&#x178;" u2="v" k="106" />
    <hkern u1="&#x178;" u2="r" k="172" />
    <hkern u1="&#x178;" u2="q" k="256" />
    <hkern u1="&#x178;" u2="p" k="160" />
    <hkern u1="&#x178;" u2="o" k="272" />
    <hkern u1="&#x178;" u2="n" k="172" />
    <hkern u1="&#x178;" u2="l" k="33" />
    <hkern u1="&#x178;" u2="k" k="49" />
    <hkern u1="&#x178;" u2="j" k="49" />
    <hkern u1="&#x178;" u2="i" k="63" />
    <hkern u1="&#x178;" u2="h" k="31" />
    <hkern u1="&#x178;" u2="e" k="303" />
    <hkern u1="&#x178;" u2="X" k="-31" />
    <hkern u1="&#x178;" u2="V" k="-47" />
    <hkern u1="&#x178;" u2="Q" k="96" />
    <hkern u1="&#x178;" u2="M" k="-33" />
    <hkern u1="&#x178;" u2="G" k="127" />
    <hkern u1="&#x178;" u2="F" k="-31" />
    <hkern u1="&#x178;" u2="E" k="-16" />
    <hkern u1="&#x178;" u2="D" k="-33" />
    <hkern u1="&#x178;" u2="&#x3f;" k="47" />
    <hkern u1="&#x178;" u2="&#x3b;" k="176" />
    <hkern u1="&#x178;" u2="&#x3a;" k="127" />
    <hkern u1="&#x178;" u2="&#x2f;" k="145" />
    <hkern u1="&#x178;" u2="&#x2c;" k="240" />
    <hkern u1="&#x178;" u2="&#x26;" k="143" />
    <hkern u1="&#x17a;" u2="x" k="-16" />
    <hkern u1="&#x17a;" u2="v" k="-33" />
    <hkern u1="&#x17a;" u2="o" k="33" />
    <hkern u1="&#x17a;" u2="e" k="33" />
    <hkern u1="&#x17c;" u2="x" k="-16" />
    <hkern u1="&#x17c;" u2="v" k="-33" />
    <hkern u1="&#x17c;" u2="o" k="33" />
    <hkern u1="&#x17c;" u2="e" k="33" />
    <hkern u1="&#x17e;" u2="x" k="-16" />
    <hkern u1="&#x17e;" u2="v" k="-33" />
    <hkern u1="&#x17e;" u2="o" k="33" />
    <hkern u1="&#x17e;" u2="e" k="33" />
    <hkern u1="&#x17f;" u2="&#x149;" k="-137" />
    <hkern u1="&#x218;" u2="&#x21b;" k="43" />
    <hkern u1="&#x218;" u2="&#x21a;" k="43" />
    <hkern u1="&#x218;" u2="&#x178;" k="63" />
    <hkern u1="&#x218;" u2="x" k="43" />
    <hkern u1="&#x218;" u2="v" k="-20" />
    <hkern u1="&#x218;" u2="n" k="14" />
    <hkern u1="&#x218;" u2="j" k="49" />
    <hkern u1="&#x218;" u2="i" k="33" />
    <hkern u1="&#x218;" u2="h" k="16" />
    <hkern u1="&#x218;" u2="Y" k="63" />
    <hkern u1="&#x218;" u2="W" k="20" />
    <hkern u1="&#x218;" u2="V" k="23" />
    <hkern u1="&#x218;" u2="T" k="43" />
    <hkern u1="&#x218;" u2="R" k="-47" />
    <hkern u1="&#x218;" u2="P" k="-47" />
    <hkern u1="&#x218;" u2="M" k="-47" />
    <hkern u1="&#x218;" u2="L" k="-47" />
    <hkern u1="&#x218;" u2="K" k="-47" />
    <hkern u1="&#x218;" u2="I" k="23" />
    <hkern u1="&#x218;" u2="E" k="-49" />
    <hkern u1="&#x218;" u2="&#x2c;" k="31" />
    <hkern u1="&#x218;" u2="&#x29;" k="63" />
    <hkern u1="&#x218;" u2="&#x26;" k="-49" />
    <hkern u1="&#x21a;" u2="&#x3b1;" k="150" />
    <hkern u1="&#x21a;" u2="&#x391;" k="150" />
    <hkern u1="&#x21a;" u2="&#x104;" k="150" />
    <hkern u1="&#x21a;" u2="&#x102;" k="150" />
    <hkern u1="&#x21a;" u2="&#x100;" k="150" />
    <hkern u1="&#x21a;" u2="&#xc5;" k="150" />
    <hkern u1="&#x21a;" u2="&#xc4;" k="150" />
    <hkern u1="&#x21a;" u2="&#xc3;" k="150" />
    <hkern u1="&#x21a;" u2="&#xc2;" k="150" />
    <hkern u1="&#x21a;" u2="&#xc1;" k="150" />
    <hkern u1="&#x21a;" u2="&#xc0;" k="150" />
    <hkern u1="&#x21a;" u2="&#x44d;" k="106" />
    <hkern u1="&#x21a;" u2="&#x42d;" k="106" />
    <hkern u1="&#x21a;" u2="&#x3bb;" k="150" />
    <hkern u1="&#x21a;" u2="&#x3b4;" k="150" />
    <hkern u1="&#x21a;" u2="&#x3ac;" k="150" />
    <hkern u1="&#x21a;" u2="&#x39b;" k="150" />
    <hkern u1="&#x21a;" u2="&#x394;" k="150" />
    <hkern u1="&#x21a;" u2="&#x219;" k="43" />
    <hkern u1="&#x21a;" u2="&#x218;" k="43" />
    <hkern u1="&#x21a;" u2="&#x178;" k="-20" />
    <hkern u1="&#x21a;" u2="&#x152;" k="106" />
    <hkern u1="&#x21a;" u2="&#xc6;" k="150" />
    <hkern u1="&#x21a;" u2="x" k="96" />
    <hkern u1="&#x21a;" u2="v" k="213" />
    <hkern u1="&#x21a;" u2="r" k="96" />
    <hkern u1="&#x21a;" u2="p" k="96" />
    <hkern u1="&#x21a;" u2="o" k="270" />
    <hkern u1="&#x21a;" u2="n" k="129" />
    <hkern u1="&#x21a;" u2="l" k="63" />
    <hkern u1="&#x21a;" u2="k" k="66" />
    <hkern u1="&#x21a;" u2="j" k="80" />
    <hkern u1="&#x21a;" u2="i" k="66" />
    <hkern u1="&#x21a;" u2="h" k="66" />
    <hkern u1="&#x21a;" u2="e" k="225" />
    <hkern u1="&#x21a;" u2="Y" k="-20" />
    <hkern u1="&#x21a;" u2="W" k="-43" />
    <hkern u1="&#x21a;" u2="V" k="-43" />
    <hkern u1="&#x21a;" u2="S" k="43" />
    <hkern u1="&#x21a;" u2="Q" k="106" />
    <hkern u1="&#x21a;" u2="P" k="-16" />
    <hkern u1="&#x21a;" u2="O" k="106" />
    <hkern u1="&#x21a;" u2="L" k="-16" />
    <hkern u1="&#x21a;" u2="G" k="49" />
    <hkern u1="&#x21a;" u2="C" k="106" />
    <hkern u1="&#x21a;" u2="A" k="150" />
    <hkern u1="&#x21a;" u2="&#x3b;" k="143" />
    <hkern u1="&#x21a;" u2="&#x3a;" k="127" />
    <hkern u1="&#x21a;" u2="&#x2f;" k="176" />
    <hkern u1="&#x21a;" u2="&#x2c;" k="225" />
    <hkern u1="&#x21a;" u2="&#x26;" k="113" />
    <hkern u1="&#x386;" u2="&#x3ce;" k="-47" />
    <hkern u1="&#x386;" u2="&#x3cd;" k="176" />
    <hkern u1="&#x386;" u2="&#x3cb;" k="176" />
    <hkern u1="&#x386;" u2="&#x3c9;" k="-47" />
    <hkern u1="&#x386;" u2="&#x3c5;" k="176" />
    <hkern u1="&#x386;" u2="&#x3b0;" k="176" />
    <hkern u1="&#x386;" u2="&#x3c4;" k="143" />
    <hkern u1="&#x386;" u2="&#x3a9;" k="-47" />
    <hkern u1="&#x386;" u2="&#x3a8;" k="63" />
    <hkern u1="&#x386;" u2="&#x3a7;" k="-31" />
    <hkern u1="&#x386;" u2="&#x3a6;" k="33" />
    <hkern u1="&#x386;" u2="&#x3a4;" k="143" />
    <hkern u1="&#x386;" u2="&#x39e;" k="-31" />
    <hkern u1="&#x386;" u2="&#x21b;" k="193" />
    <hkern u1="&#x386;" u2="&#x219;" k="20" />
    <hkern u1="&#x386;" u2="&#x14b;" k="-20" />
    <hkern u1="&#x386;" u2="&#x14a;" k="-20" />
    <hkern u1="&#x386;" u2="v" k="160" />
    <hkern u1="&#x386;" u2="q" k="16" />
    <hkern u1="&#x386;" u2="p" k="-47" />
    <hkern u1="&#x386;" u2="n" k="-33" />
    <hkern u1="&#x386;" u2="l" k="-47" />
    <hkern u1="&#x386;" u2="k" k="-33" />
    <hkern u1="&#x386;" u2="e" k="-31" />
    <hkern u1="&#x386;" u2="b" k="-16" />
    <hkern u1="&#x386;" u2="V" k="170" />
    <hkern u1="&#x386;" u2="P" k="-20" />
    <hkern u1="&#x386;" u2="N" k="-20" />
    <hkern u1="&#x386;" u2="F" k="-41" />
    <hkern u1="&#x38a;" u2="&#x3c7;" k="-43" />
    <hkern u1="&#x38a;" u2="&#x3c6;" k="66" />
    <hkern u1="&#x38a;" u2="&#x3c3;" k="-43" />
    <hkern u1="&#x38a;" u2="&#x3c2;" k="63" />
    <hkern u1="&#x38a;" u2="&#x3c1;" k="-20" />
    <hkern u1="&#x38a;" u2="&#x3bb;" k="-43" />
    <hkern u1="&#x38a;" u2="&#x3b8;" k="63" />
    <hkern u1="&#x38a;" u2="&#x3b6;" k="-43" />
    <hkern u1="&#x38a;" u2="&#x3b4;" k="-43" />
    <hkern u1="&#x38a;" u2="&#x3a9;" k="-23" />
    <hkern u1="&#x38a;" u2="&#x3a7;" k="-43" />
    <hkern u1="&#x38a;" u2="&#x3a6;" k="66" />
    <hkern u1="&#x38a;" u2="&#x3a3;" k="-43" />
    <hkern u1="&#x38a;" u2="&#x3a1;" k="-20" />
    <hkern u1="&#x38a;" u2="&#x396;" k="-43" />
    <hkern u1="&#x38c;" u2="&#x3c4;" k="94" />
    <hkern u1="&#x38c;" u2="&#x3bb;" k="43" />
    <hkern u1="&#x38c;" u2="&#x3b6;" k="43" />
    <hkern u1="&#x38c;" u2="&#x3b4;" k="43" />
    <hkern u1="&#x38c;" u2="&#x3a4;" k="94" />
    <hkern u1="&#x38c;" u2="&#x396;" k="43" />
    <hkern u1="&#x38e;" u2="&#x3ce;" k="96" />
    <hkern u1="&#x38e;" u2="&#x3cc;" k="129" />
    <hkern u1="&#x38e;" u2="&#x3c9;" k="96" />
    <hkern u1="&#x38e;" u2="&#x3bf;" k="129" />
    <hkern u1="&#x38e;" u2="&#x3c8;" k="-33" />
    <hkern u1="&#x38e;" u2="&#x3c7;" k="-33" />
    <hkern u1="&#x38e;" u2="&#x3c6;" k="113" />
    <hkern u1="&#x38e;" u2="&#x3c4;" k="-14" />
    <hkern u1="&#x38e;" u2="&#x3c2;" k="86" />
    <hkern u1="&#x38e;" u2="&#x3bb;" k="240" />
    <hkern u1="&#x38e;" u2="&#x3b8;" k="106" />
    <hkern u1="&#x38e;" u2="&#x3b6;" k="23" />
    <hkern u1="&#x38e;" u2="&#x3b4;" k="223" />
    <hkern u1="&#x38e;" u2="&#x3a9;" k="80" />
    <hkern u1="&#x38e;" u2="&#x3a7;" k="-33" />
    <hkern u1="&#x38e;" u2="&#x3a6;" k="113" />
    <hkern u1="&#x38e;" u2="&#x3a4;" k="-14" />
    <hkern u1="&#x38e;" u2="&#x39f;" k="113" />
    <hkern u1="&#x38e;" u2="&#x397;" k="-33" />
    <hkern u1="&#x38e;" u2="&#x396;" k="23" />
    <hkern u1="&#x38e;" u2="&#x392;" k="-33" />
    <hkern u1="&#x38f;" u2="&#x3cd;" k="86" />
    <hkern u1="&#x38f;" u2="&#x3cb;" k="86" />
    <hkern u1="&#x38f;" u2="&#x3c5;" k="86" />
    <hkern u1="&#x38f;" u2="&#x3b0;" k="86" />
    <hkern u1="&#x38f;" u2="&#x3c7;" k="-43" />
    <hkern u1="&#x38f;" u2="&#x3c4;" k="80" />
    <hkern u1="&#x38f;" u2="&#x3bb;" k="-41" />
    <hkern u1="&#x38f;" u2="&#x3b6;" k="-43" />
    <hkern u1="&#x38f;" u2="&#x3b4;" k="-41" />
    <hkern u1="&#x38f;" u2="&#x3b3;" k="-23" />
    <hkern u1="&#x38f;" u2="&#x3a7;" k="-43" />
    <hkern u1="&#x38f;" u2="&#x3a4;" k="80" />
    <hkern u1="&#x38f;" u2="&#x396;" k="-43" />
    <hkern u1="&#x38f;" u2="&#x393;" k="-23" />
    <hkern u1="&#x390;" u2="&#x3cc;" k="63" />
    <hkern u1="&#x390;" u2="&#x3bf;" k="63" />
    <hkern u1="&#x390;" u2="&#x3c7;" k="-43" />
    <hkern u1="&#x390;" u2="&#x3c6;" k="66" />
    <hkern u1="&#x390;" u2="&#x3c3;" k="-43" />
    <hkern u1="&#x390;" u2="&#x3c2;" k="63" />
    <hkern u1="&#x390;" u2="&#x3bb;" k="-43" />
    <hkern u1="&#x390;" u2="&#x3b8;" k="66" />
    <hkern u1="&#x390;" u2="&#x3b4;" k="-43" />
    <hkern u1="&#x390;" u2="&#x3a9;" k="-23" />
    <hkern u1="&#x390;" u2="&#x3a7;" k="-43" />
    <hkern u1="&#x390;" u2="&#x3a6;" k="66" />
    <hkern u1="&#x390;" u2="&#x3a3;" k="-43" />
    <hkern u1="&#x391;" u2="&#x3c4;" k="143" />
    <hkern u1="&#x391;" u2="&#x3a9;" k="-47" />
    <hkern u1="&#x391;" u2="&#x3a8;" k="63" />
    <hkern u1="&#x391;" u2="&#x3a7;" k="-31" />
    <hkern u1="&#x391;" u2="&#x3a6;" k="33" />
    <hkern u1="&#x391;" u2="&#x3a4;" k="143" />
    <hkern u1="&#x391;" u2="&#x39e;" k="-31" />
    <hkern u1="&#x391;" u2="&#x21b;" k="193" />
    <hkern u1="&#x391;" u2="&#x219;" k="20" />
    <hkern u1="&#x391;" u2="&#x14b;" k="-20" />
    <hkern u1="&#x391;" u2="&#x14a;" k="-20" />
    <hkern u1="&#x391;" u2="v" k="160" />
    <hkern u1="&#x391;" u2="q" k="16" />
    <hkern u1="&#x391;" u2="p" k="-47" />
    <hkern u1="&#x391;" u2="n" k="-33" />
    <hkern u1="&#x391;" u2="l" k="-47" />
    <hkern u1="&#x391;" u2="k" k="-33" />
    <hkern u1="&#x391;" u2="e" k="-31" />
    <hkern u1="&#x391;" u2="b" k="-16" />
    <hkern u1="&#x391;" u2="V" k="170" />
    <hkern u1="&#x391;" u2="P" k="-20" />
    <hkern u1="&#x391;" u2="N" k="-20" />
    <hkern u1="&#x391;" u2="F" k="-41" />
    <hkern u1="&#x392;" u2="&#x3cd;" k="80" />
    <hkern u1="&#x392;" u2="&#x3a5;" k="80" />
    <hkern u1="&#x392;" u2="&#x3cb;" k="80" />
    <hkern u1="&#x392;" u2="&#x3c5;" k="80" />
    <hkern u1="&#x392;" u2="&#x3c4;" k="63" />
    <hkern u1="&#x392;" u2="&#x3b0;" k="80" />
    <hkern u1="&#x392;" u2="&#x3ab;" k="80" />
    <hkern u1="&#x392;" u2="&#x3a4;" k="63" />
    <hkern u1="&#x392;" u2="&#x178;" k="84" />
    <hkern u1="&#x392;" u2="Y" k="84" />
    <hkern u1="&#x392;" u2="W" k="41" />
    <hkern u1="&#x392;" u2="V" k="20" />
    <hkern u1="&#x392;" u2="J" k="43" />
    <hkern u1="&#x392;" u2="I" k="20" />
    <hkern u1="&#x393;" u2="&#x3ce;" k="106" />
    <hkern u1="&#x393;" u2="&#x3cc;" k="127" />
    <hkern u1="&#x393;" u2="&#x3b1;" k="213" />
    <hkern u1="&#x393;" u2="&#x398;" k="49" />
    <hkern u1="&#x393;" u2="&#x391;" k="209" />
    <hkern u1="&#x393;" u2="&#x3c9;" k="106" />
    <hkern u1="&#x393;" u2="&#x3c6;" k="96" />
    <hkern u1="&#x393;" u2="&#x3c3;" k="20" />
    <hkern u1="&#x393;" u2="&#x3c2;" k="106" />
    <hkern u1="&#x393;" u2="&#x3bf;" k="80" />
    <hkern u1="&#x393;" u2="&#x3bb;" k="213" />
    <hkern u1="&#x393;" u2="&#x3b8;" k="80" />
    <hkern u1="&#x393;" u2="&#x3b4;" k="213" />
    <hkern u1="&#x393;" u2="&#x3ac;" k="213" />
    <hkern u1="&#x393;" u2="&#x3a9;" k="96" />
    <hkern u1="&#x393;" u2="&#x3a6;" k="113" />
    <hkern u1="&#x393;" u2="&#x3a3;" k="20" />
    <hkern u1="&#x393;" u2="&#x39f;" k="49" />
    <hkern u1="&#x393;" u2="&#x39b;" k="207" />
    <hkern u1="&#x393;" u2="&#x396;" k="33" />
    <hkern u1="&#x393;" u2="&#x394;" k="209" />
    <hkern u1="&#x394;" u2="&#x3ce;" k="-47" />
    <hkern u1="&#x394;" u2="&#x3cd;" k="176" />
    <hkern u1="&#x394;" u2="&#x3cb;" k="176" />
    <hkern u1="&#x394;" u2="&#x3c9;" k="-47" />
    <hkern u1="&#x394;" u2="&#x3c5;" k="176" />
    <hkern u1="&#x394;" u2="&#x3b0;" k="176" />
    <hkern u1="&#x394;" u2="&#x3c4;" k="162" />
    <hkern u1="&#x394;" u2="&#x3a9;" k="-47" />
    <hkern u1="&#x394;" u2="&#x3a8;" k="63" />
    <hkern u1="&#x394;" u2="&#x3a7;" k="-31" />
    <hkern u1="&#x394;" u2="&#x3a6;" k="33" />
    <hkern u1="&#x394;" u2="&#x3a4;" k="162" />
    <hkern u1="&#x394;" u2="&#x39e;" k="-31" />
    <hkern u1="&#x394;" u2="&#x21b;" k="193" />
    <hkern u1="&#x394;" u2="&#x219;" k="20" />
    <hkern u1="&#x394;" u2="&#x14b;" k="-20" />
    <hkern u1="&#x394;" u2="&#x14a;" k="-20" />
    <hkern u1="&#x394;" u2="v" k="160" />
    <hkern u1="&#x394;" u2="q" k="16" />
    <hkern u1="&#x394;" u2="p" k="-47" />
    <hkern u1="&#x394;" u2="n" k="-33" />
    <hkern u1="&#x394;" u2="l" k="-47" />
    <hkern u1="&#x394;" u2="k" k="-33" />
    <hkern u1="&#x394;" u2="e" k="-31" />
    <hkern u1="&#x394;" u2="b" k="-16" />
    <hkern u1="&#x394;" u2="V" k="170" />
    <hkern u1="&#x394;" u2="P" k="-20" />
    <hkern u1="&#x394;" u2="N" k="-20" />
    <hkern u1="&#x394;" u2="F" k="-41" />
    <hkern u1="&#x398;" u2="&#x3c4;" k="49" />
    <hkern u1="&#x398;" u2="&#x3a4;" k="49" />
    <hkern u1="&#x399;" u2="&#x3c7;" k="-43" />
    <hkern u1="&#x399;" u2="&#x3c6;" k="66" />
    <hkern u1="&#x399;" u2="&#x3c3;" k="-43" />
    <hkern u1="&#x399;" u2="&#x3c2;" k="63" />
    <hkern u1="&#x399;" u2="&#x3c1;" k="-20" />
    <hkern u1="&#x399;" u2="&#x3bb;" k="-43" />
    <hkern u1="&#x399;" u2="&#x3b8;" k="63" />
    <hkern u1="&#x399;" u2="&#x3b6;" k="-43" />
    <hkern u1="&#x399;" u2="&#x3b4;" k="-43" />
    <hkern u1="&#x399;" u2="&#x3a9;" k="-23" />
    <hkern u1="&#x399;" u2="&#x3a7;" k="-43" />
    <hkern u1="&#x399;" u2="&#x3a6;" k="66" />
    <hkern u1="&#x399;" u2="&#x3a3;" k="-43" />
    <hkern u1="&#x399;" u2="&#x3a1;" k="-20" />
    <hkern u1="&#x399;" u2="&#x396;" k="-43" />
    <hkern u1="&#x39a;" u2="&#x3ce;" k="-63" />
    <hkern u1="&#x39a;" u2="&#x3cc;" k="127" />
    <hkern u1="&#x39a;" u2="&#x3ca;" k="-63" />
    <hkern u1="&#x39a;" u2="&#x3b1;" k="-43" />
    <hkern u1="&#x39a;" u2="&#x399;" k="-63" />
    <hkern u1="&#x39a;" u2="&#x398;" k="106" />
    <hkern u1="&#x39a;" u2="&#x391;" k="-43" />
    <hkern u1="&#x39a;" u2="&#x3c9;" k="-63" />
    <hkern u1="&#x39a;" u2="&#x3c8;" k="20" />
    <hkern u1="&#x39a;" u2="&#x3c6;" k="106" />
    <hkern u1="&#x39a;" u2="&#x3c2;" k="106" />
    <hkern u1="&#x39a;" u2="&#x3bf;" k="106" />
    <hkern u1="&#x39a;" u2="&#x3bb;" k="-43" />
    <hkern u1="&#x39a;" u2="&#x3b9;" k="-63" />
    <hkern u1="&#x39a;" u2="&#x3b8;" k="106" />
    <hkern u1="&#x39a;" u2="&#x3b4;" k="-43" />
    <hkern u1="&#x39a;" u2="&#x3af;" k="-63" />
    <hkern u1="&#x39a;" u2="&#x3ac;" k="-43" />
    <hkern u1="&#x39a;" u2="&#x3aa;" k="-63" />
    <hkern u1="&#x39a;" u2="&#x3a6;" k="106" />
    <hkern u1="&#x39a;" u2="&#x39f;" k="106" />
    <hkern u1="&#x39a;" u2="&#x39b;" k="-43" />
    <hkern u1="&#x39a;" u2="&#x394;" k="-43" />
    <hkern u1="&#x39a;" u2="&#x390;" k="-63" />
    <hkern u1="&#x39b;" u2="&#x3ce;" k="-47" />
    <hkern u1="&#x39b;" u2="&#x3cd;" k="176" />
    <hkern u1="&#x39b;" u2="&#x3cb;" k="176" />
    <hkern u1="&#x39b;" u2="&#x3c9;" k="-47" />
    <hkern u1="&#x39b;" u2="&#x3c5;" k="176" />
    <hkern u1="&#x39b;" u2="&#x3b0;" k="176" />
    <hkern u1="&#x39b;" u2="&#x3c4;" k="145" />
    <hkern u1="&#x39b;" u2="&#x3a9;" k="-47" />
    <hkern u1="&#x39b;" u2="&#x3a8;" k="63" />
    <hkern u1="&#x39b;" u2="&#x3a7;" k="-31" />
    <hkern u1="&#x39b;" u2="&#x3a6;" k="33" />
    <hkern u1="&#x39b;" u2="&#x3a4;" k="145" />
    <hkern u1="&#x39b;" u2="&#x39e;" k="-31" />
    <hkern u1="&#x39b;" u2="&#x21b;" k="193" />
    <hkern u1="&#x39b;" u2="&#x219;" k="20" />
    <hkern u1="&#x39b;" u2="&#x14b;" k="-20" />
    <hkern u1="&#x39b;" u2="&#x14a;" k="-20" />
    <hkern u1="&#x39b;" u2="v" k="160" />
    <hkern u1="&#x39b;" u2="q" k="16" />
    <hkern u1="&#x39b;" u2="p" k="-47" />
    <hkern u1="&#x39b;" u2="n" k="-33" />
    <hkern u1="&#x39b;" u2="l" k="-47" />
    <hkern u1="&#x39b;" u2="k" k="-33" />
    <hkern u1="&#x39b;" u2="e" k="-31" />
    <hkern u1="&#x39b;" u2="b" k="-16" />
    <hkern u1="&#x39b;" u2="V" k="170" />
    <hkern u1="&#x39b;" u2="P" k="-20" />
    <hkern u1="&#x39b;" u2="N" k="-20" />
    <hkern u1="&#x39b;" u2="F" k="-41" />
    <hkern u1="&#x39c;" u2="&#x3c7;" k="43" />
    <hkern u1="&#x39c;" u2="&#x3a7;" k="43" />
    <hkern u1="&#x39d;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x39d;" u2="&#x392;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3cd;" k="-31" />
    <hkern u1="&#x39e;" u2="&#x3a5;" k="-31" />
    <hkern u1="&#x39e;" u2="&#x391;" k="-33" />
    <hkern u1="&#x39e;" u2="&#x3cb;" k="-31" />
    <hkern u1="&#x39e;" u2="&#x3c5;" k="-31" />
    <hkern u1="&#x39e;" u2="&#x3b0;" k="-31" />
    <hkern u1="&#x39e;" u2="&#x3ab;" k="-31" />
    <hkern u1="&#x39e;" u2="&#x39b;" k="-33" />
    <hkern u1="&#x39e;" u2="&#x394;" k="-33" />
    <hkern u1="&#x39f;" u2="&#x3c4;" k="94" />
    <hkern u1="&#x39f;" u2="&#x3bb;" k="43" />
    <hkern u1="&#x39f;" u2="&#x3b6;" k="43" />
    <hkern u1="&#x39f;" u2="&#x3b4;" k="43" />
    <hkern u1="&#x39f;" u2="&#x3a4;" k="94" />
    <hkern u1="&#x39f;" u2="&#x396;" k="43" />
    <hkern u1="&#x3a0;" u2="&#x3c7;" k="63" />
    <hkern u1="&#x3a0;" u2="&#x3ba;" k="-20" />
    <hkern u1="&#x3a0;" u2="&#x3a7;" k="63" />
    <hkern u1="&#x3a0;" u2="&#x39a;" k="-20" />
    <hkern u1="&#x3a1;" u2="&#x3cd;" k="66" />
    <hkern u1="&#x3a1;" u2="&#x3ca;" k="43" />
    <hkern u1="&#x3a1;" u2="&#x3b1;" k="106" />
    <hkern u1="&#x3a1;" u2="&#x3a5;" k="41" />
    <hkern u1="&#x3a1;" u2="&#x399;" k="43" />
    <hkern u1="&#x3a1;" u2="&#x391;" k="106" />
    <hkern u1="&#x3a1;" u2="&#x3cb;" k="41" />
    <hkern u1="&#x3a1;" u2="&#x3c8;" k="-23" />
    <hkern u1="&#x3a1;" u2="&#x3c7;" k="86" />
    <hkern u1="&#x3a1;" u2="&#x3c5;" k="41" />
    <hkern u1="&#x3a1;" u2="&#x3c4;" k="31" />
    <hkern u1="&#x3a1;" u2="&#x3c3;" k="66" />
    <hkern u1="&#x3a1;" u2="&#x3bb;" k="106" />
    <hkern u1="&#x3a1;" u2="&#x3b9;" k="43" />
    <hkern u1="&#x3a1;" u2="&#x3b6;" k="63" />
    <hkern u1="&#x3a1;" u2="&#x3b5;" k="-43" />
    <hkern u1="&#x3a1;" u2="&#x3b4;" k="106" />
    <hkern u1="&#x3a1;" u2="&#x3b2;" k="-43" />
    <hkern u1="&#x3a1;" u2="&#x3b0;" k="41" />
    <hkern u1="&#x3a1;" u2="&#x3af;" k="43" />
    <hkern u1="&#x3a1;" u2="&#x3ad;" k="-43" />
    <hkern u1="&#x3a1;" u2="&#x3ac;" k="106" />
    <hkern u1="&#x3a1;" u2="&#x3ab;" k="41" />
    <hkern u1="&#x3a1;" u2="&#x3aa;" k="43" />
    <hkern u1="&#x3a1;" u2="&#x3a7;" k="86" />
    <hkern u1="&#x3a1;" u2="&#x3a4;" k="31" />
    <hkern u1="&#x3a1;" u2="&#x3a3;" k="66" />
    <hkern u1="&#x3a1;" u2="&#x39b;" k="106" />
    <hkern u1="&#x3a1;" u2="&#x396;" k="63" />
    <hkern u1="&#x3a1;" u2="&#x394;" k="106" />
    <hkern u1="&#x3a1;" u2="&#x390;" k="43" />
    <hkern u1="&#x3a3;" u2="&#x3ce;" k="-43" />
    <hkern u1="&#x3a3;" u2="&#x3cc;" k="63" />
    <hkern u1="&#x3a3;" u2="&#x3ca;" k="-20" />
    <hkern u1="&#x3a3;" u2="&#x3b1;" k="-43" />
    <hkern u1="&#x3a3;" u2="&#x399;" k="-20" />
    <hkern u1="&#x3a3;" u2="&#x398;" k="106" />
    <hkern u1="&#x3a3;" u2="&#x391;" k="-43" />
    <hkern u1="&#x3a3;" u2="&#x3c9;" k="-43" />
    <hkern u1="&#x3a3;" u2="&#x3c7;" k="-43" />
    <hkern u1="&#x3a3;" u2="&#x3c6;" k="106" />
    <hkern u1="&#x3a3;" u2="&#x3c2;" k="106" />
    <hkern u1="&#x3a3;" u2="&#x3bf;" k="106" />
    <hkern u1="&#x3a3;" u2="&#x3bb;" k="-43" />
    <hkern u1="&#x3a3;" u2="&#x3b9;" k="-20" />
    <hkern u1="&#x3a3;" u2="&#x3b8;" k="106" />
    <hkern u1="&#x3a3;" u2="&#x3b4;" k="-43" />
    <hkern u1="&#x3a3;" u2="&#x3b3;" k="-23" />
    <hkern u1="&#x3a3;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x3a3;" u2="&#x3af;" k="-20" />
    <hkern u1="&#x3a3;" u2="&#x3ac;" k="-43" />
    <hkern u1="&#x3a3;" u2="&#x3aa;" k="-20" />
    <hkern u1="&#x3a3;" u2="&#x3a9;" k="-43" />
    <hkern u1="&#x3a3;" u2="&#x3a7;" k="-43" />
    <hkern u1="&#x3a3;" u2="&#x3a6;" k="106" />
    <hkern u1="&#x3a3;" u2="&#x39f;" k="106" />
    <hkern u1="&#x3a3;" u2="&#x39b;" k="-43" />
    <hkern u1="&#x3a3;" u2="&#x394;" k="-43" />
    <hkern u1="&#x3a3;" u2="&#x393;" k="-23" />
    <hkern u1="&#x3a3;" u2="&#x392;" k="20" />
    <hkern u1="&#x3a3;" u2="&#x390;" k="-20" />
    <hkern u1="&#x3a4;" u2="&#x3ce;" k="63" />
    <hkern u1="&#x3a4;" u2="&#x3cd;" k="-16" />
    <hkern u1="&#x3a4;" u2="&#x3cc;" k="80" />
    <hkern u1="&#x3a4;" u2="&#x3b1;" k="176" />
    <hkern u1="&#x3a4;" u2="&#x3a5;" k="-43" />
    <hkern u1="&#x3a4;" u2="&#x398;" k="47" />
    <hkern u1="&#x3a4;" u2="&#x391;" k="150" />
    <hkern u1="&#x3a4;" u2="&#x3cb;" k="-16" />
    <hkern u1="&#x3a4;" u2="&#x3c9;" k="96" />
    <hkern u1="&#x3a4;" u2="&#x3c6;" k="84" />
    <hkern u1="&#x3a4;" u2="&#x3c5;" k="-16" />
    <hkern u1="&#x3a4;" u2="&#x3c2;" k="84" />
    <hkern u1="&#x3a4;" u2="&#x3bf;" k="106" />
    <hkern u1="&#x3a4;" u2="&#x3bb;" k="176" />
    <hkern u1="&#x3a4;" u2="&#x3ba;" k="-20" />
    <hkern u1="&#x3a4;" u2="&#x3b8;" k="63" />
    <hkern u1="&#x3a4;" u2="&#x3b7;" k="-31" />
    <hkern u1="&#x3a4;" u2="&#x3b4;" k="176" />
    <hkern u1="&#x3a4;" u2="&#x3b0;" k="-16" />
    <hkern u1="&#x3a4;" u2="&#x3ae;" k="-31" />
    <hkern u1="&#x3a4;" u2="&#x3ac;" k="176" />
    <hkern u1="&#x3a4;" u2="&#x3ab;" k="-43" />
    <hkern u1="&#x3a4;" u2="&#x3a9;" k="80" />
    <hkern u1="&#x3a4;" u2="&#x3a6;" k="80" />
    <hkern u1="&#x3a4;" u2="&#x3a0;" k="-23" />
    <hkern u1="&#x3a4;" u2="&#x39f;" k="96" />
    <hkern u1="&#x3a4;" u2="&#x39b;" k="145" />
    <hkern u1="&#x3a4;" u2="&#x39a;" k="-20" />
    <hkern u1="&#x3a4;" u2="&#x394;" k="150" />
    <hkern u1="&#x3a5;" u2="&#x3c8;" k="-33" />
    <hkern u1="&#x3a5;" u2="&#x3c7;" k="-33" />
    <hkern u1="&#x3a5;" u2="&#x3c6;" k="113" />
    <hkern u1="&#x3a5;" u2="&#x3c4;" k="-14" />
    <hkern u1="&#x3a5;" u2="&#x3c2;" k="86" />
    <hkern u1="&#x3a5;" u2="&#x3bb;" k="240" />
    <hkern u1="&#x3a5;" u2="&#x3b8;" k="106" />
    <hkern u1="&#x3a5;" u2="&#x3b6;" k="23" />
    <hkern u1="&#x3a5;" u2="&#x3b4;" k="223" />
    <hkern u1="&#x3a5;" u2="&#x3a9;" k="80" />
    <hkern u1="&#x3a5;" u2="&#x3a7;" k="-33" />
    <hkern u1="&#x3a5;" u2="&#x3a6;" k="113" />
    <hkern u1="&#x3a5;" u2="&#x3a4;" k="-14" />
    <hkern u1="&#x3a5;" u2="&#x39f;" k="113" />
    <hkern u1="&#x3a5;" u2="&#x397;" k="-33" />
    <hkern u1="&#x3a5;" u2="&#x396;" k="23" />
    <hkern u1="&#x3a5;" u2="&#x392;" k="-33" />
    <hkern u1="&#x3a6;" u2="&#x3ce;" k="-23" />
    <hkern u1="&#x3a6;" u2="&#x3cd;" k="84" />
    <hkern u1="&#x3a6;" u2="&#x3ca;" k="63" />
    <hkern u1="&#x3a6;" u2="&#x3c9;" k="-23" />
    <hkern u1="&#x3a6;" u2="&#x3b1;" k="43" />
    <hkern u1="&#x3a6;" u2="&#x3a5;" k="86" />
    <hkern u1="&#x3a6;" u2="&#x399;" k="63" />
    <hkern u1="&#x3a6;" u2="&#x391;" k="43" />
    <hkern u1="&#x3a6;" u2="&#x3cb;" k="86" />
    <hkern u1="&#x3a6;" u2="&#x3c7;" k="86" />
    <hkern u1="&#x3a6;" u2="&#x3c5;" k="86" />
    <hkern u1="&#x3a6;" u2="&#x3c4;" k="63" />
    <hkern u1="&#x3a6;" u2="&#x3c3;" k="106" />
    <hkern u1="&#x3a6;" u2="&#x3bc;" k="-23" />
    <hkern u1="&#x3a6;" u2="&#x3bb;" k="43" />
    <hkern u1="&#x3a6;" u2="&#x3b9;" k="63" />
    <hkern u1="&#x3a6;" u2="&#x3b7;" k="-23" />
    <hkern u1="&#x3a6;" u2="&#x3b6;" k="84" />
    <hkern u1="&#x3a6;" u2="&#x3b4;" k="43" />
    <hkern u1="&#x3a6;" u2="&#x3b3;" k="-20" />
    <hkern u1="&#x3a6;" u2="&#x3b2;" k="-20" />
    <hkern u1="&#x3a6;" u2="&#x3b0;" k="86" />
    <hkern u1="&#x3a6;" u2="&#x3af;" k="63" />
    <hkern u1="&#x3a6;" u2="&#x3ae;" k="-23" />
    <hkern u1="&#x3a6;" u2="&#x3ac;" k="43" />
    <hkern u1="&#x3a6;" u2="&#x3ab;" k="86" />
    <hkern u1="&#x3a6;" u2="&#x3aa;" k="63" />
    <hkern u1="&#x3a6;" u2="&#x3a7;" k="86" />
    <hkern u1="&#x3a6;" u2="&#x3a4;" k="63" />
    <hkern u1="&#x3a6;" u2="&#x3a3;" k="106" />
    <hkern u1="&#x3a6;" u2="&#x39b;" k="43" />
    <hkern u1="&#x3a6;" u2="&#x396;" k="84" />
    <hkern u1="&#x3a6;" u2="&#x394;" k="43" />
    <hkern u1="&#x3a6;" u2="&#x393;" k="-20" />
    <hkern u1="&#x3a6;" u2="&#x392;" k="-20" />
    <hkern u1="&#x3a6;" u2="&#x390;" k="63" />
    <hkern u1="&#x3a7;" u2="&#x3ce;" k="-43" />
    <hkern u1="&#x3a7;" u2="&#x3cd;" k="-43" />
    <hkern u1="&#x3a7;" u2="&#x3cc;" k="63" />
    <hkern u1="&#x3a7;" u2="&#x3ca;" k="-41" />
    <hkern u1="&#x3a7;" u2="&#x3b1;" k="-43" />
    <hkern u1="&#x3a7;" u2="&#x3a5;" k="-43" />
    <hkern u1="&#x3a7;" u2="&#x399;" k="-41" />
    <hkern u1="&#x3a7;" u2="&#x398;" k="86" />
    <hkern u1="&#x3a7;" u2="&#x3cb;" k="-43" />
    <hkern u1="&#x3a7;" u2="&#x3c9;" k="-43" />
    <hkern u1="&#x3a7;" u2="&#x3c7;" k="-43" />
    <hkern u1="&#x3a7;" u2="&#x3c6;" k="63" />
    <hkern u1="&#x3a7;" u2="&#x3c5;" k="-43" />
    <hkern u1="&#x3a7;" u2="&#x3c4;" k="-14" />
    <hkern u1="&#x3a7;" u2="&#x3c2;" k="63" />
    <hkern u1="&#x3a7;" u2="&#x3bf;" k="86" />
    <hkern u1="&#x3a7;" u2="&#x3bc;" k="43" />
    <hkern u1="&#x3a7;" u2="&#x3bb;" k="-43" />
    <hkern u1="&#x3a7;" u2="&#x3ba;" k="43" />
    <hkern u1="&#x3a7;" u2="&#x3b9;" k="-41" />
    <hkern u1="&#x3a7;" u2="&#x3b8;" k="86" />
    <hkern u1="&#x3a7;" u2="&#x3b6;" k="-20" />
    <hkern u1="&#x3a7;" u2="&#x3b4;" k="-43" />
    <hkern u1="&#x3a7;" u2="&#x3b2;" k="-20" />
    <hkern u1="&#x3a7;" u2="&#x3b0;" k="-43" />
    <hkern u1="&#x3a7;" u2="&#x3af;" k="-41" />
    <hkern u1="&#x3a7;" u2="&#x3ac;" k="-43" />
    <hkern u1="&#x3a7;" u2="&#x3ab;" k="-43" />
    <hkern u1="&#x3a7;" u2="&#x3aa;" k="-41" />
    <hkern u1="&#x3a7;" u2="&#x3a9;" k="-43" />
    <hkern u1="&#x3a7;" u2="&#x3a6;" k="63" />
    <hkern u1="&#x3a7;" u2="&#x3a4;" k="-14" />
    <hkern u1="&#x3a7;" u2="&#x39f;" k="86" />
    <hkern u1="&#x3a7;" u2="&#x39c;" k="43" />
    <hkern u1="&#x3a7;" u2="&#x39a;" k="43" />
    <hkern u1="&#x3a7;" u2="&#x392;" k="-20" />
    <hkern u1="&#x3a7;" u2="&#x390;" k="-41" />
    <hkern u1="&#x3a8;" u2="&#x3b1;" k="66" />
    <hkern u1="&#x3a8;" u2="&#x391;" k="66" />
    <hkern u1="&#x3a8;" u2="&#x3c4;" k="-33" />
    <hkern u1="&#x3a8;" u2="&#x3bb;" k="66" />
    <hkern u1="&#x3a8;" u2="&#x3b4;" k="66" />
    <hkern u1="&#x3a8;" u2="&#x3b3;" k="-23" />
    <hkern u1="&#x3a8;" u2="&#x3ac;" k="66" />
    <hkern u1="&#x3a8;" u2="&#x3a4;" k="-33" />
    <hkern u1="&#x3a8;" u2="&#x39b;" k="66" />
    <hkern u1="&#x3a8;" u2="&#x394;" k="66" />
    <hkern u1="&#x3a8;" u2="&#x393;" k="-23" />
    <hkern u1="&#x3a9;" u2="&#x3c7;" k="-43" />
    <hkern u1="&#x3a9;" u2="&#x3c4;" k="80" />
    <hkern u1="&#x3a9;" u2="&#x3bb;" k="-41" />
    <hkern u1="&#x3a9;" u2="&#x3b6;" k="-43" />
    <hkern u1="&#x3a9;" u2="&#x3b4;" k="-41" />
    <hkern u1="&#x3a9;" u2="&#x3b3;" k="-23" />
    <hkern u1="&#x3a9;" u2="&#x3a7;" k="-43" />
    <hkern u1="&#x3a9;" u2="&#x3a4;" k="80" />
    <hkern u1="&#x3a9;" u2="&#x396;" k="-43" />
    <hkern u1="&#x3a9;" u2="&#x393;" k="-23" />
    <hkern u1="&#x3aa;" u2="&#x3c7;" k="-43" />
    <hkern u1="&#x3aa;" u2="&#x3c6;" k="66" />
    <hkern u1="&#x3aa;" u2="&#x3c4;" k="-16" />
    <hkern u1="&#x3aa;" u2="&#x3c3;" k="-43" />
    <hkern u1="&#x3aa;" u2="&#x3c2;" k="63" />
    <hkern u1="&#x3aa;" u2="&#x3c1;" k="-20" />
    <hkern u1="&#x3aa;" u2="&#x3bb;" k="-43" />
    <hkern u1="&#x3aa;" u2="&#x3b8;" k="63" />
    <hkern u1="&#x3aa;" u2="&#x3b6;" k="-43" />
    <hkern u1="&#x3aa;" u2="&#x3b4;" k="-43" />
    <hkern u1="&#x3aa;" u2="&#x3a9;" k="-23" />
    <hkern u1="&#x3aa;" u2="&#x3a7;" k="-43" />
    <hkern u1="&#x3aa;" u2="&#x3a6;" k="66" />
    <hkern u1="&#x3aa;" u2="&#x3a4;" k="-16" />
    <hkern u1="&#x3aa;" u2="&#x3a3;" k="-43" />
    <hkern u1="&#x3aa;" u2="&#x3a1;" k="-20" />
    <hkern u1="&#x3aa;" u2="&#x396;" k="-43" />
    <hkern u1="&#x3ab;" u2="&#x3ce;" k="96" />
    <hkern u1="&#x3ab;" u2="&#x3cc;" k="129" />
    <hkern u1="&#x3ab;" u2="&#x3c9;" k="96" />
    <hkern u1="&#x3ab;" u2="&#x3bf;" k="129" />
    <hkern u1="&#x3ab;" u2="&#x3c8;" k="-33" />
    <hkern u1="&#x3ab;" u2="&#x3c7;" k="-33" />
    <hkern u1="&#x3ab;" u2="&#x3c6;" k="113" />
    <hkern u1="&#x3ab;" u2="&#x3c4;" k="-14" />
    <hkern u1="&#x3ab;" u2="&#x3c2;" k="86" />
    <hkern u1="&#x3ab;" u2="&#x3bb;" k="240" />
    <hkern u1="&#x3ab;" u2="&#x3b8;" k="106" />
    <hkern u1="&#x3ab;" u2="&#x3b6;" k="23" />
    <hkern u1="&#x3ab;" u2="&#x3b4;" k="223" />
    <hkern u1="&#x3ab;" u2="&#x3a9;" k="80" />
    <hkern u1="&#x3ab;" u2="&#x3a7;" k="-33" />
    <hkern u1="&#x3ab;" u2="&#x3a6;" k="113" />
    <hkern u1="&#x3ab;" u2="&#x3a4;" k="-14" />
    <hkern u1="&#x3ab;" u2="&#x39f;" k="113" />
    <hkern u1="&#x3ab;" u2="&#x397;" k="-33" />
    <hkern u1="&#x3ab;" u2="&#x396;" k="23" />
    <hkern u1="&#x3ab;" u2="&#x392;" k="-33" />
    <hkern u1="&#x3ac;" u2="&#x3c8;" k="66" />
    <hkern u1="&#x3ac;" u2="&#x3c7;" k="-49" />
    <hkern u1="&#x3ac;" u2="&#x3c6;" k="47" />
    <hkern u1="&#x3ac;" u2="&#x3c4;" k="127" />
    <hkern u1="&#x3ac;" u2="&#x3c2;" k="33" />
    <hkern u1="&#x3ac;" u2="&#x3c1;" k="-47" />
    <hkern u1="&#x3ac;" u2="&#x3c0;" k="-47" />
    <hkern u1="&#x3ac;" u2="&#x3be;" k="-31" />
    <hkern u1="&#x3ac;" u2="&#x3bd;" k="-31" />
    <hkern u1="&#x3ac;" u2="&#x3bb;" k="-66" />
    <hkern u1="&#x3ac;" u2="&#x3ba;" k="-49" />
    <hkern u1="&#x3ac;" u2="&#x3b8;" k="16" />
    <hkern u1="&#x3ac;" u2="&#x3b5;" k="-33" />
    <hkern u1="&#x3ac;" u2="&#x3b4;" k="-66" />
    <hkern u1="&#x3ac;" u2="&#x3b3;" k="-33" />
    <hkern u1="&#x3ac;" u2="&#x3b2;" k="-49" />
    <hkern u1="&#x3ac;" u2="&#x3a4;" k="127" />
    <hkern u1="&#x3ac;" u2="&#x393;" k="-33" />
    <hkern u1="&#x3ac;" u2="&#x21b;" k="193" />
    <hkern u1="&#x3ac;" u2="&#x219;" k="20" />
    <hkern u1="&#x3ac;" u2="&#x14b;" k="-20" />
    <hkern u1="&#x3ac;" u2="&#x14a;" k="-20" />
    <hkern u1="&#x3ac;" u2="V" k="170" />
    <hkern u1="&#x3ac;" u2="P" k="-20" />
    <hkern u1="&#x3ac;" u2="N" k="-20" />
    <hkern u1="&#x3ac;" u2="F" k="-41" />
    <hkern u1="&#x3af;" u2="&#x3cc;" k="63" />
    <hkern u1="&#x3af;" u2="&#x3bf;" k="63" />
    <hkern u1="&#x3af;" u2="&#x3c7;" k="-43" />
    <hkern u1="&#x3af;" u2="&#x3c6;" k="66" />
    <hkern u1="&#x3af;" u2="&#x3c3;" k="-43" />
    <hkern u1="&#x3af;" u2="&#x3c2;" k="63" />
    <hkern u1="&#x3af;" u2="&#x3bb;" k="-43" />
    <hkern u1="&#x3af;" u2="&#x3b8;" k="66" />
    <hkern u1="&#x3af;" u2="&#x3b4;" k="-43" />
    <hkern u1="&#x3af;" u2="&#x3a9;" k="-23" />
    <hkern u1="&#x3af;" u2="&#x3a7;" k="-43" />
    <hkern u1="&#x3af;" u2="&#x3a6;" k="66" />
    <hkern u1="&#x3af;" u2="&#x3a3;" k="-43" />
    <hkern u1="&#x3b0;" u2="&#x3ce;" k="96" />
    <hkern u1="&#x3b0;" u2="&#x3cc;" k="129" />
    <hkern u1="&#x3b0;" u2="&#x3c9;" k="96" />
    <hkern u1="&#x3b0;" u2="&#x3bf;" k="129" />
    <hkern u1="&#x3b0;" u2="&#x3c8;" k="-33" />
    <hkern u1="&#x3b0;" u2="&#x3c7;" k="-33" />
    <hkern u1="&#x3b0;" u2="&#x3c6;" k="113" />
    <hkern u1="&#x3b0;" u2="&#x3c4;" k="-43" />
    <hkern u1="&#x3b0;" u2="&#x3c2;" k="86" />
    <hkern u1="&#x3b0;" u2="&#x3bb;" k="240" />
    <hkern u1="&#x3b0;" u2="&#x3b8;" k="106" />
    <hkern u1="&#x3b0;" u2="&#x3b6;" k="23" />
    <hkern u1="&#x3b0;" u2="&#x3b4;" k="223" />
    <hkern u1="&#x3b0;" u2="&#x3ac;" k="170" />
    <hkern u1="&#x3b0;" u2="&#x3a9;" k="80" />
    <hkern u1="&#x3b0;" u2="&#x3a7;" k="-33" />
    <hkern u1="&#x3b0;" u2="&#x3a6;" k="113" />
    <hkern u1="&#x3b0;" u2="&#x3a4;" k="-43" />
    <hkern u1="&#x3b0;" u2="&#x39f;" k="113" />
    <hkern u1="&#x3b0;" u2="&#x39b;" k="193" />
    <hkern u1="&#x3b0;" u2="&#x397;" k="-33" />
    <hkern u1="&#x3b0;" u2="&#x396;" k="23" />
    <hkern u1="&#x3b0;" u2="&#x394;" k="193" />
    <hkern u1="&#x3b0;" u2="&#x392;" k="-33" />
    <hkern u1="&#x3b1;" u2="&#x3c8;" k="66" />
    <hkern u1="&#x3b1;" u2="&#x3c7;" k="-49" />
    <hkern u1="&#x3b1;" u2="&#x3c6;" k="47" />
    <hkern u1="&#x3b1;" u2="&#x3c4;" k="127" />
    <hkern u1="&#x3b1;" u2="&#x3c2;" k="33" />
    <hkern u1="&#x3b1;" u2="&#x3c1;" k="-47" />
    <hkern u1="&#x3b1;" u2="&#x3c0;" k="-47" />
    <hkern u1="&#x3b1;" u2="&#x3be;" k="-31" />
    <hkern u1="&#x3b1;" u2="&#x3bd;" k="-31" />
    <hkern u1="&#x3b1;" u2="&#x3bb;" k="-66" />
    <hkern u1="&#x3b1;" u2="&#x3ba;" k="-49" />
    <hkern u1="&#x3b1;" u2="&#x3b8;" k="16" />
    <hkern u1="&#x3b1;" u2="&#x3b5;" k="-33" />
    <hkern u1="&#x3b1;" u2="&#x3b4;" k="-66" />
    <hkern u1="&#x3b1;" u2="&#x3b3;" k="-33" />
    <hkern u1="&#x3b1;" u2="&#x3b2;" k="-49" />
    <hkern u1="&#x3b1;" u2="&#x3a4;" k="127" />
    <hkern u1="&#x3b1;" u2="&#x393;" k="-33" />
    <hkern u1="&#x3b1;" u2="&#x21b;" k="193" />
    <hkern u1="&#x3b1;" u2="&#x219;" k="20" />
    <hkern u1="&#x3b1;" u2="&#x14b;" k="-20" />
    <hkern u1="&#x3b1;" u2="&#x14a;" k="-20" />
    <hkern u1="&#x3b1;" u2="V" k="170" />
    <hkern u1="&#x3b1;" u2="P" k="-20" />
    <hkern u1="&#x3b1;" u2="N" k="-20" />
    <hkern u1="&#x3b1;" u2="F" k="-41" />
    <hkern u1="&#x3b2;" u2="&#x3cd;" k="80" />
    <hkern u1="&#x3b2;" u2="&#x3a5;" k="80" />
    <hkern u1="&#x3b2;" u2="&#x3cb;" k="80" />
    <hkern u1="&#x3b2;" u2="&#x3c5;" k="80" />
    <hkern u1="&#x3b2;" u2="&#x3c4;" k="63" />
    <hkern u1="&#x3b2;" u2="&#x3b0;" k="80" />
    <hkern u1="&#x3b2;" u2="&#x3ab;" k="80" />
    <hkern u1="&#x3b2;" u2="&#x3a4;" k="63" />
    <hkern u1="&#x3b2;" u2="&#x178;" k="84" />
    <hkern u1="&#x3b2;" u2="Y" k="84" />
    <hkern u1="&#x3b2;" u2="W" k="41" />
    <hkern u1="&#x3b2;" u2="V" k="20" />
    <hkern u1="&#x3b2;" u2="J" k="43" />
    <hkern u1="&#x3b2;" u2="I" k="20" />
    <hkern u1="&#x3b3;" u2="&#x3ce;" k="106" />
    <hkern u1="&#x3b3;" u2="&#x3cc;" k="127" />
    <hkern u1="&#x3b3;" u2="&#x3b1;" k="213" />
    <hkern u1="&#x3b3;" u2="&#x398;" k="49" />
    <hkern u1="&#x3b3;" u2="&#x391;" k="193" />
    <hkern u1="&#x3b3;" u2="&#x3c9;" k="106" />
    <hkern u1="&#x3b3;" u2="&#x3c6;" k="96" />
    <hkern u1="&#x3b3;" u2="&#x3c3;" k="20" />
    <hkern u1="&#x3b3;" u2="&#x3c2;" k="106" />
    <hkern u1="&#x3b3;" u2="&#x3bf;" k="80" />
    <hkern u1="&#x3b3;" u2="&#x3bb;" k="213" />
    <hkern u1="&#x3b3;" u2="&#x3b8;" k="80" />
    <hkern u1="&#x3b3;" u2="&#x3b4;" k="213" />
    <hkern u1="&#x3b3;" u2="&#x3ac;" k="213" />
    <hkern u1="&#x3b3;" u2="&#x3a9;" k="96" />
    <hkern u1="&#x3b3;" u2="&#x3a6;" k="113" />
    <hkern u1="&#x3b3;" u2="&#x3a3;" k="20" />
    <hkern u1="&#x3b3;" u2="&#x39f;" k="49" />
    <hkern u1="&#x3b3;" u2="&#x39b;" k="207" />
    <hkern u1="&#x3b3;" u2="&#x396;" k="33" />
    <hkern u1="&#x3b3;" u2="&#x394;" k="193" />
    <hkern u1="&#x3b4;" u2="&#x3ce;" k="-49" />
    <hkern u1="&#x3b4;" u2="&#x3cd;" k="209" />
    <hkern u1="&#x3b4;" u2="&#x3cc;" k="33" />
    <hkern u1="&#x3b4;" u2="&#x3a5;" k="209" />
    <hkern u1="&#x3b4;" u2="&#x3cb;" k="209" />
    <hkern u1="&#x3b4;" u2="&#x3c9;" k="-49" />
    <hkern u1="&#x3b4;" u2="&#x3c8;" k="66" />
    <hkern u1="&#x3b4;" u2="&#x3c7;" k="-49" />
    <hkern u1="&#x3b4;" u2="&#x3c6;" k="47" />
    <hkern u1="&#x3b4;" u2="&#x3c5;" k="209" />
    <hkern u1="&#x3b4;" u2="&#x3c4;" k="127" />
    <hkern u1="&#x3b4;" u2="&#x3c2;" k="33" />
    <hkern u1="&#x3b4;" u2="&#x3c1;" k="-47" />
    <hkern u1="&#x3b4;" u2="&#x3c0;" k="-47" />
    <hkern u1="&#x3b4;" u2="&#x3bf;" k="33" />
    <hkern u1="&#x3b4;" u2="&#x3be;" k="-31" />
    <hkern u1="&#x3b4;" u2="&#x3bd;" k="-31" />
    <hkern u1="&#x3b4;" u2="&#x3bb;" k="-31" />
    <hkern u1="&#x3b4;" u2="&#x3ba;" k="-49" />
    <hkern u1="&#x3b4;" u2="&#x3b8;" k="16" />
    <hkern u1="&#x3b4;" u2="&#x3b5;" k="-33" />
    <hkern u1="&#x3b4;" u2="&#x3b4;" k="-66" />
    <hkern u1="&#x3b4;" u2="&#x3b3;" k="-33" />
    <hkern u1="&#x3b4;" u2="&#x3b2;" k="-49" />
    <hkern u1="&#x3b4;" u2="&#x3b0;" k="209" />
    <hkern u1="&#x3b4;" u2="&#x3ad;" k="-33" />
    <hkern u1="&#x3b4;" u2="&#x3ab;" k="209" />
    <hkern u1="&#x3b4;" u2="&#x3a4;" k="127" />
    <hkern u1="&#x3b4;" u2="&#x393;" k="-33" />
    <hkern u1="&#x3b4;" u2="&#x21b;" k="193" />
    <hkern u1="&#x3b4;" u2="&#x21a;" k="193" />
    <hkern u1="&#x3b4;" u2="&#x219;" k="20" />
    <hkern u1="&#x3b4;" u2="&#x218;" k="20" />
    <hkern u1="&#x3b4;" u2="&#x178;" k="193" />
    <hkern u1="&#x3b4;" u2="&#x152;" k="63" />
    <hkern u1="&#x3b4;" u2="&#x14b;" k="-20" />
    <hkern u1="&#x3b4;" u2="&#x14a;" k="-20" />
    <hkern u1="&#x3b4;" u2="Z" k="-43" />
    <hkern u1="&#x3b4;" u2="Y" k="193" />
    <hkern u1="&#x3b4;" u2="W" k="150" />
    <hkern u1="&#x3b4;" u2="V" k="170" />
    <hkern u1="&#x3b4;" u2="T" k="193" />
    <hkern u1="&#x3b4;" u2="S" k="20" />
    <hkern u1="&#x3b4;" u2="Q" k="63" />
    <hkern u1="&#x3b4;" u2="P" k="-20" />
    <hkern u1="&#x3b4;" u2="O" k="63" />
    <hkern u1="&#x3b4;" u2="N" k="-20" />
    <hkern u1="&#x3b4;" u2="J" k="-43" />
    <hkern u1="&#x3b4;" u2="G" k="63" />
    <hkern u1="&#x3b4;" u2="F" k="-41" />
    <hkern u1="&#x3b4;" u2="C" k="63" />
    <hkern u1="&#x3b8;" u2="&#x3cd;" k="96" />
    <hkern u1="&#x3b8;" u2="&#x3b1;" k="33" />
    <hkern u1="&#x3b8;" u2="&#x3a5;" k="96" />
    <hkern u1="&#x3b8;" u2="&#x3cb;" k="96" />
    <hkern u1="&#x3b8;" u2="&#x3c5;" k="96" />
    <hkern u1="&#x3b8;" u2="&#x3c4;" k="49" />
    <hkern u1="&#x3b8;" u2="&#x3bb;" k="33" />
    <hkern u1="&#x3b8;" u2="&#x3b4;" k="33" />
    <hkern u1="&#x3b8;" u2="&#x3b0;" k="96" />
    <hkern u1="&#x3b8;" u2="&#x3ac;" k="33" />
    <hkern u1="&#x3b8;" u2="&#x3ab;" k="96" />
    <hkern u1="&#x3b8;" u2="&#x3a4;" k="49" />
    <hkern u1="&#x3b9;" u2="&#x3cc;" k="63" />
    <hkern u1="&#x3b9;" u2="&#x3bf;" k="63" />
    <hkern u1="&#x3b9;" u2="&#x3c7;" k="-43" />
    <hkern u1="&#x3b9;" u2="&#x3c6;" k="66" />
    <hkern u1="&#x3b9;" u2="&#x3c3;" k="-43" />
    <hkern u1="&#x3b9;" u2="&#x3c2;" k="63" />
    <hkern u1="&#x3b9;" u2="&#x3bb;" k="-43" />
    <hkern u1="&#x3b9;" u2="&#x3b8;" k="66" />
    <hkern u1="&#x3b9;" u2="&#x3b4;" k="-43" />
    <hkern u1="&#x3b9;" u2="&#x3a9;" k="-23" />
    <hkern u1="&#x3b9;" u2="&#x3a7;" k="-43" />
    <hkern u1="&#x3b9;" u2="&#x3a6;" k="66" />
    <hkern u1="&#x3b9;" u2="&#x3a3;" k="-43" />
    <hkern u1="&#x3ba;" u2="&#x3ce;" k="-63" />
    <hkern u1="&#x3ba;" u2="&#x3cc;" k="127" />
    <hkern u1="&#x3ba;" u2="&#x3ca;" k="-63" />
    <hkern u1="&#x3ba;" u2="&#x3b1;" k="-43" />
    <hkern u1="&#x3ba;" u2="&#x3c9;" k="-63" />
    <hkern u1="&#x3ba;" u2="&#x3c8;" k="20" />
    <hkern u1="&#x3ba;" u2="&#x3c6;" k="106" />
    <hkern u1="&#x3ba;" u2="&#x3c2;" k="106" />
    <hkern u1="&#x3ba;" u2="&#x3bf;" k="106" />
    <hkern u1="&#x3ba;" u2="&#x3bb;" k="-43" />
    <hkern u1="&#x3ba;" u2="&#x3b9;" k="-63" />
    <hkern u1="&#x3ba;" u2="&#x3b8;" k="106" />
    <hkern u1="&#x3ba;" u2="&#x3b4;" k="-43" />
    <hkern u1="&#x3ba;" u2="&#x3af;" k="-63" />
    <hkern u1="&#x3ba;" u2="&#x3ac;" k="-43" />
    <hkern u1="&#x3ba;" u2="&#x3a6;" k="106" />
    <hkern u1="&#x3ba;" u2="&#x390;" k="-63" />
    <hkern u1="&#x3bb;" u2="&#x3ce;" k="-49" />
    <hkern u1="&#x3bb;" u2="&#x3cd;" k="209" />
    <hkern u1="&#x3bb;" u2="&#x3cc;" k="33" />
    <hkern u1="&#x3bb;" u2="&#x3a5;" k="209" />
    <hkern u1="&#x3bb;" u2="&#x3cb;" k="209" />
    <hkern u1="&#x3bb;" u2="&#x3c9;" k="-49" />
    <hkern u1="&#x3bb;" u2="&#x3c8;" k="66" />
    <hkern u1="&#x3bb;" u2="&#x3c7;" k="-49" />
    <hkern u1="&#x3bb;" u2="&#x3c6;" k="47" />
    <hkern u1="&#x3bb;" u2="&#x3c5;" k="209" />
    <hkern u1="&#x3bb;" u2="&#x3c4;" k="127" />
    <hkern u1="&#x3bb;" u2="&#x3c2;" k="33" />
    <hkern u1="&#x3bb;" u2="&#x3c1;" k="-47" />
    <hkern u1="&#x3bb;" u2="&#x3c0;" k="-47" />
    <hkern u1="&#x3bb;" u2="&#x3bf;" k="33" />
    <hkern u1="&#x3bb;" u2="&#x3be;" k="-31" />
    <hkern u1="&#x3bb;" u2="&#x3bd;" k="-31" />
    <hkern u1="&#x3bb;" u2="&#x3bb;" k="-31" />
    <hkern u1="&#x3bb;" u2="&#x3ba;" k="-49" />
    <hkern u1="&#x3bb;" u2="&#x3b8;" k="16" />
    <hkern u1="&#x3bb;" u2="&#x3b5;" k="-33" />
    <hkern u1="&#x3bb;" u2="&#x3b4;" k="-66" />
    <hkern u1="&#x3bb;" u2="&#x3b3;" k="-33" />
    <hkern u1="&#x3bb;" u2="&#x3b2;" k="-49" />
    <hkern u1="&#x3bb;" u2="&#x3b0;" k="209" />
    <hkern u1="&#x3bb;" u2="&#x3ad;" k="-33" />
    <hkern u1="&#x3bb;" u2="&#x3ab;" k="209" />
    <hkern u1="&#x3bb;" u2="&#x3a4;" k="127" />
    <hkern u1="&#x3bb;" u2="&#x393;" k="-33" />
    <hkern u1="&#x3bb;" u2="&#x21b;" k="193" />
    <hkern u1="&#x3bb;" u2="&#x21a;" k="193" />
    <hkern u1="&#x3bb;" u2="&#x219;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x218;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x178;" k="193" />
    <hkern u1="&#x3bb;" u2="&#x152;" k="63" />
    <hkern u1="&#x3bb;" u2="&#x14b;" k="-20" />
    <hkern u1="&#x3bb;" u2="&#x14a;" k="-20" />
    <hkern u1="&#x3bb;" u2="Z" k="-43" />
    <hkern u1="&#x3bb;" u2="Y" k="193" />
    <hkern u1="&#x3bb;" u2="W" k="150" />
    <hkern u1="&#x3bb;" u2="V" k="170" />
    <hkern u1="&#x3bb;" u2="T" k="193" />
    <hkern u1="&#x3bb;" u2="S" k="20" />
    <hkern u1="&#x3bb;" u2="Q" k="63" />
    <hkern u1="&#x3bb;" u2="P" k="-20" />
    <hkern u1="&#x3bb;" u2="O" k="63" />
    <hkern u1="&#x3bb;" u2="N" k="-20" />
    <hkern u1="&#x3bb;" u2="J" k="-43" />
    <hkern u1="&#x3bb;" u2="G" k="63" />
    <hkern u1="&#x3bb;" u2="F" k="-41" />
    <hkern u1="&#x3bb;" u2="C" k="63" />
    <hkern u1="&#x3bc;" u2="&#x3c7;" k="43" />
    <hkern u1="&#x3bc;" u2="&#x3a7;" k="43" />
    <hkern u1="&#x3be;" u2="&#x3b1;" k="-16" />
    <hkern u1="&#x3be;" u2="&#x3bb;" k="-16" />
    <hkern u1="&#x3be;" u2="&#x3b4;" k="-16" />
    <hkern u1="&#x3be;" u2="&#x3ac;" k="-16" />
    <hkern u1="&#x3bf;" u2="&#x3cb;" k="106" />
    <hkern u1="&#x3bf;" u2="&#x3c7;" k="86" />
    <hkern u1="&#x3bf;" u2="&#x3c5;" k="106" />
    <hkern u1="&#x3bf;" u2="&#x3c4;" k="63" />
    <hkern u1="&#x3bf;" u2="&#x3c3;" k="86" />
    <hkern u1="&#x3bf;" u2="&#x3bb;" k="43" />
    <hkern u1="&#x3bf;" u2="&#x3b9;" k="63" />
    <hkern u1="&#x3bf;" u2="&#x3b6;" k="86" />
    <hkern u1="&#x3bf;" u2="&#x3b4;" k="43" />
    <hkern u1="&#x3bf;" u2="&#x3b0;" k="106" />
    <hkern u1="&#x3bf;" u2="&#x3af;" k="63" />
    <hkern u1="&#x3bf;" u2="&#x3ab;" k="106" />
    <hkern u1="&#x3bf;" u2="&#x3aa;" k="63" />
    <hkern u1="&#x3bf;" u2="&#x3a7;" k="86" />
    <hkern u1="&#x3bf;" u2="&#x3a4;" k="63" />
    <hkern u1="&#x3bf;" u2="&#x3a3;" k="86" />
    <hkern u1="&#x3bf;" u2="&#x396;" k="86" />
    <hkern u1="&#x3bf;" u2="&#x390;" k="63" />
    <hkern u1="&#x3c0;" u2="&#x3cd;" k="-16" />
    <hkern u1="&#x3c0;" u2="&#x3a5;" k="-16" />
    <hkern u1="&#x3c0;" u2="&#x3cb;" k="-16" />
    <hkern u1="&#x3c0;" u2="&#x3c7;" k="63" />
    <hkern u1="&#x3c0;" u2="&#x3c5;" k="-16" />
    <hkern u1="&#x3c0;" u2="&#x3b3;" k="-31" />
    <hkern u1="&#x3c0;" u2="&#x3b0;" k="-16" />
    <hkern u1="&#x3c0;" u2="&#x3ab;" k="-16" />
    <hkern u1="&#x3c0;" u2="&#x3a7;" k="63" />
    <hkern u1="&#x3c0;" u2="&#x393;" k="-31" />
    <hkern u1="&#x3c1;" u2="&#x3cd;" k="66" />
    <hkern u1="&#x3c1;" u2="&#x3b1;" k="111" />
    <hkern u1="&#x3c1;" u2="&#x3a5;" k="41" />
    <hkern u1="&#x3c1;" u2="&#x3cb;" k="41" />
    <hkern u1="&#x3c1;" u2="&#x3c8;" k="-23" />
    <hkern u1="&#x3c1;" u2="&#x3c7;" k="86" />
    <hkern u1="&#x3c1;" u2="&#x3c5;" k="41" />
    <hkern u1="&#x3c1;" u2="&#x3c3;" k="66" />
    <hkern u1="&#x3c1;" u2="&#x3bb;" k="111" />
    <hkern u1="&#x3c1;" u2="&#x3b6;" k="66" />
    <hkern u1="&#x3c1;" u2="&#x3b5;" k="-43" />
    <hkern u1="&#x3c1;" u2="&#x3b4;" k="111" />
    <hkern u1="&#x3c1;" u2="&#x3b3;" k="-16" />
    <hkern u1="&#x3c1;" u2="&#x3b2;" k="-43" />
    <hkern u1="&#x3c1;" u2="&#x3b0;" k="41" />
    <hkern u1="&#x3c1;" u2="&#x3ad;" k="-43" />
    <hkern u1="&#x3c1;" u2="&#x3ac;" k="111" />
    <hkern u1="&#x3c1;" u2="&#x3ab;" k="41" />
    <hkern u1="&#x3c1;" u2="&#x3a7;" k="86" />
    <hkern u1="&#x3c1;" u2="&#x3a3;" k="66" />
    <hkern u1="&#x3c1;" u2="&#x393;" k="-16" />
    <hkern u1="&#x3c2;" u2="&#x3cd;" k="63" />
    <hkern u1="&#x3c2;" u2="&#x3ca;" k="43" />
    <hkern u1="&#x3c2;" u2="&#x3b1;" k="20" />
    <hkern u1="&#x3c2;" u2="&#x3a5;" k="63" />
    <hkern u1="&#x3c2;" u2="&#x399;" k="43" />
    <hkern u1="&#x3c2;" u2="&#x391;" k="20" />
    <hkern u1="&#x3c2;" u2="&#x3cb;" k="63" />
    <hkern u1="&#x3c2;" u2="&#x3c7;" k="41" />
    <hkern u1="&#x3c2;" u2="&#x3c5;" k="63" />
    <hkern u1="&#x3c2;" u2="&#x3c4;" k="47" />
    <hkern u1="&#x3c2;" u2="&#x3c3;" k="66" />
    <hkern u1="&#x3c2;" u2="&#x3bb;" k="20" />
    <hkern u1="&#x3c2;" u2="&#x3b9;" k="43" />
    <hkern u1="&#x3c2;" u2="&#x3b6;" k="23" />
    <hkern u1="&#x3c2;" u2="&#x3b4;" k="20" />
    <hkern u1="&#x3c2;" u2="&#x3b3;" k="-16" />
    <hkern u1="&#x3c2;" u2="&#x3b0;" k="63" />
    <hkern u1="&#x3c2;" u2="&#x3af;" k="43" />
    <hkern u1="&#x3c2;" u2="&#x3ac;" k="20" />
    <hkern u1="&#x3c2;" u2="&#x3ab;" k="63" />
    <hkern u1="&#x3c2;" u2="&#x3aa;" k="43" />
    <hkern u1="&#x3c2;" u2="&#x3a7;" k="41" />
    <hkern u1="&#x3c2;" u2="&#x3a4;" k="47" />
    <hkern u1="&#x3c2;" u2="&#x3a3;" k="66" />
    <hkern u1="&#x3c2;" u2="&#x39b;" k="20" />
    <hkern u1="&#x3c2;" u2="&#x396;" k="23" />
    <hkern u1="&#x3c2;" u2="&#x394;" k="20" />
    <hkern u1="&#x3c2;" u2="&#x393;" k="-16" />
    <hkern u1="&#x3c2;" u2="&#x390;" k="43" />
    <hkern u1="&#x3c3;" u2="&#x3ce;" k="-20" />
    <hkern u1="&#x3c3;" u2="&#x3cc;" k="63" />
    <hkern u1="&#x3c3;" u2="&#x3ca;" k="-20" />
    <hkern u1="&#x3c3;" u2="&#x399;" k="-20" />
    <hkern u1="&#x3c3;" u2="&#x398;" k="106" />
    <hkern u1="&#x3c3;" u2="&#x391;" k="-43" />
    <hkern u1="&#x3c3;" u2="&#x3c9;" k="-20" />
    <hkern u1="&#x3c3;" u2="&#x3c6;" k="109" />
    <hkern u1="&#x3c3;" u2="&#x3c4;" k="16" />
    <hkern u1="&#x3c3;" u2="&#x3c2;" k="106" />
    <hkern u1="&#x3c3;" u2="&#x3bf;" k="106" />
    <hkern u1="&#x3c3;" u2="&#x3b9;" k="-20" />
    <hkern u1="&#x3c3;" u2="&#x3b8;" k="106" />
    <hkern u1="&#x3c3;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x3c3;" u2="&#x3af;" k="-20" />
    <hkern u1="&#x3c3;" u2="&#x3aa;" k="-20" />
    <hkern u1="&#x3c3;" u2="&#x3a4;" k="16" />
    <hkern u1="&#x3c3;" u2="&#x39f;" k="106" />
    <hkern u1="&#x3c3;" u2="&#x39b;" k="-43" />
    <hkern u1="&#x3c3;" u2="&#x394;" k="-43" />
    <hkern u1="&#x3c3;" u2="&#x392;" k="20" />
    <hkern u1="&#x3c3;" u2="&#x390;" k="-20" />
    <hkern u1="&#x3c4;" u2="&#x3ce;" k="63" />
    <hkern u1="&#x3c4;" u2="&#x3cd;" k="-16" />
    <hkern u1="&#x3c4;" u2="&#x3cc;" k="80" />
    <hkern u1="&#x3c4;" u2="&#x3b1;" k="176" />
    <hkern u1="&#x3c4;" u2="&#x3a5;" k="-43" />
    <hkern u1="&#x3c4;" u2="&#x398;" k="47" />
    <hkern u1="&#x3c4;" u2="&#x391;" k="150" />
    <hkern u1="&#x3c4;" u2="&#x3cb;" k="-16" />
    <hkern u1="&#x3c4;" u2="&#x3c9;" k="96" />
    <hkern u1="&#x3c4;" u2="&#x3c6;" k="84" />
    <hkern u1="&#x3c4;" u2="&#x3c5;" k="-16" />
    <hkern u1="&#x3c4;" u2="&#x3c2;" k="84" />
    <hkern u1="&#x3c4;" u2="&#x3bf;" k="106" />
    <hkern u1="&#x3c4;" u2="&#x3bb;" k="176" />
    <hkern u1="&#x3c4;" u2="&#x3ba;" k="-20" />
    <hkern u1="&#x3c4;" u2="&#x3b8;" k="63" />
    <hkern u1="&#x3c4;" u2="&#x3b7;" k="-31" />
    <hkern u1="&#x3c4;" u2="&#x3b4;" k="176" />
    <hkern u1="&#x3c4;" u2="&#x3b0;" k="-16" />
    <hkern u1="&#x3c4;" u2="&#x3ae;" k="-31" />
    <hkern u1="&#x3c4;" u2="&#x3ac;" k="176" />
    <hkern u1="&#x3c4;" u2="&#x3ab;" k="-43" />
    <hkern u1="&#x3c4;" u2="&#x3a9;" k="80" />
    <hkern u1="&#x3c4;" u2="&#x3a6;" k="80" />
    <hkern u1="&#x3c4;" u2="&#x3a0;" k="-23" />
    <hkern u1="&#x3c4;" u2="&#x39f;" k="96" />
    <hkern u1="&#x3c4;" u2="&#x39b;" k="145" />
    <hkern u1="&#x3c4;" u2="&#x39a;" k="-20" />
    <hkern u1="&#x3c4;" u2="&#x394;" k="150" />
    <hkern u1="&#x3c5;" u2="&#x3ce;" k="96" />
    <hkern u1="&#x3c5;" u2="&#x3cc;" k="129" />
    <hkern u1="&#x3c5;" u2="&#x3c9;" k="96" />
    <hkern u1="&#x3c5;" u2="&#x3bf;" k="129" />
    <hkern u1="&#x3c5;" u2="&#x3c8;" k="-33" />
    <hkern u1="&#x3c5;" u2="&#x3c7;" k="-33" />
    <hkern u1="&#x3c5;" u2="&#x3c6;" k="113" />
    <hkern u1="&#x3c5;" u2="&#x3c4;" k="-43" />
    <hkern u1="&#x3c5;" u2="&#x3c2;" k="86" />
    <hkern u1="&#x3c5;" u2="&#x3bb;" k="240" />
    <hkern u1="&#x3c5;" u2="&#x3b8;" k="106" />
    <hkern u1="&#x3c5;" u2="&#x3b6;" k="23" />
    <hkern u1="&#x3c5;" u2="&#x3b4;" k="223" />
    <hkern u1="&#x3c5;" u2="&#x3ac;" k="170" />
    <hkern u1="&#x3c5;" u2="&#x3a9;" k="80" />
    <hkern u1="&#x3c5;" u2="&#x3a7;" k="-33" />
    <hkern u1="&#x3c5;" u2="&#x3a6;" k="113" />
    <hkern u1="&#x3c5;" u2="&#x3a4;" k="-43" />
    <hkern u1="&#x3c5;" u2="&#x39f;" k="113" />
    <hkern u1="&#x3c5;" u2="&#x39b;" k="193" />
    <hkern u1="&#x3c5;" u2="&#x397;" k="-33" />
    <hkern u1="&#x3c5;" u2="&#x396;" k="23" />
    <hkern u1="&#x3c5;" u2="&#x394;" k="193" />
    <hkern u1="&#x3c5;" u2="&#x392;" k="-33" />
    <hkern u1="&#x3c6;" u2="&#x3ce;" k="-23" />
    <hkern u1="&#x3c6;" u2="&#x3cd;" k="84" />
    <hkern u1="&#x3c6;" u2="&#x3ca;" k="63" />
    <hkern u1="&#x3c6;" u2="&#x3c9;" k="-23" />
    <hkern u1="&#x3c6;" u2="&#x3b1;" k="43" />
    <hkern u1="&#x3c6;" u2="&#x3a5;" k="80" />
    <hkern u1="&#x3c6;" u2="&#x399;" k="63" />
    <hkern u1="&#x3c6;" u2="&#x391;" k="43" />
    <hkern u1="&#x3c6;" u2="&#x3cb;" k="80" />
    <hkern u1="&#x3c6;" u2="&#x3c7;" k="63" />
    <hkern u1="&#x3c6;" u2="&#x3c5;" k="80" />
    <hkern u1="&#x3c6;" u2="&#x3c4;" k="96" />
    <hkern u1="&#x3c6;" u2="&#x3c3;" k="84" />
    <hkern u1="&#x3c6;" u2="&#x3bc;" k="-23" />
    <hkern u1="&#x3c6;" u2="&#x3bb;" k="43" />
    <hkern u1="&#x3c6;" u2="&#x3b9;" k="63" />
    <hkern u1="&#x3c6;" u2="&#x3b7;" k="-23" />
    <hkern u1="&#x3c6;" u2="&#x3b6;" k="84" />
    <hkern u1="&#x3c6;" u2="&#x3b4;" k="43" />
    <hkern u1="&#x3c6;" u2="&#x3b3;" k="-20" />
    <hkern u1="&#x3c6;" u2="&#x3b2;" k="-20" />
    <hkern u1="&#x3c6;" u2="&#x3b0;" k="80" />
    <hkern u1="&#x3c6;" u2="&#x3af;" k="63" />
    <hkern u1="&#x3c6;" u2="&#x3ae;" k="-23" />
    <hkern u1="&#x3c6;" u2="&#x3ac;" k="43" />
    <hkern u1="&#x3c6;" u2="&#x3ab;" k="80" />
    <hkern u1="&#x3c6;" u2="&#x3aa;" k="63" />
    <hkern u1="&#x3c6;" u2="&#x3a4;" k="96" />
    <hkern u1="&#x3c6;" u2="&#x39b;" k="43" />
    <hkern u1="&#x3c6;" u2="&#x396;" k="84" />
    <hkern u1="&#x3c6;" u2="&#x394;" k="43" />
    <hkern u1="&#x3c6;" u2="&#x393;" k="-20" />
    <hkern u1="&#x3c6;" u2="&#x392;" k="-20" />
    <hkern u1="&#x3c6;" u2="&#x390;" k="63" />
    <hkern u1="&#x3c7;" u2="&#x3cd;" k="-43" />
    <hkern u1="&#x3c7;" u2="&#x3cc;" k="63" />
    <hkern u1="&#x3c7;" u2="&#x3ca;" k="-41" />
    <hkern u1="&#x3c7;" u2="&#x3b1;" k="-43" />
    <hkern u1="&#x3c7;" u2="&#x3a5;" k="-31" />
    <hkern u1="&#x3c7;" u2="&#x399;" k="-41" />
    <hkern u1="&#x3c7;" u2="&#x398;" k="86" />
    <hkern u1="&#x3c7;" u2="&#x3cb;" k="-31" />
    <hkern u1="&#x3c7;" u2="&#x3c7;" k="-43" />
    <hkern u1="&#x3c7;" u2="&#x3c6;" k="63" />
    <hkern u1="&#x3c7;" u2="&#x3c5;" k="-31" />
    <hkern u1="&#x3c7;" u2="&#x3c2;" k="63" />
    <hkern u1="&#x3c7;" u2="&#x3bf;" k="86" />
    <hkern u1="&#x3c7;" u2="&#x3bc;" k="43" />
    <hkern u1="&#x3c7;" u2="&#x3bb;" k="-43" />
    <hkern u1="&#x3c7;" u2="&#x3ba;" k="43" />
    <hkern u1="&#x3c7;" u2="&#x3b9;" k="-41" />
    <hkern u1="&#x3c7;" u2="&#x3b8;" k="86" />
    <hkern u1="&#x3c7;" u2="&#x3b6;" k="-20" />
    <hkern u1="&#x3c7;" u2="&#x3b4;" k="-43" />
    <hkern u1="&#x3c7;" u2="&#x3b2;" k="-20" />
    <hkern u1="&#x3c7;" u2="&#x3b0;" k="-31" />
    <hkern u1="&#x3c7;" u2="&#x3af;" k="-41" />
    <hkern u1="&#x3c7;" u2="&#x3ac;" k="-43" />
    <hkern u1="&#x3c7;" u2="&#x3ab;" k="-31" />
    <hkern u1="&#x3c7;" u2="&#x3aa;" k="-41" />
    <hkern u1="&#x3c7;" u2="&#x39f;" k="86" />
    <hkern u1="&#x3c7;" u2="&#x39c;" k="43" />
    <hkern u1="&#x3c7;" u2="&#x39a;" k="43" />
    <hkern u1="&#x3c7;" u2="&#x392;" k="-20" />
    <hkern u1="&#x3c7;" u2="&#x390;" k="-41" />
    <hkern u1="&#x3c8;" u2="&#x3b1;" k="66" />
    <hkern u1="&#x3c8;" u2="&#x391;" k="66" />
    <hkern u1="&#x3c8;" u2="&#x3bb;" k="66" />
    <hkern u1="&#x3c8;" u2="&#x3b4;" k="66" />
    <hkern u1="&#x3c8;" u2="&#x3b3;" k="-23" />
    <hkern u1="&#x3c8;" u2="&#x3ac;" k="66" />
    <hkern u1="&#x3c8;" u2="&#x39b;" k="66" />
    <hkern u1="&#x3c8;" u2="&#x394;" k="66" />
    <hkern u1="&#x3c8;" u2="&#x393;" k="-23" />
    <hkern u1="&#x3c9;" u2="&#x3cd;" k="86" />
    <hkern u1="&#x3c9;" u2="&#x3cb;" k="106" />
    <hkern u1="&#x3c9;" u2="&#x3c5;" k="106" />
    <hkern u1="&#x3c9;" u2="&#x3c4;" k="84" />
    <hkern u1="&#x3c9;" u2="&#x3bb;" k="-41" />
    <hkern u1="&#x3c9;" u2="&#x3b6;" k="-43" />
    <hkern u1="&#x3c9;" u2="&#x3b4;" k="-41" />
    <hkern u1="&#x3c9;" u2="&#x3b3;" k="-23" />
    <hkern u1="&#x3c9;" u2="&#x3b0;" k="106" />
    <hkern u1="&#x3c9;" u2="&#x3ab;" k="106" />
    <hkern u1="&#x3c9;" u2="&#x3a4;" k="84" />
    <hkern u1="&#x3c9;" u2="&#x396;" k="-43" />
    <hkern u1="&#x3c9;" u2="&#x393;" k="-23" />
    <hkern u1="&#x3ca;" u2="&#x3c7;" k="-43" />
    <hkern u1="&#x3ca;" u2="&#x3c6;" k="66" />
    <hkern u1="&#x3ca;" u2="&#x3c3;" k="-43" />
    <hkern u1="&#x3ca;" u2="&#x3c2;" k="63" />
    <hkern u1="&#x3ca;" u2="&#x3bb;" k="-43" />
    <hkern u1="&#x3ca;" u2="&#x3b8;" k="66" />
    <hkern u1="&#x3ca;" u2="&#x3b4;" k="-43" />
    <hkern u1="&#x3ca;" u2="&#x3a9;" k="-23" />
    <hkern u1="&#x3ca;" u2="&#x3a7;" k="-43" />
    <hkern u1="&#x3ca;" u2="&#x3a6;" k="66" />
    <hkern u1="&#x3ca;" u2="&#x3a3;" k="-43" />
    <hkern u1="&#x3cb;" u2="&#x3ce;" k="96" />
    <hkern u1="&#x3cb;" u2="&#x3cc;" k="129" />
    <hkern u1="&#x3cb;" u2="&#x3c9;" k="96" />
    <hkern u1="&#x3cb;" u2="&#x3bf;" k="129" />
    <hkern u1="&#x3cb;" u2="&#x3c8;" k="-33" />
    <hkern u1="&#x3cb;" u2="&#x3c7;" k="-33" />
    <hkern u1="&#x3cb;" u2="&#x3c6;" k="113" />
    <hkern u1="&#x3cb;" u2="&#x3c4;" k="-43" />
    <hkern u1="&#x3cb;" u2="&#x3c2;" k="86" />
    <hkern u1="&#x3cb;" u2="&#x3bb;" k="240" />
    <hkern u1="&#x3cb;" u2="&#x3b8;" k="106" />
    <hkern u1="&#x3cb;" u2="&#x3b6;" k="23" />
    <hkern u1="&#x3cb;" u2="&#x3b4;" k="223" />
    <hkern u1="&#x3cb;" u2="&#x3ac;" k="170" />
    <hkern u1="&#x3cb;" u2="&#x3a9;" k="80" />
    <hkern u1="&#x3cb;" u2="&#x3a7;" k="-33" />
    <hkern u1="&#x3cb;" u2="&#x3a6;" k="113" />
    <hkern u1="&#x3cb;" u2="&#x3a4;" k="-43" />
    <hkern u1="&#x3cb;" u2="&#x39f;" k="113" />
    <hkern u1="&#x3cb;" u2="&#x39b;" k="193" />
    <hkern u1="&#x3cb;" u2="&#x397;" k="-33" />
    <hkern u1="&#x3cb;" u2="&#x396;" k="23" />
    <hkern u1="&#x3cb;" u2="&#x394;" k="193" />
    <hkern u1="&#x3cb;" u2="&#x392;" k="-33" />
    <hkern u1="&#x3cc;" u2="&#x3cb;" k="106" />
    <hkern u1="&#x3cc;" u2="&#x3c7;" k="86" />
    <hkern u1="&#x3cc;" u2="&#x3c5;" k="106" />
    <hkern u1="&#x3cc;" u2="&#x3c4;" k="63" />
    <hkern u1="&#x3cc;" u2="&#x3c3;" k="86" />
    <hkern u1="&#x3cc;" u2="&#x3bb;" k="43" />
    <hkern u1="&#x3cc;" u2="&#x3b9;" k="63" />
    <hkern u1="&#x3cc;" u2="&#x3b6;" k="86" />
    <hkern u1="&#x3cc;" u2="&#x3b4;" k="43" />
    <hkern u1="&#x3cc;" u2="&#x3b0;" k="106" />
    <hkern u1="&#x3cc;" u2="&#x3af;" k="63" />
    <hkern u1="&#x3cc;" u2="&#x3ab;" k="106" />
    <hkern u1="&#x3cc;" u2="&#x3aa;" k="63" />
    <hkern u1="&#x3cc;" u2="&#x3a7;" k="86" />
    <hkern u1="&#x3cc;" u2="&#x3a4;" k="63" />
    <hkern u1="&#x3cc;" u2="&#x3a3;" k="86" />
    <hkern u1="&#x3cc;" u2="&#x396;" k="86" />
    <hkern u1="&#x3cc;" u2="&#x390;" k="63" />
    <hkern u1="&#x3cd;" u2="&#x3c8;" k="-33" />
    <hkern u1="&#x3cd;" u2="&#x3c7;" k="-33" />
    <hkern u1="&#x3cd;" u2="&#x3c6;" k="84" />
    <hkern u1="&#x3cd;" u2="&#x3c4;" k="-20" />
    <hkern u1="&#x3cd;" u2="&#x3c2;" k="86" />
    <hkern u1="&#x3cd;" u2="&#x3bb;" k="170" />
    <hkern u1="&#x3cd;" u2="&#x3b8;" k="106" />
    <hkern u1="&#x3cd;" u2="&#x3b6;" k="23" />
    <hkern u1="&#x3cd;" u2="&#x3b4;" k="170" />
    <hkern u1="&#x3cd;" u2="&#x3ac;" k="170" />
    <hkern u1="&#x3cd;" u2="&#x3a9;" k="84" />
    <hkern u1="&#x3cd;" u2="&#x3a7;" k="-33" />
    <hkern u1="&#x3cd;" u2="&#x3a6;" k="84" />
    <hkern u1="&#x3cd;" u2="&#x3a4;" k="-20" />
    <hkern u1="&#x3cd;" u2="&#x39f;" k="113" />
    <hkern u1="&#x3cd;" u2="&#x39b;" k="193" />
    <hkern u1="&#x3cd;" u2="&#x397;" k="-33" />
    <hkern u1="&#x3cd;" u2="&#x396;" k="23" />
    <hkern u1="&#x3cd;" u2="&#x394;" k="193" />
    <hkern u1="&#x3cd;" u2="&#x392;" k="-33" />
    <hkern u1="&#x3ce;" u2="&#x3cb;" k="106" />
    <hkern u1="&#x3ce;" u2="&#x3c5;" k="106" />
    <hkern u1="&#x3ce;" u2="&#x3c4;" k="106" />
    <hkern u1="&#x3ce;" u2="&#x3bb;" k="-41" />
    <hkern u1="&#x3ce;" u2="&#x3b6;" k="-43" />
    <hkern u1="&#x3ce;" u2="&#x3b4;" k="-41" />
    <hkern u1="&#x3ce;" u2="&#x3b3;" k="-23" />
    <hkern u1="&#x3ce;" u2="&#x3b0;" k="106" />
    <hkern u1="&#x3ce;" u2="&#x3ab;" k="106" />
    <hkern u1="&#x3ce;" u2="&#x3a4;" k="106" />
    <hkern u1="&#x3ce;" u2="&#x396;" k="-43" />
    <hkern u1="&#x3ce;" u2="&#x393;" k="-23" />
    <hkern u1="&#x401;" u2="&#x44f;" k="-43" />
    <hkern u1="&#x401;" u2="&#x44e;" k="-41" />
    <hkern u1="&#x401;" u2="&#x44a;" k="-23" />
    <hkern u1="&#x401;" u2="&#x446;" k="-23" />
    <hkern u1="&#x401;" u2="&#x445;" k="-23" />
    <hkern u1="&#x401;" u2="&#x442;" k="-43" />
    <hkern u1="&#x401;" u2="&#x441;" k="-43" />
    <hkern u1="&#x401;" u2="&#x43d;" k="-43" />
    <hkern u1="&#x401;" u2="&#x43b;" k="-43" />
    <hkern u1="&#x401;" u2="&#x436;" k="-63" />
    <hkern u1="&#x401;" u2="&#x434;" k="-63" />
    <hkern u1="&#x401;" u2="&#x42f;" k="-43" />
    <hkern u1="&#x401;" u2="&#x42e;" k="-41" />
    <hkern u1="&#x401;" u2="&#x42a;" k="-23" />
    <hkern u1="&#x401;" u2="&#x422;" k="-43" />
    <hkern u1="&#x401;" u2="&#x421;" k="-43" />
    <hkern u1="&#x401;" u2="&#x41b;" k="-43" />
    <hkern u1="&#x401;" u2="&#x416;" k="-63" />
    <hkern u1="&#x401;" u2="&#x414;" k="-63" />
    <hkern u1="&#x403;" u2="&#x44f;" k="31" />
    <hkern u1="&#x403;" u2="&#x44e;" k="-31" />
    <hkern u1="&#x403;" u2="&#x44d;" k="41" />
    <hkern u1="&#x403;" u2="&#x44a;" k="-43" />
    <hkern u1="&#x403;" u2="&#x444;" k="129" />
    <hkern u1="&#x403;" u2="&#x442;" k="-16" />
    <hkern u1="&#x403;" u2="&#x441;" k="63" />
    <hkern u1="&#x403;" u2="&#x43e;" k="80" />
    <hkern u1="&#x403;" u2="&#x43b;" k="209" />
    <hkern u1="&#x403;" u2="&#x436;" k="47" />
    <hkern u1="&#x403;" u2="&#x434;" k="170" />
    <hkern u1="&#x403;" u2="&#x432;" k="-16" />
    <hkern u1="&#x403;" u2="&#x430;" k="209" />
    <hkern u1="&#x403;" u2="&#x42f;" k="47" />
    <hkern u1="&#x403;" u2="&#x42e;" k="-33" />
    <hkern u1="&#x403;" u2="&#x424;" k="80" />
    <hkern u1="&#x403;" u2="&#x421;" k="63" />
    <hkern u1="&#x403;" u2="&#x41f;" k="-33" />
    <hkern u1="&#x403;" u2="&#x41e;" k="80" />
    <hkern u1="&#x403;" u2="&#x41c;" k="-33" />
    <hkern u1="&#x403;" u2="&#x41b;" k="209" />
    <hkern u1="&#x403;" u2="&#x414;" k="176" />
    <hkern u1="&#x403;" u2="&#x410;" k="209" />
    <hkern u1="&#x409;" u2="&#x445;" k="63" />
    <hkern u1="&#x409;" u2="&#x442;" k="150" />
    <hkern u1="&#x409;" u2="&#x436;" k="43" />
    <hkern u1="&#x409;" u2="&#x432;" k="41" />
    <hkern u1="&#x409;" u2="&#x425;" k="63" />
    <hkern u1="&#x409;" u2="&#x422;" k="150" />
    <hkern u1="&#x409;" u2="&#x416;" k="43" />
    <hkern u1="&#x409;" u2="&#x412;" k="41" />
    <hkern u1="&#x40a;" u2="&#x445;" k="63" />
    <hkern u1="&#x40a;" u2="&#x442;" k="150" />
    <hkern u1="&#x40a;" u2="&#x436;" k="43" />
    <hkern u1="&#x40a;" u2="&#x432;" k="41" />
    <hkern u1="&#x40a;" u2="&#x425;" k="63" />
    <hkern u1="&#x40a;" u2="&#x422;" k="150" />
    <hkern u1="&#x40a;" u2="&#x416;" k="43" />
    <hkern u1="&#x40a;" u2="&#x412;" k="41" />
    <hkern u1="&#x40c;" u2="&#x44e;" k="-63" />
    <hkern u1="&#x40c;" u2="&#x44d;" k="86" />
    <hkern u1="&#x40c;" u2="&#x44a;" k="33" />
    <hkern u1="&#x40c;" u2="&#x447;" k="49" />
    <hkern u1="&#x40c;" u2="&#x445;" k="-33" />
    <hkern u1="&#x40c;" u2="&#x444;" k="160" />
    <hkern u1="&#x40c;" u2="&#x442;" k="20" />
    <hkern u1="&#x40c;" u2="&#x441;" k="94" />
    <hkern u1="&#x40c;" u2="&#x440;" k="-33" />
    <hkern u1="&#x40c;" u2="&#x43e;" k="106" />
    <hkern u1="&#x40c;" u2="&#x437;" k="33" />
    <hkern u1="&#x40c;" u2="&#x434;" k="-33" />
    <hkern u1="&#x40c;" u2="&#x432;" k="-33" />
    <hkern u1="&#x40c;" u2="&#x431;" k="-43" />
    <hkern u1="&#x40c;" u2="&#x42d;" k="113" />
    <hkern u1="&#x40c;" u2="&#x424;" k="96" />
    <hkern u1="&#x40c;" u2="&#x421;" k="113" />
    <hkern u1="&#x40c;" u2="&#x41e;" k="111" />
    <hkern u1="&#x40c;" u2="&#x417;" k="47" />
    <hkern u1="&#x40c;" u2="&#x416;" k="-33" />
    <hkern u1="&#x40e;" u2="&#x44f;" k="96" />
    <hkern u1="&#x40e;" u2="&#x44e;" k="-43" />
    <hkern u1="&#x40e;" u2="&#x44d;" k="80" />
    <hkern u1="&#x40e;" u2="&#x44b;" k="-43" />
    <hkern u1="&#x40e;" u2="&#x44a;" k="-43" />
    <hkern u1="&#x40e;" u2="&#x447;" k="-20" />
    <hkern u1="&#x40e;" u2="&#x445;" k="-43" />
    <hkern u1="&#x40e;" u2="&#x444;" k="113" />
    <hkern u1="&#x40e;" u2="&#x442;" k="-49" />
    <hkern u1="&#x40e;" u2="&#x441;" k="63" />
    <hkern u1="&#x40e;" u2="&#x440;" k="-43" />
    <hkern u1="&#x40e;" u2="&#x43f;" k="-43" />
    <hkern u1="&#x40e;" u2="&#x43e;" k="240" />
    <hkern u1="&#x40e;" u2="&#x43d;" k="-43" />
    <hkern u1="&#x40e;" u2="&#x43c;" k="-43" />
    <hkern u1="&#x40e;" u2="&#x43b;" k="240" />
    <hkern u1="&#x40e;" u2="&#x434;" k="129" />
    <hkern u1="&#x40e;" u2="&#x433;" k="-43" />
    <hkern u1="&#x40e;" u2="&#x432;" k="-20" />
    <hkern u1="&#x40e;" u2="&#x431;" k="-43" />
    <hkern u1="&#x40e;" u2="&#x430;" k="240" />
    <hkern u1="&#x40e;" u2="&#x42f;" k="63" />
    <hkern u1="&#x40e;" u2="&#x42e;" k="-33" />
    <hkern u1="&#x40e;" u2="&#x42d;" k="47" />
    <hkern u1="&#x40e;" u2="&#x424;" k="80" />
    <hkern u1="&#x40e;" u2="&#x422;" k="-14" />
    <hkern u1="&#x40e;" u2="&#x421;" k="66" />
    <hkern u1="&#x40e;" u2="&#x420;" k="-47" />
    <hkern u1="&#x40e;" u2="&#x41e;" k="80" />
    <hkern u1="&#x40e;" u2="&#x41b;" k="240" />
    <hkern u1="&#x40e;" u2="&#x413;" k="-43" />
    <hkern u1="&#x40e;" u2="&#x412;" k="-31" />
    <hkern u1="&#x40e;" u2="&#x410;" k="240" />
    <hkern u1="&#x40f;" u2="&#x432;" k="-20" />
    <hkern u1="&#x40f;" u2="&#x412;" k="-20" />
    <hkern u1="&#x410;" u2="&#x451;" k="-43" />
    <hkern u1="&#x410;" u2="&#x443;" k="143" />
    <hkern u1="&#x410;" u2="&#x40e;" k="143" />
    <hkern u1="&#x410;" u2="&#x45e;" k="143" />
    <hkern u1="&#x410;" u2="&#x44e;" k="-43" />
    <hkern u1="&#x410;" u2="&#x44c;" k="-41" />
    <hkern u1="&#x410;" u2="&#x44b;" k="-43" />
    <hkern u1="&#x410;" u2="&#x44a;" k="43" />
    <hkern u1="&#x410;" u2="&#x449;" k="-20" />
    <hkern u1="&#x410;" u2="&#x448;" k="-23" />
    <hkern u1="&#x410;" u2="&#x447;" k="145" />
    <hkern u1="&#x410;" u2="&#x446;" k="-43" />
    <hkern u1="&#x410;" u2="&#x445;" k="-43" />
    <hkern u1="&#x410;" u2="&#x444;" k="63" />
    <hkern u1="&#x410;" u2="&#x442;" k="160" />
    <hkern u1="&#x410;" u2="&#x441;" k="20" />
    <hkern u1="&#x410;" u2="&#x440;" k="-43" />
    <hkern u1="&#x410;" u2="&#x43f;" k="-43" />
    <hkern u1="&#x410;" u2="&#x43e;" k="20" />
    <hkern u1="&#x410;" u2="&#x43d;" k="-43" />
    <hkern u1="&#x410;" u2="&#x43b;" k="-43" />
    <hkern u1="&#x410;" u2="&#x437;" k="-20" />
    <hkern u1="&#x410;" u2="&#x436;" k="-43" />
    <hkern u1="&#x410;" u2="&#x435;" k="-43" />
    <hkern u1="&#x410;" u2="&#x434;" k="-63" />
    <hkern u1="&#x410;" u2="&#x433;" k="-43" />
    <hkern u1="&#x410;" u2="&#x432;" k="-43" />
    <hkern u1="&#x410;" u2="&#x431;" k="-20" />
    <hkern u1="&#x410;" u2="&#x42f;" k="-33" />
    <hkern u1="&#x410;" u2="&#x42e;" k="-31" />
    <hkern u1="&#x410;" u2="&#x42b;" k="-47" />
    <hkern u1="&#x410;" u2="&#x42a;" k="47" />
    <hkern u1="&#x410;" u2="&#x428;" k="-33" />
    <hkern u1="&#x410;" u2="&#x427;" k="111" />
    <hkern u1="&#x410;" u2="&#x426;" k="-47" />
    <hkern u1="&#x410;" u2="&#x425;" k="-49" />
    <hkern u1="&#x410;" u2="&#x423;" k="143" />
    <hkern u1="&#x410;" u2="&#x422;" k="127" />
    <hkern u1="&#x410;" u2="&#x421;" k="63" />
    <hkern u1="&#x410;" u2="&#x41e;" k="47" />
    <hkern u1="&#x410;" u2="&#x41b;" k="-33" />
    <hkern u1="&#x410;" u2="&#x416;" k="-33" />
    <hkern u1="&#x410;" u2="&#x414;" k="-47" />
    <hkern u1="&#x410;" u2="&#x413;" k="-43" />
    <hkern u1="&#x410;" u2="&#x412;" k="-33" />
    <hkern u1="&#x410;" u2="&#x411;" k="-33" />
    <hkern u1="&#x410;" u2="&#x410;" k="-33" />
    <hkern u1="&#x411;" u2="&#x443;" k="43" />
    <hkern u1="&#x411;" u2="&#x40e;" k="43" />
    <hkern u1="&#x411;" u2="&#x45e;" k="43" />
    <hkern u1="&#x411;" u2="&#x44f;" k="41" />
    <hkern u1="&#x411;" u2="&#x44b;" k="-20" />
    <hkern u1="&#x411;" u2="&#x44a;" k="43" />
    <hkern u1="&#x411;" u2="&#x448;" k="-20" />
    <hkern u1="&#x411;" u2="&#x447;" k="43" />
    <hkern u1="&#x411;" u2="&#x442;" k="63" />
    <hkern u1="&#x411;" u2="&#x440;" k="-20" />
    <hkern u1="&#x411;" u2="&#x43f;" k="-20" />
    <hkern u1="&#x411;" u2="&#x43e;" k="-20" />
    <hkern u1="&#x411;" u2="&#x43b;" k="23" />
    <hkern u1="&#x411;" u2="&#x437;" k="-43" />
    <hkern u1="&#x411;" u2="&#x436;" k="20" />
    <hkern u1="&#x411;" u2="&#x434;" k="20" />
    <hkern u1="&#x411;" u2="&#x432;" k="-20" />
    <hkern u1="&#x411;" u2="&#x42f;" k="41" />
    <hkern u1="&#x411;" u2="&#x42b;" k="-20" />
    <hkern u1="&#x411;" u2="&#x42a;" k="43" />
    <hkern u1="&#x411;" u2="&#x427;" k="43" />
    <hkern u1="&#x411;" u2="&#x423;" k="43" />
    <hkern u1="&#x411;" u2="&#x422;" k="63" />
    <hkern u1="&#x411;" u2="&#x41e;" k="-20" />
    <hkern u1="&#x411;" u2="&#x417;" k="-43" />
    <hkern u1="&#x411;" u2="&#x416;" k="20" />
    <hkern u1="&#x411;" u2="&#x414;" k="20" />
    <hkern u1="&#x411;" u2="&#x413;" k="-31" />
    <hkern u1="&#x411;" u2="&#x412;" k="-20" />
    <hkern u1="&#x412;" u2="&#x443;" k="63" />
    <hkern u1="&#x412;" u2="&#x40e;" k="82" />
    <hkern u1="&#x412;" u2="&#x45f;" k="-20" />
    <hkern u1="&#x412;" u2="&#x45e;" k="63" />
    <hkern u1="&#x412;" u2="&#x44f;" k="41" />
    <hkern u1="&#x412;" u2="&#x44e;" k="-43" />
    <hkern u1="&#x412;" u2="&#x44a;" k="43" />
    <hkern u1="&#x412;" u2="&#x447;" k="43" />
    <hkern u1="&#x412;" u2="&#x446;" k="-43" />
    <hkern u1="&#x412;" u2="&#x445;" k="43" />
    <hkern u1="&#x412;" u2="&#x442;" k="109" />
    <hkern u1="&#x412;" u2="&#x43f;" k="-43" />
    <hkern u1="&#x412;" u2="&#x43e;" k="43" />
    <hkern u1="&#x412;" u2="&#x43d;" k="-20" />
    <hkern u1="&#x412;" u2="&#x43c;" k="-23" />
    <hkern u1="&#x412;" u2="&#x43b;" k="43" />
    <hkern u1="&#x412;" u2="&#x437;" k="-43" />
    <hkern u1="&#x412;" u2="&#x436;" k="43" />
    <hkern u1="&#x412;" u2="&#x434;" k="23" />
    <hkern u1="&#x412;" u2="&#x433;" k="-23" />
    <hkern u1="&#x412;" u2="&#x432;" k="-20" />
    <hkern u1="&#x412;" u2="&#x430;" k="43" />
    <hkern u1="&#x412;" u2="&#x42f;" k="41" />
    <hkern u1="&#x412;" u2="&#x42e;" k="-43" />
    <hkern u1="&#x412;" u2="&#x42a;" k="43" />
    <hkern u1="&#x412;" u2="&#x427;" k="43" />
    <hkern u1="&#x412;" u2="&#x426;" k="-43" />
    <hkern u1="&#x412;" u2="&#x425;" k="43" />
    <hkern u1="&#x412;" u2="&#x423;" k="82" />
    <hkern u1="&#x412;" u2="&#x422;" k="63" />
    <hkern u1="&#x412;" u2="&#x41f;" k="-43" />
    <hkern u1="&#x412;" u2="&#x41d;" k="-20" />
    <hkern u1="&#x412;" u2="&#x41c;" k="-23" />
    <hkern u1="&#x412;" u2="&#x41b;" k="43" />
    <hkern u1="&#x412;" u2="&#x417;" k="-43" />
    <hkern u1="&#x412;" u2="&#x416;" k="43" />
    <hkern u1="&#x412;" u2="&#x414;" k="23" />
    <hkern u1="&#x412;" u2="&#x413;" k="-23" />
    <hkern u1="&#x412;" u2="&#x412;" k="-20" />
    <hkern u1="&#x412;" u2="&#x410;" k="43" />
    <hkern u1="&#x412;" u2="&#x40f;" k="-20" />
    <hkern u1="&#x413;" u2="&#x44f;" k="31" />
    <hkern u1="&#x413;" u2="&#x44e;" k="-31" />
    <hkern u1="&#x413;" u2="&#x44d;" k="41" />
    <hkern u1="&#x413;" u2="&#x44a;" k="-43" />
    <hkern u1="&#x413;" u2="&#x444;" k="129" />
    <hkern u1="&#x413;" u2="&#x442;" k="-16" />
    <hkern u1="&#x413;" u2="&#x441;" k="63" />
    <hkern u1="&#x413;" u2="&#x43e;" k="80" />
    <hkern u1="&#x413;" u2="&#x43b;" k="209" />
    <hkern u1="&#x413;" u2="&#x436;" k="47" />
    <hkern u1="&#x413;" u2="&#x434;" k="170" />
    <hkern u1="&#x413;" u2="&#x432;" k="-16" />
    <hkern u1="&#x413;" u2="&#x430;" k="209" />
    <hkern u1="&#x413;" u2="&#x42f;" k="47" />
    <hkern u1="&#x413;" u2="&#x42e;" k="-33" />
    <hkern u1="&#x413;" u2="&#x424;" k="80" />
    <hkern u1="&#x413;" u2="&#x421;" k="63" />
    <hkern u1="&#x413;" u2="&#x41f;" k="-33" />
    <hkern u1="&#x413;" u2="&#x41e;" k="80" />
    <hkern u1="&#x413;" u2="&#x41c;" k="-33" />
    <hkern u1="&#x413;" u2="&#x41b;" k="209" />
    <hkern u1="&#x413;" u2="&#x414;" k="176" />
    <hkern u1="&#x413;" u2="&#x410;" k="209" />
    <hkern u1="&#x414;" u2="&#x451;" k="-43" />
    <hkern u1="&#x414;" u2="&#x443;" k="23" />
    <hkern u1="&#x414;" u2="&#x40e;" k="23" />
    <hkern u1="&#x414;" u2="&#x45e;" k="23" />
    <hkern u1="&#x414;" u2="&#x44d;" k="41" />
    <hkern u1="&#x414;" u2="&#x44b;" k="-43" />
    <hkern u1="&#x414;" u2="&#x44a;" k="20" />
    <hkern u1="&#x414;" u2="&#x447;" k="23" />
    <hkern u1="&#x414;" u2="&#x444;" k="41" />
    <hkern u1="&#x414;" u2="&#x442;" k="43" />
    <hkern u1="&#x414;" u2="&#x441;" k="43" />
    <hkern u1="&#x414;" u2="&#x440;" k="-43" />
    <hkern u1="&#x414;" u2="&#x43e;" k="-63" />
    <hkern u1="&#x414;" u2="&#x43c;" k="-20" />
    <hkern u1="&#x414;" u2="&#x43b;" k="-63" />
    <hkern u1="&#x414;" u2="&#x435;" k="-43" />
    <hkern u1="&#x414;" u2="&#x432;" k="-20" />
    <hkern u1="&#x414;" u2="&#x431;" k="-23" />
    <hkern u1="&#x414;" u2="&#x430;" k="-63" />
    <hkern u1="&#x414;" u2="&#x42d;" k="41" />
    <hkern u1="&#x414;" u2="&#x42a;" k="20" />
    <hkern u1="&#x414;" u2="&#x427;" k="23" />
    <hkern u1="&#x414;" u2="&#x424;" k="41" />
    <hkern u1="&#x414;" u2="&#x423;" k="23" />
    <hkern u1="&#x414;" u2="&#x422;" k="43" />
    <hkern u1="&#x414;" u2="&#x421;" k="43" />
    <hkern u1="&#x414;" u2="&#x420;" k="-43" />
    <hkern u1="&#x414;" u2="&#x41e;" k="43" />
    <hkern u1="&#x414;" u2="&#x41b;" k="-63" />
    <hkern u1="&#x414;" u2="&#x415;" k="-43" />
    <hkern u1="&#x414;" u2="&#x413;" k="-31" />
    <hkern u1="&#x414;" u2="&#x412;" k="-20" />
    <hkern u1="&#x414;" u2="&#x410;" k="-63" />
    <hkern u1="&#x414;" u2="&#x401;" k="-43" />
    <hkern u1="&#x415;" u2="&#x44f;" k="-43" />
    <hkern u1="&#x415;" u2="&#x44e;" k="-41" />
    <hkern u1="&#x415;" u2="&#x44a;" k="-23" />
    <hkern u1="&#x415;" u2="&#x446;" k="-23" />
    <hkern u1="&#x415;" u2="&#x445;" k="-23" />
    <hkern u1="&#x415;" u2="&#x442;" k="-43" />
    <hkern u1="&#x415;" u2="&#x441;" k="-43" />
    <hkern u1="&#x415;" u2="&#x43d;" k="-43" />
    <hkern u1="&#x415;" u2="&#x43b;" k="-43" />
    <hkern u1="&#x415;" u2="&#x436;" k="-63" />
    <hkern u1="&#x415;" u2="&#x434;" k="-63" />
    <hkern u1="&#x415;" u2="&#x42f;" k="-43" />
    <hkern u1="&#x415;" u2="&#x42e;" k="-41" />
    <hkern u1="&#x415;" u2="&#x42a;" k="-23" />
    <hkern u1="&#x415;" u2="&#x422;" k="-43" />
    <hkern u1="&#x415;" u2="&#x421;" k="-43" />
    <hkern u1="&#x415;" u2="&#x41b;" k="-43" />
    <hkern u1="&#x415;" u2="&#x416;" k="-63" />
    <hkern u1="&#x415;" u2="&#x414;" k="-63" />
    <hkern u1="&#x416;" u2="&#x451;" k="-66" />
    <hkern u1="&#x416;" u2="&#x443;" k="-43" />
    <hkern u1="&#x416;" u2="&#x40e;" k="-43" />
    <hkern u1="&#x416;" u2="&#x45e;" k="-43" />
    <hkern u1="&#x416;" u2="&#x45c;" k="-63" />
    <hkern u1="&#x416;" u2="&#x44f;" k="-23" />
    <hkern u1="&#x416;" u2="&#x44d;" k="20" />
    <hkern u1="&#x416;" u2="&#x44c;" k="-66" />
    <hkern u1="&#x416;" u2="&#x44b;" k="-63" />
    <hkern u1="&#x416;" u2="&#x449;" k="-43" />
    <hkern u1="&#x416;" u2="&#x448;" k="-43" />
    <hkern u1="&#x416;" u2="&#x446;" k="-43" />
    <hkern u1="&#x416;" u2="&#x444;" k="86" />
    <hkern u1="&#x416;" u2="&#x441;" k="43" />
    <hkern u1="&#x416;" u2="&#x440;" k="-66" />
    <hkern u1="&#x416;" u2="&#x43f;" k="20" />
    <hkern u1="&#x416;" u2="&#x43e;" k="-63" />
    <hkern u1="&#x416;" u2="&#x43c;" k="-43" />
    <hkern u1="&#x416;" u2="&#x43b;" k="-63" />
    <hkern u1="&#x416;" u2="&#x43a;" k="-63" />
    <hkern u1="&#x416;" u2="&#x436;" k="-63" />
    <hkern u1="&#x416;" u2="&#x435;" k="-66" />
    <hkern u1="&#x416;" u2="&#x434;" k="-63" />
    <hkern u1="&#x416;" u2="&#x433;" k="-43" />
    <hkern u1="&#x416;" u2="&#x432;" k="-41" />
    <hkern u1="&#x416;" u2="&#x431;" k="-63" />
    <hkern u1="&#x416;" u2="&#x430;" k="-63" />
    <hkern u1="&#x416;" u2="&#x42f;" k="-23" />
    <hkern u1="&#x416;" u2="&#x42d;" k="20" />
    <hkern u1="&#x416;" u2="&#x42c;" k="-66" />
    <hkern u1="&#x416;" u2="&#x42b;" k="-63" />
    <hkern u1="&#x416;" u2="&#x429;" k="-43" />
    <hkern u1="&#x416;" u2="&#x428;" k="-43" />
    <hkern u1="&#x416;" u2="&#x426;" k="-43" />
    <hkern u1="&#x416;" u2="&#x424;" k="86" />
    <hkern u1="&#x416;" u2="&#x423;" k="-43" />
    <hkern u1="&#x416;" u2="&#x421;" k="43" />
    <hkern u1="&#x416;" u2="&#x420;" k="-66" />
    <hkern u1="&#x416;" u2="&#x41f;" k="20" />
    <hkern u1="&#x416;" u2="&#x41e;" k="86" />
    <hkern u1="&#x416;" u2="&#x41b;" k="-63" />
    <hkern u1="&#x416;" u2="&#x414;" k="-63" />
    <hkern u1="&#x416;" u2="&#x413;" k="-43" />
    <hkern u1="&#x416;" u2="&#x412;" k="-41" />
    <hkern u1="&#x416;" u2="&#x410;" k="-63" />
    <hkern u1="&#x417;" u2="&#x451;" k="-43" />
    <hkern u1="&#x417;" u2="&#x443;" k="20" />
    <hkern u1="&#x417;" u2="&#x40e;" k="20" />
    <hkern u1="&#x417;" u2="&#x45f;" k="-41" />
    <hkern u1="&#x417;" u2="&#x45e;" k="20" />
    <hkern u1="&#x417;" u2="&#x45c;" k="-43" />
    <hkern u1="&#x417;" u2="&#x44f;" k="20" />
    <hkern u1="&#x417;" u2="&#x44e;" k="-41" />
    <hkern u1="&#x417;" u2="&#x44c;" k="-23" />
    <hkern u1="&#x417;" u2="&#x44a;" k="41" />
    <hkern u1="&#x417;" u2="&#x449;" k="-63" />
    <hkern u1="&#x417;" u2="&#x448;" k="-43" />
    <hkern u1="&#x417;" u2="&#x446;" k="-66" />
    <hkern u1="&#x417;" u2="&#x445;" k="43" />
    <hkern u1="&#x417;" u2="&#x444;" k="-23" />
    <hkern u1="&#x417;" u2="&#x442;" k="43" />
    <hkern u1="&#x417;" u2="&#x440;" k="-66" />
    <hkern u1="&#x417;" u2="&#x43f;" k="-43" />
    <hkern u1="&#x417;" u2="&#x43d;" k="-41" />
    <hkern u1="&#x417;" u2="&#x43c;" k="-63" />
    <hkern u1="&#x417;" u2="&#x43a;" k="-43" />
    <hkern u1="&#x417;" u2="&#x439;" k="-43" />
    <hkern u1="&#x417;" u2="&#x437;" k="-66" />
    <hkern u1="&#x417;" u2="&#x436;" k="43" />
    <hkern u1="&#x417;" u2="&#x435;" k="-43" />
    <hkern u1="&#x417;" u2="&#x434;" k="43" />
    <hkern u1="&#x417;" u2="&#x433;" k="-43" />
    <hkern u1="&#x417;" u2="&#x432;" k="-20" />
    <hkern u1="&#x417;" u2="&#x431;" k="-43" />
    <hkern u1="&#x417;" u2="&#x42e;" k="-41" />
    <hkern u1="&#x417;" u2="&#x42c;" k="-23" />
    <hkern u1="&#x417;" u2="&#x42a;" k="41" />
    <hkern u1="&#x417;" u2="&#x429;" k="-63" />
    <hkern u1="&#x417;" u2="&#x428;" k="-43" />
    <hkern u1="&#x417;" u2="&#x426;" k="-66" />
    <hkern u1="&#x417;" u2="&#x425;" k="43" />
    <hkern u1="&#x417;" u2="&#x423;" k="20" />
    <hkern u1="&#x417;" u2="&#x422;" k="16" />
    <hkern u1="&#x417;" u2="&#x420;" k="-66" />
    <hkern u1="&#x417;" u2="&#x41f;" k="-43" />
    <hkern u1="&#x417;" u2="&#x41d;" k="-41" />
    <hkern u1="&#x417;" u2="&#x41c;" k="-63" />
    <hkern u1="&#x417;" u2="&#x41a;" k="-43" />
    <hkern u1="&#x417;" u2="&#x419;" k="-43" />
    <hkern u1="&#x417;" u2="&#x417;" k="-66" />
    <hkern u1="&#x417;" u2="&#x416;" k="43" />
    <hkern u1="&#x417;" u2="&#x414;" k="43" />
    <hkern u1="&#x417;" u2="&#x413;" k="-43" />
    <hkern u1="&#x417;" u2="&#x412;" k="-20" />
    <hkern u1="&#x417;" u2="&#x40f;" k="-41" />
    <hkern u1="&#x418;" u2="&#x44e;" k="-23" />
    <hkern u1="&#x418;" u2="&#x444;" k="-20" />
    <hkern u1="&#x418;" u2="&#x434;" k="-43" />
    <hkern u1="&#x418;" u2="&#x432;" k="-23" />
    <hkern u1="&#x418;" u2="&#x431;" k="-43" />
    <hkern u1="&#x418;" u2="&#x42e;" k="-23" />
    <hkern u1="&#x418;" u2="&#x424;" k="-20" />
    <hkern u1="&#x418;" u2="&#x414;" k="-43" />
    <hkern u1="&#x418;" u2="&#x413;" k="-33" />
    <hkern u1="&#x418;" u2="&#x412;" k="-23" />
    <hkern u1="&#x418;" u2="&#x411;" k="-43" />
    <hkern u1="&#x419;" u2="&#x44e;" k="-20" />
    <hkern u1="&#x419;" u2="&#x444;" k="-23" />
    <hkern u1="&#x419;" u2="&#x437;" k="-43" />
    <hkern u1="&#x419;" u2="&#x434;" k="-43" />
    <hkern u1="&#x419;" u2="&#x432;" k="-23" />
    <hkern u1="&#x419;" u2="&#x431;" k="-20" />
    <hkern u1="&#x419;" u2="&#x42e;" k="-20" />
    <hkern u1="&#x419;" u2="&#x424;" k="-23" />
    <hkern u1="&#x419;" u2="&#x417;" k="-43" />
    <hkern u1="&#x419;" u2="&#x414;" k="-43" />
    <hkern u1="&#x419;" u2="&#x412;" k="-23" />
    <hkern u1="&#x419;" u2="&#x411;" k="-20" />
    <hkern u1="&#x41a;" u2="&#x44e;" k="-63" />
    <hkern u1="&#x41a;" u2="&#x44d;" k="86" />
    <hkern u1="&#x41a;" u2="&#x44a;" k="33" />
    <hkern u1="&#x41a;" u2="&#x447;" k="49" />
    <hkern u1="&#x41a;" u2="&#x445;" k="-33" />
    <hkern u1="&#x41a;" u2="&#x444;" k="160" />
    <hkern u1="&#x41a;" u2="&#x442;" k="20" />
    <hkern u1="&#x41a;" u2="&#x441;" k="94" />
    <hkern u1="&#x41a;" u2="&#x440;" k="-33" />
    <hkern u1="&#x41a;" u2="&#x43e;" k="106" />
    <hkern u1="&#x41a;" u2="&#x437;" k="33" />
    <hkern u1="&#x41a;" u2="&#x434;" k="-33" />
    <hkern u1="&#x41a;" u2="&#x432;" k="-33" />
    <hkern u1="&#x41a;" u2="&#x431;" k="-43" />
    <hkern u1="&#x41a;" u2="&#x42d;" k="113" />
    <hkern u1="&#x41a;" u2="&#x424;" k="96" />
    <hkern u1="&#x41a;" u2="&#x421;" k="113" />
    <hkern u1="&#x41a;" u2="&#x41e;" k="111" />
    <hkern u1="&#x41a;" u2="&#x417;" k="47" />
    <hkern u1="&#x41a;" u2="&#x416;" k="-33" />
    <hkern u1="&#x41b;" u2="&#x451;" k="-43" />
    <hkern u1="&#x41b;" u2="&#x443;" k="143" />
    <hkern u1="&#x41b;" u2="&#x40e;" k="143" />
    <hkern u1="&#x41b;" u2="&#x45e;" k="143" />
    <hkern u1="&#x41b;" u2="&#x44e;" k="-43" />
    <hkern u1="&#x41b;" u2="&#x44c;" k="-41" />
    <hkern u1="&#x41b;" u2="&#x44b;" k="-43" />
    <hkern u1="&#x41b;" u2="&#x44a;" k="43" />
    <hkern u1="&#x41b;" u2="&#x449;" k="-20" />
    <hkern u1="&#x41b;" u2="&#x448;" k="-23" />
    <hkern u1="&#x41b;" u2="&#x447;" k="145" />
    <hkern u1="&#x41b;" u2="&#x446;" k="-43" />
    <hkern u1="&#x41b;" u2="&#x445;" k="-43" />
    <hkern u1="&#x41b;" u2="&#x444;" k="63" />
    <hkern u1="&#x41b;" u2="&#x442;" k="160" />
    <hkern u1="&#x41b;" u2="&#x441;" k="20" />
    <hkern u1="&#x41b;" u2="&#x440;" k="-43" />
    <hkern u1="&#x41b;" u2="&#x43f;" k="-43" />
    <hkern u1="&#x41b;" u2="&#x43e;" k="20" />
    <hkern u1="&#x41b;" u2="&#x43d;" k="-43" />
    <hkern u1="&#x41b;" u2="&#x43b;" k="-43" />
    <hkern u1="&#x41b;" u2="&#x437;" k="-20" />
    <hkern u1="&#x41b;" u2="&#x436;" k="-43" />
    <hkern u1="&#x41b;" u2="&#x435;" k="-43" />
    <hkern u1="&#x41b;" u2="&#x434;" k="-63" />
    <hkern u1="&#x41b;" u2="&#x433;" k="-43" />
    <hkern u1="&#x41b;" u2="&#x432;" k="-43" />
    <hkern u1="&#x41b;" u2="&#x431;" k="-20" />
    <hkern u1="&#x41b;" u2="&#x42f;" k="-33" />
    <hkern u1="&#x41b;" u2="&#x42e;" k="-31" />
    <hkern u1="&#x41b;" u2="&#x42b;" k="-47" />
    <hkern u1="&#x41b;" u2="&#x42a;" k="47" />
    <hkern u1="&#x41b;" u2="&#x428;" k="-33" />
    <hkern u1="&#x41b;" u2="&#x427;" k="111" />
    <hkern u1="&#x41b;" u2="&#x426;" k="-47" />
    <hkern u1="&#x41b;" u2="&#x425;" k="-49" />
    <hkern u1="&#x41b;" u2="&#x423;" k="143" />
    <hkern u1="&#x41b;" u2="&#x422;" k="127" />
    <hkern u1="&#x41b;" u2="&#x421;" k="63" />
    <hkern u1="&#x41b;" u2="&#x41e;" k="47" />
    <hkern u1="&#x41b;" u2="&#x41b;" k="-31" />
    <hkern u1="&#x41b;" u2="&#x416;" k="-33" />
    <hkern u1="&#x41b;" u2="&#x414;" k="-47" />
    <hkern u1="&#x41b;" u2="&#x413;" k="-43" />
    <hkern u1="&#x41b;" u2="&#x412;" k="-33" />
    <hkern u1="&#x41b;" u2="&#x411;" k="-33" />
    <hkern u1="&#x41c;" u2="&#x413;" k="-16" />
    <hkern u1="&#x41d;" u2="&#x432;" k="-20" />
    <hkern u1="&#x41d;" u2="&#x413;" k="-33" />
    <hkern u1="&#x41d;" u2="&#x412;" k="-20" />
    <hkern u1="&#x41e;" u2="&#x451;" k="-41" />
    <hkern u1="&#x41e;" u2="&#x443;" k="63" />
    <hkern u1="&#x41e;" u2="&#x40e;" k="80" />
    <hkern u1="&#x41e;" u2="&#x45e;" k="63" />
    <hkern u1="&#x41e;" u2="&#x45c;" k="-23" />
    <hkern u1="&#x41e;" u2="&#x44f;" k="23" />
    <hkern u1="&#x41e;" u2="&#x44a;" k="63" />
    <hkern u1="&#x41e;" u2="&#x448;" k="-41" />
    <hkern u1="&#x41e;" u2="&#x445;" k="63" />
    <hkern u1="&#x41e;" u2="&#x442;" k="84" />
    <hkern u1="&#x41e;" u2="&#x441;" k="23" />
    <hkern u1="&#x41e;" u2="&#x43f;" k="-41" />
    <hkern u1="&#x41e;" u2="&#x43e;" k="63" />
    <hkern u1="&#x41e;" u2="&#x43b;" k="63" />
    <hkern u1="&#x41e;" u2="&#x43a;" k="-23" />
    <hkern u1="&#x41e;" u2="&#x437;" k="43" />
    <hkern u1="&#x41e;" u2="&#x436;" k="106" />
    <hkern u1="&#x41e;" u2="&#x435;" k="-41" />
    <hkern u1="&#x41e;" u2="&#x434;" k="63" />
    <hkern u1="&#x41e;" u2="&#x430;" k="63" />
    <hkern u1="&#x41e;" u2="&#x42f;" k="23" />
    <hkern u1="&#x41e;" u2="&#x42a;" k="63" />
    <hkern u1="&#x41e;" u2="&#x425;" k="63" />
    <hkern u1="&#x41e;" u2="&#x423;" k="80" />
    <hkern u1="&#x41e;" u2="&#x422;" k="84" />
    <hkern u1="&#x41e;" u2="&#x421;" k="23" />
    <hkern u1="&#x41e;" u2="&#x41f;" k="-41" />
    <hkern u1="&#x41e;" u2="&#x41b;" k="63" />
    <hkern u1="&#x41e;" u2="&#x41a;" k="-23" />
    <hkern u1="&#x41e;" u2="&#x416;" k="106" />
    <hkern u1="&#x41e;" u2="&#x414;" k="63" />
    <hkern u1="&#x41e;" u2="&#x410;" k="63" />
    <hkern u1="&#x41f;" u2="&#x443;" k="-43" />
    <hkern u1="&#x41f;" u2="&#x40e;" k="-43" />
    <hkern u1="&#x41f;" u2="&#x45e;" k="-43" />
    <hkern u1="&#x41f;" u2="&#x45c;" k="-41" />
    <hkern u1="&#x41f;" u2="&#x44e;" k="-20" />
    <hkern u1="&#x41f;" u2="&#x447;" k="-20" />
    <hkern u1="&#x41f;" u2="&#x445;" k="-41" />
    <hkern u1="&#x41f;" u2="&#x444;" k="-43" />
    <hkern u1="&#x41f;" u2="&#x441;" k="-43" />
    <hkern u1="&#x41f;" u2="&#x440;" k="-41" />
    <hkern u1="&#x41f;" u2="&#x43e;" k="-43" />
    <hkern u1="&#x41f;" u2="&#x43b;" k="-43" />
    <hkern u1="&#x41f;" u2="&#x43a;" k="-41" />
    <hkern u1="&#x41f;" u2="&#x437;" k="-41" />
    <hkern u1="&#x41f;" u2="&#x436;" k="43" />
    <hkern u1="&#x41f;" u2="&#x433;" k="-43" />
    <hkern u1="&#x41f;" u2="&#x432;" k="-20" />
    <hkern u1="&#x41f;" u2="&#x430;" k="-43" />
    <hkern u1="&#x41f;" u2="&#x42e;" k="-20" />
    <hkern u1="&#x41f;" u2="&#x427;" k="-20" />
    <hkern u1="&#x41f;" u2="&#x425;" k="-41" />
    <hkern u1="&#x41f;" u2="&#x424;" k="-43" />
    <hkern u1="&#x41f;" u2="&#x423;" k="-43" />
    <hkern u1="&#x41f;" u2="&#x421;" k="-43" />
    <hkern u1="&#x41f;" u2="&#x420;" k="-41" />
    <hkern u1="&#x41f;" u2="&#x41e;" k="-43" />
    <hkern u1="&#x41f;" u2="&#x41b;" k="-43" />
    <hkern u1="&#x41f;" u2="&#x41a;" k="-41" />
    <hkern u1="&#x41f;" u2="&#x417;" k="-41" />
    <hkern u1="&#x41f;" u2="&#x416;" k="43" />
    <hkern u1="&#x41f;" u2="&#x413;" k="-43" />
    <hkern u1="&#x41f;" u2="&#x412;" k="-20" />
    <hkern u1="&#x41f;" u2="&#x410;" k="-43" />
    <hkern u1="&#x420;" u2="&#x451;" k="-43" />
    <hkern u1="&#x420;" u2="&#x40e;" k="16" />
    <hkern u1="&#x420;" u2="&#x44e;" k="-43" />
    <hkern u1="&#x420;" u2="&#x44b;" k="-43" />
    <hkern u1="&#x420;" u2="&#x448;" k="-20" />
    <hkern u1="&#x420;" u2="&#x446;" k="-43" />
    <hkern u1="&#x420;" u2="&#x445;" k="43" />
    <hkern u1="&#x420;" u2="&#x444;" k="-23" />
    <hkern u1="&#x420;" u2="&#x442;" k="43" />
    <hkern u1="&#x420;" u2="&#x43e;" k="86" />
    <hkern u1="&#x420;" u2="&#x43b;" k="86" />
    <hkern u1="&#x420;" u2="&#x436;" k="63" />
    <hkern u1="&#x420;" u2="&#x435;" k="-43" />
    <hkern u1="&#x420;" u2="&#x434;" k="106" />
    <hkern u1="&#x420;" u2="&#x433;" k="-41" />
    <hkern u1="&#x420;" u2="&#x432;" k="-43" />
    <hkern u1="&#x420;" u2="&#x431;" k="-43" />
    <hkern u1="&#x420;" u2="&#x430;" k="86" />
    <hkern u1="&#x420;" u2="&#x428;" k="-20" />
    <hkern u1="&#x420;" u2="&#x425;" k="43" />
    <hkern u1="&#x420;" u2="&#x424;" k="-23" />
    <hkern u1="&#x420;" u2="&#x423;" k="16" />
    <hkern u1="&#x420;" u2="&#x41b;" k="129" />
    <hkern u1="&#x420;" u2="&#x416;" k="63" />
    <hkern u1="&#x420;" u2="&#x414;" k="106" />
    <hkern u1="&#x420;" u2="&#x413;" k="-41" />
    <hkern u1="&#x420;" u2="&#x412;" k="-43" />
    <hkern u1="&#x420;" u2="&#x410;" k="129" />
    <hkern u1="&#x421;" u2="&#x451;" k="-43" />
    <hkern u1="&#x421;" u2="&#x443;" k="23" />
    <hkern u1="&#x421;" u2="&#x40e;" k="23" />
    <hkern u1="&#x421;" u2="&#x45e;" k="23" />
    <hkern u1="&#x421;" u2="&#x44c;" k="-41" />
    <hkern u1="&#x421;" u2="&#x44a;" k="43" />
    <hkern u1="&#x421;" u2="&#x449;" k="-43" />
    <hkern u1="&#x421;" u2="&#x448;" k="-43" />
    <hkern u1="&#x421;" u2="&#x447;" k="-41" />
    <hkern u1="&#x421;" u2="&#x445;" k="63" />
    <hkern u1="&#x421;" u2="&#x442;" k="63" />
    <hkern u1="&#x421;" u2="&#x440;" k="-43" />
    <hkern u1="&#x421;" u2="&#x43f;" k="-66" />
    <hkern u1="&#x421;" u2="&#x43d;" k="-23" />
    <hkern u1="&#x421;" u2="&#x43b;" k="43" />
    <hkern u1="&#x421;" u2="&#x437;" k="-23" />
    <hkern u1="&#x421;" u2="&#x436;" k="84" />
    <hkern u1="&#x421;" u2="&#x435;" k="-43" />
    <hkern u1="&#x421;" u2="&#x434;" k="43" />
    <hkern u1="&#x421;" u2="&#x433;" k="-43" />
    <hkern u1="&#x421;" u2="&#x429;" k="-43" />
    <hkern u1="&#x421;" u2="&#x428;" k="-43" />
    <hkern u1="&#x421;" u2="&#x427;" k="-41" />
    <hkern u1="&#x421;" u2="&#x425;" k="63" />
    <hkern u1="&#x421;" u2="&#x423;" k="23" />
    <hkern u1="&#x421;" u2="&#x422;" k="63" />
    <hkern u1="&#x421;" u2="&#x420;" k="-43" />
    <hkern u1="&#x421;" u2="&#x41f;" k="-66" />
    <hkern u1="&#x421;" u2="&#x41b;" k="33" />
    <hkern u1="&#x421;" u2="&#x416;" k="84" />
    <hkern u1="&#x421;" u2="&#x414;" k="43" />
    <hkern u1="&#x421;" u2="&#x413;" k="-43" />
    <hkern u1="&#x421;" u2="&#x410;" k="33" />
    <hkern u1="&#x422;" u2="&#x451;" k="-43" />
    <hkern u1="&#x422;" u2="&#x443;" k="-63" />
    <hkern u1="&#x422;" u2="&#x40e;" k="-16" />
    <hkern u1="&#x422;" u2="&#x45e;" k="-63" />
    <hkern u1="&#x422;" u2="&#x44a;" k="-63" />
    <hkern u1="&#x422;" u2="&#x449;" k="-20" />
    <hkern u1="&#x422;" u2="&#x445;" k="-43" />
    <hkern u1="&#x422;" u2="&#x444;" k="106" />
    <hkern u1="&#x422;" u2="&#x442;" k="-86" />
    <hkern u1="&#x422;" u2="&#x441;" k="63" />
    <hkern u1="&#x422;" u2="&#x440;" k="-43" />
    <hkern u1="&#x422;" u2="&#x43f;" k="-43" />
    <hkern u1="&#x422;" u2="&#x43e;" k="127" />
    <hkern u1="&#x422;" u2="&#x43c;" k="-23" />
    <hkern u1="&#x422;" u2="&#x43b;" k="127" />
    <hkern u1="&#x422;" u2="&#x435;" k="-43" />
    <hkern u1="&#x422;" u2="&#x434;" k="170" />
    <hkern u1="&#x422;" u2="&#x432;" k="-23" />
    <hkern u1="&#x422;" u2="&#x430;" k="127" />
    <hkern u1="&#x422;" u2="&#x42f;" k="49" />
    <hkern u1="&#x422;" u2="&#x42e;" k="-33" />
    <hkern u1="&#x422;" u2="&#x42a;" k="-16" />
    <hkern u1="&#x422;" u2="&#x424;" k="106" />
    <hkern u1="&#x422;" u2="&#x423;" k="-16" />
    <hkern u1="&#x422;" u2="&#x422;" k="-14" />
    <hkern u1="&#x422;" u2="&#x421;" k="63" />
    <hkern u1="&#x422;" u2="&#x420;" k="-43" />
    <hkern u1="&#x422;" u2="&#x41e;" k="41" />
    <hkern u1="&#x422;" u2="&#x41b;" k="160" />
    <hkern u1="&#x422;" u2="&#x414;" k="170" />
    <hkern u1="&#x422;" u2="&#x412;" k="-23" />
    <hkern u1="&#x422;" u2="&#x410;" k="160" />
    <hkern u1="&#x423;" u2="&#x44f;" k="96" />
    <hkern u1="&#x423;" u2="&#x44e;" k="-43" />
    <hkern u1="&#x423;" u2="&#x44d;" k="80" />
    <hkern u1="&#x423;" u2="&#x44b;" k="-43" />
    <hkern u1="&#x423;" u2="&#x44a;" k="-43" />
    <hkern u1="&#x423;" u2="&#x447;" k="-20" />
    <hkern u1="&#x423;" u2="&#x445;" k="-43" />
    <hkern u1="&#x423;" u2="&#x444;" k="113" />
    <hkern u1="&#x423;" u2="&#x442;" k="-49" />
    <hkern u1="&#x423;" u2="&#x441;" k="63" />
    <hkern u1="&#x423;" u2="&#x440;" k="-43" />
    <hkern u1="&#x423;" u2="&#x43f;" k="-43" />
    <hkern u1="&#x423;" u2="&#x43e;" k="240" />
    <hkern u1="&#x423;" u2="&#x43d;" k="-43" />
    <hkern u1="&#x423;" u2="&#x43c;" k="-43" />
    <hkern u1="&#x423;" u2="&#x43b;" k="240" />
    <hkern u1="&#x423;" u2="&#x434;" k="129" />
    <hkern u1="&#x423;" u2="&#x433;" k="-43" />
    <hkern u1="&#x423;" u2="&#x432;" k="-20" />
    <hkern u1="&#x423;" u2="&#x431;" k="-43" />
    <hkern u1="&#x423;" u2="&#x430;" k="240" />
    <hkern u1="&#x423;" u2="&#x42f;" k="63" />
    <hkern u1="&#x423;" u2="&#x42e;" k="-33" />
    <hkern u1="&#x423;" u2="&#x42d;" k="47" />
    <hkern u1="&#x423;" u2="&#x424;" k="80" />
    <hkern u1="&#x423;" u2="&#x422;" k="-14" />
    <hkern u1="&#x423;" u2="&#x421;" k="66" />
    <hkern u1="&#x423;" u2="&#x420;" k="-47" />
    <hkern u1="&#x423;" u2="&#x41e;" k="80" />
    <hkern u1="&#x423;" u2="&#x41b;" k="240" />
    <hkern u1="&#x423;" u2="&#x413;" k="-43" />
    <hkern u1="&#x423;" u2="&#x412;" k="-31" />
    <hkern u1="&#x423;" u2="&#x410;" k="240" />
    <hkern u1="&#x424;" u2="&#x451;" k="-23" />
    <hkern u1="&#x424;" u2="&#x443;" k="43" />
    <hkern u1="&#x424;" u2="&#x40e;" k="43" />
    <hkern u1="&#x424;" u2="&#x45e;" k="43" />
    <hkern u1="&#x424;" u2="&#x45c;" k="-20" />
    <hkern u1="&#x424;" u2="&#x44e;" k="-43" />
    <hkern u1="&#x424;" u2="&#x44c;" k="-63" />
    <hkern u1="&#x424;" u2="&#x44b;" k="-63" />
    <hkern u1="&#x424;" u2="&#x44a;" k="43" />
    <hkern u1="&#x424;" u2="&#x449;" k="-41" />
    <hkern u1="&#x424;" u2="&#x448;" k="-41" />
    <hkern u1="&#x424;" u2="&#x447;" k="-20" />
    <hkern u1="&#x424;" u2="&#x446;" k="-43" />
    <hkern u1="&#x424;" u2="&#x445;" k="66" />
    <hkern u1="&#x424;" u2="&#x444;" k="-23" />
    <hkern u1="&#x424;" u2="&#x442;" k="84" />
    <hkern u1="&#x424;" u2="&#x441;" k="-43" />
    <hkern u1="&#x424;" u2="&#x440;" k="-20" />
    <hkern u1="&#x424;" u2="&#x43f;" k="-63" />
    <hkern u1="&#x424;" u2="&#x43e;" k="23" />
    <hkern u1="&#x424;" u2="&#x43b;" k="23" />
    <hkern u1="&#x424;" u2="&#x43a;" k="-20" />
    <hkern u1="&#x424;" u2="&#x438;" k="-20" />
    <hkern u1="&#x424;" u2="&#x436;" k="63" />
    <hkern u1="&#x424;" u2="&#x435;" k="-23" />
    <hkern u1="&#x424;" u2="&#x434;" k="66" />
    <hkern u1="&#x424;" u2="&#x433;" k="-20" />
    <hkern u1="&#x424;" u2="&#x432;" k="-23" />
    <hkern u1="&#x424;" u2="&#x430;" k="23" />
    <hkern u1="&#x424;" u2="&#x42e;" k="-43" />
    <hkern u1="&#x424;" u2="&#x42c;" k="-63" />
    <hkern u1="&#x424;" u2="&#x42b;" k="-63" />
    <hkern u1="&#x424;" u2="&#x42a;" k="43" />
    <hkern u1="&#x424;" u2="&#x429;" k="-41" />
    <hkern u1="&#x424;" u2="&#x428;" k="-41" />
    <hkern u1="&#x424;" u2="&#x426;" k="-43" />
    <hkern u1="&#x424;" u2="&#x425;" k="66" />
    <hkern u1="&#x424;" u2="&#x424;" k="-23" />
    <hkern u1="&#x424;" u2="&#x423;" k="43" />
    <hkern u1="&#x424;" u2="&#x422;" k="84" />
    <hkern u1="&#x424;" u2="&#x421;" k="-43" />
    <hkern u1="&#x424;" u2="&#x420;" k="-20" />
    <hkern u1="&#x424;" u2="&#x41f;" k="-63" />
    <hkern u1="&#x424;" u2="&#x41e;" k="-23" />
    <hkern u1="&#x424;" u2="&#x41b;" k="23" />
    <hkern u1="&#x424;" u2="&#x41a;" k="-20" />
    <hkern u1="&#x424;" u2="&#x416;" k="63" />
    <hkern u1="&#x424;" u2="&#x415;" k="-23" />
    <hkern u1="&#x424;" u2="&#x414;" k="66" />
    <hkern u1="&#x424;" u2="&#x413;" k="-20" />
    <hkern u1="&#x424;" u2="&#x412;" k="-23" />
    <hkern u1="&#x424;" u2="&#x410;" k="23" />
    <hkern u1="&#x424;" u2="&#x401;" k="-23" />
    <hkern u1="&#x425;" u2="&#x451;" k="-43" />
    <hkern u1="&#x425;" u2="&#x443;" k="-63" />
    <hkern u1="&#x425;" u2="&#x40e;" k="-33" />
    <hkern u1="&#x425;" u2="&#x45e;" k="-63" />
    <hkern u1="&#x425;" u2="&#x44e;" k="-43" />
    <hkern u1="&#x425;" u2="&#x44d;" k="43" />
    <hkern u1="&#x425;" u2="&#x44c;" k="-43" />
    <hkern u1="&#x425;" u2="&#x44b;" k="-41" />
    <hkern u1="&#x425;" u2="&#x44a;" k="-43" />
    <hkern u1="&#x425;" u2="&#x449;" k="-63" />
    <hkern u1="&#x425;" u2="&#x448;" k="-43" />
    <hkern u1="&#x425;" u2="&#x447;" k="-20" />
    <hkern u1="&#x425;" u2="&#x446;" k="-63" />
    <hkern u1="&#x425;" u2="&#x445;" k="-84" />
    <hkern u1="&#x425;" u2="&#x444;" k="41" />
    <hkern u1="&#x425;" u2="&#x442;" k="-43" />
    <hkern u1="&#x425;" u2="&#x441;" k="43" />
    <hkern u1="&#x425;" u2="&#x440;" k="-63" />
    <hkern u1="&#x425;" u2="&#x43f;" k="-43" />
    <hkern u1="&#x425;" u2="&#x43e;" k="-43" />
    <hkern u1="&#x425;" u2="&#x43c;" k="-43" />
    <hkern u1="&#x425;" u2="&#x43b;" k="-43" />
    <hkern u1="&#x425;" u2="&#x436;" k="-63" />
    <hkern u1="&#x425;" u2="&#x435;" k="-43" />
    <hkern u1="&#x425;" u2="&#x434;" k="-63" />
    <hkern u1="&#x425;" u2="&#x433;" k="-43" />
    <hkern u1="&#x425;" u2="&#x432;" k="-43" />
    <hkern u1="&#x425;" u2="&#x430;" k="-43" />
    <hkern u1="&#x425;" u2="&#x42e;" k="-43" />
    <hkern u1="&#x425;" u2="&#x42d;" k="43" />
    <hkern u1="&#x425;" u2="&#x42c;" k="-43" />
    <hkern u1="&#x425;" u2="&#x42b;" k="-41" />
    <hkern u1="&#x425;" u2="&#x42a;" k="-43" />
    <hkern u1="&#x425;" u2="&#x429;" k="-63" />
    <hkern u1="&#x425;" u2="&#x428;" k="-43" />
    <hkern u1="&#x425;" u2="&#x426;" k="-63" />
    <hkern u1="&#x425;" u2="&#x425;" k="-84" />
    <hkern u1="&#x425;" u2="&#x424;" k="41" />
    <hkern u1="&#x425;" u2="&#x423;" k="-33" />
    <hkern u1="&#x425;" u2="&#x422;" k="-31" />
    <hkern u1="&#x425;" u2="&#x421;" k="43" />
    <hkern u1="&#x425;" u2="&#x41f;" k="-43" />
    <hkern u1="&#x425;" u2="&#x41e;" k="43" />
    <hkern u1="&#x425;" u2="&#x41b;" k="-43" />
    <hkern u1="&#x425;" u2="&#x416;" k="-63" />
    <hkern u1="&#x425;" u2="&#x414;" k="-63" />
    <hkern u1="&#x425;" u2="&#x413;" k="-43" />
    <hkern u1="&#x425;" u2="&#x412;" k="-43" />
    <hkern u1="&#x425;" u2="&#x410;" k="-43" />
    <hkern u1="&#x426;" u2="&#x44a;" k="43" />
    <hkern u1="&#x426;" u2="&#x445;" k="-43" />
    <hkern u1="&#x426;" u2="&#x444;" k="41" />
    <hkern u1="&#x426;" u2="&#x43e;" k="-86" />
    <hkern u1="&#x426;" u2="&#x43b;" k="-86" />
    <hkern u1="&#x426;" u2="&#x436;" k="-43" />
    <hkern u1="&#x426;" u2="&#x434;" k="-84" />
    <hkern u1="&#x426;" u2="&#x430;" k="-86" />
    <hkern u1="&#x426;" u2="&#x42a;" k="43" />
    <hkern u1="&#x426;" u2="&#x425;" k="-43" />
    <hkern u1="&#x426;" u2="&#x424;" k="41" />
    <hkern u1="&#x426;" u2="&#x422;" k="31" />
    <hkern u1="&#x426;" u2="&#x41e;" k="20" />
    <hkern u1="&#x426;" u2="&#x41b;" k="-47" />
    <hkern u1="&#x426;" u2="&#x416;" k="-43" />
    <hkern u1="&#x426;" u2="&#x414;" k="-84" />
    <hkern u1="&#x426;" u2="&#x413;" k="-33" />
    <hkern u1="&#x426;" u2="&#x410;" k="-47" />
    <hkern u1="&#x427;" u2="&#x451;" k="-20" />
    <hkern u1="&#x427;" u2="&#x443;" k="-23" />
    <hkern u1="&#x427;" u2="&#x40e;" k="-23" />
    <hkern u1="&#x427;" u2="&#x45e;" k="-23" />
    <hkern u1="&#x427;" u2="&#x45c;" k="-20" />
    <hkern u1="&#x427;" u2="&#x44f;" k="-20" />
    <hkern u1="&#x427;" u2="&#x44e;" k="-20" />
    <hkern u1="&#x427;" u2="&#x44c;" k="-23" />
    <hkern u1="&#x427;" u2="&#x44b;" k="-20" />
    <hkern u1="&#x427;" u2="&#x44a;" k="-43" />
    <hkern u1="&#x427;" u2="&#x449;" k="-20" />
    <hkern u1="&#x427;" u2="&#x448;" k="-23" />
    <hkern u1="&#x427;" u2="&#x446;" k="-20" />
    <hkern u1="&#x427;" u2="&#x445;" k="-43" />
    <hkern u1="&#x427;" u2="&#x43e;" k="-43" />
    <hkern u1="&#x427;" u2="&#x43b;" k="-43" />
    <hkern u1="&#x427;" u2="&#x43a;" k="-20" />
    <hkern u1="&#x427;" u2="&#x437;" k="-43" />
    <hkern u1="&#x427;" u2="&#x435;" k="-20" />
    <hkern u1="&#x427;" u2="&#x434;" k="-43" />
    <hkern u1="&#x427;" u2="&#x433;" k="-20" />
    <hkern u1="&#x427;" u2="&#x432;" k="-43" />
    <hkern u1="&#x427;" u2="&#x431;" k="-20" />
    <hkern u1="&#x427;" u2="&#x430;" k="-43" />
    <hkern u1="&#x427;" u2="&#x42f;" k="-20" />
    <hkern u1="&#x427;" u2="&#x42e;" k="-20" />
    <hkern u1="&#x427;" u2="&#x42c;" k="-23" />
    <hkern u1="&#x427;" u2="&#x42b;" k="-20" />
    <hkern u1="&#x427;" u2="&#x42a;" k="-43" />
    <hkern u1="&#x427;" u2="&#x429;" k="-20" />
    <hkern u1="&#x427;" u2="&#x428;" k="-23" />
    <hkern u1="&#x427;" u2="&#x426;" k="-20" />
    <hkern u1="&#x427;" u2="&#x425;" k="-43" />
    <hkern u1="&#x427;" u2="&#x423;" k="-23" />
    <hkern u1="&#x427;" u2="&#x422;" k="-33" />
    <hkern u1="&#x427;" u2="&#x41b;" k="-43" />
    <hkern u1="&#x427;" u2="&#x41a;" k="-20" />
    <hkern u1="&#x427;" u2="&#x414;" k="-43" />
    <hkern u1="&#x427;" u2="&#x413;" k="-20" />
    <hkern u1="&#x427;" u2="&#x412;" k="-43" />
    <hkern u1="&#x427;" u2="&#x410;" k="-43" />
    <hkern u1="&#x428;" u2="&#x443;" k="-23" />
    <hkern u1="&#x428;" u2="&#x40e;" k="-23" />
    <hkern u1="&#x428;" u2="&#x45e;" k="-23" />
    <hkern u1="&#x428;" u2="&#x45c;" k="-23" />
    <hkern u1="&#x428;" u2="&#x44f;" k="43" />
    <hkern u1="&#x428;" u2="&#x44a;" k="-20" />
    <hkern u1="&#x428;" u2="&#x445;" k="-20" />
    <hkern u1="&#x428;" u2="&#x43e;" k="-43" />
    <hkern u1="&#x428;" u2="&#x43b;" k="-43" />
    <hkern u1="&#x428;" u2="&#x43a;" k="-23" />
    <hkern u1="&#x428;" u2="&#x430;" k="-43" />
    <hkern u1="&#x428;" u2="&#x42f;" k="43" />
    <hkern u1="&#x428;" u2="&#x42a;" k="-20" />
    <hkern u1="&#x428;" u2="&#x425;" k="-20" />
    <hkern u1="&#x428;" u2="&#x423;" k="-23" />
    <hkern u1="&#x428;" u2="&#x41b;" k="-43" />
    <hkern u1="&#x428;" u2="&#x41a;" k="-23" />
    <hkern u1="&#x428;" u2="&#x410;" k="-43" />
    <hkern u1="&#x429;" u2="&#x45c;" k="-23" />
    <hkern u1="&#x429;" u2="&#x44a;" k="43" />
    <hkern u1="&#x429;" u2="&#x447;" k="43" />
    <hkern u1="&#x429;" u2="&#x442;" k="63" />
    <hkern u1="&#x429;" u2="&#x43e;" k="-66" />
    <hkern u1="&#x429;" u2="&#x43b;" k="-66" />
    <hkern u1="&#x429;" u2="&#x43a;" k="-23" />
    <hkern u1="&#x429;" u2="&#x436;" k="-43" />
    <hkern u1="&#x429;" u2="&#x434;" k="-63" />
    <hkern u1="&#x429;" u2="&#x430;" k="-66" />
    <hkern u1="&#x429;" u2="&#x42a;" k="43" />
    <hkern u1="&#x429;" u2="&#x427;" k="43" />
    <hkern u1="&#x429;" u2="&#x422;" k="63" />
    <hkern u1="&#x429;" u2="&#x41e;" k="43" />
    <hkern u1="&#x429;" u2="&#x41b;" k="-66" />
    <hkern u1="&#x429;" u2="&#x41a;" k="-23" />
    <hkern u1="&#x429;" u2="&#x416;" k="-43" />
    <hkern u1="&#x429;" u2="&#x414;" k="-63" />
    <hkern u1="&#x429;" u2="&#x410;" k="-66" />
    <hkern u1="&#x42a;" u2="&#x443;" k="86" />
    <hkern u1="&#x42a;" u2="&#x40e;" k="86" />
    <hkern u1="&#x42a;" u2="&#x45e;" k="86" />
    <hkern u1="&#x42a;" u2="&#x45c;" k="20" />
    <hkern u1="&#x42a;" u2="&#x44a;" k="63" />
    <hkern u1="&#x42a;" u2="&#x447;" k="43" />
    <hkern u1="&#x42a;" u2="&#x445;" k="20" />
    <hkern u1="&#x42a;" u2="&#x442;" k="172" />
    <hkern u1="&#x42a;" u2="&#x441;" k="-43" />
    <hkern u1="&#x42a;" u2="&#x440;" k="-20" />
    <hkern u1="&#x42a;" u2="&#x43a;" k="20" />
    <hkern u1="&#x42a;" u2="&#x437;" k="-41" />
    <hkern u1="&#x42a;" u2="&#x436;" k="43" />
    <hkern u1="&#x42a;" u2="&#x434;" k="20" />
    <hkern u1="&#x42a;" u2="&#x433;" k="-20" />
    <hkern u1="&#x42a;" u2="&#x431;" k="-23" />
    <hkern u1="&#x42a;" u2="&#x42a;" k="63" />
    <hkern u1="&#x42a;" u2="&#x427;" k="43" />
    <hkern u1="&#x42a;" u2="&#x425;" k="20" />
    <hkern u1="&#x42a;" u2="&#x423;" k="86" />
    <hkern u1="&#x42a;" u2="&#x422;" k="172" />
    <hkern u1="&#x42a;" u2="&#x420;" k="-20" />
    <hkern u1="&#x42a;" u2="&#x41a;" k="20" />
    <hkern u1="&#x42a;" u2="&#x417;" k="-41" />
    <hkern u1="&#x42a;" u2="&#x416;" k="43" />
    <hkern u1="&#x42a;" u2="&#x413;" k="-20" />
    <hkern u1="&#x42b;" u2="&#x413;" k="-31" />
    <hkern u1="&#x42c;" u2="&#x445;" k="63" />
    <hkern u1="&#x42c;" u2="&#x442;" k="150" />
    <hkern u1="&#x42c;" u2="&#x436;" k="43" />
    <hkern u1="&#x42c;" u2="&#x432;" k="41" />
    <hkern u1="&#x42c;" u2="&#x425;" k="63" />
    <hkern u1="&#x42c;" u2="&#x422;" k="150" />
    <hkern u1="&#x42c;" u2="&#x416;" k="43" />
    <hkern u1="&#x42c;" u2="&#x412;" k="41" />
    <hkern u1="&#x42d;" u2="&#x3b1;" k="63" />
    <hkern u1="&#x42d;" u2="&#x391;" k="63" />
    <hkern u1="&#x42d;" u2="&#x104;" k="63" />
    <hkern u1="&#x42d;" u2="&#x102;" k="63" />
    <hkern u1="&#x42d;" u2="&#x100;" k="63" />
    <hkern u1="&#x42d;" u2="&#xc5;" k="63" />
    <hkern u1="&#x42d;" u2="&#xc4;" k="63" />
    <hkern u1="&#x42d;" u2="&#xc3;" k="63" />
    <hkern u1="&#x42d;" u2="&#xc2;" k="63" />
    <hkern u1="&#x42d;" u2="&#xc1;" k="63" />
    <hkern u1="&#x42d;" u2="&#xc0;" k="63" />
    <hkern u1="&#x42d;" u2="&#x41b;" k="31" />
    <hkern u1="&#x42d;" u2="&#x410;" k="31" />
    <hkern u1="&#x42d;" u2="&#x3bb;" k="63" />
    <hkern u1="&#x42d;" u2="&#x3b4;" k="63" />
    <hkern u1="&#x42d;" u2="&#x3ac;" k="63" />
    <hkern u1="&#x42d;" u2="&#x39b;" k="63" />
    <hkern u1="&#x42d;" u2="&#x394;" k="63" />
    <hkern u1="&#x42d;" u2="&#x21b;" k="129" />
    <hkern u1="&#x42d;" u2="&#x21a;" k="129" />
    <hkern u1="&#x42d;" u2="&#x178;" k="84" />
    <hkern u1="&#x42d;" u2="&#xc6;" k="63" />
    <hkern u1="&#x42d;" u2="Z" k="84" />
    <hkern u1="&#x42d;" u2="Y" k="84" />
    <hkern u1="&#x42d;" u2="W" k="43" />
    <hkern u1="&#x42d;" u2="V" k="43" />
    <hkern u1="&#x42d;" u2="T" k="129" />
    <hkern u1="&#x42d;" u2="J" k="63" />
    <hkern u1="&#x42d;" u2="A" k="63" />
    <hkern u1="&#x42e;" u2="&#x40e;" k="80" />
    <hkern u1="&#x42e;" u2="&#x423;" k="80" />
    <hkern u1="&#x42e;" u2="&#x41b;" k="47" />
    <hkern u1="&#x42e;" u2="&#x413;" k="-47" />
    <hkern u1="&#x42e;" u2="&#x410;" k="47" />
    <hkern u1="&#x42f;" u2="&#x40e;" k="-16" />
    <hkern u1="&#x42f;" u2="&#x447;" k="-20" />
    <hkern u1="&#x42f;" u2="&#x433;" k="-20" />
    <hkern u1="&#x42f;" u2="&#x427;" k="-20" />
    <hkern u1="&#x42f;" u2="&#x423;" k="-16" />
    <hkern u1="&#x42f;" u2="&#x422;" k="-49" />
    <hkern u1="&#x42f;" u2="&#x41b;" k="-49" />
    <hkern u1="&#x42f;" u2="&#x41a;" k="-31" />
    <hkern u1="&#x42f;" u2="&#x413;" k="-49" />
    <hkern u1="&#x42f;" u2="&#x410;" k="-49" />
    <hkern u1="&#x430;" u2="&#x451;" k="-43" />
    <hkern u1="&#x430;" u2="&#x443;" k="143" />
    <hkern u1="&#x430;" u2="&#x40e;" k="143" />
    <hkern u1="&#x430;" u2="&#x45e;" k="143" />
    <hkern u1="&#x430;" u2="&#x44e;" k="-43" />
    <hkern u1="&#x430;" u2="&#x44c;" k="-41" />
    <hkern u1="&#x430;" u2="&#x44b;" k="-43" />
    <hkern u1="&#x430;" u2="&#x44a;" k="43" />
    <hkern u1="&#x430;" u2="&#x449;" k="-20" />
    <hkern u1="&#x430;" u2="&#x448;" k="-23" />
    <hkern u1="&#x430;" u2="&#x447;" k="145" />
    <hkern u1="&#x430;" u2="&#x446;" k="-43" />
    <hkern u1="&#x430;" u2="&#x445;" k="-43" />
    <hkern u1="&#x430;" u2="&#x444;" k="63" />
    <hkern u1="&#x430;" u2="&#x442;" k="160" />
    <hkern u1="&#x430;" u2="&#x441;" k="20" />
    <hkern u1="&#x430;" u2="&#x440;" k="-43" />
    <hkern u1="&#x430;" u2="&#x43f;" k="-43" />
    <hkern u1="&#x430;" u2="&#x43e;" k="-63" />
    <hkern u1="&#x430;" u2="&#x43d;" k="-43" />
    <hkern u1="&#x430;" u2="&#x43b;" k="-63" />
    <hkern u1="&#x430;" u2="&#x437;" k="-20" />
    <hkern u1="&#x430;" u2="&#x436;" k="-43" />
    <hkern u1="&#x430;" u2="&#x435;" k="-43" />
    <hkern u1="&#x430;" u2="&#x434;" k="-63" />
    <hkern u1="&#x430;" u2="&#x433;" k="-43" />
    <hkern u1="&#x430;" u2="&#x432;" k="-43" />
    <hkern u1="&#x430;" u2="&#x431;" k="-20" />
    <hkern u1="&#x430;" u2="&#x430;" k="-63" />
    <hkern u1="&#x430;" u2="&#x423;" k="143" />
    <hkern u1="&#x430;" u2="&#x41b;" k="-63" />
    <hkern u1="&#x430;" u2="&#x413;" k="-43" />
    <hkern u1="&#x430;" u2="&#x410;" k="-63" />
    <hkern u1="&#x431;" u2="&#x443;" k="63" />
    <hkern u1="&#x431;" u2="&#x40e;" k="63" />
    <hkern u1="&#x431;" u2="&#x45e;" k="63" />
    <hkern u1="&#x431;" u2="&#x45c;" k="-33" />
    <hkern u1="&#x431;" u2="&#x44f;" k="43" />
    <hkern u1="&#x431;" u2="&#x44a;" k="41" />
    <hkern u1="&#x431;" u2="&#x448;" k="-20" />
    <hkern u1="&#x431;" u2="&#x447;" k="43" />
    <hkern u1="&#x431;" u2="&#x445;" k="66" />
    <hkern u1="&#x431;" u2="&#x442;" k="63" />
    <hkern u1="&#x431;" u2="&#x440;" k="-20" />
    <hkern u1="&#x431;" u2="&#x43f;" k="-20" />
    <hkern u1="&#x431;" u2="&#x43b;" k="23" />
    <hkern u1="&#x431;" u2="&#x43a;" k="-33" />
    <hkern u1="&#x431;" u2="&#x437;" k="-23" />
    <hkern u1="&#x431;" u2="&#x436;" k="63" />
    <hkern u1="&#x431;" u2="&#x434;" k="23" />
    <hkern u1="&#x431;" u2="&#x433;" k="-33" />
    <hkern u1="&#x431;" u2="&#x423;" k="63" />
    <hkern u1="&#x431;" u2="&#x41a;" k="-33" />
    <hkern u1="&#x431;" u2="&#x413;" k="-33" />
    <hkern u1="&#x432;" u2="&#x443;" k="86" />
    <hkern u1="&#x432;" u2="&#x40e;" k="86" />
    <hkern u1="&#x432;" u2="&#x45e;" k="86" />
    <hkern u1="&#x432;" u2="&#x45c;" k="-33" />
    <hkern u1="&#x432;" u2="&#x44f;" k="43" />
    <hkern u1="&#x432;" u2="&#x44a;" k="63" />
    <hkern u1="&#x432;" u2="&#x447;" k="23" />
    <hkern u1="&#x432;" u2="&#x445;" k="43" />
    <hkern u1="&#x432;" u2="&#x442;" k="80" />
    <hkern u1="&#x432;" u2="&#x43b;" k="23" />
    <hkern u1="&#x432;" u2="&#x43a;" k="-33" />
    <hkern u1="&#x432;" u2="&#x437;" k="-43" />
    <hkern u1="&#x432;" u2="&#x436;" k="66" />
    <hkern u1="&#x432;" u2="&#x434;" k="43" />
    <hkern u1="&#x432;" u2="&#x433;" k="-47" />
    <hkern u1="&#x432;" u2="&#x423;" k="86" />
    <hkern u1="&#x432;" u2="&#x41a;" k="-33" />
    <hkern u1="&#x432;" u2="&#x413;" k="-47" />
    <hkern u1="&#x433;" u2="&#x44f;" k="31" />
    <hkern u1="&#x433;" u2="&#x44e;" k="-31" />
    <hkern u1="&#x433;" u2="&#x44d;" k="41" />
    <hkern u1="&#x433;" u2="&#x44a;" k="-43" />
    <hkern u1="&#x433;" u2="&#x444;" k="129" />
    <hkern u1="&#x433;" u2="&#x442;" k="-16" />
    <hkern u1="&#x433;" u2="&#x441;" k="63" />
    <hkern u1="&#x433;" u2="&#x43e;" k="80" />
    <hkern u1="&#x433;" u2="&#x43b;" k="209" />
    <hkern u1="&#x433;" u2="&#x436;" k="47" />
    <hkern u1="&#x433;" u2="&#x434;" k="170" />
    <hkern u1="&#x433;" u2="&#x432;" k="-16" />
    <hkern u1="&#x433;" u2="&#x430;" k="209" />
    <hkern u1="&#x433;" u2="&#x41b;" k="209" />
    <hkern u1="&#x433;" u2="&#x410;" k="209" />
    <hkern u1="&#x434;" u2="&#x443;" k="43" />
    <hkern u1="&#x434;" u2="&#x40e;" k="43" />
    <hkern u1="&#x434;" u2="&#x45e;" k="43" />
    <hkern u1="&#x434;" u2="&#x45c;" k="-49" />
    <hkern u1="&#x434;" u2="&#x44b;" k="-43" />
    <hkern u1="&#x434;" u2="&#x442;" k="47" />
    <hkern u1="&#x434;" u2="&#x441;" k="20" />
    <hkern u1="&#x434;" u2="&#x43e;" k="-33" />
    <hkern u1="&#x434;" u2="&#x43c;" k="-20" />
    <hkern u1="&#x434;" u2="&#x43b;" k="-33" />
    <hkern u1="&#x434;" u2="&#x43a;" k="-49" />
    <hkern u1="&#x434;" u2="&#x432;" k="-20" />
    <hkern u1="&#x434;" u2="&#x431;" k="-23" />
    <hkern u1="&#x434;" u2="&#x430;" k="-33" />
    <hkern u1="&#x434;" u2="&#x423;" k="43" />
    <hkern u1="&#x434;" u2="&#x41b;" k="-33" />
    <hkern u1="&#x434;" u2="&#x41a;" k="-49" />
    <hkern u1="&#x434;" u2="&#x410;" k="-33" />
    <hkern u1="&#x435;" u2="&#x44a;" k="-43" />
    <hkern u1="&#x435;" u2="&#x446;" k="-23" />
    <hkern u1="&#x435;" u2="&#x445;" k="-23" />
    <hkern u1="&#x435;" u2="&#x43d;" k="-43" />
    <hkern u1="&#x435;" u2="&#x43a;" k="-33" />
    <hkern u1="&#x435;" u2="&#x434;" k="-20" />
    <hkern u1="&#x435;" u2="&#x41a;" k="-33" />
    <hkern u1="&#x436;" u2="&#x451;" k="-66" />
    <hkern u1="&#x436;" u2="&#x44d;" k="43" />
    <hkern u1="&#x436;" u2="&#x444;" k="63" />
    <hkern u1="&#x436;" u2="&#x441;" k="63" />
    <hkern u1="&#x436;" u2="&#x43e;" k="-63" />
    <hkern u1="&#x436;" u2="&#x43c;" k="-43" />
    <hkern u1="&#x436;" u2="&#x43b;" k="-63" />
    <hkern u1="&#x436;" u2="&#x436;" k="-63" />
    <hkern u1="&#x436;" u2="&#x435;" k="-66" />
    <hkern u1="&#x436;" u2="&#x434;" k="-86" />
    <hkern u1="&#x436;" u2="&#x433;" k="-43" />
    <hkern u1="&#x436;" u2="&#x432;" k="-20" />
    <hkern u1="&#x436;" u2="&#x431;" k="-63" />
    <hkern u1="&#x436;" u2="&#x430;" k="-63" />
    <hkern u1="&#x436;" u2="&#x41b;" k="-63" />
    <hkern u1="&#x436;" u2="&#x413;" k="-43" />
    <hkern u1="&#x436;" u2="&#x410;" k="-63" />
    <hkern u1="&#x437;" u2="&#x451;" k="-43" />
    <hkern u1="&#x437;" u2="&#x443;" k="47" />
    <hkern u1="&#x437;" u2="&#x40e;" k="47" />
    <hkern u1="&#x437;" u2="&#x45e;" k="47" />
    <hkern u1="&#x437;" u2="&#x44f;" k="20" />
    <hkern u1="&#x437;" u2="&#x44c;" k="-43" />
    <hkern u1="&#x437;" u2="&#x44a;" k="43" />
    <hkern u1="&#x437;" u2="&#x449;" k="-43" />
    <hkern u1="&#x437;" u2="&#x448;" k="-43" />
    <hkern u1="&#x437;" u2="&#x446;" k="-43" />
    <hkern u1="&#x437;" u2="&#x444;" k="-23" />
    <hkern u1="&#x437;" u2="&#x442;" k="49" />
    <hkern u1="&#x437;" u2="&#x440;" k="-41" />
    <hkern u1="&#x437;" u2="&#x43f;" k="-43" />
    <hkern u1="&#x437;" u2="&#x43c;" k="-41" />
    <hkern u1="&#x437;" u2="&#x437;" k="-20" />
    <hkern u1="&#x437;" u2="&#x436;" k="43" />
    <hkern u1="&#x437;" u2="&#x435;" k="-43" />
    <hkern u1="&#x437;" u2="&#x431;" k="-43" />
    <hkern u1="&#x437;" u2="&#x423;" k="47" />
    <hkern u1="&#x438;" u2="&#x443;" k="-47" />
    <hkern u1="&#x438;" u2="&#x40e;" k="-47" />
    <hkern u1="&#x438;" u2="&#x45e;" k="-47" />
    <hkern u1="&#x438;" u2="&#x423;" k="-47" />
    <hkern u1="&#x43a;" u2="&#x44e;" k="-63" />
    <hkern u1="&#x43a;" u2="&#x44d;" k="86" />
    <hkern u1="&#x43a;" u2="&#x44a;" k="33" />
    <hkern u1="&#x43a;" u2="&#x447;" k="49" />
    <hkern u1="&#x43a;" u2="&#x445;" k="-33" />
    <hkern u1="&#x43a;" u2="&#x444;" k="160" />
    <hkern u1="&#x43a;" u2="&#x442;" k="20" />
    <hkern u1="&#x43a;" u2="&#x441;" k="94" />
    <hkern u1="&#x43a;" u2="&#x440;" k="-33" />
    <hkern u1="&#x43a;" u2="&#x43e;" k="106" />
    <hkern u1="&#x43a;" u2="&#x437;" k="33" />
    <hkern u1="&#x43a;" u2="&#x434;" k="-33" />
    <hkern u1="&#x43a;" u2="&#x432;" k="-33" />
    <hkern u1="&#x43a;" u2="&#x431;" k="-43" />
    <hkern u1="&#x43b;" u2="&#x451;" k="-43" />
    <hkern u1="&#x43b;" u2="&#x443;" k="143" />
    <hkern u1="&#x43b;" u2="&#x40e;" k="143" />
    <hkern u1="&#x43b;" u2="&#x45e;" k="143" />
    <hkern u1="&#x43b;" u2="&#x44e;" k="-43" />
    <hkern u1="&#x43b;" u2="&#x44c;" k="-41" />
    <hkern u1="&#x43b;" u2="&#x44b;" k="-43" />
    <hkern u1="&#x43b;" u2="&#x44a;" k="43" />
    <hkern u1="&#x43b;" u2="&#x449;" k="-20" />
    <hkern u1="&#x43b;" u2="&#x448;" k="-23" />
    <hkern u1="&#x43b;" u2="&#x447;" k="145" />
    <hkern u1="&#x43b;" u2="&#x446;" k="-43" />
    <hkern u1="&#x43b;" u2="&#x445;" k="-43" />
    <hkern u1="&#x43b;" u2="&#x444;" k="63" />
    <hkern u1="&#x43b;" u2="&#x442;" k="160" />
    <hkern u1="&#x43b;" u2="&#x441;" k="20" />
    <hkern u1="&#x43b;" u2="&#x440;" k="-43" />
    <hkern u1="&#x43b;" u2="&#x43f;" k="-43" />
    <hkern u1="&#x43b;" u2="&#x43e;" k="-63" />
    <hkern u1="&#x43b;" u2="&#x43d;" k="-43" />
    <hkern u1="&#x43b;" u2="&#x43b;" k="-63" />
    <hkern u1="&#x43b;" u2="&#x437;" k="-20" />
    <hkern u1="&#x43b;" u2="&#x436;" k="-43" />
    <hkern u1="&#x43b;" u2="&#x435;" k="-43" />
    <hkern u1="&#x43b;" u2="&#x434;" k="-63" />
    <hkern u1="&#x43b;" u2="&#x433;" k="-43" />
    <hkern u1="&#x43b;" u2="&#x432;" k="-43" />
    <hkern u1="&#x43b;" u2="&#x431;" k="-20" />
    <hkern u1="&#x43b;" u2="&#x423;" k="143" />
    <hkern u1="&#x43b;" u2="&#x41b;" k="-63" />
    <hkern u1="&#x43b;" u2="&#x413;" k="-43" />
    <hkern u1="&#x43b;" u2="&#x410;" k="-63" />
    <hkern u1="&#x43d;" u2="&#x443;" k="-49" />
    <hkern u1="&#x43d;" u2="&#x40e;" k="-49" />
    <hkern u1="&#x43d;" u2="&#x45e;" k="-49" />
    <hkern u1="&#x43d;" u2="&#x45c;" k="-31" />
    <hkern u1="&#x43d;" u2="&#x43a;" k="-31" />
    <hkern u1="&#x43d;" u2="&#x433;" k="-31" />
    <hkern u1="&#x43d;" u2="&#x423;" k="-49" />
    <hkern u1="&#x43d;" u2="&#x41a;" k="-31" />
    <hkern u1="&#x43d;" u2="&#x413;" k="-31" />
    <hkern u1="&#x43e;" u2="&#x451;" k="-41" />
    <hkern u1="&#x43e;" u2="&#x443;" k="63" />
    <hkern u1="&#x43e;" u2="&#x40e;" k="63" />
    <hkern u1="&#x43e;" u2="&#x45e;" k="63" />
    <hkern u1="&#x43e;" u2="&#x44f;" k="43" />
    <hkern u1="&#x43e;" u2="&#x44a;" k="66" />
    <hkern u1="&#x43e;" u2="&#x448;" k="-41" />
    <hkern u1="&#x43e;" u2="&#x445;" k="84" />
    <hkern u1="&#x43e;" u2="&#x442;" k="86" />
    <hkern u1="&#x43e;" u2="&#x43b;" k="63" />
    <hkern u1="&#x43e;" u2="&#x437;" k="43" />
    <hkern u1="&#x43e;" u2="&#x436;" k="109" />
    <hkern u1="&#x43e;" u2="&#x435;" k="-41" />
    <hkern u1="&#x43e;" u2="&#x434;" k="86" />
    <hkern u1="&#x43e;" u2="&#x430;" k="63" />
    <hkern u1="&#x43e;" u2="&#x423;" k="63" />
    <hkern u1="&#x43e;" u2="&#x41b;" k="63" />
    <hkern u1="&#x43e;" u2="&#x410;" k="63" />
    <hkern u1="&#x43f;" u2="&#x443;" k="-33" />
    <hkern u1="&#x43f;" u2="&#x40e;" k="-33" />
    <hkern u1="&#x43f;" u2="&#x45e;" k="-33" />
    <hkern u1="&#x43f;" u2="&#x45c;" k="-16" />
    <hkern u1="&#x43f;" u2="&#x43e;" k="-47" />
    <hkern u1="&#x43f;" u2="&#x43b;" k="-47" />
    <hkern u1="&#x43f;" u2="&#x43a;" k="-16" />
    <hkern u1="&#x43f;" u2="&#x433;" k="-31" />
    <hkern u1="&#x43f;" u2="&#x430;" k="-47" />
    <hkern u1="&#x43f;" u2="&#x423;" k="-33" />
    <hkern u1="&#x43f;" u2="&#x41b;" k="-47" />
    <hkern u1="&#x43f;" u2="&#x41a;" k="-16" />
    <hkern u1="&#x43f;" u2="&#x413;" k="-31" />
    <hkern u1="&#x43f;" u2="&#x410;" k="-47" />
    <hkern u1="&#x440;" u2="&#x451;" k="-43" />
    <hkern u1="&#x440;" u2="&#x443;" k="31" />
    <hkern u1="&#x440;" u2="&#x40e;" k="31" />
    <hkern u1="&#x440;" u2="&#x45e;" k="31" />
    <hkern u1="&#x440;" u2="&#x44e;" k="-43" />
    <hkern u1="&#x440;" u2="&#x44b;" k="-43" />
    <hkern u1="&#x440;" u2="&#x446;" k="-43" />
    <hkern u1="&#x440;" u2="&#x445;" k="43" />
    <hkern u1="&#x440;" u2="&#x43e;" k="129" />
    <hkern u1="&#x440;" u2="&#x43b;" k="129" />
    <hkern u1="&#x440;" u2="&#x436;" k="106" />
    <hkern u1="&#x440;" u2="&#x435;" k="-43" />
    <hkern u1="&#x440;" u2="&#x434;" k="106" />
    <hkern u1="&#x440;" u2="&#x431;" k="-43" />
    <hkern u1="&#x440;" u2="&#x430;" k="129" />
    <hkern u1="&#x440;" u2="&#x423;" k="31" />
    <hkern u1="&#x440;" u2="&#x41b;" k="129" />
    <hkern u1="&#x440;" u2="&#x410;" k="129" />
    <hkern u1="&#x441;" u2="&#x451;" k="-43" />
    <hkern u1="&#x441;" u2="&#x443;" k="43" />
    <hkern u1="&#x441;" u2="&#x40e;" k="43" />
    <hkern u1="&#x441;" u2="&#x45e;" k="43" />
    <hkern u1="&#x441;" u2="&#x44c;" k="-41" />
    <hkern u1="&#x441;" u2="&#x44a;" k="43" />
    <hkern u1="&#x441;" u2="&#x449;" k="-43" />
    <hkern u1="&#x441;" u2="&#x448;" k="-43" />
    <hkern u1="&#x441;" u2="&#x447;" k="-41" />
    <hkern u1="&#x441;" u2="&#x445;" k="66" />
    <hkern u1="&#x441;" u2="&#x442;" k="63" />
    <hkern u1="&#x441;" u2="&#x440;" k="-20" />
    <hkern u1="&#x441;" u2="&#x43f;" k="-43" />
    <hkern u1="&#x441;" u2="&#x43e;" k="49" />
    <hkern u1="&#x441;" u2="&#x43d;" k="-23" />
    <hkern u1="&#x441;" u2="&#x43b;" k="49" />
    <hkern u1="&#x441;" u2="&#x437;" k="-23" />
    <hkern u1="&#x441;" u2="&#x436;" k="43" />
    <hkern u1="&#x441;" u2="&#x435;" k="-43" />
    <hkern u1="&#x441;" u2="&#x434;" k="43" />
    <hkern u1="&#x441;" u2="&#x433;" k="-43" />
    <hkern u1="&#x441;" u2="&#x430;" k="49" />
    <hkern u1="&#x441;" u2="&#x423;" k="43" />
    <hkern u1="&#x441;" u2="&#x41b;" k="49" />
    <hkern u1="&#x441;" u2="&#x413;" k="-43" />
    <hkern u1="&#x441;" u2="&#x410;" k="49" />
    <hkern u1="&#x442;" u2="&#x451;" k="-43" />
    <hkern u1="&#x442;" u2="&#x443;" k="-16" />
    <hkern u1="&#x442;" u2="&#x40e;" k="-16" />
    <hkern u1="&#x442;" u2="&#x45e;" k="-16" />
    <hkern u1="&#x442;" u2="&#x45c;" k="16" />
    <hkern u1="&#x442;" u2="&#x44f;" k="49" />
    <hkern u1="&#x442;" u2="&#x44b;" k="-31" />
    <hkern u1="&#x442;" u2="&#x44a;" k="-16" />
    <hkern u1="&#x442;" u2="&#x445;" k="-41" />
    <hkern u1="&#x442;" u2="&#x444;" k="80" />
    <hkern u1="&#x442;" u2="&#x442;" k="-43" />
    <hkern u1="&#x442;" u2="&#x441;" k="63" />
    <hkern u1="&#x442;" u2="&#x43f;" k="-43" />
    <hkern u1="&#x442;" u2="&#x43e;" k="193" />
    <hkern u1="&#x442;" u2="&#x43c;" k="-23" />
    <hkern u1="&#x442;" u2="&#x43b;" k="193" />
    <hkern u1="&#x442;" u2="&#x43a;" k="16" />
    <hkern u1="&#x442;" u2="&#x436;" k="33" />
    <hkern u1="&#x442;" u2="&#x435;" k="-43" />
    <hkern u1="&#x442;" u2="&#x434;" k="147" />
    <hkern u1="&#x442;" u2="&#x430;" k="193" />
    <hkern u1="&#x442;" u2="&#x423;" k="-16" />
    <hkern u1="&#x442;" u2="&#x41b;" k="193" />
    <hkern u1="&#x442;" u2="&#x41a;" k="16" />
    <hkern u1="&#x442;" u2="&#x410;" k="193" />
    <hkern u1="&#x443;" u2="&#x44f;" k="96" />
    <hkern u1="&#x443;" u2="&#x44e;" k="-43" />
    <hkern u1="&#x443;" u2="&#x44d;" k="80" />
    <hkern u1="&#x443;" u2="&#x44b;" k="-43" />
    <hkern u1="&#x443;" u2="&#x44a;" k="-43" />
    <hkern u1="&#x443;" u2="&#x447;" k="-20" />
    <hkern u1="&#x443;" u2="&#x445;" k="-43" />
    <hkern u1="&#x443;" u2="&#x444;" k="113" />
    <hkern u1="&#x443;" u2="&#x442;" k="-49" />
    <hkern u1="&#x443;" u2="&#x441;" k="63" />
    <hkern u1="&#x443;" u2="&#x440;" k="-43" />
    <hkern u1="&#x443;" u2="&#x43f;" k="-43" />
    <hkern u1="&#x443;" u2="&#x43e;" k="240" />
    <hkern u1="&#x443;" u2="&#x43d;" k="-43" />
    <hkern u1="&#x443;" u2="&#x43c;" k="-43" />
    <hkern u1="&#x443;" u2="&#x43b;" k="240" />
    <hkern u1="&#x443;" u2="&#x434;" k="129" />
    <hkern u1="&#x443;" u2="&#x433;" k="-43" />
    <hkern u1="&#x443;" u2="&#x432;" k="-20" />
    <hkern u1="&#x443;" u2="&#x431;" k="-43" />
    <hkern u1="&#x443;" u2="&#x430;" k="240" />
    <hkern u1="&#x443;" u2="&#x41b;" k="240" />
    <hkern u1="&#x443;" u2="&#x413;" k="-43" />
    <hkern u1="&#x443;" u2="&#x410;" k="240" />
    <hkern u1="&#x444;" u2="&#x451;" k="-63" />
    <hkern u1="&#x444;" u2="&#x443;" k="43" />
    <hkern u1="&#x444;" u2="&#x40e;" k="43" />
    <hkern u1="&#x444;" u2="&#x45e;" k="43" />
    <hkern u1="&#x444;" u2="&#x44a;" k="41" />
    <hkern u1="&#x444;" u2="&#x449;" k="-43" />
    <hkern u1="&#x444;" u2="&#x448;" k="-43" />
    <hkern u1="&#x444;" u2="&#x447;" k="-20" />
    <hkern u1="&#x444;" u2="&#x445;" k="63" />
    <hkern u1="&#x444;" u2="&#x442;" k="86" />
    <hkern u1="&#x444;" u2="&#x441;" k="-43" />
    <hkern u1="&#x444;" u2="&#x440;" k="-41" />
    <hkern u1="&#x444;" u2="&#x43e;" k="47" />
    <hkern u1="&#x444;" u2="&#x43b;" k="47" />
    <hkern u1="&#x444;" u2="&#x438;" k="-20" />
    <hkern u1="&#x444;" u2="&#x436;" k="66" />
    <hkern u1="&#x444;" u2="&#x435;" k="-63" />
    <hkern u1="&#x444;" u2="&#x434;" k="43" />
    <hkern u1="&#x444;" u2="&#x433;" k="-43" />
    <hkern u1="&#x444;" u2="&#x432;" k="-43" />
    <hkern u1="&#x444;" u2="&#x431;" k="-43" />
    <hkern u1="&#x444;" u2="&#x430;" k="47" />
    <hkern u1="&#x444;" u2="&#x423;" k="43" />
    <hkern u1="&#x444;" u2="&#x41b;" k="47" />
    <hkern u1="&#x444;" u2="&#x413;" k="-43" />
    <hkern u1="&#x444;" u2="&#x410;" k="47" />
    <hkern u1="&#x445;" u2="&#x451;" k="-43" />
    <hkern u1="&#x445;" u2="&#x443;" k="-63" />
    <hkern u1="&#x445;" u2="&#x40e;" k="-63" />
    <hkern u1="&#x445;" u2="&#x45e;" k="-63" />
    <hkern u1="&#x445;" u2="&#x44e;" k="-66" />
    <hkern u1="&#x445;" u2="&#x447;" k="-20" />
    <hkern u1="&#x445;" u2="&#x445;" k="-63" />
    <hkern u1="&#x445;" u2="&#x444;" k="43" />
    <hkern u1="&#x445;" u2="&#x442;" k="-43" />
    <hkern u1="&#x445;" u2="&#x441;" k="43" />
    <hkern u1="&#x445;" u2="&#x440;" k="-63" />
    <hkern u1="&#x445;" u2="&#x43f;" k="-63" />
    <hkern u1="&#x445;" u2="&#x43e;" k="-41" />
    <hkern u1="&#x445;" u2="&#x43c;" k="-43" />
    <hkern u1="&#x445;" u2="&#x43b;" k="-41" />
    <hkern u1="&#x445;" u2="&#x436;" k="-43" />
    <hkern u1="&#x445;" u2="&#x435;" k="-43" />
    <hkern u1="&#x445;" u2="&#x433;" k="-43" />
    <hkern u1="&#x445;" u2="&#x432;" k="-43" />
    <hkern u1="&#x445;" u2="&#x430;" k="-41" />
    <hkern u1="&#x445;" u2="&#x423;" k="-63" />
    <hkern u1="&#x445;" u2="&#x41b;" k="-41" />
    <hkern u1="&#x445;" u2="&#x413;" k="-43" />
    <hkern u1="&#x445;" u2="&#x410;" k="-41" />
    <hkern u1="&#x446;" u2="&#x443;" k="31" />
    <hkern u1="&#x446;" u2="&#x40e;" k="31" />
    <hkern u1="&#x446;" u2="&#x45e;" k="31" />
    <hkern u1="&#x446;" u2="&#x43e;" k="-47" />
    <hkern u1="&#x446;" u2="&#x43b;" k="-47" />
    <hkern u1="&#x446;" u2="&#x433;" k="-14" />
    <hkern u1="&#x446;" u2="&#x430;" k="-47" />
    <hkern u1="&#x446;" u2="&#x423;" k="31" />
    <hkern u1="&#x446;" u2="&#x41b;" k="-47" />
    <hkern u1="&#x446;" u2="&#x413;" k="-14" />
    <hkern u1="&#x446;" u2="&#x410;" k="-47" />
    <hkern u1="&#x447;" u2="&#x451;" k="-20" />
    <hkern u1="&#x447;" u2="&#x44e;" k="-20" />
    <hkern u1="&#x447;" u2="&#x44b;" k="-43" />
    <hkern u1="&#x447;" u2="&#x43e;" k="-49" />
    <hkern u1="&#x447;" u2="&#x43b;" k="-49" />
    <hkern u1="&#x447;" u2="&#x437;" k="-43" />
    <hkern u1="&#x447;" u2="&#x435;" k="-20" />
    <hkern u1="&#x447;" u2="&#x433;" k="-20" />
    <hkern u1="&#x447;" u2="&#x432;" k="-20" />
    <hkern u1="&#x447;" u2="&#x431;" k="-20" />
    <hkern u1="&#x447;" u2="&#x430;" k="-49" />
    <hkern u1="&#x447;" u2="&#x41b;" k="-49" />
    <hkern u1="&#x447;" u2="&#x413;" k="-20" />
    <hkern u1="&#x447;" u2="&#x410;" k="-49" />
    <hkern u1="&#x449;" u2="&#x43e;" k="-47" />
    <hkern u1="&#x449;" u2="&#x43b;" k="-47" />
    <hkern u1="&#x449;" u2="&#x430;" k="-47" />
    <hkern u1="&#x449;" u2="&#x41b;" k="-47" />
    <hkern u1="&#x449;" u2="&#x410;" k="-47" />
    <hkern u1="&#x44a;" u2="&#x443;" k="160" />
    <hkern u1="&#x44a;" u2="&#x40e;" k="160" />
    <hkern u1="&#x44a;" u2="&#x45e;" k="160" />
    <hkern u1="&#x44a;" u2="&#x447;" k="43" />
    <hkern u1="&#x44a;" u2="&#x442;" k="113" />
    <hkern u1="&#x44a;" u2="&#x441;" k="-43" />
    <hkern u1="&#x44a;" u2="&#x436;" k="43" />
    <hkern u1="&#x44a;" u2="&#x434;" k="20" />
    <hkern u1="&#x44a;" u2="&#x431;" k="-23" />
    <hkern u1="&#x44a;" u2="&#x423;" k="160" />
    <hkern u1="&#x44b;" u2="&#x433;" k="-31" />
    <hkern u1="&#x44b;" u2="&#x413;" k="-31" />
    <hkern u1="&#x44c;" u2="&#x442;" k="127" />
    <hkern u1="&#x44d;" u2="&#x3b1;" k="63" />
    <hkern u1="&#x44d;" u2="&#x391;" k="63" />
    <hkern u1="&#x44d;" u2="&#x104;" k="63" />
    <hkern u1="&#x44d;" u2="&#x102;" k="63" />
    <hkern u1="&#x44d;" u2="&#x100;" k="63" />
    <hkern u1="&#x44d;" u2="&#xc5;" k="63" />
    <hkern u1="&#x44d;" u2="&#xc4;" k="63" />
    <hkern u1="&#x44d;" u2="&#xc3;" k="63" />
    <hkern u1="&#x44d;" u2="&#xc2;" k="63" />
    <hkern u1="&#x44d;" u2="&#xc1;" k="63" />
    <hkern u1="&#x44d;" u2="&#xc0;" k="63" />
    <hkern u1="&#x44d;" u2="&#x442;" k="49" />
    <hkern u1="&#x44d;" u2="&#x43e;" k="31" />
    <hkern u1="&#x44d;" u2="&#x43b;" k="31" />
    <hkern u1="&#x44d;" u2="&#x430;" k="31" />
    <hkern u1="&#x44d;" u2="&#x41b;" k="31" />
    <hkern u1="&#x44d;" u2="&#x410;" k="31" />
    <hkern u1="&#x44d;" u2="&#x3bb;" k="63" />
    <hkern u1="&#x44d;" u2="&#x3b4;" k="63" />
    <hkern u1="&#x44d;" u2="&#x3ac;" k="63" />
    <hkern u1="&#x44d;" u2="&#x39b;" k="63" />
    <hkern u1="&#x44d;" u2="&#x394;" k="63" />
    <hkern u1="&#x44d;" u2="&#x21b;" k="129" />
    <hkern u1="&#x44d;" u2="&#x21a;" k="129" />
    <hkern u1="&#x44d;" u2="&#x178;" k="84" />
    <hkern u1="&#x44d;" u2="&#xc6;" k="63" />
    <hkern u1="&#x44d;" u2="Z" k="84" />
    <hkern u1="&#x44d;" u2="Y" k="84" />
    <hkern u1="&#x44d;" u2="W" k="43" />
    <hkern u1="&#x44d;" u2="V" k="43" />
    <hkern u1="&#x44d;" u2="T" k="129" />
    <hkern u1="&#x44d;" u2="J" k="63" />
    <hkern u1="&#x44d;" u2="A" k="63" />
    <hkern u1="&#x44e;" u2="&#x443;" k="96" />
    <hkern u1="&#x44e;" u2="&#x40e;" k="96" />
    <hkern u1="&#x44e;" u2="&#x45e;" k="96" />
    <hkern u1="&#x44e;" u2="&#x442;" k="63" />
    <hkern u1="&#x44e;" u2="&#x43e;" k="63" />
    <hkern u1="&#x44e;" u2="&#x43b;" k="63" />
    <hkern u1="&#x44e;" u2="&#x433;" k="-33" />
    <hkern u1="&#x44e;" u2="&#x430;" k="63" />
    <hkern u1="&#x44e;" u2="&#x423;" k="96" />
    <hkern u1="&#x44e;" u2="&#x41b;" k="63" />
    <hkern u1="&#x44e;" u2="&#x413;" k="-33" />
    <hkern u1="&#x44e;" u2="&#x410;" k="63" />
    <hkern u1="&#x44f;" u2="&#x443;" k="-47" />
    <hkern u1="&#x44f;" u2="&#x40e;" k="-47" />
    <hkern u1="&#x44f;" u2="&#x45e;" k="-47" />
    <hkern u1="&#x44f;" u2="&#x45c;" k="-33" />
    <hkern u1="&#x44f;" u2="&#x442;" k="-49" />
    <hkern u1="&#x44f;" u2="&#x43e;" k="-33" />
    <hkern u1="&#x44f;" u2="&#x43b;" k="-33" />
    <hkern u1="&#x44f;" u2="&#x43a;" k="-33" />
    <hkern u1="&#x44f;" u2="&#x433;" k="-33" />
    <hkern u1="&#x44f;" u2="&#x430;" k="-33" />
    <hkern u1="&#x44f;" u2="&#x423;" k="-47" />
    <hkern u1="&#x44f;" u2="&#x41b;" k="-33" />
    <hkern u1="&#x44f;" u2="&#x41a;" k="-33" />
    <hkern u1="&#x44f;" u2="&#x413;" k="-33" />
    <hkern u1="&#x44f;" u2="&#x410;" k="-33" />
    <hkern u1="&#x451;" u2="&#x44a;" k="-43" />
    <hkern u1="&#x451;" u2="&#x446;" k="-23" />
    <hkern u1="&#x451;" u2="&#x445;" k="-23" />
    <hkern u1="&#x451;" u2="&#x43d;" k="-43" />
    <hkern u1="&#x451;" u2="&#x43a;" k="-33" />
    <hkern u1="&#x451;" u2="&#x434;" k="-20" />
    <hkern u1="&#x451;" u2="&#x41a;" k="-33" />
    <hkern u1="&#x453;" u2="&#x44f;" k="31" />
    <hkern u1="&#x453;" u2="&#x44e;" k="-31" />
    <hkern u1="&#x453;" u2="&#x44d;" k="41" />
    <hkern u1="&#x453;" u2="&#x44a;" k="-43" />
    <hkern u1="&#x453;" u2="&#x444;" k="129" />
    <hkern u1="&#x453;" u2="&#x442;" k="-16" />
    <hkern u1="&#x453;" u2="&#x441;" k="63" />
    <hkern u1="&#x453;" u2="&#x43e;" k="80" />
    <hkern u1="&#x453;" u2="&#x43b;" k="209" />
    <hkern u1="&#x453;" u2="&#x436;" k="47" />
    <hkern u1="&#x453;" u2="&#x434;" k="170" />
    <hkern u1="&#x453;" u2="&#x432;" k="-16" />
    <hkern u1="&#x453;" u2="&#x430;" k="209" />
    <hkern u1="&#x453;" u2="&#x41b;" k="209" />
    <hkern u1="&#x453;" u2="&#x410;" k="209" />
    <hkern u1="&#x459;" u2="&#x442;" k="127" />
    <hkern u1="&#x45a;" u2="&#x442;" k="127" />
    <hkern u1="&#x45c;" u2="&#x44e;" k="-63" />
    <hkern u1="&#x45c;" u2="&#x44d;" k="86" />
    <hkern u1="&#x45c;" u2="&#x44a;" k="33" />
    <hkern u1="&#x45c;" u2="&#x447;" k="49" />
    <hkern u1="&#x45c;" u2="&#x445;" k="-33" />
    <hkern u1="&#x45c;" u2="&#x444;" k="160" />
    <hkern u1="&#x45c;" u2="&#x442;" k="20" />
    <hkern u1="&#x45c;" u2="&#x441;" k="94" />
    <hkern u1="&#x45c;" u2="&#x440;" k="-33" />
    <hkern u1="&#x45c;" u2="&#x43e;" k="106" />
    <hkern u1="&#x45c;" u2="&#x437;" k="33" />
    <hkern u1="&#x45c;" u2="&#x434;" k="-33" />
    <hkern u1="&#x45c;" u2="&#x432;" k="-33" />
    <hkern u1="&#x45c;" u2="&#x431;" k="-43" />
    <hkern u1="&#x45e;" u2="&#x44f;" k="96" />
    <hkern u1="&#x45e;" u2="&#x44e;" k="-43" />
    <hkern u1="&#x45e;" u2="&#x44d;" k="80" />
    <hkern u1="&#x45e;" u2="&#x44b;" k="-43" />
    <hkern u1="&#x45e;" u2="&#x44a;" k="-43" />
    <hkern u1="&#x45e;" u2="&#x447;" k="-20" />
    <hkern u1="&#x45e;" u2="&#x445;" k="-43" />
    <hkern u1="&#x45e;" u2="&#x444;" k="113" />
    <hkern u1="&#x45e;" u2="&#x442;" k="-49" />
    <hkern u1="&#x45e;" u2="&#x441;" k="63" />
    <hkern u1="&#x45e;" u2="&#x440;" k="-43" />
    <hkern u1="&#x45e;" u2="&#x43f;" k="-43" />
    <hkern u1="&#x45e;" u2="&#x43e;" k="240" />
    <hkern u1="&#x45e;" u2="&#x43d;" k="-43" />
    <hkern u1="&#x45e;" u2="&#x43c;" k="-43" />
    <hkern u1="&#x45e;" u2="&#x43b;" k="240" />
    <hkern u1="&#x45e;" u2="&#x434;" k="129" />
    <hkern u1="&#x45e;" u2="&#x433;" k="-43" />
    <hkern u1="&#x45e;" u2="&#x432;" k="-20" />
    <hkern u1="&#x45e;" u2="&#x431;" k="-43" />
    <hkern u1="&#x45e;" u2="&#x430;" k="240" />
    <hkern u1="&#x45e;" u2="&#x41b;" k="240" />
    <hkern u1="&#x45e;" u2="&#x413;" k="-43" />
    <hkern u1="&#x45e;" u2="&#x410;" k="240" />
    <hkern u1="&#x45f;" u2="&#x432;" k="-20" />
    <hkern u1="&#x45f;" u2="&#x412;" k="-20" />
    <hkern u1="&#x490;" u2="&#x44f;" k="31" />
    <hkern u1="&#x490;" u2="&#x44e;" k="-31" />
    <hkern u1="&#x490;" u2="&#x44d;" k="41" />
    <hkern u1="&#x490;" u2="&#x44a;" k="-43" />
    <hkern u1="&#x490;" u2="&#x444;" k="129" />
    <hkern u1="&#x490;" u2="&#x442;" k="-16" />
    <hkern u1="&#x490;" u2="&#x441;" k="63" />
    <hkern u1="&#x490;" u2="&#x43e;" k="80" />
    <hkern u1="&#x490;" u2="&#x43b;" k="209" />
    <hkern u1="&#x490;" u2="&#x436;" k="47" />
    <hkern u1="&#x490;" u2="&#x434;" k="170" />
    <hkern u1="&#x490;" u2="&#x432;" k="-16" />
    <hkern u1="&#x490;" u2="&#x430;" k="209" />
    <hkern u1="&#x490;" u2="&#x42f;" k="47" />
    <hkern u1="&#x490;" u2="&#x42e;" k="-33" />
    <hkern u1="&#x490;" u2="&#x424;" k="80" />
    <hkern u1="&#x490;" u2="&#x421;" k="63" />
    <hkern u1="&#x490;" u2="&#x41f;" k="-33" />
    <hkern u1="&#x490;" u2="&#x41e;" k="80" />
    <hkern u1="&#x490;" u2="&#x41c;" k="-33" />
    <hkern u1="&#x490;" u2="&#x41b;" k="209" />
    <hkern u1="&#x490;" u2="&#x414;" k="176" />
    <hkern u1="&#x490;" u2="&#x410;" k="209" />
    <hkern u1="&#x491;" u2="&#x44f;" k="31" />
    <hkern u1="&#x491;" u2="&#x44e;" k="-31" />
    <hkern u1="&#x491;" u2="&#x44d;" k="41" />
    <hkern u1="&#x491;" u2="&#x44a;" k="-43" />
    <hkern u1="&#x491;" u2="&#x444;" k="129" />
    <hkern u1="&#x491;" u2="&#x442;" k="-16" />
    <hkern u1="&#x491;" u2="&#x441;" k="63" />
    <hkern u1="&#x491;" u2="&#x43e;" k="80" />
    <hkern u1="&#x491;" u2="&#x43b;" k="209" />
    <hkern u1="&#x491;" u2="&#x436;" k="47" />
    <hkern u1="&#x491;" u2="&#x434;" k="170" />
    <hkern u1="&#x491;" u2="&#x432;" k="-16" />
    <hkern u1="&#x491;" u2="&#x430;" k="209" />
    <hkern u1="&#x491;" u2="&#x41b;" k="209" />
    <hkern u1="&#x491;" u2="&#x410;" k="209" />
    <hkern u1="&#x2013;" u2="&#x1e9e;" k="25" />
    <hkern u1="&#x2013;" u2="x" k="47" />
    <hkern u1="&#x2013;" u2="v" k="80" />
    <hkern u1="&#x2013;" u2="l" k="63" />
    <hkern u1="&#x2013;" u2="i" k="63" />
    <hkern u1="&#x2013;" u2="X" k="49" />
    <hkern u1="&#x2013;" u2="V" k="63" />
    <hkern u1="&#x2013;" u2="Q" k="-33" />
    <hkern u1="&#x2013;" u2="L" k="-31" />
    <hkern u1="&#x2013;" u2="I" k="63" />
    <hkern u1="&#x2013;" u2="G" k="-16" />
    <hkern u1="&#x2014;" u2="&#x1e9e;" k="25" />
    <hkern u1="&#x2014;" u2="x" k="47" />
    <hkern u1="&#x2014;" u2="v" k="80" />
    <hkern u1="&#x2014;" u2="l" k="63" />
    <hkern u1="&#x2014;" u2="i" k="63" />
    <hkern u1="&#x2014;" u2="X" k="49" />
    <hkern u1="&#x2014;" u2="V" k="63" />
    <hkern u1="&#x2014;" u2="Q" k="-33" />
    <hkern u1="&#x2014;" u2="L" k="-31" />
    <hkern u1="&#x2014;" u2="I" k="63" />
    <hkern u1="&#x2014;" u2="G" k="-16" />
    <hkern u1="&#x2018;" u2="&#x1e9e;" k="16" />
    <hkern u1="&#x2018;" u2="&#x149;" k="-18" />
    <hkern u1="&#x2019;" u2="&#x103;" k="190" />
    <hkern u1="&#x2019;" u2="&#x101;" k="190" />
    <hkern u1="&#x2019;" u2="&#xe5;" k="190" />
    <hkern u1="&#x2019;" u2="&#xe4;" k="190" />
    <hkern u1="&#x2019;" u2="&#xe3;" k="190" />
    <hkern u1="&#x2019;" u2="&#xe2;" k="190" />
    <hkern u1="&#x2019;" u2="&#xe1;" k="190" />
    <hkern u1="&#x2019;" u2="&#xe0;" k="190" />
    <hkern u1="&#x2019;" u2="a" k="190" />
    <hkern u1="&#x2019;" u2="&#x219;" k="176" />
    <hkern u1="&#x2019;" u2="&#x14b;" k="129" />
    <hkern u1="&#x2019;" u2="&#x149;" k="129" />
    <hkern u1="&#x2019;" u2="&#x105;" k="190" />
    <hkern u1="&#x2019;" u2="&#xe6;" k="190" />
    <hkern u1="&#x2019;" u2="s" k="176" />
    <hkern u1="&#x2019;" u2="r" k="129" />
    <hkern u1="&#x2019;" u2="q" k="190" />
    <hkern u1="&#x2019;" u2="n" k="129" />
    <hkern u1="&#x2019;" u2="m" k="129" />
    <hkern u1="&#x2019;" u2="d" k="190" />
    <hkern u1="&#x2019;" u2="V" k="-43" />
    <hkern u1="&#x201c;" u2="&#x1e9e;" k="16" />
    <hkern u1="&#x201c;" u2="&#x149;" k="-18" />
    <hkern g1="f_f" u2="&#x149;" k="-27" />
    <hkern g1="f_f" u2="x" k="-16" />
    <hkern g1="f_f" u2="v" k="-49" />
    <hkern g1="f_f" u2="r" k="16" />
    <hkern g1="f_f" u2="q" k="47" />
    <hkern g1="f_f" u2="o" k="47" />
    <hkern g1="f_f" u2="j" k="16" />
    <hkern g1="f_f" u2="i" k="16" />
    <hkern g1="f_f" u2="e" k="47" />
    <hkern g1="f_f" u2="R" k="-80" />
    <hkern g1="f_f" u2="P" k="-63" />
    <hkern g1="f_f" u2="M" k="-80" />
    <hkern g1="f_f" u2="L" k="-80" />
    <hkern g1="f_f" u2="H" k="-113" />
    <hkern g1="f_f" u2="G" k="-49" />
    <hkern g1="f_f" u2="D" k="-63" />
    <hkern g1="f_f" u2="&#x2c;" k="96" />
    <hkern g1="fl" u2="&#x103;" k="-23" />
    <hkern g1="fl" u2="&#x101;" k="-23" />
    <hkern g1="fl" u2="&#xe5;" k="-23" />
    <hkern g1="fl" u2="&#xe4;" k="-23" />
    <hkern g1="fl" u2="&#xe3;" k="-23" />
    <hkern g1="fl" u2="&#xe2;" k="-23" />
    <hkern g1="fl" u2="&#xe1;" k="-23" />
    <hkern g1="fl" u2="&#xe0;" k="-23" />
    <hkern g1="fl" u2="a" k="-23" />
    <hkern g1="fl" u2="&#x105;" k="-23" />
    <hkern g1="fl" u2="&#xe6;" k="-23" />
    <hkern g1="Alphatonos,Alpha,Delta,Lambda"
	g2="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	k="160" />
    <hkern g1="Alphatonos,Alpha,Delta,Lambda"
	g2="omega,omegatonos"
	k="-47" />
    <hkern g1="Alphatonos,Alpha,Delta,Lambda"
	g2="Alpha,Delta,Lambda"
	k="-33" />
    <hkern g1="Alphatonos,Alpha,Delta,Lambda"
	g2="Theta,Omicron"
	k="49" />
    <hkern g1="Alphatonos,Alpha,Delta,Lambda"
	g2="Upsilon,Upsilondieresis"
	k="160" />
    <hkern g1="Alphatonos,Alpha,Delta,Lambda"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek"
	k="-33" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek"
	k="113" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="quotedbl,quotesingle"
	k="111" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="hyphen,endash,emdash"
	k="63" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="-33" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_j"
	k="129" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="z,zacute,zdotaccent,zcaron"
	k="-14" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek"
	k="31" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="66" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="z,zacute,zdotaccent,zcaron"
	k="23" />
    <hkern g1="Iotatonos,Iota,Iotadieresis"
	g2="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	k="-16" />
    <hkern g1="Iotatonos,Iota,Iotadieresis"
	g2="omega,omegatonos"
	k="-23" />
    <hkern g1="Iotatonos,Iota,Iotadieresis"
	g2="Alpha,Delta,Lambda"
	k="-43" />
    <hkern g1="Iotatonos,Iota,Iotadieresis"
	g2="Theta,Omicron"
	k="63" />
    <hkern g1="Iotatonos,Iota,Iotadieresis"
	g2="Upsilon,Upsilondieresis"
	k="-16" />
    <hkern g1="Iotatonos,Iota,Iotadieresis"
	g2="Iota,Iotadieresis"
	k="-20" />
    <hkern g1="Iotatonos,Iota,Iotadieresis"
	g2="omicron,omicrontonos"
	k="63" />
    <hkern g1="Iotatonos,Iota,Iotadieresis"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="-20" />
    <hkern g1="Iotatonos,Iota,Iotadieresis"
	g2="alphatonos,alpha"
	k="-43" />
    <hkern g1="Omegatonos,Omega"
	g2="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	k="84" />
    <hkern g1="Omegatonos,Omega"
	g2="omega,omegatonos"
	k="-43" />
    <hkern g1="Omegatonos,Omega"
	g2="Alpha,Delta,Lambda"
	k="-41" />
    <hkern g1="Omegatonos,Omega"
	g2="Upsilon,Upsilondieresis"
	k="84" />
    <hkern g1="Omegatonos,Omega"
	g2="Iota,Iotadieresis"
	k="-43" />
    <hkern g1="Omegatonos,Omega"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="-43" />
    <hkern g1="Omegatonos,Omega"
	g2="alphatonos,alpha"
	k="-41" />
    <hkern g1="Omicrontonos,Omicron"
	g2="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	k="96" />
    <hkern g1="Omicrontonos,Omicron"
	g2="Alpha,Delta,Lambda"
	k="43" />
    <hkern g1="Omicrontonos,Omicron"
	g2="Upsilon,Upsilondieresis"
	k="96" />
    <hkern g1="Omicrontonos,Omicron"
	g2="Iota,Iotadieresis"
	k="63" />
    <hkern g1="Omicrontonos,Omicron"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="63" />
    <hkern g1="Omicrontonos,Omicron"
	g2="alphatonos,alpha"
	k="43" />
    <hkern g1="Theta,Omicron"
	g2="Alpha,Delta,Lambda"
	k="47" />
    <hkern g1="Theta,Omicron"
	g2="Upsilon,Upsilondieresis"
	k="80" />
    <hkern g1="Theta,Omicron"
	g2="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	k="80" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="Alpha,Delta,Lambda"
	k="170" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="Upsilon,Upsilondieresis"
	k="-63" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="omicron,omicrontonos"
	k="86" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="omega,omegatonos"
	k="84" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="Theta,Omicron"
	k="106" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="Iota,Iotadieresis"
	k="-16" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="-43" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="alphatonos,alpha"
	k="193" />
    <hkern g1="afii10023,afii10022"
	g2="afii10062,afii10037"
	k="-43" />
    <hkern g1="afii10023,afii10022"
	g2="afii10085,afii10110"
	k="-43" />
    <hkern g1="afii10058,afii10059,afii10046"
	g2="afii10062,afii10037"
	k="86" />
    <hkern g1="afii10058,afii10059,afii10046"
	g2="afii10085,afii10110"
	k="86" />
    <hkern g1="afii10052,afii10020,afii10050"
	g2="afii10062,afii10037"
	k="-33" />
    <hkern g1="afii10052,afii10020,afii10050"
	g2="afii10085,afii10110"
	k="-33" />
    <hkern g1="afii10052,afii10020,afii10050"
	g2="afii10070,afii10071"
	k="-16" />
    <hkern g1="afii10062,afii10037"
	g2="afii10062,afii10037"
	k="-31" />
    <hkern g1="afii10062,afii10037"
	g2="afii10070,afii10071"
	k="-23" />
    <hkern g1="afii10070,afii10071"
	g2="afii10062,afii10037"
	k="-47" />
    <hkern g1="afii10070,afii10071"
	g2="afii10085,afii10110"
	k="-47" />
    <hkern g1="afii10085,afii10110"
	g2="afii10062,afii10037"
	k="-63" />
    <hkern g1="afii10085,afii10110"
	g2="afii10085,afii10110"
	k="-63" />
    <hkern g1="afii10085,afii10110"
	g2="afii10070,afii10071"
	k="-23" />
    <hkern g1="afii10094,afii10106,afii10107"
	g2="afii10062,afii10037"
	k="129" />
    <hkern g1="afii10094,afii10106,afii10107"
	g2="afii10085,afii10110"
	k="129" />
    <hkern g1="afii10068,afii10100,afii10098"
	g2="afii10062,afii10037"
	k="-33" />
    <hkern g1="afii10068,afii10100,afii10098"
	g2="afii10085,afii10110"
	k="-33" />
    <hkern g1="afii10068,afii10100,afii10098"
	g2="afii10070,afii10071"
	k="-16" />
    <hkern g1="alphatonos,alpha"
	g2="Upsilon,Upsilondieresis"
	k="209" />
    <hkern g1="alphatonos,alpha"
	g2="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	k="209" />
    <hkern g1="alphatonos,alpha"
	g2="omicron,omicrontonos"
	k="33" />
    <hkern g1="alphatonos,alpha"
	g2="omega,omegatonos"
	k="-49" />
    <hkern g1="alphatonos,alpha"
	g2="alphatonos,alpha"
	k="-66" />
    <hkern g1="b,p,thorn"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek"
	k="-16" />
    <hkern g1="b,p,thorn"
	g2="z,zacute,zdotaccent,zcaron"
	k="31" />
    <hkern g1="f,f_f"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek"
	k="33" />
    <hkern g1="f,f_f"
	g2="z,zacute,zdotaccent,zcaron"
	k="-14" />
    <hkern g1="hyphen,endash,emdash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek"
	k="-31" />
    <hkern g1="hyphen,endash,emdash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="66" />
    <hkern g1="iotadieresistonos,iotatonos,iota,iotadieresis"
	g2="Upsilon,Upsilondieresis"
	k="-43" />
    <hkern g1="iotadieresistonos,iotatonos,iota,iotadieresis"
	g2="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	k="-43" />
    <hkern g1="iotadieresistonos,iotatonos,iota,iotadieresis"
	g2="omicron,omicrontonos"
	k="66" />
    <hkern g1="iotadieresistonos,iotatonos,iota,iotadieresis"
	g2="omega,omegatonos"
	k="-23" />
    <hkern g1="iotadieresistonos,iotatonos,iota,iotadieresis"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="-20" />
    <hkern g1="iotadieresistonos,iotatonos,iota,iotadieresis"
	g2="alphatonos,alpha"
	k="-43" />
    <hkern g1="omega,omegatonos"
	g2="Alpha,Delta,Lambda"
	k="-41" />
    <hkern g1="omega,omegatonos"
	g2="Upsilon,Upsilondieresis"
	k="63" />
    <hkern g1="omega,omegatonos"
	g2="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	k="63" />
    <hkern g1="omega,omegatonos"
	g2="omega,omegatonos"
	k="-43" />
    <hkern g1="omega,omegatonos"
	g2="Iota,Iotadieresis"
	k="-43" />
    <hkern g1="omega,omegatonos"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="-43" />
    <hkern g1="omega,omegatonos"
	g2="alphatonos,alpha"
	k="-41" />
    <hkern g1="omicron,omicrontonos"
	g2="Upsilon,Upsilondieresis"
	k="80" />
    <hkern g1="omicron,omicrontonos"
	g2="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	k="80" />
    <hkern g1="omicron,omicrontonos"
	g2="Iota,Iotadieresis"
	k="63" />
    <hkern g1="omicron,omicrontonos"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="63" />
    <hkern g1="omicron,omicrontonos"
	g2="alphatonos,alpha"
	k="43" />
    <hkern g1="quotedbl,quotesingle"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek"
	k="96" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="96" />
    <hkern g1="quotedbl,quotesingle"
	g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_j"
	k="-33" />
    <hkern g1="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	g2="Alpha,Delta,Lambda"
	k="170" />
    <hkern g1="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	g2="Upsilon,Upsilondieresis"
	k="-43" />
    <hkern g1="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	g2="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	k="-43" />
    <hkern g1="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	g2="omicron,omicrontonos"
	k="86" />
    <hkern g1="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	g2="omega,omegatonos"
	k="84" />
    <hkern g1="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	g2="Theta,Omicron"
	k="106" />
    <hkern g1="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	g2="Iota,Iotadieresis"
	k="-16" />
    <hkern g1="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="-43" />
    <hkern g1="upsilondieresistonos,upsilon,upsilondieresis,upsilontonos"
	g2="alphatonos,alpha"
	k="193" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek"
	k="31" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-16" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_j"
	k="-31" />
  </font>
</defs></svg>
