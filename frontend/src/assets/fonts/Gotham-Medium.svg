<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Sun Jan 22 11:00:49 2006
 By Aleksey,,,
HTF Gotham\252 Copr. 2000 The Hoefler Type Foundry, Inc. Info: www.typography.com
</metadata>
<defs>
<font id="Gotham-Medium" horiz-adv-x="500" >
  <font-face 
    font-family="Gotham"
    font-weight="500"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 6 4 4 0 0 2 0 4"
    ascent="800"
    descent="-200"
    x-height="528"
    cap-height="700"
    bbox="-70 -165.029 1180 922"
    underline-thickness="20"
    underline-position="-163"
    unicode-range="U+0020-FB02"
  />
<missing-glyph 
 />
    <glyph glyph-name=".notdef" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="300" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="300" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="302" 
d="M80 700v-35l38 -444h67l37 444v35h-142zM218 0v137h-133v-137h133z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="487" 
d="M279 401h63l98 294v5h-137zM60 401h63l97 294v5h-137z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="700" 
d="M660 439v104h-89l27 157h-107l-26 -157h-164l27 157h-107l-27 -157h-118v-104h100l-29 -174h-108v-104h90l-28 -161h107l27 161h164l-28 -161h107l28 161h118v104h-100l29 174h107zM447 439l-30 -174h-164l30 174h164z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="642" 
d="M585 195q0 81 -51.5 129.5t-165.5 77.5v190q71 -15 136 -64l56 94q-42 31 -87.5 50t-100.5 26v60h-87v-58q-47 -4 -85.5 -19t-66.5 -40t-43 -59t-15 -74q0 -42 13 -74.5t39.5 -57t66.5 -42.5t95 -32v-196q-49 8 -91 29.5t-85 56.5l-64 -91q51 -41 110.5 -66t125.5 -33
v-100h87v99q48 4 87 19.5t67 40.5t43.5 59t15.5 75zM289 423q-58 17 -77 38.5t-19 53.5q0 33 24 56.5t72 27.5v-176zM467 188q0 -36 -25.5 -59t-73.5 -27v181q29 -9 48 -18.5t30.5 -21t16 -25t4.5 -30.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="849" 
d="M378 530q0 36 -12 68.5t-33.5 57t-51.5 38.5t-67 14t-67.5 -14.5t-52.5 -39t-34 -57t-12 -69.5q0 -36 12 -68.5t33.5 -57t51.5 -38.5t67 -14t67.5 14.5t52.5 39t34 57t12 69.5zM730 700h-99l-512 -700h99zM801 172q0 36 -12 68.5t-33.5 57t-51.5 38.5t-67 14t-67.5 -14.5
t-52.5 -39t-34 -57t-12 -69.5q0 -36 12 -68.5t33.5 -57t51.5 -38.5t67 -14t67.5 14.5t52.5 39t34 57t12 69.5zM289 528q0 -42 -20 -71.5t-55 -29.5q-34 0 -55.5 29.5t-21.5 73.5q0 42 20 71.5t55 29.5q34 0 55.5 -29.5t21.5 -73.5zM712 170q0 -42 -20 -71.5t-55 -29.5
q-34 0 -55.5 29.5t-21.5 73.5q0 42 20 71.5t55 29.5q34 0 55.5 -29.5t21.5 -73.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="696" 
d="M666 51l-110 112q27 37 50.5 77.5t44.5 82.5l-93 49q-17 -37 -36 -71t-39 -64l-120 122q70 26 112 69.5t42 113.5q0 36 -13.5 67t-38 54t-58.5 36t-76 13q-47 0 -84 -14.5t-63 -38.5t-40 -56t-14 -68q0 -39 14 -73.5t44 -73.5q-72 -31 -110.5 -79.5t-38.5 -117.5
q0 -46 17.5 -83t48 -63.5t72 -40.5t90.5 -14q64 0 117 24.5t98 66.5l93 -95zM408 535q0 -39 -28 -64.5t-82 -45.5q-29 32 -41 55t-12 50q0 38 22.5 61.5t60.5 23.5q35 0 57.5 -21.5t22.5 -58.5zM412 153q-30 -29 -63 -45t-69 -16q-51 0 -84 29t-33 74q0 35 22 65.5t71 51.5z
" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="269" 
d="M60 401h64l98 294v5h-139z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="440" 
d="M393 634l-53 81q-137 -75 -207 -180t-70 -248t70 -248t207 -180l53 81q-107 70 -158 152.5t-51 194.5q0 56 12.5 104t38.5 90t65.5 80t92.5 73z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="440" 
d="M48 634q106 -71 157.5 -153t51.5 -194q0 -56 -12.5 -104t-38.5 -90t-65.5 -80t-92.5 -73l53 -81q136 75 206.5 180.5t70.5 247.5t-70.5 247.5t-206.5 180.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="430" 
d="M372 492l-114 50l114 50l-36 61l-100 -73l14 122h-70l14 -122l-100 73l-36 -61l114 -50l-114 -50l36 -61l100 73l-14 -122h70l-14 122l100 -73z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="640" 
d="M263 407h-192v-110h192v-191h114v191h192v110h-192v191h-114v-191z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="269" 
d="M34 -94l13 -51q76 6 115 44t39 122v116h-133v-137h51q7 -76 -85 -94z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="408" 
d="M352 247v119h-296v-119h296z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="269" 
d="M201 0v137h-133v-137h133z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="521" 
d="M427 798l-452 -926h105l452 926h-105z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="724" 
d="M667 351q0 75 -21.5 141t-61.5 115t-96 77t-125 28t-125.5 -28.5t-96.5 -77.5t-62 -115.5t-22 -141.5t21.5 -141t61.5 -115t96 -77t125 -28t125.5 28.5t96.5 77.5t62 115.5t22 141.5zM540 349q0 -52 -12.5 -97.5t-35.5 -79.5t-55.5 -53.5t-73.5 -19.5t-74 19.5t-56.5 54
t-36 80t-12.5 98.5q0 52 12.5 98t35.5 79.5t55.5 53t73.5 19.5t74 -20t56.5 -54t36 -80t12.5 -98z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="396" 
d="M26 645l26 -100l126 35v-580h122v705h-86z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="613" 
d="M308 600q48 0 80.5 -28.5t32.5 -80.5q0 -24 -7 -45t-22.5 -43t-40.5 -46.5t-61 -54.5l-241 -205v-97h506v108h-333l144 120q46 38 80.5 70t57 63t33.5 64t11 75q0 47 -16.5 85.5t-46.5 66.5t-73 43t-95 15q-47 0 -83.5 -10t-67 -28.5t-56.5 -46t-51 -62.5l87 -68
q41 53 77.5 79t84.5 26z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="618" 
d="M385 593l-189 -201l20 -77h57q75 0 118 -28t43 -81q0 -49 -34.5 -78t-86.5 -29q-59 0 -102.5 23.5t-81.5 68.5l-86 -80q45 -54 110.5 -88.5t157.5 -34.5q54 0 99.5 16.5t77.5 46t50 70t18 89.5t-17 84t-45.5 58.5t-66 37t-77.5 18.5l195 200v92h-463v-107h303z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="681" 
d="M425 705l-389 -460l23 -88h353v-157h118v157h100v101h-100v447h-105zM412 258h-230l230 277v-277z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="622" 
d="M94 357l74 -49q27 13 59 22t70 9q66 0 105.5 -31.5t39.5 -85.5q0 -56 -38 -89t-101 -33q-49 0 -93.5 22.5t-86.5 61.5l-76 -89q50 -48 113 -77.5t144 -29.5q58 0 106 17t82 48t52.5 75t18.5 98q0 56 -18.5 97.5t-51.5 69t-77.5 41t-95.5 13.5q-35 0 -60.5 -5.5
t-50.5 -14.5l11 164h312v109h-418z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="656" 
d="M571 634q-46 37 -96 57.5t-118 20.5q-74 0 -130.5 -29.5t-94 -81t-56.5 -121t-19 -150.5q0 -53 5.5 -94t16.5 -73.5t27 -57t36 -44.5q35 -35 82.5 -54t112.5 -19q55 0 102.5 17.5t82.5 49t55 75.5t20 97t-19 94t-52.5 69.5t-78 43t-95.5 14.5q-31 0 -55.5 -6t-45 -15.5
t-36.5 -22.5t-30 -26q2 48 14 88.5t33 70.5t51.5 46.5t69.5 16.5q46 0 80.5 -16t71.5 -45zM474 222q0 -55 -37 -91.5t-103 -36.5t-105.5 36t-39.5 91q0 25 10 48t28.5 39.5t44.5 26.5t59 10q66 0 104.5 -34.5t38.5 -88.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="609" 
d="M68 592h352l-314 -592h138l316 607v93h-492v-108z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="630" 
d="M582 194q0 63 -34.5 103t-91.5 66q44 23 73 60.5t29 96.5q0 40 -18.5 75t-50.5 60.5t-77 40t-97 14.5q-53 0 -97.5 -14.5t-76.5 -40t-50.5 -60.5t-18.5 -75q0 -59 29 -96.5t73 -60.5q-60 -25 -93 -66t-33 -106q0 -45 20 -82.5t56 -63.5t85 -40.5t106 -14.5t106 14
t85 40.5t56 64.5t20 85zM440 508q0 -46 -35.5 -75t-89.5 -29t-89.5 29t-35.5 76q0 41 34 69.5t91 28.5t91 -28.5t34 -70.5zM462 199q0 -45 -39 -75.5t-108 -30.5t-108 30.5t-39 75.5q0 24 11.5 44t31 34t46.5 21.5t58 7.5t58 -7.5t46.5 -21.5t31 -34t11.5 -44z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="656" 
d="M599 370q0 104 -23 166.5t-62 101.5q-37 37 -82.5 55.5t-111.5 18.5q-58 0 -106 -18.5t-82.5 -51t-53.5 -77t-19 -96.5q0 -51 18 -92t50.5 -70.5t78 -45t101.5 -15.5q57 0 97.5 21t66.5 53q-2 -48 -14 -88.5t-33.5 -70t-52 -45.5t-69.5 -16q-45 0 -82.5 16t-77.5 50
l-67 -94q45 -37 98 -60.5t126 -23.5q70 0 126 28t94.5 78.5t59 120.5t20.5 155zM467 476q0 -26 -9.5 -49t-28 -40.5t-45 -27.5t-59.5 -10q-66 0 -104.5 35t-38.5 91q0 55 36.5 93t102.5 38q33 0 60 -10t46 -27.5t29.5 -41t10.5 -51.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="279" 
d="M206 391v137h-133v-137h133zM206 0v137h-133v-137h133z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="279" 
d="M206 391v137h-133v-137h133zM39 -94l13 -51q76 6 115 44t39 122v116h-133v-137h51q7 -76 -85 -94z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="640" 
d="M70 406v-108l477 -221v116l-357 160l357 159v115z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="640" 
d="M86 533v-112h468v112h-468zM86 283v-112h468v112h-468z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="640" 
d="M570 298v108l-477 221v-116l357 -160l-357 -159v-115z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="541" 
d="M192 388l-5 -5l20 -162h81l10 81q42 7 78.5 22t63.5 39t43 59t16 83q0 47 -17 85t-47.5 64.5t-74 40.5t-95.5 14q-81 0 -139 -30.5t-102 -80.5l75 -80q36 38 75.5 58.5t87.5 20.5q54 0 84.5 -26.5t30.5 -69.5q0 -51 -44.5 -80t-140.5 -33zM312 0v137h-134v-137h134z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="980" 
d="M927 316q0 78 -33.5 149.5t-92.5 126t-138.5 87.5t-171.5 33t-172 -35t-139 -94.5t-93 -139t-34 -169.5t33.5 -169t93.5 -138.5t141.5 -94t177.5 -34.5q78 0 142.5 20t122.5 55l-18 30q-57 -33 -115 -50t-132 -17q-89 0 -163.5 31t-128.5 85t-84 126.5t-30 155.5
q0 82 30 155t83.5 127.5t126 86t158.5 31.5q85 0 157.5 -30t125.5 -80t83 -114.5t30 -133.5q0 -51 -10.5 -89.5t-28.5 -64t-42 -38.5t-51 -13q-37 0 -57.5 19.5t-20.5 51.5q0 7 1.5 18t3.5 25l44 252l-97 15l-12 -60q-20 30 -53.5 52t-87.5 22q-44 0 -84.5 -19t-72.5 -53
t-51 -79.5t-19 -98.5q0 -43 14 -78t39 -59.5t57.5 -38t70.5 -13.5q53 0 90.5 21t64.5 49q17 -28 53 -49t95 -21q39 0 74 15.5t61.5 46t42.5 77.5t16 110zM595 312q0 -33 -11.5 -61.5t-30.5 -50t-44 -34t-52 -12.5q-47 0 -76.5 28t-29.5 80q0 34 11.5 63t30.5 50t44.5 33
t52.5 12q45 0 75 -30t30 -78z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="790" 
d="M452 705h-114l-308 -705h126l72 169h331l71 -169h130zM514 278h-241l120 280z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="722" 
d="M404 700h-314v-700h324q58 0 106 12.5t82 37t52.5 60.5t18.5 83q0 36 -10 63.5t-28.5 47.5t-44 34.5t-56.5 25.5q19 10 37.5 24t32 32.5t22 43.5t8.5 57q0 83 -62 131t-168 48zM379 405h-168v186h177q60 0 91.5 -23.5t31.5 -66.5q0 -49 -36 -72.5t-96 -23.5zM415 109
h-204v192h195q73 0 108.5 -25t35.5 -70q0 -48 -36 -72.5t-99 -24.5z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="738" 
d="M699 112l-79 80q-45 -42 -91 -66.5t-110 -24.5q-50 0 -92.5 19.5t-73.5 53.5t-48 79.5t-17 97.5t17 97t48 78.5t73.5 53t92.5 19.5q60 0 107 -24t89 -63l79 91q-25 24 -53 44t-61 34.5t-72.5 22.5t-87.5 8q-80 0 -146 -28.5t-114 -78t-74.5 -115.5t-26.5 -141
q0 -76 27 -142t74.5 -114.5t112.5 -76.5t142 -28q49 0 89.5 8.5t74.5 25t63.5 39t56.5 51.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="782" 
d="M723 351q0 74 -27 138t-76.5 111t-118 73.5t-150.5 26.5h-261v-700h261q82 0 150.5 27t118 74t76.5 111.5t27 138.5zM594 349q0 -52 -17.5 -95.5t-49 -75t-76.5 -49t-100 -17.5h-138v476h138q55 0 100 -18t76.5 -50t49 -75.5t17.5 -95.5z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="670" 
d="M609 700h-519v-700h524v110h-401v188h351v110h-351v182h396v110z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="656" 
d="M611 700h-521v-700h123v285h353v112h-353v191h398v112z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="784" 
d="M714 97v294h-298v-107h179v-131q-32 -24 -75.5 -38.5t-93.5 -14.5q-54 0 -97.5 19t-75 52.5t-48.5 79.5t-17 100q0 51 17.5 96t48 79t72 53.5t89.5 19.5q33 0 60.5 -5.5t50.5 -15t44 -23.5t41 -31l78 93q-27 23 -55.5 40.5t-60.5 29.5t-69.5 18.5t-83.5 6.5
q-79 0 -145.5 -29t-114.5 -78.5t-74.5 -115.5t-26.5 -140q0 -77 26 -143t73.5 -114.5t114.5 -76t149 -27.5q46 0 88 8.5t78.5 23.5t68 35t57.5 42z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="760" 
d="M547 295v-295h123v700h-123v-291h-334v291h-123v-700h123v295h334z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="317" 
d="M97 700v-700h123v700h-123z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="559" 
d="M480 238v462h-126v-459q0 -70 -30.5 -103.5t-80.5 -33.5q-45 0 -77.5 21.5t-63.5 60.5l-81 -83q35 -48 89 -80.5t135 -32.5q52 0 95 15.5t74 46.5t48.5 77.5t17.5 108.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="725" 
d="M406 399l291 301h-151l-333 -352v352h-123v-700h123v203l109 112l239 -315h149z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="619" 
d="M90 0h491v112h-368v588h-123v-700z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="868" 
d="M655 502v-502h123v700h-131l-213 -331l-213 331h-131v-700h121v500l219 -328h4z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="790" 
d="M579 216l-375 484h-114v-700h121v498l386 -498h103v700h-121v-484z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="850" 
d="M791 351q0 74 -27 140t-75 115t-115 77.5t-148 28.5t-148 -29t-116 -78.5t-76 -115.5t-27 -140t27 -140t75 -115t115 -77.5t148 -28.5t148 29t116 78.5t76 115.5t27 140zM662 349q0 -51 -17.5 -96.5t-48.5 -79t-74.5 -53t-95.5 -19.5t-96 20t-75.5 54t-49 79.5
t-17.5 96.5t17.5 96.5t48.5 79t74.5 53t95.5 19.5t96 -20t75.5 -54t49 -79.5t17.5 -96.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="668" 
d="M632 465q0 54 -18.5 97t-53 74t-84 47.5t-110.5 16.5h-276v-700h123v225h139q58 0 109 15.5t89 45.5t60 75t22 104zM507 462q0 -57 -40.5 -91.5t-110.5 -34.5h-143v252h143q69 0 110 -31.5t41 -94.5z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="850" 
d="M720 135q34 46 52.5 101t18.5 115q0 74 -27 140t-75 115t-115 77.5t-148 28.5t-148 -29t-116 -78.5t-76 -115.5t-27 -140t27 -140t75 -115t115 -77.5t148 -28.5q62 0 115 17.5t98 48.5l84 -75l79 88zM546 135q-25 -16 -55 -25t-65 -9q-52 0 -96 20t-75.5 54t-49 79.5
t-17.5 96.5t17.5 96.5t48.5 79t74.5 53t95.5 19.5t96 -20t75.5 -54t49 -79.5t17.5 -96.5q0 -37 -9 -70.5t-27 -61.5l-121 110l-79 -90z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="723" 
d="M684 0l-189 265q37 10 67.5 28.5t52.5 45t34.5 61t12.5 78.5q0 51 -18 92.5t-51.5 70t-82 44t-108.5 15.5h-312v-700h123v244h154l172 -244h145zM537 471q0 -54 -39 -86t-105 -32h-180v235h179q69 0 107 -29.5t38 -87.5z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="640" 
d="M194 517q0 35 30 59.5t84 24.5q48 0 95 -18.5t95 -53.5l66 93q-54 43 -115.5 65.5t-138.5 22.5q-52 0 -96 -14.5t-75.5 -41.5t-49.5 -64.5t-18 -83.5q0 -49 15.5 -83t45 -58t73 -40t99.5 -29q49 -12 79.5 -23t48 -23.5t24 -27.5t6.5 -34q0 -41 -33.5 -65t-90.5 -24
q-66 0 -118 23.5t-103 67.5l-74 -88q63 -57 137 -84.5t155 -27.5q55 0 101 14t79 41t51.5 66t18.5 89q0 44 -14.5 76.5t-42.5 57t-70.5 41.5t-97.5 31q-50 12 -82.5 23t-51 24t-25.5 28t-7 36z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="648" 
d="M386 586h222v114h-568v-114h222v-586h124v586z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="757" 
d="M678 700h-123v-403q0 -97 -47 -146t-129 -49q-83 0 -130 51t-47 149v398h-123v-403q0 -76 21 -133.5t60 -96.5t94 -58.5t123 -19.5q69 0 124.5 19.5t94.5 59t60.5 98.5t21.5 137v397z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="750" 
d="M377 162l-211 538h-136l291 -705h108l291 705h-133z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1116" 
d="M336 181l-167 519h-133l244 -705h106l172 503l172 -503h106l244 705h-129l-167 -519l-172 521h-104z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="729" 
d="M692 0l-254 358l244 342h-141l-175 -251l-174 251h-145l245 -344l-255 -356h141l185 265l184 -265h145z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="716" 
d="M420 279l278 421h-141l-198 -310l-196 310h-145l278 -424v-276h124v279z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="702" 
d="M478 591l-413 -500v-91h573v109h-413l413 500v91h-560v-109h400z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="454" 
d="M395 700h-307v-830h307v95h-192v640h192v95z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="521" 
d="M94 798h-105l452 -926h105z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="454" 
d="M59 700v-95h192v-640h-192v-95h308v830h-308z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M249 625l102 -132h93l-151 208h-86l-151 -208h91z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="600" 
d="M-2 -66v-94h604v94h-604z" />
    <glyph glyph-name="grave" unicode="`" 
d="M246 595h94l-106 164l-110 -49z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="587" 
d="M519 0v313q0 106 -57 164.5t-175 58.5q-65 0 -111 -12t-90 -32l33 -97q36 15 71 24t80 9q63 0 96.5 -29t33.5 -84v-13q-31 10 -64.5 16t-81.5 6q-46 0 -85.5 -10.5t-68 -31.5t-44.5 -53t-16 -75v-2q0 -40 15.5 -70.5t41 -51t59.5 -31t72 -10.5q60 0 102 22t69 54v-65h120
zM402 191q0 -25 -10.5 -45.5t-29.5 -35t-44.5 -22.5t-55.5 -8q-43 0 -72.5 20t-29.5 58v2q0 41 32 63t89 22q35 0 66.5 -6t54.5 -15v-33z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="670" 
d="M622 264q0 66 -21 117t-55.5 86.5t-78.5 53.5t-91 18q-64 0 -107 -28.5t-72 -70.5v290h-121v-730h121v82q28 -37 71 -65t108 -28q47 0 91.5 18t79 53.5t55 86.5t20.5 117zM499 264q0 -39 -12 -70.5t-32.5 -53.5t-48 -34t-58.5 -12t-59 12t-49 34.5t-33.5 53.5t-12.5 70
t12.5 70t33.5 53.5t49 34.5t59 12t58.5 -12t48 -34t32.5 -53.5t12 -70.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="571" 
d="M536 87l-73 72q-29 -29 -61.5 -47.5t-75.5 -18.5q-35 0 -64 13.5t-50 36.5t-32.5 54t-11.5 67q0 35 11.5 65.5t31.5 53.5t48 36.5t61 13.5q46 0 77.5 -18.5t59.5 -47.5l75 81q-37 41 -86.5 66t-124.5 25q-59 0 -109.5 -22t-87 -60t-57 -88t-20.5 -107t20.5 -106.5t57 -87
t86.5 -59t109 -21.5q77 0 127 27.5t89 71.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="670" 
d="M595 0v730h-121v-284q-29 37 -72 65t-108 28q-47 0 -91.5 -18t-78.5 -53.5t-54.5 -86.5t-20.5 -117t21 -117t55 -86.5t78 -53.5t91 -18q64 0 107.5 29t72.5 70v-88h121zM476 264q0 -39 -12.5 -70t-33.5 -53.5t-49 -34.5t-58 -12q-31 0 -58.5 12t-48.5 34t-33 53.5
t-12 70.5q0 40 12 71.5t33 53.5t48.5 33.5t58.5 11.5q30 0 58 -12t49 -34.5t33.5 -53.5t12.5 -70z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="604" 
d="M168 221h388q1 9 1.5 17t0.5 16q0 57 -16 109t-47.5 91t-79 62t-110.5 23q-57 0 -104 -22t-81.5 -59.5t-53.5 -87.5t-19 -107q0 -62 21 -112.5t57.5 -86.5t86 -56t106.5 -20q73 0 125.5 27t90.5 72l-71 63q-32 -31 -65 -47t-78 -16q-59 0 -100 34.5t-52 99.5zM167 301
q8 62 44.5 100.5t92.5 38.5q30 0 53.5 -11t40.5 -29.5t27 -44t13 -54.5h-271z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="376" 
d="M222 524v29q0 80 70 80q20 0 36.5 -3.5t34.5 -9.5v102q-20 6 -43 10t-55 4q-78 0 -120 -42.5t-42 -130.5v-38h-67v-101h67v-424h121v424h138v100h-140z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="670" 
d="M595 108v420h-121v-79q-31 37 -74.5 63.5t-110.5 26.5q-46 0 -89.5 -17t-77 -49t-53.5 -78t-20 -105t20 -105t53.5 -78t76.5 -48.5t90 -16.5q65 0 109 27t77 70v-41q0 -80 -43 -121t-124 -41q-54 0 -100 15t-88 41l-45 -91q104 -63 236 -63q136 0 210 65t74 205zM476 290
q0 -33 -12.5 -59.5t-33.5 -46t-49.5 -30t-59.5 -10.5t-58.5 10.5t-47.5 30t-32 46.5t-12 60t11.5 60t32 46t47.5 29.5t59 10.5q31 0 59.5 -10.5t49.5 -29.5t33.5 -46.5t12.5 -60.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="620" 
d="M197 298q0 62 33 96.5t86 34.5q54 0 83.5 -33.5t29.5 -95.5v-300h121v336q0 93 -49 148t-139 55q-31 0 -55.5 -7.5t-45 -20.5t-36 -29.5t-28.5 -35.5v284h-121v-730h121v298z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="286" 
d="M208 609v115h-130v-115h130zM204 0v528h-121v-528h121z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="286" 
d="M208 609v115h-130v-115h130zM83 528v-528q0 -35 -15.5 -49.5t-41.5 -14.5q-16 0 -34 3v-97q14 -2 27.5 -3.5t30.5 -1.5q75 0 114.5 37.5t39.5 118.5v535h-121z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="582" 
d="M353 313l210 215h-147l-219 -234v436h-121v-730h121v151l74 77l159 -228h140z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="286" 
d="M204 0v730h-121v-730h121z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="950" 
d="M515 446q-21 42 -60.5 67.5t-96.5 25.5q-30 0 -54.5 -7.5t-44 -20.5t-34.5 -29t-28 -34v80h-121v-528h121v297q0 62 31 97t82 35t79 -33t28 -95v-301h121v298q0 64 31.5 97.5t81.5 33.5q51 0 79 -33t28 -96v-300h121v337q0 97 -49 149.5t-136 52.5q-60 0 -103.5 -25.5
t-75.5 -67.5z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="620" 
d="M197 298q0 62 33 96.5t86 34.5q54 0 83.5 -33.5t29.5 -95.5v-300h121v336q0 93 -49 148t-139 55q-31 0 -55.5 -7.5t-45 -20.5t-36 -29.5t-28.5 -35.5v82h-121v-528h121v298z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="657" 
d="M610 264q0 57 -21 107t-58.5 87.5t-89 59t-112.5 21.5t-112.5 -22t-89 -59.5t-59 -88t-21.5 -107.5t21 -106.5t58.5 -87t89 -59t111.5 -21.5q61 0 113 22t89.5 59.5t59 87.5t21.5 107zM489 262q0 -35 -11 -65.5t-32 -53.5t-50.5 -36.5t-66.5 -13.5q-35 0 -64.5 13.5
t-51 36.5t-33.5 54.5t-12 66.5t11 65.5t32 53.5t50.5 36.5t65.5 13.5t65.5 -13.5t51 -37t33.5 -54.5t12 -66z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="670" 
d="M622 264q0 66 -21 117t-55.5 86.5t-78.5 53.5t-91 18q-64 0 -107 -28.5t-72 -70.5v88h-121v-688h121v242q28 -37 71 -65t108 -28q47 0 91.5 18t79 53.5t55 86.5t20.5 117zM499 264q0 -39 -12 -70.5t-32.5 -53.5t-48 -34t-58.5 -12t-59 12t-49 34.5t-33.5 53.5t-12.5 70
t12.5 70t33.5 53.5t49 34.5t59 12t58.5 -12t48 -34t32.5 -53.5t12 -70.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="670" 
d="M595 -160v688h-121v-82q-29 37 -72 65t-108 28q-47 0 -91.5 -18t-78.5 -53.5t-54.5 -86.5t-20.5 -117t21 -117t55 -86.5t78 -53.5t91 -18q64 0 107.5 29t72.5 70v-248h121zM476 264q0 -39 -12.5 -70t-33.5 -53.5t-49 -34.5t-58 -12q-31 0 -58.5 12t-48.5 34t-33 53.5
t-12 70.5q0 40 12 71.5t33 53.5t48.5 33.5t58.5 11.5q30 0 58 -12t49 -34.5t33.5 -53.5t12.5 -70z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="413" 
d="M197 201q0 52 13 91.5t37 65.5t57 39t73 13h7v128q-70 3 -116 -33t-71 -96v119h-121v-528h121v201z" />
    <glyph glyph-name="s" unicode="s" 
d="M453 156v2q0 36 -15 61t-38.5 42.5t-52.5 29.5t-58 21q-23 7 -44.5 14.5t-38 16t-26.5 19t-10 24.5v2q0 23 19.5 38t54.5 15q33 0 72 -13.5t77 -36.5l48 86q-42 28 -93 44t-101 16q-39 0 -73.5 -11t-60 -32t-40 -51t-14.5 -67v-2q0 -38 15 -63.5t38.5 -43t53 -28.5
t58.5 -20q23 -7 44.5 -13.5t38 -15t26 -19.5t9.5 -26v-2q0 -26 -21.5 -41.5t-60.5 -15.5q-41 0 -85.5 16.5t-87.5 48.5l-54 -82q50 -39 108.5 -59t115.5 -20q41 0 77 10.5t62.5 31t41.5 51.5t15 73z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="410" 
d="M222 424h142v104h-142v145h-121v-145h-67v-104h67v-278q0 -44 11 -73.5t31.5 -47.5t48.5 -26t61 -8q34 0 60 7t49 20v99q-36 -18 -75 -18q-31 0 -48 15.5t-17 50.5v259z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="620" 
d="M423 230q0 -62 -33 -96.5t-86 -34.5q-54 0 -83.5 33.5t-29.5 95.5v300h-121v-336q0 -93 49 -148t139 -55q61 0 100 27.5t65 65.5v-82h121v528h-121v-298z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="595" 
d="M298 140l-142 388h-130l217 -532h109l216 532h-127z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="861" 
d="M482 530h-103l-117 -364l-106 362h-124l171 -532h108l119 361l118 -361h108l173 532h-121l-108 -362z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="580" 
d="M355 271l187 257h-129l-122 -174l-122 174h-132l187 -260l-194 -268h129l129 185l130 -185h132z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="596" 
d="M568 528h-126l-137 -392l-150 392h-129l220 -528q-15 -35 -32 -48t-43 -13q-19 0 -36.5 5.5t-34.5 14.5l-41 -90q28 -15 57.5 -23.5t67.5 -8.5q32 0 57 8t46 25.5t38 45.5t33 69z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="560" 
d="M357 429l-305 -346v-83h459v99h-305l305 346v83h-448v-99h294z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="487" 
d="M437 635l-21 80q-68 -12 -112 -30.5t-69.5 -43.5t-35.5 -56t-10 -70q0 -6 0.5 -15.5t0.5 -20.5q0 -10 0.5 -20t0.5 -16q0 -59 -25 -84.5t-88 -25.5h-26v-92h26q63 0 88 -25.5t25 -84.5q0 -6 -0.5 -16t-0.5 -21q0 -10 -0.5 -19.5t-0.5 -15.5q0 -39 10 -70.5t35.5 -56
t69.5 -43t112 -30.5l21 80q-45 12 -72 25t-41 28.5t-18.5 36t-4.5 47.5q0 15 1 36.5t1 37.5q0 59 -26.5 89.5t-77.5 47.5q50 16 77 47t27 90q0 16 -1 37.5t-1 36.5q0 27 4.5 47.5t18.5 36t41 28.5t72 25z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="331" 
d="M214 -128v926h-97v-926h97z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="487" 
d="M50 -61l21 -80q68 12 112 30.5t69.5 43t35.5 56t10 70.5q0 6 -0.5 15.5t-0.5 19.5q0 11 -0.5 21t-0.5 16q0 59 25 84.5t88 25.5h26v92h-26q-63 0 -88 25.5t-25 84.5q0 6 0.5 16t0.5 20q0 11 0.5 20.5t0.5 15.5q0 39 -10 70t-35.5 56t-69.5 43.5t-112 30.5l-21 -80
q45 -12 72 -25t41 -28.5t18.5 -36t4.5 -47.5q0 -15 -1 -36.5t-1 -37.5q0 -59 26.5 -89.5t77.5 -47.5q-50 -16 -77 -47t-27 -90q0 -16 1 -37.5t1 -36.5q0 -27 -4.5 -47.5t-18.5 -36t-41 -28.5t-72 -25z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="495" 
d="M436 364l-67 20q-11 -26 -22 -38t-33 -12q-13 0 -29.5 6t-34.5 13.5t-38 14t-41 6.5q-46 0 -70.5 -32t-41.5 -92l67 -21q11 26 22 38t33 12q13 0 29.5 -6.5t34.5 -14t38 -13.5t41 -6q46 0 70.5 32t41.5 93z" />
    <glyph glyph-name="nonbreakingspace" unicode="&#xa0;" horiz-adv-x="300" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="302" 
d="M85 700v-137h133v137h-133zM222 0v35l-37 444h-67l-38 -444v-35h142z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="581" 
d="M538 181l-70 69q-26 -26 -57.5 -44t-69.5 -20q-7 0 -10 1l63 323q20 -8 36.5 -20.5t33.5 -28.5l73 78q-24 26 -52 46.5t-66 31.5l18 89h-88l-15 -76h-9q-59 0 -109 -22t-86.5 -59.5t-57 -87.5t-20.5 -107q0 -45 13 -85.5t36.5 -74.5t56.5 -59.5t72 -39.5l-20 -101h88
l16 87q4 -1 12 -1q72 1 122.5 28.5t89.5 72.5zM317 525l-62 -322q-39 21 -62 61.5t-23 91.5q0 34 11 64t30 53t45.5 36.5t58.5 15.5h2z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="644" 
d="M142 382h-79v-104h79v-176l-79 -24v-78h528v107h-325v171h249v104h-249v89q0 58 28 91.5t79 33.5q42 0 70.5 -19t55.5 -54l94 75q-18 24 -39 44.5t-47.5 35.5t-59.5 23.5t-75 8.5q-110 0 -170 -63.5t-60 -172.5v-92z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="720" 
d="M130 395q-2 -11 -2 -23v-23v-20q0 -9 1 -19h-77v-85h91q14 -53 40.5 -96.5t63.5 -75t84 -48.5t103 -17q43 0 78 10.5t64.5 29t54 44.5t45.5 57l-88 64q-17 -23 -34 -40.5t-35 -29.5t-38.5 -18.5t-45.5 -6.5q-59 0 -101 34t-62 93h216v85h-234q-1 11 -1.5 20.5t-0.5 20.5
t0.5 22t1.5 22h234v85h-214q20 57 59.5 89.5t93.5 32.5q52 0 87 -23.5t67 -67.5l93 69q-20 28 -43.5 52.5t-52.5 42t-65.5 27.5t-82.5 10q-53 0 -98.5 -17.5t-81.5 -48t-62.5 -73t-41.5 -93.5h-94v-85h78z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="686" 
d="M402 264h193v89h-160l232 347h-139l-183 -293l-183 293h-143l231 -347h-159v-89h193v-73h-193v-89h193v-102h118v102h193v89h-193v73z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="632" 
d="M574 350q0 55 -50 88t-166 59q-41 9 -66.5 17.5t-40 16.5t-20 17t-5.5 20q0 22 19.5 36t61.5 14q48 0 87 -19.5t74 -51.5l69 68q-42 45 -99.5 70t-131.5 25q-93 0 -144.5 -40t-51.5 -107q0 -61 55 -96q-48 -13 -77.5 -41t-29.5 -76q0 -28 12 -49.5t38 -38.5t67 -31.5
t99 -27.5q40 -9 66 -17.5t40.5 -16.5t20 -17t5.5 -20q0 -22 -19.5 -36t-61.5 -14q-48 0 -87 19.5t-74 51.5l-69 -68q42 -45 99.5 -70t131.5 -25q93 0 144.5 40t51.5 107q0 61 -55 96q48 13 77.5 41t29.5 76zM465 333q0 -23 -20.5 -39t-56.5 -16q-17 0 -41.5 4t-62.5 14
q-64 16 -90.5 32t-26.5 39t20.5 39t56.5 16q17 0 41 -4t63 -14q64 -16 90.5 -32t26.5 -39z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" 
d="M411 595v118h-123v-118h123zM212 595v118h-123v-118h123z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="830" 
d="M777 351q0 74 -28 140t-76.5 115t-114.5 77.5t-142 28.5t-142.5 -29t-115.5 -78.5t-77 -115.5t-28 -140t28 -140t76.5 -115t114.5 -77.5t142 -28.5t142.5 29t115.5 78.5t77 115.5t28 140zM739 351q0 -68 -25 -127.5t-68.5 -103.5t-103 -70t-128.5 -26t-128 25.5
t-102.5 69.5t-68 103t-24.5 127q0 67 25 127t68.5 104t103 70t128.5 26t128 -25.5t102.5 -69.5t68 -103.5t24.5 -126.5zM573 227l-48 47q-23 -22 -46.5 -34.5t-57.5 -12.5q-26 0 -47.5 10t-37 27.5t-24.5 41t-9 49.5q0 27 8.5 50t24.5 40.5t37.5 27.5t47.5 10q30 0 55 -12.5
t47 -32.5l47 54q-27 26 -61 42.5t-88 16.5q-43 0 -78.5 -15.5t-61 -42.5t-40 -63t-14.5 -76q0 -41 14.5 -76.5t40 -62t61 -41.5t76.5 -15q54 0 89 18t65 50z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="390" 
d="M319 409v172q0 59 -31.5 90.5t-95.5 31.5q-36 0 -61 -6.5t-51 -18.5l19 -54q21 8 40.5 13.5t42.5 5.5q70 0 70 -61v-5q-17 5 -34.5 8t-41.5 3q-54 0 -87 -23t-33 -71q0 -23 8.5 -40t22.5 -28t32.5 -17t39.5 -6q32 0 54 11t38 29v-34h68zM323 282v58h-270v-58h270z
M252 514q0 -26 -21.5 -43t-53.5 -17q-23 0 -37.5 11t-14.5 32q0 22 17 34t47 12q20 0 35 -3t28 -8v-18z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="606" 
d="M461 488l-165 -220v-8l165 -219l92 47l-132 177l132 176zM210 488l-166 -220v-8l166 -219l92 47l-132 177l132 176z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="830" 
d="M777 351q0 74 -28 140t-76.5 115t-114.5 77.5t-142 28.5t-142.5 -29t-115.5 -78.5t-77 -115.5t-28 -140t28 -140t76.5 -115t114.5 -77.5t142 -28.5t142.5 29t115.5 78.5t77 115.5t28 140zM739 351q0 -68 -25 -127.5t-68.5 -103.5t-103 -70t-128.5 -26t-128 25.5
t-102.5 69.5t-68 103t-24.5 127q0 67 25 127t68.5 104t103 70t128.5 26t128 -25.5t102.5 -69.5t68 -103.5t24.5 -126.5zM584 429q0 57 -40 89t-106 32h-169v-379h72v131h79l92 -131h86l-102 143q39 11 63.5 39.5t24.5 75.5zM510 425q0 -28 -20 -44t-55 -16h-94v120h94
q35 0 55 -15.5t20 -44.5z" />
    <glyph glyph-name="macron" unicode="&#xaf;" 
d="M421 600v89h-342v-89h342z" />
    <glyph glyph-name="macron" unicode="&#x2c9;" 
d="M421 600v89h-342v-89h342z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="471" 
d="M402 544q0 33 -13.5 62t-37 51.5t-53.5 35.5t-63 13t-63 -13t-53 -35.5t-36.5 -51.5t-13.5 -62t13.5 -62.5t36.5 -52t53 -35.5t63 -13t63 13t53.5 35.5t37 52t13.5 62.5zM330 544q0 -20 -7 -37.5t-20 -30.5t-30 -21t-38 -8q-20 0 -37.5 8t-30 21t-19.5 30.5t-7 37.5
q0 19 7 36.5t19.5 30.5t30 21t37.5 8q21 0 38 -8t30 -21t20 -30.5t7 -36.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="610" 
d="M560 200q0 32 -9.5 57t-26 44.5t-39 33.5t-48.5 24q21 10 40 25t33.5 35t22.5 46t8 59q0 41 -17.5 75.5t-49 59.5t-76 39t-98.5 14q-44 0 -79.5 -9t-65 -26t-55 -41t-47.5 -53l86 -70q35 41 71.5 64.5t87.5 23.5q57 0 89 -26.5t32 -68.5q0 -46 -35.5 -73.5t-96.5 -27.5
h-78v-104h75q73 0 113.5 -25.5t40.5 -74.5q0 -47 -33.5 -74.5t-91.5 -27.5q-61 0 -106.5 24t-82.5 66l-85 -79q48 -55 117 -88.5t155 -33.5q58 0 104.5 16t78.5 44.5t49 67.5t17 84z" />
    <glyph glyph-name="acute" unicode="&#xb4;" 
d="M254 595l122 115l-110 49l-106 -164h94z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="850" 
d="M791 351q0 74 -27 140t-75 115t-115 77.5t-148 28.5t-148 -29t-116 -78.5t-76 -115.5t-27 -140t27 -140t75 -115t115 -77.5t148 -28.5q65 0 126 22l116 -111h180v8l-175 174q57 51 88.5 120.5t31.5 149.5zM662 349q0 -51 -17.5 -96.5t-48.5 -79t-74.5 -53t-95.5 -19.5
t-96 20t-75.5 54t-49 79.5t-17.5 96.5t17.5 96.5t48.5 79t74.5 53t95.5 19.5t96 -20t75.5 -54t49 -79.5t17.5 -96.5z" />
    <glyph glyph-name="mu" unicode="&#x3bc;" horiz-adv-x="850" 
d="M791 351q0 74 -27 140t-75 115t-115 77.5t-148 28.5t-148 -29t-116 -78.5t-76 -115.5t-27 -140t27 -140t75 -115t115 -77.5t148 -28.5q65 0 126 22l116 -111h180v8l-175 174q57 51 88.5 120.5t31.5 149.5zM662 349q0 -51 -17.5 -96.5t-48.5 -79t-74.5 -53t-95.5 -19.5
t-96 20t-75.5 54t-49 79.5t-17.5 96.5t17.5 96.5t48.5 79t74.5 53t95.5 19.5t96 -20t75.5 -54t49 -79.5t17.5 -96.5z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="557" 
d="M311 700q-60 0 -110 -15.5t-85.5 -45t-55.5 -71.5t-20 -96q0 -56 22 -99t60.5 -71.5t91 -43t114.5 -14.5h18v-244h124v700h-159z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="269" 
d="M201 238v137h-133v-137h133z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" 
d="M221 10l-90 -131l106 -44l75 175h-91z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="390" 
d="M346 554q0 31 -11.5 59t-31.5 48t-47.5 32t-59.5 12q-33 0 -60.5 -12t-48 -32.5t-32 -48.5t-11.5 -59q0 -32 11.5 -59.5t31.5 -47.5t47.5 -32t59.5 -12q33 0 60.5 12t48 32.5t32 48t11.5 59.5zM339 282v58h-288v-58h288zM276 553q0 -37 -21.5 -63t-58.5 -26
q-36 0 -59 26.5t-23 63.5t21.5 63t58.5 26q36 0 59 -26.5t23 -63.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="606" 
d="M396 41l166 219v8l-166 220l-92 -48l132 -177l-132 -176zM145 41l165 219v8l-165 220l-92 -48l132 -177l-132 -176z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="541" 
d="M229 700v-137h134v137h-134zM349 312l5 5l-20 162h-81l-10 -81q-42 -7 -78.5 -22t-63.5 -39t-43 -59t-16 -83q0 -47 17 -85t47.5 -64.5t73.5 -40.5t96 -14q81 0 139 30.5t102 80.5l-75 80q-36 -38 -75.5 -58.5t-87.5 -20.5q-54 0 -84.5 26.5t-30.5 69.5q0 51 44.5 80
t140.5 33z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="790" 
d="M452 705h-114l-308 -705h126l72 169h331l71 -169h130zM514 278h-241l120 280zM351 758h94l-106 164l-110 -49z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="790" 
d="M452 705h-114l-308 -705h126l72 169h331l71 -169h130zM514 278h-241l120 280zM439 758l122 115l-110 49l-106 -164h94z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="790" 
d="M452 705h-114l-308 -705h126l72 169h331l71 -169h130zM514 278h-241l120 280zM394 824l77 -67h96l-116 146h-112l-116 -146h94z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="790" 
d="M452 705h-114l-308 -705h126l72 169h331l71 -169h130zM514 278h-241l120 280zM584 882l-67 21q-11 -26 -22 -38t-33 -12q-14 0 -30 6t-34.5 13.5t-38.5 14t-41 6.5q-46 0 -70.5 -32t-41.5 -93l67 -20q11 26 22 38t33 12q13 0 29.5 -6.5t35 -14t38.5 -13.5t41 -6
q46 0 70.5 32t41.5 92z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="790" 
d="M452 705h-114l-308 -705h126l72 169h331l71 -169h130zM514 278h-241l120 280zM556 758v118h-123v-118h123zM357 758v118h-123v-118h123z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="790" 
d="M328 684l-298 -684h126l72 169h331l71 -169h130l-298 684q20 15 33.5 37.5t13.5 50.5q0 22 -9.5 42t-25 35t-36.5 24t-43 9q-23 0 -43.5 -9t-36 -24t-25 -35t-9.5 -42q0 -28 13.5 -50.5t33.5 -37.5zM457 772q0 -26 -18 -44t-44 -18t-44 18t-18 44t18 44t44 18t44 -18
t18 -44zM514 278h-241l120 280z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1045" 
d="M232 169h255v-169h502v110h-379v188h329v110h-329v182h374v110h-581l-391 -700h127zM467 593h20v-315h-196z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="738" 
d="M345 -5l-79 -116l105 -44l66 154q45 2 82 11.5t68.5 25.5t58.5 37.5t53 48.5l-79 80q-45 -42 -91 -66.5t-110 -24.5q-50 0 -92.5 19.5t-73.5 53.5t-48 79.5t-17 97.5t17 97t48 78.5t73.5 53t92.5 19.5q60 0 107 -24t89 -63l79 91q-25 24 -53 44t-61 34.5t-72.5 22.5
t-87.5 8q-80 0 -146 -28.5t-114 -78t-74.5 -115.5t-26.5 -141q0 -67 21 -126t59 -106t90.5 -78.5t115.5 -43.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="670" 
d="M609 700h-519v-700h524v110h-401v188h351v110h-351v182h396v110zM327 758h94l-106 164l-110 -49z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="670" 
d="M609 700h-519v-700h524v110h-401v188h351v110h-351v182h396v110zM371 758l122 115l-110 49l-106 -164h94z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="670" 
d="M609 700h-519v-700h524v110h-401v188h351v110h-351v182h396v110zM346 825l77 -67h96l-116 146h-112l-116 -146h94z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="670" 
d="M609 700h-519v-700h524v110h-401v188h351v110h-351v182h396v110zM508 758v118h-123v-118h123zM309 758v118h-123v-118h123z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="317" 
d="M97 700v-700h123v700h-123zM220 758h94l-106 164l-110 -49z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="317" 
d="M97 700v-700h123v700h-123zM203 758l122 115l-110 49l-106 -164h94z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="317" 
d="M97 700v-700h123v700h-123zM268 825l77 -67h96l-116 146h-112l-116 -146h94z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="317" 
d="M97 700v-700h123v700h-123zM419 758v118h-123v-118h123zM220 758v118h-123v-118h123z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="812" 
d="M120 297v-297h261q82 0 150.5 27t118 74t76.5 111.5t27 138.5t-27 138t-76.5 111t-118 73.5t-150.5 26.5h-261v-293h-75v-110h75zM242 407v181h139q55 0 100 -18t76.5 -50t49 -75.5t17.5 -95.5t-17.5 -95.5t-49 -75t-76.5 -49t-100 -17.5h-139v185h176v110h-176z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="790" 
d="M579 216l-375 484h-114v-700h121v498l386 -498h103v700h-121v-484zM584 882l-67 21q-11 -26 -22 -38t-33 -12q-14 0 -30 6t-34.5 13.5t-38.5 14t-41 6.5q-46 0 -70.5 -32t-41.5 -93l67 -20q11 26 22 38t33 12q13 0 29.5 -6.5t35 -14t38.5 -13.5t41 -6q46 0 70.5 32
t41.5 92z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="850" 
d="M791 351q0 74 -27 140t-75 115t-115 77.5t-148 28.5t-148 -29t-116 -78.5t-76 -115.5t-27 -140t27 -140t75 -115t115 -77.5t148 -28.5t148 29t116 78.5t76 115.5t27 140zM662 349q0 -51 -17.5 -96.5t-48.5 -79t-74.5 -53t-95.5 -19.5t-96 20t-75.5 54t-49 79.5
t-17.5 96.5t17.5 96.5t48.5 79t74.5 53t95.5 19.5t96 -20t75.5 -54t49 -79.5t17.5 -96.5zM374 758h94l-106 164l-110 -49z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="850" 
d="M791 351q0 74 -27 140t-75 115t-115 77.5t-148 28.5t-148 -29t-116 -78.5t-76 -115.5t-27 -140t27 -140t75 -115t115 -77.5t148 -28.5t148 29t116 78.5t76 115.5t27 140zM662 349q0 -51 -17.5 -96.5t-48.5 -79t-74.5 -53t-95.5 -19.5t-96 20t-75.5 54t-49 79.5
t-17.5 96.5t17.5 96.5t48.5 79t74.5 53t95.5 19.5t96 -20t75.5 -54t49 -79.5t17.5 -96.5zM476 758l122 115l-110 49l-106 -164h94z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="850" 
d="M791 351q0 74 -27 140t-75 115t-115 77.5t-148 28.5t-148 -29t-116 -78.5t-76 -115.5t-27 -140t27 -140t75 -115t115 -77.5t148 -28.5t148 29t116 78.5t76 115.5t27 140zM662 349q0 -51 -17.5 -96.5t-48.5 -79t-74.5 -53t-95.5 -19.5t-96 20t-75.5 54t-49 79.5
t-17.5 96.5t17.5 96.5t48.5 79t74.5 53t95.5 19.5t96 -20t75.5 -54t49 -79.5t17.5 -96.5zM424 825l77 -67h96l-116 146h-112l-116 -146h94z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="850" 
d="M791 351q0 74 -27 140t-75 115t-115 77.5t-148 28.5t-148 -29t-116 -78.5t-76 -115.5t-27 -140t27 -140t75 -115t115 -77.5t148 -28.5t148 29t116 78.5t76 115.5t27 140zM662 349q0 -51 -17.5 -96.5t-48.5 -79t-74.5 -53t-95.5 -19.5t-96 20t-75.5 54t-49 79.5
t-17.5 96.5t17.5 96.5t48.5 79t74.5 53t95.5 19.5t96 -20t75.5 -54t49 -79.5t17.5 -96.5zM614 882l-67 21q-11 -26 -22 -38t-33 -12q-14 0 -30 6t-34.5 13.5t-38.5 14t-41 6.5q-46 0 -70.5 -32t-41.5 -93l67 -20q11 26 22 38t33 12q13 0 29.5 -6.5t35 -14t38.5 -13.5t41 -6
q46 0 70.5 32t41.5 92z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="850" 
d="M791 351q0 74 -27 140t-75 115t-115 77.5t-148 28.5t-148 -29t-116 -78.5t-76 -115.5t-27 -140t27 -140t75 -115t115 -77.5t148 -28.5t148 29t116 78.5t76 115.5t27 140zM662 349q0 -51 -17.5 -96.5t-48.5 -79t-74.5 -53t-95.5 -19.5t-96 20t-75.5 54t-49 79.5
t-17.5 96.5t17.5 96.5t48.5 79t74.5 53t95.5 19.5t96 -20t75.5 -54t49 -79.5t17.5 -96.5zM586 758v118h-123v-118h123zM387 758v118h-123v-118h123z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="850" 
d="M791 351q0 70 -23.5 132t-67.5 110l99 112h-121l-48 -53q-43 29 -94 44.5t-110 15.5q-81 0 -148 -29t-116 -78.5t-76 -115.5t-27 -140q0 -70 23.5 -132t67.5 -110l-99 -112h121l48 53q43 -29 94 -44.5t110 -15.5q81 0 148 29t116 78.5t76 115.5t27 140zM552 562
l-320 -361q-23 32 -35 70t-12 80q0 51 17.5 96.5t49.5 79.5t75.5 54t96.5 20q37 0 69.5 -10.5t58.5 -28.5zM665 349q0 -51 -17.5 -96.5t-49.5 -79.5t-76 -54t-96 -20q-37 0 -69.5 10.5t-58.5 28.5l320 361q23 -32 35 -70t12 -80z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="757" 
d="M678 700h-123v-403q0 -97 -47 -146t-129 -49q-83 0 -130 51t-47 149v398h-123v-403q0 -76 21 -133.5t60 -96.5t94 -58.5t123 -19.5q69 0 124.5 19.5t94.5 59t60.5 98.5t21.5 137v397zM341 753h94l-106 164l-110 -49z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="757" 
d="M678 700h-123v-403q0 -97 -47 -146t-129 -49q-83 0 -130 51t-47 149v398h-123v-403q0 -76 21 -133.5t60 -96.5t94 -58.5t123 -19.5q69 0 124.5 19.5t94.5 59t60.5 98.5t21.5 137v397zM416 753l122 115l-110 49l-106 -164h94z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="757" 
d="M678 700h-123v-403q0 -97 -47 -146t-129 -49q-83 0 -130 51t-47 149v398h-123v-403q0 -76 21 -133.5t60 -96.5t94 -58.5t123 -19.5q69 0 124.5 19.5t94.5 59t60.5 98.5t21.5 137v397zM378 824l77 -67h96l-116 146h-112l-116 -146h94z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="757" 
d="M678 700h-123v-403q0 -97 -47 -146t-129 -49q-83 0 -130 51t-47 149v398h-123v-403q0 -76 21 -133.5t60 -96.5t94 -58.5t123 -19.5q69 0 124.5 19.5t94.5 59t60.5 98.5t21.5 137v397zM540 757v118h-123v-118h123zM341 757v118h-123v-118h123z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="716" 
d="M420 279l278 421h-141l-198 -310l-196 310h-145l278 -424v-276h124v279zM405 753l122 115l-110 49l-106 -164h94z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="673" 
d="M632 356q0 53 -18.5 96.5t-53 74t-84 47t-110.5 16.5h-153v110h-123v-700h123v116h139q58 0 109 15t89 45t60 75t22 105zM507 353q0 -57 -40.5 -92t-110.5 -35h-143v253h143q69 0 110 -31.5t41 -94.5z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="620" 
d="M529 548q0 40 -14.5 74.5t-42 60t-67.5 40t-91 14.5q-55 0 -99 -16.5t-75 -47t-47.5 -74t-16.5 -97.5v-502h120v506q0 59 31 93.5t81 34.5q46 0 73.5 -26t27.5 -71q0 -50 -32.5 -83.5t-80.5 -56.5v-82q69 -8 111.5 -36.5t42.5 -78.5q0 -52 -40.5 -79.5t-113.5 -29.5v-93
q63 -2 114 10.5t86.5 38t55 63.5t19.5 87q0 38 -12.5 67t-33.5 50.5t-48.5 36.5t-57.5 25q22 12 42 27.5t35 36.5t24 47.5t9 60.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="587" 
d="M519 0v313q0 106 -57 164.5t-175 58.5q-65 0 -111 -12t-90 -32l33 -97q36 15 71 24t80 9q63 0 96.5 -29t33.5 -84v-13q-31 10 -64.5 16t-81.5 6q-46 0 -85.5 -10.5t-68 -31.5t-44.5 -53t-16 -75v-2q0 -40 15.5 -70.5t41 -51t59.5 -31t72 -10.5q60 0 102 22t69 54v-65h120
zM402 191q0 -25 -10.5 -45.5t-29.5 -35t-44.5 -22.5t-55.5 -8q-43 0 -72.5 20t-29.5 58v2q0 41 32 63t89 22q35 0 66.5 -6t54.5 -15v-33zM257 595h94l-106 164l-110 -49z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="587" 
d="M519 0v313q0 106 -57 164.5t-175 58.5q-65 0 -111 -12t-90 -32l33 -97q36 15 71 24t80 9q63 0 96.5 -29t33.5 -84v-13q-31 10 -64.5 16t-81.5 6q-46 0 -85.5 -10.5t-68 -31.5t-44.5 -53t-16 -75v-2q0 -40 15.5 -70.5t41 -51t59.5 -31t72 -10.5q60 0 102 22t69 54v-65h120
zM402 191q0 -25 -10.5 -45.5t-29.5 -35t-44.5 -22.5t-55.5 -8q-43 0 -72.5 20t-29.5 58v2q0 41 32 63t89 22q35 0 66.5 -6t54.5 -15v-33zM329 595l122 115l-110 49l-106 -164h94z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="587" 
d="M519 0v313q0 106 -57 164.5t-175 58.5q-65 0 -111 -12t-90 -32l33 -97q36 15 71 24t80 9q63 0 96.5 -29t33.5 -84v-13q-31 10 -64.5 16t-81.5 6q-46 0 -85.5 -10.5t-68 -31.5t-44.5 -53t-16 -75v-2q0 -40 15.5 -70.5t41 -51t59.5 -31t72 -10.5q60 0 102 22t69 54v-65h120
zM402 191q0 -25 -10.5 -45.5t-29.5 -35t-44.5 -22.5t-55.5 -8q-43 0 -72.5 20t-29.5 58v2q0 41 32 63t89 22q35 0 66.5 -6t54.5 -15v-33zM292 662l77 -67h96l-116 146h-112l-116 -146h94z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="587" 
d="M519 0v313q0 106 -57 164.5t-175 58.5q-65 0 -111 -12t-90 -32l33 -97q36 15 71 24t80 9q63 0 96.5 -29t33.5 -84v-13q-31 10 -64.5 16t-81.5 6q-46 0 -85.5 -10.5t-68 -31.5t-44.5 -53t-16 -75v-2q0 -40 15.5 -70.5t41 -51t59.5 -31t72 -10.5q60 0 102 22t69 54v-65h120
zM402 191q0 -25 -10.5 -45.5t-29.5 -35t-44.5 -22.5t-55.5 -8q-43 0 -72.5 20t-29.5 58v2q0 41 32 63t89 22q35 0 66.5 -6t54.5 -15v-33zM483 719l-67 21q-11 -26 -22 -38t-33 -12q-14 0 -30 6t-34.5 13.5t-38.5 14t-41 6.5q-46 0 -70.5 -32t-41.5 -93l67 -20q11 26 22 38
t33 12q13 0 29.5 -6.5t35 -14t38.5 -13.5t41 -6q46 0 70.5 32t41.5 92z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="587" 
d="M519 0v313q0 106 -57 164.5t-175 58.5q-65 0 -111 -12t-90 -32l33 -97q36 15 71 24t80 9q63 0 96.5 -29t33.5 -84v-13q-31 10 -64.5 16t-81.5 6q-46 0 -85.5 -10.5t-68 -31.5t-44.5 -53t-16 -75v-2q0 -40 15.5 -70.5t41 -51t59.5 -31t72 -10.5q60 0 102 22t69 54v-65h120
zM402 191q0 -25 -10.5 -45.5t-29.5 -35t-44.5 -22.5t-55.5 -8q-43 0 -72.5 20t-29.5 58v2q0 41 32 63t89 22q35 0 66.5 -6t54.5 -15v-33zM454 595v118h-123v-118h123zM255 595v118h-123v-118h123z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="587" 
d="M519 0v313q0 106 -57 164.5t-175 58.5q-65 0 -111 -12t-90 -32l33 -97q36 15 71 24t80 9q63 0 96.5 -29t33.5 -84v-13q-31 10 -64.5 16t-81.5 6q-46 0 -85.5 -10.5t-68 -31.5t-44.5 -53t-16 -75v-2q0 -40 15.5 -70.5t41 -51t59.5 -31t72 -10.5q60 0 102 22t69 54v-65h120
zM402 191q0 -25 -10.5 -45.5t-29.5 -35t-44.5 -22.5t-55.5 -8q-43 0 -72.5 20t-29.5 58v2q0 41 32 63t89 22q35 0 66.5 -6t54.5 -15v-33zM407 703q0 22 -9.5 42t-25 35t-36.5 24t-43 9q-23 0 -43.5 -9t-36 -24t-25 -35t-9.5 -42q0 -23 9.5 -43t25 -35t36 -23.5t43.5 -8.5
q22 0 43 8.5t36.5 23.5t25 35t9.5 43zM355 703q0 -26 -18 -44t-44 -18t-44 18t-18 44t18 44t44 18t44 -18t18 -44z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="952" 
d="M461 78q36 -42 89 -66t115 -24q74 0 126.5 27t90.5 72l-71 63q-32 -31 -65.5 -47t-78.5 -16q-58 0 -99 34.5t-52 99.5h388q1 9 1 17v16q0 57 -15.5 109t-47 91t-78.5 62t-109 23q-59 0 -106 -24t-79 -66q-24 42 -70.5 64.5t-115.5 22.5q-63 0 -108.5 -12t-89.5 -32
l33 -97q36 15 71 24t80 9q63 0 96.5 -29t33.5 -84v-13q-31 10 -64.5 16t-81.5 6q-46 0 -85.5 -10.5t-68 -31.5t-44.5 -53t-16 -74q0 -40 14.5 -71t41 -52t63 -32t81.5 -11q67 0 121.5 26t99.5 63zM514 301q8 62 44.5 100.5t93.5 38.5q30 0 53.5 -11t40.5 -29.5t27 -44
t13 -54.5h-272zM397 227q2 -23 7 -42.5t13 -38.5q-33 -31 -71 -48.5t-84 -17.5q-43 0 -72.5 20.5t-29.5 59.5q0 41 32 63t89 22q35 0 64 -5t52 -13z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="571" 
d="M250 -3l-80 -118l106 -44l65 154q68 4 113 31t82 67l-73 72q-29 -29 -61.5 -47.5t-75.5 -18.5q-35 0 -64 13.5t-50 36.5t-32.5 54t-11.5 67q0 35 11.5 65.5t31.5 53.5t48 36.5t61 13.5q46 0 77.5 -18.5t59.5 -47.5l75 81q-37 41 -86.5 66t-124.5 25q-59 0 -109.5 -22
t-87 -60t-57 -88t-20.5 -107q0 -48 15 -91t41.5 -78t64 -60t82.5 -36z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="604" 
d="M168 221h388q1 9 1.5 17t0.5 16q0 57 -16 109t-47.5 91t-79 62t-110.5 23q-57 0 -104 -22t-81.5 -59.5t-53.5 -87.5t-19 -107q0 -62 21 -112.5t57.5 -86.5t86 -56t106.5 -20q73 0 125.5 27t90.5 72l-71 63q-32 -31 -65 -47t-78 -16q-59 0 -100 34.5t-52 99.5zM167 301
q8 62 44.5 100.5t92.5 38.5q30 0 53.5 -11t40.5 -29.5t27 -44t13 -54.5h-271zM261 595h94l-106 164l-110 -49z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="604" 
d="M168 221h388q1 9 1.5 17t0.5 16q0 57 -16 109t-47.5 91t-79 62t-110.5 23q-57 0 -104 -22t-81.5 -59.5t-53.5 -87.5t-19 -107q0 -62 21 -112.5t57.5 -86.5t86 -56t106.5 -20q73 0 125.5 27t90.5 72l-71 63q-32 -31 -65 -47t-78 -16q-59 0 -100 34.5t-52 99.5zM167 301
q8 62 44.5 100.5t92.5 38.5q30 0 53.5 -11t40.5 -29.5t27 -44t13 -54.5h-271zM343 595l122 115l-110 49l-106 -164h94z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="604" 
d="M168 221h388q1 9 1.5 17t0.5 16q0 57 -16 109t-47.5 91t-79 62t-110.5 23q-57 0 -104 -22t-81.5 -59.5t-53.5 -87.5t-19 -107q0 -62 21 -112.5t57.5 -86.5t86 -56t106.5 -20q73 0 125.5 27t90.5 72l-71 63q-32 -31 -65 -47t-78 -16q-59 0 -100 34.5t-52 99.5zM167 301
q8 62 44.5 100.5t92.5 38.5q30 0 53.5 -11t40.5 -29.5t27 -44t13 -54.5h-271zM302 662l77 -67h96l-116 146h-112l-116 -146h94z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="604" 
d="M168 221h388q1 9 1.5 17t0.5 16q0 57 -16 109t-47.5 91t-79 62t-110.5 23q-57 0 -104 -22t-81.5 -59.5t-53.5 -87.5t-19 -107q0 -62 21 -112.5t57.5 -86.5t86 -56t106.5 -20q73 0 125.5 27t90.5 72l-71 63q-32 -31 -65 -47t-78 -16q-59 0 -100 34.5t-52 99.5zM167 301
q8 62 44.5 100.5t92.5 38.5q30 0 53.5 -11t40.5 -29.5t27 -44t13 -54.5h-271zM464 595v118h-123v-118h123zM265 595v118h-123v-118h123z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="286" 
d="M204 0v528h-121v-528h121zM206 595h94l-106 164l-110 -49z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="286" 
d="M204 0v528h-121v-528h121zM186 595l122 115l-110 49l-106 -164h94z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="286" 
d="M204 0v528h-121v-528h121zM254 662l77 -67h96l-116 146h-112l-116 -146h94z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="286" 
d="M204 0v528h-121v-528h121zM406 595v118h-123v-118h123zM207 595v118h-123v-118h123z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="642" 
d="M313 588l105 -143q-32 20 -64 30.5t-75 10.5q-49 0 -91.5 -17t-73.5 -48.5t-49 -75t-18 -96.5q0 -54 20 -101.5t56 -83t85.5 -56t107.5 -20.5q65 0 116.5 22t87 59t54.5 86t19 103q0 34 -5.5 64t-16.5 59.5t-27.5 59t-38.5 61.5l-89 129l102 44l-47 63l-99 -43l-24 35
h-139l58 -79l-121 -53l48 -63zM474 246q0 -32 -11 -60t-31 -49t-48 -33.5t-63 -12.5q-34 0 -62.5 12.5t-49 34t-32 49.5t-11.5 59q0 32 10.5 59.5t30.5 48t47.5 32t62.5 11.5t64 -12t49.5 -32t32 -48t11.5 -59z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="620" 
d="M197 298q0 62 33 96.5t86 34.5q54 0 83.5 -33.5t29.5 -95.5v-300h121v336q0 93 -49 148t-139 55q-31 0 -55.5 -7.5t-45 -20.5t-36 -29.5t-28.5 -35.5v82h-121v-528h121v298zM503 719l-67 21q-11 -26 -22 -38t-33 -12q-14 0 -30 6t-34.5 13.5t-38.5 14t-41 6.5
q-46 0 -70.5 -32t-41.5 -93l67 -20q11 26 22 38t33 12q13 0 29.5 -6.5t35 -14t38.5 -13.5t41 -6q46 0 70.5 32t41.5 92z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="657" 
d="M610 264q0 57 -21 107t-58.5 87.5t-89 59t-112.5 21.5t-112.5 -22t-89 -59.5t-59 -88t-21.5 -107.5t21 -106.5t58.5 -87t89 -59t111.5 -21.5q61 0 113 22t89.5 59.5t59 87.5t21.5 107zM489 262q0 -35 -11 -65.5t-32 -53.5t-50.5 -36.5t-66.5 -13.5q-35 0 -64.5 13.5
t-51 36.5t-33.5 54.5t-12 66.5t11 65.5t32 53.5t50.5 36.5t65.5 13.5t65.5 -13.5t51 -37t33.5 -54.5t12 -66zM281 595h94l-106 164l-110 -49z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="657" 
d="M610 264q0 57 -21 107t-58.5 87.5t-89 59t-112.5 21.5t-112.5 -22t-89 -59.5t-59 -88t-21.5 -107.5t21 -106.5t58.5 -87t89 -59t111.5 -21.5q61 0 113 22t89.5 59.5t59 87.5t21.5 107zM489 262q0 -35 -11 -65.5t-32 -53.5t-50.5 -36.5t-66.5 -13.5q-35 0 -64.5 13.5
t-51 36.5t-33.5 54.5t-12 66.5t11 65.5t32 53.5t50.5 36.5t65.5 13.5t65.5 -13.5t51 -37t33.5 -54.5t12 -66zM375 595l122 115l-110 49l-106 -164h94z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="657" 
d="M610 264q0 57 -21 107t-58.5 87.5t-89 59t-112.5 21.5t-112.5 -22t-89 -59.5t-59 -88t-21.5 -107.5t21 -106.5t58.5 -87t89 -59t111.5 -21.5q61 0 113 22t89.5 59.5t59 87.5t21.5 107zM489 262q0 -35 -11 -65.5t-32 -53.5t-50.5 -36.5t-66.5 -13.5q-35 0 -64.5 13.5
t-51 36.5t-33.5 54.5t-12 66.5t11 65.5t32 53.5t50.5 36.5t65.5 13.5t65.5 -13.5t51 -37t33.5 -54.5t12 -66zM327 662l77 -67h96l-116 146h-112l-116 -146h94z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="657" 
d="M610 264q0 57 -21 107t-58.5 87.5t-89 59t-112.5 21.5t-112.5 -22t-89 -59.5t-59 -88t-21.5 -107.5t21 -106.5t58.5 -87t89 -59t111.5 -21.5q61 0 113 22t89.5 59.5t59 87.5t21.5 107zM489 262q0 -35 -11 -65.5t-32 -53.5t-50.5 -36.5t-66.5 -13.5q-35 0 -64.5 13.5
t-51 36.5t-33.5 54.5t-12 66.5t11 65.5t32 53.5t50.5 36.5t65.5 13.5t65.5 -13.5t51 -37t33.5 -54.5t12 -66zM517 719l-67 21q-11 -26 -22 -38t-33 -12q-14 0 -30 6t-34.5 13.5t-38.5 14t-41 6.5q-46 0 -70.5 -32t-41.5 -93l67 -20q11 26 22 38t33 12q13 0 29.5 -6.5t35 -14
t38.5 -13.5t41 -6q46 0 70.5 32t41.5 92z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="657" 
d="M610 264q0 57 -21 107t-58.5 87.5t-89 59t-112.5 21.5t-112.5 -22t-89 -59.5t-59 -88t-21.5 -107.5t21 -106.5t58.5 -87t89 -59t111.5 -21.5q61 0 113 22t89.5 59.5t59 87.5t21.5 107zM489 262q0 -35 -11 -65.5t-32 -53.5t-50.5 -36.5t-66.5 -13.5q-35 0 -64.5 13.5
t-51 36.5t-33.5 54.5t-12 66.5t11 65.5t32 53.5t50.5 36.5t65.5 13.5t65.5 -13.5t51 -37t33.5 -54.5t12 -66zM489 595v118h-123v-118h123zM290 595v118h-123v-118h123z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="657" 
d="M610 264q0 52 -17 97.5t-49 81.5l81 91h-110l-35 -38q-32 20 -70 31.5t-81 11.5q-61 0 -112.5 -22t-89 -59.5t-59 -88t-21.5 -107.5q0 -51 17 -96.5t48 -81.5l-80 -89h110l34 36q32 -20 70.5 -31.5t80.5 -11.5q61 0 113 22t89.5 59.5t59 87.5t21.5 107zM409 412
l-219 -240q-25 41 -25 92q0 36 11.5 67.5t33 54.5t51.5 36.5t66 13.5q47 0 82 -24zM492 262q0 -36 -11.5 -67t-33 -54.5t-51.5 -37t-67 -13.5q-23 0 -43 6.5t-37 17.5l218 241q25 -41 25 -93z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="620" 
d="M423 230q0 -62 -33 -96.5t-86 -34.5q-54 0 -83.5 33.5t-29.5 95.5v300h-121v-336q0 -93 49 -148t139 -55q61 0 100 27.5t65 65.5v-82h121v528h-121v-298zM275 594h94l-106 164l-110 -49z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="620" 
d="M423 230q0 -62 -33 -96.5t-86 -34.5q-54 0 -83.5 33.5t-29.5 95.5v300h-121v-336q0 -93 49 -148t139 -55q61 0 100 27.5t65 65.5v-82h121v528h-121v-298zM343 594l122 115l-110 49l-106 -164h94z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="620" 
d="M423 230q0 -62 -33 -96.5t-86 -34.5q-54 0 -83.5 33.5t-29.5 95.5v300h-121v-336q0 -93 49 -148t139 -55q61 0 100 27.5t65 65.5v-82h121v528h-121v-298zM309 662l77 -67h96l-116 146h-112l-116 -146h94z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="620" 
d="M423 230q0 -62 -33 -96.5t-86 -34.5q-54 0 -83.5 33.5t-29.5 95.5v300h-121v-336q0 -93 49 -148t139 -55q61 0 100 27.5t65 65.5v-82h121v528h-121v-298zM471 595v118h-123v-118h123zM272 595v118h-123v-118h123z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="596" 
d="M568 528h-126l-137 -392l-150 392h-129l220 -528q-15 -35 -32 -48t-43 -13q-19 0 -36.5 5.5t-34.5 14.5l-41 -90q28 -15 57.5 -23.5t67.5 -8.5q32 0 57 8t46 25.5t38 45.5t33 69zM338 594l122 115l-110 49l-106 -164h94z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="670" 
d="M622 264q0 66 -21 117t-55.5 86.5t-78.5 53.5t-91 18q-64 0 -107 -28.5t-72 -70.5v290h-121v-890h121v242q28 -37 71 -65t108 -28q47 0 91.5 18t79 53.5t55 86.5t20.5 117zM499 264q0 -39 -12 -70.5t-32.5 -53.5t-48 -34t-58.5 -12t-59 12t-49 34.5t-33.5 53.5t-12.5 70
t12.5 70t33.5 53.5t49 34.5t59 12t58.5 -12t48 -34t32.5 -53.5t12 -70.5z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="596" 
d="M568 528h-126l-137 -392l-150 392h-129l220 -528q-15 -35 -32 -48t-43 -13q-19 0 -36.5 5.5t-34.5 14.5l-41 -90q28 -15 57.5 -23.5t67.5 -8.5q32 0 57 8t46 25.5t38 45.5t33 69zM459 595v118h-123v-118h123zM260 595v118h-123v-118h123z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="286" 
d="M204 0v528h-121v-528h121z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="649" 
d="M611 0v112h-369v203l202 61v114l-202 -60v270h-122v-307l-75 -23v-114l75 22v-278h491z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="346" 
d="M233 332l76 23v115l-76 -23v283h-120v-318l-76 -23v-115l76 23v-297h120v332z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1098" 
d="M1042 0v110h-379v188h329v110h-329v182h374v110h-606q-83 0 -151 -27t-117.5 -74.5t-76.5 -111.5t-27 -138t27 -138t76.5 -111t117.5 -73.5t151 -26.5h611zM540 112h-109q-55 0 -100 18t-76.5 50t-49 75.5t-17.5 95.5t17.5 95.5t49 75t76.5 49t100 17.5h109v-476z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1040" 
d="M539 102q33 -54 89 -84t126 -30q72 0 125 27t91 72l-71 63q-32 -31 -65.5 -47t-78.5 -16q-58 0 -99.5 34.5t-52.5 99.5h388q1 9 1.5 17t0.5 16q0 57 -16 109t-47.5 91t-78 62t-108.5 23q-66 0 -118 -31t-84 -82q-35 51 -90.5 82t-124.5 31q-59 0 -110 -22t-88.5 -59.5
t-59 -88t-21.5 -107.5t21.5 -106.5t58.5 -87t87 -59t107 -21.5q70 0 126.5 31.5t91.5 82.5zM602 301q8 62 44.5 100.5t93.5 38.5q30 0 53.5 -11t40 -29.5t27 -44t13.5 -54.5h-272zM482 262q0 -35 -11 -65.5t-31.5 -53.5t-49 -36.5t-64.5 -13.5q-35 0 -64 13.5t-50 36.5
t-32.5 54.5t-11.5 66.5t11 65.5t31.5 53.5t49 36.5t64.5 13.5q34 0 63 -13.5t50 -37t33 -54.5t12 -66z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="640" 
d="M194 517q0 35 30 59.5t84 24.5q48 0 95 -18.5t95 -53.5l66 93q-54 43 -115.5 65.5t-138.5 22.5q-52 0 -96 -14.5t-75.5 -41.5t-49.5 -64.5t-18 -83.5q0 -49 15.5 -83t45 -58t73 -40t99.5 -29q49 -12 79.5 -23t48 -23.5t24 -27.5t6.5 -34q0 -41 -33.5 -65t-90.5 -24
q-66 0 -118 23.5t-103 67.5l-74 -88q63 -57 137 -84.5t155 -27.5q55 0 101 14t79 41t51.5 66t18.5 89q0 44 -14.5 76.5t-42.5 57t-70.5 41.5t-97.5 31q-50 12 -82.5 23t-51 24t-25.5 28t-7 36zM321 838l-77 66h-96l116 -146h112l116 146h-94z" />
    <glyph glyph-name="scaron" unicode="&#x161;" 
d="M453 156v2q0 36 -15 61t-38.5 42.5t-52.5 29.5t-58 21q-23 7 -44.5 14.5t-38 16t-26.5 19t-10 24.5v2q0 23 19.5 38t54.5 15q33 0 72 -13.5t77 -36.5l48 86q-42 28 -93 44t-101 16q-39 0 -73.5 -11t-60 -32t-40 -51t-14.5 -67v-2q0 -38 15 -63.5t38.5 -43t53 -28.5
t58.5 -20q23 -7 44.5 -13.5t38 -15t26 -19.5t9.5 -26v-2q0 -26 -21.5 -41.5t-60.5 -15.5q-41 0 -85.5 16.5t-87.5 48.5l-54 -82q50 -39 108.5 -59t115.5 -20q41 0 77 10.5t62.5 31t41.5 51.5t15 73zM255 675l-77 66h-96l116 -146h112l116 146h-94z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="716" 
d="M420 279l278 421h-141l-198 -310l-196 310h-145l278 -424v-276h124v279zM519 757v118h-123v-118h123zM320 757v118h-123v-118h123z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="702" 
d="M478 591l-413 -500v-91h573v109h-413l413 500v91h-560v-109h400zM356 838l-77 66h-96l116 -146h112l116 146h-94z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="560" 
d="M357 429l-305 -346v-83h459v99h-305l305 346v83h-448v-99h294zM289 675l-77 66h-96l116 -146h112l116 146h-94z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="527" 
d="M491 592v102q-19 5 -41 8t-51 3q-81 0 -120.5 -39.5t-54.5 -115.5l-18 -93h-97v-101h80l-35 -193q-7 -36 -24 -51.5t-49 -15.5q-19 0 -40 3v-99q12 -3 25 -4t31 -1q78 0 120 34.5t56 115.5l38 211h145v101h-127l12 68q8 41 27 59t54 18q16 0 33.5 -2.5t35.5 -7.5z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" 
d="M249 662l77 -67h96l-116 146h-112l-116 -146h94z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" 
d="M251 675l-77 66h-96l116 -146h112l116 146h-94z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" 
d="M421 741h-83q-9 -29 -29.5 -46t-58.5 -17t-58.5 17t-29.5 46h-83q5 -71 52 -109.5t119 -38.5t119 38.5t52 109.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" 
d="M313 595v119h-126v-119h126z" />
    <glyph glyph-name="ring" unicode="&#x2da;" 
d="M364 703q0 22 -9.5 42t-25 35t-36.5 24t-43 9q-23 0 -43.5 -9t-36 -24t-25 -35t-9.5 -42q0 -23 9.5 -43t25 -35t36 -23.5t43.5 -8.5q22 0 43 8.5t36.5 23.5t25 35t9.5 43zM312 703q0 -26 -18 -44t-44 -18t-44 18t-18 44t18 44t44 18t44 -18t18 -44z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" 
d="M277 10h-86q-6 -14 -10.5 -31.5t-4.5 -34.5q0 -56 48.5 -85t141.5 -23v56q-51 3 -73 21.5t-22 52.5q0 21 6 44z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" 
d="M439 719l-67 21q-11 -26 -22 -38t-33 -12q-14 0 -30 6t-34.5 13.5t-38.5 14t-41 6.5q-46 0 -70.5 -32t-41.5 -93l67 -20q11 26 22 38t33 12q13 0 29.5 -6.5t35 -14t38.5 -13.5t41 -6q46 0 70.5 32t41.5 92z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" 
d="M328 595l122 115l-106 49l-106 -164h90zM146 595l120 115l-106 49l-104 -164h90z" />
    <glyph glyph-name="Omega" unicode="&#x3a9;" horiz-adv-x="752" 
d="M598 524v29q0 80 70 80q20 0 36.5 -3.5t34.5 -9.5v102q-20 6 -43.5 10t-55.5 4q-78 0 -119.5 -42.5t-41.5 -130.5v-38h-67v-101h67v-424h121v424h138v100h-140zM222 524v29q0 80 70 80q20 0 36.5 -3.5t34.5 -9.5v102q-20 6 -43 10t-55 4q-78 0 -120 -42.5t-42 -130.5v-38
h-67v-101h67v-424h121v424h138v100h-140z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="830" 
d="M777 351q0 74 -28 140t-76.5 115t-114.5 77.5t-142 28.5t-142.5 -29t-115.5 -78.5t-77 -115.5t-28 -140t28 -140t76.5 -115t114.5 -77.5t142 -28.5t142.5 29t115.5 78.5t77 115.5t28 140zM739 351q0 -68 -25 -127.5t-68.5 -103.5t-103 -70t-128.5 -26t-128 25.5
t-102.5 69.5t-68 103t-24.5 127q0 67 25 127t68.5 104t103 70t128.5 26t128 -25.5t102.5 -69.5t68 -103.5t24.5 -126.5zM586 418q0 60 -39.5 94.5t-107.5 34.5h-153v-380h74v120h71q31 0 59.5 8t49.5 24.5t33.5 41t12.5 57.5zM511 417q0 -29 -21 -47t-57 -18h-73v129h73
q36 0 57 -16t21 -48z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="528" 
d="M472 249v115h-416v-115h416z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="898" 
d="M842 249v115h-786v-115h786z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="269" 
d="M230 653l-13 52q-76 -6 -115 -44t-39 -122v-117h133v138h-51q-7 76 85 93z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="269" 
d="M39 469l13 -52q76 6 115 44t39 122v117h-133v-137h51q7 -77 -85 -94z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="269" 
d="M34 -94l13 -51q76 6 115 44t39 122v116h-133v-137h51q7 -76 -85 -94z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="501" 
d="M462 653l-13 52q-76 -6 -115 -44t-39 -122v-117h133v138h-52q-6 76 86 93zM230 653l-13 52q-76 -6 -115 -44t-39 -122v-117h133v138h-51q-7 76 85 93z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="501" 
d="M271 469l13 -52q76 6 115 44t39 122v117h-133v-137h51q7 -77 -85 -94zM39 469l13 -52q76 6 115 44t39 122v117h-133v-137h51q7 -77 -85 -94z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="501" 
d="M266 -94l13 -51q76 6 115 44t39 122v116h-133v-137h51q7 -76 -85 -94zM34 -94l13 -51q76 6 115 44t39 122v116h-133v-137h51q7 -76 -85 -94z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="410" 
d="M232 513l122 -9v71l-121 -9l12 134h-80l12 -134l-121 9v-71l122 9l-9 -275h72z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="430" 
d="M364 125v71l-121 -9l8 163l-8 163l121 -9v71l-121 -9l12 134h-80l12 -134l-121 9v-71l121 9l-8 -163l8 -163l-121 9v-71l121 9l-12 -134h80l-12 134z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="491" 
d="M387 352q0 29 -11 55t-30.5 45t-45.5 30t-55 11t-54.5 -11t-45 -30t-30.5 -45t-11 -55t11 -55t30.5 -45t45 -30t54.5 -11t55 11t45.5 30t30.5 45t11 55z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="792" 
d="M724 0v134h-130v-134h130zM461 0v134h-130v-134h130zM198 0v134h-130v-134h130z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1228" 
d="M378 530q0 36 -12 68.5t-33.5 57t-51.5 38.5t-67 14t-67.5 -14.5t-52.5 -39t-34 -57t-12 -69.5q0 -36 12 -68.5t33.5 -57t51.5 -38.5t67 -14t67.5 14.5t52.5 39t34 57t12 69.5zM631 700l-512 -700h99l512 700h-99zM1180 172q0 36 -12 68.5t-33.5 57t-51.5 38.5t-67 14
q-38 0 -68.5 -14.5t-52 -39t-33.5 -57t-12 -69.5q0 -36 11.5 -68.5t33 -57t52 -38.5t67.5 -14t67.5 14.5t52.5 39t34 57t12 69.5zM801 172q0 36 -12 68.5t-33.5 57t-51.5 38.5t-67 14t-67.5 -14.5t-52.5 -39t-34 -57t-12 -69.5q0 -36 12 -68.5t33.5 -57t51.5 -38.5t67 -14
t67.5 14.5t52.5 39t34 57t12 69.5zM289 528q0 -42 -20 -71.5t-55 -29.5q-34 0 -55.5 29.5t-21.5 73.5q0 42 20 71.5t55 29.5q34 0 55.5 -29.5t21.5 -73.5zM1091 170q0 -42 -20 -71.5t-55 -29.5q-34 0 -55.5 29.5t-21.5 73.5q0 42 20 71.5t55 29.5q34 0 55.5 -29.5
t21.5 -73.5zM712 170q0 -42 -20 -71.5t-55 -29.5q-34 0 -55.5 29.5t-21.5 73.5q0 42 20 71.5t55 29.5q34 0 55.5 -29.5t21.5 -73.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="354" 
d="M210 488l-166 -220v-8l166 -219l92 47l-132 177l132 176z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="354" 
d="M145 41l165 219v8l-165 220l-92 -48l132 -177l-132 -176z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="690" 
d="M594 611v-219h57v308h-61l-93 -146l-93 146h-63v-308h57v219l95 -148h6zM180 646h95v54h-249v-54h95v-254h59v254z" />
    <glyph glyph-name="fraction" unicode="&#x2215;" horiz-adv-x="459" 
d="M397 758l-467 -856h110l467 856h-110z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="1038" 
d="M598 524v29q0 80 70 80q20 0 36.5 -3.5t34.5 -9.5v102q-20 6 -43.5 10t-55.5 4q-78 0 -119.5 -42.5t-41.5 -130.5v-38h-67v-101h67v-424h121v424h138v100h-140zM222 524v29q0 80 70 80q20 0 36.5 -3.5t34.5 -9.5v102q-20 6 -43 10t-55 4q-78 0 -120 -42.5t-42 -130.5v-38
h-67v-101h67v-424h121v424h138v100h-140zM956 0v730h-122v-730h122z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="1038" 
d="M960 609v115h-130v-115h130zM598 524v29q0 80 70 80q20 0 36.5 -3.5t34.5 -9.5v102q-20 6 -43.5 10t-55.5 4q-78 0 -119.5 -42.5t-41.5 -130.5v-38h-67v-101h67v-424h121v424h138v100h-140zM222 524v29q0 80 70 80q20 0 36.5 -3.5t34.5 -9.5v102q-20 6 -43 10t-55 4
q-78 0 -120 -42.5t-42 -130.5v-38h-67v-101h67v-424h121v424h138v100h-140zM956 0v528h-122v-528h122z" />
    <glyph glyph-name="apple" unicode="&#xf8ff;" horiz-adv-x="967" 
d="M868 0v750h-767v-750h767zM859 -104l-6 34h-1l-13 -26l-13 26h-1l-6 -34h6l3 20l10 -21h2l10 21l3 -20h6zM806 -87q0 7 -4.5 12t-11.5 5q-8 0 -13 -5t-5 -13q0 -7 4.5 -12t12.5 -5t12.5 5t4.5 13zM758 -96q-6 -4 -10 -4q-13 0 -13 13q0 5 3.5 8.5t8.5 3.5q6 0 9 -3l2 5
q-5 3 -11 3q-8 0 -13 -5t-5 -13q0 -17 18 -17q7 0 10 3zM702 -71h-7l-9 -14l-11 15l-6 -1l14 -19v-14h6v14zM656 -104v33h-6v-13h-16v13h-6v-33h6v15h16v-15h6zM612 -82q0 5 -4.5 9t-18.5 2v-33h6v10q17 0 17 12zM576 -104l-16 34h-2l-15 -34h6l3 8h14l4 -8h6zM534 -104
l-10 13q7 3 7 10q0 5 -4.5 8.5t-18.5 1.5v-33h6v11h5l8 -11h7zM491 -101v15h-15v-5h9v-7q-4 -2 -6 -2q-13 0 -13 12q0 13 12 13q7 0 11 -4l2 5q-6 4 -12 4q-8 0 -13.5 -5t-5.5 -13t5 -12.5t13 -4.5q7 0 13 4zM447 -87q0 7 -4.5 12t-11.5 5q-8 0 -13 -5t-5 -13q0 -7 4.5 -12
t12.5 -5t12.5 5t4.5 13zM400 -82q0 5 -4.5 9t-18.5 2v-33h6v10q17 0 17 12zM364 -71h-7l-9 -14l-11 15l-6 -1l14 -19v-14h6v14zM323 -71h-30v-5h12v-28h6v28h12v5zM267 -71h-6l-7 -22l-10 23h-1l-10 -23l-7 23l-6 -1l12 -34h1l10 23l11 -23h1zM211 -71h-6l-7 -22l-10 23h-1
l-10 -23l-7 23l-6 -1l12 -34h1l10 23l11 -23h1zM155 -71h-6l-7 -22l-10 23h-1l-10 -23l-7 23l-6 -1l12 -34h1l10 23l11 -23h1zM716 -101q0 4 -4 4t-4 -4t4 -4t4 4zM284 -101q0 4 -4 4t-4 -4t4 -4t4 4zM559 686h-2l-2.5 4.5t-7.5 4.5q-3 0 -3 -3v-23q0 -5 2 -5h3v-2h-17v2h2
q3 0 3 5v23q0 3 -3 3q-5 0 -7.5 -4.5l-2.5 -4.5h-2l0.5 4.5t0.5 7.5q3 -1 18 -1t17 1q1 -3 1 -8v-4zM504 662h-18v2h2q3 0 3 5v10h-17v-10q0 -4 2.5 -4.5l2.5 -0.5v-2h-17v2l2.5 0.5t2.5 4.5v21q0 5 -3 5h-2v2h17v-1v-1h-3q-2 0 -2 -5v-9h17v9q0 5 -3 5h-2v1v1h18v-2h-3
t-3 -5v-21q0 -5 3 -5h3v-2zM439 664v-2h-30l-2 11l2 1l2.5 -4.5t7.5 -4.5h4q4 0 4 3v11h-4q-5 0 -5.5 -3l-0.5 -3h-2v13h2l0.5 -2.5t5.5 -2.5h4v10q0 3 -4 3h-4q-5 0 -7 -4l-2 -4l-2 1l2 10h29v-2h-3q-2 0 -2 -5v-21q0 -5 2 -5h3zM827 618h-685v6h685v-6zM696 540
q0 -12 -8 -17t-15 -5q-14 0 -25.5 11t-11.5 27q0 12 8 17t15 5q14 0 25.5 -11.5t11.5 -26.5zM589 537q0 -8 -6 -13.5t-19 -5.5q-16 0 -16 13q0 10 9 10q7 0 7 -5t-4 -5q-2 0 -3 1t-2 1q-3 0 -3 -3q0 -9 12 -9t12 14q0 10 -6 15q-5 -5 -11 -5q-8 0 -8 6q0 8 16 6q2 2 2 6
q0 12 -10 12q-7 0 -7 -5q0 -2 1.5 -2.5t1.5 -3.5t-4 -3q-5 0 -5 6q0 11 13 11q11 0 16 -5t5 -10q0 -6 -5 -9q14 -5 14 -17zM295 537q0 -8 -6 -13.5t-19 -5.5q-16 0 -16 13q0 10 9 10q7 0 7 -5t-4 -5q-2 0 -3 1t-2 1q-3 0 -3 -3q0 -9 12 -9t12 14q0 10 -6 15q-5 -5 -11 -5
q-8 0 -8 6q0 8 16 6q2 2 2 6q0 12 -10 12q-7 0 -7 -5q0 -2 1.5 -2.5t1.5 -3.5t-4 -3q-5 0 -5 6q0 11 13 11q11 0 16 -5t5 -10q0 -6 -5 -9q14 -5 14 -17zM820 522l1 -3h-29l-1 3l4.5 0.5t2.5 7.5l-3 16h-24l3 -16q2 -7 6 -7.5l4 -0.5l1 -3h-29l-1 3l4.5 0.5t2.5 7.5l-7 35
q-2 7 -6 7.5l-4 0.5l-1 3h29l1 -3l-4.5 -0.5t-2.5 -7.5l3 -15h24l-3 15q-2 7 -5.5 7.5l-3.5 0.5l-1 3h28l1 -3l-4.5 -0.5t-2.5 -7.5l7 -35q2 -7 6 -7.5zM498 519h-29l-1 3l4.5 0.5t2.5 7.5l-3 17h-4q-7 0 -7.5 -5.5l-0.5 -5.5h-3l-5 23h3l2.5 -4.5t9.5 -4.5h4l-3 16
q-1 6 -6 6h-4q-11 0 -12 -6.5l-1 -6.5h-4l0.5 7t-0.5 10h45l1 -3l-4.5 -0.5t-2.5 -7.5l7 -35q2 -7 6 -7.5l4 -0.5zM202 522l1 -3h-29l-1 3l4.5 0.5t2.5 7.5l-3 15q-4 0 -5 -3l-12 -23h-19l-1 3l5 0.5t9 7.5l9 16q-8 1 -13.5 5t-5.5 10q0 15 20 15h27l1 -3l-4.5 -0.5
t-2.5 -7.5l7 -35q2 -7 6 -7.5zM400 521q0 -3 -4 -3q-9 0 -12 7q-8 0 -10 -6.5t-15 -6.5q-5 0 -10 3.5t-5 8.5q0 7 5 7t5 -5q0 -2 -1 -2.5t-1 -2.5q0 -4 5 -4t8.5 6.5t14.5 6.5q-6 6 -8.5 13.5t-3.5 14.5t-2.5 12t-6.5 5t-5 -6t-4 -6q-5 0 -5 5q0 3 4 6.5t9 3.5
q12 0 16.5 -6.5t6 -15t2 -16t4.5 -10.5q7 -1 10 -4t3 -5zM827 475h-685v6h685v-6zM827 378h-36v28h-11v-65h14v-33h-81v33h14v65h-11v-28h-36v62h147v-62zM648 406h-14l-37 -45v-20h15v-33h-81v33h14v19l-37 46h-12v34h62v-34h-10l15 -23l15 23h-11v34h81v-34zM458 308h-81
v33h15v19h-30q-22 0 -34.5 11.5t-12.5 28.5t12.5 28.5t34.5 11.5h96v-34h-14v-65h14v-33zM283 308h-141v61h41v-28h33v21h-24v24h24v20h-33v-28h-41v62h141v-34h-14v-65h14v-33zM827 271h-685v6h685v-6zM728 192q0 -12 -8 -17t-15 -5q-14 0 -25.5 11t-11.5 27q0 12 8 17
t15 5q14 0 25.5 -11.5t11.5 -26.5zM203 217l-2 -2l-4.5 4.5t-7.5 2.5q-9 0 -9 -13q0 -8 5 -25q2 -8 6 -8.5l4 -0.5l1 -4h-28l-1 4l4.5 1t1.5 14q-3 12 -8.5 22.5t-15.5 10.5q-8 0 -8 -6l-3 1q0 6 3.5 9t8.5 3q8 0 13 -6.5t8 -16.5q-1 23 14 23q8 0 11.5 -4t6.5 -9zM823 171
h-29l-1 3l4.5 0.5t2.5 7.5l-3 17h-4q-7 0 -7.5 -5.5l-0.5 -5.5h-3l-5 23h3l2.5 -4.5t9.5 -4.5h4l-3 16q-1 6 -6 6h-4q-11 0 -12 -6.5l-1 -6.5h-4l0.5 7t-0.5 10h45l1 -3l-4.5 -0.5t-2.5 -7.5l7 -35q2 -7 6 -7.5l4 -0.5zM621 225l-5 -0.5t-3 -8.5l5 -22q2 -8 0 -16t-19 -8
q-14 0 -20 9.5t-8 17.5l-4 19q-2 8 -6.5 8.5l-4.5 0.5l-1 3h24v-3l-4.5 -0.5t-2.5 -8.5l4 -19q2 -9 7 -15.5t13 -6.5q11 0 12 5t-1 15l-5 21q-2 8 -6 8.5l-4 0.5l-1 3h29zM522 174l1 -3h-21l-1 3l4 1t2 9l-6 30l-30 -44l-3 1l-8 45q-2 7 -5.5 8l-3.5 1l-1 3h21l1 -3l-4.5 -1
t-2.5 -8l5 -30l29 42h14l1 -3l-5 -0.5t-3 -8.5l7 -32q2 -8 5.5 -9zM414 171h-32q-13 0 -24 9.5t-11 25.5q0 11 8 16.5t17 5.5h30l1 -3q-11 -1 -8 -10l8 -31q2 -8 6 -8.5l4 -0.5zM306 174l1 -3h-29l-1 3l4.5 0.5t2.5 7.5l-3 15q-4 0 -5 -3l-12 -23h-19l-1 3l5 0.5t9 7.5l9 16
q-8 1 -13.5 5t-5.5 10q0 15 20 15h27l1 -3l-4.5 -0.5t-2.5 -7.5l7 -35q2 -7 6 -7.5zM827 127h-685v6h685v-6zM694 46h-14v2l2.5 1t2.5 4v20l-25 -28h-3v28q0 4 -3 5l-3 1v2h15v-2l-3 -1t-3 -5v-18l24 26h10v-2l-3 -2t-3 -4v-20q0 -3 3 -4l3 -1v-2zM633 48v-2h-30l-2 11l2 1
l2.5 -4.5t7.5 -4.5h4q4 0 4 3v11h-4q-5 0 -5.5 -3l-0.5 -3h-2v13h2l0.5 -2.5t5.5 -2.5h4v10q0 3 -4 3h-4q-5 0 -7 -4l-2 -4l-2 1l2 10h29v-2h-3q-2 0 -2 -5v-21q0 -5 2 -5h3zM583 81v-2l-2.5 -0.5t-4.5 -5.5l-12 -27h-1l-9 16l-8 -16q0 -1 -0.5 -0.5l-0.5 0.5h-1l-13 27
q-2 4 -4 5l-2 1v2h13v-2l-2 -1t0 -5l8 -18l7 13l-3 5q-3 5 -5 5.5l-2 0.5l-1 2h26v-2l-2 -1.5t-4 -4.5l-4 -8l5 -10l9 19q2 3 0 4l-2 1v2h15zM480 79l-2.5 -0.5t-4.5 -4.5l-8 -12v-8q0 -5 2.5 -5.5l2.5 -0.5v-2h-17v2l2.5 0.5t2.5 5.5v8l-8 12q-2 4 -5 4.5l-3 0.5v2h15v-2
l-2.5 -0.5t-0.5 -4.5l6 -9l6 9q2 4 0.5 4.5l-1.5 0.5v2h15v-2zM425 64q0 -8 -5 -13.5t-15 -5.5t-15 5.5t-5 13.5t5 13t15 5t15 -5t5 -13zM364 46h-17v2l2.5 0.5t2.5 4.5v9q-4 0 -7 -4l-7 -12h-11v2l2.5 0.5t5.5 4.5l7 10q-9 4 -9 9q0 9 13 9h18v-2l-2.5 -0.5t-2.5 -5.5v-20
q0 -4 2.5 -4.5l2.5 -0.5v-2zM309 46h-18v2l3 0.5t3 5.5v9q-2 0 -4 -2l-8 -8q-3 -4 -0.5 -4.5l2.5 -0.5v-2h-17v2l2.5 1.5t4.5 3.5l13 13l-10 9q-2 2 -5 3l-3 1v2h14v-2l-1.5 -1t0.5 -3l8 -8q2 -2 4 -2v9q0 4 -3 4.5l-3 0.5v2h18v-2l-3 -0.5t-3 -4.5v-20q0 -4 3 -5l3 -1v-2z
M606 -83q0 -6 -11 -6v13q11 1 11 -7zM525 -82q0 -7 -11 -6v12q11 2 11 -6zM394 -83q0 -6 -11 -6v13q11 1 11 -7zM564 -92h-10l5 12zM800 -88q0 -12 -11 -12t-11 13q0 12 11 12t11 -13zM441 -88q0 -12 -11 -12t-11 13q0 12 11 12t11 -13zM176 548l-3 19q-1 5 -9 5t-8 -9
q0 -5 3.5 -10t11.5 -5h5zM566 553q-2 1 -7 1q-4 0 -4 -3q0 -2 4 -2t7 4zM272 553q-2 1 -7 1q-4 0 -4 -3q0 -2 4 -2t7 4zM684 535q0 16 -8 27.5t-17 11.5q-11 0 -11 -14q0 -15 8 -26.5t17 -11.5q11 0 11 13zM392 384v22h-11q-13 0 -13 -11t13 -11h11zM280 200l-3 19
q-1 5 -9 5t-8 -9q0 -5 3.5 -10t11.5 -5h5zM716 187q0 16 -8 27.5t-17 11.5q-11 0 -11 -14q0 -15 8 -26.5t17 -11.5q11 0 11 13zM393 182l-9 35q-2 5 -2.5 5.5t-3.5 1.5h-6q-14 0 -14 -17q0 -12 7.5 -22t18.5 -10h5q4 0 4 7zM553 79q-5 0 0 -9q4 9 0 9zM352 64v10q0 4 -4 4
q-7 0 -7 -6q0 -8 8 -8h3zM417 64q0 16 -12 16t-12 -16q0 -7 3 -12t9 -5q7 0 9.5 5t2.5 12z" />
    <glyph glyph-name="fi" unicode="&#xfb01;" horiz-adv-x="662" 
d="M584 609v115h-130v-115h130zM222 524v29q0 80 70 80q20 0 36.5 -3.5t34.5 -9.5v102q-20 6 -43 10t-55 4q-78 0 -120 -42.5t-42 -130.5v-38h-67v-101h67v-424h121v424h138v100h-140zM580 0v528h-121v-528h121z" />
    <glyph glyph-name="fl" unicode="&#xfb02;" horiz-adv-x="662" 
d="M222 524v29q0 80 70 80q20 0 36.5 -3.5t34.5 -9.5v102q-20 6 -43 10t-55 4q-78 0 -120 -42.5t-42 -130.5v-38h-67v-101h67v-424h121v424h138v100h-140zM580 0v730h-121v-730h121z" />
    <glyph glyph-name="NUL" horiz-adv-x="0" 
 />
    <hkern u1="&#x24;" u2="&#x37;" k="10" />
    <hkern u1="&#x26;" u2="Y" k="63" />
    <hkern u1="&#x26;" u2="W" k="47" />
    <hkern u1="&#x26;" u2="V" k="57" />
    <hkern u1="&#x26;" u2="T" k="62" />
    <hkern u1="&#x26;" u2="S" k="3" />
    <hkern u1="&#x28;" u2="&#x153;" k="30" />
    <hkern u1="&#x28;" u2="&#x152;" k="30" />
    <hkern u1="&#x28;" u2="&#xb5;" k="30" />
    <hkern u1="&#x28;" u2="s" k="15" />
    <hkern u1="&#x28;" u2="q" k="30" />
    <hkern u1="&#x28;" u2="o" k="30" />
    <hkern u1="&#x28;" u2="j" k="-30" />
    <hkern u1="&#x28;" u2="g" k="20" />
    <hkern u1="&#x28;" u2="e" k="30" />
    <hkern u1="&#x28;" u2="d" k="30" />
    <hkern u1="&#x28;" u2="c" k="30" />
    <hkern u1="&#x28;" u2="Q" k="30" />
    <hkern u1="&#x28;" u2="O" k="30" />
    <hkern u1="&#x28;" u2="J" k="15" />
    <hkern u1="&#x28;" u2="G" k="30" />
    <hkern u1="&#x28;" u2="C" k="30" />
    <hkern u1="&#x2a;" u2="&#x153;" k="20" />
    <hkern u1="&#x2a;" u2="&#xe6;" k="10" />
    <hkern u1="&#x2a;" u2="&#xc6;" k="100" />
    <hkern u1="&#x2a;" u2="t" k="-10" />
    <hkern u1="&#x2a;" u2="s" k="10" />
    <hkern u1="&#x2a;" u2="q" k="15" />
    <hkern u1="&#x2a;" u2="o" k="20" />
    <hkern u1="&#x2a;" u2="g" k="15" />
    <hkern u1="&#x2a;" u2="e" k="20" />
    <hkern u1="&#x2a;" u2="d" k="15" />
    <hkern u1="&#x2a;" u2="c" k="20" />
    <hkern u1="&#x2a;" u2="a" k="10" />
    <hkern u1="&#x2a;" u2="J" k="80" />
    <hkern u1="&#x2a;" u2="A" k="100" />
    <hkern u1="&#x2c;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x2c;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x2c;" u2="&#x2248;" k="15" />
    <hkern u1="&#x2c;" u2="&#x221a;" k="15" />
    <hkern u1="&#x2c;" u2="&#x201d;" k="40" />
    <hkern u1="&#x2c;" u2="&#x2019;" k="40" />
    <hkern u1="&#x2c;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x2c;" u2="&#x153;" k="20" />
    <hkern u1="&#x2c;" u2="&#x152;" k="40" />
    <hkern u1="&#x2c;" u2="&#xb5;" k="40" />
    <hkern u1="&#x2c;" u2="y" k="45" />
    <hkern u1="&#x2c;" u2="w" k="70" />
    <hkern u1="&#x2c;" u2="v" k="85" />
    <hkern u1="&#x2c;" u2="t" k="25" />
    <hkern u1="&#x2c;" u2="q" k="10" />
    <hkern u1="&#x2c;" u2="o" k="20" />
    <hkern u1="&#x2c;" u2="j" k="-15" />
    <hkern u1="&#x2c;" u2="g" k="10" />
    <hkern u1="&#x2c;" u2="f" k="15" />
    <hkern u1="&#x2c;" u2="e" k="20" />
    <hkern u1="&#x2c;" u2="d" k="10" />
    <hkern u1="&#x2c;" u2="c" k="20" />
    <hkern u1="&#x2c;" u2="Y" k="130" />
    <hkern u1="&#x2c;" u2="W" k="100" />
    <hkern u1="&#x2c;" u2="V" k="120" />
    <hkern u1="&#x2c;" u2="U" k="15" />
    <hkern u1="&#x2c;" u2="T" k="100" />
    <hkern u1="&#x2c;" u2="Q" k="40" />
    <hkern u1="&#x2c;" u2="O" k="40" />
    <hkern u1="&#x2c;" u2="G" k="40" />
    <hkern u1="&#x2c;" u2="C" k="40" />
    <hkern u1="&#x2c;" u2="&#x37;" k="20" />
    <hkern u1="&#x2c;" u2="&#x31;" k="50" />
    <hkern u1="&#x2c;" u2="&#x30;" k="20" />
    <hkern u1="&#x2d;" u2="&#xc6;" k="40" />
    <hkern u1="&#x2d;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2d;" u2="z" k="10" />
    <hkern u1="&#x2d;" u2="y" k="15" />
    <hkern u1="&#x2d;" u2="x" k="30" />
    <hkern u1="&#x2d;" u2="w" k="10" />
    <hkern u1="&#x2d;" u2="v" k="15" />
    <hkern u1="&#x2d;" u2="Z" k="30" />
    <hkern u1="&#x2d;" u2="Y" k="80" />
    <hkern u1="&#x2d;" u2="X" k="50" />
    <hkern u1="&#x2d;" u2="W" k="35" />
    <hkern u1="&#x2d;" u2="V" k="40" />
    <hkern u1="&#x2d;" u2="T" k="90" />
    <hkern u1="&#x2d;" u2="A" k="40" />
    <hkern u1="&#x2d;" u2="&#x37;" k="40" />
    <hkern u1="&#x2d;" u2="&#x33;" k="10" />
    <hkern u1="&#x2d;" u2="&#x31;" k="30" />
    <hkern u1="&#x2e;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x2e;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x2e;" u2="&#x2248;" k="15" />
    <hkern u1="&#x2e;" u2="&#x221a;" k="15" />
    <hkern u1="&#x2e;" u2="&#x201d;" k="40" />
    <hkern u1="&#x2e;" u2="&#x2019;" k="40" />
    <hkern u1="&#x2e;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x2e;" u2="&#x153;" k="20" />
    <hkern u1="&#x2e;" u2="&#x152;" k="40" />
    <hkern u1="&#x2e;" u2="&#xb5;" k="40" />
    <hkern u1="&#x2e;" u2="y" k="60" />
    <hkern u1="&#x2e;" u2="w" k="70" />
    <hkern u1="&#x2e;" u2="v" k="85" />
    <hkern u1="&#x2e;" u2="t" k="25" />
    <hkern u1="&#x2e;" u2="q" k="10" />
    <hkern u1="&#x2e;" u2="o" k="20" />
    <hkern u1="&#x2e;" u2="g" k="10" />
    <hkern u1="&#x2e;" u2="f" k="15" />
    <hkern u1="&#x2e;" u2="e" k="20" />
    <hkern u1="&#x2e;" u2="d" k="10" />
    <hkern u1="&#x2e;" u2="c" k="20" />
    <hkern u1="&#x2e;" u2="Y" k="130" />
    <hkern u1="&#x2e;" u2="W" k="100" />
    <hkern u1="&#x2e;" u2="V" k="120" />
    <hkern u1="&#x2e;" u2="U" k="15" />
    <hkern u1="&#x2e;" u2="T" k="100" />
    <hkern u1="&#x2e;" u2="Q" k="40" />
    <hkern u1="&#x2e;" u2="O" k="40" />
    <hkern u1="&#x2e;" u2="G" k="40" />
    <hkern u1="&#x2e;" u2="C" k="40" />
    <hkern u1="&#x2e;" u2="&#x37;" k="20" />
    <hkern u1="&#x2e;" u2="&#x31;" k="50" />
    <hkern u1="&#x2e;" u2="&#x30;" k="20" />
    <hkern u1="&#x2f;" u2="&#xfb02;" k="25" />
    <hkern u1="&#x2f;" u2="&#xfb01;" k="25" />
    <hkern u1="&#x2f;" u2="&#x2248;" k="25" />
    <hkern u1="&#x2f;" u2="&#x221a;" k="25" />
    <hkern u1="&#x2f;" u2="&#x3a9;" k="25" />
    <hkern u1="&#x2f;" u2="&#x153;" k="80" />
    <hkern u1="&#x2f;" u2="&#x152;" k="40" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="65" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="120" />
    <hkern u1="&#x2f;" u2="&#xb5;" k="40" />
    <hkern u1="&#x2f;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2f;" u2="z" k="60" />
    <hkern u1="&#x2f;" u2="y" k="50" />
    <hkern u1="&#x2f;" u2="x" k="50" />
    <hkern u1="&#x2f;" u2="w" k="50" />
    <hkern u1="&#x2f;" u2="v" k="50" />
    <hkern u1="&#x2f;" u2="u" k="50" />
    <hkern u1="&#x2f;" u2="t" k="20" />
    <hkern u1="&#x2f;" u2="s" k="85" />
    <hkern u1="&#x2f;" u2="r" k="50" />
    <hkern u1="&#x2f;" u2="q" k="70" />
    <hkern u1="&#x2f;" u2="p" k="50" />
    <hkern u1="&#x2f;" u2="o" k="80" />
    <hkern u1="&#x2f;" u2="n" k="50" />
    <hkern u1="&#x2f;" u2="m" k="50" />
    <hkern u1="&#x2f;" u2="g" k="70" />
    <hkern u1="&#x2f;" u2="f" k="25" />
    <hkern u1="&#x2f;" u2="e" k="80" />
    <hkern u1="&#x2f;" u2="d" k="70" />
    <hkern u1="&#x2f;" u2="c" k="80" />
    <hkern u1="&#x2f;" u2="a" k="65" />
    <hkern u1="&#x2f;" u2="Z" k="20" />
    <hkern u1="&#x2f;" u2="S" k="30" />
    <hkern u1="&#x2f;" u2="Q" k="40" />
    <hkern u1="&#x2f;" u2="O" k="40" />
    <hkern u1="&#x2f;" u2="J" k="130" />
    <hkern u1="&#x2f;" u2="G" k="40" />
    <hkern u1="&#x2f;" u2="C" k="40" />
    <hkern u1="&#x2f;" u2="A" k="120" />
    <hkern u1="&#x2f;" u2="&#x39;" k="20" />
    <hkern u1="&#x2f;" u2="&#x38;" k="15" />
    <hkern u1="&#x2f;" u2="&#x37;" k="10" />
    <hkern u1="&#x2f;" u2="&#x36;" k="35" />
    <hkern u1="&#x2f;" u2="&#x35;" k="20" />
    <hkern u1="&#x2f;" u2="&#x34;" k="95" />
    <hkern u1="&#x2f;" u2="&#x33;" k="10" />
    <hkern u1="&#x2f;" u2="&#x32;" k="20" />
    <hkern u1="&#x2f;" u2="&#x31;" k="-10" />
    <hkern u1="&#x2f;" u2="&#x30;" k="35" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="163" />
    <hkern u1="&#x30;" u2="&#xb1;" k="15" />
    <hkern u1="&#x30;" u2="&#x37;" k="27" />
    <hkern u1="&#x30;" u2="&#x33;" k="10" />
    <hkern u1="&#x30;" u2="&#x32;" k="10" />
    <hkern u1="&#x30;" u2="&#x31;" k="5" />
    <hkern u1="&#x30;" u2="&#x2f;" k="35" />
    <hkern u1="&#x30;" u2="&#x2e;" k="20" />
    <hkern u1="&#x30;" u2="&#x2c;" k="20" />
    <hkern u1="&#x31;" u2="&#x2215;" k="-55" />
    <hkern u1="&#x32;" u2="&#x2215;" k="-60" />
    <hkern u1="&#x32;" u2="&#x37;" k="8" />
    <hkern u1="&#x32;" u2="&#x34;" k="20" />
    <hkern u1="&#x33;" u2="&#x2215;" k="-30" />
    <hkern u1="&#x33;" u2="&#x39;" k="5" />
    <hkern u1="&#x33;" u2="&#x37;" k="21" />
    <hkern u1="&#x33;" u2="&#x35;" k="5" />
    <hkern u1="&#x33;" u2="&#x2f;" k="10" />
    <hkern u1="&#x34;" u2="&#x2215;" k="-30" />
    <hkern u1="&#x34;" u2="&#x39;" k="10" />
    <hkern u1="&#x34;" u2="&#x37;" k="33" />
    <hkern u1="&#x34;" u2="&#x31;" k="20" />
    <hkern u1="&#x34;" u2="&#x2f;" k="20" />
    <hkern u1="&#x35;" u2="&#x2215;" k="-20" />
    <hkern u1="&#x35;" u2="&#xb1;" k="5" />
    <hkern u1="&#x35;" u2="&#x39;" k="5" />
    <hkern u1="&#x35;" u2="&#x37;" k="30" />
    <hkern u1="&#x35;" u2="&#x33;" k="5" />
    <hkern u1="&#x35;" u2="&#x32;" k="10" />
    <hkern u1="&#x35;" u2="&#x2f;" k="20" />
    <hkern u1="&#x36;" u2="&#x2215;" k="-30" />
    <hkern u1="&#x36;" u2="&#xb1;" k="5" />
    <hkern u1="&#x36;" u2="&#x39;" k="10" />
    <hkern u1="&#x36;" u2="&#x37;" k="18" />
    <hkern u1="&#x36;" u2="&#x33;" k="10" />
    <hkern u1="&#x36;" u2="&#x31;" k="13" />
    <hkern u1="&#x36;" u2="&#x2f;" k="10" />
    <hkern u1="&#x37;" u2="&#x2215;" k="110" />
    <hkern u1="&#x37;" u2="&#x2014;" k="30" />
    <hkern u1="&#x37;" u2="&#x2013;" k="30" />
    <hkern u1="&#x37;" u2="&#xb1;" k="10" />
    <hkern u1="&#x37;" u2="&#x39;" k="15" />
    <hkern u1="&#x37;" u2="&#x38;" k="10" />
    <hkern u1="&#x37;" u2="&#x36;" k="20" />
    <hkern u1="&#x37;" u2="&#x35;" k="25" />
    <hkern u1="&#x37;" u2="&#x34;" k="85" />
    <hkern u1="&#x37;" u2="&#x33;" k="20" />
    <hkern u1="&#x37;" u2="&#x32;" k="15" />
    <hkern u1="&#x37;" u2="&#x31;" k="-10" />
    <hkern u1="&#x37;" u2="&#x30;" k="20" />
    <hkern u1="&#x37;" u2="&#x2f;" k="140" />
    <hkern u1="&#x37;" u2="&#x2e;" k="100" />
    <hkern u1="&#x37;" u2="&#x2d;" k="30" />
    <hkern u1="&#x37;" u2="&#x2c;" k="100" />
    <hkern u1="&#x38;" u2="&#x2215;" k="-40" />
    <hkern u1="&#x38;" u2="&#xb1;" k="5" />
    <hkern u1="&#x38;" u2="&#x39;" k="5" />
    <hkern u1="&#x38;" u2="&#x37;" k="10" />
    <hkern u1="&#x39;" u2="&#x2215;" k="-15" />
    <hkern u1="&#x39;" u2="&#xb1;" k="10" />
    <hkern u1="&#x39;" u2="&#x37;" k="15" />
    <hkern u1="&#x39;" u2="&#x35;" k="5" />
    <hkern u1="&#x39;" u2="&#x33;" k="10" />
    <hkern u1="&#x39;" u2="&#x32;" k="10" />
    <hkern u1="&#x39;" u2="&#x2f;" k="25" />
    <hkern u1="&#x39;" u2="&#x2e;" k="10" />
    <hkern u1="&#x39;" u2="&#x2c;" k="10" />
    <hkern u1="&#x3a;" u2="Y" k="40" />
    <hkern u1="&#x3a;" u2="W" k="15" />
    <hkern u1="&#x3a;" u2="V" k="20" />
    <hkern u1="&#x3a;" u2="T" k="22" />
    <hkern u1="&#x3b;" u2="Y" k="40" />
    <hkern u1="&#x3b;" u2="W" k="15" />
    <hkern u1="&#x3b;" u2="V" k="20" />
    <hkern u1="&#x3b;" u2="T" k="22" />
    <hkern u1="A" u2="&#xfb02;" k="20" />
    <hkern u1="A" u2="&#xfb01;" k="20" />
    <hkern u1="A" u2="&#x2248;" k="20" />
    <hkern u1="A" u2="&#x221a;" k="20" />
    <hkern u1="A" u2="&#x2122;" k="100" />
    <hkern u1="A" u2="&#x201d;" k="80" />
    <hkern u1="A" u2="&#x201c;" k="80" />
    <hkern u1="A" u2="&#x2019;" k="80" />
    <hkern u1="A" u2="&#x2018;" k="80" />
    <hkern u1="A" u2="&#x2014;" k="40" />
    <hkern u1="A" u2="&#x2013;" k="40" />
    <hkern u1="A" u2="&#x3a9;" k="20" />
    <hkern u1="A" u2="&#x153;" k="25" />
    <hkern u1="A" u2="&#x152;" k="43" />
    <hkern u1="A" u2="&#xc6;" k="14" />
    <hkern u1="A" u2="&#xb5;" k="43" />
    <hkern u1="A" u2="&#xab;" k="20" />
    <hkern u1="A" u2="y" k="47" />
    <hkern u1="A" u2="w" k="52" />
    <hkern u1="A" u2="v" k="67" />
    <hkern u1="A" u2="u" k="10" />
    <hkern u1="A" u2="t" k="30" />
    <hkern u1="A" u2="q" k="25" />
    <hkern u1="A" u2="o" k="25" />
    <hkern u1="A" u2="g" k="25" />
    <hkern u1="A" u2="f" k="20" />
    <hkern u1="A" u2="e" k="25" />
    <hkern u1="A" u2="d" k="25" />
    <hkern u1="A" u2="c" k="25" />
    <hkern u1="A" u2="\" k="120" />
    <hkern u1="A" u2="Y" k="110" />
    <hkern u1="A" u2="X" k="14" />
    <hkern u1="A" u2="W" k="90" />
    <hkern u1="A" u2="V" k="100" />
    <hkern u1="A" u2="U" k="25" />
    <hkern u1="A" u2="T" k="90" />
    <hkern u1="A" u2="S" k="11" />
    <hkern u1="A" u2="Q" k="43" />
    <hkern u1="A" u2="O" k="43" />
    <hkern u1="A" u2="G" k="43" />
    <hkern u1="A" u2="C" k="43" />
    <hkern u1="A" u2="A" k="14" />
    <hkern u1="A" u2="&#x3f;" k="60" />
    <hkern u1="A" u2="&#x2d;" k="40" />
    <hkern u1="A" u2="&#x2a;" k="100" />
    <hkern u1="B" u2="y" k="10" />
    <hkern u1="B" u2="w" k="10" />
    <hkern u1="B" u2="v" k="10" />
    <hkern u1="B" u2="Y" k="30" />
    <hkern u1="B" u2="X" k="20" />
    <hkern u1="B" u2="W" k="15" />
    <hkern u1="B" u2="V" k="20" />
    <hkern u1="B" u2="T" k="13" />
    <hkern u1="B" u2="&#x3f;" k="5" />
    <hkern u1="B" u2="&#x26;" k="-10" />
    <hkern u1="C" u2="&#x2014;" k="10" />
    <hkern u1="C" u2="&#x2013;" k="10" />
    <hkern u1="C" u2="&#x153;" k="10" />
    <hkern u1="C" u2="&#x152;" k="20" />
    <hkern u1="C" u2="&#xb5;" k="20" />
    <hkern u1="C" u2="y" k="10" />
    <hkern u1="C" u2="x" k="10" />
    <hkern u1="C" u2="w" k="10" />
    <hkern u1="C" u2="v" k="10" />
    <hkern u1="C" u2="q" k="10" />
    <hkern u1="C" u2="o" k="10" />
    <hkern u1="C" u2="g" k="10" />
    <hkern u1="C" u2="e" k="10" />
    <hkern u1="C" u2="d" k="10" />
    <hkern u1="C" u2="c" k="10" />
    <hkern u1="C" u2="Y" k="10" />
    <hkern u1="C" u2="X" k="10" />
    <hkern u1="C" u2="W" k="3" />
    <hkern u1="C" u2="V" k="3" />
    <hkern u1="C" u2="Q" k="20" />
    <hkern u1="C" u2="O" k="20" />
    <hkern u1="C" u2="G" k="20" />
    <hkern u1="C" u2="C" k="20" />
    <hkern u1="C" u2="&#x2d;" k="10" />
    <hkern u1="D" u2="&#x2026;" k="40" />
    <hkern u1="D" u2="&#xc6;" k="48" />
    <hkern u1="D" u2="&#x7d;" k="20" />
    <hkern u1="D" u2="x" k="10" />
    <hkern u1="D" u2="]" k="20" />
    <hkern u1="D" u2="\" k="40" />
    <hkern u1="D" u2="Z" k="42" />
    <hkern u1="D" u2="Y" k="65" />
    <hkern u1="D" u2="X" k="55" />
    <hkern u1="D" u2="W" k="38" />
    <hkern u1="D" u2="V" k="45" />
    <hkern u1="D" u2="T" k="45" />
    <hkern u1="D" u2="S" k="10" />
    <hkern u1="D" u2="J" k="40" />
    <hkern u1="D" u2="A" k="48" />
    <hkern u1="D" u2="&#x3f;" k="20" />
    <hkern u1="D" u2="&#x2f;" k="40" />
    <hkern u1="D" u2="&#x2e;" k="40" />
    <hkern u1="D" u2="&#x2c;" k="40" />
    <hkern u1="D" u2="&#x29;" k="30" />
    <hkern u1="E" u2="&#x153;" k="10" />
    <hkern u1="E" u2="y" k="10" />
    <hkern u1="E" u2="w" k="10" />
    <hkern u1="E" u2="v" k="10" />
    <hkern u1="E" u2="o" k="10" />
    <hkern u1="E" u2="e" k="10" />
    <hkern u1="E" u2="d" k="10" />
    <hkern u1="E" u2="c" k="10" />
    <hkern u1="F" u2="&#x203a;" k="15" />
    <hkern u1="F" u2="&#x2026;" k="100" />
    <hkern u1="F" u2="&#x201d;" k="-20" />
    <hkern u1="F" u2="&#x2019;" k="-20" />
    <hkern u1="F" u2="&#x153;" k="15" />
    <hkern u1="F" u2="&#x152;" k="6" />
    <hkern u1="F" u2="&#xe6;" k="25" />
    <hkern u1="F" u2="&#xc6;" k="80" />
    <hkern u1="F" u2="&#xbb;" k="15" />
    <hkern u1="F" u2="&#xb5;" k="6" />
    <hkern u1="F" u2="z" k="15" />
    <hkern u1="F" u2="y" k="15" />
    <hkern u1="F" u2="w" k="10" />
    <hkern u1="F" u2="v" k="15" />
    <hkern u1="F" u2="s" k="10" />
    <hkern u1="F" u2="q" k="10" />
    <hkern u1="F" u2="o" k="15" />
    <hkern u1="F" u2="g" k="10" />
    <hkern u1="F" u2="e" k="15" />
    <hkern u1="F" u2="d" k="10" />
    <hkern u1="F" u2="c" k="15" />
    <hkern u1="F" u2="a" k="25" />
    <hkern u1="F" u2="Z" k="10" />
    <hkern u1="F" u2="Q" k="6" />
    <hkern u1="F" u2="O" k="6" />
    <hkern u1="F" u2="J" k="110" />
    <hkern u1="F" u2="G" k="6" />
    <hkern u1="F" u2="C" k="6" />
    <hkern u1="F" u2="A" k="80" />
    <hkern u1="F" u2="&#x3f;" k="-10" />
    <hkern u1="F" u2="&#x2f;" k="70" />
    <hkern u1="F" u2="&#x2e;" k="100" />
    <hkern u1="F" u2="&#x2c;" k="100" />
    <hkern u1="F" u2="&#x26;" k="20" />
    <hkern u1="G" u2="&#xe6;" k="-10" />
    <hkern u1="G" u2="y" k="5" />
    <hkern u1="G" u2="v" k="5" />
    <hkern u1="G" u2="a" k="-10" />
    <hkern u1="G" u2="\" k="15" />
    <hkern u1="G" u2="Y" k="30" />
    <hkern u1="G" u2="X" k="10" />
    <hkern u1="G" u2="W" k="18" />
    <hkern u1="G" u2="V" k="23" />
    <hkern u1="G" u2="T" k="13" />
    <hkern u1="G" u2="&#x3f;" k="10" />
    <hkern u1="J" u2="&#x2026;" k="15" />
    <hkern u1="J" u2="&#xc6;" k="25" />
    <hkern u1="J" u2="J" k="20" />
    <hkern u1="J" u2="A" k="25" />
    <hkern u1="J" u2="&#x2e;" k="15" />
    <hkern u1="J" u2="&#x2c;" k="15" />
    <hkern u1="K" u2="&#xfb02;" k="20" />
    <hkern u1="K" u2="&#xfb01;" k="20" />
    <hkern u1="K" u2="&#x2248;" k="20" />
    <hkern u1="K" u2="&#x221a;" k="20" />
    <hkern u1="K" u2="&#x2014;" k="50" />
    <hkern u1="K" u2="&#x2013;" k="50" />
    <hkern u1="K" u2="&#x3a9;" k="20" />
    <hkern u1="K" u2="&#x153;" k="30" />
    <hkern u1="K" u2="&#x152;" k="53" />
    <hkern u1="K" u2="&#xe6;" k="10" />
    <hkern u1="K" u2="&#xc6;" k="14" />
    <hkern u1="K" u2="&#xb5;" k="53" />
    <hkern u1="K" u2="&#xab;" k="20" />
    <hkern u1="K" u2="y" k="50" />
    <hkern u1="K" u2="w" k="50" />
    <hkern u1="K" u2="v" k="60" />
    <hkern u1="K" u2="u" k="20" />
    <hkern u1="K" u2="t" k="25" />
    <hkern u1="K" u2="q" k="25" />
    <hkern u1="K" u2="o" k="30" />
    <hkern u1="K" u2="g" k="25" />
    <hkern u1="K" u2="f" k="20" />
    <hkern u1="K" u2="e" k="30" />
    <hkern u1="K" u2="d" k="25" />
    <hkern u1="K" u2="c" k="30" />
    <hkern u1="K" u2="a" k="10" />
    <hkern u1="K" u2="Y" k="33" />
    <hkern u1="K" u2="W" k="30" />
    <hkern u1="K" u2="V" k="30" />
    <hkern u1="K" u2="U" k="15" />
    <hkern u1="K" u2="T" k="10" />
    <hkern u1="K" u2="S" k="10" />
    <hkern u1="K" u2="Q" k="53" />
    <hkern u1="K" u2="O" k="53" />
    <hkern u1="K" u2="G" k="53" />
    <hkern u1="K" u2="C" k="53" />
    <hkern u1="K" u2="A" k="14" />
    <hkern u1="K" u2="&#x2d;" k="50" />
    <hkern u1="K" u2="&#x26;" k="3" />
    <hkern u1="L" u2="&#xfb02;" k="20" />
    <hkern u1="L" u2="&#xfb01;" k="20" />
    <hkern u1="L" u2="&#x2248;" k="20" />
    <hkern u1="L" u2="&#x221a;" k="20" />
    <hkern u1="L" u2="&#x2122;" k="90" />
    <hkern u1="L" u2="&#x201d;" k="40" />
    <hkern u1="L" u2="&#x201c;" k="40" />
    <hkern u1="L" u2="&#x2019;" k="40" />
    <hkern u1="L" u2="&#x2018;" k="40" />
    <hkern u1="L" u2="&#x2014;" k="40" />
    <hkern u1="L" u2="&#x2013;" k="40" />
    <hkern u1="L" u2="&#x3a9;" k="20" />
    <hkern u1="L" u2="&#x153;" k="10" />
    <hkern u1="L" u2="&#x152;" k="40" />
    <hkern u1="L" u2="&#xb5;" k="40" />
    <hkern u1="L" u2="y" k="60" />
    <hkern u1="L" u2="w" k="50" />
    <hkern u1="L" u2="v" k="60" />
    <hkern u1="L" u2="t" k="20" />
    <hkern u1="L" u2="q" k="5" />
    <hkern u1="L" u2="o" k="10" />
    <hkern u1="L" u2="g" k="5" />
    <hkern u1="L" u2="f" k="20" />
    <hkern u1="L" u2="e" k="10" />
    <hkern u1="L" u2="d" k="5" />
    <hkern u1="L" u2="c" k="10" />
    <hkern u1="L" u2="\" k="120" />
    <hkern u1="L" u2="Y" k="130" />
    <hkern u1="L" u2="W" k="100" />
    <hkern u1="L" u2="V" k="115" />
    <hkern u1="L" u2="U" k="20" />
    <hkern u1="L" u2="T" k="100" />
    <hkern u1="L" u2="Q" k="40" />
    <hkern u1="L" u2="O" k="40" />
    <hkern u1="L" u2="G" k="40" />
    <hkern u1="L" u2="C" k="40" />
    <hkern u1="L" u2="&#x3f;" k="60" />
    <hkern u1="L" u2="&#x2d;" k="40" />
    <hkern u1="L" u2="&#x2a;" k="80" />
    <hkern u1="L" u2="&#x26;" k="3" />
    <hkern u1="O" u2="&#x2026;" k="40" />
    <hkern u1="O" u2="&#xc6;" k="43" />
    <hkern u1="O" u2="&#x7d;" k="20" />
    <hkern u1="O" u2="x" k="5" />
    <hkern u1="O" u2="]" k="20" />
    <hkern u1="O" u2="\" k="40" />
    <hkern u1="O" u2="Z" k="37" />
    <hkern u1="O" u2="Y" k="60" />
    <hkern u1="O" u2="X" k="50" />
    <hkern u1="O" u2="W" k="38" />
    <hkern u1="O" u2="V" k="43" />
    <hkern u1="O" u2="T" k="38" />
    <hkern u1="O" u2="S" k="5" />
    <hkern u1="O" u2="J" k="30" />
    <hkern u1="O" u2="A" k="43" />
    <hkern u1="O" u2="&#x3f;" k="20" />
    <hkern u1="O" u2="&#x2f;" k="40" />
    <hkern u1="O" u2="&#x2e;" k="40" />
    <hkern u1="O" u2="&#x2c;" k="40" />
    <hkern u1="O" u2="&#x29;" k="30" />
    <hkern u1="P" u2="&#xfb02;" k="-15" />
    <hkern u1="P" u2="&#xfb01;" k="-15" />
    <hkern u1="P" u2="&#x2248;" k="-15" />
    <hkern u1="P" u2="&#x221a;" k="-15" />
    <hkern u1="P" u2="&#x2026;" k="100" />
    <hkern u1="P" u2="&#x201d;" k="-20" />
    <hkern u1="P" u2="&#x2019;" k="-20" />
    <hkern u1="P" u2="&#x3a9;" k="-15" />
    <hkern u1="P" u2="&#x153;" k="5" />
    <hkern u1="P" u2="&#xe6;" k="10" />
    <hkern u1="P" u2="&#xc6;" k="70" />
    <hkern u1="P" u2="&#xab;" k="-10" />
    <hkern u1="P" u2="y" k="-10" />
    <hkern u1="P" u2="w" k="-10" />
    <hkern u1="P" u2="v" k="-10" />
    <hkern u1="P" u2="u" k="-5" />
    <hkern u1="P" u2="t" k="-15" />
    <hkern u1="P" u2="o" k="5" />
    <hkern u1="P" u2="f" k="-15" />
    <hkern u1="P" u2="e" k="5" />
    <hkern u1="P" u2="c" k="5" />
    <hkern u1="P" u2="a" k="10" />
    <hkern u1="P" u2="Z" k="15" />
    <hkern u1="P" u2="Y" k="10" />
    <hkern u1="P" u2="X" k="30" />
    <hkern u1="P" u2="W" k="5" />
    <hkern u1="P" u2="V" k="10" />
    <hkern u1="P" u2="J" k="100" />
    <hkern u1="P" u2="A" k="70" />
    <hkern u1="P" u2="&#x2f;" k="60" />
    <hkern u1="P" u2="&#x2e;" k="100" />
    <hkern u1="P" u2="&#x2c;" k="100" />
    <hkern u1="P" u2="&#x26;" k="6" />
    <hkern u1="Q" u2="Y" k="65" />
    <hkern u1="Q" u2="W" k="38" />
    <hkern u1="Q" u2="V" k="43" />
    <hkern u1="Q" u2="T" k="38" />
    <hkern u1="Q" u2="&#x3f;" k="20" />
    <hkern u1="Q" u2="&#x29;" k="10" />
    <hkern u1="R" u2="&#xfb02;" k="-10" />
    <hkern u1="R" u2="&#xfb01;" k="-10" />
    <hkern u1="R" u2="&#x2248;" k="-10" />
    <hkern u1="R" u2="&#x221a;" k="-10" />
    <hkern u1="R" u2="&#x3a9;" k="-10" />
    <hkern u1="R" u2="&#x153;" k="10" />
    <hkern u1="R" u2="t" k="-10" />
    <hkern u1="R" u2="q" k="5" />
    <hkern u1="R" u2="o" k="10" />
    <hkern u1="R" u2="g" k="5" />
    <hkern u1="R" u2="f" k="-10" />
    <hkern u1="R" u2="e" k="10" />
    <hkern u1="R" u2="d" k="5" />
    <hkern u1="R" u2="c" k="10" />
    <hkern u1="R" u2="Y" k="25" />
    <hkern u1="R" u2="W" k="15" />
    <hkern u1="R" u2="V" k="20" />
    <hkern u1="R" u2="T" k="3" />
    <hkern u1="R" u2="J" k="5" />
    <hkern u1="S" u2="&#xfb02;" k="5" />
    <hkern u1="S" u2="&#xfb01;" k="5" />
    <hkern u1="S" u2="&#x2248;" k="5" />
    <hkern u1="S" u2="&#x221a;" k="5" />
    <hkern u1="S" u2="&#x3a9;" k="5" />
    <hkern u1="S" u2="&#xc6;" k="15" />
    <hkern u1="S" u2="z" k="5" />
    <hkern u1="S" u2="y" k="15" />
    <hkern u1="S" u2="x" k="15" />
    <hkern u1="S" u2="w" k="10" />
    <hkern u1="S" u2="v" k="15" />
    <hkern u1="S" u2="t" k="5" />
    <hkern u1="S" u2="f" k="5" />
    <hkern u1="S" u2="\" k="20" />
    <hkern u1="S" u2="Z" k="10" />
    <hkern u1="S" u2="Y" k="30" />
    <hkern u1="S" u2="X" k="25" />
    <hkern u1="S" u2="W" k="25" />
    <hkern u1="S" u2="V" k="30" />
    <hkern u1="S" u2="T" k="15" />
    <hkern u1="S" u2="S" k="10" />
    <hkern u1="S" u2="A" k="15" />
    <hkern u1="S" u2="&#x3f;" k="10" />
    <hkern u1="T" u2="&#xfb02;" k="36" />
    <hkern u1="T" u2="&#xfb01;" k="36" />
    <hkern u1="T" u2="&#x2248;" k="36" />
    <hkern u1="T" u2="&#x221a;" k="36" />
    <hkern u1="T" u2="&#x203a;" k="70" />
    <hkern u1="T" u2="&#x2026;" k="100" />
    <hkern u1="T" u2="&#x2014;" k="90" />
    <hkern u1="T" u2="&#x2013;" k="90" />
    <hkern u1="T" u2="&#x3a9;" k="36" />
    <hkern u1="T" u2="&#x153;" k="114" />
    <hkern u1="T" u2="&#x152;" k="38" />
    <hkern u1="T" u2="&#xe6;" k="114" />
    <hkern u1="T" u2="&#xc6;" k="90" />
    <hkern u1="T" u2="&#xbb;" k="70" />
    <hkern u1="T" u2="&#xb5;" k="38" />
    <hkern u1="T" u2="&#xab;" k="90" />
    <hkern u1="T" u2="z" k="89" />
    <hkern u1="T" u2="y" k="65" />
    <hkern u1="T" u2="x" k="65" />
    <hkern u1="T" u2="w" k="58" />
    <hkern u1="T" u2="v" k="65" />
    <hkern u1="T" u2="u" k="65" />
    <hkern u1="T" u2="t" k="29" />
    <hkern u1="T" u2="s" k="98" />
    <hkern u1="T" u2="r" k="72" />
    <hkern u1="T" u2="q" k="104" />
    <hkern u1="T" u2="p" k="72" />
    <hkern u1="T" u2="o" k="114" />
    <hkern u1="T" u2="n" k="72" />
    <hkern u1="T" u2="m" k="72" />
    <hkern u1="T" u2="l" k="8" />
    <hkern u1="T" u2="j" k="19" />
    <hkern u1="T" u2="i" k="19" />
    <hkern u1="T" u2="h" k="6" />
    <hkern u1="T" u2="g" k="104" />
    <hkern u1="T" u2="f" k="36" />
    <hkern u1="T" u2="e" k="114" />
    <hkern u1="T" u2="d" k="104" />
    <hkern u1="T" u2="c" k="114" />
    <hkern u1="T" u2="a" k="114" />
    <hkern u1="T" u2="Z" k="13" />
    <hkern u1="T" u2="S" k="15" />
    <hkern u1="T" u2="Q" k="38" />
    <hkern u1="T" u2="O" k="38" />
    <hkern u1="T" u2="J" k="110" />
    <hkern u1="T" u2="G" k="38" />
    <hkern u1="T" u2="C" k="38" />
    <hkern u1="T" u2="A" k="90" />
    <hkern u1="T" u2="&#x3b;" k="22" />
    <hkern u1="T" u2="&#x3a;" k="22" />
    <hkern u1="T" u2="&#x2f;" k="90" />
    <hkern u1="T" u2="&#x2e;" k="100" />
    <hkern u1="T" u2="&#x2d;" k="90" />
    <hkern u1="T" u2="&#x2c;" k="100" />
    <hkern u1="T" u2="&#x26;" k="52" />
    <hkern u1="U" u2="&#x2026;" k="15" />
    <hkern u1="U" u2="&#xc6;" k="25" />
    <hkern u1="U" u2="x" k="5" />
    <hkern u1="U" u2="X" k="10" />
    <hkern u1="U" u2="J" k="20" />
    <hkern u1="U" u2="A" k="25" />
    <hkern u1="U" u2="&#x2f;" k="15" />
    <hkern u1="U" u2="&#x2e;" k="15" />
    <hkern u1="U" u2="&#x2c;" k="15" />
    <hkern u1="V" u2="&#xfb02;" k="25" />
    <hkern u1="V" u2="&#xfb01;" k="25" />
    <hkern u1="V" u2="&#x2248;" k="25" />
    <hkern u1="V" u2="&#x221a;" k="25" />
    <hkern u1="V" u2="&#x203a;" k="40" />
    <hkern u1="V" u2="&#x2026;" k="120" />
    <hkern u1="V" u2="&#x2014;" k="40" />
    <hkern u1="V" u2="&#x2013;" k="40" />
    <hkern u1="V" u2="&#x3a9;" k="25" />
    <hkern u1="V" u2="&#x153;" k="70" />
    <hkern u1="V" u2="&#x152;" k="43" />
    <hkern u1="V" u2="&#xe6;" k="70" />
    <hkern u1="V" u2="&#xc6;" k="100" />
    <hkern u1="V" u2="&#xbb;" k="40" />
    <hkern u1="V" u2="&#xb5;" k="43" />
    <hkern u1="V" u2="&#xab;" k="60" />
    <hkern u1="V" u2="z" k="55" />
    <hkern u1="V" u2="y" k="40" />
    <hkern u1="V" u2="x" k="50" />
    <hkern u1="V" u2="w" k="35" />
    <hkern u1="V" u2="v" k="40" />
    <hkern u1="V" u2="u" k="40" />
    <hkern u1="V" u2="t" k="20" />
    <hkern u1="V" u2="s" k="60" />
    <hkern u1="V" u2="r" k="40" />
    <hkern u1="V" u2="q" k="65" />
    <hkern u1="V" u2="p" k="40" />
    <hkern u1="V" u2="o" k="70" />
    <hkern u1="V" u2="n" k="40" />
    <hkern u1="V" u2="m" k="40" />
    <hkern u1="V" u2="l" k="10" />
    <hkern u1="V" u2="j" k="20" />
    <hkern u1="V" u2="i" k="20" />
    <hkern u1="V" u2="g" k="65" />
    <hkern u1="V" u2="f" k="25" />
    <hkern u1="V" u2="e" k="70" />
    <hkern u1="V" u2="d" k="65" />
    <hkern u1="V" u2="c" k="70" />
    <hkern u1="V" u2="a" k="70" />
    <hkern u1="V" u2="Z" k="10" />
    <hkern u1="V" u2="Y" k="20" />
    <hkern u1="V" u2="X" k="20" />
    <hkern u1="V" u2="W" k="10" />
    <hkern u1="V" u2="V" k="10" />
    <hkern u1="V" u2="S" k="25" />
    <hkern u1="V" u2="Q" k="43" />
    <hkern u1="V" u2="O" k="43" />
    <hkern u1="V" u2="J" k="120" />
    <hkern u1="V" u2="G" k="43" />
    <hkern u1="V" u2="C" k="43" />
    <hkern u1="V" u2="A" k="100" />
    <hkern u1="V" u2="&#x3b;" k="20" />
    <hkern u1="V" u2="&#x3a;" k="20" />
    <hkern u1="V" u2="&#x2f;" k="120" />
    <hkern u1="V" u2="&#x2e;" k="120" />
    <hkern u1="V" u2="&#x2d;" k="40" />
    <hkern u1="V" u2="&#x2c;" k="120" />
    <hkern u1="V" u2="&#x26;" k="45" />
    <hkern u1="W" u2="&#xfb02;" k="30" />
    <hkern u1="W" u2="&#xfb01;" k="30" />
    <hkern u1="W" u2="&#x2248;" k="30" />
    <hkern u1="W" u2="&#x221a;" k="30" />
    <hkern u1="W" u2="&#x203a;" k="35" />
    <hkern u1="W" u2="&#x2026;" k="100" />
    <hkern u1="W" u2="&#x2014;" k="35" />
    <hkern u1="W" u2="&#x2013;" k="35" />
    <hkern u1="W" u2="&#x3a9;" k="30" />
    <hkern u1="W" u2="&#x153;" k="65" />
    <hkern u1="W" u2="&#x152;" k="38" />
    <hkern u1="W" u2="&#xe6;" k="70" />
    <hkern u1="W" u2="&#xc6;" k="90" />
    <hkern u1="W" u2="&#xbb;" k="35" />
    <hkern u1="W" u2="&#xb5;" k="38" />
    <hkern u1="W" u2="&#xab;" k="50" />
    <hkern u1="W" u2="z" k="55" />
    <hkern u1="W" u2="y" k="35" />
    <hkern u1="W" u2="x" k="40" />
    <hkern u1="W" u2="w" k="35" />
    <hkern u1="W" u2="v" k="35" />
    <hkern u1="W" u2="u" k="35" />
    <hkern u1="W" u2="t" k="25" />
    <hkern u1="W" u2="s" k="60" />
    <hkern u1="W" u2="r" k="35" />
    <hkern u1="W" u2="q" k="60" />
    <hkern u1="W" u2="p" k="35" />
    <hkern u1="W" u2="o" k="65" />
    <hkern u1="W" u2="n" k="35" />
    <hkern u1="W" u2="m" k="35" />
    <hkern u1="W" u2="l" k="10" />
    <hkern u1="W" u2="j" k="15" />
    <hkern u1="W" u2="i" k="15" />
    <hkern u1="W" u2="g" k="60" />
    <hkern u1="W" u2="f" k="30" />
    <hkern u1="W" u2="e" k="65" />
    <hkern u1="W" u2="d" k="60" />
    <hkern u1="W" u2="c" k="65" />
    <hkern u1="W" u2="a" k="70" />
    <hkern u1="W" u2="Z" k="10" />
    <hkern u1="W" u2="Y" k="20" />
    <hkern u1="W" u2="X" k="15" />
    <hkern u1="W" u2="W" k="10" />
    <hkern u1="W" u2="V" k="10" />
    <hkern u1="W" u2="S" k="20" />
    <hkern u1="W" u2="Q" k="38" />
    <hkern u1="W" u2="O" k="38" />
    <hkern u1="W" u2="J" k="105" />
    <hkern u1="W" u2="G" k="38" />
    <hkern u1="W" u2="C" k="38" />
    <hkern u1="W" u2="A" k="90" />
    <hkern u1="W" u2="&#x3b;" k="15" />
    <hkern u1="W" u2="&#x3a;" k="15" />
    <hkern u1="W" u2="&#x2f;" k="100" />
    <hkern u1="W" u2="&#x2e;" k="100" />
    <hkern u1="W" u2="&#x2d;" k="35" />
    <hkern u1="W" u2="&#x2c;" k="100" />
    <hkern u1="W" u2="&#x26;" k="35" />
    <hkern u1="X" u2="&#xfb02;" k="20" />
    <hkern u1="X" u2="&#xfb01;" k="20" />
    <hkern u1="X" u2="&#x2248;" k="20" />
    <hkern u1="X" u2="&#x221a;" k="20" />
    <hkern u1="X" u2="&#x203a;" k="20" />
    <hkern u1="X" u2="&#x2014;" k="50" />
    <hkern u1="X" u2="&#x2013;" k="50" />
    <hkern u1="X" u2="&#x3a9;" k="20" />
    <hkern u1="X" u2="&#x153;" k="45" />
    <hkern u1="X" u2="&#x152;" k="50" />
    <hkern u1="X" u2="&#xe6;" k="10" />
    <hkern u1="X" u2="&#xc6;" k="14" />
    <hkern u1="X" u2="&#xbb;" k="20" />
    <hkern u1="X" u2="&#xb5;" k="50" />
    <hkern u1="X" u2="&#xab;" k="50" />
    <hkern u1="X" u2="y" k="40" />
    <hkern u1="X" u2="w" k="40" />
    <hkern u1="X" u2="v" k="50" />
    <hkern u1="X" u2="u" k="20" />
    <hkern u1="X" u2="t" k="20" />
    <hkern u1="X" u2="q" k="40" />
    <hkern u1="X" u2="o" k="45" />
    <hkern u1="X" u2="l" k="10" />
    <hkern u1="X" u2="j" k="10" />
    <hkern u1="X" u2="i" k="10" />
    <hkern u1="X" u2="g" k="40" />
    <hkern u1="X" u2="f" k="20" />
    <hkern u1="X" u2="e" k="45" />
    <hkern u1="X" u2="d" k="40" />
    <hkern u1="X" u2="c" k="45" />
    <hkern u1="X" u2="a" k="10" />
    <hkern u1="X" u2="Y" k="27" />
    <hkern u1="X" u2="W" k="15" />
    <hkern u1="X" u2="V" k="20" />
    <hkern u1="X" u2="U" k="10" />
    <hkern u1="X" u2="S" k="30" />
    <hkern u1="X" u2="Q" k="50" />
    <hkern u1="X" u2="O" k="50" />
    <hkern u1="X" u2="J" k="10" />
    <hkern u1="X" u2="G" k="50" />
    <hkern u1="X" u2="C" k="50" />
    <hkern u1="X" u2="A" k="14" />
    <hkern u1="X" u2="&#x3f;" k="15" />
    <hkern u1="X" u2="&#x2d;" k="50" />
    <hkern u1="X" u2="&#x26;" k="10" />
    <hkern u1="Y" u2="&#xfb02;" k="40" />
    <hkern u1="Y" u2="&#xfb01;" k="40" />
    <hkern u1="Y" u2="&#x2248;" k="40" />
    <hkern u1="Y" u2="&#x221a;" k="40" />
    <hkern u1="Y" u2="&#x203a;" k="75" />
    <hkern u1="Y" u2="&#x2026;" k="130" />
    <hkern u1="Y" u2="&#x2014;" k="80" />
    <hkern u1="Y" u2="&#x2013;" k="80" />
    <hkern u1="Y" u2="&#x3a9;" k="40" />
    <hkern u1="Y" u2="&#x153;" k="110" />
    <hkern u1="Y" u2="&#x152;" k="60" />
    <hkern u1="Y" u2="&#xe6;" k="100" />
    <hkern u1="Y" u2="&#xc6;" k="110" />
    <hkern u1="Y" u2="&#xbb;" k="75" />
    <hkern u1="Y" u2="&#xb5;" k="60" />
    <hkern u1="Y" u2="&#xab;" k="100" />
    <hkern u1="Y" u2="z" k="80" />
    <hkern u1="Y" u2="y" k="60" />
    <hkern u1="Y" u2="x" k="70" />
    <hkern u1="Y" u2="w" k="55" />
    <hkern u1="Y" u2="v" k="60" />
    <hkern u1="Y" u2="u" k="75" />
    <hkern u1="Y" u2="t" k="30" />
    <hkern u1="Y" u2="s" k="100" />
    <hkern u1="Y" u2="r" k="75" />
    <hkern u1="Y" u2="q" k="105" />
    <hkern u1="Y" u2="p" k="75" />
    <hkern u1="Y" u2="o" k="110" />
    <hkern u1="Y" u2="n" k="75" />
    <hkern u1="Y" u2="m" k="75" />
    <hkern u1="Y" u2="l" k="10" />
    <hkern u1="Y" u2="j" k="20" />
    <hkern u1="Y" u2="i" k="20" />
    <hkern u1="Y" u2="g" k="105" />
    <hkern u1="Y" u2="f" k="40" />
    <hkern u1="Y" u2="e" k="110" />
    <hkern u1="Y" u2="d" k="105" />
    <hkern u1="Y" u2="c" k="110" />
    <hkern u1="Y" u2="a" k="100" />
    <hkern u1="Y" u2="Z" k="10" />
    <hkern u1="Y" u2="Y" k="14" />
    <hkern u1="Y" u2="X" k="27" />
    <hkern u1="Y" u2="W" k="20" />
    <hkern u1="Y" u2="V" k="20" />
    <hkern u1="Y" u2="S" k="35" />
    <hkern u1="Y" u2="Q" k="60" />
    <hkern u1="Y" u2="O" k="60" />
    <hkern u1="Y" u2="J" k="130" />
    <hkern u1="Y" u2="G" k="60" />
    <hkern u1="Y" u2="C" k="60" />
    <hkern u1="Y" u2="A" k="110" />
    <hkern u1="Y" u2="&#x3b;" k="40" />
    <hkern u1="Y" u2="&#x3a;" k="40" />
    <hkern u1="Y" u2="&#x2f;" k="110" />
    <hkern u1="Y" u2="&#x2e;" k="130" />
    <hkern u1="Y" u2="&#x2d;" k="80" />
    <hkern u1="Y" u2="&#x2c;" k="130" />
    <hkern u1="Y" u2="&#x26;" k="60" />
    <hkern u1="Z" u2="&#xfb02;" k="10" />
    <hkern u1="Z" u2="&#xfb01;" k="10" />
    <hkern u1="Z" u2="&#x2248;" k="10" />
    <hkern u1="Z" u2="&#x221a;" k="10" />
    <hkern u1="Z" u2="&#x2014;" k="30" />
    <hkern u1="Z" u2="&#x2013;" k="30" />
    <hkern u1="Z" u2="&#x3a9;" k="10" />
    <hkern u1="Z" u2="&#x153;" k="25" />
    <hkern u1="Z" u2="&#x152;" k="37" />
    <hkern u1="Z" u2="&#xb5;" k="37" />
    <hkern u1="Z" u2="&#xab;" k="20" />
    <hkern u1="Z" u2="y" k="15" />
    <hkern u1="Z" u2="w" k="15" />
    <hkern u1="Z" u2="v" k="20" />
    <hkern u1="Z" u2="q" k="20" />
    <hkern u1="Z" u2="o" k="25" />
    <hkern u1="Z" u2="g" k="20" />
    <hkern u1="Z" u2="f" k="10" />
    <hkern u1="Z" u2="e" k="25" />
    <hkern u1="Z" u2="d" k="20" />
    <hkern u1="Z" u2="c" k="25" />
    <hkern u1="Z" u2="Z" k="10" />
    <hkern u1="Z" u2="S" k="10" />
    <hkern u1="Z" u2="Q" k="37" />
    <hkern u1="Z" u2="O" k="37" />
    <hkern u1="Z" u2="G" k="37" />
    <hkern u1="Z" u2="C" k="37" />
    <hkern u1="Z" u2="&#x2d;" k="30" />
    <hkern u1="Z" u2="&#x26;" k="5" />
    <hkern u1="[" u2="&#x153;" k="20" />
    <hkern u1="[" u2="&#x152;" k="20" />
    <hkern u1="[" u2="&#xe6;" k="10" />
    <hkern u1="[" u2="&#xb5;" k="20" />
    <hkern u1="[" u2="y" k="10" />
    <hkern u1="[" u2="x" k="10" />
    <hkern u1="[" u2="w" k="20" />
    <hkern u1="[" u2="v" k="20" />
    <hkern u1="[" u2="s" k="15" />
    <hkern u1="[" u2="q" k="20" />
    <hkern u1="[" u2="o" k="20" />
    <hkern u1="[" u2="j" k="-30" />
    <hkern u1="[" u2="e" k="20" />
    <hkern u1="[" u2="d" k="20" />
    <hkern u1="[" u2="c" k="20" />
    <hkern u1="[" u2="a" k="10" />
    <hkern u1="[" u2="Q" k="20" />
    <hkern u1="[" u2="O" k="20" />
    <hkern u1="[" u2="J" k="10" />
    <hkern u1="[" u2="G" k="20" />
    <hkern u1="[" u2="C" k="20" />
    <hkern u1="\" u2="&#xfb02;" k="10" />
    <hkern u1="\" u2="&#xfb01;" k="10" />
    <hkern u1="\" u2="&#x2248;" k="10" />
    <hkern u1="\" u2="&#x221a;" k="10" />
    <hkern u1="\" u2="&#x3a9;" k="10" />
    <hkern u1="\" u2="&#x152;" k="40" />
    <hkern u1="\" u2="&#xb5;" k="40" />
    <hkern u1="\" u2="y" k="60" />
    <hkern u1="\" u2="w" k="60" />
    <hkern u1="\" u2="v" k="70" />
    <hkern u1="\" u2="t" k="30" />
    <hkern u1="\" u2="j" k="-30" />
    <hkern u1="\" u2="f" k="10" />
    <hkern u1="\" u2="Y" k="110" />
    <hkern u1="\" u2="W" k="100" />
    <hkern u1="\" u2="V" k="120" />
    <hkern u1="\" u2="U" k="15" />
    <hkern u1="\" u2="T" k="90" />
    <hkern u1="\" u2="Q" k="40" />
    <hkern u1="\" u2="O" k="40" />
    <hkern u1="\" u2="G" k="40" />
    <hkern u1="\" u2="C" k="40" />
    <hkern u1="a" u2="y" k="20" />
    <hkern u1="a" u2="w" k="20" />
    <hkern u1="a" u2="v" k="20" />
    <hkern u1="a" u2="t" k="5" />
    <hkern u1="a" u2="\" k="75" />
    <hkern u1="a" u2="&#x3f;" k="35" />
    <hkern u1="a" u2="&#x2a;" k="15" />
    <hkern u1="b" u2="&#x203a;" k="5" />
    <hkern u1="b" u2="&#x2026;" k="10" />
    <hkern u1="b" u2="&#x201c;" k="10" />
    <hkern u1="b" u2="&#x2018;" k="10" />
    <hkern u1="b" u2="&#xbb;" k="5" />
    <hkern u1="b" u2="&#x7d;" k="15" />
    <hkern u1="b" u2="z" k="15" />
    <hkern u1="b" u2="y" k="25" />
    <hkern u1="b" u2="x" k="30" />
    <hkern u1="b" u2="w" k="20" />
    <hkern u1="b" u2="v" k="25" />
    <hkern u1="b" u2="]" k="20" />
    <hkern u1="b" u2="\" k="70" />
    <hkern u1="b" u2="&#x3f;" k="35" />
    <hkern u1="b" u2="&#x2e;" k="10" />
    <hkern u1="b" u2="&#x2c;" k="10" />
    <hkern u1="b" u2="&#x2a;" k="15" />
    <hkern u1="b" u2="&#x29;" k="30" />
    <hkern u1="c" u2="&#x203a;" k="-10" />
    <hkern u1="c" u2="&#x2039;" k="10" />
    <hkern u1="c" u2="&#x201d;" k="-15" />
    <hkern u1="c" u2="&#x201c;" k="-10" />
    <hkern u1="c" u2="&#x2019;" k="-15" />
    <hkern u1="c" u2="&#x2018;" k="-10" />
    <hkern u1="c" u2="&#x153;" k="15" />
    <hkern u1="c" u2="&#xbb;" k="-10" />
    <hkern u1="c" u2="&#xab;" k="10" />
    <hkern u1="c" u2="y" k="5" />
    <hkern u1="c" u2="x" k="10" />
    <hkern u1="c" u2="w" k="5" />
    <hkern u1="c" u2="v" k="5" />
    <hkern u1="c" u2="q" k="10" />
    <hkern u1="c" u2="o" k="15" />
    <hkern u1="c" u2="g" k="10" />
    <hkern u1="c" u2="e" k="15" />
    <hkern u1="c" u2="d" k="10" />
    <hkern u1="c" u2="c" k="15" />
    <hkern u1="c" u2="\" k="40" />
    <hkern u1="c" u2="&#x3f;" k="15" />
    <hkern u1="c" u2="&#x29;" k="15" />
    <hkern u1="e" u2="&#x2026;" k="10" />
    <hkern u1="e" u2="&#x7d;" k="10" />
    <hkern u1="e" u2="z" k="15" />
    <hkern u1="e" u2="y" k="25" />
    <hkern u1="e" u2="x" k="30" />
    <hkern u1="e" u2="w" k="25" />
    <hkern u1="e" u2="v" k="25" />
    <hkern u1="e" u2="]" k="20" />
    <hkern u1="e" u2="\" k="80" />
    <hkern u1="e" u2="&#x3f;" k="40" />
    <hkern u1="e" u2="&#x2e;" k="10" />
    <hkern u1="e" u2="&#x2c;" k="10" />
    <hkern u1="e" u2="&#x2a;" k="20" />
    <hkern u1="e" u2="&#x29;" k="30" />
    <hkern u1="f" u2="&#x2122;" k="-45" />
    <hkern u1="f" u2="&#x2039;" k="15" />
    <hkern u1="f" u2="&#x2026;" k="45" />
    <hkern u1="f" u2="&#x201d;" k="-35" />
    <hkern u1="f" u2="&#x201c;" k="-30" />
    <hkern u1="f" u2="&#x2019;" k="-35" />
    <hkern u1="f" u2="&#x2018;" k="-30" />
    <hkern u1="f" u2="&#x153;" k="10" />
    <hkern u1="f" u2="&#xe6;" k="15" />
    <hkern u1="f" u2="&#xab;" k="15" />
    <hkern u1="f" u2="&#x7d;" k="-30" />
    <hkern u1="f" u2="z" k="10" />
    <hkern u1="f" u2="q" k="10" />
    <hkern u1="f" u2="o" k="10" />
    <hkern u1="f" u2="g" k="10" />
    <hkern u1="f" u2="e" k="10" />
    <hkern u1="f" u2="d" k="10" />
    <hkern u1="f" u2="c" k="10" />
    <hkern u1="f" u2="a" k="15" />
    <hkern u1="f" u2="]" k="-20" />
    <hkern u1="f" u2="\" k="-30" />
    <hkern u1="f" u2="&#x3f;" k="-35" />
    <hkern u1="f" u2="&#x2f;" k="45" />
    <hkern u1="f" u2="&#x2e;" k="45" />
    <hkern u1="f" u2="&#x2c;" k="45" />
    <hkern u1="f" u2="&#x2a;" k="-30" />
    <hkern u1="f" u2="&#x29;" k="-30" />
    <hkern u1="g" u2="\" k="50" />
    <hkern u1="h" u2="y" k="15" />
    <hkern u1="h" u2="w" k="15" />
    <hkern u1="h" u2="v" k="20" />
    <hkern u1="h" u2="\" k="75" />
    <hkern u1="h" u2="&#x3f;" k="30" />
    <hkern u1="h" u2="&#x2a;" k="15" />
    <hkern u1="k" u2="&#x203a;" k="10" />
    <hkern u1="k" u2="&#x2039;" k="20" />
    <hkern u1="k" u2="&#x2014;" k="20" />
    <hkern u1="k" u2="&#x2013;" k="20" />
    <hkern u1="k" u2="&#x153;" k="25" />
    <hkern u1="k" u2="&#xe6;" k="10" />
    <hkern u1="k" u2="&#xbb;" k="10" />
    <hkern u1="k" u2="&#xab;" k="20" />
    <hkern u1="k" u2="y" k="15" />
    <hkern u1="k" u2="w" k="20" />
    <hkern u1="k" u2="v" k="20" />
    <hkern u1="k" u2="u" k="10" />
    <hkern u1="k" u2="t" k="10" />
    <hkern u1="k" u2="q" k="25" />
    <hkern u1="k" u2="o" k="25" />
    <hkern u1="k" u2="g" k="25" />
    <hkern u1="k" u2="e" k="25" />
    <hkern u1="k" u2="d" k="25" />
    <hkern u1="k" u2="c" k="25" />
    <hkern u1="k" u2="a" k="10" />
    <hkern u1="k" u2="\" k="40" />
    <hkern u1="k" u2="&#x2d;" k="20" />
    <hkern u1="m" u2="y" k="15" />
    <hkern u1="m" u2="w" k="15" />
    <hkern u1="m" u2="v" k="20" />
    <hkern u1="m" u2="\" k="75" />
    <hkern u1="m" u2="&#x3f;" k="30" />
    <hkern u1="m" u2="&#x2a;" k="15" />
    <hkern u1="n" u2="y" k="15" />
    <hkern u1="n" u2="w" k="15" />
    <hkern u1="n" u2="v" k="20" />
    <hkern u1="n" u2="\" k="75" />
    <hkern u1="n" u2="&#x3f;" k="30" />
    <hkern u1="n" u2="&#x2a;" k="15" />
    <hkern u1="o" u2="&#x203a;" k="10" />
    <hkern u1="o" u2="&#x2026;" k="20" />
    <hkern u1="o" u2="&#x201c;" k="20" />
    <hkern u1="o" u2="&#x2018;" k="20" />
    <hkern u1="o" u2="&#xbb;" k="10" />
    <hkern u1="o" u2="&#x7d;" k="15" />
    <hkern u1="o" u2="z" k="20" />
    <hkern u1="o" u2="y" k="30" />
    <hkern u1="o" u2="x" k="35" />
    <hkern u1="o" u2="w" k="25" />
    <hkern u1="o" u2="v" k="30" />
    <hkern u1="o" u2="]" k="20" />
    <hkern u1="o" u2="\" k="80" />
    <hkern u1="o" u2="&#x3f;" k="50" />
    <hkern u1="o" u2="&#x2e;" k="20" />
    <hkern u1="o" u2="&#x2c;" k="20" />
    <hkern u1="o" u2="&#x2a;" k="20" />
    <hkern u1="o" u2="&#x29;" k="30" />
    <hkern u1="p" u2="&#x203a;" k="5" />
    <hkern u1="p" u2="&#x2026;" k="10" />
    <hkern u1="p" u2="&#x201c;" k="10" />
    <hkern u1="p" u2="&#x2018;" k="10" />
    <hkern u1="p" u2="&#xbb;" k="5" />
    <hkern u1="p" u2="&#x7d;" k="15" />
    <hkern u1="p" u2="z" k="15" />
    <hkern u1="p" u2="y" k="25" />
    <hkern u1="p" u2="x" k="30" />
    <hkern u1="p" u2="w" k="20" />
    <hkern u1="p" u2="v" k="25" />
    <hkern u1="p" u2="]" k="20" />
    <hkern u1="p" u2="\" k="70" />
    <hkern u1="p" u2="&#x3f;" k="35" />
    <hkern u1="p" u2="&#x2e;" k="10" />
    <hkern u1="p" u2="&#x2c;" k="10" />
    <hkern u1="p" u2="&#x2a;" k="15" />
    <hkern u1="p" u2="&#x29;" k="30" />
    <hkern u1="q" u2="\" k="50" />
    <hkern u1="r" u2="&#x2039;" k="10" />
    <hkern u1="r" u2="&#x2026;" k="90" />
    <hkern u1="r" u2="&#x201d;" k="-35" />
    <hkern u1="r" u2="&#x201c;" k="-20" />
    <hkern u1="r" u2="&#x2019;" k="-35" />
    <hkern u1="r" u2="&#x2018;" k="-20" />
    <hkern u1="r" u2="&#x153;" k="18" />
    <hkern u1="r" u2="&#xe6;" k="25" />
    <hkern u1="r" u2="&#xab;" k="10" />
    <hkern u1="r" u2="z" k="10" />
    <hkern u1="r" u2="q" k="15" />
    <hkern u1="r" u2="o" k="18" />
    <hkern u1="r" u2="g" k="15" />
    <hkern u1="r" u2="e" k="18" />
    <hkern u1="r" u2="d" k="15" />
    <hkern u1="r" u2="c" k="18" />
    <hkern u1="r" u2="a" k="25" />
    <hkern u1="r" u2="\" k="30" />
    <hkern u1="r" u2="&#x2f;" k="75" />
    <hkern u1="r" u2="&#x2e;" k="90" />
    <hkern u1="r" u2="&#x2c;" k="90" />
    <hkern u1="r" u2="&#x2a;" k="-20" />
    <hkern u1="s" u2="&#x2039;" k="10" />
    <hkern u1="s" u2="&#x201c;" k="10" />
    <hkern u1="s" u2="&#x2018;" k="10" />
    <hkern u1="s" u2="&#xab;" k="10" />
    <hkern u1="s" u2="&#x7d;" k="10" />
    <hkern u1="s" u2="z" k="10" />
    <hkern u1="s" u2="y" k="15" />
    <hkern u1="s" u2="x" k="25" />
    <hkern u1="s" u2="w" k="15" />
    <hkern u1="s" u2="v" k="20" />
    <hkern u1="s" u2="t" k="10" />
    <hkern u1="s" u2="s" k="10" />
    <hkern u1="s" u2="]" k="15" />
    <hkern u1="s" u2="\" k="75" />
    <hkern u1="s" u2="&#x3f;" k="35" />
    <hkern u1="s" u2="&#x29;" k="20" />
    <hkern u1="t" u2="&#x2039;" k="10" />
    <hkern u1="t" u2="&#x201d;" k="-10" />
    <hkern u1="t" u2="&#x2019;" k="-10" />
    <hkern u1="t" u2="&#x153;" k="15" />
    <hkern u1="t" u2="&#xab;" k="10" />
    <hkern u1="t" u2="q" k="15" />
    <hkern u1="t" u2="o" k="15" />
    <hkern u1="t" u2="g" k="15" />
    <hkern u1="t" u2="e" k="15" />
    <hkern u1="t" u2="d" k="15" />
    <hkern u1="t" u2="c" k="15" />
    <hkern u1="t" u2="\" k="40" />
    <hkern u1="u" u2="\" k="50" />
    <hkern u1="v" u2="&#x203a;" k="15" />
    <hkern u1="v" u2="&#x2039;" k="30" />
    <hkern u1="v" u2="&#x2026;" k="85" />
    <hkern u1="v" u2="&#x2014;" k="15" />
    <hkern u1="v" u2="&#x2013;" k="15" />
    <hkern u1="v" u2="&#x153;" k="30" />
    <hkern u1="v" u2="&#xe6;" k="25" />
    <hkern u1="v" u2="&#xbb;" k="15" />
    <hkern u1="v" u2="&#xab;" k="30" />
    <hkern u1="v" u2="&#x7d;" k="10" />
    <hkern u1="v" u2="z" k="5" />
    <hkern u1="v" u2="y" k="15" />
    <hkern u1="v" u2="x" k="10" />
    <hkern u1="v" u2="w" k="15" />
    <hkern u1="v" u2="v" k="15" />
    <hkern u1="v" u2="s" k="20" />
    <hkern u1="v" u2="q" k="25" />
    <hkern u1="v" u2="o" k="30" />
    <hkern u1="v" u2="g" k="25" />
    <hkern u1="v" u2="e" k="30" />
    <hkern u1="v" u2="d" k="25" />
    <hkern u1="v" u2="c" k="30" />
    <hkern u1="v" u2="a" k="25" />
    <hkern u1="v" u2="]" k="20" />
    <hkern u1="v" u2="\" k="50" />
    <hkern u1="v" u2="&#x3f;" k="10" />
    <hkern u1="v" u2="&#x2f;" k="70" />
    <hkern u1="v" u2="&#x2e;" k="85" />
    <hkern u1="v" u2="&#x2d;" k="15" />
    <hkern u1="v" u2="&#x2c;" k="85" />
    <hkern u1="w" u2="&#x203a;" k="10" />
    <hkern u1="w" u2="&#x2039;" k="20" />
    <hkern u1="w" u2="&#x2026;" k="70" />
    <hkern u1="w" u2="&#x2014;" k="10" />
    <hkern u1="w" u2="&#x2013;" k="10" />
    <hkern u1="w" u2="&#x153;" k="25" />
    <hkern u1="w" u2="&#xe6;" k="20" />
    <hkern u1="w" u2="&#xbb;" k="10" />
    <hkern u1="w" u2="&#xab;" k="20" />
    <hkern u1="w" u2="&#x7d;" k="10" />
    <hkern u1="w" u2="z" k="5" />
    <hkern u1="w" u2="y" k="10" />
    <hkern u1="w" u2="x" k="10" />
    <hkern u1="w" u2="w" k="10" />
    <hkern u1="w" u2="v" k="15" />
    <hkern u1="w" u2="s" k="15" />
    <hkern u1="w" u2="q" k="20" />
    <hkern u1="w" u2="o" k="25" />
    <hkern u1="w" u2="g" k="20" />
    <hkern u1="w" u2="e" k="25" />
    <hkern u1="w" u2="d" k="20" />
    <hkern u1="w" u2="c" k="25" />
    <hkern u1="w" u2="a" k="20" />
    <hkern u1="w" u2="]" k="20" />
    <hkern u1="w" u2="\" k="50" />
    <hkern u1="w" u2="&#x3f;" k="10" />
    <hkern u1="w" u2="&#x2f;" k="60" />
    <hkern u1="w" u2="&#x2e;" k="70" />
    <hkern u1="w" u2="&#x2d;" k="10" />
    <hkern u1="w" u2="&#x2c;" k="70" />
    <hkern u1="x" u2="&#x203a;" k="15" />
    <hkern u1="x" u2="&#x2039;" k="45" />
    <hkern u1="x" u2="&#x2014;" k="30" />
    <hkern u1="x" u2="&#x2013;" k="30" />
    <hkern u1="x" u2="&#x153;" k="35" />
    <hkern u1="x" u2="&#xe6;" k="15" />
    <hkern u1="x" u2="&#xbb;" k="15" />
    <hkern u1="x" u2="&#xab;" k="45" />
    <hkern u1="x" u2="&#x7d;" k="10" />
    <hkern u1="x" u2="y" k="10" />
    <hkern u1="x" u2="w" k="10" />
    <hkern u1="x" u2="v" k="10" />
    <hkern u1="x" u2="s" k="20" />
    <hkern u1="x" u2="q" k="30" />
    <hkern u1="x" u2="o" k="35" />
    <hkern u1="x" u2="g" k="30" />
    <hkern u1="x" u2="e" k="35" />
    <hkern u1="x" u2="d" k="30" />
    <hkern u1="x" u2="c" k="35" />
    <hkern u1="x" u2="a" k="15" />
    <hkern u1="x" u2="]" k="10" />
    <hkern u1="x" u2="\" k="50" />
    <hkern u1="x" u2="&#x3f;" k="15" />
    <hkern u1="x" u2="&#x2d;" k="30" />
    <hkern u1="y" u2="&#x203a;" k="15" />
    <hkern u1="y" u2="&#x2039;" k="30" />
    <hkern u1="y" u2="&#x2026;" k="85" />
    <hkern u1="y" u2="&#x2014;" k="15" />
    <hkern u1="y" u2="&#x2013;" k="15" />
    <hkern u1="y" u2="&#x153;" k="30" />
    <hkern u1="y" u2="&#xe6;" k="25" />
    <hkern u1="y" u2="&#xbb;" k="15" />
    <hkern u1="y" u2="&#xab;" k="30" />
    <hkern u1="y" u2="&#x7d;" k="10" />
    <hkern u1="y" u2="z" k="5" />
    <hkern u1="y" u2="y" k="10" />
    <hkern u1="y" u2="x" k="10" />
    <hkern u1="y" u2="w" k="10" />
    <hkern u1="y" u2="v" k="15" />
    <hkern u1="y" u2="s" k="20" />
    <hkern u1="y" u2="q" k="25" />
    <hkern u1="y" u2="o" k="30" />
    <hkern u1="y" u2="g" k="25" />
    <hkern u1="y" u2="e" k="30" />
    <hkern u1="y" u2="d" k="25" />
    <hkern u1="y" u2="c" k="30" />
    <hkern u1="y" u2="a" k="25" />
    <hkern u1="y" u2="]" k="20" />
    <hkern u1="y" u2="\" k="50" />
    <hkern u1="y" u2="&#x3f;" k="10" />
    <hkern u1="y" u2="&#x2f;" k="70" />
    <hkern u1="y" u2="&#x2e;" k="85" />
    <hkern u1="y" u2="&#x2d;" k="15" />
    <hkern u1="y" u2="&#x2c;" k="85" />
    <hkern u1="z" u2="&#x2039;" k="15" />
    <hkern u1="z" u2="&#x153;" k="15" />
    <hkern u1="z" u2="&#xab;" k="15" />
    <hkern u1="z" u2="q" k="15" />
    <hkern u1="z" u2="o" k="15" />
    <hkern u1="z" u2="g" k="15" />
    <hkern u1="z" u2="e" k="15" />
    <hkern u1="z" u2="d" k="15" />
    <hkern u1="z" u2="c" k="15" />
    <hkern u1="z" u2="\" k="45" />
    <hkern u1="&#x7b;" u2="&#x153;" k="15" />
    <hkern u1="&#x7b;" u2="&#x152;" k="20" />
    <hkern u1="&#x7b;" u2="&#xb5;" k="20" />
    <hkern u1="&#x7b;" u2="z" k="10" />
    <hkern u1="&#x7b;" u2="y" k="10" />
    <hkern u1="&#x7b;" u2="x" k="10" />
    <hkern u1="&#x7b;" u2="w" k="10" />
    <hkern u1="&#x7b;" u2="v" k="10" />
    <hkern u1="&#x7b;" u2="s" k="10" />
    <hkern u1="&#x7b;" u2="q" k="15" />
    <hkern u1="&#x7b;" u2="o" k="15" />
    <hkern u1="&#x7b;" u2="j" k="-35" />
    <hkern u1="&#x7b;" u2="g" k="10" />
    <hkern u1="&#x7b;" u2="e" k="15" />
    <hkern u1="&#x7b;" u2="d" k="15" />
    <hkern u1="&#x7b;" u2="c" k="15" />
    <hkern u1="&#x7b;" u2="Q" k="20" />
    <hkern u1="&#x7b;" u2="O" k="20" />
    <hkern u1="&#x7b;" u2="J" k="10" />
    <hkern u1="&#x7b;" u2="G" k="20" />
    <hkern u1="&#x7b;" u2="C" k="20" />
    <hkern u1="&#xa3;" u2="&#x34;" k="15" />
    <hkern u1="&#xa4;" u2="&#x31;" k="-20" />
    <hkern u1="&#xa5;" u2="&#x34;" k="20" />
    <hkern u1="&#xa7;" u2="&#x37;" k="15" />
    <hkern u1="&#xab;" u2="&#x153;" k="10" />
    <hkern u1="&#xab;" u2="y" k="15" />
    <hkern u1="&#xab;" u2="x" k="15" />
    <hkern u1="&#xab;" u2="w" k="10" />
    <hkern u1="&#xab;" u2="v" k="15" />
    <hkern u1="&#xab;" u2="q" k="5" />
    <hkern u1="&#xab;" u2="o" k="10" />
    <hkern u1="&#xab;" u2="g" k="5" />
    <hkern u1="&#xab;" u2="e" k="10" />
    <hkern u1="&#xab;" u2="d" k="5" />
    <hkern u1="&#xab;" u2="c" k="10" />
    <hkern u1="&#xab;" u2="Y" k="75" />
    <hkern u1="&#xab;" u2="X" k="20" />
    <hkern u1="&#xab;" u2="W" k="35" />
    <hkern u1="&#xab;" u2="V" k="40" />
    <hkern u1="&#xab;" u2="T" k="70" />
    <hkern u1="&#xb1;" u2="&#x2215;" k="-30" />
    <hkern u1="&#xb1;" u2="&#x37;" k="11" />
    <hkern u1="&#xb5;" u2="&#x2026;" k="40" />
    <hkern u1="&#xb5;" u2="&#xc6;" k="43" />
    <hkern u1="&#xb5;" u2="&#x7d;" k="20" />
    <hkern u1="&#xb5;" u2="x" k="5" />
    <hkern u1="&#xb5;" u2="]" k="20" />
    <hkern u1="&#xb5;" u2="\" k="40" />
    <hkern u1="&#xb5;" u2="Z" k="37" />
    <hkern u1="&#xb5;" u2="Y" k="60" />
    <hkern u1="&#xb5;" u2="X" k="50" />
    <hkern u1="&#xb5;" u2="W" k="38" />
    <hkern u1="&#xb5;" u2="V" k="43" />
    <hkern u1="&#xb5;" u2="T" k="38" />
    <hkern u1="&#xb5;" u2="S" k="5" />
    <hkern u1="&#xb5;" u2="J" k="30" />
    <hkern u1="&#xb5;" u2="A" k="43" />
    <hkern u1="&#xb5;" u2="&#x3f;" k="20" />
    <hkern u1="&#xb5;" u2="&#x2f;" k="40" />
    <hkern u1="&#xb5;" u2="&#x2e;" k="40" />
    <hkern u1="&#xb5;" u2="&#x2c;" k="40" />
    <hkern u1="&#xb5;" u2="&#x29;" k="30" />
    <hkern u1="&#xbb;" u2="&#xfb02;" k="10" />
    <hkern u1="&#xbb;" u2="&#xfb01;" k="10" />
    <hkern u1="&#xbb;" u2="&#x2248;" k="10" />
    <hkern u1="&#xbb;" u2="&#x221a;" k="10" />
    <hkern u1="&#xbb;" u2="&#x3a9;" k="10" />
    <hkern u1="&#xbb;" u2="&#xc6;" k="20" />
    <hkern u1="&#xbb;" u2="z" k="20" />
    <hkern u1="&#xbb;" u2="y" k="30" />
    <hkern u1="&#xbb;" u2="x" k="45" />
    <hkern u1="&#xbb;" u2="w" k="20" />
    <hkern u1="&#xbb;" u2="v" k="30" />
    <hkern u1="&#xbb;" u2="t" k="10" />
    <hkern u1="&#xbb;" u2="s" k="10" />
    <hkern u1="&#xbb;" u2="f" k="10" />
    <hkern u1="&#xbb;" u2="Z" k="15" />
    <hkern u1="&#xbb;" u2="Y" k="100" />
    <hkern u1="&#xbb;" u2="X" k="50" />
    <hkern u1="&#xbb;" u2="W" k="50" />
    <hkern u1="&#xbb;" u2="V" k="60" />
    <hkern u1="&#xbb;" u2="T" k="90" />
    <hkern u1="&#xbb;" u2="S" k="10" />
    <hkern u1="&#xbb;" u2="A" k="20" />
    <hkern u1="&#xbf;" u2="&#xfb02;" k="10" />
    <hkern u1="&#xbf;" u2="&#xfb01;" k="10" />
    <hkern u1="&#xbf;" u2="&#x2248;" k="10" />
    <hkern u1="&#xbf;" u2="&#x221a;" k="10" />
    <hkern u1="&#xbf;" u2="&#x3a9;" k="10" />
    <hkern u1="&#xbf;" u2="&#x152;" k="20" />
    <hkern u1="&#xbf;" u2="&#xe6;" k="-10" />
    <hkern u1="&#xbf;" u2="&#xb5;" k="20" />
    <hkern u1="&#xbf;" u2="y" k="35" />
    <hkern u1="&#xbf;" u2="w" k="35" />
    <hkern u1="&#xbf;" u2="v" k="45" />
    <hkern u1="&#xbf;" u2="t" k="15" />
    <hkern u1="&#xbf;" u2="f" k="10" />
    <hkern u1="&#xbf;" u2="a" k="-10" />
    <hkern u1="&#xbf;" u2="Y" k="70" />
    <hkern u1="&#xbf;" u2="X" k="10" />
    <hkern u1="&#xbf;" u2="W" k="50" />
    <hkern u1="&#xbf;" u2="V" k="60" />
    <hkern u1="&#xbf;" u2="U" k="15" />
    <hkern u1="&#xbf;" u2="T" k="60" />
    <hkern u1="&#xbf;" u2="Q" k="20" />
    <hkern u1="&#xbf;" u2="O" k="20" />
    <hkern u1="&#xbf;" u2="G" k="20" />
    <hkern u1="&#xbf;" u2="C" k="20" />
    <hkern u1="&#xc6;" u2="&#x153;" k="10" />
    <hkern u1="&#xc6;" u2="y" k="10" />
    <hkern u1="&#xc6;" u2="w" k="10" />
    <hkern u1="&#xc6;" u2="v" k="10" />
    <hkern u1="&#xc6;" u2="o" k="10" />
    <hkern u1="&#xc6;" u2="e" k="10" />
    <hkern u1="&#xc6;" u2="d" k="10" />
    <hkern u1="&#xc6;" u2="c" k="10" />
    <hkern u1="&#xdf;" u2="y" k="10" />
    <hkern u1="&#xdf;" u2="x" k="10" />
    <hkern u1="&#xdf;" u2="w" k="5" />
    <hkern u1="&#xdf;" u2="v" k="10" />
    <hkern u1="&#xe6;" u2="&#x2026;" k="10" />
    <hkern u1="&#xe6;" u2="&#x7d;" k="10" />
    <hkern u1="&#xe6;" u2="z" k="15" />
    <hkern u1="&#xe6;" u2="y" k="25" />
    <hkern u1="&#xe6;" u2="x" k="30" />
    <hkern u1="&#xe6;" u2="w" k="25" />
    <hkern u1="&#xe6;" u2="v" k="25" />
    <hkern u1="&#xe6;" u2="]" k="20" />
    <hkern u1="&#xe6;" u2="\" k="80" />
    <hkern u1="&#xe6;" u2="&#x3f;" k="40" />
    <hkern u1="&#xe6;" u2="&#x2e;" k="10" />
    <hkern u1="&#xe6;" u2="&#x2c;" k="10" />
    <hkern u1="&#xe6;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe6;" u2="&#x29;" k="30" />
    <hkern u1="&#x152;" u2="&#x153;" k="10" />
    <hkern u1="&#x152;" u2="y" k="10" />
    <hkern u1="&#x152;" u2="w" k="10" />
    <hkern u1="&#x152;" u2="v" k="10" />
    <hkern u1="&#x152;" u2="o" k="10" />
    <hkern u1="&#x152;" u2="e" k="10" />
    <hkern u1="&#x152;" u2="d" k="10" />
    <hkern u1="&#x152;" u2="c" k="10" />
    <hkern u1="&#x153;" u2="&#x2026;" k="10" />
    <hkern u1="&#x153;" u2="&#x7d;" k="10" />
    <hkern u1="&#x153;" u2="z" k="15" />
    <hkern u1="&#x153;" u2="y" k="25" />
    <hkern u1="&#x153;" u2="x" k="30" />
    <hkern u1="&#x153;" u2="w" k="25" />
    <hkern u1="&#x153;" u2="v" k="25" />
    <hkern u1="&#x153;" u2="]" k="20" />
    <hkern u1="&#x153;" u2="\" k="80" />
    <hkern u1="&#x153;" u2="&#x3f;" k="40" />
    <hkern u1="&#x153;" u2="&#x2e;" k="10" />
    <hkern u1="&#x153;" u2="&#x2c;" k="10" />
    <hkern u1="&#x153;" u2="&#x2a;" k="20" />
    <hkern u1="&#x153;" u2="&#x29;" k="30" />
    <hkern u1="&#x192;" u2="&#x35;" k="10" />
    <hkern u1="&#x192;" u2="&#x34;" k="30" />
    <hkern u1="&#x192;" u2="&#x31;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x2122;" k="-45" />
    <hkern u1="&#x3a9;" u2="&#x2039;" k="15" />
    <hkern u1="&#x3a9;" u2="&#x2026;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x201d;" k="-35" />
    <hkern u1="&#x3a9;" u2="&#x201c;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x2019;" k="-35" />
    <hkern u1="&#x3a9;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x153;" k="10" />
    <hkern u1="&#x3a9;" u2="&#xe6;" k="15" />
    <hkern u1="&#x3a9;" u2="&#xab;" k="15" />
    <hkern u1="&#x3a9;" u2="&#x7d;" k="-30" />
    <hkern u1="&#x3a9;" u2="z" k="10" />
    <hkern u1="&#x3a9;" u2="q" k="10" />
    <hkern u1="&#x3a9;" u2="o" k="10" />
    <hkern u1="&#x3a9;" u2="g" k="10" />
    <hkern u1="&#x3a9;" u2="e" k="10" />
    <hkern u1="&#x3a9;" u2="d" k="10" />
    <hkern u1="&#x3a9;" u2="c" k="10" />
    <hkern u1="&#x3a9;" u2="a" k="15" />
    <hkern u1="&#x3a9;" u2="]" k="-20" />
    <hkern u1="&#x3a9;" u2="\" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x3f;" k="-35" />
    <hkern u1="&#x3a9;" u2="&#x2f;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x2e;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x2c;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x2a;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x29;" k="-30" />
    <hkern u1="&#x2013;" u2="&#xc6;" k="40" />
    <hkern u1="&#x2013;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2013;" u2="z" k="10" />
    <hkern u1="&#x2013;" u2="y" k="15" />
    <hkern u1="&#x2013;" u2="x" k="30" />
    <hkern u1="&#x2013;" u2="w" k="10" />
    <hkern u1="&#x2013;" u2="v" k="15" />
    <hkern u1="&#x2013;" u2="Z" k="30" />
    <hkern u1="&#x2013;" u2="Y" k="80" />
    <hkern u1="&#x2013;" u2="X" k="50" />
    <hkern u1="&#x2013;" u2="W" k="35" />
    <hkern u1="&#x2013;" u2="V" k="40" />
    <hkern u1="&#x2013;" u2="T" k="90" />
    <hkern u1="&#x2013;" u2="A" k="40" />
    <hkern u1="&#x2013;" u2="&#x37;" k="40" />
    <hkern u1="&#x2013;" u2="&#x33;" k="10" />
    <hkern u1="&#x2013;" u2="&#x31;" k="30" />
    <hkern u1="&#x2014;" u2="&#xc6;" k="40" />
    <hkern u1="&#x2014;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2014;" u2="z" k="10" />
    <hkern u1="&#x2014;" u2="y" k="15" />
    <hkern u1="&#x2014;" u2="x" k="30" />
    <hkern u1="&#x2014;" u2="w" k="10" />
    <hkern u1="&#x2014;" u2="v" k="15" />
    <hkern u1="&#x2014;" u2="Z" k="30" />
    <hkern u1="&#x2014;" u2="Y" k="80" />
    <hkern u1="&#x2014;" u2="X" k="50" />
    <hkern u1="&#x2014;" u2="W" k="35" />
    <hkern u1="&#x2014;" u2="V" k="40" />
    <hkern u1="&#x2014;" u2="T" k="90" />
    <hkern u1="&#x2014;" u2="A" k="40" />
    <hkern u1="&#x2014;" u2="&#x37;" k="40" />
    <hkern u1="&#x2014;" u2="&#x33;" k="10" />
    <hkern u1="&#x2014;" u2="&#x31;" k="30" />
    <hkern u1="&#x2018;" u2="&#x153;" k="15" />
    <hkern u1="&#x2018;" u2="&#xe6;" k="10" />
    <hkern u1="&#x2018;" u2="&#xc6;" k="90" />
    <hkern u1="&#x2018;" u2="&#xbf;" k="35" />
    <hkern u1="&#x2018;" u2="z" k="10" />
    <hkern u1="&#x2018;" u2="t" k="-15" />
    <hkern u1="&#x2018;" u2="s" k="10" />
    <hkern u1="&#x2018;" u2="q" k="15" />
    <hkern u1="&#x2018;" u2="o" k="15" />
    <hkern u1="&#x2018;" u2="g" k="15" />
    <hkern u1="&#x2018;" u2="e" k="15" />
    <hkern u1="&#x2018;" u2="d" k="15" />
    <hkern u1="&#x2018;" u2="c" k="15" />
    <hkern u1="&#x2018;" u2="a" k="10" />
    <hkern u1="&#x2018;" u2="J" k="80" />
    <hkern u1="&#x2018;" u2="A" k="90" />
    <hkern u1="&#x2019;" u2="&#x153;" k="38" />
    <hkern u1="&#x2019;" u2="&#xe6;" k="20" />
    <hkern u1="&#x2019;" u2="&#xc6;" k="100" />
    <hkern u1="&#x2019;" u2="s" k="20" />
    <hkern u1="&#x2019;" u2="q" k="33" />
    <hkern u1="&#x2019;" u2="o" k="38" />
    <hkern u1="&#x2019;" u2="g" k="33" />
    <hkern u1="&#x2019;" u2="e" k="38" />
    <hkern u1="&#x2019;" u2="d" k="33" />
    <hkern u1="&#x2019;" u2="c" k="38" />
    <hkern u1="&#x2019;" u2="a" k="20" />
    <hkern u1="&#x2019;" u2="J" k="100" />
    <hkern u1="&#x2019;" u2="A" k="100" />
    <hkern u1="&#x201a;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x201a;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x201a;" u2="&#x2248;" k="15" />
    <hkern u1="&#x201a;" u2="&#x221a;" k="15" />
    <hkern u1="&#x201a;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x201a;" u2="&#x153;" k="20" />
    <hkern u1="&#x201a;" u2="&#x152;" k="40" />
    <hkern u1="&#x201a;" u2="&#xb5;" k="40" />
    <hkern u1="&#x201a;" u2="y" k="45" />
    <hkern u1="&#x201a;" u2="w" k="70" />
    <hkern u1="&#x201a;" u2="v" k="85" />
    <hkern u1="&#x201a;" u2="t" k="25" />
    <hkern u1="&#x201a;" u2="q" k="10" />
    <hkern u1="&#x201a;" u2="o" k="20" />
    <hkern u1="&#x201a;" u2="j" k="-15" />
    <hkern u1="&#x201a;" u2="g" k="10" />
    <hkern u1="&#x201a;" u2="f" k="15" />
    <hkern u1="&#x201a;" u2="e" k="20" />
    <hkern u1="&#x201a;" u2="d" k="10" />
    <hkern u1="&#x201a;" u2="c" k="20" />
    <hkern u1="&#x201a;" u2="Y" k="130" />
    <hkern u1="&#x201a;" u2="W" k="100" />
    <hkern u1="&#x201a;" u2="V" k="120" />
    <hkern u1="&#x201a;" u2="U" k="15" />
    <hkern u1="&#x201a;" u2="T" k="100" />
    <hkern u1="&#x201a;" u2="Q" k="40" />
    <hkern u1="&#x201a;" u2="O" k="40" />
    <hkern u1="&#x201a;" u2="G" k="40" />
    <hkern u1="&#x201a;" u2="C" k="40" />
    <hkern u1="&#x201c;" u2="&#x153;" k="15" />
    <hkern u1="&#x201c;" u2="&#xe6;" k="10" />
    <hkern u1="&#x201c;" u2="&#xc6;" k="90" />
    <hkern u1="&#x201c;" u2="&#xbf;" k="35" />
    <hkern u1="&#x201c;" u2="z" k="10" />
    <hkern u1="&#x201c;" u2="t" k="-15" />
    <hkern u1="&#x201c;" u2="s" k="10" />
    <hkern u1="&#x201c;" u2="q" k="15" />
    <hkern u1="&#x201c;" u2="o" k="15" />
    <hkern u1="&#x201c;" u2="g" k="15" />
    <hkern u1="&#x201c;" u2="e" k="15" />
    <hkern u1="&#x201c;" u2="d" k="15" />
    <hkern u1="&#x201c;" u2="c" k="15" />
    <hkern u1="&#x201c;" u2="a" k="10" />
    <hkern u1="&#x201c;" u2="J" k="80" />
    <hkern u1="&#x201c;" u2="A" k="90" />
    <hkern u1="&#x201e;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x201e;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x201e;" u2="&#x2248;" k="15" />
    <hkern u1="&#x201e;" u2="&#x221a;" k="15" />
    <hkern u1="&#x201e;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x201e;" u2="&#x153;" k="20" />
    <hkern u1="&#x201e;" u2="&#x152;" k="40" />
    <hkern u1="&#x201e;" u2="&#xb5;" k="40" />
    <hkern u1="&#x201e;" u2="y" k="45" />
    <hkern u1="&#x201e;" u2="w" k="70" />
    <hkern u1="&#x201e;" u2="v" k="85" />
    <hkern u1="&#x201e;" u2="t" k="25" />
    <hkern u1="&#x201e;" u2="q" k="10" />
    <hkern u1="&#x201e;" u2="o" k="20" />
    <hkern u1="&#x201e;" u2="j" k="-15" />
    <hkern u1="&#x201e;" u2="g" k="10" />
    <hkern u1="&#x201e;" u2="f" k="15" />
    <hkern u1="&#x201e;" u2="e" k="20" />
    <hkern u1="&#x201e;" u2="d" k="10" />
    <hkern u1="&#x201e;" u2="c" k="20" />
    <hkern u1="&#x201e;" u2="Y" k="130" />
    <hkern u1="&#x201e;" u2="W" k="100" />
    <hkern u1="&#x201e;" u2="V" k="120" />
    <hkern u1="&#x201e;" u2="U" k="15" />
    <hkern u1="&#x201e;" u2="T" k="100" />
    <hkern u1="&#x201e;" u2="Q" k="40" />
    <hkern u1="&#x201e;" u2="O" k="40" />
    <hkern u1="&#x201e;" u2="G" k="40" />
    <hkern u1="&#x201e;" u2="C" k="40" />
    <hkern u1="&#x2026;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x2026;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x2026;" u2="&#x2248;" k="15" />
    <hkern u1="&#x2026;" u2="&#x221a;" k="15" />
    <hkern u1="&#x2026;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x2026;" u2="&#x153;" k="20" />
    <hkern u1="&#x2026;" u2="&#x152;" k="40" />
    <hkern u1="&#x2026;" u2="&#xb5;" k="40" />
    <hkern u1="&#x2026;" u2="y" k="60" />
    <hkern u1="&#x2026;" u2="w" k="70" />
    <hkern u1="&#x2026;" u2="v" k="85" />
    <hkern u1="&#x2026;" u2="t" k="25" />
    <hkern u1="&#x2026;" u2="q" k="10" />
    <hkern u1="&#x2026;" u2="o" k="20" />
    <hkern u1="&#x2026;" u2="g" k="10" />
    <hkern u1="&#x2026;" u2="f" k="15" />
    <hkern u1="&#x2026;" u2="e" k="20" />
    <hkern u1="&#x2026;" u2="d" k="10" />
    <hkern u1="&#x2026;" u2="c" k="20" />
    <hkern u1="&#x2026;" u2="Y" k="130" />
    <hkern u1="&#x2026;" u2="W" k="100" />
    <hkern u1="&#x2026;" u2="V" k="120" />
    <hkern u1="&#x2026;" u2="U" k="15" />
    <hkern u1="&#x2026;" u2="T" k="100" />
    <hkern u1="&#x2026;" u2="Q" k="40" />
    <hkern u1="&#x2026;" u2="O" k="40" />
    <hkern u1="&#x2026;" u2="G" k="40" />
    <hkern u1="&#x2026;" u2="C" k="40" />
    <hkern u1="&#x2039;" u2="&#x153;" k="10" />
    <hkern u1="&#x2039;" u2="y" k="15" />
    <hkern u1="&#x2039;" u2="x" k="15" />
    <hkern u1="&#x2039;" u2="w" k="10" />
    <hkern u1="&#x2039;" u2="v" k="15" />
    <hkern u1="&#x2039;" u2="q" k="5" />
    <hkern u1="&#x2039;" u2="o" k="10" />
    <hkern u1="&#x2039;" u2="g" k="5" />
    <hkern u1="&#x2039;" u2="e" k="10" />
    <hkern u1="&#x2039;" u2="d" k="5" />
    <hkern u1="&#x2039;" u2="c" k="10" />
    <hkern u1="&#x2039;" u2="Y" k="75" />
    <hkern u1="&#x2039;" u2="X" k="20" />
    <hkern u1="&#x2039;" u2="W" k="35" />
    <hkern u1="&#x2039;" u2="V" k="40" />
    <hkern u1="&#x2039;" u2="T" k="70" />
    <hkern u1="&#x203a;" u2="&#xfb02;" k="10" />
    <hkern u1="&#x203a;" u2="&#xfb01;" k="10" />
    <hkern u1="&#x203a;" u2="&#x2248;" k="10" />
    <hkern u1="&#x203a;" u2="&#x221a;" k="10" />
    <hkern u1="&#x203a;" u2="&#x3a9;" k="10" />
    <hkern u1="&#x203a;" u2="z" k="20" />
    <hkern u1="&#x203a;" u2="y" k="30" />
    <hkern u1="&#x203a;" u2="x" k="45" />
    <hkern u1="&#x203a;" u2="w" k="20" />
    <hkern u1="&#x203a;" u2="v" k="30" />
    <hkern u1="&#x203a;" u2="t" k="10" />
    <hkern u1="&#x203a;" u2="s" k="10" />
    <hkern u1="&#x203a;" u2="f" k="10" />
    <hkern u1="&#x2215;" u2="&#xb1;" k="-15" />
    <hkern u1="&#x2215;" u2="&#x39;" k="-20" />
    <hkern u1="&#x2215;" u2="&#x38;" k="-20" />
    <hkern u1="&#x2215;" u2="&#x37;" k="-30" />
    <hkern u1="&#x2215;" u2="&#x35;" k="-20" />
    <hkern u1="&#x2215;" u2="&#x34;" k="75" />
    <hkern u1="&#x2215;" u2="&#x33;" k="-40" />
    <hkern u1="&#x2215;" u2="&#x32;" k="-10" />
    <hkern u1="&#x2215;" u2="&#x31;" k="-60" />
  </font>
</defs></svg>
