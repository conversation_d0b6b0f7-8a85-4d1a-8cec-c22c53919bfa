<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Sun Jan 22 11:00:51 2006
 By Aleksey,,,
HTF Gotham\252 Copr. 2002 The Hoefler Type Foundry, Inc. Info: www.typography.com
</metadata>
<defs>
<font id="Gotham-MediumItalic" horiz-adv-x="500" >
  <font-face 
    font-family="Gotham"
    font-weight="500"
    font-style="italic"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 6 4 3 0 0 9 0 4"
    ascent="800"
    descent="-200"
    x-height="528"
    cap-height="700"
    bbox="-131 -200 1221 922"
    underline-thickness="20"
    underline-position="-163"
    slope="-12"
    unicode-range="U+0020-FB02"
  />
<missing-glyph 
 />
    <glyph glyph-name=".notdef" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="300" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="300" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="303" 
d="M106 221h67l156 444l10 35h-142l-10 -35zM14 0h133l37 137h-134z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="493" 
d="M323 401h64l159 294l2 5h-138zM100 401h64l159 294l1 5h-138z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="687" 
d="M55 0h107l56 161h164l-56 -161h106l56 161h115l27 104h-105l60 174h103l27 104h-93l54 157h-106l-55 -157h-164l55 157h-107l-55 -157h-113l-28 -104h105l-60 -174h-102l-28 -104h93zM255 265l60 174h164l-61 -174h-163z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="631" 
d="M170 -98h87l29 98h6q58 0 108.5 15.5t87 43.5t57.5 67.5t21 86.5q0 36 -11.5 64t-32.5 49.5t-51.5 38.5t-69.5 31l53 186q32 -9 59 -26t52 -40l83 82q-31 32 -70 55t-89 35l20 70h-87l-18 -59q-5 1 -10 1h-9q-56 0 -103.5 -16t-83 -44t-55.5 -66.5t-20 -83.5
q0 -38 12 -65.5t33.5 -48.5t53 -37t70.5 -31l-56 -195q-45 10 -82.5 32.5t-67.5 57.5l-90 -76q37 -47 89 -76.5t116 -42.5zM328 424q-44 18 -61.5 35.5t-17.5 43.5q0 19 8.5 35.5t25 29t40 19.5t53.5 6zM314 106l51 175q42 -18 58 -36t16 -44q0 -43 -34 -68t-91 -27z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="862" 
d="M236 346q42 0 77.5 18t61 48t40 68t14.5 77q0 33 -11 60.5t-30.5 48t-47 31.5t-60.5 11q-42 0 -77.5 -18t-61 -48t-40 -68t-14.5 -77q0 -33 10.5 -60.5t30 -48t47 -31.5t61.5 -11zM62 0h108l676 700h-109zM628 -8q42 0 77.5 18t61 48t40 68t14.5 78q0 33 -11 60.5
t-30.5 47.5t-47 31t-60.5 11q-42 0 -77.5 -18t-61 -47.5t-40 -67.5t-14.5 -78q0 -33 10.5 -60.5t30 -47.5t47 -31.5t61.5 -11.5zM241 420q-33 0 -52 21.5t-19 59.5q0 23 7.5 47t21.5 43t33 31t42 12q34 0 52.5 -21.5t18.5 -59.5q0 -23 -7.5 -47t-21 -43t-33 -31t-42.5 -12z
M633 66q-33 0 -52 22t-19 59q0 23 7.5 47t21.5 43.5t33.5 31.5t42.5 12q33 0 52 -22t19 -60q0 -23 -7.5 -46.5t-21.5 -43t-33.5 -31.5t-42.5 -12z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="694" 
d="M532 -14l91 58l-75 104q57 63 114 152l-77 52q-23 -37 -46 -69.5t-48 -59.5l-98 135q58 18 96.5 41t61.5 48t32.5 51.5t9.5 52.5q0 35 -13.5 64.5t-37.5 51t-58 33.5t-75 12q-47 0 -86 -15t-67 -41t-43 -60.5t-15 -72.5q0 -30 10.5 -61.5t29.5 -63.5q-111 -34 -162 -94
t-51 -130q0 -44 16.5 -78t45 -57.5t68 -36t86.5 -12.5q65 0 119.5 22t106.5 64zM341 428q-19 29 -26.5 51.5t-7.5 42.5q0 19 7 35.5t20 29.5t30.5 20.5t38.5 7.5q38 0 58.5 -21.5t20.5 -49.5q0 -17 -6.5 -32t-22.5 -29t-43 -28t-69 -27zM254 92q-54 0 -82.5 25t-28.5 65
q0 42 34 80.5t111 62.5l123 -171q-79 -62 -157 -62z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="269" 
d="M100 401h64l159 294l1 5h-138z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="440" 
d="M251 -142l74 76q-55 58 -93 127t-38 155q0 58 19 115.5t58 110.5t98.5 100t140.5 83l-44 90q-97 -39 -171 -93t-124 -118.5t-75.5 -138.5t-25.5 -153q0 -110 49.5 -198t131.5 -156z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="440" 
d="M-13 -139q97 39 171 92.5t124 118.5t75.5 139t25.5 153q0 110 -49.5 198t-131.5 156l-75 -77q28 -29 52 -60t42 -66t28 -73.5t10 -81.5q0 -58 -19 -115.5t-58 -110.5t-98.5 -100t-140.5 -83z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="430" 
d="M276 379l18 121l77 -97l51 51l-98 77l124 19l-19 68l-116 -45l45 115l-67 17l-19 -121l-77 97l-50 -50l97 -78l-124 -18l19 -69l116 45l-45 -115z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="646" 
d="M224 106h114l51 191h192l30 110h-192l51 191h-115l-50 -191h-192l-30 -110h192z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="293" 
d="M-45 -145q39 3 69.5 13.5t54.5 30t41 49.5t28 73l31 116h-133l-37 -137h52q-8 -38 -34.5 -62t-80.5 -32z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="411" 
d="M53 247h296l32 119h-296z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="293" 
d="M9 0h133l37 137h-133z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="521" 
d="M-131 -128h116l692 926h-116z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="724" 
d="M330 -12q85 0 153.5 39.5t116.5 102.5t74 142t26 158q0 63 -18.5 115t-52 89t-82 57.5t-108.5 20.5q-86 0 -154.5 -39.5t-116.5 -102.5t-74 -142t-26 -158q0 -63 18.5 -115t52.5 -89t82.5 -57.5t108.5 -20.5zM339 99q-69 0 -106.5 46t-37.5 125q0 57 17.5 116t48.5 106.5
t74 78t94 30.5q69 0 106.5 -46t37.5 -125q0 -57 -17.5 -116t-48.5 -106.5t-74 -78t-94 -30.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="404" 
d="M94 0h122l189 705h-85l-207 -60l2 -106l133 34z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="606" 
d="M-27 0h514l29 108h-327l260 188q43 31 73 59t49 55t28 55t9 60q0 41 -16.5 75t-46.5 58.5t-72 38t-93 13.5q-81 0 -145 -34.5t-114 -88.5l77 -79q40 40 83 65.5t94 25.5q50 0 78 -23t28 -63q0 -38 -27 -72t-81 -73l-373 -268z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="605" 
d="M244 -12q60 0 112.5 18.5t91.5 51t61.5 77t22.5 96.5q0 39 -12.5 68.5t-35 51t-52.5 34.5t-66 19l250 204l25 92h-466l-29 -107h298l-238 -200l8 -75h38q72 0 113.5 -24.5t41.5 -73.5q0 -25 -12 -47t-33 -38.5t-49.5 -26t-62.5 -9.5q-61 0 -99.5 23.5t-66.5 65.5l-93 -66
q33 -60 95 -97t158 -37z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="655" 
d="M331 0h117l42 157h100l27 101h-100l120 447h-119l-498 -457l13 -91h340zM177 258l298 279l-75 -279h-223z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="607" 
d="M251 -11q69 0 125 21t96 56.5t61.5 82.5t21.5 98q0 45 -16 82t-45 63t-69.5 40t-89.5 14q-22 0 -46.5 -4.5t-43.5 -11.5l53 161h299l31 109h-411l-109 -334l64 -52q26 13 57 21t57 8q69 0 105.5 -30t36.5 -78q0 -27 -12 -51.5t-34 -43.5t-53 -30t-68 -11q-55 0 -97 22
t-77 64l-88 -71q18 -26 44.5 -49t59 -40t70.5 -26.5t78 -9.5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="642" 
d="M314 -12q60 0 111.5 22t90 58.5t60 84t21.5 98.5q0 45 -16.5 81.5t-45 62.5t-68.5 40t-86 14q-58 0 -105.5 -23.5t-72.5 -53.5q9 48 33 92t54 74q62 63 149 63q48 0 84 -17t65 -44l73 87q-38 38 -91 61.5t-121 23.5q-74 0 -135.5 -27.5t-109.5 -75.5q-66 -66 -102 -161
t-36 -200q0 -67 18 -114t53 -82q31 -31 77 -47.5t100 -16.5zM315 93q-60 0 -97 35q-32 32 -32 80q0 27 11.5 52.5t32.5 45t51 31t66 11.5q60 0 92.5 -32t32.5 -79q0 -28 -11.5 -54t-32 -46t-49.5 -32t-64 -12z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="600" 
d="M30 0h145l450 607l26 93h-492l-29 -108h346z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="656" 
d="M298 -10q66 0 119 17t90.5 45.5t57.5 65.5t20 78q0 57 -32.5 95.5t-79.5 56.5q72 16 116.5 62t44.5 115q0 40 -18 74t-50 59t-76.5 38.5t-98.5 13.5q-62 0 -110 -17t-81 -44t-50.5 -62t-17.5 -71q0 -46 25 -83t67 -56q-40 -9 -75 -25t-60.5 -40t-40 -56t-14.5 -73
q0 -42 20.5 -77.5t56.5 -61t84 -40t103 -14.5zM377 405q-59 0 -94.5 28t-34.5 70q0 22 10.5 41.5t29 33.5t43.5 22t55 8q63 0 97 -28t34 -69q0 -21 -10 -40.5t-28.5 -34t-44 -23t-57.5 -8.5zM305 91q-68 0 -108.5 29t-40.5 74q1 47 43.5 79.5t114.5 32.5q69 0 109.5 -30
t40.5 -74q0 -25 -12 -45.5t-33 -35t-50.5 -22.5t-63.5 -8z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="642" 
d="M243 -12q70 0 130 26.5t109 75.5q66 66 103 158t37 194q0 75 -18 123.5t-53 82.5q-31 31 -78.5 47.5t-98.5 16.5q-60 0 -111.5 -21t-90 -57.5t-60.5 -85.5t-22 -104q0 -47 16.5 -84t45 -63t68 -40t86.5 -14q30 0 57 7t49.5 18t41 25t30.5 28q-10 -45 -32 -86t-53 -72
q-65 -65 -150 -65q-48 0 -84.5 18.5t-71.5 50.5l-74 -87q45 -44 101.5 -68t122.5 -24zM343 345q-61 0 -94.5 32t-33.5 81q0 30 11.5 57t32.5 47.5t50 32.5t64 12q61 0 97 -36q15 -15 23.5 -36t8.5 -45q0 -29 -11.5 -55t-32.5 -46t-50.5 -32t-64.5 -12z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="296" 
d="M116 391h133l37 137h-133zM11 0h133l37 137h-133z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="296" 
d="M116 391h133l37 137h-133zM-44 -145q39 3 69.5 13.5t54.5 30t41.5 49.5t28.5 73l31 116h-133l-37 -137h51q-7 -38 -34 -62t-81 -32z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="640" 
d="M493 77l29 110l-315 160l400 159l33 121l-537 -221l-29 -108z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="640" 
d="M129 421h468l30 112h-468zM62 171h468l30 112h-468z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="640" 
d="M38 77l537 221l29 108l-419 221l-29 -110l315 -160l-400 -159z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="541" 
d="M182 221h81l33 87q120 18 189.5 73.5t69.5 147.5q0 36 -15 69t-42.5 57.5t-67.5 39t-91 14.5q-70 0 -132.5 -26t-108.5 -73l66 -81q32 30 73.5 49t85.5 19q49 0 76 -21.5t27 -60.5q0 -55 -57 -87t-158 -40l-6 -5zM99 0h133l37 137h-133z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="980" 
d="M476 -157q60 0 113 11.5t108 37.5l-16 33q-51 -23 -100 -34t-105 -11q-84 0 -154 25.5t-119.5 72t-77 111.5t-27.5 143q0 89 35 169.5t95.5 142t142.5 97.5t176 36q84 0 150.5 -25t113 -67t71.5 -98.5t25 -119.5t-16 -111t-40 -80.5t-53 -48.5t-55 -16q-43 0 -61 30.5
t-1 84.5l78 247l-92 25l-22 -73q-18 35 -51 58.5t-87 23.5q-46 0 -90.5 -20.5t-80 -57t-57 -86.5t-21.5 -109q0 -40 13 -71.5t34.5 -53t50.5 -32.5t62 -11q54 0 94.5 24t73.5 61q13 -38 47.5 -61t86.5 -23q41 0 81.5 18t73 55.5t52.5 93.5t20 133q0 70 -27.5 133t-79 110.5
t-125.5 75.5t-168 28q-102 0 -191.5 -39t-155.5 -105t-104 -153.5t-38 -184.5q0 -86 30.5 -157.5t85.5 -123t132 -80t170 -28.5zM456 151q-42 0 -67 25.5t-25 70.5q0 36 12.5 67.5t33.5 55.5t48.5 37.5t56.5 13.5q45 0 72.5 -26.5t27.5 -70.5q0 -35 -14 -66.5t-36.5 -55
t-51 -37.5t-57.5 -14z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="763" 
d="M-50 0h133l117 169h331l27 -169h126l-119 705h-118zM275 278l196 283l45 -283h-241z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="717" 
d="M19 0h335q71 0 126.5 15.5t93.5 43.5t58 66.5t20 85.5q0 57 -30.5 91t-77.5 52q70 15 115 60.5t45 117.5q0 36 -14 66.5t-42.5 53t-71.5 35.5t-101 13h-268zM248 405l50 186h166q59 0 87.5 -21t28.5 -58q0 -52 -42 -79.5t-116 -27.5h-174zM169 109l51 192h187
q57 0 90 -21.5t33 -61.5q0 -51 -42.5 -80t-116.5 -29h-202z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="730" 
d="M391 -12q91 0 155 31.5t114 79.5l-81 82q-42 -36 -84 -58t-102 -22q-42 0 -76.5 13.5t-60 38.5t-39.5 60t-14 78q0 61 23 117t61.5 98.5t89.5 67.5t107 25q34 0 61.5 -7.5t49.5 -21.5t40 -33.5t34 -43.5l102 69q-39 69 -106 109.5t-167 40.5q-88 0 -165 -35t-134.5 -93.5
t-90.5 -136t-33 -161.5q0 -69 24 -124t66.5 -94t100 -59.5t125.5 -20.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="775" 
d="M19 0h251q118 0 209.5 34t154.5 97q55 55 84 125t29 149q0 128 -77 204q-42 42 -109 66.5t-164 24.5h-190zM172 112l129 476h87q64 0 109 -15.5t73 -43.5q23 -23 36 -55t13 -74q0 -57 -20 -106t-58 -87q-47 -47 -112.5 -71t-143.5 -24h-113z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="660" 
d="M19 0h519l30 110h-397l50 188h347l30 109h-347l49 183h391l30 110h-514z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="661" 
d="M19 0h124l76 285h353l30 112h-353l51 191h398l30 112h-521z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="782" 
d="M395 -12q77 0 137.5 18t114.5 50l95 335h-298l-29 -107h179l-43 -148q-27 -14 -63.5 -25t-84.5 -11q-43 0 -79.5 13t-63.5 37.5t-42 60.5t-15 82q0 60 22.5 115t62 97.5t94 68t117.5 25.5q37 0 66 -7t52.5 -19t42 -28.5t33.5 -34.5l92 78q-18 24 -42.5 46.5t-58.5 40
t-77.5 27.5t-98.5 10q-93 0 -172.5 -35.5t-137.5 -94.5t-91 -135.5t-33 -157.5q0 -75 27 -131.5t72 -94t102.5 -56.5t119.5 -19z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="756" 
d="M19 0h124l79 295h329l-79 -295h123l188 700h-123l-78 -291h-329l78 291h-124z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="317" 
d="M26 0h124l188 700h-124z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="564" 
d="M190 -10q60 0 108.5 17.5t86.5 54.5q18 18 32 38t25.5 44t21.5 53.5t20 67.5l117 435h-127l-109 -407q-9 -32 -16.5 -55.5t-15 -40t-16 -28.5t-18.5 -22q-20 -20 -46 -31.5t-58 -11.5q-44 0 -76 21t-58 66l-90 -71q32 -61 89 -95.5t130 -34.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="723" 
d="M19 0h124l49 186l169 142l141 -328h136l-179 401l356 299h-169l-410 -354l95 354h-124z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="618" 
d="M19 0h491l31 112h-368l158 588h-124z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="868" 
d="M19 0h123l134 500l129 -328h4l308 330l-135 -502h125l188 700h-141l-300 -331l-122 331h-125z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="786" 
d="M19 0h122l136 509l245 -509h103l188 700h-121l-134 -495l-237 495h-114z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="836" 
d="M390 -12q91 0 167.5 35.5t131.5 93.5t86 133t31 155q0 65 -23 121t-64 97.5t-99.5 65t-128.5 23.5q-91 0 -167 -35.5t-131 -93.5t-86 -133.5t-31 -154.5q0 -65 22.5 -121.5t64 -97.5t99 -64.5t128.5 -23.5zM403 101q-47 0 -84 15t-62.5 42t-39 63.5t-13.5 80.5
q0 54 20 107t56.5 95t87 68.5t111.5 26.5q47 0 84 -15t62.5 -42t39 -64t13.5 -80q0 -54 -20 -107t-56.5 -95t-87 -68.5t-111.5 -26.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="674" 
d="M19 0h124l61 227h146q74 0 137 18t108.5 52t71 83.5t25.5 112.5q0 86 -52 138q-36 37 -88 53t-124 16h-221zM233 336l67 252h124q77 0 113 -36q27 -27 27 -69q0 -70 -55.5 -108.5t-152.5 -38.5h-123z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="836" 
d="M640 -38l100 68l-61 77q60 59 93.5 136.5t33.5 161.5q0 65 -23 121t-64 97.5t-99.5 65t-128.5 23.5q-91 0 -167 -35.5t-131 -93.5t-86 -133.5t-31 -154.5q0 -65 22.5 -121.5t64 -97.5t99 -64.5t128.5 -23.5q55 0 104 13.5t92 37.5zM403 101q-47 0 -84 15t-62.5 42
t-39 63.5t-13.5 80.5q0 54 20 107t56.5 95t87 68.5t111.5 26.5q47 0 84 -15t62.5 -42t39 -64t13.5 -80q0 -51 -18.5 -101t-51.5 -91l-84 120l-101 -69l99 -124q-26 -15 -56 -23.5t-63 -8.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="723" 
d="M19 0h124l65 244h146l120 -244h141l-130 256q98 20 153.5 71.5t66.5 128.5q9 38 3.5 74t-22.5 68q-26 49 -81.5 75.5t-143.5 26.5h-254zM237 353l64 235h153q54 0 85 -15.5t41 -43.5q16 -34 1 -73q-9 -51 -59.5 -77t-125.5 -26h-159z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="637" 
d="M296 -10q55 0 105 15.5t87 45t59 71t22 92.5q0 36 -12 64t-34.5 50.5t-55 40t-72.5 33.5q-38 15 -64 27.5t-42 24.5t-23 25t-7 30q0 18 9 34.5t25.5 29t40.5 20.5t55 8q55 0 97.5 -20.5t82.5 -59.5l86 84q-43 48 -106 76.5t-151 28.5q-56 0 -105 -16t-85.5 -44.5
t-57.5 -69.5t-21 -90q0 -36 13 -64t37 -50.5t57 -40t73 -33.5q37 -15 62 -27.5t40 -24.5t21 -25t6 -29q0 -44 -38 -70.5t-97 -26.5q-68 0 -118 25t-93 74l-93 -77q48 -64 122.5 -97.5t174.5 -33.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="618" 
d="M174 0h123l158 586h222l31 114h-568l-31 -114h223z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="762" 
d="M345 -11q73 0 129.5 22t99.5 65q73 73 109 207l112 417h-123l-107 -398q-14 -50 -33 -86t-44 -61q-53 -53 -131 -53q-73 0 -110 37.5t-37 97.5q0 22 2.5 41t7.5 36l103 386h-123l-98 -369q-8 -29 -12 -56.5t-4 -54.5q0 -107 67.5 -169t191.5 -62z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="721" 
d="M229 -5h109l477 705h-139l-351 -535l-63 535h-132z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1098" 
d="M191 -5h111l302 497l36 -497h113l433 705h-140l-306 -519l-34 521h-104l-309 -521l-29 519h-128z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="698" 
d="M-43 0h158l240 261l118 -261h138l-166 348l336 352h-158l-230 -250l-112 250h-138l161 -337z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="655" 
d="M189 0h123l71 265l395 435h-152l-277 -316l-106 316h-136l156 -422z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="681" 
d="M-19 0h574l29 109h-404l535 500l25 91h-557l-30 -109h387l-535 -500z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="454" 
d="M-2 -130h308l25 95h-192l172 640h192l26 95h-308z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="521" 
d="M339 -128h101l-212 926h-101z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="454" 
d="M-64 -130h308l223 830h-308l-25 -95h192l-172 -640h-192z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M98 493h100l137 132l67 -132h84l-96 208h-85z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="600" 
d="M-115 -160h605l25 94h-604z" />
    <glyph glyph-name="grave" unicode="`" 
d="M353 595h83l-61 164l-113 -49z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="583" 
d="M174 -11q54 0 95 21.5t76 58.5l-18 -69h119l79 297q5 20 8 38t3 37q0 78 -53.5 121t-158.5 43q-54 0 -101 -9.5t-88 -26.5l20 -100q32 13 68.5 21t76.5 8q61 0 88 -21.5t27 -56.5q0 -12 -1.5 -21t-4.5 -21l-3 -11q-35 12 -74.5 19t-83.5 7q-51 0 -94 -12.5t-74.5 -36.5
t-49 -59.5t-17.5 -81.5q0 -33 12.5 -60t34 -45.5t51 -29t63.5 -10.5zM221 78q-39 0 -61.5 18.5t-22.5 53.5q0 42 34.5 68.5t95.5 26.5q35 0 65 -6t55 -14l-6 -23q-6 -24 -21.5 -46.5t-37 -40t-47.5 -27.5t-54 -10z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="668" 
d="M329 -11q57 0 110 24.5t94 67.5t65.5 102.5t24.5 129.5q0 52 -15.5 94t-43 71.5t-65.5 45t-82 15.5q-52 0 -92.5 -22.5t-76.5 -58.5l73 272h-121l-195 -730h121l25 94q24 -44 66 -74.5t112 -30.5zM315 92q-30 0 -54.5 10t-42 28t-27 42.5t-9.5 53.5q0 42 16 80t42.5 66.5
t61 45.5t70.5 17q57 0 91 -35t34 -96q0 -44 -14.5 -82.5t-39.5 -67.5t-58 -45.5t-70 -16.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="561" 
d="M286 -12q67 0 114 23t89 61l-64 79q-32 -28 -63.5 -43.5t-71.5 -15.5q-26 0 -48.5 9t-39.5 26t-27 41.5t-10 55.5q0 42 15 80t40 67t57.5 46t68.5 17q51 0 80 -22.5t50 -56.5l94 64q-14 24 -33 45.5t-45 38t-60 26.5t-76 10q-63 0 -119.5 -25t-99.5 -68t-68 -100.5
t-25 -122.5q0 -54 18.5 -97.5t51.5 -74t77 -47t95 -16.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="668" 
d="M250 -11q52 0 92.5 22.5t76.5 58.5l-19 -70h121l195 730h-121l-79 -296q-24 44 -66 74.5t-112 30.5q-57 0 -110 -24.5t-94 -67.5t-65.5 -102.5t-24.5 -129.5q0 -53 15.5 -94.5t43.5 -71t65.5 -45t81.5 -15.5zM296 92q-57 0 -91.5 35.5t-34.5 95.5q0 44 14.5 83t39.5 67.5
t58 45t70 16.5q30 0 54.5 -10t42 -27.5t27.5 -42t10 -53.5q0 -42 -16 -80t-42.5 -67t-61 -46t-70.5 -17z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="600" 
d="M159 222h384q11 27 17 55.5t6 56.5q0 43 -14 80.5t-41 65t-66 43.5t-90 16q-69 0 -126 -28t-98.5 -73.5t-64 -103t-22.5 -115.5q0 -53 17 -96t48 -73t75.5 -46t99.5 -16q69 0 118 21t87 52l-58 77q-34 -26 -65.5 -38.5t-69.5 -12.5q-68 0 -102.5 37.5t-34.5 97.5z
M172 302q20 60 66 99t108 39q55 0 80 -30t25 -71q0 -22 -5 -37h-274z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="396" 
d="M33 0h121l114 424h144l28 104h-146l8 30q20 74 87 74q36 0 74 -16l27 101q-20 8 -46 13t-58 5q-77 0 -129 -39.5t-72 -124.5l-10 -43h-67l-28 -104h67z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="666" 
d="M246 -162q71 0 121 18.5t87 55.5q15 15 27.5 31.5t23 37.5t20 48t19.5 63l116 436h-121l-25 -95q-11 20 -25.5 39t-35 34t-48.5 24t-65 9q-55 0 -107.5 -23t-93.5 -64t-66 -97t-25 -122q0 -46 17 -81.5t45.5 -60t65 -37t75.5 -12.5q56 0 97.5 21t83.5 59l-9 -32
q-10 -35 -22.5 -61t-32.5 -46q-23 -23 -54.5 -35t-74.5 -12q-54 0 -104.5 17.5t-92.5 49.5l-67 -84q51 -38 122 -59.5t149 -21.5zM296 141q-51 0 -86.5 28t-35.5 85q0 37 14.5 70.5t39 58t56.5 39t68 14.5q59 0 93.5 -33.5t34.5 -84.5q0 -35 -15 -67t-40.5 -56.5t-59 -39
t-69.5 -14.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="618" 
d="M5 0h121l80 297q16 59 55 95.5t90 36.5q44 0 68 -21.5t24 -61.5q0 -11 -2 -25.5t-6 -27.5l-78 -293h121l76 288q6 23 11 47.5t5 45.5q0 73 -43.5 115.5t-117.5 42.5q-51 0 -89.5 -23.5t-70.5 -55.5l72 270h-121z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="286" 
d="M171 609h130l32 115h-131zM12 0h121l142 528h-121z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="285" 
d="M171 609h130l31 115h-130zM-48 -163q31 0 58.5 8.5t50.5 27t40.5 48t29.5 72.5l144 535h-121l-140 -520q-20 -72 -73 -72q-11 0 -20.5 2t-19.5 4l-26 -96q18 -5 36 -7t41 -2z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="582" 
d="M5 0h121l41 153l102 82l94 -235h136l-127 313l266 215h-160l-270 -225l114 427h-121z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="286" 
d="M12 0h121l196 730h-121z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="950" 
d="M5 0h121l80 297q16 59 53 95.5t88 36.5q42 0 64 -21.5t22 -61.5q0 -11 -1.5 -25.5t-5.5 -27.5l-79 -293h121l80 297q16 59 53 95.5t87 36.5q42 0 64 -21.5t22 -61.5q0 -11 -1.5 -25.5t-5.5 -27.5l-79 -293h121l77 288q6 23 11.5 47.5t5.5 45.5q0 72 -42.5 115t-114.5 43
q-64 0 -113.5 -29t-84.5 -77q-14 45 -49.5 75.5t-94.5 30.5q-51 0 -87 -23.5t-68 -55.5l18 68h-121z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="617" 
d="M5 0h121l80 297q16 59 55 95.5t90 36.5q44 0 68 -21.5t24 -61.5q0 -11 -2 -25.5t-6 -27.5l-78 -293h121l76 288q6 23 11 47.5t5 45.5q0 73 -43.5 115.5t-117.5 42.5q-51 0 -89.5 -23.5t-70.5 -55.5l18 68h-121z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="646" 
d="M293 -12q63 0 119 25.5t98 68.5t66.5 99t24.5 116q0 52 -17.5 96.5t-50 77t-78 50.5t-102.5 18q-64 0 -120 -26t-98 -69t-66 -99t-24 -116q0 -52 17.5 -96t50 -76.5t78 -50.5t102.5 -18zM300 93q-60 0 -97.5 36.5t-37.5 103.5q0 35 13.5 71t38 65t57.5 47t72 18
q60 0 97.5 -36.5t37.5 -103.5q0 -36 -14 -72t-38.5 -64.5t-57.5 -46.5t-71 -18z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="668" 
d="M-38 -160h121l68 254q24 -44 66 -74.5t112 -30.5q57 0 110 24.5t94 67.5t65.5 102.5t24.5 129.5q0 52 -15.5 94t-43 71.5t-65.5 45t-82 15.5q-52 0 -92.5 -22.5t-76.5 -58.5l19 70h-121zM315 92q-30 0 -54.5 10t-42 28t-27 42.5t-9.5 53.5q0 42 16 80t42.5 66.5t61 45.5
t70.5 17q57 0 91 -35t34 -96q0 -44 -14.5 -82.5t-39.5 -67.5t-58 -45.5t-70 -16.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="668" 
d="M347 -200h121l194 728h-121l-25 -93q-24 45 -66 74.5t-112 29.5q-57 0 -110 -24.5t-94 -67.5t-65.5 -102.5t-24.5 -129.5q0 -53 15.5 -94.5t43.5 -71t65.5 -45t81.5 -15.5q52 0 93 22.5t76 59.5zM296 92q-57 0 -91.5 35.5t-34.5 95.5q0 44 14.5 83t39.5 67.5t58 45
t70 16.5q60 0 97 -37t37 -96q0 -42 -16 -80t-42.5 -67t-61 -46t-70.5 -17z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="412" 
d="M5 0h121l53 196q15 55 39 95t54.5 66.5t67 39.5t76.5 13h7l34 128q-71 5 -124 -30.5t-97 -98.5l31 119h-121z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="498" 
d="M217 -12q45 0 83.5 12.5t67 36t44.5 56.5t16 74q0 29 -11.5 51t-31 39t-46 31t-56.5 27q-45 19 -64.5 34t-19.5 35q0 26 21 43t60 17q72 0 145 -58l67 78q-33 29 -86.5 51.5t-116.5 22.5q-48 0 -86 -13.5t-65 -37t-41.5 -55.5t-14.5 -69q0 -30 11.5 -52.5t31.5 -40
t48 -31.5t61 -27q40 -17 58.5 -31.5t18.5 -34.5q0 -29 -24.5 -46t-60.5 -17q-40 0 -84 16t-86 58l-72 -74q46 -48 109.5 -71.5t123.5 -23.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="409" 
d="M196 -9q57 0 99 19l28 105q-40 -17 -77 -17q-53 0 -53 41q0 6 1 14t3 16l68 255h141l28 104h-141l39 145h-121l-39 -145h-67l-28 -104h67l-71 -268q-4 -15 -6.5 -30.5t-2.5 -30.5q0 -48 34 -76t98 -28z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="618" 
d="M209 -11q51 0 89.5 23.5t70.5 55.5l-18 -68h121l141 528h-121l-80 -297q-16 -59 -55 -95.5t-90 -36.5q-44 0 -67.5 21.5t-23.5 61.5q0 11 2 26t5 27l79 293h-121l-77 -288q-6 -23 -11 -47.5t-5 -45.5q0 -73 43.5 -115.5t117.5 -42.5z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="581" 
d="M159 -4h109l356 532h-132l-243 -386l-38 386h-125z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="853" 
d="M121 -4h107l216 361l21 -361h109l316 532h-122l-206 -361l-20 363h-103l-214 -363l-10 361h-123z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="572" 
d="M-44 0h147l167 180l79 -180h130l-125 263l256 265h-146l-156 -169l-75 169h-130l122 -253z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="585" 
d="M65 -163q32 0 60.5 8t55 25.5t51.5 45.5t51 69l341 543h-132l-233 -391l-44 391h-124l77 -530q-26 -35 -46.5 -47t-43.5 -12q-17 0 -35.5 6.5t-32.5 16.5l-64 -89q25 -17 53 -26.5t66 -9.5z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="554" 
d="M-24 0h458l27 99h-299l389 346l22 83h-445l-26 -99h285l-388 -346z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="487" 
d="M309 -141l39 74q-59 20 -79.5 43.5t-20.5 59.5q0 20 5.5 42t12.5 42q5 17 10.5 35t5.5 36q0 34 -20.5 56.5t-60.5 30.5q36 4 62.5 15t46 33t32 56.5t19.5 86.5q5 39 15 68t30 49t52 31.5t82 15.5l-6 82q-69 -2 -118 -16t-82 -41t-52 -67.5t-27 -94.5q-7 -49 -17.5 -80.5
t-28 -50t-42.5 -25.5t-62 -7h-30l-25 -92h31q27 0 44 -4.5t26 -12.5t12 -19.5t3 -25.5q0 -12 -3.5 -30t-9.5 -37q-11 -33 -16 -54.5t-5 -41.5q0 -58 40 -97.5t137 -59.5z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="331" 
d="M135 -128h97v926h-97v-926z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="487" 
d="M-35 -141q69 2 118 16t82 41t52 67.5t27 94.5q7 49 17.5 80.5t28 50t43 25.5t61.5 7h30l25 92h-30q-28 0 -45 4.5t-26 12.5t-12 19.5t-3 25.5q0 12 4 30t10 37q11 33 15.5 54.5t4.5 41.5q0 58 -40 97.5t-137 59.5l-39 -74q59 -20 79.5 -43.5t20.5 -59.5q0 -20 -6 -42.5
t-12 -41.5q-6 -17 -10.5 -35t-4.5 -36q0 -34 20 -56.5t60 -30.5q-36 -5 -62.5 -15.5t-46 -32.5t-32 -56.5t-19.5 -86.5q-5 -39 -15 -68t-30 -49t-52 -31.5t-82 -15.5z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="495" 
d="M132 228q15 26 30.5 42.5t36.5 16.5q12 0 22 -6.5t29 -20.5q18 -14 34 -22.5t42 -8.5q43 0 77.5 33t60.5 89l-68 32q-15 -27 -30 -43t-36 -16q-13 0 -23 6t-29 21q-17 14 -33.5 22.5t-41.5 8.5q-44 0 -78 -33t-60 -89z" />
    <glyph glyph-name="nonbreakingspace" unicode="&#xa0;" horiz-adv-x="300" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="303" 
d="M165 563h133l37 137h-133zM9 0h142l10 35l81 444h-67l-156 -444z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="578" 
d="M203 104l-44 -110h88l35 88q21 -3 46 -3q69 0 119.5 23t87.5 57l-65 81q-32 -26 -66 -41t-79 -15h-8l128 328q23 -11 40 -27.5t31 -38.5l92 63q-17 32 -46 59t-73 44l37 94h-88l-30 -77q-6 1 -13 1h-13q-66 0 -123 -24t-99.5 -66t-66.5 -99t-24 -122q0 -75 35.5 -130.5
t98.5 -84.5zM371 525l-124 -318q-26 17 -42 46.5t-16 67.5q0 39 13.5 75t38 64t58 45.5t72.5 19.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="638" 
d="M-10 0h529l29 107h-327l46 171h251l27 104h-251l24 84q8 30 20 54t29 41t36.5 26t47.5 9q43 0 69 -19t46 -51l99 66q-28 51 -77.5 84t-127.5 33q-57 0 -101 -17.5t-79 -52.5q-54 -54 -80 -152l-28 -105h-79l-28 -104h79l-47 -176l-86 -24z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="709" 
d="M389 -12q81 0 140 32.5t104 85.5l-83 72q-36 -39 -72 -59t-87 -20q-56 0 -92.5 33t-44.5 93h209l23 85h-231q2 22 6.5 43.5t12.5 41.5h234l23 85h-225q29 56 75 88.5t106 32.5q30 0 52 -7t39.5 -20t30.5 -32t24 -43l105 51q-13 36 -33.5 66t-50 51t-69 33t-90.5 12
q-57 0 -107.5 -17.5t-91.5 -48.5t-73.5 -73.5t-53.5 -92.5h-73l-23 -85h70q-11 -38 -16 -85h-76l-23 -85h98q5 -55 26 -99t55.5 -74.5t80.5 -47t101 -16.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="708" 
d="M222 0h119l27 102h193l24 89h-193l19 73h193l24 89h-157l328 347h-149l-266 -294l-108 294h-133l138 -347h-157l-24 -89h192l-19 -73h-192l-24 -89h192z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="632" 
d="M285 -9q45 0 83 10.5t66.5 30t44.5 46.5t16 60q0 29 -14 50.5t-34 36.5q73 10 113.5 44.5t40.5 85.5q0 30 -14 51.5t-39 38t-59.5 29.5t-75.5 25q-62 19 -87.5 33.5t-25.5 35.5q0 23 24 36.5t62 13.5q49 0 91.5 -19.5t70.5 -46.5l76 70q-35 40 -95.5 64.5t-135.5 24.5
q-45 0 -83 -10.5t-66.5 -30t-44.5 -46.5t-16 -60q0 -29 14 -50.5t34 -36.5q-73 -10 -113.5 -44t-40.5 -86q0 -30 14 -51.5t39 -38t59.5 -29.5t75.5 -25q62 -19 87.5 -33.5t25.5 -35.5q0 -23 -24 -36.5t-62 -13.5q-49 0 -91.5 19.5t-70.5 46.5l-76 -70q35 -40 95.5 -64.5
t135.5 -24.5zM397 275q-24 0 -47 5t-58 16q-52 17 -76 32t-24 38q0 26 26 43.5t63 17.5q24 0 47 -5t58 -16q52 -17 76 -32t24 -38q0 -26 -26 -43.5t-63 -17.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" 
d="M373 595h122l33 118h-123zM174 595h123l32 118h-123z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="830" 
d="M436 -12q76 0 142.5 29t115.5 78.5t77 115.5t28 140t-28 140t-76.5 115t-114.5 77.5t-142 28.5t-142.5 -29t-115.5 -78.5t-77 -115.5t-28 -140t28 -140t76.5 -115t114.5 -77.5t142 -28.5zM436 24q-69 0 -128 25.5t-102 69.5t-67.5 103t-24.5 127q0 67 24.5 127t68 104
t103 70t128.5 26t128 -25.5t102.5 -69.5t68 -103.5t24.5 -126.5q0 -68 -25 -127.5t-68.5 -103.5t-103 -70t-128.5 -26zM441 159q54 0 89 18t65 50l-48 47q-23 -22 -46.5 -34.5t-56.5 -12.5q-26 0 -48 10t-37.5 27.5t-24.5 41t-9 49.5q0 27 8.5 50t24.5 40.5t37.5 27.5
t47.5 10q30 0 55 -12.5t47 -32.5l47 54q-27 26 -61 42.5t-87 16.5q-43 0 -78.5 -15.5t-61.5 -42.5t-40.5 -63t-14.5 -76q0 -41 14.5 -76.5t40 -62t61 -41.5t76.5 -15z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="390" 
d="M189 403q29 0 52.5 12.5t43.5 32.5l-11 -39h69l44 162q6 21 6 43q0 43 -29.5 66.5t-87.5 23.5q-31 0 -56.5 -5.5t-48.5 -14.5l12 -56q17 7 37 11t41 4q34 0 49 -11.5t15 -31.5q0 -4 -1 -9.5t-3 -10.5l-2 -7q-19 6 -41 10.5t-47 4.5q-54 0 -91 -28t-37 -79q0 -35 24 -56.5
t62 -21.5zM53 282h259l16 58h-259zM217 452q-20 0 -31.5 10.5t-11.5 26.5q0 24 19 39t50 15q19 0 35.5 -3.5t29.5 -7.5l-2 -10q-3 -15 -12 -27.5t-21 -22t-26.5 -15t-29.5 -5.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="605" 
d="M403 34l97 45l-81 167l181 177l-71 65l-231 -226l-2 -8zM152 34l98 45l-81 167l181 177l-72 65l-230 -226l-2 -8z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="830" 
d="M436 -12q76 0 142.5 29t115.5 78.5t77 115.5t28 140t-28 140t-76.5 115t-114.5 77.5t-142 28.5t-142.5 -29t-115.5 -78.5t-77 -115.5t-28 -140t28 -140t76.5 -115t114.5 -77.5t142 -28.5zM436 24q-69 0 -128 25.5t-102 69.5t-67.5 103t-24.5 127q0 67 24.5 127t68 104
t103 70t128.5 26t128 -25.5t102.5 -69.5t68 -103.5t24.5 -126.5q0 -68 -25 -127.5t-68.5 -103.5t-103 -70t-128.5 -26zM291 171h73v131h78l93 -131h86l-103 143q39 11 63.5 39.5t24.5 75.5q0 57 -40 89t-106 32h-169v-379zM364 365v120h93q35 0 55.5 -15.5t20.5 -44.5
q0 -28 -20.5 -44t-55.5 -16h-93z" />
    <glyph glyph-name="macron" unicode="&#xaf;" 
d="M168 600h342l24 89h-342z" />
    <glyph glyph-name="macron" unicode="&#x2c9;" 
d="M168 600h342l24 89h-342z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="421" 
d="M238 377q38 0 71.5 15.5t58.5 41t39.5 59t14.5 69.5q0 31 -10.5 58t-30.5 46.5t-48 30.5t-62 11q-38 0 -71.5 -15.5t-58 -41t-39 -59t-14.5 -69.5q0 -32 10.5 -58.5t30 -46t47.5 -30.5t62 -11zM242 445q-35 0 -56.5 22t-21.5 60q0 21 8 41t21.5 36.5t32.5 26.5t42 10
q35 0 56.5 -22.5t21.5 -59.5q0 -21 -8 -41.5t-21.5 -36.5t-33 -26t-41.5 -10z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="615" 
d="M257 -12q63 0 115 17t89.5 46t58 69t20.5 86q0 28 -9 51.5t-24 41.5t-34.5 30.5t-40.5 20.5q83 21 127.5 67t44.5 115q0 39 -16 71.5t-46 56.5t-72 37.5t-94 13.5q-83 0 -141 -28.5t-105 -74.5l69 -78q38 33 76 52t89 19q48 0 80.5 -23.5t32.5 -63.5q0 -26 -13 -46.5
t-35.5 -34.5t-54.5 -21t-69 -7h-68l-28 -103h62q69 0 106.5 -23t37.5 -70q0 -21 -10 -41t-29 -35.5t-46.5 -24.5t-63.5 -9q-66 0 -107.5 24.5t-72.5 64.5l-93 -68q16 -26 39.5 -50t56 -42t74.5 -29t94 -11z" />
    <glyph glyph-name="acute" unicode="&#xb4;" 
d="M221 595h105l153 115l-108 49z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="836" 
d="M640 -38l100 68l-61 77q60 59 93.5 136.5t33.5 161.5q0 65 -23 121t-64 97.5t-99.5 65t-128.5 23.5q-91 0 -167 -35.5t-131 -93.5t-86 -133.5t-31 -154.5q0 -65 22.5 -121.5t64 -97.5t99 -64.5t128.5 -23.5q55 0 104 13.5t92 37.5zM403 101q-47 0 -84 15t-62.5 42
t-39 63.5t-13.5 80.5q0 54 20 107t56.5 95t87 68.5t111.5 26.5q47 0 84 -15t62.5 -42t39 -64t13.5 -80q0 -51 -18.5 -101t-51.5 -91l-84 120l-101 -69l99 -124q-26 -15 -56 -23.5t-63 -8.5z" />
    <glyph glyph-name="mu" unicode="&#x3bc;" horiz-adv-x="836" 
d="M640 -38l100 68l-61 77q60 59 93.5 136.5t33.5 161.5q0 65 -23 121t-64 97.5t-99.5 65t-128.5 23.5q-91 0 -167 -35.5t-131 -93.5t-86 -133.5t-31 -154.5q0 -65 22.5 -121.5t64 -97.5t99 -64.5t128.5 -23.5q55 0 104 13.5t92 37.5zM403 101q-47 0 -84 15t-62.5 42
t-39 63.5t-13.5 80.5q0 54 20 107t56.5 95t87 68.5t111.5 26.5q47 0 84 -15t62.5 -42t39 -64t13.5 -80q0 -51 -18.5 -101t-51.5 -91l-84 120l-101 -69l99 -124q-26 -15 -56 -23.5t-63 -8.5z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="565" 
d="M281 0h123l188 700h-156q-86 0 -150 -20t-107.5 -56t-65 -85.5t-21.5 -107.5q0 -51 18 -89t50.5 -62.5t78.5 -37.5t102 -15z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="293" 
d="M69 238h134l37 137h-134z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" 
d="M145 -165l122 175h-91l-124 -131z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="398" 
d="M246 401q35 0 66 14t54 37.5t36 54.5t13 64q0 29 -10 53.5t-28 42.5t-44 28t-57 10q-35 0 -66 -14t-54 -37.5t-36 -54.5t-13 -64q0 -58 37.5 -96t101.5 -38zM54 282h275l15 58h-275zM249 462q-32 0 -52 21t-20 55q0 19 7 38t20 34t30.5 24.5t38.5 9.5q32 0 52 -20.5
t20 -55.5q0 -20 -7 -39t-20 -34t-30.5 -24t-38.5 -9z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="605" 
d="M324 34l231 226l2 8l-107 219l-97 -44l81 -167l-181 -178zM74 34l231 226l2 8l-107 219l-97 -44l80 -167l-180 -178z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="541" 
d="M318 563h133l37 137h-133zM249 -9q69 0 131.5 26t108.5 73l-66 81q-32 -30 -73.5 -49t-85.5 -19q-49 0 -76 21.5t-27 60.5q0 55 57 87t158 40l6 5l23 162h-81l-33 -87q-120 -18 -189.5 -74t-69.5 -147q0 -36 15 -69t42.5 -57.5t68 -39t91.5 -14.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="763" 
d="M-50 0h133l117 169h331l27 -169h126l-119 705h-118zM275 278l196 283l45 -283h-241zM479 758h83l-61 164l-113 -49z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="763" 
d="M-50 0h133l117 169h331l27 -169h126l-119 705h-118zM275 278l196 283l45 -283h-241zM445 758h105l153 115l-108 49z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="763" 
d="M-50 0h133l117 169h331l27 -169h126l-119 705h-118zM275 278l196 283l45 -283h-241zM329 758h100l96 67l58 -67h91l-77 146h-113z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="763" 
d="M-50 0h133l117 169h331l27 -169h126l-119 705h-118zM275 278l196 283l45 -283h-241zM387 753q15 26 30.5 42.5t36.5 16.5q12 0 22 -6.5t29 -20.5q17 -14 33.5 -22.5t41.5 -8.5q44 0 78.5 33t60.5 89l-68 32q-15 -27 -30 -43t-36 -16q-13 0 -23 6t-29 21
q-17 14 -33.5 22.5t-41.5 8.5q-44 0 -78 -33t-60 -89z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="763" 
d="M-50 0h133l117 169h331l27 -169h126l-119 705h-118zM275 278l196 283l45 -283h-241zM553 758h122l33 118h-123zM354 758h123l32 118h-123z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="769" 
d="M-47 0h133l117 169h331l27 -169h126l-115 680q29 15 47.5 43.5t18.5 63.5q0 41 -28.5 69t-77.5 28q-26 0 -49 -10t-40 -27t-27 -39.5t-10 -47.5q0 -44 32 -71zM514 710q-25 0 -38.5 14.5t-13.5 35.5q0 29 19 52.5t50 23.5q24 0 37.5 -14.5t13.5 -35.5q0 -29 -19 -52.5
t-49 -23.5zM278 278l196 283l45 -283h-241z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1061" 
d="M-52 0h135l139 169h256l-46 -169h502l30 110h-380l50 188h330l30 109h-330l49 183h374l30 110h-590zM311 278l259 315h21l-84 -315h-196z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="730" 
d="M384 -12h7q91 0 155 31.5t114 79.5l-81 82q-42 -36 -84 -58t-102 -22q-42 0 -76.5 13.5t-60 38.5t-39.5 60t-14 78q0 61 23 117t61.5 98.5t89.5 67.5t107 25q34 0 61.5 -7.5t49.5 -21.5t40 -33.5t34 -43.5l102 69q-39 69 -106 109.5t-167 40.5q-88 0 -165 -35
t-134.5 -93.5t-90.5 -136t-33 -161.5q0 -57 16.5 -104t46 -83.5t70.5 -62t90 -37.5l-114 -120l93 -44z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="660" 
d="M19 0h519l30 110h-397l50 188h347l30 109h-347l49 183h391l30 110h-514zM444 758h83l-61 164l-113 -49z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="660" 
d="M19 0h519l30 110h-397l50 188h347l30 109h-347l49 183h391l30 110h-514zM393 758h105l153 115l-108 49z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="660" 
d="M19 0h519l30 110h-397l50 188h347l30 109h-347l49 183h391l30 110h-514zM302 758h100l96 67l58 -67h91l-77 146h-113z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="660" 
d="M19 0h519l30 110h-397l50 188h347l30 109h-347l49 183h391l30 110h-514zM514 758h122l33 118h-123zM315 758h123l32 118h-123z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="317" 
d="M26 0h124l188 700h-124zM260 758h83l-61 164l-113 -49z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="317" 
d="M26 0h124l188 700h-124zM218 758h105l153 115l-108 49z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="317" 
d="M26 0h124l188 700h-124zM110 758h100l96 67l58 -67h91l-77 146h-113z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="317" 
d="M26 0h124l188 700h-124zM330 758h122l33 118h-123zM131 758h123l32 118h-123z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="798" 
d="M42 0h251q118 0 209.5 34t154.5 97q55 55 84 125t29 149q0 128 -77 204q-42 42 -109 66.5t-164 24.5h-190l-78 -293h-81l-30 -110h81zM195 112l50 185h169l30 110h-169l49 181h87q64 0 109 -15.5t73 -43.5q23 -23 36 -55t13 -74q0 -57 -20 -106t-58 -87
q-47 -47 -112.5 -71t-143.5 -24h-113z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="786" 
d="M19 0h122l136 509l245 -509h103l188 700h-121l-134 -495l-237 495h-114zM393 753q15 26 30.5 42.5t36.5 16.5q12 0 22 -6.5t29 -20.5q17 -14 33.5 -22.5t41.5 -8.5q44 0 78.5 33t60.5 89l-68 32q-15 -27 -30 -43t-36 -16q-13 0 -23 6t-29 21q-17 14 -33.5 22.5t-41.5 8.5
q-44 0 -78 -33t-60 -89z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="836" 
d="M390 -12q91 0 167.5 35.5t131.5 93.5t86 133t31 155q0 65 -23 121t-64 97.5t-99.5 65t-128.5 23.5q-91 0 -167 -35.5t-131 -93.5t-86 -133.5t-31 -154.5q0 -65 22.5 -121.5t64 -97.5t99 -64.5t128.5 -23.5zM403 101q-47 0 -84 15t-62.5 42t-39 63.5t-13.5 80.5
q0 54 20 107t56.5 95t87 68.5t111.5 26.5q47 0 84 -15t62.5 -42t39 -64t13.5 -80q0 -54 -20 -107t-56.5 -95t-87 -68.5t-111.5 -26.5zM495 758h83l-61 164l-113 -49z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="836" 
d="M390 -12q91 0 167.5 35.5t131.5 93.5t86 133t31 155q0 65 -23 121t-64 97.5t-99.5 65t-128.5 23.5q-91 0 -167 -35.5t-131 -93.5t-86 -133.5t-31 -154.5q0 -65 22.5 -121.5t64 -97.5t99 -64.5t128.5 -23.5zM403 101q-47 0 -84 15t-62.5 42t-39 63.5t-13.5 80.5
q0 54 20 107t56.5 95t87 68.5t111.5 26.5q47 0 84 -15t62.5 -42t39 -64t13.5 -80q0 -54 -20 -107t-56.5 -95t-87 -68.5t-111.5 -26.5zM431 758h105l153 115l-108 49z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="836" 
d="M390 -12q91 0 167.5 35.5t131.5 93.5t86 133t31 155q0 65 -23 121t-64 97.5t-99.5 65t-128.5 23.5q-91 0 -167 -35.5t-131 -93.5t-86 -133.5t-31 -154.5q0 -65 22.5 -121.5t64 -97.5t99 -64.5t128.5 -23.5zM403 101q-47 0 -84 15t-62.5 42t-39 63.5t-13.5 80.5
q0 54 20 107t56.5 95t87 68.5t111.5 26.5q47 0 84 -15t62.5 -42t39 -64t13.5 -80q0 -54 -20 -107t-56.5 -95t-87 -68.5t-111.5 -26.5zM350 758h100l96 67l58 -67h91l-77 146h-113z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="836" 
d="M390 -12q91 0 167.5 35.5t131.5 93.5t86 133t31 155q0 65 -23 121t-64 97.5t-99.5 65t-128.5 23.5q-91 0 -167 -35.5t-131 -93.5t-86 -133.5t-31 -154.5q0 -65 22.5 -121.5t64 -97.5t99 -64.5t128.5 -23.5zM403 101q-47 0 -84 15t-62.5 42t-39 63.5t-13.5 80.5
q0 54 20 107t56.5 95t87 68.5t111.5 26.5q47 0 84 -15t62.5 -42t39 -64t13.5 -80q0 -54 -20 -107t-56.5 -95t-87 -68.5t-111.5 -26.5zM405 753q15 26 30.5 42.5t36.5 16.5q12 0 22 -6.5t29 -20.5q17 -14 33.5 -22.5t41.5 -8.5q44 0 78.5 33t60.5 89l-68 32q-15 -27 -30 -43
t-36 -16q-13 0 -23 6t-29 21q-17 14 -33.5 22.5t-41.5 8.5q-44 0 -78 -33t-60 -89z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="836" 
d="M390 -12q91 0 167.5 35.5t131.5 93.5t86 133t31 155q0 65 -23 121t-64 97.5t-99.5 65t-128.5 23.5q-91 0 -167 -35.5t-131 -93.5t-86 -133.5t-31 -154.5q0 -65 22.5 -121.5t64 -97.5t99 -64.5t128.5 -23.5zM403 101q-47 0 -84 15t-62.5 42t-39 63.5t-13.5 80.5
q0 54 20 107t56.5 95t87 68.5t111.5 26.5q47 0 84 -15t62.5 -42t39 -64t13.5 -80q0 -54 -20 -107t-56.5 -95t-87 -68.5t-111.5 -26.5zM565 758h122l33 118h-123zM366 758h123l32 118h-123z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="836" 
d="M390 -12q91 0 167.5 35.5t131.5 93.5t86 133t31 155q0 47 -11.5 89t-33.5 77l149 134h-123l-80 -71q-41 37 -95.5 57.5t-120.5 20.5q-91 0 -167 -35.5t-131 -93.5t-86 -133.5t-31 -154.5q0 -96 47 -170l-146 -130h123l78 68q40 -35 93.5 -55t118.5 -20zM219 206
q-19 41 -19 95t20.5 108t57.5 97t88 70t113 27q83 0 136 -47zM402 96q-80 0 -131 45l393 349q18 -41 18 -92q0 -54 -20.5 -108t-57.5 -97t-88.5 -70t-113.5 -27z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="762" 
d="M345 -11q73 0 129.5 22t99.5 65q73 73 109 207l112 417h-123l-107 -398q-14 -50 -33 -86t-44 -61q-53 -53 -131 -53q-73 0 -110 37.5t-37 97.5q0 22 2.5 41t7.5 36l103 386h-123l-98 -369q-8 -29 -12 -56.5t-4 -54.5q0 -107 67.5 -169t191.5 -62zM476 758h83l-61 164
l-113 -49z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="762" 
d="M345 -11q73 0 129.5 22t99.5 65q73 73 109 207l112 417h-123l-107 -398q-14 -50 -33 -86t-44 -61q-53 -53 -131 -53q-73 0 -110 37.5t-37 97.5q0 22 2.5 41t7.5 36l103 386h-123l-98 -369q-8 -29 -12 -56.5t-4 -54.5q0 -107 67.5 -169t191.5 -62zM424 758h105l153 115
l-108 49z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="762" 
d="M345 -11q73 0 129.5 22t99.5 65q73 73 109 207l112 417h-123l-107 -398q-14 -50 -33 -86t-44 -61q-53 -53 -131 -53q-73 0 -110 37.5t-37 97.5q0 22 2.5 41t7.5 36l103 386h-123l-98 -369q-8 -29 -12 -56.5t-4 -54.5q0 -107 67.5 -169t191.5 -62zM329 758h100l96 67
l58 -67h91l-77 146h-113z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="762" 
d="M345 -11q73 0 129.5 22t99.5 65q73 73 109 207l112 417h-123l-107 -398q-14 -50 -33 -86t-44 -61q-53 -53 -131 -53q-73 0 -110 37.5t-37 97.5q0 22 2.5 41t7.5 36l103 386h-123l-98 -369q-8 -29 -12 -56.5t-4 -54.5q0 -107 67.5 -169t191.5 -62zM549 758h122l33 118
h-123zM350 758h123l32 118h-123z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="655" 
d="M189 0h123l71 265l395 435h-152l-277 -316l-106 316h-136l156 -422zM370 758h105l153 115l-108 49z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="683" 
d="M19 0h124l32 118h146q74 0 136.5 17.5t108.5 51.5t71.5 83.5t25.5 113.5q0 86 -52 138q-37 37 -88.5 52.5t-123.5 15.5h-98l29 110h-123zM203 226l68 253h123q78 0 114 -37q27 -27 27 -68q0 -71 -55.5 -109.5t-152.5 -38.5h-124z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="628" 
d="M225 -2q85 -2 148 14t105 45t62.5 69t20.5 87q0 30 -9.5 53t-26 41t-38.5 30.5t-47 21.5q75 23 123.5 71.5t48.5 125.5q0 39 -14.5 72t-41.5 57t-65.5 38t-86.5 14q-54 0 -99 -18.5t-78 -50.5q-59 -59 -87 -166l-135 -502h121l131 492q18 68 54 105t88 37
q42 0 69.5 -23.5t27.5 -65.5q0 -50 -38.5 -87.5t-125.5 -57.5l-22 -83q72 -10 103.5 -36.5t31.5 -64.5q0 -25 -10.5 -47.5t-33.5 -39t-60 -26.5t-90 -9z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="583" 
d="M174 -11q54 0 95 21.5t76 58.5l-18 -69h119l79 297q5 20 8 38t3 37q0 78 -53.5 121t-158.5 43q-54 0 -101 -9.5t-88 -26.5l20 -100q32 13 68.5 21t76.5 8q61 0 88 -21.5t27 -56.5q0 -12 -1.5 -21t-4.5 -21l-3 -11q-35 12 -74.5 19t-83.5 7q-51 0 -94 -12.5t-74.5 -36.5
t-49 -59.5t-17.5 -81.5q0 -33 12.5 -60t34 -45.5t51 -29t63.5 -10.5zM221 78q-39 0 -61.5 18.5t-22.5 53.5q0 42 34.5 68.5t95.5 26.5q35 0 65 -6t55 -14l-6 -23q-6 -24 -21.5 -46.5t-37 -40t-47.5 -27.5t-54 -10zM343 595h83l-61 164l-113 -49z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="583" 
d="M174 -11q54 0 95 21.5t76 58.5l-18 -69h119l79 297q5 20 8 38t3 37q0 78 -53.5 121t-158.5 43q-54 0 -101 -9.5t-88 -26.5l20 -100q32 13 68.5 21t76.5 8q61 0 88 -21.5t27 -56.5q0 -12 -1.5 -21t-4.5 -21l-3 -11q-35 12 -74.5 19t-83.5 7q-51 0 -94 -12.5t-74.5 -36.5
t-49 -59.5t-17.5 -81.5q0 -33 12.5 -60t34 -45.5t51 -29t63.5 -10.5zM221 78q-39 0 -61.5 18.5t-22.5 53.5q0 42 34.5 68.5t95.5 26.5q35 0 65 -6t55 -14l-6 -23q-6 -24 -21.5 -46.5t-37 -40t-47.5 -27.5t-54 -10zM289 595h105l153 115l-108 49z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="583" 
d="M174 -11q54 0 95 21.5t76 58.5l-18 -69h119l79 297q5 20 8 38t3 37q0 78 -53.5 121t-158.5 43q-54 0 -101 -9.5t-88 -26.5l20 -100q32 13 68.5 21t76.5 8q61 0 88 -21.5t27 -56.5q0 -12 -1.5 -21t-4.5 -21l-3 -11q-35 12 -74.5 19t-83.5 7q-51 0 -94 -12.5t-74.5 -36.5
t-49 -59.5t-17.5 -81.5q0 -33 12.5 -60t34 -45.5t51 -29t63.5 -10.5zM221 78q-39 0 -61.5 18.5t-22.5 53.5q0 42 34.5 68.5t95.5 26.5q35 0 65 -6t55 -14l-6 -23q-6 -24 -21.5 -46.5t-37 -40t-47.5 -27.5t-54 -10zM199 595h100l96 67l58 -67h91l-77 146h-113z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="583" 
d="M174 -11q54 0 95 21.5t76 58.5l-18 -69h119l79 297q5 20 8 38t3 37q0 78 -53.5 121t-158.5 43q-54 0 -101 -9.5t-88 -26.5l20 -100q32 13 68.5 21t76.5 8q61 0 88 -21.5t27 -56.5q0 -12 -1.5 -21t-4.5 -21l-3 -11q-35 12 -74.5 19t-83.5 7q-51 0 -94 -12.5t-74.5 -36.5
t-49 -59.5t-17.5 -81.5q0 -33 12.5 -60t34 -45.5t51 -29t63.5 -10.5zM221 78q-39 0 -61.5 18.5t-22.5 53.5q0 42 34.5 68.5t95.5 26.5q35 0 65 -6t55 -14l-6 -23q-6 -24 -21.5 -46.5t-37 -40t-47.5 -27.5t-54 -10zM250 590q15 26 30.5 42.5t36.5 16.5q12 0 22 -6.5t29 -20.5
q17 -14 33.5 -22.5t41.5 -8.5q44 0 78.5 33t60.5 89l-68 32q-15 -27 -30 -43t-36 -16q-13 0 -23 6t-29 21q-17 14 -33.5 22.5t-41.5 8.5q-44 0 -78 -33t-60 -89z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="583" 
d="M174 -11q54 0 95 21.5t76 58.5l-18 -69h119l79 297q5 20 8 38t3 37q0 78 -53.5 121t-158.5 43q-54 0 -101 -9.5t-88 -26.5l20 -100q32 13 68.5 21t76.5 8q61 0 88 -21.5t27 -56.5q0 -12 -1.5 -21t-4.5 -21l-3 -11q-35 12 -74.5 19t-83.5 7q-51 0 -94 -12.5t-74.5 -36.5
t-49 -59.5t-17.5 -81.5q0 -33 12.5 -60t34 -45.5t51 -29t63.5 -10.5zM221 78q-39 0 -61.5 18.5t-22.5 53.5q0 42 34.5 68.5t95.5 26.5q35 0 65 -6t55 -14l-6 -23q-6 -24 -21.5 -46.5t-37 -40t-47.5 -27.5t-54 -10zM410 595h122l33 118h-123zM211 595h123l32 118h-123z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="583" 
d="M174 -11q54 0 95 21.5t76 58.5l-18 -69h119l79 297q5 20 8 38t3 37q0 78 -53.5 121t-158.5 43q-54 0 -101 -9.5t-88 -26.5l20 -100q32 13 68.5 21t76.5 8q61 0 88 -21.5t27 -56.5q0 -12 -1.5 -21t-4.5 -21l-3 -11q-35 12 -74.5 19t-83.5 7q-51 0 -94 -12.5t-74.5 -36.5
t-49 -59.5t-17.5 -81.5q0 -33 12.5 -60t34 -45.5t51 -29t63.5 -10.5zM221 78q-39 0 -61.5 18.5t-22.5 53.5q0 42 34.5 68.5t95.5 26.5q35 0 65 -6t55 -14l-6 -23q-6 -24 -21.5 -46.5t-37 -40t-47.5 -27.5t-54 -10zM389 594q26 0 48.5 10t39.5 26.5t27 39t10 48.5
q0 42 -28.5 69.5t-77.5 27.5q-26 0 -48.5 -10t-39.5 -27t-27 -39.5t-10 -47.5q0 -42 28.5 -69.5t77.5 -27.5zM390 642q-25 0 -38.5 14t-13.5 36q0 29 19.5 52t49.5 23q25 0 38.5 -14t13.5 -36q0 -29 -19.5 -52t-49.5 -23z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="948" 
d="M431 92q29 -51 81 -77.5t121 -26.5q65 0 117 21t87 52l-57 77q-35 -26 -66.5 -38.5t-69.5 -12.5q-68 0 -102.5 37.5t-34.5 97.5h384q23 55 23 112q0 43 -14 80.5t-40 65t-65 43.5t-88 16q-60 0 -111 -25.5t-89 -67.5q-18 43 -62.5 66.5t-120.5 23.5q-54 0 -101 -9.5
t-88 -26.5l20 -100q32 13 68.5 21t76.5 8q61 0 88 -21.5t27 -56.5q0 -12 -1.5 -21t-4.5 -21l-3 -11q-35 12 -75 19t-83 7q-51 0 -94 -12t-74.5 -35.5t-49 -57.5t-17.5 -78q0 -70 49.5 -111t135.5 -41q72 0 128 29t105 74zM520 302q10 30 27 55.5t39.5 43.5t50 28.5
t57.5 10.5q28 0 47.5 -8t32.5 -22t19 -32t6 -39q0 -11 -1 -20.5t-3 -16.5h-275zM400 221q0 -15 1.5 -29t3.5 -28q-17 -18 -39 -33.5t-45 -27.5t-46.5 -18.5t-44.5 -6.5q-45 0 -69 18.5t-24 53.5q0 42 34.5 68.5t95.5 26.5q65 0 133 -24z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="559" 
d="M204 0l-116 -121l93 -44l107 153h1q66 1 112.5 24t87.5 60l-64 79q-32 -28 -63.5 -43.5t-71.5 -15.5q-26 0 -48.5 9t-39.5 26t-27 41.5t-10 55.5q0 42 15 80t40 67t57.5 46t68.5 17q51 0 80 -22.5t50 -56.5l94 64q-14 24 -33 45.5t-45 38t-60 26.5t-76 10
q-63 0 -119.5 -25t-99.5 -68t-68 -100.5t-25 -122.5q0 -85 44.5 -142.5t115.5 -80.5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="600" 
d="M159 222h384q11 27 17 55.5t6 56.5q0 43 -14 80.5t-41 65t-66 43.5t-90 16q-69 0 -126 -28t-98.5 -73.5t-64 -103t-22.5 -115.5q0 -53 17 -96t48 -73t75.5 -46t99.5 -16q69 0 118 21t87 52l-58 77q-34 -26 -65.5 -38.5t-69.5 -12.5q-68 0 -102.5 37.5t-34.5 97.5z
M172 302q20 60 66 99t108 39q55 0 80 -30t25 -71q0 -22 -5 -37h-274zM349 595h83l-61 164l-113 -49z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="600" 
d="M159 222h384q11 27 17 55.5t6 56.5q0 43 -14 80.5t-41 65t-66 43.5t-90 16q-69 0 -126 -28t-98.5 -73.5t-64 -103t-22.5 -115.5q0 -53 17 -96t48 -73t75.5 -46t99.5 -16q69 0 118 21t87 52l-58 77q-34 -26 -65.5 -38.5t-69.5 -12.5q-68 0 -102.5 37.5t-34.5 97.5z
M172 302q20 60 66 99t108 39q55 0 80 -30t25 -71q0 -22 -5 -37h-274zM293 595h105l153 115l-108 49z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="600" 
d="M159 222h384q11 27 17 55.5t6 56.5q0 43 -14 80.5t-41 65t-66 43.5t-90 16q-69 0 -126 -28t-98.5 -73.5t-64 -103t-22.5 -115.5q0 -53 17 -96t48 -73t75.5 -46t99.5 -16q69 0 118 21t87 52l-58 77q-34 -26 -65.5 -38.5t-69.5 -12.5q-68 0 -102.5 37.5t-34.5 97.5z
M172 302q20 60 66 99t108 39q55 0 80 -30t25 -71q0 -22 -5 -37h-274zM202 595h100l96 67l58 -67h91l-77 146h-113z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="600" 
d="M159 222h384q11 27 17 55.5t6 56.5q0 43 -14 80.5t-41 65t-66 43.5t-90 16q-69 0 -126 -28t-98.5 -73.5t-64 -103t-22.5 -115.5q0 -53 17 -96t48 -73t75.5 -46t99.5 -16q69 0 118 21t87 52l-58 77q-34 -26 -65.5 -38.5t-69.5 -12.5q-68 0 -102.5 37.5t-34.5 97.5z
M172 302q20 60 66 99t108 39q55 0 80 -30t25 -71q0 -22 -5 -37h-274zM416 595h122l33 118h-123zM217 595h123l32 118h-123z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="286" 
d="M12 0h121l142 528h-121zM200 595h83l-61 164l-113 -49z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="286" 
d="M12 0h121l142 528h-121zM159 595h105l153 115l-108 49z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="286" 
d="M12 0h121l142 528h-121zM53 595h100l96 67l58 -67h91l-77 146h-113z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="286" 
d="M12 0h121l142 528h-121zM268 595h122l33 118h-123zM69 595h123l32 118h-123z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="631" 
d="M281 -12q70 0 126.5 26t96 70.5t60.5 103t21 124.5q0 52 -11.5 99t-33.5 101l-48 116l119 46l-32 64l-115 -45l-16 37h-123l34 -76l-143 -56l32 -64l141 55l71 -160q-29 26 -65 41.5t-87 15.5q-54 0 -102 -21t-85 -57.5t-58.5 -85.5t-21.5 -105q0 -51 17.5 -93t49.5 -72
t76 -47t97 -17zM289 92q-57 0 -94 34.5t-37 97.5q0 35 13 66.5t36 55t53.5 37.5t66.5 14q63 0 99 -37t36 -94q0 -34 -13 -65.5t-36.5 -55.5t-55 -38.5t-68.5 -14.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="617" 
d="M5 0h121l80 297q16 59 55 95.5t90 36.5q44 0 68 -21.5t24 -61.5q0 -11 -2 -25.5t-6 -27.5l-78 -293h121l76 288q6 23 11 47.5t5 45.5q0 73 -43.5 115.5t-117.5 42.5q-51 0 -89.5 -23.5t-70.5 -55.5l18 68h-121zM267 590q15 26 30.5 42.5t36.5 16.5q12 0 22 -6.5t29 -20.5
q17 -14 33.5 -22.5t41.5 -8.5q44 0 78.5 33t60.5 89l-68 32q-15 -27 -30 -43t-36 -16q-13 0 -23 6t-29 21q-17 14 -33.5 22.5t-41.5 8.5q-44 0 -78 -33t-60 -89z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="646" 
d="M293 -12q63 0 119 25.5t98 68.5t66.5 99t24.5 116q0 52 -17.5 96.5t-50 77t-78 50.5t-102.5 18q-64 0 -120 -26t-98 -69t-66 -99t-24 -116q0 -52 17.5 -96t50 -76.5t78 -50.5t102.5 -18zM300 93q-60 0 -97.5 36.5t-37.5 103.5q0 35 13.5 71t38 65t57.5 47t72 18
q60 0 97.5 -36.5t37.5 -103.5q0 -36 -14 -72t-38.5 -64.5t-57.5 -46.5t-71 -18zM359 595h83l-61 164l-113 -49z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="646" 
d="M293 -12q63 0 119 25.5t98 68.5t66.5 99t24.5 116q0 52 -17.5 96.5t-50 77t-78 50.5t-102.5 18q-64 0 -120 -26t-98 -69t-66 -99t-24 -116q0 -52 17.5 -96t50 -76.5t78 -50.5t102.5 -18zM300 93q-60 0 -97.5 36.5t-37.5 103.5q0 35 13.5 71t38 65t57.5 47t72 18
q60 0 97.5 -36.5t37.5 -103.5q0 -36 -14 -72t-38.5 -64.5t-57.5 -46.5t-71 -18zM298 595h105l153 115l-108 49z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="646" 
d="M293 -12q63 0 119 25.5t98 68.5t66.5 99t24.5 116q0 52 -17.5 96.5t-50 77t-78 50.5t-102.5 18q-64 0 -120 -26t-98 -69t-66 -99t-24 -116q0 -52 17.5 -96t50 -76.5t78 -50.5t102.5 -18zM300 93q-60 0 -97.5 36.5t-37.5 103.5q0 35 13.5 71t38 65t57.5 47t72 18
q60 0 97.5 -36.5t37.5 -103.5q0 -36 -14 -72t-38.5 -64.5t-57.5 -46.5t-71 -18zM215 595h100l96 67l58 -67h91l-77 146h-113z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="646" 
d="M293 -12q63 0 119 25.5t98 68.5t66.5 99t24.5 116q0 52 -17.5 96.5t-50 77t-78 50.5t-102.5 18q-64 0 -120 -26t-98 -69t-66 -99t-24 -116q0 -52 17.5 -96t50 -76.5t78 -50.5t102.5 -18zM300 93q-60 0 -97.5 36.5t-37.5 103.5q0 35 13.5 71t38 65t57.5 47t72 18
q60 0 97.5 -36.5t37.5 -103.5q0 -36 -14 -72t-38.5 -64.5t-57.5 -46.5t-71 -18zM263 590q15 26 30.5 42.5t36.5 16.5q12 0 22 -6.5t29 -20.5q17 -14 33.5 -22.5t41.5 -8.5q44 0 78.5 33t60.5 89l-68 32q-15 -27 -30 -43t-36 -16q-13 0 -23 6t-29 21q-17 14 -33.5 22.5
t-41.5 8.5q-44 0 -78 -33t-60 -89z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="646" 
d="M293 -12q63 0 119 25.5t98 68.5t66.5 99t24.5 116q0 52 -17.5 96.5t-50 77t-78 50.5t-102.5 18q-64 0 -120 -26t-98 -69t-66 -99t-24 -116q0 -52 17.5 -96t50 -76.5t78 -50.5t102.5 -18zM300 93q-60 0 -97.5 36.5t-37.5 103.5q0 35 13.5 71t38 65t57.5 47t72 18
q60 0 97.5 -36.5t37.5 -103.5q0 -36 -14 -72t-38.5 -64.5t-57.5 -46.5t-71 -18zM424 595h122l33 118h-123zM225 595h123l32 118h-123z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="646" 
d="M293 -12q63 0 119 25.5t98 68.5t66.5 99t24.5 116q0 74 -33 130l121 107h-113l-58 -51q-31 26 -72.5 41t-92.5 15q-64 0 -120 -26t-98 -69t-66 -99t-24 -116q0 -69 31 -125l-124 -109h114l58 51q32 -27 74.5 -42.5t94.5 -15.5zM169 181q-8 24 -8 52q0 36 14 72.5t39 66
t59.5 48t75.5 18.5q50 0 85 -26zM297 89q-54 0 -88 28l267 234q9 -24 9 -57q0 -36 -14 -73t-39.5 -66t-60 -47.5t-74.5 -18.5z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="618" 
d="M209 -11q51 0 89.5 23.5t70.5 55.5l-18 -68h121l141 528h-121l-80 -297q-16 -59 -55 -95.5t-90 -36.5q-44 0 -67.5 21.5t-23.5 61.5q0 11 2 26t5 27l79 293h-121l-77 -288q-6 -23 -11 -47.5t-5 -45.5q0 -73 43.5 -115.5t117.5 -42.5zM369 595h83l-61 164l-113 -49z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="618" 
d="M209 -11q51 0 89.5 23.5t70.5 55.5l-18 -68h121l141 528h-121l-80 -297q-16 -59 -55 -95.5t-90 -36.5q-44 0 -67.5 21.5t-23.5 61.5q0 11 2 26t5 27l79 293h-121l-77 -288q-6 -23 -11 -47.5t-5 -45.5q0 -73 43.5 -115.5t117.5 -42.5zM308 595h105l153 115l-108 49z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="618" 
d="M209 -11q51 0 89.5 23.5t70.5 55.5l-18 -68h121l141 528h-121l-80 -297q-16 -59 -55 -95.5t-90 -36.5q-44 0 -67.5 21.5t-23.5 61.5q0 11 2 26t5 27l79 293h-121l-77 -288q-6 -23 -11 -47.5t-5 -45.5q0 -73 43.5 -115.5t117.5 -42.5zM216 595h100l96 67l58 -67h91
l-77 146h-113z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="618" 
d="M209 -11q51 0 89.5 23.5t70.5 55.5l-18 -68h121l141 528h-121l-80 -297q-16 -59 -55 -95.5t-90 -36.5q-44 0 -67.5 21.5t-23.5 61.5q0 11 2 26t5 27l79 293h-121l-77 -288q-6 -23 -11 -47.5t-5 -45.5q0 -73 43.5 -115.5t117.5 -42.5zM429 595h122l33 118h-123zM230 595
h123l32 118h-123z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="585" 
d="M65 -163q32 0 60.5 8t55 25.5t51.5 45.5t51 69l341 543h-132l-233 -391l-44 391h-124l77 -530q-26 -35 -46.5 -47t-43.5 -12q-17 0 -35.5 6.5t-32.5 16.5l-64 -89q25 -17 53 -26.5t66 -9.5zM295 595h105l153 115l-108 49z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="668" 
d="M-38 -160h121l68 254q24 -44 66 -74.5t112 -30.5q57 0 110 24.5t94 67.5t65.5 102.5t24.5 129.5q0 52 -15.5 94t-43 71.5t-65.5 45t-82 15.5q-52 0 -92.5 -22.5t-76.5 -58.5l74 272h-121zM315 92q-30 0 -54.5 10t-42 28t-27 42.5t-9.5 53.5q0 42 16 80t42.5 66.5t61 45.5
t70.5 17q57 0 91 -35t34 -96q0 -44 -14.5 -82.5t-39.5 -67.5t-58 -45.5t-70 -16.5z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="585" 
d="M65 -163q32 0 60.5 8t55 25.5t51.5 45.5t51 69l341 543h-132l-233 -391l-44 391h-124l77 -530q-26 -35 -46.5 -47t-43.5 -12q-17 0 -35.5 6.5t-32.5 16.5l-64 -89q25 -17 53 -26.5t66 -9.5zM404 595h122l33 118h-123zM205 595h123l32 118h-123z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="286" 
d="M12 0h121l142 528h-121z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="655" 
d="M56 0h491l30 112h-371l55 203l219 61l31 114l-220 -61l73 271h-120l-82 -307l-81 -23l-31 -114l80 22z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="344" 
d="M42 0h121l90 333l81 22l31 115l-82 -22l76 282h-121l-86 -319l-81 -22l-30 -115l81 22z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1096" 
d="M424 0h545l30 110h-380l51 188h329l30 109h-330l49 183h374l30 110h-601q-118 0 -209 -34t-154 -97q-56 -55 -84.5 -125t-28.5 -149q0 -65 18.5 -115t53.5 -85q46 -46 113 -70.5t164 -24.5zM434 112q-65 0 -110 15.5t-73 43.5q-48 48 -48 129q0 57 19.5 106t57.5 87
q47 47 113 71t144 24h88l-128 -476h-63z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1033" 
d="M593 222h384q23 55 23 112q0 43 -14 80.5t-40.5 65t-66 43.5t-90.5 16q-78 0 -135 -35.5t-94 -87.5q-23 54 -75.5 88.5t-132.5 34.5q-64 0 -120 -25.5t-97.5 -68.5t-65.5 -99t-24 -117q0 -52 17.5 -96t50 -76.5t78.5 -50.5t102 -18q73 0 129 32.5t94 82.5
q24 -54 76 -84.5t127 -30.5q65 0 117 21t87 52l-57 77q-35 -26 -66.5 -38.5t-69.5 -12.5q-68 0 -102.5 37.5t-34.5 97.5zM606 302q10 30 27 55.5t39.5 43.5t50 28.5t57.5 10.5q55 0 80 -30t25 -71q0 -22 -5 -37h-274zM299 93q-60 0 -97 38t-37 102q0 37 13.5 73t38 64.5
t58 46t72.5 17.5q60 0 97 -38t37 -104q0 -37 -14 -73t-38 -64t-57.5 -45t-72.5 -17z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="637" 
d="M296 -10q55 0 105 15.5t87 45t59 71t22 92.5q0 36 -12 64t-34.5 50.5t-55 40t-72.5 33.5q-38 15 -64 27.5t-42 24.5t-23 25t-7 30q0 18 9 34.5t25.5 29t40.5 20.5t55 8q55 0 97.5 -20.5t82.5 -59.5l86 84q-43 48 -106 76.5t-151 28.5q-56 0 -105 -16t-85.5 -44.5
t-57.5 -69.5t-21 -90q0 -36 13 -64t37 -50.5t57 -40t73 -33.5q37 -15 62 -27.5t40 -24.5t21 -25t6 -29q0 -44 -38 -70.5t-97 -26.5q-68 0 -118 25t-93 74l-93 -77q48 -64 122.5 -97.5t174.5 -33.5zM377 758h112l156 146h-100l-97 -66l-58 66h-90z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="498" 
d="M217 -12q45 0 83.5 12.5t67 36t44.5 56.5t16 74q0 29 -11.5 51t-31 39t-46 31t-56.5 27q-45 19 -64.5 34t-19.5 35q0 26 21 43t60 17q72 0 145 -58l67 78q-33 29 -86.5 51.5t-116.5 22.5q-48 0 -86 -13.5t-65 -37t-41.5 -55.5t-14.5 -69q0 -30 11.5 -52.5t31.5 -40
t48 -31.5t61 -27q40 -17 58.5 -31.5t18.5 -34.5q0 -29 -24.5 -46t-60.5 -17q-40 0 -84 16t-86 58l-72 -74q46 -48 109.5 -71.5t123.5 -23.5zM277 595h112l156 146h-100l-97 -66l-58 66h-90z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="655" 
d="M189 0h123l71 265l395 435h-152l-277 -316l-106 316h-136l156 -422zM482 758h122l33 118h-123zM283 758h123l32 118h-123z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="681" 
d="M-19 0h574l29 109h-404l535 500l25 91h-557l-30 -109h387l-535 -500zM402 758h112l156 146h-100l-97 -66l-58 66h-90z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="554" 
d="M-24 0h458l27 99h-299l389 346l22 83h-445l-26 -99h285l-388 -346zM295 595h112l156 146h-100l-97 -66l-58 66h-90z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="538" 
d="M53 -9q56 0 102 31t81 109l99 225h150l27 101h-132l29 66q18 42 42 63t58 21q24 0 41.5 -4t33.5 -12l28 100q-20 8 -46 13t-64 5q-67 0 -114.5 -38.5t-81.5 -113.5l-45 -100h-100l-27 -101h82l-83 -189q-18 -41 -40 -58.5t-46 -17.5q-15 0 -25.5 2t-21.5 5l-27 -99
q15 -3 34 -5.5t46 -2.5z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" 
d="M167 595h100l96 67l58 -67h91l-77 146h-113z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" 
d="M280 595h112l156 146h-100l-97 -66l-58 66h-90z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" 
d="M353 593q66 0 120 39.5t76 108.5h-87q-17 -26 -42 -44.5t-56 -18.5q-38 0 -56 18.5t-19 44.5h-85q-1 -5 -1 -13q0 -27 9 -51.5t28 -43t47 -29.5t66 -11z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" 
d="M277 594h125l32 120h-124z" />
    <glyph glyph-name="ring" unicode="&#x2da;" 
d="M358 594q26 0 48.5 10t39.5 26.5t27 39t10 48.5q0 42 -28.5 69.5t-77.5 27.5q-26 0 -48.5 -10t-39.5 -27t-27 -39.5t-10 -47.5q0 -42 28.5 -69.5t77.5 -27.5zM359 642q-25 0 -38.5 14t-13.5 36q0 29 19.5 52t49.5 23q25 0 38.5 -14t13.5 -36q0 -29 -19.5 -52t-49.5 -23z
" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" 
d="M230 -164l15 56q-79 4 -79 51q0 13 6 31.5t16 35.5h-86q-14 -20 -25.5 -46t-11.5 -47q0 -43 41.5 -64.5t123.5 -16.5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" 
d="M224 590q15 26 30.5 42.5t36.5 16.5q12 0 22 -6.5t29 -20.5q17 -14 33.5 -22.5t41.5 -8.5q44 0 78.5 33t60.5 89l-68 32q-15 -27 -30 -43t-36 -16q-13 0 -23 6t-29 21q-17 14 -33.5 22.5t-41.5 8.5q-44 0 -78 -33t-60 -89z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" 
d="M326 595h90l153 115l-93 49zM144 595h90l152 115l-93 49z" />
    <glyph glyph-name="Omega" unicode="&#x3a9;" horiz-adv-x="793" 
d="M429 0h121l114 424h144l28 104h-146l8 30q19 74 87 74q36 0 74 -16l27 101q-20 8 -46 13t-58 5q-77 0 -127.5 -39.5t-72.5 -124.5l-11 -43h-67l-28 -104h67zM33 0h121l114 424h144l28 104h-146l8 30q20 74 87 74q36 0 74 -16l27 101q-20 8 -46 13t-58 5q-77 0 -129 -39.5
t-72 -124.5l-10 -43h-67l-28 -104h67z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="830" 
d="M436 -12q76 0 142.5 29t115.5 78.5t77 115.5t28 140t-28 140t-76.5 115t-114.5 77.5t-142 28.5t-142.5 -29t-115.5 -78.5t-77 -115.5t-28 -140t28 -140t76.5 -115t114.5 -77.5t142 -28.5zM436 24q-69 0 -128 25.5t-102 69.5t-67.5 103t-24.5 127q0 67 24.5 127t68 104
t103 70t128.5 26t128 -25.5t102.5 -69.5t68 -103.5t24.5 -126.5q0 -68 -25 -127.5t-68.5 -103.5t-103 -70t-128.5 -26zM308 167h74v120h72q31 0 59 8t49 24.5t33.5 41t12.5 57.5q0 60 -39.5 94.5t-107.5 34.5h-153v-380zM382 352v129h73q36 0 57 -16t21 -48q0 -29 -20.5 -47
t-57.5 -18h-73z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="530" 
d="M53 249h416l31 115h-416z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="900" 
d="M53 249h786l31 115h-786z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="293" 
d="M109 423h133l37 137h-51q7 39 34 62.5t81 31.5l-9 51q-77 -5 -124.5 -43t-69.5 -123z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="293" 
d="M102 417q78 6 124.5 43.5t69.5 122.5l31 117h-133l-37 -137h51q-7 -39 -34 -62.5t-81 -31.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="293" 
d="M-45 -145q39 3 69.5 13.5t54.5 30t41 49.5t28 73l31 116h-133l-37 -137h52q-8 -38 -34.5 -62t-80.5 -32z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="524" 
d="M341 422h134l37 138h-52q7 38 34 61.5t81 31.5l-9 52q-77 -6 -123.5 -43.5t-69.5 -122.5zM110 422h133l37 138h-52q7 38 34 61.5t81 31.5l-9 52q-78 -6 -124 -43.5t-69 -122.5z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="524" 
d="M331 417q78 6 124.5 43.5t69.5 122.5l31 117h-133l-37 -137h51q-7 -39 -34 -62.5t-81 -31.5zM100 417q78 6 124 43.5t69 122.5l31 117h-133l-37 -137h52q-8 -39 -34.5 -62.5t-80.5 -31.5z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="525" 
d="M187 -145q39 3 69.5 13.5t54.5 30t41 49.5t28 73l31 116h-133l-37 -137h52q-7 -38 -34 -62t-81 -32zM-45 -145q39 3 69.5 13.5t54.5 30t41 49.5t28 73l31 116h-133l-37 -137h52q-8 -38 -34.5 -62t-80.5 -32z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="411" 
d="M157 238h72l65 275l119 -9l20 71l-124 -9l48 134h-79l-26 -134l-117 9l-20 -71l124 9z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="417" 
d="M93 0h79l25 134l118 -9l19 71l-124 -9l52 163l36 163l118 -9l20 71l-124 -9l48 134h-79l-25 -134l-118 9l-20 -71l123 9l-51 -163l-36 -163l-118 9l-19 -71l123 9z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="491" 
d="M242 207q35 0 64.5 15.5t50.5 40.5t33 57t12 65q0 52 -30 83t-82 31q-35 0 -64.5 -15.5t-51 -40.5t-33.5 -57t-12 -65q0 -53 30 -83.5t83 -30.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="815" 
d="M535 0h131l36 135h-130zM272 0h131l36 135h-131zM9 0h130l37 135h-131z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1262" 
d="M236 346q42 0 77.5 18t61 48t40 68t14.5 77q0 33 -11 60.5t-30.5 48t-47 31.5t-60.5 11q-42 0 -77.5 -18t-61 -48t-40 -68t-14.5 -77q0 -33 10.5 -60.5t30 -48t47 -31.5t61.5 -11zM62 0h108l676 700h-109zM1028 -8q42 0 77.5 18t61 48t40 68t14.5 78q0 33 -10.5 60.5
t-30 47.5t-47 31t-61.5 11q-42 0 -77.5 -18t-61 -47.5t-40 -67.5t-14.5 -78q0 -33 11 -60.5t30.5 -47.5t47 -31.5t60.5 -11.5zM628 -8q42 0 77.5 18t61 48t40 68t14.5 78q0 33 -11 60.5t-30.5 47.5t-47 31t-60.5 11q-42 0 -77.5 -18t-61 -47.5t-40 -67.5t-14.5 -78
q0 -33 10.5 -60.5t30 -47.5t47 -31.5t61.5 -11.5zM241 420q-33 0 -52 21.5t-19 59.5q0 23 7.5 47t21.5 43t33 31t42 12q34 0 52.5 -21.5t18.5 -59.5q0 -23 -7.5 -47t-21 -43t-33 -31t-42.5 -12zM1033 66q-33 0 -52 22t-19 59q0 23 7.5 47t21.5 43.5t33.5 31.5t42.5 12
q33 0 52 -22t19 -60q0 -23 -7.5 -46.5t-21.5 -43t-33.5 -31.5t-42.5 -12zM633 66q-33 0 -52 22t-19 59q0 23 7.5 47t21.5 43.5t33.5 31.5t42.5 12q33 0 52 -22t19 -60q0 -23 -7.5 -46.5t-21.5 -43t-33.5 -31.5t-42.5 -12z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="354" 
d="M152 34l98 45l-81 167l181 177l-72 65l-230 -226l-2 -8z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="354" 
d="M74 34l231 226l2 8l-107 219l-97 -44l80 -167l-180 -178z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="697" 
d="M363 392h57l61 219l55 -148h6l135 148l-61 -219h57l85 308h-66l-132 -146l-53 146h-59zM143 392h59l70 254h95l14 54h-248l-15 -54h94z" />
    <glyph glyph-name="fraction" unicode="&#x2215;" horiz-adv-x="529" 
d="M-121 -98h111l696 856h-112z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="1079" 
d="M429 0h121l114 424h144l28 104h-146l8 30q19 74 87 74q36 0 74 -16l27 101q-20 8 -46 13t-58 5q-77 0 -127.5 -39.5t-72.5 -124.5l-11 -43h-67l-28 -104h67zM33 0h121l114 424h144l28 104h-146l8 30q20 74 87 74q36 0 74 -16l27 101q-20 8 -46 13t-58 5q-77 0 -129 -39.5
t-72 -124.5l-10 -43h-67l-28 -104h67zM804 0h121l196 730h-121z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="1079" 
d="M964 609h130l31 115h-130zM429 0h121l114 424h144l28 104h-146l8 30q19 74 87 74q36 0 74 -16l27 101q-20 8 -46 13t-58 5q-77 0 -127.5 -39.5t-72.5 -124.5l-11 -43h-67l-28 -104h67zM33 0h121l114 424h144l28 104h-146l8 30q20 74 87 74q36 0 74 -16l27 101
q-20 8 -46 13t-58 5q-77 0 -129 -39.5t-72 -124.5l-10 -43h-67l-28 -104h67zM804 0h121l142 528h-121z" />
    <glyph glyph-name="apple" unicode="&#xf8ff;" horiz-adv-x="967" 
d="M868 0v750h-767v-750h767zM859 -104l-6 34h-1l-13 -26l-13 26h-1l-6 -34h6l3 20l10 -21h2l10 21l3 -20h6zM806 -87q0 7 -4.5 12t-11.5 5q-8 0 -13 -5t-5 -13q0 -7 4.5 -12t12.5 -5t12.5 5t4.5 13zM758 -96q-6 -4 -10 -4q-13 0 -13 13q0 5 3.5 8.5t8.5 3.5q6 0 9 -3l2 5
q-5 3 -11 3q-8 0 -13 -5t-5 -13q0 -17 18 -17q7 0 10 3zM702 -71h-7l-9 -14l-11 15l-6 -1l14 -19v-14h6v14zM656 -104v33h-6v-13h-16v13h-6v-33h6v15h16v-15h6zM612 -82q0 5 -4.5 9t-18.5 2v-33h6v10q17 0 17 12zM576 -104l-16 34h-2l-15 -34h6l3 8h14l4 -8h6zM534 -104
l-10 13q7 3 7 10q0 5 -4.5 8.5t-18.5 1.5v-33h6v11h5l8 -11h7zM491 -101v15h-15v-5h9v-7q-4 -2 -6 -2q-13 0 -13 12q0 13 12 13q7 0 11 -4l2 5q-6 4 -12 4q-8 0 -13.5 -5t-5.5 -13t5 -12.5t13 -4.5q7 0 13 4zM447 -87q0 7 -4.5 12t-11.5 5q-8 0 -13 -5t-5 -13q0 -7 4.5 -12
t12.5 -5t12.5 5t4.5 13zM400 -82q0 5 -4.5 9t-18.5 2v-33h6v10q17 0 17 12zM364 -71h-7l-9 -14l-11 15l-6 -1l14 -19v-14h6v14zM323 -71h-30v-5h12v-28h6v28h12v5zM267 -71h-6l-7 -22l-10 23h-1l-10 -23l-7 23l-6 -1l12 -34h1l10 23l11 -23h1zM211 -71h-6l-7 -22l-10 23h-1
l-10 -23l-7 23l-6 -1l12 -34h1l10 23l11 -23h1zM155 -71h-6l-7 -22l-10 23h-1l-10 -23l-7 23l-6 -1l12 -34h1l10 23l11 -23h1zM716 -101q0 4 -4 4t-4 -4t4 -4t4 4zM284 -101q0 4 -4 4t-4 -4t4 -4t4 4zM559 686h-2l-2.5 4.5t-7.5 4.5q-3 0 -3 -3v-23q0 -5 2 -5h3v-2h-17v2h2
q3 0 3 5v23q0 3 -3 3q-5 0 -7.5 -4.5l-2.5 -4.5h-2l0.5 4.5t0.5 7.5q3 -1 18 -1t17 1q1 -3 1 -8v-4zM504 662h-18v2h2q3 0 3 5v10h-17v-10q0 -4 2.5 -4.5l2.5 -0.5v-2h-17v2l2.5 0.5t2.5 4.5v21q0 5 -3 5h-2v2h17v-1v-1h-3q-2 0 -2 -5v-9h17v9q0 5 -3 5h-2v1v1h18v-2h-3
t-3 -5v-21q0 -5 3 -5h3v-2zM439 664v-2h-30l-2 11l2 1l2.5 -4.5t7.5 -4.5h4q4 0 4 3v11h-4q-5 0 -5.5 -3l-0.5 -3h-2v13h2l0.5 -2.5t5.5 -2.5h4v10q0 3 -4 3h-4q-5 0 -7 -4l-2 -4l-2 1l2 10h29v-2h-3q-2 0 -2 -5v-21q0 -5 2 -5h3zM827 618h-685v6h685v-6zM696 540
q0 -12 -8 -17t-15 -5q-14 0 -25.5 11t-11.5 27q0 12 8 17t15 5q14 0 25.5 -11.5t11.5 -26.5zM589 537q0 -8 -6 -13.5t-19 -5.5q-16 0 -16 13q0 10 9 10q7 0 7 -5t-4 -5q-2 0 -3 1t-2 1q-3 0 -3 -3q0 -9 12 -9t12 14q0 10 -6 15q-5 -5 -11 -5q-8 0 -8 6q0 8 16 6q2 2 2 6
q0 12 -10 12q-7 0 -7 -5q0 -2 1.5 -2.5t1.5 -3.5t-4 -3q-5 0 -5 6q0 11 13 11q11 0 16 -5t5 -10q0 -6 -5 -9q14 -5 14 -17zM295 537q0 -8 -6 -13.5t-19 -5.5q-16 0 -16 13q0 10 9 10q7 0 7 -5t-4 -5q-2 0 -3 1t-2 1q-3 0 -3 -3q0 -9 12 -9t12 14q0 10 -6 15q-5 -5 -11 -5
q-8 0 -8 6q0 8 16 6q2 2 2 6q0 12 -10 12q-7 0 -7 -5q0 -2 1.5 -2.5t1.5 -3.5t-4 -3q-5 0 -5 6q0 11 13 11q11 0 16 -5t5 -10q0 -6 -5 -9q14 -5 14 -17zM820 522l1 -3h-29l-1 3l4.5 0.5t2.5 7.5l-3 16h-24l3 -16q2 -7 6 -7.5l4 -0.5l1 -3h-29l-1 3l4.5 0.5t2.5 7.5l-7 35
q-2 7 -6 7.5l-4 0.5l-1 3h29l1 -3l-4.5 -0.5t-2.5 -7.5l3 -15h24l-3 15q-2 7 -5.5 7.5l-3.5 0.5l-1 3h28l1 -3l-4.5 -0.5t-2.5 -7.5l7 -35q2 -7 6 -7.5zM498 519h-29l-1 3l4.5 0.5t2.5 7.5l-3 17h-4q-7 0 -7.5 -5.5l-0.5 -5.5h-3l-5 23h3l2.5 -4.5t9.5 -4.5h4l-3 16
q-1 6 -6 6h-4q-11 0 -12 -6.5l-1 -6.5h-4l0.5 7t-0.5 10h45l1 -3l-4.5 -0.5t-2.5 -7.5l7 -35q2 -7 6 -7.5l4 -0.5zM202 522l1 -3h-29l-1 3l4.5 0.5t2.5 7.5l-3 15q-4 0 -5 -3l-12 -23h-19l-1 3l5 0.5t9 7.5l9 16q-8 1 -13.5 5t-5.5 10q0 15 20 15h27l1 -3l-4.5 -0.5
t-2.5 -7.5l7 -35q2 -7 6 -7.5zM400 521q0 -3 -4 -3q-9 0 -12 7q-8 0 -10 -6.5t-15 -6.5q-5 0 -10 3.5t-5 8.5q0 7 5 7t5 -5q0 -2 -1 -2.5t-1 -2.5q0 -4 5 -4t8.5 6.5t14.5 6.5q-6 6 -8.5 13.5t-3.5 14.5t-2.5 12t-6.5 5t-5 -6t-4 -6q-5 0 -5 5q0 3 4 6.5t9 3.5
q12 0 16.5 -6.5t6 -15t2 -16t4.5 -10.5q7 -1 10 -4t3 -5zM827 475h-685v6h685v-6zM827 378h-36v28h-11v-65h14v-33h-81v33h14v65h-11v-28h-36v62h147v-62zM648 406h-14l-37 -45v-20h15v-33h-81v33h14v19l-37 46h-12v34h62v-34h-10l15 -23l15 23h-11v34h81v-34zM458 308h-81
v33h15v19h-30q-22 0 -34.5 11.5t-12.5 28.5t12.5 28.5t34.5 11.5h96v-34h-14v-65h14v-33zM283 308h-141v61h41v-28h33v21h-24v24h24v20h-33v-28h-41v62h141v-34h-14v-65h14v-33zM827 271h-685v6h685v-6zM728 192q0 -12 -8 -17t-15 -5q-14 0 -25.5 11t-11.5 27q0 12 8 17
t15 5q14 0 25.5 -11.5t11.5 -26.5zM203 217l-2 -2l-4.5 4.5t-7.5 2.5q-9 0 -9 -13q0 -8 5 -25q2 -8 6 -8.5l4 -0.5l1 -4h-28l-1 4l4.5 1t1.5 14q-3 12 -8.5 22.5t-15.5 10.5q-8 0 -8 -6l-3 1q0 6 3.5 9t8.5 3q8 0 13 -6.5t8 -16.5q-1 23 14 23q8 0 11.5 -4t6.5 -9zM823 171
h-29l-1 3l4.5 0.5t2.5 7.5l-3 17h-4q-7 0 -7.5 -5.5l-0.5 -5.5h-3l-5 23h3l2.5 -4.5t9.5 -4.5h4l-3 16q-1 6 -6 6h-4q-11 0 -12 -6.5l-1 -6.5h-4l0.5 7t-0.5 10h45l1 -3l-4.5 -0.5t-2.5 -7.5l7 -35q2 -7 6 -7.5l4 -0.5zM621 225l-5 -0.5t-3 -8.5l5 -22q2 -8 0 -16t-19 -8
q-14 0 -20 9.5t-8 17.5l-4 19q-2 8 -6.5 8.5l-4.5 0.5l-1 3h24v-3l-4.5 -0.5t-2.5 -8.5l4 -19q2 -9 7 -15.5t13 -6.5q11 0 12 5t-1 15l-5 21q-2 8 -6 8.5l-4 0.5l-1 3h29zM522 174l1 -3h-21l-1 3l4 1t2 9l-6 30l-30 -44l-3 1l-8 45q-2 7 -5.5 8l-3.5 1l-1 3h21l1 -3l-4.5 -1
t-2.5 -8l5 -30l29 42h14l1 -3l-5 -0.5t-3 -8.5l7 -32q2 -8 5.5 -9zM414 171h-32q-13 0 -24 9.5t-11 25.5q0 11 8 16.5t17 5.5h30l1 -3q-11 -1 -8 -10l8 -31q2 -8 6 -8.5l4 -0.5zM306 174l1 -3h-29l-1 3l4.5 0.5t2.5 7.5l-3 15q-4 0 -5 -3l-12 -23h-19l-1 3l5 0.5t9 7.5l9 16
q-8 1 -13.5 5t-5.5 10q0 15 20 15h27l1 -3l-4.5 -0.5t-2.5 -7.5l7 -35q2 -7 6 -7.5zM827 127h-685v6h685v-6zM694 46h-14v2l2.5 1t2.5 4v20l-25 -28h-3v28q0 4 -3 5l-3 1v2h15v-2l-3 -1t-3 -5v-18l24 26h10v-2l-3 -2t-3 -4v-20q0 -3 3 -4l3 -1v-2zM633 48v-2h-30l-2 11l2 1
l2.5 -4.5t7.5 -4.5h4q4 0 4 3v11h-4q-5 0 -5.5 -3l-0.5 -3h-2v13h2l0.5 -2.5t5.5 -2.5h4v10q0 3 -4 3h-4q-5 0 -7 -4l-2 -4l-2 1l2 10h29v-2h-3q-2 0 -2 -5v-21q0 -5 2 -5h3zM583 81v-2l-2.5 -0.5t-4.5 -5.5l-12 -27h-1l-9 16l-8 -16q0 -1 -0.5 -0.5l-0.5 0.5h-1l-13 27
q-2 4 -4 5l-2 1v2h13v-2l-2 -1t0 -5l8 -18l7 13l-3 5q-3 5 -5 5.5l-2 0.5l-1 2h26v-2l-2 -1.5t-4 -4.5l-4 -8l5 -10l9 19q2 3 0 4l-2 1v2h15zM480 79l-2.5 -0.5t-4.5 -4.5l-8 -12v-8q0 -5 2.5 -5.5l2.5 -0.5v-2h-17v2l2.5 0.5t2.5 5.5v8l-8 12q-2 4 -5 4.5l-3 0.5v2h15v-2
l-2.5 -0.5t-0.5 -4.5l6 -9l6 9q2 4 0.5 4.5l-1.5 0.5v2h15v-2zM425 64q0 -8 -5 -13.5t-15 -5.5t-15 5.5t-5 13.5t5 13t15 5t15 -5t5 -13zM364 46h-17v2l2.5 0.5t2.5 4.5v9q-4 0 -7 -4l-7 -12h-11v2l2.5 0.5t5.5 4.5l7 10q-9 4 -9 9q0 9 13 9h18v-2l-2.5 -0.5t-2.5 -5.5v-20
q0 -4 2.5 -4.5l2.5 -0.5v-2zM309 46h-18v2l3 0.5t3 5.5v9q-2 0 -4 -2l-8 -8q-3 -4 -0.5 -4.5l2.5 -0.5v-2h-17v2l2.5 1.5t4.5 3.5l13 13l-10 9q-2 2 -5 3l-3 1v2h14v-2l-1.5 -1t0.5 -3l8 -8q2 -2 4 -2v9q0 4 -3 4.5l-3 0.5v2h18v-2l-3 -0.5t-3 -4.5v-20q0 -4 3 -5l3 -1v-2z
M606 -83q0 -6 -11 -6v13q11 1 11 -7zM525 -82q0 -7 -11 -6v12q11 2 11 -6zM394 -83q0 -6 -11 -6v13q11 1 11 -7zM564 -92h-10l5 12zM800 -88q0 -12 -11 -12t-11 13q0 12 11 12t11 -13zM441 -88q0 -12 -11 -12t-11 13q0 12 11 12t11 -13zM176 548l-3 19q-1 5 -9 5t-8 -9
q0 -5 3.5 -10t11.5 -5h5zM566 553q-2 1 -7 1q-4 0 -4 -3q0 -2 4 -2t7 4zM272 553q-2 1 -7 1q-4 0 -4 -3q0 -2 4 -2t7 4zM684 535q0 16 -8 27.5t-17 11.5q-11 0 -11 -14q0 -15 8 -26.5t17 -11.5q11 0 11 13zM392 384v22h-11q-13 0 -13 -11t13 -11h11zM280 200l-3 19
q-1 5 -9 5t-8 -9q0 -5 3.5 -10t11.5 -5h5zM716 187q0 16 -8 27.5t-17 11.5q-11 0 -11 -14q0 -15 8 -26.5t17 -11.5q11 0 11 13zM393 182l-9 35q-2 5 -2.5 5.5t-3.5 1.5h-6q-14 0 -14 -17q0 -12 7.5 -22t18.5 -10h5q4 0 4 7zM553 79q-5 0 0 -9q4 9 0 9zM352 64v10q0 4 -4 4
q-7 0 -7 -6q0 -8 8 -8h3zM417 64q0 16 -12 16t-12 -16q0 -7 3 -12t9 -5q7 0 9.5 5t2.5 12z" />
    <glyph glyph-name="fi" unicode="&#xfb01;" horiz-adv-x="683" 
d="M567 609h131l31 115h-130zM33 0h121l114 424h144l28 104h-146l8 30q20 74 87 74q36 0 74 -16l27 101q-20 8 -46 13t-58 5q-77 0 -129 -39.5t-72 -124.5l-10 -43h-67l-28 -104h67zM408 0h121l142 528h-121z" />
    <glyph glyph-name="fl" unicode="&#xfb02;" horiz-adv-x="683" 
d="M33 0h121l114 424h144l28 104h-146l8 30q20 74 87 74q36 0 74 -16l27 101q-20 8 -46 13t-58 5q-77 0 -129 -39.5t-72 -124.5l-10 -43h-67l-28 -104h67zM408 0h121l196 730h-121z" />
    <glyph glyph-name="NUL" horiz-adv-x="0" 
 />
    <hkern u1="&#x24;" u2="&#x37;" k="10" />
    <hkern u1="&#x26;" u2="y" k="10" />
    <hkern u1="&#x26;" u2="w" k="14" />
    <hkern u1="&#x26;" u2="v" k="21" />
    <hkern u1="&#x26;" u2="Y" k="68" />
    <hkern u1="&#x26;" u2="W" k="55" />
    <hkern u1="&#x26;" u2="V" k="63" />
    <hkern u1="&#x26;" u2="T" k="26" />
    <hkern u1="&#x28;" u2="&#x153;" k="30" />
    <hkern u1="&#x28;" u2="&#x152;" k="10" />
    <hkern u1="&#x28;" u2="&#xb5;" k="10" />
    <hkern u1="&#x28;" u2="s" k="15" />
    <hkern u1="&#x28;" u2="q" k="30" />
    <hkern u1="&#x28;" u2="o" k="30" />
    <hkern u1="&#x28;" u2="j" k="-30" />
    <hkern u1="&#x28;" u2="g" k="20" />
    <hkern u1="&#x28;" u2="e" k="30" />
    <hkern u1="&#x28;" u2="d" k="30" />
    <hkern u1="&#x28;" u2="c" k="30" />
    <hkern u1="&#x28;" u2="Q" k="10" />
    <hkern u1="&#x28;" u2="O" k="10" />
    <hkern u1="&#x28;" u2="J" k="15" />
    <hkern u1="&#x28;" u2="G" k="10" />
    <hkern u1="&#x28;" u2="C" k="10" />
    <hkern u1="&#x2a;" u2="&#x153;" k="20" />
    <hkern u1="&#x2a;" u2="&#xe6;" k="10" />
    <hkern u1="&#x2a;" u2="&#xc6;" k="83" />
    <hkern u1="&#x2a;" u2="t" k="-10" />
    <hkern u1="&#x2a;" u2="s" k="10" />
    <hkern u1="&#x2a;" u2="q" k="15" />
    <hkern u1="&#x2a;" u2="o" k="20" />
    <hkern u1="&#x2a;" u2="g" k="15" />
    <hkern u1="&#x2a;" u2="e" k="20" />
    <hkern u1="&#x2a;" u2="d" k="15" />
    <hkern u1="&#x2a;" u2="c" k="20" />
    <hkern u1="&#x2a;" u2="a" k="10" />
    <hkern u1="&#x2a;" u2="J" k="80" />
    <hkern u1="&#x2a;" u2="A" k="83" />
    <hkern u1="&#x2c;" u2="&#x201d;" k="40" />
    <hkern u1="&#x2c;" u2="&#x2019;" k="40" />
    <hkern u1="&#x2c;" u2="&#x38;" k="35" />
    <hkern u1="&#x2c;" u2="&#x37;" k="25" />
    <hkern u1="&#x2c;" u2="&#x36;" k="45" />
    <hkern u1="&#x2c;" u2="&#x34;" k="6" />
    <hkern u1="&#x2c;" u2="&#x31;" k="50" />
    <hkern u1="&#x2c;" u2="&#x30;" k="55" />
    <hkern u1="&#x2d;" u2="&#xc6;" k="40" />
    <hkern u1="&#x2d;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2d;" u2="z" k="10" />
    <hkern u1="&#x2d;" u2="y" k="15" />
    <hkern u1="&#x2d;" u2="x" k="30" />
    <hkern u1="&#x2d;" u2="w" k="10" />
    <hkern u1="&#x2d;" u2="v" k="15" />
    <hkern u1="&#x2d;" u2="Z" k="30" />
    <hkern u1="&#x2d;" u2="Y" k="83" />
    <hkern u1="&#x2d;" u2="X" k="50" />
    <hkern u1="&#x2d;" u2="W" k="35" />
    <hkern u1="&#x2d;" u2="V" k="40" />
    <hkern u1="&#x2d;" u2="T" k="83" />
    <hkern u1="&#x2d;" u2="A" k="40" />
    <hkern u1="&#x2d;" u2="&#x37;" k="40" />
    <hkern u1="&#x2d;" u2="&#x33;" k="10" />
    <hkern u1="&#x2d;" u2="&#x31;" k="30" />
    <hkern u1="&#x2e;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x2e;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x2e;" u2="&#x2248;" k="15" />
    <hkern u1="&#x2e;" u2="&#x221a;" k="15" />
    <hkern u1="&#x2e;" u2="&#x201d;" k="40" />
    <hkern u1="&#x2e;" u2="&#x2019;" k="40" />
    <hkern u1="&#x2e;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x2e;" u2="&#x153;" k="55" />
    <hkern u1="&#x2e;" u2="&#x152;" k="80" />
    <hkern u1="&#x2e;" u2="&#xe6;" k="23" />
    <hkern u1="&#x2e;" u2="&#xb5;" k="80" />
    <hkern u1="&#x2e;" u2="y" k="65" />
    <hkern u1="&#x2e;" u2="w" k="75" />
    <hkern u1="&#x2e;" u2="v" k="94" />
    <hkern u1="&#x2e;" u2="t" k="25" />
    <hkern u1="&#x2e;" u2="q" k="55" />
    <hkern u1="&#x2e;" u2="o" k="55" />
    <hkern u1="&#x2e;" u2="g" k="30" />
    <hkern u1="&#x2e;" u2="f" k="15" />
    <hkern u1="&#x2e;" u2="e" k="20" />
    <hkern u1="&#x2e;" u2="d" k="55" />
    <hkern u1="&#x2e;" u2="c" k="34" />
    <hkern u1="&#x2e;" u2="a" k="23" />
    <hkern u1="&#x2e;" u2="Y" k="126" />
    <hkern u1="&#x2e;" u2="W" k="125" />
    <hkern u1="&#x2e;" u2="V" k="145" />
    <hkern u1="&#x2e;" u2="U" k="50" />
    <hkern u1="&#x2e;" u2="T" k="98" />
    <hkern u1="&#x2e;" u2="Q" k="80" />
    <hkern u1="&#x2e;" u2="O" k="80" />
    <hkern u1="&#x2e;" u2="G" k="80" />
    <hkern u1="&#x2e;" u2="C" k="80" />
    <hkern u1="&#x2e;" u2="&#x38;" k="35" />
    <hkern u1="&#x2e;" u2="&#x37;" k="25" />
    <hkern u1="&#x2e;" u2="&#x36;" k="45" />
    <hkern u1="&#x2e;" u2="&#x34;" k="6" />
    <hkern u1="&#x2e;" u2="&#x31;" k="50" />
    <hkern u1="&#x2e;" u2="&#x30;" k="55" />
    <hkern u1="&#x2f;" u2="&#xfb02;" k="25" />
    <hkern u1="&#x2f;" u2="&#xfb01;" k="25" />
    <hkern u1="&#x2f;" u2="&#x2248;" k="25" />
    <hkern u1="&#x2f;" u2="&#x221a;" k="25" />
    <hkern u1="&#x2f;" u2="&#x3a9;" k="25" />
    <hkern u1="&#x2f;" u2="&#x153;" k="80" />
    <hkern u1="&#x2f;" u2="&#x152;" k="40" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="65" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="114" />
    <hkern u1="&#x2f;" u2="&#xb5;" k="40" />
    <hkern u1="&#x2f;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2f;" u2="z" k="60" />
    <hkern u1="&#x2f;" u2="y" k="50" />
    <hkern u1="&#x2f;" u2="x" k="50" />
    <hkern u1="&#x2f;" u2="w" k="50" />
    <hkern u1="&#x2f;" u2="v" k="50" />
    <hkern u1="&#x2f;" u2="u" k="50" />
    <hkern u1="&#x2f;" u2="t" k="20" />
    <hkern u1="&#x2f;" u2="s" k="65" />
    <hkern u1="&#x2f;" u2="r" k="50" />
    <hkern u1="&#x2f;" u2="q" k="70" />
    <hkern u1="&#x2f;" u2="p" k="50" />
    <hkern u1="&#x2f;" u2="o" k="80" />
    <hkern u1="&#x2f;" u2="n" k="50" />
    <hkern u1="&#x2f;" u2="m" k="50" />
    <hkern u1="&#x2f;" u2="g" k="70" />
    <hkern u1="&#x2f;" u2="f" k="25" />
    <hkern u1="&#x2f;" u2="e" k="80" />
    <hkern u1="&#x2f;" u2="d" k="70" />
    <hkern u1="&#x2f;" u2="c" k="80" />
    <hkern u1="&#x2f;" u2="a" k="65" />
    <hkern u1="&#x2f;" u2="Z" k="20" />
    <hkern u1="&#x2f;" u2="S" k="10" />
    <hkern u1="&#x2f;" u2="Q" k="40" />
    <hkern u1="&#x2f;" u2="O" k="40" />
    <hkern u1="&#x2f;" u2="J" k="130" />
    <hkern u1="&#x2f;" u2="G" k="40" />
    <hkern u1="&#x2f;" u2="C" k="40" />
    <hkern u1="&#x2f;" u2="A" k="114" />
    <hkern u1="&#x2f;" u2="&#x39;" k="17" />
    <hkern u1="&#x2f;" u2="&#x38;" k="15" />
    <hkern u1="&#x2f;" u2="&#x37;" k="7" />
    <hkern u1="&#x2f;" u2="&#x36;" k="35" />
    <hkern u1="&#x2f;" u2="&#x35;" k="20" />
    <hkern u1="&#x2f;" u2="&#x34;" k="98" />
    <hkern u1="&#x2f;" u2="&#x33;" k="7" />
    <hkern u1="&#x2f;" u2="&#x32;" k="17" />
    <hkern u1="&#x2f;" u2="&#x31;" k="-16" />
    <hkern u1="&#x2f;" u2="&#x30;" k="29" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="160" />
    <hkern u1="&#x30;" u2="&#xb1;" k="13" />
    <hkern u1="&#x30;" u2="&#x37;" k="27" />
    <hkern u1="&#x30;" u2="&#x33;" k="10" />
    <hkern u1="&#x30;" u2="&#x32;" k="10" />
    <hkern u1="&#x30;" u2="&#x31;" k="5" />
    <hkern u1="&#x30;" u2="&#x2f;" k="29" />
    <hkern u1="&#x30;" u2="&#x2e;" k="20" />
    <hkern u1="&#x30;" u2="&#x2c;" k="20" />
    <hkern u1="&#x31;" u2="&#x2215;" k="-55" />
    <hkern u1="&#x32;" u2="&#x2215;" k="-60" />
    <hkern u1="&#x32;" u2="&#x37;" k="8" />
    <hkern u1="&#x32;" u2="&#x34;" k="20" />
    <hkern u1="&#x32;" u2="&#x2f;" k="-5" />
    <hkern u1="&#x33;" u2="&#x2215;" k="-30" />
    <hkern u1="&#x33;" u2="&#x39;" k="5" />
    <hkern u1="&#x33;" u2="&#x37;" k="18" />
    <hkern u1="&#x33;" u2="&#x35;" k="5" />
    <hkern u1="&#x33;" u2="&#x2f;" k="7" />
    <hkern u1="&#x34;" u2="&#x2215;" k="-30" />
    <hkern u1="&#x34;" u2="&#x39;" k="10" />
    <hkern u1="&#x34;" u2="&#x37;" k="33" />
    <hkern u1="&#x34;" u2="&#x31;" k="20" />
    <hkern u1="&#x34;" u2="&#x2f;" k="14" />
    <hkern u1="&#x35;" u2="&#x2215;" k="-20" />
    <hkern u1="&#x35;" u2="&#xb1;" k="5" />
    <hkern u1="&#x35;" u2="&#x39;" k="5" />
    <hkern u1="&#x35;" u2="&#x37;" k="30" />
    <hkern u1="&#x35;" u2="&#x33;" k="5" />
    <hkern u1="&#x35;" u2="&#x32;" k="10" />
    <hkern u1="&#x35;" u2="&#x2f;" k="14" />
    <hkern u1="&#x36;" u2="&#x2215;" k="-30" />
    <hkern u1="&#x36;" u2="&#xb1;" k="3" />
    <hkern u1="&#x36;" u2="&#x39;" k="10" />
    <hkern u1="&#x36;" u2="&#x37;" k="18" />
    <hkern u1="&#x36;" u2="&#x33;" k="10" />
    <hkern u1="&#x36;" u2="&#x31;" k="13" />
    <hkern u1="&#x36;" u2="&#x2f;" k="7" />
    <hkern u1="&#x37;" u2="&#x2215;" k="110" />
    <hkern u1="&#x37;" u2="&#x2014;" k="30" />
    <hkern u1="&#x37;" u2="&#x2013;" k="30" />
    <hkern u1="&#x37;" u2="&#xb1;" k="10" />
    <hkern u1="&#x37;" u2="&#x39;" k="15" />
    <hkern u1="&#x37;" u2="&#x38;" k="25" />
    <hkern u1="&#x37;" u2="&#x36;" k="20" />
    <hkern u1="&#x37;" u2="&#x35;" k="25" />
    <hkern u1="&#x37;" u2="&#x34;" k="85" />
    <hkern u1="&#x37;" u2="&#x33;" k="20" />
    <hkern u1="&#x37;" u2="&#x32;" k="15" />
    <hkern u1="&#x37;" u2="&#x31;" k="-10" />
    <hkern u1="&#x37;" u2="&#x30;" k="20" />
    <hkern u1="&#x37;" u2="&#x2f;" k="134" />
    <hkern u1="&#x37;" u2="&#x2e;" k="97" />
    <hkern u1="&#x37;" u2="&#x2d;" k="30" />
    <hkern u1="&#x37;" u2="&#x2c;" k="97" />
    <hkern u1="&#x38;" u2="&#x2215;" k="-40" />
    <hkern u1="&#x38;" u2="&#xb1;" k="3" />
    <hkern u1="&#x38;" u2="&#x39;" k="5" />
    <hkern u1="&#x38;" u2="&#x37;" k="30" />
    <hkern u1="&#x39;" u2="&#x2215;" k="-15" />
    <hkern u1="&#x39;" u2="&#xb1;" k="10" />
    <hkern u1="&#x39;" u2="&#x37;" k="30" />
    <hkern u1="&#x39;" u2="&#x35;" k="5" />
    <hkern u1="&#x39;" u2="&#x33;" k="11" />
    <hkern u1="&#x39;" u2="&#x32;" k="10" />
    <hkern u1="&#x39;" u2="&#x2f;" k="25" />
    <hkern u1="&#x39;" u2="&#x2e;" k="10" />
    <hkern u1="&#x39;" u2="&#x2c;" k="10" />
    <hkern u1="&#x3a;" u2="T" k="12" />
    <hkern u1="&#x3b;" u2="T" k="12" />
    <hkern u1="A" u2="&#xfb02;" k="20" />
    <hkern u1="A" u2="&#xfb01;" k="20" />
    <hkern u1="A" u2="&#x2248;" k="20" />
    <hkern u1="A" u2="&#x221a;" k="20" />
    <hkern u1="A" u2="&#x2122;" k="100" />
    <hkern u1="A" u2="&#x2039;" k="20" />
    <hkern u1="A" u2="&#x201d;" k="80" />
    <hkern u1="A" u2="&#x201c;" k="80" />
    <hkern u1="A" u2="&#x2019;" k="80" />
    <hkern u1="A" u2="&#x2018;" k="80" />
    <hkern u1="A" u2="&#x2014;" k="40" />
    <hkern u1="A" u2="&#x2013;" k="40" />
    <hkern u1="A" u2="&#x3a9;" k="20" />
    <hkern u1="A" u2="&#x153;" k="23" />
    <hkern u1="A" u2="&#x152;" k="53" />
    <hkern u1="A" u2="&#xc6;" k="20" />
    <hkern u1="A" u2="&#xb5;" k="53" />
    <hkern u1="A" u2="&#xab;" k="20" />
    <hkern u1="A" u2="y" k="47" />
    <hkern u1="A" u2="w" k="37" />
    <hkern u1="A" u2="v" k="52" />
    <hkern u1="A" u2="u" k="10" />
    <hkern u1="A" u2="t" k="30" />
    <hkern u1="A" u2="q" k="25" />
    <hkern u1="A" u2="o" k="23" />
    <hkern u1="A" u2="g" k="25" />
    <hkern u1="A" u2="f" k="20" />
    <hkern u1="A" u2="e" k="23" />
    <hkern u1="A" u2="d" k="25" />
    <hkern u1="A" u2="c" k="23" />
    <hkern u1="A" u2="\" k="120" />
    <hkern u1="A" u2="Y" k="75" />
    <hkern u1="A" u2="X" k="20" />
    <hkern u1="A" u2="W" k="75" />
    <hkern u1="A" u2="V" k="85" />
    <hkern u1="A" u2="U" k="50" />
    <hkern u1="A" u2="T" k="80" />
    <hkern u1="A" u2="S" k="10" />
    <hkern u1="A" u2="Q" k="53" />
    <hkern u1="A" u2="O" k="53" />
    <hkern u1="A" u2="G" k="53" />
    <hkern u1="A" u2="C" k="53" />
    <hkern u1="A" u2="A" k="20" />
    <hkern u1="A" u2="&#x3f;" k="60" />
    <hkern u1="A" u2="&#x2d;" k="40" />
    <hkern u1="A" u2="&#x2a;" k="100" />
    <hkern u1="B" u2="&#xe6;" k="-20" />
    <hkern u1="B" u2="y" k="10" />
    <hkern u1="B" u2="w" k="10" />
    <hkern u1="B" u2="v" k="10" />
    <hkern u1="B" u2="s" k="-15" />
    <hkern u1="B" u2="a" k="-20" />
    <hkern u1="B" u2="Y" k="33" />
    <hkern u1="B" u2="X" k="5" />
    <hkern u1="B" u2="W" k="23" />
    <hkern u1="B" u2="V" k="27" />
    <hkern u1="B" u2="T" k="26" />
    <hkern u1="B" u2="&#x3f;" k="5" />
    <hkern u1="C" u2="&#x2014;" k="13" />
    <hkern u1="C" u2="&#x2013;" k="13" />
    <hkern u1="C" u2="&#x153;" k="10" />
    <hkern u1="C" u2="&#x152;" k="21" />
    <hkern u1="C" u2="&#xb5;" k="21" />
    <hkern u1="C" u2="y" k="10" />
    <hkern u1="C" u2="x" k="10" />
    <hkern u1="C" u2="w" k="10" />
    <hkern u1="C" u2="v" k="10" />
    <hkern u1="C" u2="q" k="10" />
    <hkern u1="C" u2="o" k="10" />
    <hkern u1="C" u2="g" k="10" />
    <hkern u1="C" u2="e" k="10" />
    <hkern u1="C" u2="d" k="10" />
    <hkern u1="C" u2="c" k="10" />
    <hkern u1="C" u2="Y" k="20" />
    <hkern u1="C" u2="X" k="3" />
    <hkern u1="C" u2="W" k="13" />
    <hkern u1="C" u2="V" k="13" />
    <hkern u1="C" u2="U" k="15" />
    <hkern u1="C" u2="T" k="5" />
    <hkern u1="C" u2="Q" k="21" />
    <hkern u1="C" u2="O" k="21" />
    <hkern u1="C" u2="G" k="21" />
    <hkern u1="C" u2="C" k="21" />
    <hkern u1="C" u2="&#x2d;" k="13" />
    <hkern u1="D" u2="&#x2026;" k="40" />
    <hkern u1="D" u2="&#xc6;" k="23" />
    <hkern u1="D" u2="&#x7d;" k="10" />
    <hkern u1="D" u2="x" k="10" />
    <hkern u1="D" u2="]" k="10" />
    <hkern u1="D" u2="\" k="40" />
    <hkern u1="D" u2="Z" k="27" />
    <hkern u1="D" u2="Y" k="60" />
    <hkern u1="D" u2="X" k="30" />
    <hkern u1="D" u2="W" k="48" />
    <hkern u1="D" u2="V" k="55" />
    <hkern u1="D" u2="T" k="60" />
    <hkern u1="D" u2="S" k="7" />
    <hkern u1="D" u2="J" k="10" />
    <hkern u1="D" u2="A" k="23" />
    <hkern u1="D" u2="&#x3f;" k="20" />
    <hkern u1="D" u2="&#x2f;" k="40" />
    <hkern u1="D" u2="&#x2e;" k="40" />
    <hkern u1="D" u2="&#x2c;" k="40" />
    <hkern u1="D" u2="&#x2a;" k="24" />
    <hkern u1="D" u2="&#x29;" k="10" />
    <hkern u1="E" u2="&#x153;" k="10" />
    <hkern u1="E" u2="y" k="10" />
    <hkern u1="E" u2="w" k="10" />
    <hkern u1="E" u2="v" k="10" />
    <hkern u1="E" u2="o" k="10" />
    <hkern u1="E" u2="e" k="10" />
    <hkern u1="E" u2="d" k="10" />
    <hkern u1="E" u2="c" k="10" />
    <hkern u1="F" u2="&#xfb02;" k="6" />
    <hkern u1="F" u2="&#xfb01;" k="6" />
    <hkern u1="F" u2="&#x2248;" k="6" />
    <hkern u1="F" u2="&#x221a;" k="6" />
    <hkern u1="F" u2="&#x203a;" k="15" />
    <hkern u1="F" u2="&#x2026;" k="94" />
    <hkern u1="F" u2="&#x201d;" k="-20" />
    <hkern u1="F" u2="&#x2019;" k="-20" />
    <hkern u1="F" u2="&#x3a9;" k="6" />
    <hkern u1="F" u2="&#x153;" k="18" />
    <hkern u1="F" u2="&#x152;" k="6" />
    <hkern u1="F" u2="&#xe6;" k="38" />
    <hkern u1="F" u2="&#xc6;" k="70" />
    <hkern u1="F" u2="&#xbb;" k="15" />
    <hkern u1="F" u2="&#xb5;" k="6" />
    <hkern u1="F" u2="z" k="30" />
    <hkern u1="F" u2="y" k="27" />
    <hkern u1="F" u2="x" k="42" />
    <hkern u1="F" u2="w" k="23" />
    <hkern u1="F" u2="v" k="27" />
    <hkern u1="F" u2="u" k="9" />
    <hkern u1="F" u2="t" k="6" />
    <hkern u1="F" u2="s" k="33" />
    <hkern u1="F" u2="r" k="8" />
    <hkern u1="F" u2="q" k="15" />
    <hkern u1="F" u2="p" k="9" />
    <hkern u1="F" u2="o" k="18" />
    <hkern u1="F" u2="n" k="8" />
    <hkern u1="F" u2="m" k="8" />
    <hkern u1="F" u2="g" k="15" />
    <hkern u1="F" u2="f" k="6" />
    <hkern u1="F" u2="e" k="18" />
    <hkern u1="F" u2="d" k="15" />
    <hkern u1="F" u2="c" k="18" />
    <hkern u1="F" u2="a" k="38" />
    <hkern u1="F" u2="Z" k="15" />
    <hkern u1="F" u2="Q" k="6" />
    <hkern u1="F" u2="O" k="6" />
    <hkern u1="F" u2="J" k="107" />
    <hkern u1="F" u2="G" k="6" />
    <hkern u1="F" u2="C" k="6" />
    <hkern u1="F" u2="A" k="70" />
    <hkern u1="F" u2="&#x3f;" k="-10" />
    <hkern u1="F" u2="&#x2f;" k="70" />
    <hkern u1="F" u2="&#x2e;" k="94" />
    <hkern u1="F" u2="&#x2c;" k="94" />
    <hkern u1="F" u2="&#x26;" k="6" />
    <hkern u1="G" u2="&#xe6;" k="-10" />
    <hkern u1="G" u2="y" k="5" />
    <hkern u1="G" u2="v" k="5" />
    <hkern u1="G" u2="a" k="-10" />
    <hkern u1="G" u2="\" k="15" />
    <hkern u1="G" u2="Y" k="30" />
    <hkern u1="G" u2="W" k="18" />
    <hkern u1="G" u2="V" k="23" />
    <hkern u1="G" u2="T" k="33" />
    <hkern u1="G" u2="&#x3f;" k="10" />
    <hkern u1="J" u2="&#x2026;" k="15" />
    <hkern u1="J" u2="&#xc6;" k="10" />
    <hkern u1="J" u2="X" k="7" />
    <hkern u1="J" u2="J" k="20" />
    <hkern u1="J" u2="A" k="10" />
    <hkern u1="J" u2="&#x2e;" k="15" />
    <hkern u1="J" u2="&#x2c;" k="15" />
    <hkern u1="K" u2="&#xfb02;" k="20" />
    <hkern u1="K" u2="&#xfb01;" k="20" />
    <hkern u1="K" u2="&#x2248;" k="20" />
    <hkern u1="K" u2="&#x221a;" k="20" />
    <hkern u1="K" u2="&#x2039;" k="20" />
    <hkern u1="K" u2="&#x2014;" k="50" />
    <hkern u1="K" u2="&#x2013;" k="50" />
    <hkern u1="K" u2="&#x3a9;" k="20" />
    <hkern u1="K" u2="&#x153;" k="30" />
    <hkern u1="K" u2="&#x152;" k="45" />
    <hkern u1="K" u2="&#xe6;" k="10" />
    <hkern u1="K" u2="&#xc6;" k="30" />
    <hkern u1="K" u2="&#xb5;" k="45" />
    <hkern u1="K" u2="&#xab;" k="20" />
    <hkern u1="K" u2="y" k="45" />
    <hkern u1="K" u2="w" k="47" />
    <hkern u1="K" u2="v" k="57" />
    <hkern u1="K" u2="u" k="20" />
    <hkern u1="K" u2="t" k="25" />
    <hkern u1="K" u2="s" k="15" />
    <hkern u1="K" u2="q" k="25" />
    <hkern u1="K" u2="o" k="30" />
    <hkern u1="K" u2="g" k="25" />
    <hkern u1="K" u2="f" k="20" />
    <hkern u1="K" u2="e" k="30" />
    <hkern u1="K" u2="d" k="25" />
    <hkern u1="K" u2="c" k="30" />
    <hkern u1="K" u2="a" k="10" />
    <hkern u1="K" u2="Y" k="20" />
    <hkern u1="K" u2="W" k="20" />
    <hkern u1="K" u2="V" k="25" />
    <hkern u1="K" u2="U" k="25" />
    <hkern u1="K" u2="T" k="10" />
    <hkern u1="K" u2="S" k="15" />
    <hkern u1="K" u2="Q" k="45" />
    <hkern u1="K" u2="O" k="45" />
    <hkern u1="K" u2="G" k="45" />
    <hkern u1="K" u2="C" k="45" />
    <hkern u1="K" u2="A" k="30" />
    <hkern u1="K" u2="&#x2d;" k="50" />
    <hkern u1="K" u2="&#x26;" k="14" />
    <hkern u1="L" u2="&#xfb02;" k="20" />
    <hkern u1="L" u2="&#xfb01;" k="20" />
    <hkern u1="L" u2="&#x2248;" k="20" />
    <hkern u1="L" u2="&#x221a;" k="20" />
    <hkern u1="L" u2="&#x2122;" k="90" />
    <hkern u1="L" u2="&#x201d;" k="40" />
    <hkern u1="L" u2="&#x201c;" k="40" />
    <hkern u1="L" u2="&#x2019;" k="40" />
    <hkern u1="L" u2="&#x2018;" k="40" />
    <hkern u1="L" u2="&#x2014;" k="60" />
    <hkern u1="L" u2="&#x2013;" k="60" />
    <hkern u1="L" u2="&#x3a9;" k="20" />
    <hkern u1="L" u2="&#x153;" k="50" />
    <hkern u1="L" u2="&#x152;" k="75" />
    <hkern u1="L" u2="&#xe6;" k="3" />
    <hkern u1="L" u2="&#xb5;" k="75" />
    <hkern u1="L" u2="y" k="75" />
    <hkern u1="L" u2="w" k="70" />
    <hkern u1="L" u2="v" k="75" />
    <hkern u1="L" u2="u" k="25" />
    <hkern u1="L" u2="t" k="30" />
    <hkern u1="L" u2="q" k="50" />
    <hkern u1="L" u2="o" k="50" />
    <hkern u1="L" u2="g" k="40" />
    <hkern u1="L" u2="f" k="20" />
    <hkern u1="L" u2="e" k="50" />
    <hkern u1="L" u2="d" k="40" />
    <hkern u1="L" u2="c" k="50" />
    <hkern u1="L" u2="a" k="3" />
    <hkern u1="L" u2="\" k="120" />
    <hkern u1="L" u2="Y" k="110" />
    <hkern u1="L" u2="W" k="95" />
    <hkern u1="L" u2="V" k="110" />
    <hkern u1="L" u2="U" k="50" />
    <hkern u1="L" u2="T" k="100" />
    <hkern u1="L" u2="S" k="13" />
    <hkern u1="L" u2="Q" k="75" />
    <hkern u1="L" u2="O" k="75" />
    <hkern u1="L" u2="J" k="3" />
    <hkern u1="L" u2="G" k="75" />
    <hkern u1="L" u2="C" k="75" />
    <hkern u1="L" u2="&#x3f;" k="60" />
    <hkern u1="L" u2="&#x2d;" k="60" />
    <hkern u1="L" u2="&#x2a;" k="80" />
    <hkern u1="L" u2="&#x26;" k="15" />
    <hkern u1="O" u2="&#x2026;" k="40" />
    <hkern u1="O" u2="&#xc6;" k="13" />
    <hkern u1="O" u2="&#x7d;" k="10" />
    <hkern u1="O" u2="y" k="10" />
    <hkern u1="O" u2="x" k="5" />
    <hkern u1="O" u2="v" k="10" />
    <hkern u1="O" u2="]" k="10" />
    <hkern u1="O" u2="\" k="40" />
    <hkern u1="O" u2="Z" k="21" />
    <hkern u1="O" u2="Y" k="60" />
    <hkern u1="O" u2="X" k="25" />
    <hkern u1="O" u2="W" k="48" />
    <hkern u1="O" u2="V" k="53" />
    <hkern u1="O" u2="T" k="63" />
    <hkern u1="O" u2="S" k="3" />
    <hkern u1="O" u2="J" k="10" />
    <hkern u1="O" u2="A" k="13" />
    <hkern u1="O" u2="&#x3f;" k="20" />
    <hkern u1="O" u2="&#x2f;" k="40" />
    <hkern u1="O" u2="&#x2e;" k="40" />
    <hkern u1="O" u2="&#x2c;" k="40" />
    <hkern u1="O" u2="&#x2a;" k="28" />
    <hkern u1="O" u2="&#x29;" k="10" />
    <hkern u1="P" u2="&#xfb02;" k="-15" />
    <hkern u1="P" u2="&#xfb01;" k="-15" />
    <hkern u1="P" u2="&#x2248;" k="-15" />
    <hkern u1="P" u2="&#x221a;" k="-15" />
    <hkern u1="P" u2="&#x2039;" k="-10" />
    <hkern u1="P" u2="&#x2026;" k="93" />
    <hkern u1="P" u2="&#x201d;" k="-20" />
    <hkern u1="P" u2="&#x2019;" k="-20" />
    <hkern u1="P" u2="&#x3a9;" k="-15" />
    <hkern u1="P" u2="&#x153;" k="5" />
    <hkern u1="P" u2="&#x152;" k="-5" />
    <hkern u1="P" u2="&#xe6;" k="10" />
    <hkern u1="P" u2="&#xc6;" k="53" />
    <hkern u1="P" u2="&#xb5;" k="-5" />
    <hkern u1="P" u2="&#xab;" k="-10" />
    <hkern u1="P" u2="y" k="-5" />
    <hkern u1="P" u2="w" k="-5" />
    <hkern u1="P" u2="v" k="-5" />
    <hkern u1="P" u2="u" k="-5" />
    <hkern u1="P" u2="t" k="-15" />
    <hkern u1="P" u2="o" k="5" />
    <hkern u1="P" u2="f" k="-15" />
    <hkern u1="P" u2="e" k="5" />
    <hkern u1="P" u2="c" k="5" />
    <hkern u1="P" u2="a" k="10" />
    <hkern u1="P" u2="Z" k="25" />
    <hkern u1="P" u2="Y" k="25" />
    <hkern u1="P" u2="X" k="42" />
    <hkern u1="P" u2="W" k="15" />
    <hkern u1="P" u2="V" k="15" />
    <hkern u1="P" u2="T" k="20" />
    <hkern u1="P" u2="Q" k="-5" />
    <hkern u1="P" u2="O" k="-5" />
    <hkern u1="P" u2="J" k="80" />
    <hkern u1="P" u2="G" k="-5" />
    <hkern u1="P" u2="C" k="-5" />
    <hkern u1="P" u2="A" k="53" />
    <hkern u1="P" u2="&#x2f;" k="60" />
    <hkern u1="P" u2="&#x2e;" k="93" />
    <hkern u1="P" u2="&#x2c;" k="93" />
    <hkern u1="Q" u2="&#x2026;" k="40" />
    <hkern u1="Q" u2="x" k="5" />
    <hkern u1="Q" u2="\" k="40" />
    <hkern u1="Q" u2="Y" k="60" />
    <hkern u1="Q" u2="W" k="48" />
    <hkern u1="Q" u2="V" k="53" />
    <hkern u1="Q" u2="T" k="63" />
    <hkern u1="Q" u2="&#x3f;" k="20" />
    <hkern u1="Q" u2="&#x2e;" k="40" />
    <hkern u1="Q" u2="&#x2c;" k="40" />
    <hkern u1="Q" u2="&#x2a;" k="28" />
    <hkern u1="R" u2="&#xfb02;" k="-10" />
    <hkern u1="R" u2="&#xfb01;" k="-10" />
    <hkern u1="R" u2="&#x2248;" k="-10" />
    <hkern u1="R" u2="&#x221a;" k="-10" />
    <hkern u1="R" u2="&#x3a9;" k="-10" />
    <hkern u1="R" u2="&#x152;" k="1" />
    <hkern u1="R" u2="&#xe6;" k="-3" />
    <hkern u1="R" u2="&#xc6;" k="10" />
    <hkern u1="R" u2="&#xb5;" k="1" />
    <hkern u1="R" u2="t" k="-10" />
    <hkern u1="R" u2="f" k="-10" />
    <hkern u1="R" u2="a" k="-3" />
    <hkern u1="R" u2="Y" k="25" />
    <hkern u1="R" u2="W" k="15" />
    <hkern u1="R" u2="V" k="20" />
    <hkern u1="R" u2="U" k="1" />
    <hkern u1="R" u2="T" k="28" />
    <hkern u1="R" u2="S" k="-8" />
    <hkern u1="R" u2="Q" k="1" />
    <hkern u1="R" u2="O" k="1" />
    <hkern u1="R" u2="J" k="10" />
    <hkern u1="R" u2="G" k="1" />
    <hkern u1="R" u2="C" k="1" />
    <hkern u1="R" u2="A" k="10" />
    <hkern u1="S" u2="&#xfb02;" k="5" />
    <hkern u1="S" u2="&#xfb01;" k="5" />
    <hkern u1="S" u2="&#x2248;" k="5" />
    <hkern u1="S" u2="&#x221a;" k="5" />
    <hkern u1="S" u2="&#x3a9;" k="5" />
    <hkern u1="S" u2="&#x152;" k="15" />
    <hkern u1="S" u2="&#xc6;" k="5" />
    <hkern u1="S" u2="&#xb5;" k="15" />
    <hkern u1="S" u2="z" k="5" />
    <hkern u1="S" u2="y" k="15" />
    <hkern u1="S" u2="x" k="15" />
    <hkern u1="S" u2="w" k="10" />
    <hkern u1="S" u2="v" k="15" />
    <hkern u1="S" u2="t" k="5" />
    <hkern u1="S" u2="f" k="5" />
    <hkern u1="S" u2="\" k="20" />
    <hkern u1="S" u2="Y" k="31" />
    <hkern u1="S" u2="X" k="10" />
    <hkern u1="S" u2="W" k="30" />
    <hkern u1="S" u2="V" k="35" />
    <hkern u1="S" u2="U" k="20" />
    <hkern u1="S" u2="T" k="31" />
    <hkern u1="S" u2="S" k="10" />
    <hkern u1="S" u2="Q" k="15" />
    <hkern u1="S" u2="O" k="15" />
    <hkern u1="S" u2="G" k="15" />
    <hkern u1="S" u2="C" k="15" />
    <hkern u1="S" u2="A" k="5" />
    <hkern u1="S" u2="&#x3f;" k="10" />
    <hkern u1="T" u2="&#xfb02;" k="36" />
    <hkern u1="T" u2="&#xfb01;" k="36" />
    <hkern u1="T" u2="&#x2248;" k="36" />
    <hkern u1="T" u2="&#x221a;" k="36" />
    <hkern u1="T" u2="&#x203a;" k="70" />
    <hkern u1="T" u2="&#x2039;" k="83" />
    <hkern u1="T" u2="&#x2026;" k="83" />
    <hkern u1="T" u2="&#x2014;" k="83" />
    <hkern u1="T" u2="&#x2013;" k="83" />
    <hkern u1="T" u2="&#x3a9;" k="36" />
    <hkern u1="T" u2="&#x153;" k="89" />
    <hkern u1="T" u2="&#x152;" k="18" />
    <hkern u1="T" u2="&#xe6;" k="89" />
    <hkern u1="T" u2="&#xdf;" k="8" />
    <hkern u1="T" u2="&#xc6;" k="80" />
    <hkern u1="T" u2="&#xbb;" k="70" />
    <hkern u1="T" u2="&#xb5;" k="18" />
    <hkern u1="T" u2="&#xab;" k="83" />
    <hkern u1="T" u2="z" k="71" />
    <hkern u1="T" u2="y" k="65" />
    <hkern u1="T" u2="x" k="65" />
    <hkern u1="T" u2="w" k="58" />
    <hkern u1="T" u2="v" k="65" />
    <hkern u1="T" u2="u" k="65" />
    <hkern u1="T" u2="t" k="29" />
    <hkern u1="T" u2="s" k="83" />
    <hkern u1="T" u2="r" k="72" />
    <hkern u1="T" u2="q" k="86" />
    <hkern u1="T" u2="p" k="72" />
    <hkern u1="T" u2="o" k="89" />
    <hkern u1="T" u2="n" k="72" />
    <hkern u1="T" u2="m" k="72" />
    <hkern u1="T" u2="l" k="8" />
    <hkern u1="T" u2="j" k="19" />
    <hkern u1="T" u2="i" k="19" />
    <hkern u1="T" u2="h" k="6" />
    <hkern u1="T" u2="g" k="86" />
    <hkern u1="T" u2="f" k="36" />
    <hkern u1="T" u2="e" k="89" />
    <hkern u1="T" u2="d" k="86" />
    <hkern u1="T" u2="c" k="89" />
    <hkern u1="T" u2="a" k="89" />
    <hkern u1="T" u2="Z" k="13" />
    <hkern u1="T" u2="S" k="-7" />
    <hkern u1="T" u2="Q" k="18" />
    <hkern u1="T" u2="O" k="18" />
    <hkern u1="T" u2="J" k="90" />
    <hkern u1="T" u2="G" k="18" />
    <hkern u1="T" u2="C" k="18" />
    <hkern u1="T" u2="A" k="80" />
    <hkern u1="T" u2="&#x3b;" k="22" />
    <hkern u1="T" u2="&#x3a;" k="22" />
    <hkern u1="T" u2="&#x2f;" k="90" />
    <hkern u1="T" u2="&#x2e;" k="83" />
    <hkern u1="T" u2="&#x2d;" k="83" />
    <hkern u1="T" u2="&#x2c;" k="83" />
    <hkern u1="T" u2="&#x26;" k="29" />
    <hkern u1="U" u2="&#x2026;" k="15" />
    <hkern u1="U" u2="&#xc6;" k="20" />
    <hkern u1="U" u2="x" k="5" />
    <hkern u1="U" u2="X" k="17" />
    <hkern u1="U" u2="J" k="20" />
    <hkern u1="U" u2="A" k="20" />
    <hkern u1="U" u2="&#x2f;" k="15" />
    <hkern u1="U" u2="&#x2e;" k="15" />
    <hkern u1="U" u2="&#x2c;" k="15" />
    <hkern u1="V" u2="&#xfb02;" k="5" />
    <hkern u1="V" u2="&#xfb01;" k="5" />
    <hkern u1="V" u2="&#x2248;" k="5" />
    <hkern u1="V" u2="&#x221a;" k="5" />
    <hkern u1="V" u2="&#x203a;" k="40" />
    <hkern u1="V" u2="&#x2039;" k="60" />
    <hkern u1="V" u2="&#x2026;" k="120" />
    <hkern u1="V" u2="&#x2014;" k="40" />
    <hkern u1="V" u2="&#x2013;" k="40" />
    <hkern u1="V" u2="&#x3a9;" k="5" />
    <hkern u1="V" u2="&#x153;" k="42" />
    <hkern u1="V" u2="&#x152;" k="13" />
    <hkern u1="V" u2="&#xe6;" k="40" />
    <hkern u1="V" u2="&#xdf;" k="10" />
    <hkern u1="V" u2="&#xc6;" k="75" />
    <hkern u1="V" u2="&#xbb;" k="40" />
    <hkern u1="V" u2="&#xb5;" k="13" />
    <hkern u1="V" u2="&#xab;" k="60" />
    <hkern u1="V" u2="z" k="30" />
    <hkern u1="V" u2="y" k="34" />
    <hkern u1="V" u2="x" k="47" />
    <hkern u1="V" u2="w" k="30" />
    <hkern u1="V" u2="v" k="34" />
    <hkern u1="V" u2="u" k="38" />
    <hkern u1="V" u2="t" k="5" />
    <hkern u1="V" u2="s" k="35" />
    <hkern u1="V" u2="r" k="38" />
    <hkern u1="V" u2="q" k="42" />
    <hkern u1="V" u2="p" k="38" />
    <hkern u1="V" u2="o" k="42" />
    <hkern u1="V" u2="n" k="38" />
    <hkern u1="V" u2="m" k="38" />
    <hkern u1="V" u2="l" k="10" />
    <hkern u1="V" u2="j" k="20" />
    <hkern u1="V" u2="i" k="20" />
    <hkern u1="V" u2="g" k="42" />
    <hkern u1="V" u2="f" k="5" />
    <hkern u1="V" u2="e" k="42" />
    <hkern u1="V" u2="d" k="42" />
    <hkern u1="V" u2="c" k="42" />
    <hkern u1="V" u2="a" k="40" />
    <hkern u1="V" u2="Z" k="20" />
    <hkern u1="V" u2="Y" k="15" />
    <hkern u1="V" u2="X" k="20" />
    <hkern u1="V" u2="W" k="20" />
    <hkern u1="V" u2="V" k="20" />
    <hkern u1="V" u2="S" k="5" />
    <hkern u1="V" u2="Q" k="13" />
    <hkern u1="V" u2="O" k="13" />
    <hkern u1="V" u2="J" k="100" />
    <hkern u1="V" u2="G" k="18" />
    <hkern u1="V" u2="C" k="18" />
    <hkern u1="V" u2="A" k="75" />
    <hkern u1="V" u2="&#x3b;" k="20" />
    <hkern u1="V" u2="&#x3a;" k="20" />
    <hkern u1="V" u2="&#x2f;" k="114" />
    <hkern u1="V" u2="&#x2e;" k="120" />
    <hkern u1="V" u2="&#x2d;" k="40" />
    <hkern u1="V" u2="&#x2c;" k="120" />
    <hkern u1="V" u2="&#x26;" k="18" />
    <hkern u1="W" u2="&#xfb02;" k="10" />
    <hkern u1="W" u2="&#xfb01;" k="10" />
    <hkern u1="W" u2="&#x2248;" k="10" />
    <hkern u1="W" u2="&#x221a;" k="10" />
    <hkern u1="W" u2="&#x203a;" k="35" />
    <hkern u1="W" u2="&#x2039;" k="50" />
    <hkern u1="W" u2="&#x2026;" k="100" />
    <hkern u1="W" u2="&#x2014;" k="35" />
    <hkern u1="W" u2="&#x2013;" k="35" />
    <hkern u1="W" u2="&#x3a9;" k="10" />
    <hkern u1="W" u2="&#x153;" k="45" />
    <hkern u1="W" u2="&#x152;" k="13" />
    <hkern u1="W" u2="&#xe6;" k="40" />
    <hkern u1="W" u2="&#xdf;" k="10" />
    <hkern u1="W" u2="&#xc6;" k="65" />
    <hkern u1="W" u2="&#xbb;" k="35" />
    <hkern u1="W" u2="&#xb5;" k="13" />
    <hkern u1="W" u2="&#xab;" k="50" />
    <hkern u1="W" u2="z" k="25" />
    <hkern u1="W" u2="y" k="35" />
    <hkern u1="W" u2="x" k="40" />
    <hkern u1="W" u2="w" k="35" />
    <hkern u1="W" u2="v" k="35" />
    <hkern u1="W" u2="u" k="33" />
    <hkern u1="W" u2="t" k="10" />
    <hkern u1="W" u2="s" k="35" />
    <hkern u1="W" u2="r" k="33" />
    <hkern u1="W" u2="q" k="52" />
    <hkern u1="W" u2="p" k="33" />
    <hkern u1="W" u2="o" k="45" />
    <hkern u1="W" u2="n" k="33" />
    <hkern u1="W" u2="m" k="33" />
    <hkern u1="W" u2="l" k="10" />
    <hkern u1="W" u2="j" k="15" />
    <hkern u1="W" u2="i" k="15" />
    <hkern u1="W" u2="g" k="52" />
    <hkern u1="W" u2="f" k="10" />
    <hkern u1="W" u2="e" k="45" />
    <hkern u1="W" u2="d" k="52" />
    <hkern u1="W" u2="c" k="45" />
    <hkern u1="W" u2="a" k="40" />
    <hkern u1="W" u2="Z" k="20" />
    <hkern u1="W" u2="Y" k="10" />
    <hkern u1="W" u2="X" k="15" />
    <hkern u1="W" u2="W" k="10" />
    <hkern u1="W" u2="V" k="20" />
    <hkern u1="W" u2="S" k="5" />
    <hkern u1="W" u2="Q" k="13" />
    <hkern u1="W" u2="O" k="13" />
    <hkern u1="W" u2="J" k="90" />
    <hkern u1="W" u2="G" k="13" />
    <hkern u1="W" u2="C" k="13" />
    <hkern u1="W" u2="A" k="65" />
    <hkern u1="W" u2="&#x3b;" k="15" />
    <hkern u1="W" u2="&#x3a;" k="15" />
    <hkern u1="W" u2="&#x2f;" k="100" />
    <hkern u1="W" u2="&#x2e;" k="100" />
    <hkern u1="W" u2="&#x2d;" k="35" />
    <hkern u1="W" u2="&#x2c;" k="100" />
    <hkern u1="W" u2="&#x26;" k="23" />
    <hkern u1="X" u2="&#xfb02;" k="20" />
    <hkern u1="X" u2="&#xfb01;" k="20" />
    <hkern u1="X" u2="&#x2248;" k="20" />
    <hkern u1="X" u2="&#x221a;" k="20" />
    <hkern u1="X" u2="&#x203a;" k="20" />
    <hkern u1="X" u2="&#x2039;" k="50" />
    <hkern u1="X" u2="&#x2014;" k="50" />
    <hkern u1="X" u2="&#x2013;" k="50" />
    <hkern u1="X" u2="&#x3a9;" k="20" />
    <hkern u1="X" u2="&#x153;" k="40" />
    <hkern u1="X" u2="&#x152;" k="35" />
    <hkern u1="X" u2="&#xe6;" k="10" />
    <hkern u1="X" u2="&#xdf;" k="10" />
    <hkern u1="X" u2="&#xc6;" k="20" />
    <hkern u1="X" u2="&#xbb;" k="20" />
    <hkern u1="X" u2="&#xb5;" k="35" />
    <hkern u1="X" u2="&#xab;" k="50" />
    <hkern u1="X" u2="y" k="40" />
    <hkern u1="X" u2="w" k="40" />
    <hkern u1="X" u2="v" k="50" />
    <hkern u1="X" u2="u" k="21" />
    <hkern u1="X" u2="t" k="10" />
    <hkern u1="X" u2="q" k="35" />
    <hkern u1="X" u2="o" k="40" />
    <hkern u1="X" u2="l" k="10" />
    <hkern u1="X" u2="j" k="10" />
    <hkern u1="X" u2="i" k="10" />
    <hkern u1="X" u2="g" k="32" />
    <hkern u1="X" u2="f" k="20" />
    <hkern u1="X" u2="e" k="40" />
    <hkern u1="X" u2="d" k="35" />
    <hkern u1="X" u2="c" k="40" />
    <hkern u1="X" u2="a" k="10" />
    <hkern u1="X" u2="Y" k="10" />
    <hkern u1="X" u2="W" k="15" />
    <hkern u1="X" u2="V" k="20" />
    <hkern u1="X" u2="U" k="20" />
    <hkern u1="X" u2="S" k="15" />
    <hkern u1="X" u2="Q" k="35" />
    <hkern u1="X" u2="O" k="35" />
    <hkern u1="X" u2="J" k="10" />
    <hkern u1="X" u2="G" k="35" />
    <hkern u1="X" u2="C" k="35" />
    <hkern u1="X" u2="A" k="20" />
    <hkern u1="X" u2="&#x3f;" k="15" />
    <hkern u1="X" u2="&#x2d;" k="50" />
    <hkern u1="X" u2="&#x26;" k="10" />
    <hkern u1="Y" u2="&#xfb02;" k="15" />
    <hkern u1="Y" u2="&#xfb01;" k="15" />
    <hkern u1="Y" u2="&#x2248;" k="15" />
    <hkern u1="Y" u2="&#x221a;" k="15" />
    <hkern u1="Y" u2="&#x203a;" k="72" />
    <hkern u1="Y" u2="&#x2039;" k="85" />
    <hkern u1="Y" u2="&#x2026;" k="107" />
    <hkern u1="Y" u2="&#x2014;" k="77" />
    <hkern u1="Y" u2="&#x2013;" k="77" />
    <hkern u1="Y" u2="&#x3a9;" k="15" />
    <hkern u1="Y" u2="&#x153;" k="57" />
    <hkern u1="Y" u2="&#x152;" k="11" />
    <hkern u1="Y" u2="&#xe6;" k="65" />
    <hkern u1="Y" u2="&#xdf;" k="10" />
    <hkern u1="Y" u2="&#xc6;" k="75" />
    <hkern u1="Y" u2="&#xbb;" k="72" />
    <hkern u1="Y" u2="&#xb5;" k="11" />
    <hkern u1="Y" u2="&#xab;" k="85" />
    <hkern u1="Y" u2="z" k="40" />
    <hkern u1="Y" u2="y" k="54" />
    <hkern u1="Y" u2="x" k="65" />
    <hkern u1="Y" u2="w" k="50" />
    <hkern u1="Y" u2="v" k="54" />
    <hkern u1="Y" u2="u" k="57" />
    <hkern u1="Y" u2="t" k="11" />
    <hkern u1="Y" u2="s" k="50" />
    <hkern u1="Y" u2="r" k="57" />
    <hkern u1="Y" u2="q" k="57" />
    <hkern u1="Y" u2="p" k="57" />
    <hkern u1="Y" u2="o" k="57" />
    <hkern u1="Y" u2="n" k="57" />
    <hkern u1="Y" u2="m" k="57" />
    <hkern u1="Y" u2="l" k="10" />
    <hkern u1="Y" u2="j" k="20" />
    <hkern u1="Y" u2="i" k="20" />
    <hkern u1="Y" u2="g" k="57" />
    <hkern u1="Y" u2="f" k="15" />
    <hkern u1="Y" u2="e" k="57" />
    <hkern u1="Y" u2="d" k="57" />
    <hkern u1="Y" u2="c" k="57" />
    <hkern u1="Y" u2="a" k="65" />
    <hkern u1="Y" u2="Z" k="10" />
    <hkern u1="Y" u2="X" k="10" />
    <hkern u1="Y" u2="W" k="10" />
    <hkern u1="Y" u2="V" k="15" />
    <hkern u1="Y" u2="S" k="3" />
    <hkern u1="Y" u2="Q" k="11" />
    <hkern u1="Y" u2="O" k="11" />
    <hkern u1="Y" u2="J" k="110" />
    <hkern u1="Y" u2="G" k="11" />
    <hkern u1="Y" u2="C" k="11" />
    <hkern u1="Y" u2="A" k="75" />
    <hkern u1="Y" u2="&#x3b;" k="34" />
    <hkern u1="Y" u2="&#x3a;" k="34" />
    <hkern u1="Y" u2="&#x2f;" k="104" />
    <hkern u1="Y" u2="&#x2e;" k="107" />
    <hkern u1="Y" u2="&#x2d;" k="77" />
    <hkern u1="Y" u2="&#x2c;" k="107" />
    <hkern u1="Y" u2="&#x26;" k="27" />
    <hkern u1="Z" u2="&#xfb02;" k="10" />
    <hkern u1="Z" u2="&#xfb01;" k="10" />
    <hkern u1="Z" u2="&#x2248;" k="10" />
    <hkern u1="Z" u2="&#x221a;" k="10" />
    <hkern u1="Z" u2="&#x2039;" k="20" />
    <hkern u1="Z" u2="&#x2014;" k="30" />
    <hkern u1="Z" u2="&#x2013;" k="30" />
    <hkern u1="Z" u2="&#x3a9;" k="10" />
    <hkern u1="Z" u2="&#x153;" k="25" />
    <hkern u1="Z" u2="&#x152;" k="23" />
    <hkern u1="Z" u2="&#xe6;" k="3" />
    <hkern u1="Z" u2="&#xb5;" k="23" />
    <hkern u1="Z" u2="&#xab;" k="20" />
    <hkern u1="Z" u2="y" k="15" />
    <hkern u1="Z" u2="w" k="15" />
    <hkern u1="Z" u2="v" k="20" />
    <hkern u1="Z" u2="s" k="1" />
    <hkern u1="Z" u2="q" k="20" />
    <hkern u1="Z" u2="o" k="25" />
    <hkern u1="Z" u2="g" k="20" />
    <hkern u1="Z" u2="f" k="10" />
    <hkern u1="Z" u2="e" k="25" />
    <hkern u1="Z" u2="d" k="20" />
    <hkern u1="Z" u2="c" k="25" />
    <hkern u1="Z" u2="a" k="3" />
    <hkern u1="Z" u2="Z" k="10" />
    <hkern u1="Z" u2="S" k="10" />
    <hkern u1="Z" u2="Q" k="23" />
    <hkern u1="Z" u2="O" k="23" />
    <hkern u1="Z" u2="J" k="6" />
    <hkern u1="Z" u2="G" k="23" />
    <hkern u1="Z" u2="C" k="23" />
    <hkern u1="Z" u2="&#x2d;" k="30" />
    <hkern u1="Z" u2="&#x26;" k="5" />
    <hkern u1="[" u2="&#x153;" k="20" />
    <hkern u1="[" u2="&#x152;" k="10" />
    <hkern u1="[" u2="&#xe6;" k="10" />
    <hkern u1="[" u2="&#xb5;" k="10" />
    <hkern u1="[" u2="y" k="10" />
    <hkern u1="[" u2="x" k="10" />
    <hkern u1="[" u2="w" k="20" />
    <hkern u1="[" u2="v" k="20" />
    <hkern u1="[" u2="s" k="15" />
    <hkern u1="[" u2="q" k="20" />
    <hkern u1="[" u2="o" k="20" />
    <hkern u1="[" u2="j" k="-30" />
    <hkern u1="[" u2="e" k="20" />
    <hkern u1="[" u2="d" k="20" />
    <hkern u1="[" u2="c" k="20" />
    <hkern u1="[" u2="a" k="10" />
    <hkern u1="[" u2="Q" k="10" />
    <hkern u1="[" u2="O" k="10" />
    <hkern u1="[" u2="J" k="15" />
    <hkern u1="[" u2="G" k="10" />
    <hkern u1="[" u2="C" k="10" />
    <hkern u1="\" u2="&#xfb02;" k="10" />
    <hkern u1="\" u2="&#xfb01;" k="10" />
    <hkern u1="\" u2="&#x2248;" k="10" />
    <hkern u1="\" u2="&#x221a;" k="10" />
    <hkern u1="\" u2="&#x3a9;" k="10" />
    <hkern u1="\" u2="&#x152;" k="40" />
    <hkern u1="\" u2="&#xb5;" k="40" />
    <hkern u1="\" u2="y" k="60" />
    <hkern u1="\" u2="w" k="60" />
    <hkern u1="\" u2="v" k="70" />
    <hkern u1="\" u2="t" k="30" />
    <hkern u1="\" u2="j" k="-30" />
    <hkern u1="\" u2="f" k="10" />
    <hkern u1="\" u2="Y" k="110" />
    <hkern u1="\" u2="W" k="100" />
    <hkern u1="\" u2="V" k="120" />
    <hkern u1="\" u2="U" k="15" />
    <hkern u1="\" u2="T" k="90" />
    <hkern u1="\" u2="Q" k="40" />
    <hkern u1="\" u2="O" k="40" />
    <hkern u1="\" u2="G" k="40" />
    <hkern u1="\" u2="C" k="40" />
    <hkern u1="a" u2="y" k="30" />
    <hkern u1="a" u2="w" k="20" />
    <hkern u1="a" u2="v" k="30" />
    <hkern u1="a" u2="t" k="10" />
    <hkern u1="a" u2="\" k="75" />
    <hkern u1="a" u2="&#x3f;" k="35" />
    <hkern u1="a" u2="&#x2a;" k="32" />
    <hkern u1="b" u2="&#xfb02;" k="1" />
    <hkern u1="b" u2="&#xfb01;" k="1" />
    <hkern u1="b" u2="&#x2248;" k="1" />
    <hkern u1="b" u2="&#x221a;" k="1" />
    <hkern u1="b" u2="&#x2122;" k="7" />
    <hkern u1="b" u2="&#x203a;" k="5" />
    <hkern u1="b" u2="&#x2026;" k="10" />
    <hkern u1="b" u2="&#x201c;" k="10" />
    <hkern u1="b" u2="&#x2018;" k="10" />
    <hkern u1="b" u2="&#x3a9;" k="1" />
    <hkern u1="b" u2="&#xbb;" k="5" />
    <hkern u1="b" u2="&#x7d;" k="15" />
    <hkern u1="b" u2="z" k="10" />
    <hkern u1="b" u2="y" k="29" />
    <hkern u1="b" u2="x" k="25" />
    <hkern u1="b" u2="w" k="30" />
    <hkern u1="b" u2="v" k="32" />
    <hkern u1="b" u2="t" k="3" />
    <hkern u1="b" u2="f" k="1" />
    <hkern u1="b" u2="]" k="20" />
    <hkern u1="b" u2="\" k="70" />
    <hkern u1="b" u2="&#x3f;" k="35" />
    <hkern u1="b" u2="&#x2e;" k="10" />
    <hkern u1="b" u2="&#x2c;" k="10" />
    <hkern u1="b" u2="&#x2a;" k="43" />
    <hkern u1="b" u2="&#x29;" k="30" />
    <hkern u1="c" u2="&#xfb02;" k="1" />
    <hkern u1="c" u2="&#xfb01;" k="1" />
    <hkern u1="c" u2="&#x2248;" k="1" />
    <hkern u1="c" u2="&#x221a;" k="1" />
    <hkern u1="c" u2="&#x203a;" k="-10" />
    <hkern u1="c" u2="&#x2039;" k="10" />
    <hkern u1="c" u2="&#x201d;" k="-15" />
    <hkern u1="c" u2="&#x201c;" k="-10" />
    <hkern u1="c" u2="&#x2019;" k="-15" />
    <hkern u1="c" u2="&#x2018;" k="-10" />
    <hkern u1="c" u2="&#x3a9;" k="1" />
    <hkern u1="c" u2="&#x153;" k="13" />
    <hkern u1="c" u2="&#xbb;" k="-10" />
    <hkern u1="c" u2="&#xab;" k="10" />
    <hkern u1="c" u2="y" k="10" />
    <hkern u1="c" u2="x" k="1" />
    <hkern u1="c" u2="w" k="13" />
    <hkern u1="c" u2="v" k="13" />
    <hkern u1="c" u2="q" k="8" />
    <hkern u1="c" u2="o" k="13" />
    <hkern u1="c" u2="g" k="8" />
    <hkern u1="c" u2="f" k="1" />
    <hkern u1="c" u2="e" k="13" />
    <hkern u1="c" u2="d" k="8" />
    <hkern u1="c" u2="c" k="13" />
    <hkern u1="c" u2="\" k="40" />
    <hkern u1="c" u2="&#x3f;" k="15" />
    <hkern u1="c" u2="&#x2a;" k="10" />
    <hkern u1="c" u2="&#x29;" k="8" />
    <hkern u1="e" u2="&#x2122;" k="7" />
    <hkern u1="e" u2="&#x2026;" k="10" />
    <hkern u1="e" u2="&#x7d;" k="10" />
    <hkern u1="e" u2="z" k="5" />
    <hkern u1="e" u2="y" k="20" />
    <hkern u1="e" u2="x" k="30" />
    <hkern u1="e" u2="w" k="23" />
    <hkern u1="e" u2="v" k="23" />
    <hkern u1="e" u2="]" k="20" />
    <hkern u1="e" u2="\" k="80" />
    <hkern u1="e" u2="&#x3f;" k="40" />
    <hkern u1="e" u2="&#x2e;" k="10" />
    <hkern u1="e" u2="&#x2c;" k="10" />
    <hkern u1="e" u2="&#x2a;" k="30" />
    <hkern u1="e" u2="&#x29;" k="30" />
    <hkern u1="f" u2="&#x2122;" k="-45" />
    <hkern u1="f" u2="&#x2039;" k="15" />
    <hkern u1="f" u2="&#x2026;" k="45" />
    <hkern u1="f" u2="&#x201d;" k="-35" />
    <hkern u1="f" u2="&#x201c;" k="-30" />
    <hkern u1="f" u2="&#x2019;" k="-35" />
    <hkern u1="f" u2="&#x2018;" k="-30" />
    <hkern u1="f" u2="&#x153;" k="10" />
    <hkern u1="f" u2="&#xe6;" k="22" />
    <hkern u1="f" u2="&#xab;" k="15" />
    <hkern u1="f" u2="&#x7d;" k="-30" />
    <hkern u1="f" u2="z" k="10" />
    <hkern u1="f" u2="x" k="10" />
    <hkern u1="f" u2="s" k="10" />
    <hkern u1="f" u2="q" k="8" />
    <hkern u1="f" u2="o" k="10" />
    <hkern u1="f" u2="g" k="8" />
    <hkern u1="f" u2="e" k="10" />
    <hkern u1="f" u2="d" k="8" />
    <hkern u1="f" u2="c" k="10" />
    <hkern u1="f" u2="a" k="22" />
    <hkern u1="f" u2="]" k="-20" />
    <hkern u1="f" u2="\" k="-30" />
    <hkern u1="f" u2="&#x3f;" k="-35" />
    <hkern u1="f" u2="&#x2f;" k="45" />
    <hkern u1="f" u2="&#x2e;" k="45" />
    <hkern u1="f" u2="&#x2c;" k="45" />
    <hkern u1="f" u2="&#x2a;" k="-30" />
    <hkern u1="f" u2="&#x29;" k="-30" />
    <hkern u1="g" u2="\" k="50" />
    <hkern u1="h" u2="y" k="30" />
    <hkern u1="h" u2="w" k="15" />
    <hkern u1="h" u2="v" k="30" />
    <hkern u1="h" u2="\" k="75" />
    <hkern u1="h" u2="&#x3f;" k="30" />
    <hkern u1="h" u2="&#x2a;" k="32" />
    <hkern u1="k" u2="&#x203a;" k="10" />
    <hkern u1="k" u2="&#x2039;" k="20" />
    <hkern u1="k" u2="&#x2014;" k="20" />
    <hkern u1="k" u2="&#x2013;" k="20" />
    <hkern u1="k" u2="&#x153;" k="30" />
    <hkern u1="k" u2="&#xe6;" k="10" />
    <hkern u1="k" u2="&#xbb;" k="10" />
    <hkern u1="k" u2="&#xab;" k="20" />
    <hkern u1="k" u2="y" k="13" />
    <hkern u1="k" u2="w" k="13" />
    <hkern u1="k" u2="v" k="13" />
    <hkern u1="k" u2="u" k="10" />
    <hkern u1="k" u2="t" k="10" />
    <hkern u1="k" u2="s" k="7" />
    <hkern u1="k" u2="q" k="25" />
    <hkern u1="k" u2="o" k="30" />
    <hkern u1="k" u2="g" k="25" />
    <hkern u1="k" u2="e" k="30" />
    <hkern u1="k" u2="d" k="25" />
    <hkern u1="k" u2="c" k="30" />
    <hkern u1="k" u2="a" k="10" />
    <hkern u1="k" u2="\" k="40" />
    <hkern u1="k" u2="&#x2d;" k="20" />
    <hkern u1="m" u2="y" k="30" />
    <hkern u1="m" u2="w" k="15" />
    <hkern u1="m" u2="v" k="30" />
    <hkern u1="m" u2="\" k="75" />
    <hkern u1="m" u2="&#x3f;" k="30" />
    <hkern u1="m" u2="&#x2a;" k="32" />
    <hkern u1="n" u2="y" k="30" />
    <hkern u1="n" u2="w" k="15" />
    <hkern u1="n" u2="v" k="30" />
    <hkern u1="n" u2="\" k="75" />
    <hkern u1="n" u2="&#x3f;" k="30" />
    <hkern u1="n" u2="&#x2a;" k="32" />
    <hkern u1="o" u2="&#xfb02;" k="1" />
    <hkern u1="o" u2="&#xfb01;" k="1" />
    <hkern u1="o" u2="&#x2248;" k="1" />
    <hkern u1="o" u2="&#x221a;" k="1" />
    <hkern u1="o" u2="&#x2122;" k="17" />
    <hkern u1="o" u2="&#x203a;" k="10" />
    <hkern u1="o" u2="&#x2026;" k="20" />
    <hkern u1="o" u2="&#x201c;" k="20" />
    <hkern u1="o" u2="&#x2018;" k="20" />
    <hkern u1="o" u2="&#x3a9;" k="1" />
    <hkern u1="o" u2="&#xbb;" k="10" />
    <hkern u1="o" u2="&#x7d;" k="15" />
    <hkern u1="o" u2="z" k="5" />
    <hkern u1="o" u2="y" k="34" />
    <hkern u1="o" u2="x" k="30" />
    <hkern u1="o" u2="w" k="30" />
    <hkern u1="o" u2="v" k="37" />
    <hkern u1="o" u2="t" k="3" />
    <hkern u1="o" u2="f" k="1" />
    <hkern u1="o" u2="]" k="20" />
    <hkern u1="o" u2="\" k="80" />
    <hkern u1="o" u2="&#x3f;" k="50" />
    <hkern u1="o" u2="&#x2e;" k="20" />
    <hkern u1="o" u2="&#x2c;" k="20" />
    <hkern u1="o" u2="&#x2a;" k="48" />
    <hkern u1="o" u2="&#x29;" k="30" />
    <hkern u1="p" u2="&#xfb02;" k="1" />
    <hkern u1="p" u2="&#xfb01;" k="1" />
    <hkern u1="p" u2="&#x2248;" k="1" />
    <hkern u1="p" u2="&#x221a;" k="1" />
    <hkern u1="p" u2="&#x2122;" k="7" />
    <hkern u1="p" u2="&#x203a;" k="5" />
    <hkern u1="p" u2="&#x2026;" k="10" />
    <hkern u1="p" u2="&#x201c;" k="10" />
    <hkern u1="p" u2="&#x2018;" k="10" />
    <hkern u1="p" u2="&#x3a9;" k="1" />
    <hkern u1="p" u2="&#xbb;" k="5" />
    <hkern u1="p" u2="&#x7d;" k="15" />
    <hkern u1="p" u2="z" k="10" />
    <hkern u1="p" u2="y" k="29" />
    <hkern u1="p" u2="x" k="25" />
    <hkern u1="p" u2="w" k="30" />
    <hkern u1="p" u2="v" k="32" />
    <hkern u1="p" u2="t" k="3" />
    <hkern u1="p" u2="f" k="1" />
    <hkern u1="p" u2="]" k="20" />
    <hkern u1="p" u2="\" k="70" />
    <hkern u1="p" u2="&#x3f;" k="35" />
    <hkern u1="p" u2="&#x2e;" k="10" />
    <hkern u1="p" u2="&#x2c;" k="10" />
    <hkern u1="p" u2="&#x2a;" k="43" />
    <hkern u1="p" u2="&#x29;" k="30" />
    <hkern u1="q" u2="\" k="50" />
    <hkern u1="r" u2="&#x2122;" k="-14" />
    <hkern u1="r" u2="&#x2039;" k="7" />
    <hkern u1="r" u2="&#x2026;" k="80" />
    <hkern u1="r" u2="&#x201d;" k="-35" />
    <hkern u1="r" u2="&#x201c;" k="-20" />
    <hkern u1="r" u2="&#x2019;" k="-35" />
    <hkern u1="r" u2="&#x2018;" k="-20" />
    <hkern u1="r" u2="&#x153;" k="5" />
    <hkern u1="r" u2="&#xe6;" k="15" />
    <hkern u1="r" u2="&#xab;" k="7" />
    <hkern u1="r" u2="z" k="10" />
    <hkern u1="r" u2="q" k="5" />
    <hkern u1="r" u2="o" k="5" />
    <hkern u1="r" u2="g" k="5" />
    <hkern u1="r" u2="e" k="5" />
    <hkern u1="r" u2="d" k="5" />
    <hkern u1="r" u2="c" k="5" />
    <hkern u1="r" u2="a" k="15" />
    <hkern u1="r" u2="\" k="30" />
    <hkern u1="r" u2="&#x2f;" k="65" />
    <hkern u1="r" u2="&#x2e;" k="80" />
    <hkern u1="r" u2="&#x2c;" k="80" />
    <hkern u1="r" u2="&#x2a;" k="-20" />
    <hkern u1="s" u2="&#x2039;" k="10" />
    <hkern u1="s" u2="&#x201c;" k="10" />
    <hkern u1="s" u2="&#x2018;" k="10" />
    <hkern u1="s" u2="&#xab;" k="10" />
    <hkern u1="s" u2="&#x7d;" k="10" />
    <hkern u1="s" u2="z" k="10" />
    <hkern u1="s" u2="y" k="10" />
    <hkern u1="s" u2="x" k="25" />
    <hkern u1="s" u2="w" k="18" />
    <hkern u1="s" u2="v" k="20" />
    <hkern u1="s" u2="t" k="10" />
    <hkern u1="s" u2="s" k="10" />
    <hkern u1="s" u2="]" k="15" />
    <hkern u1="s" u2="\" k="75" />
    <hkern u1="s" u2="&#x3f;" k="35" />
    <hkern u1="s" u2="&#x29;" k="20" />
    <hkern u1="t" u2="&#x2039;" k="10" />
    <hkern u1="t" u2="&#x201d;" k="-10" />
    <hkern u1="t" u2="&#x2019;" k="-10" />
    <hkern u1="t" u2="&#x153;" k="10" />
    <hkern u1="t" u2="&#xab;" k="10" />
    <hkern u1="t" u2="q" k="5" />
    <hkern u1="t" u2="o" k="10" />
    <hkern u1="t" u2="g" k="5" />
    <hkern u1="t" u2="e" k="10" />
    <hkern u1="t" u2="d" k="5" />
    <hkern u1="t" u2="c" k="10" />
    <hkern u1="t" u2="\" k="40" />
    <hkern u1="u" u2="\" k="50" />
    <hkern u1="v" u2="&#x203a;" k="15" />
    <hkern u1="v" u2="&#x2039;" k="30" />
    <hkern u1="v" u2="&#x2026;" k="85" />
    <hkern u1="v" u2="&#x2014;" k="15" />
    <hkern u1="v" u2="&#x2013;" k="15" />
    <hkern u1="v" u2="&#x153;" k="20" />
    <hkern u1="v" u2="&#xe6;" k="21" />
    <hkern u1="v" u2="&#xbb;" k="15" />
    <hkern u1="v" u2="&#xab;" k="30" />
    <hkern u1="v" u2="&#x7d;" k="10" />
    <hkern u1="v" u2="z" k="10" />
    <hkern u1="v" u2="y" k="15" />
    <hkern u1="v" u2="x" k="10" />
    <hkern u1="v" u2="w" k="15" />
    <hkern u1="v" u2="v" k="15" />
    <hkern u1="v" u2="s" k="15" />
    <hkern u1="v" u2="q" k="15" />
    <hkern u1="v" u2="o" k="20" />
    <hkern u1="v" u2="g" k="15" />
    <hkern u1="v" u2="e" k="20" />
    <hkern u1="v" u2="d" k="15" />
    <hkern u1="v" u2="c" k="20" />
    <hkern u1="v" u2="a" k="21" />
    <hkern u1="v" u2="]" k="20" />
    <hkern u1="v" u2="\" k="50" />
    <hkern u1="v" u2="&#x3f;" k="10" />
    <hkern u1="v" u2="&#x2f;" k="70" />
    <hkern u1="v" u2="&#x2e;" k="85" />
    <hkern u1="v" u2="&#x2d;" k="15" />
    <hkern u1="v" u2="&#x2c;" k="85" />
    <hkern u1="w" u2="&#x203a;" k="10" />
    <hkern u1="w" u2="&#x2039;" k="20" />
    <hkern u1="w" u2="&#x2026;" k="70" />
    <hkern u1="w" u2="&#x2014;" k="10" />
    <hkern u1="w" u2="&#x2013;" k="10" />
    <hkern u1="w" u2="&#x153;" k="15" />
    <hkern u1="w" u2="&#xe6;" k="17" />
    <hkern u1="w" u2="&#xbb;" k="10" />
    <hkern u1="w" u2="&#xab;" k="20" />
    <hkern u1="w" u2="&#x7d;" k="10" />
    <hkern u1="w" u2="z" k="5" />
    <hkern u1="w" u2="y" k="10" />
    <hkern u1="w" u2="x" k="10" />
    <hkern u1="w" u2="w" k="10" />
    <hkern u1="w" u2="v" k="15" />
    <hkern u1="w" u2="s" k="15" />
    <hkern u1="w" u2="q" k="11" />
    <hkern u1="w" u2="o" k="15" />
    <hkern u1="w" u2="g" k="11" />
    <hkern u1="w" u2="e" k="15" />
    <hkern u1="w" u2="d" k="11" />
    <hkern u1="w" u2="c" k="15" />
    <hkern u1="w" u2="a" k="17" />
    <hkern u1="w" u2="]" k="20" />
    <hkern u1="w" u2="\" k="50" />
    <hkern u1="w" u2="&#x3f;" k="10" />
    <hkern u1="w" u2="&#x2f;" k="60" />
    <hkern u1="w" u2="&#x2e;" k="70" />
    <hkern u1="w" u2="&#x2d;" k="10" />
    <hkern u1="w" u2="&#x2c;" k="70" />
    <hkern u1="x" u2="&#x203a;" k="15" />
    <hkern u1="x" u2="&#x2039;" k="45" />
    <hkern u1="x" u2="&#x2014;" k="30" />
    <hkern u1="x" u2="&#x2013;" k="30" />
    <hkern u1="x" u2="&#x153;" k="31" />
    <hkern u1="x" u2="&#xe6;" k="15" />
    <hkern u1="x" u2="&#xbb;" k="15" />
    <hkern u1="x" u2="&#xab;" k="45" />
    <hkern u1="x" u2="&#x7d;" k="10" />
    <hkern u1="x" u2="y" k="17" />
    <hkern u1="x" u2="w" k="13" />
    <hkern u1="x" u2="v" k="17" />
    <hkern u1="x" u2="s" k="17" />
    <hkern u1="x" u2="q" k="27" />
    <hkern u1="x" u2="o" k="31" />
    <hkern u1="x" u2="g" k="27" />
    <hkern u1="x" u2="e" k="31" />
    <hkern u1="x" u2="d" k="27" />
    <hkern u1="x" u2="c" k="31" />
    <hkern u1="x" u2="a" k="15" />
    <hkern u1="x" u2="]" k="10" />
    <hkern u1="x" u2="\" k="50" />
    <hkern u1="x" u2="&#x3f;" k="15" />
    <hkern u1="x" u2="&#x2d;" k="30" />
    <hkern u1="y" u2="&#x203a;" k="15" />
    <hkern u1="y" u2="&#x2039;" k="30" />
    <hkern u1="y" u2="&#x2026;" k="85" />
    <hkern u1="y" u2="&#x2014;" k="15" />
    <hkern u1="y" u2="&#x2013;" k="15" />
    <hkern u1="y" u2="&#x153;" k="20" />
    <hkern u1="y" u2="&#xe6;" k="21" />
    <hkern u1="y" u2="&#xbb;" k="15" />
    <hkern u1="y" u2="&#xab;" k="30" />
    <hkern u1="y" u2="&#x7d;" k="10" />
    <hkern u1="y" u2="z" k="5" />
    <hkern u1="y" u2="y" k="13" />
    <hkern u1="y" u2="x" k="10" />
    <hkern u1="y" u2="w" k="13" />
    <hkern u1="y" u2="v" k="18" />
    <hkern u1="y" u2="s" k="20" />
    <hkern u1="y" u2="q" k="15" />
    <hkern u1="y" u2="o" k="20" />
    <hkern u1="y" u2="g" k="15" />
    <hkern u1="y" u2="e" k="20" />
    <hkern u1="y" u2="d" k="15" />
    <hkern u1="y" u2="c" k="20" />
    <hkern u1="y" u2="a" k="21" />
    <hkern u1="y" u2="]" k="20" />
    <hkern u1="y" u2="\" k="50" />
    <hkern u1="y" u2="&#x3f;" k="10" />
    <hkern u1="y" u2="&#x2f;" k="70" />
    <hkern u1="y" u2="&#x2e;" k="85" />
    <hkern u1="y" u2="&#x2d;" k="15" />
    <hkern u1="y" u2="&#x2c;" k="85" />
    <hkern u1="z" u2="&#x2039;" k="15" />
    <hkern u1="z" u2="&#x153;" k="11" />
    <hkern u1="z" u2="&#xab;" k="15" />
    <hkern u1="z" u2="s" k="1" />
    <hkern u1="z" u2="q" k="7" />
    <hkern u1="z" u2="o" k="11" />
    <hkern u1="z" u2="g" k="7" />
    <hkern u1="z" u2="e" k="11" />
    <hkern u1="z" u2="d" k="7" />
    <hkern u1="z" u2="c" k="11" />
    <hkern u1="z" u2="\" k="45" />
    <hkern u1="&#x7b;" u2="&#x153;" k="15" />
    <hkern u1="&#x7b;" u2="&#x152;" k="10" />
    <hkern u1="&#x7b;" u2="&#xb5;" k="10" />
    <hkern u1="&#x7b;" u2="z" k="10" />
    <hkern u1="&#x7b;" u2="y" k="10" />
    <hkern u1="&#x7b;" u2="x" k="10" />
    <hkern u1="&#x7b;" u2="w" k="10" />
    <hkern u1="&#x7b;" u2="v" k="10" />
    <hkern u1="&#x7b;" u2="s" k="10" />
    <hkern u1="&#x7b;" u2="q" k="15" />
    <hkern u1="&#x7b;" u2="o" k="15" />
    <hkern u1="&#x7b;" u2="j" k="-35" />
    <hkern u1="&#x7b;" u2="g" k="10" />
    <hkern u1="&#x7b;" u2="e" k="15" />
    <hkern u1="&#x7b;" u2="d" k="15" />
    <hkern u1="&#x7b;" u2="c" k="15" />
    <hkern u1="&#x7b;" u2="Q" k="10" />
    <hkern u1="&#x7b;" u2="O" k="10" />
    <hkern u1="&#x7b;" u2="J" k="15" />
    <hkern u1="&#x7b;" u2="G" k="10" />
    <hkern u1="&#x7b;" u2="C" k="10" />
    <hkern u1="&#xa3;" u2="&#x34;" k="15" />
    <hkern u1="&#xa4;" u2="&#x31;" k="-20" />
    <hkern u1="&#xa5;" u2="&#x34;" k="20" />
    <hkern u1="&#xa7;" u2="&#x37;" k="15" />
    <hkern u1="&#xab;" u2="&#x153;" k="10" />
    <hkern u1="&#xab;" u2="y" k="15" />
    <hkern u1="&#xab;" u2="x" k="15" />
    <hkern u1="&#xab;" u2="w" k="10" />
    <hkern u1="&#xab;" u2="v" k="15" />
    <hkern u1="&#xab;" u2="q" k="5" />
    <hkern u1="&#xab;" u2="o" k="10" />
    <hkern u1="&#xab;" u2="g" k="5" />
    <hkern u1="&#xab;" u2="e" k="10" />
    <hkern u1="&#xab;" u2="d" k="5" />
    <hkern u1="&#xab;" u2="c" k="10" />
    <hkern u1="&#xab;" u2="Y" k="75" />
    <hkern u1="&#xab;" u2="X" k="20" />
    <hkern u1="&#xab;" u2="W" k="35" />
    <hkern u1="&#xab;" u2="V" k="40" />
    <hkern u1="&#xab;" u2="T" k="63" />
    <hkern u1="&#xb1;" u2="&#x2215;" k="-30" />
    <hkern u1="&#xb1;" u2="&#x37;" k="11" />
    <hkern u1="&#xb5;" u2="&#x2026;" k="40" />
    <hkern u1="&#xb5;" u2="&#xc6;" k="10" />
    <hkern u1="&#xb5;" u2="&#x7d;" k="7" />
    <hkern u1="&#xb5;" u2="y" k="7" />
    <hkern u1="&#xb5;" u2="x" k="5" />
    <hkern u1="&#xb5;" u2="v" k="7" />
    <hkern u1="&#xb5;" u2="]" k="7" />
    <hkern u1="&#xb5;" u2="\" k="40" />
    <hkern u1="&#xb5;" u2="Z" k="14" />
    <hkern u1="&#xb5;" u2="Y" k="60" />
    <hkern u1="&#xb5;" u2="X" k="17" />
    <hkern u1="&#xb5;" u2="W" k="48" />
    <hkern u1="&#xb5;" u2="V" k="53" />
    <hkern u1="&#xb5;" u2="T" k="63" />
    <hkern u1="&#xb5;" u2="J" k="7" />
    <hkern u1="&#xb5;" u2="A" k="10" />
    <hkern u1="&#xb5;" u2="&#x3f;" k="20" />
    <hkern u1="&#xb5;" u2="&#x2f;" k="28" />
    <hkern u1="&#xb5;" u2="&#x2e;" k="40" />
    <hkern u1="&#xb5;" u2="&#x2c;" k="40" />
    <hkern u1="&#xb5;" u2="&#x2a;" k="28" />
    <hkern u1="&#xb5;" u2="&#x29;" k="7" />
    <hkern u1="&#xbb;" u2="&#xfb02;" k="10" />
    <hkern u1="&#xbb;" u2="&#xfb01;" k="10" />
    <hkern u1="&#xbb;" u2="&#x2248;" k="10" />
    <hkern u1="&#xbb;" u2="&#x221a;" k="10" />
    <hkern u1="&#xbb;" u2="&#x3a9;" k="10" />
    <hkern u1="&#xbb;" u2="&#xc6;" k="20" />
    <hkern u1="&#xbb;" u2="z" k="20" />
    <hkern u1="&#xbb;" u2="y" k="30" />
    <hkern u1="&#xbb;" u2="x" k="45" />
    <hkern u1="&#xbb;" u2="w" k="20" />
    <hkern u1="&#xbb;" u2="v" k="30" />
    <hkern u1="&#xbb;" u2="t" k="10" />
    <hkern u1="&#xbb;" u2="s" k="10" />
    <hkern u1="&#xbb;" u2="f" k="10" />
    <hkern u1="&#xbb;" u2="Z" k="15" />
    <hkern u1="&#xbb;" u2="Y" k="107" />
    <hkern u1="&#xbb;" u2="X" k="50" />
    <hkern u1="&#xbb;" u2="W" k="50" />
    <hkern u1="&#xbb;" u2="V" k="60" />
    <hkern u1="&#xbb;" u2="T" k="90" />
    <hkern u1="&#xbb;" u2="S" k="10" />
    <hkern u1="&#xbb;" u2="A" k="20" />
    <hkern u1="&#xbf;" u2="&#xfb02;" k="10" />
    <hkern u1="&#xbf;" u2="&#xfb01;" k="10" />
    <hkern u1="&#xbf;" u2="&#x2248;" k="10" />
    <hkern u1="&#xbf;" u2="&#x221a;" k="10" />
    <hkern u1="&#xbf;" u2="&#x3a9;" k="10" />
    <hkern u1="&#xbf;" u2="&#x152;" k="20" />
    <hkern u1="&#xbf;" u2="&#xe6;" k="-10" />
    <hkern u1="&#xbf;" u2="&#xb5;" k="20" />
    <hkern u1="&#xbf;" u2="y" k="30" />
    <hkern u1="&#xbf;" u2="w" k="30" />
    <hkern u1="&#xbf;" u2="v" k="40" />
    <hkern u1="&#xbf;" u2="t" k="15" />
    <hkern u1="&#xbf;" u2="f" k="10" />
    <hkern u1="&#xbf;" u2="a" k="-10" />
    <hkern u1="&#xbf;" u2="Y" k="70" />
    <hkern u1="&#xbf;" u2="X" k="10" />
    <hkern u1="&#xbf;" u2="W" k="50" />
    <hkern u1="&#xbf;" u2="V" k="60" />
    <hkern u1="&#xbf;" u2="U" k="15" />
    <hkern u1="&#xbf;" u2="T" k="60" />
    <hkern u1="&#xbf;" u2="Q" k="20" />
    <hkern u1="&#xbf;" u2="O" k="20" />
    <hkern u1="&#xbf;" u2="G" k="20" />
    <hkern u1="&#xbf;" u2="C" k="20" />
    <hkern u1="&#xc6;" u2="&#x153;" k="10" />
    <hkern u1="&#xc6;" u2="y" k="10" />
    <hkern u1="&#xc6;" u2="w" k="10" />
    <hkern u1="&#xc6;" u2="v" k="10" />
    <hkern u1="&#xc6;" u2="o" k="10" />
    <hkern u1="&#xc6;" u2="e" k="10" />
    <hkern u1="&#xc6;" u2="d" k="10" />
    <hkern u1="&#xc6;" u2="c" k="10" />
    <hkern u1="&#xdf;" u2="&#x2039;" k="10" />
    <hkern u1="&#xdf;" u2="&#x201c;" k="10" />
    <hkern u1="&#xdf;" u2="&#x2018;" k="10" />
    <hkern u1="&#xdf;" u2="&#xab;" k="10" />
    <hkern u1="&#xdf;" u2="&#x7d;" k="10" />
    <hkern u1="&#xdf;" u2="z" k="10" />
    <hkern u1="&#xdf;" u2="y" k="10" />
    <hkern u1="&#xdf;" u2="x" k="25" />
    <hkern u1="&#xdf;" u2="w" k="18" />
    <hkern u1="&#xdf;" u2="v" k="20" />
    <hkern u1="&#xdf;" u2="t" k="10" />
    <hkern u1="&#xdf;" u2="s" k="10" />
    <hkern u1="&#xdf;" u2="]" k="15" />
    <hkern u1="&#xdf;" u2="\" k="75" />
    <hkern u1="&#xdf;" u2="&#x3f;" k="35" />
    <hkern u1="&#xdf;" u2="&#x29;" k="20" />
    <hkern u1="&#xe6;" u2="&#x2122;" k="7" />
    <hkern u1="&#xe6;" u2="&#x2026;" k="10" />
    <hkern u1="&#xe6;" u2="&#x7d;" k="10" />
    <hkern u1="&#xe6;" u2="z" k="5" />
    <hkern u1="&#xe6;" u2="y" k="20" />
    <hkern u1="&#xe6;" u2="x" k="30" />
    <hkern u1="&#xe6;" u2="w" k="23" />
    <hkern u1="&#xe6;" u2="v" k="23" />
    <hkern u1="&#xe6;" u2="]" k="20" />
    <hkern u1="&#xe6;" u2="\" k="80" />
    <hkern u1="&#xe6;" u2="&#x3f;" k="40" />
    <hkern u1="&#xe6;" u2="&#x2e;" k="10" />
    <hkern u1="&#xe6;" u2="&#x2c;" k="10" />
    <hkern u1="&#xe6;" u2="&#x2a;" k="30" />
    <hkern u1="&#xe6;" u2="&#x29;" k="30" />
    <hkern u1="&#x152;" u2="&#x153;" k="10" />
    <hkern u1="&#x152;" u2="y" k="10" />
    <hkern u1="&#x152;" u2="w" k="10" />
    <hkern u1="&#x152;" u2="v" k="10" />
    <hkern u1="&#x152;" u2="o" k="10" />
    <hkern u1="&#x152;" u2="e" k="10" />
    <hkern u1="&#x152;" u2="d" k="10" />
    <hkern u1="&#x152;" u2="c" k="10" />
    <hkern u1="&#x153;" u2="&#x2122;" k="7" />
    <hkern u1="&#x153;" u2="&#x2026;" k="10" />
    <hkern u1="&#x153;" u2="&#x7d;" k="10" />
    <hkern u1="&#x153;" u2="z" k="5" />
    <hkern u1="&#x153;" u2="y" k="20" />
    <hkern u1="&#x153;" u2="x" k="30" />
    <hkern u1="&#x153;" u2="w" k="23" />
    <hkern u1="&#x153;" u2="v" k="23" />
    <hkern u1="&#x153;" u2="]" k="20" />
    <hkern u1="&#x153;" u2="\" k="80" />
    <hkern u1="&#x153;" u2="&#x3f;" k="40" />
    <hkern u1="&#x153;" u2="&#x2e;" k="10" />
    <hkern u1="&#x153;" u2="&#x2c;" k="10" />
    <hkern u1="&#x153;" u2="&#x2a;" k="30" />
    <hkern u1="&#x153;" u2="&#x29;" k="30" />
    <hkern u1="&#x192;" u2="&#x35;" k="10" />
    <hkern u1="&#x192;" u2="&#x34;" k="30" />
    <hkern u1="&#x192;" u2="&#x31;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x2122;" k="-45" />
    <hkern u1="&#x3a9;" u2="&#x2039;" k="15" />
    <hkern u1="&#x3a9;" u2="&#x2026;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x201d;" k="-35" />
    <hkern u1="&#x3a9;" u2="&#x201c;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x2019;" k="-35" />
    <hkern u1="&#x3a9;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x153;" k="10" />
    <hkern u1="&#x3a9;" u2="&#xe6;" k="22" />
    <hkern u1="&#x3a9;" u2="&#xab;" k="15" />
    <hkern u1="&#x3a9;" u2="&#x7d;" k="-30" />
    <hkern u1="&#x3a9;" u2="z" k="10" />
    <hkern u1="&#x3a9;" u2="x" k="10" />
    <hkern u1="&#x3a9;" u2="s" k="10" />
    <hkern u1="&#x3a9;" u2="q" k="8" />
    <hkern u1="&#x3a9;" u2="o" k="10" />
    <hkern u1="&#x3a9;" u2="g" k="8" />
    <hkern u1="&#x3a9;" u2="e" k="10" />
    <hkern u1="&#x3a9;" u2="d" k="8" />
    <hkern u1="&#x3a9;" u2="c" k="10" />
    <hkern u1="&#x3a9;" u2="a" k="22" />
    <hkern u1="&#x3a9;" u2="]" k="-20" />
    <hkern u1="&#x3a9;" u2="\" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x3f;" k="-35" />
    <hkern u1="&#x3a9;" u2="&#x2f;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x2e;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x2c;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x2a;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x29;" k="-30" />
    <hkern u1="&#x2013;" u2="&#xc6;" k="40" />
    <hkern u1="&#x2013;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2013;" u2="z" k="10" />
    <hkern u1="&#x2013;" u2="y" k="15" />
    <hkern u1="&#x2013;" u2="x" k="30" />
    <hkern u1="&#x2013;" u2="w" k="10" />
    <hkern u1="&#x2013;" u2="v" k="15" />
    <hkern u1="&#x2013;" u2="Z" k="30" />
    <hkern u1="&#x2013;" u2="Y" k="83" />
    <hkern u1="&#x2013;" u2="X" k="50" />
    <hkern u1="&#x2013;" u2="W" k="35" />
    <hkern u1="&#x2013;" u2="V" k="40" />
    <hkern u1="&#x2013;" u2="T" k="83" />
    <hkern u1="&#x2013;" u2="A" k="40" />
    <hkern u1="&#x2013;" u2="&#x37;" k="40" />
    <hkern u1="&#x2013;" u2="&#x33;" k="10" />
    <hkern u1="&#x2013;" u2="&#x31;" k="30" />
    <hkern u1="&#x2014;" u2="&#xc6;" k="40" />
    <hkern u1="&#x2014;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2014;" u2="z" k="10" />
    <hkern u1="&#x2014;" u2="y" k="15" />
    <hkern u1="&#x2014;" u2="x" k="30" />
    <hkern u1="&#x2014;" u2="w" k="10" />
    <hkern u1="&#x2014;" u2="v" k="15" />
    <hkern u1="&#x2014;" u2="Z" k="30" />
    <hkern u1="&#x2014;" u2="Y" k="83" />
    <hkern u1="&#x2014;" u2="X" k="50" />
    <hkern u1="&#x2014;" u2="W" k="35" />
    <hkern u1="&#x2014;" u2="V" k="40" />
    <hkern u1="&#x2014;" u2="T" k="83" />
    <hkern u1="&#x2014;" u2="A" k="40" />
    <hkern u1="&#x2014;" u2="&#x37;" k="40" />
    <hkern u1="&#x2014;" u2="&#x33;" k="10" />
    <hkern u1="&#x2014;" u2="&#x31;" k="30" />
    <hkern u1="&#x2018;" u2="&#x153;" k="8" />
    <hkern u1="&#x2018;" u2="&#xe6;" k="3" />
    <hkern u1="&#x2018;" u2="&#xc6;" k="80" />
    <hkern u1="&#x2018;" u2="&#xbf;" k="35" />
    <hkern u1="&#x2018;" u2="z" k="10" />
    <hkern u1="&#x2018;" u2="t" k="-15" />
    <hkern u1="&#x2018;" u2="s" k="10" />
    <hkern u1="&#x2018;" u2="q" k="8" />
    <hkern u1="&#x2018;" u2="o" k="8" />
    <hkern u1="&#x2018;" u2="g" k="8" />
    <hkern u1="&#x2018;" u2="e" k="8" />
    <hkern u1="&#x2018;" u2="d" k="8" />
    <hkern u1="&#x2018;" u2="c" k="8" />
    <hkern u1="&#x2018;" u2="a" k="3" />
    <hkern u1="&#x2018;" u2="J" k="80" />
    <hkern u1="&#x2018;" u2="A" k="80" />
    <hkern u1="&#x2019;" u2="&#x153;" k="38" />
    <hkern u1="&#x2019;" u2="&#xe6;" k="20" />
    <hkern u1="&#x2019;" u2="&#xc6;" k="100" />
    <hkern u1="&#x2019;" u2="s" k="20" />
    <hkern u1="&#x2019;" u2="q" k="33" />
    <hkern u1="&#x2019;" u2="o" k="38" />
    <hkern u1="&#x2019;" u2="g" k="33" />
    <hkern u1="&#x2019;" u2="e" k="38" />
    <hkern u1="&#x2019;" u2="d" k="33" />
    <hkern u1="&#x2019;" u2="c" k="38" />
    <hkern u1="&#x2019;" u2="a" k="20" />
    <hkern u1="&#x2019;" u2="J" k="100" />
    <hkern u1="&#x2019;" u2="A" k="100" />
    <hkern u1="&#x201a;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x201a;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x201a;" u2="&#x2248;" k="15" />
    <hkern u1="&#x201a;" u2="&#x221a;" k="15" />
    <hkern u1="&#x201a;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x201a;" u2="&#x153;" k="55" />
    <hkern u1="&#x201a;" u2="&#x152;" k="80" />
    <hkern u1="&#x201a;" u2="&#xe6;" k="23" />
    <hkern u1="&#x201a;" u2="&#xb5;" k="80" />
    <hkern u1="&#x201a;" u2="y" k="65" />
    <hkern u1="&#x201a;" u2="w" k="75" />
    <hkern u1="&#x201a;" u2="v" k="94" />
    <hkern u1="&#x201a;" u2="t" k="25" />
    <hkern u1="&#x201a;" u2="q" k="55" />
    <hkern u1="&#x201a;" u2="o" k="55" />
    <hkern u1="&#x201a;" u2="g" k="30" />
    <hkern u1="&#x201a;" u2="f" k="15" />
    <hkern u1="&#x201a;" u2="e" k="20" />
    <hkern u1="&#x201a;" u2="d" k="55" />
    <hkern u1="&#x201a;" u2="c" k="34" />
    <hkern u1="&#x201a;" u2="a" k="23" />
    <hkern u1="&#x201a;" u2="Y" k="126" />
    <hkern u1="&#x201a;" u2="W" k="125" />
    <hkern u1="&#x201a;" u2="V" k="145" />
    <hkern u1="&#x201a;" u2="U" k="50" />
    <hkern u1="&#x201a;" u2="T" k="98" />
    <hkern u1="&#x201a;" u2="Q" k="80" />
    <hkern u1="&#x201a;" u2="O" k="80" />
    <hkern u1="&#x201a;" u2="G" k="80" />
    <hkern u1="&#x201a;" u2="C" k="80" />
    <hkern u1="&#x201c;" u2="&#x153;" k="8" />
    <hkern u1="&#x201c;" u2="&#xe6;" k="3" />
    <hkern u1="&#x201c;" u2="&#xc6;" k="80" />
    <hkern u1="&#x201c;" u2="&#xbf;" k="35" />
    <hkern u1="&#x201c;" u2="z" k="10" />
    <hkern u1="&#x201c;" u2="t" k="-15" />
    <hkern u1="&#x201c;" u2="s" k="10" />
    <hkern u1="&#x201c;" u2="q" k="8" />
    <hkern u1="&#x201c;" u2="o" k="8" />
    <hkern u1="&#x201c;" u2="g" k="8" />
    <hkern u1="&#x201c;" u2="e" k="8" />
    <hkern u1="&#x201c;" u2="d" k="8" />
    <hkern u1="&#x201c;" u2="c" k="8" />
    <hkern u1="&#x201c;" u2="a" k="3" />
    <hkern u1="&#x201c;" u2="J" k="80" />
    <hkern u1="&#x201c;" u2="A" k="80" />
    <hkern u1="&#x201e;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x201e;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x201e;" u2="&#x2248;" k="15" />
    <hkern u1="&#x201e;" u2="&#x221a;" k="15" />
    <hkern u1="&#x201e;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x201e;" u2="&#x153;" k="55" />
    <hkern u1="&#x201e;" u2="&#x152;" k="80" />
    <hkern u1="&#x201e;" u2="&#xe6;" k="23" />
    <hkern u1="&#x201e;" u2="&#xb5;" k="80" />
    <hkern u1="&#x201e;" u2="y" k="65" />
    <hkern u1="&#x201e;" u2="w" k="75" />
    <hkern u1="&#x201e;" u2="v" k="94" />
    <hkern u1="&#x201e;" u2="t" k="25" />
    <hkern u1="&#x201e;" u2="q" k="55" />
    <hkern u1="&#x201e;" u2="o" k="55" />
    <hkern u1="&#x201e;" u2="g" k="30" />
    <hkern u1="&#x201e;" u2="f" k="15" />
    <hkern u1="&#x201e;" u2="e" k="20" />
    <hkern u1="&#x201e;" u2="d" k="55" />
    <hkern u1="&#x201e;" u2="c" k="34" />
    <hkern u1="&#x201e;" u2="a" k="23" />
    <hkern u1="&#x201e;" u2="Y" k="126" />
    <hkern u1="&#x201e;" u2="W" k="125" />
    <hkern u1="&#x201e;" u2="V" k="145" />
    <hkern u1="&#x201e;" u2="U" k="50" />
    <hkern u1="&#x201e;" u2="T" k="98" />
    <hkern u1="&#x201e;" u2="Q" k="80" />
    <hkern u1="&#x201e;" u2="O" k="80" />
    <hkern u1="&#x201e;" u2="G" k="80" />
    <hkern u1="&#x201e;" u2="C" k="80" />
    <hkern u1="&#x2026;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x2026;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x2026;" u2="&#x2248;" k="15" />
    <hkern u1="&#x2026;" u2="&#x221a;" k="15" />
    <hkern u1="&#x2026;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x2026;" u2="&#x153;" k="55" />
    <hkern u1="&#x2026;" u2="&#x152;" k="80" />
    <hkern u1="&#x2026;" u2="&#xe6;" k="23" />
    <hkern u1="&#x2026;" u2="&#xb5;" k="80" />
    <hkern u1="&#x2026;" u2="y" k="65" />
    <hkern u1="&#x2026;" u2="w" k="75" />
    <hkern u1="&#x2026;" u2="v" k="94" />
    <hkern u1="&#x2026;" u2="t" k="25" />
    <hkern u1="&#x2026;" u2="q" k="55" />
    <hkern u1="&#x2026;" u2="o" k="55" />
    <hkern u1="&#x2026;" u2="g" k="30" />
    <hkern u1="&#x2026;" u2="f" k="15" />
    <hkern u1="&#x2026;" u2="e" k="20" />
    <hkern u1="&#x2026;" u2="d" k="55" />
    <hkern u1="&#x2026;" u2="c" k="34" />
    <hkern u1="&#x2026;" u2="a" k="23" />
    <hkern u1="&#x2026;" u2="Y" k="126" />
    <hkern u1="&#x2026;" u2="W" k="125" />
    <hkern u1="&#x2026;" u2="V" k="145" />
    <hkern u1="&#x2026;" u2="U" k="50" />
    <hkern u1="&#x2026;" u2="T" k="98" />
    <hkern u1="&#x2026;" u2="Q" k="80" />
    <hkern u1="&#x2026;" u2="O" k="80" />
    <hkern u1="&#x2026;" u2="G" k="80" />
    <hkern u1="&#x2026;" u2="C" k="80" />
    <hkern u1="&#x2039;" u2="&#x153;" k="10" />
    <hkern u1="&#x2039;" u2="y" k="15" />
    <hkern u1="&#x2039;" u2="x" k="15" />
    <hkern u1="&#x2039;" u2="w" k="10" />
    <hkern u1="&#x2039;" u2="v" k="15" />
    <hkern u1="&#x2039;" u2="q" k="5" />
    <hkern u1="&#x2039;" u2="o" k="10" />
    <hkern u1="&#x2039;" u2="g" k="5" />
    <hkern u1="&#x2039;" u2="e" k="10" />
    <hkern u1="&#x2039;" u2="d" k="5" />
    <hkern u1="&#x2039;" u2="c" k="10" />
    <hkern u1="&#x2039;" u2="Y" k="75" />
    <hkern u1="&#x2039;" u2="X" k="20" />
    <hkern u1="&#x2039;" u2="W" k="35" />
    <hkern u1="&#x2039;" u2="V" k="40" />
    <hkern u1="&#x2039;" u2="T" k="63" />
    <hkern u1="&#x203a;" u2="&#xfb02;" k="10" />
    <hkern u1="&#x203a;" u2="&#xfb01;" k="10" />
    <hkern u1="&#x203a;" u2="&#x2248;" k="10" />
    <hkern u1="&#x203a;" u2="&#x221a;" k="10" />
    <hkern u1="&#x203a;" u2="&#x3a9;" k="10" />
    <hkern u1="&#x203a;" u2="&#xc6;" k="20" />
    <hkern u1="&#x203a;" u2="z" k="20" />
    <hkern u1="&#x203a;" u2="y" k="30" />
    <hkern u1="&#x203a;" u2="x" k="45" />
    <hkern u1="&#x203a;" u2="w" k="20" />
    <hkern u1="&#x203a;" u2="v" k="30" />
    <hkern u1="&#x203a;" u2="t" k="10" />
    <hkern u1="&#x203a;" u2="s" k="10" />
    <hkern u1="&#x203a;" u2="f" k="10" />
    <hkern u1="&#x203a;" u2="Z" k="15" />
    <hkern u1="&#x203a;" u2="Y" k="107" />
    <hkern u1="&#x203a;" u2="X" k="50" />
    <hkern u1="&#x203a;" u2="W" k="50" />
    <hkern u1="&#x203a;" u2="V" k="60" />
    <hkern u1="&#x203a;" u2="T" k="90" />
    <hkern u1="&#x203a;" u2="S" k="10" />
    <hkern u1="&#x203a;" u2="A" k="20" />
    <hkern u1="&#x2215;" u2="&#xb1;" k="-15" />
    <hkern u1="&#x2215;" u2="&#x39;" k="-40" />
    <hkern u1="&#x2215;" u2="&#x38;" k="-20" />
    <hkern u1="&#x2215;" u2="&#x37;" k="-30" />
    <hkern u1="&#x2215;" u2="&#x35;" k="-20" />
    <hkern u1="&#x2215;" u2="&#x34;" k="75" />
    <hkern u1="&#x2215;" u2="&#x33;" k="-40" />
    <hkern u1="&#x2215;" u2="&#x32;" k="-30" />
    <hkern u1="&#x2215;" u2="&#x31;" k="-60" />
  </font>
</defs></svg>
