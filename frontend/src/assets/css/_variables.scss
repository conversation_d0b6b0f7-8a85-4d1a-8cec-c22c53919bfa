//Bootstrap Variables
$enable-negative-margins: true;
$font-family-base: 'Montserrat', sans-serif;
$headings-font-family: 'DM Serif Display', sans-serif;
$white: #FFFFFF;
$black: #000000;
$body-color: #393956;
$primary: #5223FC;
$secondary: #5875F7;
$success: #0F9D58;
$danger: #FF3030;
$warning: #ec8a09;
$light: #E5EAFE;
$light-bg-subtle: #575778;
$dark: $body-color;
$dark-bg-subtle: #30304D;
$info: #47C4F2;
$input-padding-y: 8.5px;
$input-padding-x: 1rem;
$input-font-family: $font-family-base;
$input-font-size: 14px;
$input-disabled-bg: #F1F1F1;
$border-radius: 7px;
$btn-font-size: $input-font-size;
$btn-font-weight: bold;
$btn-padding-y: .532rem;
$btn-padding-x: 1.5rem;
$btn-padding-x-sm: .875rem;
$btn-border-radius-sm:$border-radius;
$btn-padding-y-lg: 0.75rem;
$btn-padding-x-lg: 2.5rem;
$btn-font-size-lg: 1rem;
$border-color: $light;
$box-shadow-sm: 0 2px 4px rgba($black, .15);
$box-shadow: 0 3px 6px rgba($black, .16);
$box-shadow-lg: 0 -3px 6px rgba($black, .10);
$card-spacer-x: 1.25rem;
$card-spacer-y: 1.25rem;
$card-border-radius: 10px;
$card-cap-bg: $white;
$card-cap-padding-y: 1.25rem;
$card-border-color: $light;
$tooltip-bg: $body-color;
$tooltip-opacity: 1;
$tooltip-border-radius: .25rem;
$tooltip-max-width: 300px;
$line-height-lg: 1.875;
$breadcrumb-font-size: $input-font-size;
$breadcrumb-item-padding-x: 10px;
$breadcrumb-margin-bottom: 0;
$breadcrumb-divider-color: $body-color;
$breadcrumb-active-color: $body-color;
$breadcrumb-divider: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='7.766' height='7.569' viewBox='0 0 7.766 7.569'><path id='Icon_awesome-arrow-right' data-name='Icon awesome-arrow-right' d='M3.3,3.154l.385-.385a.414.414,0,0,1,.588,0l3.37,3.368a.414.414,0,0,1,0,.588l-3.37,3.37a.414.414,0,0,1-.588,0L3.3,9.71a.416.416,0,0,1,.007-.595L5.4,7.125H.416A.415.415,0,0,1,0,6.709V6.155a.415.415,0,0,1,.416-.416H5.4L3.309,3.749A.413.413,0,0,1,3.3,3.154Z' transform='translate(0 -2.647)' fill='#{$breadcrumb-divider-color}' opacity='0.5'/></svg>");
$navbar-light-color: $body-color;
$navbar-light-hover-color: $primary;
$navbar-light-active-color: $primary;
$navbar-light-icon-color: $body-color;
$navbar-light-toggler-border-color: $light;
$navbar-toggler-focus-width: 1px;
$offcanvas-horizontal-width: 230px;
$btn-close-color: $body-color;
$font-weight-bolder: 800;
$h2-font-size: 36px;
$accordion-body-padding-y: 1.25rem;
$accordion-button-active-bg: $white;
$accordion-button-active-color: $body-color;
$accordion-button-focus-box-shadow: none;
$accordion-icon-width: 1.75rem;
$accordion-border-radius: .5rem;
$accordion-icon-transform: rotate(0deg);
$accordion-button-icon: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='28.002' height='28.001' viewBox='0 0 28.002 28.001'><path d='M-16745,9160a14,14,0,0,1,14-14,14,14,0,0,1,14,14,14,14,0,0,1-14,14A14.005,14.005,0,0,1-16745,9160Zm5.166-8.838a12.425,12.425,0,0,0-3.666,8.838,12.425,12.425,0,0,0,3.666,8.842,12.4,12.4,0,0,0,8.838,3.66,12.418,12.418,0,0,0,8.838-3.66,12.42,12.42,0,0,0,3.66-8.842,12.419,12.419,0,0,0-3.66-8.838,12.422,12.422,0,0,0-8.838-3.66A12.4,12.4,0,0,0-16739.836,9151.16Zm8.367,15.394v-5.526H-16737a.752.752,0,0,1-.748-.748.756.756,0,0,1,.748-.756h5.535V9154a.747.747,0,0,1,.748-.748.749.749,0,0,1,.748.748v5.526h5.525a.757.757,0,0,1,.752.756.753.753,0,0,1-.752.748h-5.525v5.526a.749.749,0,0,1-.748.748A.747.747,0,0,1-16731.469,9166.554Z' transform='translate(16745.002 -9146)' fill='%23B4BBD8'/></svg>");
$accordion-button-active-icon: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='28.002' height='27.997' viewBox='0 0 28.002 27.997'><path d='M-16745,9380a14,14,0,0,1,14-14,14,14,0,0,1,14,14,14,14,0,0,1-14,14A14,14,0,0,1-16745,9380Zm5.166-8.838a12.418,12.418,0,0,0-3.666,8.838,12.423,12.423,0,0,0,3.666,8.838,12.4,12.4,0,0,0,8.838,3.66,12.418,12.418,0,0,0,8.838-3.66,12.417,12.417,0,0,0,3.66-8.838,12.412,12.412,0,0,0-3.66-8.838,12.426,12.426,0,0,0-8.838-3.664A12.406,12.406,0,0,0-16739.836,9371.162Zm2.836,9.863a.75.75,0,0,1-.752-.748.751.751,0,0,1,.752-.752h12.553a.75.75,0,0,1,.748.752.749.749,0,0,1-.748.748Z' transform='translate(16745.002 -9366.002)' fill='%23B4BBD8'/></svg>");
$body-tertiary-bg: #F6F8FF;
$input-group-addon-bg: $light;
$secondary-text-emphasis: #976DFC;
$input-placeholder-color: rgba($body-color, .5);
$display-font-sizes: (
  1: 5rem,
  2: 4.5rem,
  3: 4rem,
  4: 3.5rem,
  5: 3rem,
  6: 45px
);


//Theme Custom Variables
$theme-body-bg: #F3F6FF;
$theme-focus-shadow: 0 0 0 0.2rem rgba($primary, .2);
$theme-sidebar-width: 230px;
$theme-sidebar-brand-height: 5em;
$theme-mobile-header-height: 3.75rem;
$theme-size-8: .5rem;
$theme-size-9: 0.5625rem;
$theme-size-10: 0.625rem;
$theme-size-11: 0.6875rem;
$theme-size-12: 0.75rem;
$theme-size-13: 0.8125rem;
$theme-size-14: 0.875rem;
$theme-size-15: 0.9375rem;
$theme-size-18: 1.125rem;


//Responsive Mixins
$below-xsm: 374px;
$below-sm: 575px;
$below-md: 767px;
$below-lg: 991px;
$below-xl: 1199px;
$below-1300: 1299px;
$below-1400: 1399px;
$below-1600: 1600px;
$below-1800: 1800px;

$maxheight600: 600px;
$maxheight400: 400px;

$above-sm: 576px;
$above-md: 768px;
$above-lg: 992px;
$above-xl: 1200px;
$above-1300: 1300px;
$above-1400: 1400px;
$above-1900: 1900px;

@mixin below-xsm {
  @media only screen and (max-width: $below-xsm) {
    @content;
  }
}

@mixin below-sm {
  @media only screen and (max-width: $below-sm) {
    @content;
  }
}

@mixin below-md {
  @media only screen and (max-width: $below-md) {
    @content;
  }
}

@mixin below-lg {
  @media only screen and (max-width: $below-lg) {
    @content;
  }
}

@mixin below-xl {
  @media only screen and (max-width: $below-xl) {
    @content;
  }
}

@mixin below-1300 {
  @media only screen and (max-width: $below-1300) {
    @content;
  }
}

@mixin below-1400 {
  @media only screen and (max-width: $below-1400) {
    @content;
  }
}

@mixin below-1600 {
  @media only screen and (max-width: $below-1600) {
    @content;
  }
}

@mixin below-1800 {
  @media only screen and (max-width: $below-1800) {
    @content;
  }
}

@mixin maxheight600 {
  @media only screen and (max-height: $maxheight600) {
    @content;
  }
}

@mixin maxheight400 {
  @media only screen and (max-height: $maxheight400) {
    @content;
  }
}

@mixin above-sm {
  @media only screen and (min-width: $above-sm) {
    @content;
  }
}

@mixin above-md {
  @media only screen and (min-width: $above-md) {
    @content;
  }
}

@mixin above-lg {
  @media only screen and (min-width: $above-lg) {
    @content;
  }
}

@mixin above-xl {
  @media only screen and (min-width: $above-xl) {
    @content;
  }
}

@mixin above-1300 {
  @media only screen and (min-width: $above-1300) {
    @content;
  }
}

@mixin above-1400 {
  @media only screen and (min-width: $above-1400) {
    @content;
  }
}

@mixin above-1900 {
  @media only screen and (min-width: $above-1900) {
    @content;
  }
}

//End of Responsive Mixins