import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ErrorMessages } from '../validators/error-messages';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { UserServiceService } from '../services/user-service.service';
import { environment } from 'src/environments/environment';
import { emailValidation, nameValidation, passwordValidator, phoneValidator, regNumberValidator } from '../validators/constant';
import { RegistrationService } from '../services/registration.service';
import { SvgIcons } from '../validators/svg-icons';
import { MessageService } from 'primeng/api';
import { Title } from '@angular/platform-browser';
import { CryptoService } from '../services/crypto.service';

@Component({
  template: `
  <form class="px-xl-3 d-flex flex-column h-100 pt-4" [formGroup]="userRegistrationForm" (ngSubmit)="onSubmit()">
    <div class="row gx-3 gx-xl-4">
      <div class="col-sm-6 mb-4">
        <div class="form-group">
          <label class="fs-14 fw-semibold mb-1 lh-sm" for="account-owner-first-name">First Name<span class="text-danger">*</span></label>
          <input pInputText id="account-owner-first-name" formControlName="firstName" />
          <div *ngIf="isFieldInvalid('firstName')" class="fs-14 lh-sm my-1 text-danger">
            <div *ngIf="userRegistrationForm.get('firstName')?.errors?.['required']">{{errorMessages.firstName.required}}</div>
            <div *ngIf="userRegistrationForm.get('firstName')?.errors?.['pattern']">{{errorMessages.firstName.pattern}}</div>
          </div>
        </div>
      </div>
      <div class="col-sm-6 mb-4">
        <div class="form-group">
          <label class="fs-14 fw-semibold mb-1 lh-sm" for="account-owner-last-name">Last Name<span class="text-danger">*</span></label>
          <input pInputText id="account-owner-last-name" formControlName="lastName" />
          <div *ngIf="isFieldInvalid('lastName')" class="fs-14 lh-sm my-1 text-danger">
            <div *ngIf="userRegistrationForm.get('lastName')?.errors?.['required']">{{errorMessages.lastName.required}}</div>
            <div *ngIf="userRegistrationForm.get('lastName')?.errors?.['pattern']">{{errorMessages.lastName.pattern}}</div>
          </div>
        </div>
      </div>
      <div class="col-lg-6 mb-4">
        <div class="form-group">
          <label class="fs-14 fw-semibold mb-1 lh-sm" for="account-owner-email">Email<span class="text-danger">*</span></label>
          <input pInputText id="account-owner-email" autocomplete="cc-name" formControlName="email" (focusout)="checkEmail()"/>
          <div *ngIf="isFieldInvalid('email')" class="fs-14 lh-sm my-1 text-danger">
            <div *ngIf="userRegistrationForm.get('email')?.errors?.['required']">{{errorMessages.email.required}}</div>
            <div *ngIf="userRegistrationForm.get('email')?.errors?.['email']">{{errorMessages.email.email}}</div>
            <div *ngIf="userRegistrationForm.get('email')?.errors?.['pattern']">{{errorMessages.email.pattern}}</div>
          </div>
        </div>
      </div>
      <div class="col-lg-6 mb-4">
        <div class="form-group">
          <label class="fs-14 fw-semibold mb-1 lh-sm" for="account-owner-phone">Phone Number<span class="text-danger">*</span></label>
          <input pInputText id="account-owner-phone" formControlName="mobileNo"/>
          <div *ngIf="isFieldInvalid('mobileNo')" class="fs-14 lh-sm my-1 text-danger">
            <div *ngIf="userRegistrationForm.get('mobileNo')?.errors?.['required']">{{errorMessages.phoneNumber.required}}</div>
            <div *ngIf="userRegistrationForm.get('mobileNo')?.errors?.['pattern']">{{errorMessages.phoneNumber.pattern}}</div>
          </div>
        </div>
      </div>
      <div class="col-lg-6 mb-4">
        <div class="form-group">
          <label class="fs-14 fw-semibold mb-1 lh-sm" for="specialization">Specialisation<span class="text-danger">*</span></label>
          <p-dropdown [options]="specialisationList" placeholder="Select a specialisation" optionLabel="name" optionValue="id" formControlName="specialization" placeholder="Select Specialisation" (onChange)="handleSpecialisationChange()"></p-dropdown>
          <div *ngIf="isFieldInvalid('specialization')" class="fs-14 lh-sm my-1 text-danger">{{errorMessages.specialization.required}}</div>
        </div>
        <div class="form-group mt-3" *ngIf="showOtherTextbox">
          <input pInputText placeholder="Enter Specialisation" formControlName="others" />
          <div *ngIf="isFieldInvalid('others')" class="fs-14 lh-sm my-1 text-danger">
            <div *ngIf="userRegistrationForm.get('others')?.errors?.['required']">{{errorMessages.others.required}}</div>
            <div *ngIf="userRegistrationForm.get('others')?.errors?.['pattern']">{{errorMessages.others.pattern}}</div>
          </div>
        </div>
      </div>
      <div class="col-lg-6 mb-4">
        <div class="form-group">
          <label class="align-items-center d-inline-flex fs-14 fw-semibold lh-sm mb-1" for="registration-gmc-number">Registration Number</label>
          <a appBsTooltip data-bs-html="true" data-bs-toggle="tooltip" data-bs-placement="top" title={{gmcNumberInfo}} class="btn btn-link text-body d-inline-block infoIcon ms-1 p-0">
            <span class="theme-info-icon" [innerHTML]="svgIcons.infoIcon | safeHtml"></span>
          </a>
          <input placeholder="GMC, NMC, PH etc." pInputText id="registration-gmc-number" formControlName="gmcNumber" />
          <div *ngIf="isFieldInvalid('gmcNumber')" class="fs-14 lh-sm my-1 text-danger">{{errorMessages.gmcNumber.pattern}}</div>
        </div>
      </div>
      <div class="col-12">
        <div class="fw-bold my-3">Create Password</div>
      </div>
      <div class="col-sm-6 mb-4">
        <div class="form-group">
          <label class="align-items-center d-inline-flex fs-14 fw-semibold lh-sm mb-1" for="password">Password<span class="text-danger">*</span>
            <a appBsTooltip data-bs-html="true" data-bs-toggle="tooltip" data-bs-placement="top" title={{passwordInfoMessage}} class="btn btn-link text-body d-inline-block infoIcon ms-1 p-0">
              <span class="theme-info-icon" [innerHTML]="svgIcons.infoIcon | safeHtml"></span>
            </a>
          </label>
          <p-password id="password" [feedback]="false" [toggleMask]="true" formControlName="password"></p-password>
          <div *ngIf="isFieldInvalid('password')" class="fs-14 lh-sm my-1 text-danger">
            <div *ngIf="userRegistrationForm.get('password')?.errors?.['required']">{{errorMessages.password.required}}</div>
            <div *ngIf="userRegistrationForm.get('password')?.errors?.['pattern']">{{errorMessages.password.pattern}}</div>
          </div>
        </div>
      </div>
      <div class="col-sm-6 mb-4">
        <div class="form-group">
          <label class="align-items-center d-inline-flex fs-14 fw-semibold lh-sm mb-1" for="confirm-password">Confirm Password<span class="text-danger">*</span>
            <a appBsTooltip data-bs-html="true" data-bs-toggle="tooltip" data-bs-placement="top" title={{passwordInfoMessage}} class="btn btn-link text-body d-inline-block infoIcon ms-1 p-0">
              <span class="theme-info-icon" [innerHTML]="svgIcons.infoIcon | safeHtml"></span>
            </a>
          </label>
          <p-password id="confirm-password" [feedback]="false" [toggleMask]="true" formControlName="confirmPassword"></p-password>
          <div *ngIf="isFieldInvalid('confirmPassword')" class="fs-14 lh-sm my-1 text-danger">
            <div *ngIf="userRegistrationForm.get('confirmPassword')?.errors?.['required']">{{errorMessages.confirmPassword.required}}</div>
            <div *ngIf="userRegistrationForm.get('confirmPassword')?.errors?.['passwordMismatch']">{{errorMessages.confirmPassword.equalTo}}</div>
          </div>
        </div>
      </div>
      <div class="col-12 mb-4">
        <div class="form-group">
          <div class="align-items-start d-flex theme-checkbox fs-14">
            <p-checkbox formControlName="terms" value="terms" styleClass="align-items-center fs-11"></p-checkbox>
            <label class="ps-2">I have agreed to all the <a [routerLink]="['/terms-and-conditions']" target="_blank" class="fw-medium lh-sm text-decoration-none">Terms and Conditions</a>, <a [routerLink]="['/privacy-policy']" target="_blank" class="fw-medium lh-sm text-decoration-none">Privacy Policy</a> & <a [routerLink]="['/dpa']" target="_blank" class="fw-medium lh-sm text-decoration-none">DPA.</a></label>         
          </div>
          <div *ngIf="isFieldInvalid('terms')" class="fs-14 lh-sm my-1 text-danger">{{errorMessages.terms.required}}</div>
        </div>
      </div>
      <div class="col-12 mb-4 pt-1">
        <div class="form-group">
        <ngx-recaptcha2 #captchaElem [siteKey]="siteKey" formControlName="recaptcha"></ngx-recaptcha2>
        <div *ngIf="isFieldInvalid('recaptcha')" class="fs-14 lh-sm my-1 text-danger">{{errorMessages.captcha.required}}</div>
        </div>
      </div>
    </div>
    <div class="text-sm-end mt-auto pb-4 pt-2 mb-1">
      <p-toast></p-toast>
      <button type="submit" [disabled]="isDisabled" class="btn btn-primary">Submit</button>
    </div>
  </form>
  <!-- Otp Popup -->
  <p-dialog styleClass="theme-modal-main theme-modal-sm theme-modal-center" header="Header" [modal]="true"
    [draggable]="false" [resizable]="false" [dismissableMask]="true" [(visible)]="visible2" [closable]="false">
    <ng-template pTemplate="header">
        <h5 class="mb-0 fw-normal">Your email has been previously registered. Please click on send OTP to verify the details.</h5>
    </ng-template>
    <div class="theme-modal-body">
      <form [formGroup]="otpForm" (ngSubmit)="verifyOtp()">
        <label class="fs-14 fw-semibold mb-1 lh-sm" for="otp" #otpInput>Enter OTP<span class="text-danger">*</span></label>
        <input pInputText id="otp" formControlName="otp">
        <div *ngIf="isFieldInvalid2('otp')" class="fs-14 lh-sm my-1 text-danger">
            <div *ngIf="otpForm.get('otp')?.errors?.['required']">{{errorMessages2.otp.required}}</div>
            <div *ngIf="otpForm.get('otp')?.errors?.['pattern']">You can enter only 5 digits</div>
        </div>
          <div class="gap-4 hstack justify-content-center pt-4">
              <button type="button" (click)="sendOtp()" class="btn btn-primary">Send OTP</button>
              <button type="submit" class="btn btn-primary">Verify OTP</button>
          </div>
      </form>
    </div>
</p-dialog>
  <!-- Success Popup -->
  <p-dialog 
        styleClass="theme-modal-main theme-modal-lg theme-modal-center" 
        header="Header" 
        [modal]="true" 
        [draggable]="false"
        [resizable]="false" 
        [dismissableMask]="true" 
        [(visible)]="visible" 
        [closable]="false"
        >
        <ng-template pTemplate="header">
            <h3 class="mb-0 fw-normal">Successful!</h3>
        </ng-template>
        <div class="theme-modal-body">
            <p class="fw-medium mb-4 pt-3" *ngIf="!otpVerificationSuccess || (otpVerificationSuccess && !isEmailVerified)">Your account has been successfully registered. Please check your mail.</p>
            <p class="fw-medium mb-4 pt-3" *ngIf="otpVerificationSuccess && isEmailVerified">Your account has been successfully registered and already verified. Please log in with your credintials</p>
        </div>
        <ng-template pTemplate="footer">
            <div class="gap-4 hstack justify-content-center">
                <button (click)="visible=false" class="btn btn-primary">Ok</button>
            </div>
        </ng-template>
    </p-dialog>
    <!-- Success Popup -->
    <p-dialog 
        styleClass="theme-modal-main theme-modal-lg theme-modal-center" 
        header="" 
        [modal]="true" 
        [draggable]="false"
        [resizable]="false" 
        [dismissableMask]="true" 
        [(visible)]="isAlreadyRegister" 
        [closable]="false"
        >
        <div class="theme-modal-body">
            <p class="fw-medium mb-4 pt-3">Your account is already registered with this Email Id and User Type {{type}}</p>
        </div>
        <ng-template pTemplate="footer">
            <div class="gap-4 hstack justify-content-center">
                <button (click)="isAlreadyRegister=false" class="btn btn-primary">Ok</button>
            </div>
        </ng-template>
    </p-dialog>`
})


export class AccountOwner implements OnInit {

  public passwordInfoMessage = ErrorMessages.toolTips.passwordInfoMessage;
  public gmcNumberInfo = ErrorMessages.toolTips.gmcNumberInfo;
  svgIcons = SvgIcons;
  errorMessages: any = ErrorMessages.registrationForm;
  userRegistrationForm: FormGroup;
  otpForm: FormGroup;
  siteKey = environment.siteKey;
  clinicalId: any = localStorage.getItem('clinicId');
  specialisationList: { id: number, name: string }[] = [];
  selectedCategory = localStorage.getItem('selectedCategory');
  type: any;
  visible: boolean = false;
  visible2: boolean = false;
  showOtherTextbox: boolean = false;
  errorMessages2 = ErrorMessages.loginForm;
  otpVerificationSuccess: boolean = false;
  isDisabled: boolean = false;
  isAlreadyRegister: boolean = false;
  isEmailVerified: boolean = false;

  constructor(private router: Router,
    private fb: FormBuilder,
    private services: UserServiceService,
    private registrationService: RegistrationService,
    private messageService: MessageService,
    private titleService: Title,
    private route: ActivatedRoute,
    private cryptoService: CryptoService
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
    this.userRegistrationForm = this.fb.group({
      firstName: ['', [Validators.required, nameValidation]],
      lastName: ['', [Validators.required, nameValidation]],
      email: ['', [Validators.required, Validators.email, emailValidation]],
      mobileNo: ['', [Validators.required, phoneValidator]],
      specialization: ['', Validators.required],
      others: [''],
      gmcNumber: ['', [regNumberValidator]],
      password: ['', [Validators.required, passwordValidator]],
      confirmPassword: ['', Validators.required],
      terms: ['', Validators.required],
      recaptcha: ['', Validators.required]
    },
      {
        validators: this.passwordMatchValidator
      }
    );
    this.otpForm = this.fb.group({
      otp: ['', [Validators.required, Validators.pattern(/^\d{5}$/)]]
    });
  }

  ngOnInit(): void {
    this.updateCategorySelection();
    this.getSpecList();
  }

  updateCategorySelection() {
    this.selectedCategory = localStorage.getItem('selectedCategory');
    if (this.selectedCategory == 'forClinic') {
      this.type = "Clinic";
    } else if (this.selectedCategory == 'forSelf') {
      this.type = "Individual";
    }
  }

  getSpecList() {
    this.services.getSpecList().subscribe((response) => {
      if (response.statusCode == 200) {
        const sortedSpecialisationList = [];
        for (const item of response.data) {
          if (item.name !== 'Other') {
            sortedSpecialisationList.push({ id: item.id, name: item.name });
          }
        }
        sortedSpecialisationList.push({ id: '83', name: 'Other' });

        this.specialisationList = sortedSpecialisationList;
      } else {
        this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Error Loading Specialisations' });
      }
    });
  }
  handleSpecialisationChange() {
    const selectedSpecialisation: any = this.specialisationList.find(spec => spec?.name === this.userRegistrationForm.value.specialization.name);
    this.showOtherTextbox = selectedSpecialisation.name === "Other";
    const othersField = this.userRegistrationForm.get('others');
    if (this.showOtherTextbox && othersField) {
      othersField.setValidators([Validators.required, Validators.pattern(/^[A-Za-z\s]{5,50}$/)]);
    } else if (othersField) {
      othersField.clearValidators();
    }
    if (othersField) {
      othersField.updateValueAndValidity();
    }
  }
  onSubmit() {

    if (this.userRegistrationForm.valid) {
      if (this.type === "Clinic") {
        let data: { [key: string]: any } = {
          user_type: this.type,
          first_name: this.userRegistrationForm.value.firstName,
          last_name: this.userRegistrationForm.value.lastName,
          email: this.userRegistrationForm.value.email,
          phone: this.userRegistrationForm.value.mobileNo,
          password: this.userRegistrationForm.value.password,
          clinic_id: this.clinicalId,
          clinical_specializations_id: this.userRegistrationForm.value.specialization
        }
        if (this.userRegistrationForm.value.gmcNumber) {
          data['reg_gmc_no'] = this.userRegistrationForm.value.gmcNumber;
        }
        if (this.userRegistrationForm.value.others) {
          data['others'] = this.userRegistrationForm.value.others;
        }
        const encryptedData = this.cryptoService.encrypt(data);
        let request = {
          encrypet: encryptedData
        }
        this.services.postDocRegForm(request).subscribe((Response) => {
          if (Response.statusCode == 200) {
            this.registrationService.setCompletedStep(1);
            this.userRegistrationForm.reset();
            this.visible = true;
            this.clinicalId = '';
            localStorage.removeItem('clinicId');
            localStorage.removeItem('selectedCategory');
          }
        },
          (error) => {
            console.log('Error occurred:', error);
            let errorMsg = error?.error?.msg || 'An error occurred';
            this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
          })
      } else if (this.type === "Individual") {
        let data: { [key: string]: any } = {
          user_type: this.type,
          first_name: this.userRegistrationForm.value.firstName,
          last_name: this.userRegistrationForm.value.lastName,
          email: this.userRegistrationForm.value.email,
          phone: this.userRegistrationForm.value.mobileNo,
          password: this.userRegistrationForm.value.password,
          clinic_id: this.clinicalId,
          clinical_specializations_id: this.userRegistrationForm.value.specialization
        }
        if (this.userRegistrationForm.value.gmcNumber) {
          data['reg_gmc_no'] = this.userRegistrationForm.value.gmcNumber;
        }
        if (this.userRegistrationForm.value.others) {
          data['others'] = this.userRegistrationForm.value.others;
        }
        const encryptedData = this.cryptoService.encrypt(data);
        let request = {
          encrypet: encryptedData
        }
        this.services.postDocRegForm(request).subscribe((Response) => {
          if (Response.statusCode == 200) {
            this.registrationService.setCompletedStep(1);
            this.userRegistrationForm.reset();
            this.visible = true;
            localStorage.removeItem('clinicId');
            localStorage.removeItem('selectedCategory');
          }
        },
          (error) => {
            console.log('Error occurred:', error);
            let errorMsg = error?.error?.msg || 'An error occurred';
            this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
          })
      }
    } else {
      this.markFormGroupTouched(this.userRegistrationForm);
    }
  }

  isFieldInvalid(fieldName: string): any {
    const field = this.userRegistrationForm.get(fieldName);
    return field?.invalid && (field?.dirty || field?.touched);
  }
  isFieldInvalid2(fieldName: string): any {
    const field = this.otpForm.get(fieldName);
    return field?.invalid && (field?.dirty || field?.touched);
  }

  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
  markFormGroupTouched2(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched2(control);
      }
    });
  }

  passwordMatchValidator(control: FormGroup) {
    const password = control.get('password');
    const confirmPassword = control.get('confirmPassword');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
    }
  }

  checkEmail() {
    this.updateCategorySelection();
    this.isDisabled = false;
    let data = {
      email: this.userRegistrationForm.value.email
    }
    const encryptedData = this.cryptoService.encrypt(data);
        let request = {
          encrypet: encryptedData
    }
    this.services.checkEmail(request).subscribe((Response: any) => {
      if (Response.statusCode === 200) {
        const decryptedData = this.cryptoService.decrypt(Response.data);
        this.isEmailVerified = decryptedData.is_email_verified;
        let returnType = decryptedData.user_role_detail.find((type: any) => {
          return type.user_type === this.type;
        })
        if(returnType === undefined) {
          this.visible2 = true;
        } else {
          this.isAlreadyRegister = true;
          this.isDisabled = true;
        }
      }
    }, error => {
      console.log('Error: ', error)
    })
  }

  sendOtp() {
    let data = {
      email: this.userRegistrationForm.value.email
    }
    const encryptedData = this.cryptoService.encrypt(data);
    let request = {
      encrypet: encryptedData
}
    this.services.sendOtp(request).subscribe((Response: any) => {
      if (Response.statusCode === 200) {
        this.messageService.add({ severity: 'success', summary: 'success', detail: Response.msg });
      }
    },
      (error) => {
        console.log('Error occurred:', error);
        let errorMsg = error?.error?.msg || 'An error occurred';
        this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
      })
  }

  verifyOtp() {
    if (this.otpForm.valid) {
      let data = {
        email: this.userRegistrationForm.value.email,
        otp: this.otpForm.value.otp
      }
      const encryptedData = this.cryptoService.encrypt(data);
      let request = {
        encrypet: encryptedData
  }
      this.services.verifyOtp(request).subscribe((Response: any) => {
        if (Response.statusCode === 200) {
          this.otpVerificationSuccess = true;
          const decryptedData = this.cryptoService.decrypt(Response.data);
          this.userRegistrationForm.patchValue({
            firstName: decryptedData.first_name,
            lastName: decryptedData.last_name,
            email: decryptedData.email,
            mobileNo: decryptedData.phone,
            specialization: decryptedData.clinical_specializations_id,
            others: decryptedData.others,
            gmcNumber: decryptedData.reg_gmc_no,
          })
          this.messageService.add({ severity: 'success', summary: 'success', detail: Response.msg });
          this.visible2 = false;
        }
      },
        (error) => {
          console.log('Error occurred:', error);
          let errorMsg = error?.error?.msg || 'An error occurred';
          this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
        })
    } else {
      this.markFormGroupTouched2(this.otpForm);
    }
  }
}