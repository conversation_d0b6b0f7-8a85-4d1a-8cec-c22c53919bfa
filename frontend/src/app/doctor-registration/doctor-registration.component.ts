import { ChangeDetectorRef, Component } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { RegistrationService } from '../services/registration.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-doctor-registration',
  templateUrl: './doctor-registration.component.html',
  styleUrls: ['./doctor-registration.component.scss']
})
export class DoctorRegistrationComponent {
  selectedCategory: any = null;
  categories: any[] = [
    { name: 'Clinic/Hospital', key: 'forClinic' },
    { name: 'Individual', key: 'forSelf' }
  ];
  items: MenuItem[] | undefined;
  isSelfEmployed: boolean = false;
  isClinic: boolean = true;

  constructor(
    private registrationService: RegistrationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    const currentPath = window.location.pathname;
    if(currentPath  == '/signup/clinic'){
      this.isClinic = false;
    }
    this.selectedCategory = this.categories.find(category => category.key === 'forClinic');
    localStorage.setItem('selectedCategory', 'forClinic');
    if (this.selectedCategory.key == 'forClinic') {
      this.router.navigate(['/sign-up/clinic']);
    } else if (this.selectedCategory.key == 'forSelf') {
      this.router.navigate(['/sign-up/owner']);
    }
    this.registrationService.completedStep$.subscribe(completedStepIndex => {
      this.items = [
        {
          label: 'Clinic Information',
          routerLink: 'clinic',
          styleClass: completedStepIndex === 0 ? 'p-steps-completed' : '',
          disabled: this.isClinic 
        },
        {
          label: 'Account Owner',
          routerLink: 'owner',
          styleClass: completedStepIndex === 1 ? 'p-steps-completed' : '',
          disabled: !this.isSelfEmployed
        }
      ];
    });
  }

  onCategorySelected(category: any): void {
    this.selectedCategory = category;
    this.isSelfEmployed = category.key === 'forSelf';
    localStorage.setItem('selectedCategory', category.key);
    if (this.isSelfEmployed) {
      this.router.navigate(['/sign-up/owner']);
    } else {
      this.router.navigate(['/sign-up/clinic']);
    }
  }

}

