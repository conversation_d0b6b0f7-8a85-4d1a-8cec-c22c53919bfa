<ng-template pTemplate="header">
    <h3 class="mb-0 fw-normal">Contact Us</h3>
</ng-template>
<div class="theme-modal-body">
    <p class="fw-medium mb-4 text-center">Thank you for your interest in contacting&nbsp;us. Please&nbsp;fill out
        the contact form below, and our team will get back to you as soon as possible. We strive to respond to all
        inquiries within 48 hours.</p>
    <form [formGroup]="contactUsForm" (ngSubmit)="onSubmit()">
        <div class="form-group mb-4">
            <label class="fs-14 fw-semibold mb-1 lh-sm" for="name">Name<span class="text-danger">*</span></label>
            <input pInputText id="name" formControlName="name" />
            <div *ngIf="isFieldInvalid('name')" class="fs-14 lh-sm my-1 text-danger">
                <div *ngIf="contactUsForm.get('name')?.errors?.['required']">
                    {{errorMessages.name.required}}</div>
                <div *ngIf="contactUsForm.get('name')?.errors?.['pattern']">
                    {{errorMessages.name.pattern}}</div>
            </div>
        </div>
        <div class="form-group mb-4">
            <label class="fs-14 fw-semibold mb-1 lh-sm" for="email">Email<span class="text-danger">*</span></label>
            <input pInputText id="email" formControlName="email" />
            <div *ngIf="isFieldInvalid('email')" class="fs-14 lh-sm my-1 text-danger">
                <div *ngIf="contactUsForm.get('email')?.errors?.['required']">
                    {{errorMessages.email.required}}</div>
                <div *ngIf="contactUsForm.get('email')?.errors?.['email']">
                    {{errorMessages.email.email}}</div>
                <div *ngIf="contactUsForm.get('email')?.errors?.['pattern']">
                    {{errorMessages.email.pattern}}</div>
            </div>
        </div>
        <div class="form-group mb-4">
            <label class="fs-14 fw-semibold mb-1 lh-sm" for="subject">Subject<span class="text-danger">*</span></label>
            <input pInputText id="subject" formControlName="subject" />
            <div *ngIf="isFieldInvalid('subject')" class="fs-14 lh-sm my-1 text-danger">
                <div *ngIf="contactUsForm.get('subject')?.errors?.['required']">
                    {{errorMessages.subject.required}}</div>
                <div *ngIf="contactUsForm.get('subject')?.errors?.['maxlength']">
                    {{errorMessages.subject.maxlength}}</div>
            </div>
        </div>
        <div class="form-group mb-4">
            <label class="fs-14 fw-semibold mb-1 lh-sm" for="message">Message<span class="text-danger">*</span></label>
            <textarea id="message" rows="4" pInputTextarea formControlName="message"></textarea>
            <div *ngIf="isFieldInvalid('message')" class="fs-14 lh-sm my-1 text-danger">
                <div *ngIf="contactUsForm.get('message')?.errors?.['required']">
                    {{errorMessages.message.required}}</div>
                <div *ngIf="contactUsForm.get('message')?.errors?.['maxlength']">
                    {{errorMessages.message.maxlength}}</div>
            </div>
        </div>
        <div class="form-group mb-4">
            <ngx-recaptcha2 #captchaElem [siteKey]="siteKey"></ngx-recaptcha2>
        </div>
        <div class="gap-4 hstack justify-content-center">
            <button (click)="onCancel()" class="btn btn-light bg-white text-body">Cancel</button>
            <p-toast></p-toast>
            <button type="submit" class="btn btn-primary">Submit</button>
        </div>
    </form>
</div>
<p-toast></p-toast>
