import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ClinicRegistration } from './doctor-registration/clinical-info';
import { AccountOwner } from './doctor-registration/account-owner-reg';
import { DoctorRegistrationComponent } from './doctor-registration/doctor-registration.component';
import { HomeComponent } from './home/<USER>';
import { DataUsagePolicyComponent } from './data-usage-policy/data-usage-policy.component';
import { PrivacyPolicyComponent } from './privacy-policy/privacy-policy.component';
import { AboutUsComponent } from './about-us/about-us.component';
import { DataSecurityComponent } from './data-security/data-security.component';
import { TermsAndConditionsComponent } from './terms-and-conditions/terms-and-conditions.component';
import { InviteUserRegistrationComponent } from './invite-user-registration/invite-user-registration.component';
import { DpaComponent } from './dpa/dpa.component';
import { PricingPlansComponent } from './pricing-plans/pricing-plans.component';

const routes: Routes = [
  { path: '', component: HomeComponent, data: { title: 'Clinical Pad | A New Way To Create Clinical Letters' } },
  { path: 'home', component: HomeComponent, data: { title: 'Clinical Pad | A New Way To Create Clinical Letters' } },
  { path: 'pricing', component: PricingPlansComponent, data: { title: 'Clinical Pad | Pricing & Plans' } },
  { path: 'data-usage-policy', component: DataUsagePolicyComponent, data: { title: 'Data Usage Policy | Clinical Pad' } },
  { path: 'privacy-policy', component: PrivacyPolicyComponent, data: { title: 'Privacy Policy | Clinical Pad' } },
  { path: 'terms-and-conditions', component: TermsAndConditionsComponent, data: { title: 'Terms and Conditions | Clinical Pad' } },
  { path: 'about-us', component: AboutUsComponent, data: { title: 'About Us | Clinical Pad' } },
  { path: 'dpa', component: DpaComponent, data: { title: 'Data Processing Agreement | Clinical Pad' } },
  { path: 'data-security', component: DataSecurityComponent, data: { title: 'Data Security | Clinical Pad' } },
  { path: 'invite-user', component: InviteUserRegistrationComponent, data: { title: 'Clinical Pad | Invite User Registration' } },
  { path: 'sign-up', component: DoctorRegistrationComponent, children: [
    { path: 'clinic', component: ClinicRegistration, data: { title: 'Sign Up | Clinical Pad' }  },
    { path: 'owner', component: AccountOwner, data: { title: 'Sign Up | Clinical Pad' }  }] 
  },
  {
    path: 'user-account',
    loadChildren: () => import('./user-account/user-account.module').then(m => m.UserAccountModule)
  },
  {
    path: 'admin',
    loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule)
  },
  /* {
    path: '**',
    redirectTo: '/'
  } */
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
