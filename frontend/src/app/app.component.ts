import { Component, OnInit, ChangeDetectorRef, AfterContentChecked } from '@angular/core';
import { LoadingService } from './services/loading.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit, AfterContentChecked {
  title = 'clinicalnotes-frontend';
  isLoading$!: Observable<boolean>; // Initialize it here
  loadingText$!: Observable<string>; // Initialize it here

  constructor(
    private loadingService: LoadingService,
    private changeDetector: ChangeDetectorRef
  ) {}

  ngOnInit() {
    // Subscribe to the loading$ and loadingText$ observables from the LoadingService
    this.isLoading$ = this.loadingService.loading$;
    this.loadingText$ = this.loadingService.loadingText$;
  }

  ngAfterContentChecked(): void {
    this.changeDetector.detectChanges();
  }
}
