<main class="theme-body-main d-flex flex-column vh-100">
    <app-header></app-header>

    <div class="flex-grow-1 overflow-y-scroll">

        <div class="overflow-hidden position-relative px-4 px-lg-4 px-md-5 px-xl-5 pt-4">
            <div class="container pt-2">
                <img alt="Hospital Plus Sign background"
                    class="end-0 hospital-sign img-fluid mt-n3 pe-none position-absolute theme-cms-bg-1 theme-flip user-select-none z-n1"
                    src="assets/images/hospital-plus-sign.png" style="height:223px; width:235px" />

                <!-- Pricing Section -->
                <section class="pricing-section text-center">
                    <h1 class="mb-1 display-5 fw-normal">Pick Your Plan</h1>
                    <div class="fw-medium fs-4 mb-5 lh-sm">Choose the plan that best suits your needs and get started
                        for free.<br />No Card details required.</div>

                    <p-carousel [value]="plans" [numVisible]="4" [numScroll]="1" [responsiveOptions]="responsiveOptions"
                        [showNavigators]="false" [ngClass]="{'p-indicator-length': plans.length <= 4}">
                        <ng-template pTemplate="header">
                            <div class="small fw-semibold text-primary mb-1">Yearly 10% off</div>
                            <div
                                class="align-items-center d-flex flex-column flex-md-row flex-wrap gap-2 justify-content-center mb-4 ng-star-inserted position-relative">
                                <!-- <div
                                    class="float-md-start mt-md-2 positioned-currency-dropdown pt-md-1 start-0 theme-select-rounded top-0 w-100 z-2 text-start ps-md-3">
                                    <p-dropdown placeholder="Currency" [options]="currencyOptions"
                                        [(ngModel)]="selectedCurrency"
                                        (onChange)="onCurrencyChange($event)"></p-dropdown>
                                </div> -->
                                <div
                                    class="switch-wrapper position-relative d-inline-flex p-1 border rounded-5 bg-white overflow-hidden">
                                    <input id="monthly" type="radio" name="switch"
                                        class="position-absolute z-n1 opacity-0 invisible" checked
                                        (change)="onPlanTypeChange('Monthly')">
                                    <input id="yearly" type="radio" name="switch"
                                        class="position-absolute z-n1 opacity-0 invisible"
                                        (change)="onPlanTypeChange('Yearly')">
                                    <label class="fs-5 fw-semibold z-1 rounded-5" for="monthly">Monthly</label>
                                    <label class="fs-5 fw-semibold z-1 rounded-5" for="yearly">Yearly</label>
                                    <span class="highlighter position-absolute rounded-5 bg-primary"></span>
                                </div>
                            </div>
                        </ng-template>

                        <ng-template let-plan pTemplate="item">
                            <div class="plans-item p-sm-1 p-md-3 text-start h-100">
                                <div class="plans-item-content h-100" [class.active]="plan.id == selectedPlanId">
                                    <div class="fs-4 mb-2 fw-medium d-flex flex-wrap gap-2 align-items-center">
                                        <span class="me-auto">{{ plan.name }}</span>
                                        <span *ngIf="plan.name == 'Basic'"
                                            class="fs-12 fw-semibold rounded px-2 py-1 text-primary border border-primary popular-label">Most
                                            Popular</span>
                                    </div>
                                    <p class="fs-13 fw-semibold mb-3">{{ plan.description }}</p>
                                    <div class="fw-bold fs-4 mb-3">
                                        <span *ngIf="plan.name === 'Free'">Pay As You Go</span>
                                        <span *ngIf="plan.id === 99889989">Exclusive</span>
                                        <span *ngIf="plan.name !== 'Free' && plan.id != 99889989">£{{
                                            plan.price }}
                                        </span>
                                        <small *ngIf="plan.name !== 'Free' && plan.id != 99889989"
                                            class="fw-normal">per
                                            {{ getPlanType(plan.plan_type) }}</small>
                                    </div>
                                    <div class="px-2 mb-3">
                                        <button class="w-100 btn btn-primary" (click)="onSubmit(plan.id)"
                                            *ngIf="plan.id != 99889989">Sign Up</button>
                                        <button class="w-100 btn btn-primary" (click)="showContactDialog()"
                                            *ngIf="plan.id === 99889989">Contact Sales</button>
                                    </div>
                                    <div class="plan-content fs-13 fw-medium" *ngIf="plan.id !== 99889989">
                                        <div class="fs-14 fw-bold mb-2 pb-1">Includes:</div>
                                        <ul
                                            class="list-check-circle-style d-flex flex-column gap-3 mb-2 lh-sm list-unstyled">
                                            <li *ngIf="plan.free_trial_docs !== 0">Free Trial - {{plan.free_trial_docs}}
                                                clinical documents</li>
                                            <li *ngIf="plan.included_docs !== 0">{{plan.included_docs}} clinical
                                                documents included per {{ getPlanType(plan.plan_type) }}</li>
                                            <li>Additional Documents at £{{plan.additional_document_price}} per document
                                            </li>
                                            <li>Up to {{plan.dictation_limit_per_doc_min}} minutes of dictation per
                                                documents</li>
                                            <li *ngIf="plan.max_team_members !== 0">Invite up to
                                                {{plan.max_team_members}} additional team members</li>
                                            <li *ngIf="plan.free_support">Free Support</li>
                                            <li *ngIf="plan.priority_feature_access">Priority access to new feature
                                                releases</li>
                                        </ul>
                                    </div>
                                    <div class="plan-content fs-13 fw-medium" *ngIf="plan.id === 99889989">
                                        <div class="fs-14 fw-bold mb-2 pb-1">Includes:</div>
                                        <ul
                                            class="list-check-circle-style d-flex flex-column gap-3 mb-2 lh-sm list-unstyled">
                                            <li>Free Trial - Customised</li>
                                            <li>Custom clinical
                                                documents included per month</li>
                                            <li>Additional Documents at custom amount
                                            </li>
                                            <li>Up to customised minutes of dictation per
                                                documents</li>
                                            <li>Invite unlimited team members</li>
                                            <li>Dedicated Account Manager</li>
                                            <li>Cancel Anytime</li>
                                            <li>Priority access to new feature
                                                releases</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                       
                    </p-carousel>
                    <div *ngIf="noPlanMessage" class="fw-medium fs-4 mb-5 lh-sm">No Plan Found.</div>
                </section>

                <!-- Pricing Section -->
                <section class="pricing-section text-center pt-5">
                    <h2 class="mb-3 display-6 fw-normal">How Much Can You Save Using Clinicalpad?</h2>
                    <div class="fw-medium fs-4 mb-4 lh-sm">See how much time and money you could save on average using
                        ClinicalPad compared totraditional medical transcription services and in-house transcription
                        assistants</div>

                    <div class="py-3">
                        <div class="fs-3 fw-semibold mb-3 pb-1">Documents you create per month: <span
                                class="min-w-50 d-inline-block text-start">{{rangeValue}}</span></div>
                        <div class="theme-range-slider">
                            <p-slider [(ngModel)]="rangeValue" class="w-full" [max]="maxRangeValue"
                                (onChange)="onRangeChange()"></p-slider>
                        </div>
                        <div class="pt-5 pb-4 col-sm-10 col-xxl-9 mx-auto">
                            <h3 class="mb-4">Save On Average</h3>
                            <div class="row gy-3">
                                <div class="col-sm-6">
                                    <div class="border rounded h-100 p-2 p-sm-3">
                                        <div class="text-primary">
                                            <span class="fs-3 fw-bold">$ {{ medicalTranscriptionSavings.toFixed(2) }}
                                            </span>
                                            <span class="fs-5"> per month</span>
                                        </div>
                                        <p class="fs-5 mb-0 fw-semibold">on medical transcriptional costs</p>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="border rounded h-100 p-2 p-sm-3">
                                        <div class="text-primary">
                                            <span class="fs-3 fw-bold">{{ inHouseTranscriptionHours.toFixed(0) }}
                                            </span>
                                            <span class="fs-5"> hours</span>
                                        </div>
                                        <p class="fs-5 mb-0 fw-semibold">of in-house transcription</p>
                                    </div>
                                </div>
                            </div>
                            <p class="small my-4">*Estimates based on $0.39 per letter, 1.5 days of average medical
                                transcription turnaround times,
                                and 40 mins of transcription time per document by an in-house transcriptionist.</p>
                        </div>
                    </div>
                </section>

                <!-- Frequently Asked Questions Section -->
                <app-security-faq></app-security-faq>

            </div>
        </div>

        <!-- Footer -->
        <app-footer></app-footer>
    </div>
</main>
<!--Confirmation Popup-->
<p-dialog styleClass="theme-modal-main theme-modal-sm theme-modal-center" header="Header" [modal]="true"
    [draggable]="false" [resizable]="false" [dismissableMask]="true" [(visible)]="visible" [closable]="false">
    <ng-template pTemplate="header">
        <h3 class="mb-0 fw-normal">Please complete the registration first</h3>
    </ng-template>
    <div class="theme-modal-body">
        <p class="fw-medium mb-4 text-center">Are you sure you want to proceed?</p>
        <div class="gap-4 hstack justify-content-center">
            <button (click)="visible = false" class="btn btn-light bg-white text-body">No</button>
            <button type="submit" class="btn btn-primary" (click)="onProceed()">Yes</button>
        </div>
    </div>
</p-dialog>