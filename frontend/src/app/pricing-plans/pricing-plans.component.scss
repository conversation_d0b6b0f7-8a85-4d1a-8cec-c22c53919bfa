@import "../../assets/css/variables";

:host ::ng-deep {
    .p-carousel {

        // Switch CSS
        .switch-wrapper {
            [type="radio"] {
                &:checked#monthly {
                    ~ {
                        label[for="monthly"] {
                            color: $white;

                            &:hover {
                                background: transparent;
                            }
                        }
                    }

                    + {
                        label[for="yearly"] {
                            ~ {
                                .highlighter {
                                    transform: none;
                                }
                            }
                        }
                    }
                }

                &:checked#yearly {
                    ~ {
                        label[for="yearly"] {
                            color: $white;

                            &:hover {
                                background: transparent;
                            }
                        }
                    }

                    + {
                        label[for="monthly"] {
                            ~ {
                                .highlighter {
                                    transform: translateX(100%);
                                }
                            }
                        }
                    }
                }
            }

            label {
                padding: 12px 0;
                min-width: 120px;
                cursor: pointer;
                transition: color 0.25s ease-in-out;

                &:hover {
                    color: $primary;
                }
            }

            .highlighter {
                top: 4px;
                left: 4px;
                width: calc(50% - 4px);
                height: calc(100% - 8px);
                transition: transform 0.25s ease-in-out;
            }
        }

        //Currency Select Box
        .theme-select-rounded {
            max-width: 200px;

            @include below-lg() {
                max-width: 165px;
            }

            &.positioned-currency-dropdown {
                @include above-md() {
                    position: absolute;
                }
            }

            .p-dropdown {
                border-radius: 40px;

                .p-dropdown-trigger {
                    .p-icon-wrapper {
                        background-color: $primary;
                        border-radius: 40px;
                        color: $white;
                        width: 31px;
                        height: 31px;
                        margin-right: -4px;
                        align-items: center;
                        justify-content: center;
                    }
                }
            }
        }

        // Plans Item
        .plans-item {
            .plans-item-content {
                padding: .75rem;
                border-radius: .75rem;
                border: 1px solid $light;
                transition: all .25s ease-in-out;

                &:hover,
                &.active {
                    border-color: $primary;
                }

                &.active {
                    background-color: $primary;
                    color: $white;

                    .popular-label {
                        background-color: $white;
                    }

                    .btn-primary {
                        background-color: #60CAF1 !important;
                        border-color: #60CAF1 !important;
                    }

                    .list-check-circle-style {
                        li {
                            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12"><path id="Icon_ionic-ios-checkmark-circle" data-name="Icon ionic-ios-checkmark-circle" d="M9.375,3.375a6,6,0,1,0,6,6A6,6,0,0,0,9.375,3.375Zm3.072,4.341L8.59,11.59h0a.521.521,0,0,1-.335.159.505.505,0,0,1-.338-.164L6.3,9.969a.115.115,0,0,1,0-.164l.513-.513a.112.112,0,0,1,.162,0l1.281,1.281,3.519-3.545a.114.114,0,0,1,.081-.035h0a.1.1,0,0,1,.081.035l.5.522A.114.114,0,0,1,12.447,7.716Z" transform="translate(-3.375 -3.375)" fill="%23C5B5FF"/></svg>');
                        }
                    }
                }

                .list-check-circle-style {
                    li {
                        padding-left: 22px;
                        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12"><path id="Icon_ionic-ios-checkmark-circle" data-name="Icon ionic-ios-checkmark-circle" d="M9.375,3.375a6,6,0,1,0,6,6A6,6,0,0,0,9.375,3.375Zm3.072,4.341L8.59,11.59h0a.521.521,0,0,1-.335.159.505.505,0,0,1-.338-.164L6.3,9.969a.115.115,0,0,1,0-.164l.513-.513a.112.112,0,0,1,.162,0l1.281,1.281,3.519-3.545a.114.114,0,0,1,.081-.035h0a.1.1,0,0,1,.081.035l.5.522A.114.114,0,0,1,12.447,7.716Z" transform="translate(-3.375 -3.375)" fill="%235223fc"/></svg>');
                        background-repeat: no-repeat;
                        background-size: 12px;
                        background-position: top 3px left;
                    }
                }
            }
        }

        // Indicators CSS
        .p-carousel-indicators {
            padding-top: 1.25rem;
            padding-bottom: 0;

            .p-carousel-indicator {
                margin-right: 0;

                &:not(:last-child) {
                    margin-right: 1rem;
                }

                button {
                    background-color: $light;
                    width: 0.75rem;
                    height: 0.75rem;
                    border-radius: 0.75rem;
                }

                &.p-highlight {
                    button {
                        background-color: $primary;
                    }
                }
            }
        }
    }

    .p-indicator-length {
        .p-carousel-indicators {
            @include above-1300(){
                display: none;
            }
        }
    }

    .theme-range-slider {
        .p-slider {
            &.p-slider-horizontal {
                height: 6px;
                border-radius: $border-radius;

                .p-slider-handle {
                    margin-top: -13.5px;
                    margin-left: -13.5px;
                }
            }

            .p-slider-range {
                border-radius: $border-radius;
                background: $primary;
            }

            .p-slider-handle {
                width: 27px;
                height: 27px;
                border-width: $border-radius;
                border-color: $white;
                background: $primary;
                box-shadow: $box-shadow;

                &:focus {
                    box-shadow: 0 0 0 0.2rem rgba($primary, .1);
                }
            }

            &:not(.p-disabled) {
                .p-slider-handle {
                    &:hover {
                        background: $primary;
                        border-color: $white;
                    }
                }
            }
        }
    }

}