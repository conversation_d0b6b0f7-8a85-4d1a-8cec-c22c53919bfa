import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { CreditsService } from 'src/app/services/credit.service';
import { CryptoService } from 'src/app/services/crypto.service';
import { StripeService } from 'src/app/services/stripe.service';
import { UserServiceService } from 'src/app/services/user-service.service';

@Component({
  selector: 'app-confirmation',
  templateUrl: './confirmation.component.html',
  styleUrls: ['./confirmation.component.scss']
})
export class ConfirmationComponent implements OnInit {

  billingInfoParsed: any = '';
  cardDetails: any = localStorage.getItem('cardDetails');
  cardDetailsParsed: any = JSON.parse(this.cardDetails);
  cardHolderName: any = localStorage.getItem('cardHolderName');
  customerId: any;
  planId: any = localStorage.getItem('planId');
  visible: boolean = false;
  planData: any;
  apiCallSuccess: boolean = false;
  getToken = localStorage.getItem('currentUser');

  constructor(
    private route: ActivatedRoute,
    private titleService: Title,
    private services: UserServiceService,
    private cryptoService: CryptoService,
    private stripeService: StripeService,
    private router: Router,
    private messageService: MessageService,
    private creditsService: CreditsService

  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
  }

  ngOnInit(): void {
    this.getCustomerDetails();
    this.getPlanDetail();
  }
  getCustomerDetails() {
    this.services.getStripeCustomer().subscribe((Response) => {
      const decryptedData = this.cryptoService.decrypt(Response.data);
      this.billingInfoParsed = decryptedData;
      this.customerId = decryptedData?.customer_id;
    })
  }
  getPlanDetail() {
    this.services.getUserPlanDetail(this.planId).subscribe((Response)=>{
      const decryptedData = this.cryptoService.decrypt(Response.data);
      this.planData = decryptedData;
    })
  }
  getPlanType(planType: string): string {
    switch (planType) {
      case 'Monthly':
        return 'month';
      case 'Yearly':
        return 'year';
      default:
        return planType;
    }
  }
  onSubmit() {
    let request = {
      customer_id: this.customerId,
      plan_id: this.planId,
      payment_method_id: this.billingInfoParsed.payment_method_id,
      billing_address_info: {
        first_name: this.billingInfoParsed.billing_address_info.first_name,
        last_name: this.billingInfoParsed.billing_address_info.last_name,
        email: this.billingInfoParsed.billing_address_info.email,
        phone: this.billingInfoParsed.billing_address_info.phone,
        address: this.billingInfoParsed.billing_address_info.address,
        country: this.billingInfoParsed.billing_address_info.country,
        state: this.billingInfoParsed.billing_address_info.state,
        town: this.billingInfoParsed.billing_address_info.town,
      }
    }
    this.services.createSubscription(request).subscribe((Response) => {
      this.apiCallSuccess = true;
      const decryptedData = this.cryptoService.decrypt(Response.data);
      let clientSecrentKey = decryptedData.clientSecret;
      let cliensetupIntenttSecret = decryptedData.cliensetupIntenttSecret;
      let subscription_id = decryptedData.subscriptionId;
      this.stripeService.confirmCardPayment(clientSecrentKey, this.billingInfoParsed.payment_method_id)
        .then(result => {
          if (result.error) {
            // Handle the error
            console.error('Error:', result.error.message);
            this.router.navigate(['/user/payment-failed'])
          } else {
            let request = {
              subscription_id: subscription_id
            }
            this.services.confirmSubscription(request).subscribe((Response) => {
              
            })
            setTimeout(() => {
              this.stripeService.orderComplete(cliensetupIntenttSecret).then(result => {
                this.services.getUserProfile(this.getToken).subscribe((Response)=> {
                  const decryptedData = this.cryptoService.decrypt(Response.data);
                  let creditsBalance = decryptedData?.credit_documents + decryptedData?.subscription_documents;
                  this.creditsService.updateCredits(creditsBalance);
                })
                this.router.navigate(['/user/payment-successful'])
              });
            }, 1000);

          }
        })
        .catch(error => {
          console.error('Error confirming card setup:', error);
        });
    },
    (error) => {
      this.apiCallSuccess = false;
      let errorMsg = error?.error?.msg || 'An error occurred';
      this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
    });
  }
}
