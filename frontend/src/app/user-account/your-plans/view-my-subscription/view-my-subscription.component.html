<div class="d-flex flex-column pageContainer px-3 pb-3 h-100 overflow-y-scroll">
    <div class="contentHeader py-2 my-1">
        <div class="hstack gap-2 flex-wrap justify-content-between">
            <div class="theme-left-header">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb fw-medium">
                        <li class="breadcrumb-item">
                            <a class="text-decoration-none"
                                [routerLink]="['/user/secure/payment-billing/my-subscriptions']">My Subscriptions</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">My Subscriptions Details</li>
                    </ol>
                </nav>
                <h1 class="fw-normal h3 mb-0">My Subscription Details</h1>
            </div>
        </div>
    </div>
    <div class="flex-grow-1 pageContent position-relative">
        <form class="p-xl-1 d-flex flex-column w-100 h-100">
            <div class="card mb-2">
                <div class="card-body">
                    <div class="hstack gap-3 flex-wrap bg-primary text-white p-3 rounded mb-4">
                        <div class="me-auto p-xl-1">
                            <h3 class="mb-1 hstack flex-wrap gap-2">{{ subscriptionData?.plan?.name }} <div *ngIf="subStatus === 1 && !subscriptionCancel"
                                    class="align-middle bg-white d-inline-block font-family-base fs-15 fw-semibold mb-0 px-3 py-1 rounded-1 rounded-5 text-primary">
                                    Active</div>
                                    <div *ngIf="subscriptionCancel"
                                    class="align-middle bg-white d-inline-block font-family-base fs-15 fw-semibold mb-0 px-3 py-1 rounded-1 rounded-5 text-danger">
                                    Cancelled</div>
                                    <div *ngIf="subStatus === 8"
                                    class="align-middle bg-white d-inline-block font-family-base fs-15 fw-semibold mb-0 px-3 py-1 rounded-1 rounded-5 text-danger">
                                    Incomplete</div>
                                    <div *ngIf="subStatus === 4"
                                    class="align-middle bg-white d-inline-block font-family-base fs-15 fw-semibold mb-0 px-3 py-1 rounded-1 rounded-5 text-warning">
                                    Past Due</div>
                                    <div *ngIf="subStatus === 5"
                                    class="align-middle bg-white d-inline-block font-family-base fs-15 fw-semibold mb-0 px-3 py-1 rounded-1 rounded-5 text-info">
                                    Trailing</div>
                                    <div *ngIf="subStatus === 2"
                                    class="align-middle bg-white d-inline-block font-family-base fs-15 fw-semibold mb-0 px-3 py-1 rounded-1 rounded-5 text-danger">
                                    Expired</div>
                            </h3>
                            <p class="fs-15 fw-medium mb-0">Best for busy individuals andsmall clinics</p>
                        </div>
                        <div class="h6 mb-0 p-xl-1">
                            <span class="h3 mb-0">£ {{
                                subscriptionData?.plan?.price }} </span> <span class="text-nowrap">Per {{
                                getBillingFrequency(subscriptionData?.plan?.plan_type) }}</span>
                        </div>
                    </div>
                    <div class="plan-content fs-13 fw-medium">
                        <div class="fs-14 fw-bold mb-3">Includes:</div>
                        <div class="row gx-xl-5 gy-4 lh-sm">
                            <div class="col-sm-6 col-md-4 col-lg-3 py-sm-1">
                                <div class="list-check-circle-style">Free Trial -
                                    {{subscriptionData.subscription_features?.free_trial_docs}} clinical documents</div>
                            </div>
                            <div class="col-sm-6 col-md-4 col-lg-3 py-sm-1">
                                <div class="list-check-circle-style">
                                    {{subscriptionData.subscription_features?.included_docs}} clinical documents
                                    included per month</div>
                            </div>
                            <div class="col-sm-6 col-md-4 col-lg-3 py-sm-1">
                                <div class="list-check-circle-style">Additional documents at
                                    ${{subscriptionData.subscription_features?.additional_document_price}} per document
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-4 col-lg-3 py-sm-1">
                                <div class="list-check-circle-style">Up to
                                    {{subscriptionData.subscription_features?.dictation_limit_per_doc_min}} minutes of
                                    dictation per document</div>
                            </div>
                            <div class="col-sm-6 col-md-4 col-lg-3 py-sm-1">
                                <div class="list-check-circle-style">Invite up to
                                    {{subscriptionData.subscription_features?.max_team_members}} additional team members
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-4 col-lg-3 py-sm-1"
                                *ngIf="subscriptionData.subscription_features?.free_support">
                                <div class="list-check-circle-style"
                                    *ngIf="subscriptionData.subscription_features?.free_support">Free Support
                                </div>
                            </div>
                            <!-- <div class="col-sm-6 col-md-4 col-lg-3 py-sm-1"
                                *ngIf="subscriptionData.subscription_features?.cancel_anytime">
                                <div class="list-check-circle-style"
                                    *ngIf="subscriptionData.subscription_features?.cancel_anytime">Cancel anytime</div>
                            </div> -->
                            <div class="col-sm-6 col-md-4 col-lg-3 py-sm-1"
                                *ngIf="subscriptionData.subscription_features?.priority_feature_access">
                                <div class="list-check-circle-style"
                                    *ngIf="subscriptionData.subscription_features?.priority_feature_access">Priority
                                    access to new feature releases</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card mb-2">
                <div class="card-body theme-data-table-cover">
                    <p-table styleClass="p-datatable-striped text-nowrap" [value]="invoiceData?.list"
                        [paginator]="false">
                        <ng-template pTemplate="header">
                            <tr>
                                <th pSortableColumn="act_payment_date" scope="Act. Payment Date">
                                    Act. Payment Date
                                    <p-sortIcon field="act_payment_date"></p-sortIcon>
                                </th>
                                <th pSortableColumn="period_start" scope="Period Start">Period Start <p-sortIcon
                                        field="period_start"></p-sortIcon>
                                </th>
                                <th pSortableColumn="period_end" scope="Period End">Period End <p-sortIcon
                                        field="period_end"></p-sortIcon>
                                </th>
                                <th scope="Amount">Amount</th>
                                <th pSortableColumn="card_number" scope="Card Number">Card Number <p-sortIcon
                                        field="card_number"></p-sortIcon>
                                </th>
                                <th class="text-center" style="width: 100px;" scope="Actions">Actions</th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-invoice>
                            <tr>
                                <td>{{ formatEndDate(invoice?.createdAt) }}</td>
                                <td>{{ formatEndDate(invoice?.createdAt) }}</td>
                                <td>{{ formatEndDate(invoice?.due_date) }}</td>
                                <td>£ {{
                                    invoice?.amount}}</td>
                                <td>xxxx xxxx xxxx {{ invoice?.card_last_four }}</td>
                                <td>
                                    <div class="hstack gap-3 justify-content-center">
                                        <button (click)="viewInvoice(invoice?.invoice_id)" data-bs-toggle="tooltip"
                                            data-bs-placement="top" title="View Invoice" aria-label="View Invoice"
                                            appBsTooltip
                                            class="btn btn-link h-20 p-1 rounded-1 w-20 d-flex align-items-center justify-content-center">
                                            <span [innerHTML]="svgIcons.eyeIcon | safeHtml"></span>
                                        </button>
                                        <button appBsTooltip data-bs-toggle="tooltip" data-bs-placement="top"
                                            title="Download Invoice" aria-label="Download Invoice"
                                            class="btn btn-link h-20 p-1 rounded-1 w-20 d-flex align-items-center justify-content-center">
                                            <span [innerHTML]="svgIcons.downloadIconSingle | safeHtml"
                                                (click)="generatePDF(invoice?.invoice_id)"></span>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td class="bg-body-tertiary fw-medium text-center" colspan="6">No data found.</td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <p-paginator (onPageChange)="onPageChange($event)" [rows]="invoiceData?.recordsPerPage"
                        [totalRecords]="invoiceData?.totalRecords"></p-paginator>
                </div>
            </div>
            <div class="hstack mt-auto justify-content-end gap-3 flex-wrap">
                <a [routerLink]="['/user/secure/payment-billing/my-subscriptions']"
                    class="bg-white btn btn-light text-body flex-sm-grow-0 flex-grow-1">Back</a>
            </div>
        </form>

    </div>
</div>

<!-- Preview Dialog -->
<p-dialog styleClass="theme-modal-main theme-modal-xl theme-modal-header-close theme-preview-modal" [modal]="true"
    [draggable]="false" [resizable]="false" [dismissableMask]="true" [(visible)]="visible">

    <div class="p-dialog-header">
        <h5 class="mb-0 font-family-base fw-semibold">Invoice Preview</h5>
    </div>
    <div class="theme-modal-body">
        <div class="table-responsive">
            <div #invoicePreview>
                <table
                    style="font-family: Arial, Helvetica, sans-serif;font-size: 14px;color: #393956;margin: 0;padding: 0;width: 100%;border: 0;background: #FFFFFF;text-align: left;border-collapse: collapse;">
                    <thead>
                        <tr>
                            <th scope="col" style="border-bottom:1px solid #E5EAFE; padding: 20px;">
                                <a [routerLink]="['']" aria-label="Link to Clinical Pad Website">
                                    <img src="assets/images/logo.png" alt="Clinical Pad Logo" width="200" height="31"
                                        style="border: 0; display: inline-block;" />
                                </a>
                            </th>
                            <th scope="col"
                                style="border-bottom:1px solid #E5EAFE; padding: 20px;text-align: right;font-size: 28px;white-space: nowrap;">
                                INVOICE
                            </th>
                        </tr>
                        <tr>
                            <th scope="col"
                                style="padding: 20px;font-size: 16px;line-height: 1.5;font-weight: normal;vertical-align: top;">
                                <strong style="font-weight: bold;">Bill to:</strong><br />
                                {{ invoiceDetail?.billing_data?.first_name + invoiceDetail?.billing_data?.last_name
                                }}<br />
                                {{ invoiceDetail?.billing_data?.address }}<br />
                                {{ invoiceDetail?.billing_data?.state }}<br />
                                {{ invoiceDetail?.billing_data?.town }}<br />
                                {{ invoiceDetail?.billing_data?.zip_code }}
                            </th>
                            <th scope="col"
                                style="padding: 20px;font-size: 16px;line-height: 1.5;font-weight: normal;text-align: right;vertical-align: top;width: 350px;">
                                <p style="margin: 0 0 4px;white-space: nowrap;">
                                    <strong style="font-weight: bold;">Invoice Number:</strong>
                                    {{invoiceDetail?.invoice_number}}
                                </p>
                                <p style="margin: 0;white-space: nowrap;">
                                    <strong style="font-weight: bold;">Invoice Date:</strong>
                                    {{ invoiceDetail?.issue_date | date }}
                                </p>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="2" style="padding: 20px 0;">
                                <table
                                    style="width: 100%; border:0;border-collapse: collapse;margin: 0 0 20px;padding: 0;">
                                    <thead>
                                        <tr>
                                            <th scope="colgroup" colspan="4"
                                                style="padding: 15px 20px; border-bottom:2px solid #d2d8ed; text-align: left; background: #E5EAFE;">
                                                Summary for {{ invoiceDetail?.issue_date | date }} - {{
                                                invoiceDetail?.due_date | date }}
                                            </th>
                                        </tr>
                                        <tr>
                                            <th scope="col"
                                                style="padding: 15px 20px; border-bottom:1px solid #E5EAFE; text-align: left; width: 50%; background: #F3F6FF;white-space: nowrap;">
                                                Description</th>
                                            <th scope="col"
                                                style="padding: 15px 20px; border-bottom:1px solid #E5EAFE; text-align: right; background: #F3F6FF;white-space: nowrap;">
                                                Quantity</th>
                                            <th scope="col"
                                                style="padding: 15px 20px; border-bottom:1px solid #E5EAFE; text-align: right; background: #F3F6FF;white-space: nowrap;">
                                                Unit Price</th>
                                            <th scope="col"
                                                style="padding: 15px 20px; border-bottom:1px solid #E5EAFE; text-align: right; background: #F3F6FF;white-space: nowrap;">
                                                Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td
                                                style="padding: 15px 20px; border-bottom:1px solid #E5EAFE; text-align: left; width: 50%;">
                                                {{ invoiceDetail?.plan_data?.name }} Plan ({{
                                                getBillingFrequency(invoiceDetail?.plan_data?.plan_type) }}) -
                                                Subscription
                                            </td>
                                            <td
                                                style="padding: 15px 20px; border-bottom:1px solid #E5EAFE; text-align: right;white-space: nowrap;">
                                                1</td>
                                            <td
                                                style="padding: 15px 20px; border-bottom:1px solid #E5EAFE; text-align: right;white-space: nowrap;">
                                                £ {{
                                                invoiceDetail?.plan_data?.price }} </td>
                                            <td
                                                style="padding: 15px 20px; border-bottom:1px solid #E5EAFE; text-align: right;white-space: nowrap;">
                                                £ {{
                                                invoiceDetail?.plan_data?.price }} </td>
                                        </tr>
                                        <ng-container
                                            *ngIf="invoiceDetail.additional_credit && invoiceDetail.additional_credit.length > 0">
                                            <tr *ngFor="let credit of invoiceDetail.additional_credit">
                                                <td
                                                    style="padding: 15px 20px; border-bottom:1px solid #E5EAFE; text-align: left; width: 50%;">
                                                    Additional Credits
                                                </td>
                                                <td
                                                    style="padding: 15px 20px; border-bottom:1px solid #E5EAFE; text-align: right;white-space: nowrap;">
                                                    <!-- Display quantity for each additional credit -->
                                                    {{
                                                    (credit.credit_amount/invoiceDetail.plan_data?.additional_document_price)
                                                    | number:'1.0-0'
                                                    }}
                                                </td>
                                                <td
                                                    style="padding: 15px 20px; border-bottom:1px solid #E5EAFE; text-align: right;white-space: nowrap;">
                                                    <!-- Display unit price for each additional credit -->
                                                    £ {{
                                                    invoiceDetail.plan_data?.additional_document_price }}
                                                </td>
                                                <td
                                                    style="padding: 15px 20px; border-bottom:1px solid #E5EAFE; text-align: right;white-space: nowrap;">
                                                    <!-- Display total unit price -->
                                                    £{{
                                                    credit.credit_amount }}
                                                </td>
                                            </tr>
                                        </ng-container>

                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="2" rowspan="3"
                                                style="padding: 15px 20px;text-align: left;width: 50%;vertical-align: top;border-right: solid 2px #E5EAFE;">
                                                <div
                                                    style="width: 100%; border:0;border-collapse: collapse;margin: 0; padding: 0;box-sizing: border-box;line-height: 1.8;">
                                                    <p
                                                        style="margin: 0;padding: 0;font-weight: bold;font-size: 16px;white-space: nowrap;">
                                                        PAYMENT
                                                        METHOD</p>
                                                    <!-- <p style="margin: 0;padding: 0;white-space: nowrap;">Credit Card</p> -->
                                                    <p style="margin: 0;padding: 0;white-space: nowrap;"><strong>Card
                                                            Type:
                                                        </strong> {{ invoiceDetail?.card?.brand }}</p>
                                                    <p style="margin: 0;padding: 0;white-space: nowrap;"><strong>Card
                                                            Ending: </strong> {{invoiceDetail?.card_last_four}}</p>
                                                </div>
                                            </td>
                                            <td
                                                style="padding: 15px 20px; border-bottom:1px solid #E5EAFE; text-align: right;background: #F3F6FF;white-space: nowrap;">
                                                <strong>Subtotal:</strong>
                                            </td>
                                            <td
                                                style="padding: 15px 20px; border-bottom:1px solid #E5EAFE; text-align: right;background: #F3F6FF;white-space: nowrap;">
                                                £ {{
                                                getTotalCreditAmount() }}</td>
                                        </tr>
                                        <!-- <tr>
                                        <td
                                            style="padding: 15px 20px; border-bottom:1px solid #E5EAFE;background: #F3F6FF; text-align: right;white-space: nowrap;">
                                            <strong>VAT (0%):</strong>
                                        </td>
                                        <td
                                            style="padding: 15px 20px; border-bottom:1px solid #E5EAFE;background: #F3F6FF; text-align: right;white-space: nowrap;">
                                            $0</td>
                                    </tr> -->
                                        <tr>
                                            <td
                                                style="padding: 15px 20px; border-bottom:1px solid #E5EAFE;background: #E5EAFE; text-align: right;font-size: 16px;white-space: nowrap;">
                                                <strong>Total in {{invoiceDetail?.plan_data?.currency}}:</strong>
                                            </td>
                                            <td
                                                style="padding: 15px 20px; border-bottom:1px solid #E5EAFE;background: #E5EAFE; text-align: right;font-size: 16px;white-space: nowrap;">
                                                £ {{
                                                getTotalCreditAmount() }}</td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="2" style="padding: 20px;text-align: center;line-height: 1.5;font-size: 16px;">
                                <h4
                                    style="margin: 0 0 16px;font-weight: bold;font-size: 20px;font-family:Arial, Helvetica, sans-serif;">
                                    Thank you for choosing
                                    ClinicalPad.
                                </h4>
                                <p style="margin: 0 0 16px;">This invoice amount has been paid by Credit Card.</p>
                                <p style="margin: 0 0 16px;">If you have any questions regarding this invoice or any
                                    other
                                    payment
                                    related enquiries,<br /> please visit your account or contact us on <a
                                        style="color: #5223FC;text-decoration: none;"
                                        href="mailto:<EMAIL>"><EMAIL></a></p>
                                <p style="margin: 0 0 16px;">Neurosens Ltd. Company Number ********</p>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</p-dialog>

<p-toast></p-toast>