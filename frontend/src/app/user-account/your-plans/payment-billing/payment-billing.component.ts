import { Component, AfterViewInit, ViewChild, ElementRef, OnInit } from '@angular/core';
import { StripeService } from 'src/app/services/stripe.service';
import { StripeCardNumberElement, StripeElements } from '@stripe/stripe-js';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { UserServiceService } from 'src/app/services/user-service.service';
import { CryptoService } from 'src/app/services/crypto.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { nameValidation, addressValidation, phoneValidator, emailValidation } from 'src/app/validators/constant';
import { environment } from 'src/environments/environment';
import { ErrorMessages } from 'src/app/validators/error-messages';
import { MessageService } from 'primeng/api';
import { CreditsService } from 'src/app/services/credit.service';
import { countries } from 'src/app/validators/countries';

declare let google: any;

@Component({
  selector: 'app-payment-billing',
  templateUrl: './payment-billing.component.html',
  styleUrls: ['./payment-billing.component.scss']
})
export class PaymentBillingComponent implements AfterViewInit, OnInit {

  @ViewChild('addressInput', { static: true })
  public addressInput!: ElementRef;
  @ViewChild('cardElement') cardElement!: ElementRef;
  // @ViewChild('cardNumber') cardNumberElement!: ElementRef;
  // @ViewChild('cardExpiry') cardExpiryElement!: ElementRef;
  // @ViewChild('cardCvc') cardCvcElement!: ElementRef;
  billingInfoForm: FormGroup;
  private readonly googleMapsApiKey: string = environment.googleMapsApiKey;
  errorMessages: any = ErrorMessages.registrationForm;
  cardBrand: string = '';
  card: any;
  country_short_name: any;
  getToken = localStorage.getItem('currentUser');
  credits: any;
  planId: any;
  planName: any;
  apiCallSuccess: boolean = false;

  constructor(
    private stripeService: StripeService,
    private services: UserServiceService,
    private cryptoService: CryptoService,
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private titleService: Title,
    private messageService: MessageService,
    private creditsService: CreditsService
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
    this.billingInfoForm = this.formBuilder.group({
      first_name: ['', [Validators.required, nameValidation]],
      last_name: ['', [Validators.required, nameValidation]],
      email: ['', [Validators.required, Validators.email]],
      address: ['', [Validators.required, addressValidation]],
      phone: ['', [Validators.required, phoneValidator]],
      country: ['', [Validators.required]],
      town: ['', [Validators.required]],
      post_code: ['', [Validators.required]],
      county: ['', [Validators.required]],
      card_holder_name: ['', [Validators.required, nameValidation]]
    });
  }

  ngOnInit(): void {
    this.initAutocomplete();
    this.getUserProfile();
    this.route.queryParams.subscribe(params => {
      this.credits = JSON.parse(params['credits']);
    });
    this.route.queryParams.subscribe(params => {
      this.planId = params['planId'];
      this.planName = params['planName']
    })
  }

  ngAfterViewInit(): void {
    this.initializeStripe();
  }

  getUserProfile() {
    this.services.getUserProfile(this.getToken).subscribe((Response) => {
      const decryptedData = this.cryptoService.decrypt(Response.data);
      if (decryptedData) {
        this.billingInfoForm.patchValue({
          first_name: decryptedData.first_name,
          last_name: decryptedData.last_name,
          email: decryptedData.email,
          phone: decryptedData.phone,
          address: decryptedData.address,
          country: decryptedData.country,
          town: decryptedData.town_id,
          county: decryptedData.county_id,
          post_code: decryptedData.pincode
        })
      }
    })
  }

  private initAutocomplete(): void {
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = `https://maps.googleapis.com/maps/api/js?key=${this.googleMapsApiKey}&libraries=places`;
    script.onload = () => {
      const autocomplete = new google.maps.places.Autocomplete(
        this.addressInput.nativeElement,
        {
          types: ['geocode']
        }
      );
      autocomplete.addListener('place_changed', () => {
        if (this.billingInfoForm) {
          const place = autocomplete.getPlace();

          if (place.geometry) {
            const addressComponents = place.address_components;
            this.country_short_name = place.address_components.find(
              (c: any) => c.types.includes('country')
            )?.short_name || '';
            let selectedAddress = '';
            addressComponents.forEach((component: any) => {
              const componentType = component.types[0];
              switch (componentType) {
                case 'street_number':
                  this.billingInfoForm!.get('address')!.setValue(component.long_name);
                  break;
                case 'route':
                  const existingRoute = this.billingInfoForm!.get('address')!.value || '';
                  this.billingInfoForm!.get('address')!.setValue(existingRoute + ' ' + component.long_name);
                  break;
                case 'postal_code':
                case 'country':
                case 'administrative_area_level_1':
                case 'locality':
                  break;
                default:
                  selectedAddress += component.long_name + ' ';
                  break;
              }
            });
            selectedAddress = selectedAddress.trim();
            this.billingInfoForm!.get('address')!.setValue(selectedAddress);
            this.billingInfoForm!.get('town')!.setValue(place.address_components.find((c: any) => c.types.includes('locality'))?.long_name || '');
            this.billingInfoForm!.get('county')!.setValue(place.address_components.find((c: any) => c.types.includes('administrative_area_level_1'))?.long_name || '');
            this.billingInfoForm!.get('country')!.setValue(place.address_components.find((c: any) => c.types.includes('country'))?.long_name || '');
            this.billingInfoForm!.get('post_code')!.setValue(place.address_components.find((c: any) => c.types.includes('postal_code'))?.long_name || '');
          }
        }
      });
    };
    script.onerror = (error) => {
      console.error('Error loading Google Maps script:', error);
    };
    document.head.appendChild(script);
  }

  initializeStripe() {
    this.stripeService.getStripe().then(stripe => {
      const elements: StripeElements = stripe.elements();
  
      this.card = elements.create('card', {
        hidePostalCode: true,
      });
      this.card.mount(this.cardElement.nativeElement);

      this.card.on('change', (event: any) => {
        this.handleCardValidation(event, 'cardElement');
      });
    })
    .catch(error => {
      console.error('Error initializing Stripe:', error);
    });
  }
  
  handleCardValidation(event: any, fieldId: string) {
    const errorMessageContainer = document.getElementById(fieldId + 'Errors');
    if (errorMessageContainer) {
      errorMessageContainer.textContent = event.error ? event.error.message : '';
    }
  }
  getCountryCode(countryName: string): string {
    const country = countries.find(c => c.name.toLowerCase() === countryName.toLowerCase());
    return country ? country.code : ''; // Return empty string if country is not found
  }

  async onSubmit() {

    if (this.billingInfoForm.valid) {
      if (this.credits) {
        const result = await this.stripeService.createPaymentMethodForCredits(this.card, this.country_short_name, this.billingInfoForm.value);
        if (result.error) {
          console.error(result.error)
        } else { 
          if (!this.country_short_name) {
            this.country_short_name = this.getCountryCode(this.billingInfoForm.value.country);
          }
          let payment_method_id = result.paymentMethod.id;
          let request = {
            "currency": this.credits.currency,
            "paymentMethodId": payment_method_id,
            "amount": this.credits.amount,
            "credit": this.credits.credits,
            "unit_price": this.credits.unit_price,
            "billing_address_info": {
              first_name: this.billingInfoForm.value.first_name,
              last_name: this.billingInfoForm.value.last_name,
              email: this.billingInfoForm.value.email,
              phone: this.billingInfoForm.value.phone,
              address: this.billingInfoForm.value.address,
              country: this.country_short_name,
              state: this.billingInfoForm.value.county,
              town: this.billingInfoForm.value.town,
              zip_code: this.billingInfoForm.value.post_code
            }
          }
          this.services.postBuyCredits(request).subscribe((Response) => {
            this.apiCallSuccess = true;
            const decryptedData = this.cryptoService.decrypt(Response.data);
            const clientSecret = decryptedData.clientSecret;
            const userBuyCreditDataId = decryptedData.userBuyCreditDataId;
            this.stripeService.confirmCardPayment(clientSecret, payment_method_id)
              .then(result => {
                if (result.error) {
                  console.error('Error:', result.error.message);
                  this.router.navigate(['/user/payment-failed'])
                } else {
                  let request = {
                    "userBuyCreditDataId": userBuyCreditDataId
                  }
                  this.services.confirmCreditsPurchase(request).subscribe((Response) => {
                    setTimeout(() => {
                      this.services.getUserProfile(this.getToken).subscribe((Response)=> {
                        const decryptedData = this.cryptoService.decrypt(Response.data);
                        let creditsBalance = decryptedData?.credit_documents + decryptedData?.subscription_documents;
                        this.creditsService.updateCredits(creditsBalance);
                      })
                      this.router.navigate(['/user/payment-successful'])
                    }, 1000);
                  },
                    (error) => {
                      console.log('Error occurred:', error);
                      let errorMsg = error?.error?.msg || 'An error occurred';
                      this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
                    })
                }
              })
              .catch(error => {
                console.error('Error confirming card setup:', error);
              });
          },
          (error) => {
            this.apiCallSuccess = false;
            console.log('Error occurred:', error);
            let errorMsg = error?.error?.msg || 'An error occurred';
            this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
          })
        }
      } else {
        const result = await this.stripeService.createPaymentMethod(this.card, this.country_short_name, this.billingInfoForm.value);
        if (result.error) {
          // this.messageService.add({ severity: 'error', summary: 'Error', detail: result.error.message });
          console.log("Error:", result.error)
        } else {
          if (!this.country_short_name) {
            this.country_short_name = this.getCountryCode(this.billingInfoForm.value.country);
          }
          let request = {
            payment_method_id: result.paymentMethod.id,
            billing_address_info: {
              first_name: this.billingInfoForm.value.first_name,
              last_name: this.billingInfoForm.value.last_name,
              email: this.billingInfoForm.value.email,
              phone: this.billingInfoForm.value.phone,
              address: this.billingInfoForm.value.address,
              country: this.country_short_name,
              state: this.billingInfoForm.value.county,
              town: this.billingInfoForm.value.town,
              zip_code: this.billingInfoForm.value.post_code
            }
          }
          this.services.createCustomer(request).subscribe((Response) => {
            const decryptedData = this.cryptoService.decrypt(Response.data);
            localStorage.setItem('cardDetails', JSON.stringify(result.paymentMethod.card));
            localStorage.setItem('cardHolderName', this.billingInfoForm.value.card_holder_name);
            localStorage.setItem('planId', this.planId);
            this.messageService.add({ severity: 'success', summary: 'Confirmation', detail: 'Please confirm the details' });
            this.router.navigate(['/user/secure/payment-billing/confirmation'])
          },
            (error) => {
              console.log('Error occurred:', error);
              let errorMsg = error?.error?.msg || 'An error occurred';
              this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
            })
        }
      }
    }
    else {
      this.markFormGroupTouched(this.billingInfoForm);
    }
  }
  isFieldInvalid(fieldName: string): any {
    const field = this.billingInfoForm.get(fieldName);
    return field?.invalid && (field?.dirty || field?.touched);
  }
  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
