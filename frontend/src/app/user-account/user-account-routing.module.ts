import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { LoginComponent } from './login/login.component';
import { ForgetPasswordComponent } from './forget-password/forget-password.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { CreateNotes } from './patient-management/notes-management/create-notes';
import { EditPreview } from './patient-management/notes-management/edit-preview';
import { PrintPDF } from './patient-management/notes-management/print-pdf';
import { AuthGuard } from './guards/auth.guard';
import { LoggedInAuthGuard } from './guards/logged-in-auth.guard';
import { UserDashboardComponent } from './user-dashboard/user-dashboard.component';
import { LayoutComponent } from './layout/layout.component';
import { PatientManagementComponent } from './patient-management/patient-management.component';
import { MyprofileComponent } from './myprofile/myprofile.component';
import { NotesManagementComponent } from './patient-management/notes-management/notes-management.component';
import { ViewPatientComponent } from './patient-management/view-patient/view-patient.component';
import { VerifyEmailComponent } from './verify-email/verify-email.component';
import { RolesAndRightsComponent } from './roles-and-rights/roles-and-rights.component';
import { EditRoleComponent } from './roles-and-rights/edit-role/edit-role.component';
import { AddNewPatientComponent } from './patient-management/add-new-patient/add-new-patient.component';
import { EditPatientComponent } from './patient-management/edit-patient/edit-patient.component';
import { UserManagementComponent } from './user-management/user-management.component';
import { InviteUserComponent } from './user-management/invite-user/invite-user.component';
import { EditUserComponent } from './user-management/edit-user/edit-user.component';
import { SupportComponent } from './support/support.component';
import { DoctorDashboardComponent } from './doctor-dashboard/doctor-dashboard.component';
import { NavDemoComponent } from './patient-management/nav-demo/nav-demo.component';
import { UsernotedocumentComponent } from './usernotedocument/usernotedocument.component';
import { ViewNotesComponent } from './view-notes/view-notes.component';
import { UserVerifyOtpComponent } from './user-verify-otp/user-verify-otp.component';
import { UploadPatientComponent } from './patient-management/upload-patient/upload-patient.component';
import { PaymentBillingComponent } from './your-plans/payment-billing/payment-billing.component';
import { ConfirmationComponent } from './your-plans/confirmation/confirmation.component';
import { MySubscriptionsComponent } from './your-plans/my-subscriptions/my-subscriptions.component';
import { ViewMySubscriptionComponent } from './your-plans/view-my-subscription/view-my-subscription.component';
import { AddCreditsComponent } from './add-credits/add-credits.component';
import { PaymentSuccessfulComponent } from './payment-successful/payment-successful.component';
import { PaymentFailedComponent } from './payment-failed/payment-failed.component';
import { UserPricingPlansComponent } from './user-pricing-plans/user-pricing-plans.component';
import { CreditsListComponent } from './credits-list/credits-list.component';
import { TransactionFailedComponent } from './transaction-failed/transaction-failed.component';

const routes: Routes = [
    { path: 'usernotedocumenthgerertfgfg', component:UsernotedocumentComponent},
    { path: 'login', component: LoginComponent, canActivate: [LoggedInAuthGuard], data: { title: 'Login | Clinical Pad' } },
    { path: 'user-verify-otp', component: UserVerifyOtpComponent, canActivate: [AuthGuard], data: { title: 'Verify Otp | Clinical Pad' } },
    { path: 'forgot-password', component: ForgetPasswordComponent, canActivate: [LoggedInAuthGuard], data: { title: 'Forgot Password | Clinical Pad' } },
    { path: 'reset-password', component: ResetPasswordComponent, canActivate: [LoggedInAuthGuard], data: { title: 'Reset Password | Clinical Pad' } },
    {
        path: 'user', component: LayoutComponent, children: [
            { path: 'user-pricing-plans', component: UserPricingPlansComponent, canActivate: [AuthGuard], data: { title: ' Pricing Plans | Clinical Pad' } },
            { path: 'user-dashboard', component: UserDashboardComponent, canActivate: [AuthGuard], data: { title: ' Dashboard | Clinical Pad' } },
            { path: 'doctor-dashboard', component: DoctorDashboardComponent, canActivate: [AuthGuard], data: { title: ' Dashboard | Clinical Pad' } },
            {
                path: 'secure', component: NavDemoComponent, children: [
                    { path: 'patient-management/view-patient', canActivate: [AuthGuard], component: ViewPatientComponent, data: { title: ' View Patient | Clinical Pad' } },
                    { path: 'patient-management/upload-patient', canActivate: [AuthGuard], component: UploadPatientComponent, data: { title: ' Upload Patient | Clinical Pad' } },
                    { path: 'patient-management/add-new-patient', canActivate: [AuthGuard], component: AddNewPatientComponent, data: { title: ' Add New Patient | Clinical Pad' } },
                    { path: 'patient-management/edit-patient', canActivate: [AuthGuard], component: EditPatientComponent, data: { title: ' Edit Patient | Clinical Pad' } },
                    { path: 'patient-management', component: PatientManagementComponent, data: { title: 'Patient Management | Clinical Pad' } },
                    {
                        path: 'patient-management/notes', component: NotesManagementComponent, canActivate: [AuthGuard], children: [
                            { path: 'create-notes', component: CreateNotes, canActivate: [AuthGuard], data: { title: ' Create Notes | Clinical Pad' } },
                            { path: 'edit-preview', component: EditPreview, canActivate: [AuthGuard], data: { title: ' Edit & Preview | Clinical Pad' } },
                            { path: 'print-document', component: PrintPDF, canActivate: [AuthGuard], data: { title: ' Share | Clinical Pad' } },]
                    },
                ],
                canActivate: [AuthGuard], data: { title: 'Patient Management | Clinical Pad' }
            },
            {
                path: 'secure', component: NavDemoComponent, canActivate: [AuthGuard], children: [
                    { path: 'roles-management', component: RolesAndRightsComponent, data: { title: 'Roles and Rights | Clinical Pad' } },
                    { path: 'roles-management/edit-role', component: EditRoleComponent, canActivate: [AuthGuard], data: { title: ' Edit Role | Clinical Pad' } },
                ], data: { title: ' Roles and Rights | Clinical Pad' }
            },
            {
                path: 'secure', component: NavDemoComponent, canActivate: [AuthGuard], children: [
                    { path: 'payment-billing', component: PaymentBillingComponent, data: { title: 'Payment & Billing | Clinical Pad' } },
                    { path: 'payment-billing/confirmation', component: ConfirmationComponent, data: { title: 'Confirmation| Clinical Pad' } },
                    { path: 'payment-billing/my-subscriptions', component: MySubscriptionsComponent, data: { title: 'My Subscriptions | Clinical Pad' } },
                    { path: 'payment-billing/view-my-subscription', component: ViewMySubscriptionComponent, data: { title: 'View My Subscription | Clinical Pad' } },
                ], data: { title: ' Payment & Billing | Clinical Pad' }
            },
            {
                path: 'secure', component: NavDemoComponent, canActivate: [AuthGuard], children: [
                    { path: 'user-management/invite-user', component: InviteUserComponent, canActivate: [AuthGuard], data: { title: ' Invite User | Clinical Pad' } },
                    { path: 'user-management/edit-user', component: EditUserComponent, canActivate: [AuthGuard], data: { title: ' Edit User | Clinical Pad' } },
                    { path: 'user-management', component: UserManagementComponent, canActivate: [AuthGuard], data: { title: ' User Management | Clinical Pad' } },

                ], data: { title: ' User Management | Clinical Pad' }
            },
            { path: 'support', component: SupportComponent, canActivate: [AuthGuard], data: { title: ' Support | Clinical Pad' } },
            { path: 'credits-listing', component: CreditsListComponent, canActivate: [AuthGuard], data: { title: ' Credits Purchased | Clinical Pad' } },
            { path: 'my-profile', canActivate: [AuthGuard], component: MyprofileComponent, data: { title: ' My Profile | Clinical Pad' } },
            { path: 'view-note', canActivate: [AuthGuard], component: ViewNotesComponent, data: { title: 'View Note | Clinical Pad' } },
            { path: 'add-credits', component: AddCreditsComponent, data: { title: 'Add Credits | Clinical Pad' } },
            { path: 'payment-successful', component: PaymentSuccessfulComponent, data: { title: 'Payment Successful | Clinical Pad' } },
            { path: 'payment-failed', component: PaymentFailedComponent, data: { title: 'Payment Failed | Clinical Pad' } },
            { path: 'transaction-failed', component: TransactionFailedComponent, data: { title: 'Transaction Failed | Clinical Pad' } },
        ]
    },
    { path: 'activate-account', component: VerifyEmailComponent, data: { title: 'Email Verification | Clinical Pad' } },
];
@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
    providers: [
        AuthGuard,
        LoggedInAuthGuard
    ]
})
export class UserAccountRoutingModule { }