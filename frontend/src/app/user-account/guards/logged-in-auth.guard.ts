import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { CommonService } from '../../services/common.service';

@Injectable({
  providedIn: 'root'
})
export class LoggedInAuthGuard implements CanActivate {
  constructor(private router: Router) {}

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    const roleName = localStorage.getItem('roleName');
    
    if (localStorage.getItem('currentUser')) {
      if (state.url.includes("/login/")) {
        return true;
      } else {
        // Redirect based on roleName
        if (roleName === 'Clinician') {
          this.router.navigate(['user/doctor-dashboard']);
        } else {
          this.router.navigate(['user/user-dashboard']);
        }
        return false;
      }
    } else {
      return true;
    }
  }
}
