import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { MessageService } from 'primeng/api';
import { CryptoService } from 'src/app/services/crypto.service';
import { UserServiceService } from 'src/app/services/user-service.service';
import { emailValidation } from 'src/app/validators/constant';
import { ErrorMessages } from 'src/app/validators/error-messages';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-forget-password',
  templateUrl: './forget-password.component.html',
  styleUrls: ['./forget-password.component.scss']
})
export class ForgetPasswordComponent {

  siteKey: any = environment.siteKey;
  forgetForm: FormGroup;
  public errorMessages = ErrorMessages.loginForm;
  constructor(
    private titleService: Title,
    private router: ActivatedRoute,
    private fb: FormBuilder,
    private services: UserServiceService,
    private messageService: MessageService,
    private cryptoService: CryptoService
  ) {
    this.titleService.setTitle(this.router.snapshot.data['title']);
    this.forgetForm = this.fb.group({
      email: ['', [Validators.required, Validators.email, emailValidation]],
      recaptcha: ['', Validators.required]
    });
  }

  isFieldInvalid(fieldName: string): any {
    const field = this.forgetForm.get(fieldName);
    return field?.invalid && (field?.dirty || field?.touched);
  }

  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  onSubmit() {
    if (this.forgetForm.valid) {
      let data = {
        email: this.forgetForm.value.email
      }
      const encryptedData = this.cryptoService.encrypt(data);
      let request = {
        encrypet: encryptedData
      }
      this.services.postForgetPassword(request).subscribe((Response) => {
        this.forgetForm.reset();
        this.messageService.add({ severity: 'success', summary: 'Success', detail: Response.msg });
      },
        (error) => {
          console.log('Error occurred:', error);
          let errorMsg = error?.error?.msg || 'An error occurred';
          this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
        })
    } else {
      this.markFormGroupTouched(this.forgetForm);
    }
  }
}
