@import "../../../assets/css/variables";
::ng-deep {
    :root {
        --ck-border-radius: #{$border-radius};
        --ck-color-base-border: #{$light};
        --ck-color-text: #{$body-color};
        --ck-color-base-text: #{$body-color};
        --ck-color-button-on-color:#{$primary};
        --ck-color-button-default-hover-background: #{$light};
    }

    .ck.ck-editor__top {
        .ck-sticky-panel {
            .ck-toolbar {
                background-color: $light;
                padding-top: .5rem;
                padding-bottom: .5rem;
            }
        }
    }

    .ck-editor__editable {
        min-height: 200px;
    }
    .ck-powered-by{
        display: none !important;
    }
}
.theme-profile-image-cover{
    width: 150px;
}
