import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { MessageService } from 'primeng/api';
import { CryptoService } from 'src/app/services/crypto.service';
import { UserServiceService } from 'src/app/services/user-service.service';
import { passwordValidator } from 'src/app/validators/constant';
import { ErrorMessages } from 'src/app/validators/error-messages';
import { SvgIcons } from 'src/app/validators/svg-icons';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss']
})
export class ResetPasswordComponent implements OnInit {

  siteKey: any = environment.siteKey;
  svgIcons = SvgIcons;
  resetForm: FormGroup;
  public errorMessages = ErrorMessages.registrationForm;
  token: any;
  public passwordInfoMessage = ErrorMessages.toolTips.passwordInfoMessage;
  constructor(
    private titleService: Title,
    private router: ActivatedRoute,
    private fb: FormBuilder,
    private services: UserServiceService,
    private messageService: MessageService,
    private cryptoService: CryptoService
  ) {
    this.titleService.setTitle(this.router.snapshot.data['title']);
    this.resetForm = this.fb.group({
      password: ['', [Validators.required,passwordValidator]],
      confirmPassword: ['', [Validators.required]],
      recaptcha: ['', Validators.required],
    } ,
    {
      validators: this.passwordMatchValidator
    });
  }

  ngOnInit(): void {
    this.router.queryParams.subscribe(params => {
      let request = params['t'];
      this.token = request;
    });
  }

  isFieldInvalid(fieldName: string): any {
    const field = this.resetForm.get(fieldName);
    return field?.invalid && (field?.dirty || field?.touched);
  }

  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  passwordMatchValidator(control: FormGroup) {
    const password = control.get('password');
    const confirmPassword = control.get('confirmPassword');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
    }
  }

  onSubmit() {
    if (this.resetForm.valid) {
      let data = {
        token: this.token,
        password: this.resetForm.value.password
      }
      const encryptedData = this.cryptoService.encrypt(data);
      let request = {
        encrypet: encryptedData
      }
      this.services.postResetPassword(request).subscribe((Response) => {
        this.resetForm.reset();
        this.messageService.add({ severity: 'success', summary: 'Success', detail: Response.msg });
      },
        (error) => {
          console.log('Error occurred:', error);
          let errorMsg = error?.error?.msg || 'An error occurred';
          this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
        })
    } else {
      this.markFormGroupTouched(this.resetForm);
    }
  }
}
