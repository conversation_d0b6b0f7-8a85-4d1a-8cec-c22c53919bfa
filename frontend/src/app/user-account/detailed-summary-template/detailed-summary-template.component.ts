import { Component, ElementRef, Renderer2, ViewChild } from '@angular/core';
import { DomSanitizer, Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { CreditsService } from 'src/app/services/credit.service';
import { CryptoService } from 'src/app/services/crypto.service';
import { RegistrationService } from 'src/app/services/registration.service';
import { UserServiceService } from 'src/app/services/user-service.service';
import { ErrorMessages } from 'src/app/validators/error-messages';
import { SvgIcons } from 'src/app/validators/svg-icons';

@Component({
  selector: 'app-detailed-summary-template',
  templateUrl: './detailed-summary-template.component.html',
  styleUrls: ['./detailed-summary-template.component.scss']
})
export class DetailedSummaryTemplateComponent {
  value!: number;
  bodyContent: string = '';
  svgIcons = SvgIcons;
  visible: boolean = false;
  visibleDisclaimerDialog: boolean = false;
  isButtonActive: boolean = true;
  clinicId = localStorage.getItem('clinicId');
  note_id = localStorage.getItem('note_id');
  phone = localStorage.getItem('phone')
  email = localStorage.getItem('email');
  clinic_name: any;
  letterData: any;
  previewText: any;
  text: any;
  generatedText: any;
  formattedDate: any;
  convertedText: string = '';
  generatedHtml: string = '';
  logoImage: string = '';
  signImage: string = '';
  feedback: any;
  checkValue: boolean = false;
  quillConfiguration: any = {
    toolbar: [
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      [{ 'font': [] }],
      ['bold', 'italic', 'underline'],
      [{ color: [] }, { background: [] }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ 'align': [] }],
      ['link'],
      ['code-block'],
      ['clean'],
    ],
  }
  public starMessage = ErrorMessages.toolTips.starMessage;
  public thumbsMessage = ErrorMessages.toolTips.thumbsMessage;
  public headerPreview: any;
  public footerPreview: any;
  // public isAttempts = false;
  @ViewChild('headerContent', { static: true }) divheaderContent!: ElementRef;
  @ViewChild('footerContent', { static: true }) divfooterContent!: ElementRef;
  @ViewChild('previewContent', { static: true }) divpreviewContent!: ElementRef;
  letterType: string = '';
  logoImageSrc: any;
  patientId: any;
  public headerContentHtml: any;
  public footerContentHtml: any;
  headerContentHtmlWithCss: any;
  getToken: any = localStorage.getItem('currentUser');


  constructor(
    private titleService: Title,
    private route: ActivatedRoute,
    private services: UserServiceService,
    private renderer: Renderer2,
    private router: Router,
    private messageService: MessageService,
    private registrationService: RegistrationService,
    private cryptoService: CryptoService,
    public sanitizer: DomSanitizer,
    private creditsService: CreditsService
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
  }

  ngOnInit(): void {
    this.getNoteDetails();
    this.getUserSettings();
    setTimeout(() => {
      this.headerContentHtml = this.divheaderContent.nativeElement.innerHTML;
      this.headerContentHtmlWithCss = this.sanitizer.bypassSecurityTrustHtml(this.headerContentHtml)
      this.footerContentHtml = this.divfooterContent.nativeElement.innerHTML;
    }, 1000);

    this.divheaderContent.nativeElement.addEventListener('input', () => {
      this.headerContentHtml = this.divheaderContent.nativeElement.innerHTML;
    });
    this.divfooterContent.nativeElement.addEventListener('input', () => {
      this.footerContentHtml = this.divfooterContent.nativeElement.innerHTML;
    });
  }

  getNoteDetails() {
    this.services.getGeneratedLetter(this.note_id).subscribe((Response) => {
      const decryptedData = this.cryptoService.decrypt(Response.data);
      this.letterData = decryptedData;
      this.generatedText = this.letterData?.system_genrated_body_content;
      this.clinic_name = this.letterData?.clinic?.name;
      this.previewText = this.generatedText?.replace(/\n/g, '<br>');
      this.text = `<strong><u>Observations</u></strong></p><br>` + this.previewText;
      const createdAt = this.letterData?.createdAt
      const dateObject = new Date(createdAt);
      this.formattedDate = `${dateObject.getUTCDate().toString().padStart(2, '0')}/${(dateObject.getUTCMonth() + 1).toString().padStart(2, '0')}/${dateObject.getUTCFullYear()}`;
      this.letterType = this.letterData?.letter_type;
      this.patientId = this.letterData?.patient_id
    })
  }

  goBack() {
    this.router.navigate(['/user/secure/patient-management/notes/create-notes'], {
      queryParams: { generatedNote: 'previousNote', patientId: this.patientId},
    });
  }

  formattedDateFun(date: string) {
    if (!date) return;

    const dateObject = new Date(date);
    return `${dateObject.getUTCDate().toString().padStart(2, '0')}/${(dateObject.getUTCMonth() + 1).toString().padStart(2, '0')}/${dateObject.getUTCFullYear()}`;
  }

  addressDetail(address: any): string {
    if (!address) return '';
    const addressStr = address.address ?? '';
    const cityStr = address.city ?? '';
    const countyStr = address.state ?? '';
    const countryStr = address.country ?? '';
    const postCodeStr = address.post_code ?? '';
    const combinedAddress = [addressStr, cityStr, countyStr, countryStr, postCodeStr].filter(part => part).join(' ');
    return `${combinedAddress}`;
  }

  createHtmlFile(content: string): string {
    const htmlContent = `
          <!DOCTYPE html>
          <html>
          <head>
          </head>
          <body>
              ${content}
          </body>
          </html>
      `;
    return htmlContent.replace(/"/g, "'").replace(/\n/g, '');
  }
  private getImgElementFromHTML(htmlString: string): HTMLImageElement | null {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, 'text/html');
    return doc.querySelector('img');
  }
  getUserSettings() {
    let data = {
      clinic_id: this.clinicId
    }
    const encryptedData = this.cryptoService.encrypt(data);
    let request = {
      encrypet: encryptedData
    }
    this.services.getUserSettings(request).subscribe((Response: any) => {
      const decryptedData = this.cryptoService.decrypt(Response.data);
      this.logoImage = decryptedData?.header_image;
      this.signImage = decryptedData?.footer_image;
      this.headerPreview = this.sanitizer.bypassSecurityTrustHtml(decryptedData?.letter_header_template);
      this.footerPreview = this.sanitizer.bypassSecurityTrustHtml(decryptedData?.letter_footer_template);
      const imgElement = this.getImgElementFromHTML(decryptedData?.letter_footer_template);
      if (imgElement) {
        this.logoImageSrc = imgElement.getAttribute('src');
      }
    })
  }

  onSubmit() {
    const headerContent = this.divheaderContent.nativeElement.innerHTML;
    const footerContent = this.divfooterContent.nativeElement.innerHTML;
    const previewContent = this.divpreviewContent.nativeElement.innerHTML;
    this.generatedHtml = this.createHtmlFile(previewContent);
    const plainText = this.text.replace(/<br>/g, '\n');
    this.convertedText = plainText.replace(/<\/?p>/g, '');
    const updatedBodyContentText = this.convertedText ? this.convertedText : 'null';
    if(this.checkValue == true){
      this.feedback = 1
    } else {
      this.feedback = 0
    }
    let data: { [key: string]: any } = {
      updated_body_content: this.generatedHtml,
      updated_body_content_text: updatedBodyContentText,
      header_content: headerContent,
      footer_content: footerContent,
      feedback: this.feedback,
      edit_view_letter: 0
    }
    if (this.value) {
      data['approval_rating'] = this.value;
    }
    const encryptedData = this.cryptoService.encrypt(data);
    let request = {
      encrypet: encryptedData
    }
    this.services.updateGeneratedLetter(this.note_id, request).subscribe((Response: any) => {
      if (Response.statusCode === 200) {
        this.registrationService.setCompletedStep(2);
        this.services.getUserProfile(this.getToken).subscribe((Response)=> {
          const decryptedData = this.cryptoService.decrypt(Response.data);
          let creditsBalance = decryptedData?.credit_documents + decryptedData?.subscription_documents;
          let creditsBalanceForUsers = decryptedData?.owner_info?.credit_documents + decryptedData?.owner_info?.subscription_documents;
          if(creditsBalance >= 0){
            this.creditsService.updateCredits(creditsBalance);
          }
          if(creditsBalanceForUsers > 0) {
            this.creditsService.updateCredits(creditsBalanceForUsers);
          }
        })
        this.router.navigate(['/user/secure/patient-management/notes/print-document']);
        this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Letter Confirmed Successfully' });
        localStorage.removeItem('generatedNote');
        localStorage.removeItem('letterType');
      }
    },
      (error) => {
        console.log('Error occurred:', error);
        let errorMsg = error?.error?.msg || 'An error occurred';
        this.messageService.add({ severity: 'error', summary: 'Error', detail: "Preferable format feedback is Required" });
      })
  }
}
