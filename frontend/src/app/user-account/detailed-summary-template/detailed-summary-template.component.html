<div class="card border-0">
    <div class="bg-light border-2 border-bottom border-primary card-header">
        <div class="p-md-1">
            <div class="hstack gap-2 mb-2">
                <div class="fw-bold me-auto" style="text-align: center;">Patient Information</div>
            </div>
            <div class="fw-medium lh-lg table-responsive mb-3" #headerContent contenteditable="true">
                <table
                    class="w-100 border-dark-subtle table table-bordered table-layout-fixed table-flex-below-sm table-light mb-0"
                    aria-hidden="true" style="width: 100%; line-height: 1.8;">
                    <tbody>
                        <tr>
                            <td>
                                <strong>First Name</strong>
                                <div class="text-truncate fw-normal" style="font-weight: normal;">{{ letterData?.patient?.first_name }}</div>
                            </td>
                            <td>
                                <strong>Last Name</strong>
                                <div class="text-truncate fw-normal" style="font-weight: normal;">{{ letterData?.patient?.last_name }}</div>
                            </td>
                            <td>
                                <strong>Date of Birth</strong>
                                <div class="text-truncate fw-normal" style="font-weight: normal;">{{formattedDateFun(letterData?.patient?.dob)}}
                                </div>
                            </td>
                            <td>
                                <strong>Gender</strong>
                                <div class="text-truncate fw-normal" style="font-weight: normal;">{{ letterData?.patient?.gender || '-'}}</div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <strong>Clinician/Assessor</strong>
                                <div class="text-truncate fw-normal" style="font-weight: normal;">{{
                                    (letterData?.patient?.users?.first_name + ' ' +
                                    letterData?.patient?.users?.last_name) ||
                                    '-'}}</div>
                            </td>
                            <td colspan="2">
                                <strong>Other</strong>
                                <div class="text-truncate fw-normal" style="font-weight: normal;">-</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="fw-bold mb-n2" style="text-align: center;">Summary</div>
        </div>
    </div>
    <div class="card-body">
        <div class="theme-edit-view-cover">
            <p-editor [(ngModel)]="text" [modules]="quillConfiguration">
                <ng-template pTemplate="header"></ng-template>
            </p-editor>
        </div>
    </div>
    <div class="bg-light border-2 border-bottom border-primary border-top card-footer theme-border-bootom-6">
        <div class="p-md-1">
            <div class="letter-footer" #footerContent>
                <div class="hstack gap-2 mb-4">
                    <div class="me-auto">
                        <div class="fw-bold mb-1">Signature</div>
                        <div>
                            <img alt="Signature" [src]="logoImageSrc" style="height:50px; width:100px" />
                        </div>
                    </div>
                    <div>
                        <div class="fw-bold mb-1">Date/Time</div>
                        <div>{{ formattedDate }}</div>
                    </div>
                </div>
            </div>
            <div class="flex-wrap gap-3 hstack">
                <div class="flex-wrap gap-3 gap-sm-4 vstack me-auto">


                    <div class="flex-wrap gap-2 hstack me-auto">

                        <div class="btn h-40 p-1 w-40"><p-checkbox value="check" name="checkValue" inputId="check" [(ngModel)]="checkValue"
                            styleClass="mt-1" [binary]="true"></p-checkbox></div>
                        <span class="fw-medium">Do you prefer this format?<a
                                appBsTooltip data-bs-html="true" data-bs-toggle="tooltip" data-bs-placement="top"
                                title={{thumbsMessage}}
                                class="btn btn-link text-body align-bottom d-inline-block infoIcon ms-1 p-0">
                                <span class="theme-info-icon" [innerHTML]="svgIcons.infoIcon | safeHtml"></span>
                                <span class="visually-hidden">{{thumbsMessage}}</span>
                            </a></span>

                    </div>
                    <div class="flex-wrap gap-2 hstack me-auto">


                        <p-rating [(ngModel)]="value" class="d-inline-flex">
                            <ng-template pTemplate="cancel">
                                <span aria-hidden="true" class="d-inline-flex theme-start-color"
                                    [innerHTML]="svgIcons.starIcon | safeHtml"></span>
                            </ng-template>
                            <ng-template pTemplate="onicon">
                                <span aria-hidden="true" class="d-inline-flex theme-start-fill-color"
                                    [innerHTML]="svgIcons.starIcon | safeHtml"></span>
                            </ng-template>
                            <ng-template pTemplate="officon">
                                <span aria-hidden="true" class="d-inline-flex theme-start-color"
                                    [innerHTML]="svgIcons.starIcon | safeHtml"></span>
                            </ng-template>
                        </p-rating>
                        <span class="fw-medium">Rate this letter<a appBsTooltip data-bs-html="true"
                                data-bs-toggle="tooltip" data-bs-placement="top" title={{starMessage}}
                                class="btn btn-link text-body align-bottom d-inline-block infoIcon ms-1 p-0">
                                <span class="theme-info-icon" [innerHTML]="svgIcons.infoIcon | safeHtml"></span>
                                <span class="visually-hidden">{{starMessage}}</span>
                            </a></span>
                    </div>

                </div>
                <div class="flex-wrap gap-3 gap-sm-4 hstack mt-auto">
                    <button (click)="goBack()" class="btn btn-info text-white me-auto">Back</button>
                    <button (click)="visible = true" class="btn btn-info text-white me-auto"> Save & Preview</button>
                    <button (click)="onSubmit()" class="btn btn-primary">Save & Confirm Letter</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Dialog -->
<p-dialog styleClass="theme-modal-main theme-modal-xl theme-modal-header-close theme-preview-modal" [modal]="true"
    [draggable]="false" [resizable]="false" [dismissableMask]="true" [(visible)]="visible">
    <div #previewContent>
        <div class="theme-modal-body pt-4">
            <section-2>
                <table style="border: 1px solid #adb5bd;width: 100%;border-collapse: collapse; color: #393956;">
                    <thead>
                        <tr>
                            <th>
                                <table class="table-layout-fixed table-flex-below-sm"
                                    style="margin-bottom: 0;width: 100%;border-collapse: collapse;border: 0;text-align: left;background-color: #e5eafe;">
                                    <thead>
                                        <tr>
                                            <th style="padding: 16px; text-align: center;">
                                                <strong>Patient Information</strong>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="preview-editable-info-content" [innerHTML]="headerContentHtmlWithCss">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </th>
                        </tr>
                        <tr>
                            <td style="border-bottom: 1px solid #adb5bd; padding: 16px;text-align: center;background-color: #e5eafe;">
                                <strong>Summary</strong>
                            </td>
                        </tr>
                    </thead>
                    <tbody>
                            <tr>
                                <td style="padding: 12px;">
                                    <div [innerHtml]="sanitizer.bypassSecurityTrustHtml(text)"></div>
                                </td>
                            </tr>
                        <tr>
                            <td style="padding: 0;">
                                <table class="table-layout-fixed table-flex-below-sm"
                                    style="margin-bottom: 0;width: 100%;border-collapse: collapse;border: 0;background-color: #e5eafe;"
                                    aria-hidden="true">
                                    <tbody>
                                        <tr>
                                            <td class="w-75"
                                                style="border: 1px solid #adb5bd; padding: 12px; border-left: 0; width: 75%;border-bottom: 0;">
                                                <div><strong>Signature</strong></div>
                                                <div>
                                                    <img alt="Signature" [src]="logoImageSrc"
                                                        style="height:50px; width:100px" />
                                                </div>
                                            </td>
                                            <td
                                                style="border: 1px solid #adb5bd; padding: 12px; border-right: 0;border-bottom: 0;">
                                                <div><strong>Date/Time</strong></div>
                                                <div>{{ formattedDate }}</div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section-2>
        </div>
    </div>
</p-dialog>