<!-- Pricing Section -->
<div class="d-flex flex-column pageContainer px-3 pb-3 h-100 overflow-y-scroll">
    <div class="contentHeader py-3 my-1">
        <div class="hstack gap-2 flex-wrap justify-content-between">
            <div class="theme-left-header">
                <h1 class="fw-normal h3 mb-0">Subscription Plans</h1>
            </div>
        </div>
    </div>
    <div class="flex-grow-1 pageContent position-relative">
        <div class="card border-0 w-100 h-100">
            <div class="card-body w-100 h-100">
                <section class="pricing-section text-center pt-3">
                    <h1 class="mb-1 fs-2 fw-normal">Pick Your Plan</h1>
                    <div class="fw-medium fs-5 mb-4 lh-sm">Choose the plan that best suits your needs</div>

                    <p-carousel [value]="plans" [numVisible]="4" [numScroll]="1" [responsiveOptions]="responsiveOptions"
                        [showNavigators]="false" [ngClass]="{'p-indicator-length': plans.length <= 4}">
                        <ng-template pTemplate="header">
                            <div class="small fw-semibold text-primary mb-1">Yearly 10% off</div>
                            <div
                                class="align-items-center d-flex flex-column flex-md-row flex-wrap gap-2 justify-content-center mb-4 ng-star-inserted position-relative">
                                <!-- <div
                                    class="float-md-start mt-md-2 positioned-currency-dropdown pt-md-1 start-0 theme-select-rounded top-0 w-100 z-2 text-start ps-md-3">
                                    <p-dropdown placeholder="Currency" [options]="currencyOptions"
                                        [(ngModel)]="selectedCurrency"
                                        (onChange)="onCurrencyChange($event)"></p-dropdown>
                                </div> -->
                                <div
                                    class="switch-wrapper position-relative d-inline-flex p-1 border rounded-5 bg-white overflow-hidden">
                                    <input id="monthly" type="radio" name="switch"
                                        class="position-absolute z-n1 opacity-0 invisible" checked
                                        (change)="onPlanTypeChange('Monthly')">
                                    <input id="yearly" type="radio" name="switch"
                                        class="position-absolute z-n1 opacity-0 invisible"
                                        (change)="onPlanTypeChange('Yearly')">
                                    <label class="fs-5 fw-semibold z-1 rounded-5" for="monthly">Monthly</label>
                                    <label class="fs-5 fw-semibold z-1 rounded-5" for="yearly">Yearly</label>
                                    <span class="highlighter position-absolute rounded-5 bg-primary"></span>
                                </div>
                            </div>
                        </ng-template>

                        <ng-template let-plan pTemplate="item">
                            <div class="plans-item p-sm-1 p-md-3 text-start h-100">
                                <div class="plans-item-content h-100" [class.active]="plan.id == selectedPlanId">
                                    <div class="fs-4 mb-2 fw-medium d-flex flex-wrap gap-2 align-items-center">
                                        <span class="me-auto">{{ plan.name }}</span>
                                        <span *ngIf="plan.name == 'Basic'"
                                            class="fs-12 fw-semibold rounded px-2 py-1 text-primary border border-primary popular-label">Most
                                            Popular</span>
                                    </div>
                                    <p class="fs-13 fw-semibold mb-3">{{ plan.description }}</p>
                                    <div class="fw-bold fs-4 mb-3">
                                        <span *ngIf="plan.name === 'Free'">Pay As You Go</span>
                                        <span *ngIf="plan.id === 99889989">Exclusive</span>
                                        <span *ngIf="plan.name !== 'Free' && plan.id !== 99889989">£{{
                                            plan.price }}
                                        </span>
                                        <small *ngIf="plan.name !== 'Free' && plan.id != 99889989"
                                            class="fw-normal">per
                                            {{ getPlanType(plan.plan_type) }}</small>
                                    </div>
                                    <div class="px-2 mb-3">
                                        <button class="w-100 btn btn-primary" (click)="onSubmit(plan.id,plan.name)"
                                            *ngIf="plan.id != 99889989">Select and Continue</button>
                                        <button class="w-100 btn btn-primary" (click)="showContactDialog()"
                                            *ngIf="plan.id === 99889989">Contact Sales</button>
                                    </div>
                                    <div class="plan-content fs-13 fw-medium" *ngIf="plan.id !== 99889989">
                                        <div class="fs-14 fw-bold mb-2 pb-1">Includes:</div>
                                        <ul
                                            class="list-check-circle-style d-flex flex-column gap-3 mb-2 lh-sm list-unstyled">
                                            <li *ngIf="plan.free_trial_docs !== 0">Free Trial - {{plan.free_trial_docs}}
                                                clinical documents</li>
                                            <li *ngIf="plan.included_docs !== 0">{{plan.included_docs}} clinical
                                                documents included per {{ getPlanType(plan.plan_type) }}</li>
                                            <li>Additional Documents at £
                                                {{plan.additional_document_price}} per document
                                            </li>
                                            <li>Up to {{plan.dictation_limit_per_doc_min}} minutes of dictation per
                                                documents</li>
                                            <li *ngIf="plan.max_team_members !== 0">Invite up to
                                                {{plan.max_team_members}} additional team members</li>
                                            <li *ngIf="plan.free_support">Free Support</li>
                                            <li *ngIf="plan.priority_feature_access">Priority access to new feature
                                                releases</li>
                                        </ul>
                                    </div>
                                    <div class="plan-content fs-13 fw-medium" *ngIf="plan.id === 99889989">
                                        <div class="fs-14 fw-bold mb-2 pb-1">Includes:</div>
                                        <ul
                                            class="list-check-circle-style d-flex flex-column gap-3 mb-2 lh-sm list-unstyled">
                                            <li>Free Trial - Customised</li>
                                            <li>Custom clinical
                                                documents included per month</li>
                                            <li>Additional Documents at custom amount
                                            </li>
                                            <li>Up to customised minutes of dictation per
                                                documents</li>
                                            <li>Invite unlimited team members</li>
                                            <li>Dedicated Account Manager</li>
                                            <li>Cancel Anytime</li>
                                            <li>Priority access to new feature
                                                releases</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </p-carousel>
                    <div *ngIf="noPlanMessage" class="fw-medium fs-4 mb-5 lh-sm">No Plan Found.</div>
                </section>
            </div>
        </div>
    </div>
</div>
 <!-- Disclaimer Dialog -->
 <p-dialog 
 styleClass="theme-modal-main theme-modal-lg theme-modal-center" 
 header="Header" 
 [modal]="true" 
 [draggable]="false"
 [resizable]="false" 
 [dismissableMask]="true" 
 [(visible)]="visibleDisclaimerDialog" 
 [closable]="false"
 >
 <ng-template pTemplate="header">
     <h3 class="mb-0 fw-normal">Contact Sales</h3>
 </ng-template>
     <div class="theme-modal-body">
         <form [formGroup]="supportForm">
             <div class="form-group mb-4">
                 <label class="fs-14 fw-semibold mb-1 lh-sm" for="subject">Subject<span class="text-danger">*</span></label>
                 <input pInputText id="subject" formControlName="subject" />
                 <div *ngIf="isFieldInvalid('subject')" class="fs-14 lh-sm my-1 text-danger">
                     <div *ngIf="supportForm.get('subject')?.errors?.['required']">
                         {{errorMessages.subject.required}}</div>
                     <div *ngIf="supportForm.get('subject')?.errors?.['maxlength']">
                         {{errorMessages.subject.pattern}}</div>
                 </div>
             </div>
             <div class="form-group mb-4">
                 <label class="fs-14 fw-semibold mb-1 lh-sm" for="message">Message<span class="text-danger">*</span></label>
                 <textarea id="message" rows="5" pInputTextarea formControlName="message"></textarea>
                 <div *ngIf="isFieldInvalid('message')" class="fs-14 lh-sm my-1 text-danger">
                     <div *ngIf="supportForm.get('message')?.errors?.['required']">
                         {{errorMessages.message.required}}</div>
                     <div *ngIf="supportForm.get('message')?.errors?.['maxlength']">
                         {{errorMessages.message.maxlength}}</div>
                 </div>
             </div>
         </form>            
     </div>  
     <ng-template pTemplate="footer">
                 <div class="gap-4 hstack justify-content-center">
                     <button (click)="onCancel()" class="btn btn-light bg-white text-body">Cancel</button>
                     <button (click)="onEmailSent()" class="btn btn-primary">Submit</button>
                 </div>
             </ng-template>  
</p-dialog>
<p-toast></p-toast>