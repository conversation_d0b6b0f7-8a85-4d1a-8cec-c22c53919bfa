import { AfterViewInit, Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { CryptoService } from 'src/app/services/crypto.service';
import { UserServiceService } from 'src/app/services/user-service.service';
import { currencyOptions } from 'src/app/validators/constant';
import { ErrorMessages } from 'src/app/validators/error-messages';

@Component({
  selector: 'app-user-pricing-plans',
  templateUrl: './user-pricing-plans.component.html',
  styleUrls: ['./user-pricing-plans.component.scss']
})
export class UserPricingPlansComponent implements OnInit, AfterViewInit {


  plans: any[] = [];
  responsiveOptions: any[] | undefined;
  currencyOptions = currencyOptions;
  selectedCurrency: string = 'GBP';
  selectedPlanType: string = 'Monthly';
  clinicId = localStorage.getItem('clinicId');
  selectedPlanId = localStorage.getItem('selectedPlanIdForNewUser');
  noPlanMessage: boolean = false;
  orgPlan: any[] = [
    {
      id: 99889989,
      name: 'Organisation',
      description: 'Best for large organisations, clinics and busy individuals',
      price: '',
    }
  ]
  visibleDisclaimerDialog: boolean = false;
  supportForm: FormGroup;
  errorMessages = ErrorMessages.supportForm;
  clinic_id = localStorage.getItem('clinicId')

  constructor(
    private route: ActivatedRoute,
    private titleService: Title,
    private services: UserServiceService,
    private router: Router,
    private cryptoService: CryptoService,
    private formBuilder: FormBuilder,
    private messageService: MessageService
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
    this.supportForm = this.formBuilder.group({
      subject: ['', [Validators.required, Validators.maxLength(50)]],
      message: ['', [Validators.required, Validators.maxLength(500)]]
    });
  }

  ngOnInit(): void {
    this.responsiveOptions = [
      {
        breakpoint: '1299px',
        numVisible: 3,
        numScroll: 1
      },
      {
        breakpoint: '991px',
        numVisible: 2,
        numScroll: 1
      },
      {
        breakpoint: '575px',
        numVisible: 1,
        numScroll: 1
      }
    ];
  }
  ngAfterViewInit(): void {
    this.getPricingPlans();
  }

  private populatePlans(apiPlans: any[]) {
    // Filter out the plan named 'Free'
    const filteredPlans = apiPlans.filter(plan => plan.name !== 'Free');
  
    // Add orgPlan to the dynamic plans array
    this.plans = filteredPlans.map(apiPlan => ({ ...apiPlan }));
    
    // Concatenate orgPlan with the dynamic plans array
    this.plans = this.plans.concat(this.orgPlan);
  
    this.noPlanMessage = this.plans.length === 0;
  }
  
  
  
  getPricingPlans() {
   
    let request: { [key: string]: any }  = {
      plan_type: this.selectedPlanType,
      currency: this.selectedCurrency,
    };
    if (this.clinicId) {
      request['clinic_id'] = this.clinicId;
    }
    this.services.postPrcingQuery(request).subscribe((Response) => {
      const decryptedData = this.cryptoService.decrypt(Response.data);
      this.populatePlans(decryptedData);
    });
  }
  getPlanType(planType: string): string {
    switch (planType) {
      case 'Monthly':
        return 'month';
      case 'Yearly':
        return 'year';
      default:
        return planType;
    }
  }
   // Function to handle currency dropdown change
  //  onCurrencyChange(event: any) {
  //   this.selectedCurrency = event.value;
  //   this.getPricingPlans();
  // }

  // Function to handle radio button change
  onPlanTypeChange(planType: string) {
    this.selectedPlanType = planType;
    this.getPricingPlans();
  }
  onSubmit(planId: any, planName: any) {
      this.router.navigate(['/user/secure/payment-billing'], {
        queryParams: { planId: planId, planName: planName }
      });
  }
  showContactDialog() {
    this.visibleDisclaimerDialog = true;
  }

  isFieldInvalid(fieldName: string): any {
    const field = this.supportForm.get(fieldName);
    return field?.invalid && (field?.dirty || field?.touched);
  }

  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
  onEmailSent() {
    if (this.supportForm.valid) {
        let data = {
            subject: this.supportForm.value.subject,
            message: this.supportForm.value.message,
            clinic_id: this.clinic_id
        };
        this.services.postSupportForm(data).subscribe((Response) => {
            if (Response.statusCode === 200) {
                this.visibleDisclaimerDialog = false;
                this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Email Sent Successfully' });
                this.supportForm.reset();
            }
        }, (error) => {
            console.log('Error occurred:', error);
            let errorMsg = error?.error?.msg || 'An error occurred';
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Error Sending the email' });
        });
    }
    else {
        this.markFormGroupTouched(this.supportForm);
    }
}
onCancel(){
  this.visibleDisclaimerDialog = false;
  this.supportForm.reset();
}

}
