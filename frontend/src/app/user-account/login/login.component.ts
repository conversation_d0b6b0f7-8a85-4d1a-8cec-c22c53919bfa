import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { CommonService } from 'src/app/services/common.service';
import { UserServiceService } from 'src/app/services/user-service.service';
import { ErrorMessages } from 'src/app/validators/error-messages';
import { SvgIcons } from 'src/app/validators/svg-icons';
import { environment } from 'src/environments/environment';
import { emailValidation } from 'src/app/validators/constant';
import { CryptoService } from 'src/app/services/crypto.service';


@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  private recaptchaToken: string = '';
  svgIcons = SvgIcons;
  public myLoginForm: FormGroup;
  public token: any;
  public siteKey: any = environment.siteKey;
  currentUser: any;
  user_id: any;
  userProfile: any;
  public errorMessages = ErrorMessages.loginForm;
  tokenData: any;
  public passwordInfoMessage = ErrorMessages.toolTips.passwordInfoMessage;

  constructor(
    private loginService: UserServiceService,
    private fb: FormBuilder,
    private route: Router,
    private router: ActivatedRoute,
    private _commonService: CommonService,
    private titleService: Title,
    private messageService: MessageService,
    private cryptoService: CryptoService
  ) {
    this.titleService.setTitle(this.router.snapshot.data['title'])
    this.myLoginForm = this.fb.group({
      password: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email, emailValidation]],
      rememberMe: [''],
      recaptcha: ['', Validators.required]
    });
  }
  ngOnInit(): void {
    const rememberedEmail = localStorage.getItem('rememberedEmail');
    const rememberedPassword = localStorage.getItem('rememberedPassword');

    if (rememberedEmail && rememberedPassword) {
      this.myLoginForm.patchValue({
        email: rememberedEmail,
        password: rememberedPassword,
        rememberMe: true // Set the checkbox to be checked
      });
    }
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === ' ') {
      event.preventDefault();
    }
  }

  isFieldInvalid(fieldName: string): any {
    const field = this.myLoginForm.get(fieldName);
    return field?.invalid && (field?.dirty || field?.touched);
  }

  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  postAccountLogin() {
    if (this.myLoginForm.valid) {
      const rememberMe = this.myLoginForm.value.rememberMe;

    // Clear any previously stored credentials if Remember Me is not selected
    if (!rememberMe) {
      localStorage.removeItem('rememberedEmail');
      localStorage.removeItem('rememberedPassword');
    } else {
      // Store user's email and password in local storage
      localStorage.setItem('rememberedEmail', this.myLoginForm.value.email);
      localStorage.setItem('rememberedPassword', this.myLoginForm.value.password);
    }
      let data = {
        email: this.myLoginForm.value.email,
        password: this.myLoginForm.value.password
      }
      const encryptedData = this.cryptoService.encrypt(data);
        let request = {
          encrypet: encryptedData
      }
      this.loginService.postUserLogin(request).subscribe((Response) => {
        localStorage.removeItem('currentUser');
        localStorage.removeItem('userSignature');
        if (Response.statusCode == 200) {
          const decryptedData = this.cryptoService.decrypt(Response.data);
          let signature = decryptedData.signature;
          localStorage.setItem('userSignature', signature);
          this.route.navigate(['user-verify-otp']);
        }
      },
        (error) => {
          console.log('Error occurred:', error);
            let errorMsg = error?.error?.msg || 'An error occurred';
            this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
        })

    } else {
      this.markFormGroupTouched(this.myLoginForm);
    }
  }
}