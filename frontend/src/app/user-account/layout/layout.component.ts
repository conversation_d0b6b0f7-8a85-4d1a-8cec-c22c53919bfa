import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { CreditsService } from 'src/app/services/credit.service';
import { CryptoService } from 'src/app/services/crypto.service';
import { UserServiceService } from 'src/app/services/user-service.service';
import { ErrorMessages } from 'src/app/validators/error-messages';
import { SvgIcons } from 'src/app/validators/svg-icons';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss']
})
export class LayoutComponent implements OnInit {
  svgIcons = SvgIcons;
  isButtonActive: boolean = false;
  visible: boolean = false;
  feedbackForm: FormGroup;
  errorMessages = ErrorMessages.supportForm;
  userName: any;
  getToken: any = localStorage.getItem('currentUser');
  showUserManagement: boolean = false;
  roleName: any;
  clinicId: any;
  profileImage: any;
  updateCount: any;
  visible2: boolean = true;
  creditsRemaining: any;
  creditsRemainingForUsers: any;
  userSubMinutes: any;

  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private services: UserServiceService,
    private messageService: MessageService,
    private cryptoService: CryptoService,
    private creditsService: CreditsService
  ) {
    this.feedbackForm = this.formBuilder.group({
      feedback_text: ['', [Validators.required, Validators.maxLength(500)]],
    });
  }

  ngOnInit(): void {
    this.getProfileName();
    setTimeout(()=>{
      this.getUserSettings();
    },2000)
    this.creditsService.credits$.subscribe((credits) => {
      this.creditsRemaining = credits;
    });
  }

  getProfileName() {
    this.services.getUserProfile(this.getToken).subscribe((Response) => {
      if (Response.statusCode == 200) {
        const decryptedData = this.cryptoService.decrypt(Response.data);
        this.userName = decryptedData.first_name + ' ' + decryptedData.last_name;
        this.clinicId = decryptedData.clinic_id;
        let userType = decryptedData.usertype;
        let userId = decryptedData.id;
        this.creditsRemaining = decryptedData.credit_documents + decryptedData?.subscription_documents;
        this.profileImage = decryptedData.profileS3Url;
        this.creditsRemainingForUsers = decryptedData?.owner_info?.credit_documents + decryptedData?.owner_info?.subscription_documents;
        this.userSubMinutes = decryptedData?.owner_info?.subscription_dictation_minutes;
        localStorage.setItem('userId', userId)
        localStorage.setItem('email', decryptedData.email)
        localStorage.setItem('userName', this.userName)
        localStorage.setItem('clinicId', this.clinicId)
        localStorage.setItem('userType', userType)
        localStorage.setItem('phone', decryptedData.phone)
        localStorage.setItem('clinic_name', decryptedData.clinic_name);
        localStorage.setItem('dicMinutes', decryptedData.subscription_dictation_minutes);
        localStorage.setItem('balanceCredits', this.creditsRemaining);
        localStorage.setItem('userSubminutes', this.userSubMinutes)
        const roledetailcount = localStorage.getItem('roledetailcount');
        if(roledetailcount) {
          if(decryptedData.roledetailcount < parseInt(roledetailcount)) {
            this.onLogout();
          } else {
            localStorage.setItem('roledetailcount', decryptedData.roledetailcount);
          }
        } else {
          localStorage.setItem('roledetailcount', decryptedData.roledetailcount);
        }
        this.roleName = localStorage.getItem('roleName')
        if (this.roleName === 'Account Owner') {
          this.showUserManagement = true;
        } else {
          this.showUserManagement = false;
        }
      }
    })
  }

  getDashboardLink(): string {
    if (this.roleName === 'Clinician') {
      return '/user/doctor-dashboard';
    } else {
      return '/user/user-dashboard';
    }
  }

  getUserSettings() {
    let data = {
      clinic_id: this.clinicId
    }
    const encryptedData = this.cryptoService.encrypt(data);
      let request = {
        encrypet: encryptedData
      }
    this.services.getUserSettings(request).subscribe((Response: any) => {
      const decryptedData = this.cryptoService.decrypt(Response.data);
      this.updateCount = decryptedData.update_count;
    })
  }

  onSubmit() {
    if (this.feedbackForm.valid) {
      let request = {
        feedback_text: this.feedbackForm.value.feedback_text,
        clinic_id: this.clinicId
      }
      this.services.postFeedbackForm(request).subscribe((Response) => {
        if (Response.statusCode == 200) {
          this.visible = false;
          this.feedbackForm.reset();
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Feedback submitted successfully' });
        }
      },
      (error) => {
        console.log('Error occurred:', error);
          let errorMsg = error?.error?.msg || 'An error occurred';
          this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
      })
    } else {
      this.markFormGroupTouched(this.feedbackForm);
    }
  }

  showFeedbackDialog() {
    this.visible = true;
  }

  toggleButton(): void {
    this.isButtonActive = !this.isButtonActive;
  }

  onLogout() {
    localStorage.removeItem('currentUser')
    localStorage.removeItem('userSignature');
    localStorage.removeItem('_grecaptcha')
    localStorage.removeItem('clinicId')
    localStorage.removeItem('userProfile')
    localStorage.removeItem('userType')
    localStorage.removeItem('roleName')
    localStorage.removeItem('email')
    localStorage.removeItem('phone')
    localStorage.removeItem('clinic_name')
    localStorage.removeItem('logoImage')
    localStorage.removeItem('signImage')
    localStorage.removeItem('userName')
    localStorage.removeItem('note_id');
    localStorage.removeItem('roledetailcount');
    localStorage.removeItem('userId');
    localStorage.removeItem('patientId');
    localStorage.removeItem('cardDetails');
    localStorage.removeItem('cardHolderName');
    localStorage.removeItem('planId')
    localStorage.removeItem('dicMinutes')
    localStorage.removeItem('customerId')
    localStorage.removeItem('selectedPlanIdForNewUser');
    localStorage.removeItem('addDocPrice');
    localStorage.removeItem('paymentMethodId');
    localStorage.removeItem('userSubminutes');
    localStorage.removeItem('balanceCredits');
    this.router.navigate(['/login']);
  }
  isFieldInvalid(fieldName: string): any {
    const field = this.feedbackForm.get(fieldName);
    return field?.invalid && (field?.dirty || field?.touched);
  }

  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
  onClick() {
    this.router.navigate(['/user/my-profile']);
    this.visible2= false;
  }

}
