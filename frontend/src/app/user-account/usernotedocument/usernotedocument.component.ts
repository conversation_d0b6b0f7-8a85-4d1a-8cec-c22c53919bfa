import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { UserServiceService } from 'src/app/services/user-service.service';

@Component({
  selector: 'app-usernotedocument',
  templateUrl: './usernotedocument.component.html',
  styleUrls: ['./usernotedocument.component.scss']
})
export class UsernotedocumentComponent implements OnInit{

  note_id:any;
  htmlContent: any;

  constructor(
    private route: ActivatedRoute,
    private services: UserServiceService
  ){
   }


ngOnInit(): void {
  // this.route.queryParams.subscribe(params => {
  //   let token = params['t'];
  //   if (token) {
  //     this.note_id = token
  //   }
  // });
}
  
}
