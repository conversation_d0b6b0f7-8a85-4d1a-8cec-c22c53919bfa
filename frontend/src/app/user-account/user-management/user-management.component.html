<div class="d-flex flex-column pageContainer px-3 pb-3 h-100 overflow-y-scroll">
    <div class="contentHeader py-3 my-1">
        <div class="hstack gap-3 flex-wrap justify-content-between">
            <div class="theme-left-header me-auto">
                <h1 class="fw-normal h3 mb-0">User Management</h1>
            </div>
            <button [disabled]="balanceTeamMebers <= 0" [routerLink]="['invite-user']" class="align-items-center btn btn-primary d-inline-flex order-md-last">
                <span class="d-inline-flex me-2" [innerHTML]="svgIcons.addIcon | safeHtml"></span>
                Invite New User
            </button>
            <div class="border p-1 hstack gap-2 rounded text-primary" *ngIf="userSubminutes !== null">
                <span aria-label="Add Credits" [innerHTML]="svgIcons.usersManagementIcon | safeHtml"
                    class="ps-1"></span>
                <span class="px-1 fs-15 fw-bold">Invitations Left - {{ balanceTeamMebers }}</span>
            </div>
            <div class="col-12 col-md-auto theme-search-with-btn">
                <span class="p-input-icon-left w-100">
                    <i class="text-body text-opacity-25" [innerHTML]="svgIcons.searchIcon | safeHtml"></i>
                    <input pInputText aria-label="Search" placeholder="Search" (input)="onSearch()"
                        [(ngModel)]="searchInput" />
                </span>
            </div>
        </div>
    </div>
    <div class="flex-grow-1 pageContent position-relative">
        <div class="card border-0">
            <div class="card-body theme-data-table-cover w-100 h-100">
                <p-table styleClass="p-datatable-striped text-nowrap" [value]="inviteUserList?.list" [paginator]="false">
                    <ng-template pTemplate="header">
                        <tr>
                            <th style="width: 15%;" pSortableColumn="name" scope="Name">Name
                                <p-sortIcon field="name"></p-sortIcon>
                            </th>
                            <th pSortableColumn="email" scope="Email">Email
                                <p-sortIcon field="email"></p-sortIcon>
                            </th>
                            <th pSortableColumn="reg_number" scope="GMC / Registration Number">GMC / Registration Number
                                <p-sortIcon field="reg_number"></p-sortIcon>
                            </th>
                            <th style="width: 15%;" pSortableColumn="user_role" scope="User Role">User Role
                                <p-sortIcon field="user_role"></p-sortIcon>
                            </th>
                            <th class="text-center" style="width: 140px;" scope="Actions">Actions</th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-user>
                        <tr>
                            <td>{{ user.first_name  }} {{ user.last_name }}</td>
                            <td>{{ user.email }}</td>
                            <td>{{ user.reg_gmc_no || '-'}}</td>
                            <td>{{ user.role?.name }}</td>
                            <td>
                                <div class="hstack gap-3 justify-content-center">
                                    <a [routerLink]="['edit-user']" [queryParams]="{ userId:user.id }" appBsTooltip
                                        data-bs-toggle="tooltip" data-bs-placement="top" title="Edit" aria-label="Edit"
                                        class="btn btn-link h-20 p-1 rounded-1 w-20 d-flex align-items-center justify-content-center">
                                        <span [innerHTML]="svgIcons.editIcon | safeHtml"></span>
                                    </a>
                                    <button appBsTooltip data-bs-toggle="tooltip" data-bs-placement="top" title="Delete"
                                        aria-label="Delete"
                                        class="btn btn-link h-20 p-1 rounded-1 w-20 d-flex align-items-center justify-content-center" (click)="deleteUser(user.id)">
                                        <span [innerHTML]="svgIcons.deleteIcon | safeHtml"></span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="emptymessage">
                        <tr>
                            <td class="bg-body-tertiary fw-medium text-center" colspan="5">No User found.</td>
                        </tr>
                    </ng-template>
                </p-table>
                <p-paginator (onPageChange)="onPageChange($event)" [rows]="inviteUserList?.recordsPerPage"
                [totalRecords]="inviteUserList?.totalRecords"></p-paginator>
            </div>
        </div>
    </div>
</div>
<p-toast></p-toast>