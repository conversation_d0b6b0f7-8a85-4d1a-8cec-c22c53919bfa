<div class="d-flex flex-column pageContainer px-3 pb-3 h-100 overflow-y-scroll">
    <div class="contentHeader py-2 my-1">
        <div class="hstack gap-2 flex-wrap justify-content-between">
            <div class="theme-left-header">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb fw-medium">
                        <li class="breadcrumb-item">
                            <a class="text-decoration-none" [routerLink]="['/user/secure/roles-management']">Roles &
                                Rights</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Edit Role</li>
                    </ol>
                </nav>
                <h1 class="fw-normal h3 mb-0">Edit Role</h1>
            </div>
        </div>
    </div>
    <div class="flex-grow-1 pageContent position-relative">
        <div class="card border-0 h-100">
            <div class="card-body theme-data-table-cover w-100 h-100">
                <form class="p-xl-1 d-flex flex-column w-100 h-100" [formGroup]="editRoleForm" (ngSubmit)="onSubmit()">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="row">
                                <div class="col-sm-6 mb-4">
                                    <div class="form-group">
                                        <label class="fs-14 fw-semibold mb-1 lh-sm" for="roles-name">Role Name<span class="text-danger">*</span></label>
                                        <input pInputText id="roles-name" placeholder="Role Name"
                                            formControlName="roleName" />
                                        <div *ngIf="isFieldInvalid('roleName')" class="fs-14 lh-sm my-1 text-danger">
                                            {{errorMessages.name.required}}</div>
                                    </div>
                                </div>
                                <div class="col-12 mb-4">
                                    <div class="form-group">
                                        <label class="fs-14 fw-semibold mb-1 lh-sm" for="role-description">Role
                                            Description<span class="text-danger">*</span></label>
                                        <textarea id="role-description" rows="5" pInputTextarea
                                            placeholder="Role Description" formControlName="roleDescription"></textarea>
                                        <div *ngIf="isFieldInvalid('roleDescription')"
                                            class="fs-14 lh-sm my-1 text-danger">
                                            <div *ngIf="editRoleForm.get('roleDescription')?.errors?.['required']">
                                                {{errorMessages.roleDescription.required}}</div>
                                            <div *ngIf="editRoleForm.get('roleDescription')?.errors?.['pattern']">
                                                {{errorMessages.roleDescription.pattern}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <ng-container *ngFor="let section of groupedPermissions | keyvalue">
                                    <div class="col-sm-4 mb-4" *ngIf="section.key !== 'Adhoc Letter' && section.key !== 'Letters'">
                                        <label class="fs-14 fw-semibold mb-2 pb-1">{{ section.key }}</label>
                                        <div *ngFor="let permission of section.value" class="form-group mb-2 pb-1">
                                            <div class="align-items-start d-flex theme-checkbox fs-14">
                                                <p-checkbox [formControlName]="permission.name" [binary]="true"
                                                    styleClass="fs-14"></p-checkbox>
                                                <label class="ps-2 fw-medium">{{ permission.label }}</label>
                                            </div>
                                        </div>
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                    <div class="hstack mt-auto justify-content-end gap-3">
                        <p-toast></p-toast>
                        <a [routerLink]="['/user/secure/roles-management']" class="bg-white btn btn-light text-body">Cancel</a>
                        <button type="submit" class="btn btn-primary">Update</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>