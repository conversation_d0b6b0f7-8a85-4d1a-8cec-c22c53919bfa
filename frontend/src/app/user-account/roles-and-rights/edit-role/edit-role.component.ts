import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { CryptoService } from 'src/app/services/crypto.service';
import { UserServiceService } from 'src/app/services/user-service.service';
import { emailValidation } from 'src/app/validators/constant';
import { ErrorMessages } from 'src/app/validators/error-messages';

interface CheckboxItem {
  id: number;
  section: string;
  name: string;
  description: string;
  label: string;
  status: number;
  isSelected: number;
}
@Component({
  selector: 'app-edit-role',
  templateUrl: './edit-role.component.html',
  styleUrls: ['./edit-role.component.scss']
})
export class EditRoleComponent implements OnInit {

  roleId: any;
  editRoleForm: FormGroup;
  errorMessages = ErrorMessages.editRoleForm;
  rolesList: any;
  clinic_id = localStorage.getItem('clinicId')
  permissionsList: CheckboxItem[] = [];
  updatedPermissionList: CheckboxItem[] = [];
  groupedPermissions: { [section: string]: CheckboxItem[] } = {};
  getToken = localStorage.getItem('currentUser');

  constructor(
    private route: ActivatedRoute,
    private titleService: Title,
    private formBuilder: FormBuilder,
    private services: UserServiceService,
    private messageService: MessageService,
    private router: Router,
    private cryptoService: CryptoService
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
    this.editRoleForm = this.formBuilder.group({
      roleName: [{ value: '', disabled: true }, Validators.required],
      roleDescription: ['', [Validators.required, emailValidation]],
    });
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.roleId = params['id'];
    })
   
    this.initializePermissions();
  }

  initializePermissions() {
    this.services.getPermissionsList(this.getToken).subscribe((response) => {
      if (response.statusCode === 200) {
        const decryptedData = this.cryptoService.decrypt(response.data);
        this.permissionsList = decryptedData.filter((permission: CheckboxItem) =>
          permission.name !== 'Setting-Delete' && permission.name !== 'Setting-Add'
        );
        this.groupPermissions();
        this.getRolesPerimissions();
        this.createFormControls();
      } else {
        this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Failed to fetch permissions' });
      }
    });
  }

  groupPermissions() {
    this.groupedPermissions = {};

    for (const permission of this.permissionsList) {
      if (!this.groupedPermissions[permission.section]) {
        this.groupedPermissions[permission.section] = [];
      }
      this.groupedPermissions[permission.section].push(permission);
    }
  }

  createFormControls() {
    const permissionKeys = this.editRoleForm.controls;
    for (const section of Object.keys(this.groupedPermissions)) {
      for (const permission of this.groupedPermissions[section]) {
        if (!permissionKeys[permission.name]) {
          permissionKeys[permission.name] = this.formBuilder.control(false);
        }
      }
    }
  }

  isFieldInvalid(fieldName: string): any {
    const field = this.editRoleForm.get(fieldName);
    return field?.invalid && (field?.dirty || field?.touched);
  }



  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  onSubmit() {
    if (this.editRoleForm.valid) {
      this.updatedPermissionList = this.permissionsList.filter(permission => this.editRoleForm.get(permission.name)?.value);
      const permissions: number[] = this.updatedPermissionList.map(permission => permission.id);
      let data = {
        role_descriptions: this.editRoleForm.value.roleDescription,
        permissions: permissions
      };
      const encryptedData = this.cryptoService.encrypt(data);
      let request = {
        encrypet: encryptedData
      }
      this.services.updateRolePermissions(this.roleId, request).subscribe((Response) => {
        this.messageService.add({ severity: 'success', summary: 'success', detail: 'Updated successfully' });
        setTimeout(() => {
          this.router.navigate(['/user/secure/roles-management']);
        }, 1000);
      },
      (error) => {
        console.log('Error occurred:', error);
          let errorMsg = error?.error?.msg || 'An error occurred';
          this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
      });
    }
  }
  

  getRolesPerimissions() {
    this.services.getRolePermissions(this.roleId).subscribe((Response) => {
      const decryptedData = this.cryptoService.decrypt(Response.data);
      this.rolesList = decryptedData;
      this.editRoleForm.patchValue({
        roleName: this.rolesList.name,
        roleDescription: this.rolesList.role_descriptions,
      });

      // Loop through the permissionsList and set form controls dynamically
      for (const permission of this.rolesList.permissions) {
        const controlName = permission.name;
        const isSelected = permission.isSelected === 1;
        if (this.editRoleForm.get(controlName)) {
          this.editRoleForm.get(controlName)?.setValue(isSelected);
        }
      }
    },
      (error) => {
        console.log('Error occurred:', error);
        let errorMsg = error?.error?.msg || 'An error occurred';
        this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
      });
  }


}
