import { Component } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { SvgIcons } from 'src/app/validators/svg-icons';

@Component({
  selector: 'app-transaction-failed',
  templateUrl: './transaction-failed.component.html',
  styleUrls: ['./transaction-failed.component.css']
})
export class TransactionFailedComponent {
  svgIcons = SvgIcons;
  constructor(
   private route: ActivatedRoute,
   private titleService: Title,
 ) {
   this.titleService.setTitle(this.route.snapshot.data['title']);

 }
}
