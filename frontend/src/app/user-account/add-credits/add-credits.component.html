<div class="d-flex flex-column pageContainer px-3 pb-3 h-100 overflow-y-scroll">
    <div class="contentHeader py-2 my-1">
        <div class="hstack gap-2 flex-wrap justify-content-between">
            <div class="theme-left-header">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb fw-medium">
                        <li class="breadcrumb-item">
                            <a class="text-decoration-none" [routerLink]="['/user/user-dashboard']">Dashboard</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Buy Credits</li>
                    </ol>
                </nav>
                <h1 class="fw-normal h3 mb-0">Buy Credits</h1>
            </div>
        </div>
    </div>
    <div class="flex-grow-1 pageContent position-relative">
        <div class="card border-0 h-100">
            <div class="card-body theme-data-table-cover w-100 h-100 d-flex flex-column">
                <div class="p-2 d-flex flex-column m-auto text-center">
                    <span class="mb-4 pb-2 text-primary" aria-label="Add Credits" [innerHTML]="svgIcons.creditListImage | safeHtml"></span>
                    <h5 class="mb-3 pt-1">{{creditCounterValue}} Credits</h5>
                    <p class="fs-15 fw-medium text-primary">£ {{creditCounterValue*unitPrice | number: '1.2-2'}} {{ currency }}</p>
                    <div class="number-counter-cover mb-4 max-w-170 mx-auto" [ngClass]="{'btn-disabled-min': creditCounterValue <= 10}">
                        <p-inputNumber inputStyleClass="text-center" [(ngModel)]="creditCounterValue" [min]="10" [step]="1"
                            [showButtons]="true" buttonLayout="horizontal" spinnerMode="horizontal" inputId="horizontal"
                            decrementButtonClass="bg-white btn btn-light text-body" incrementButtonClass="bg-white btn btn-light text-body"
                            incrementButtonIcon="pi pi-plus" decrementButtonIcon="pi pi-minus" placeholder="10">
                        </p-inputNumber>
                    </div>
                    <p class="fs-15 fw-medium mb-1 pb-1">1 Credit = 1 Document.</p>
                    <p class="fs-15 fw-medium mb-1 pb-1">Each time the "Generate" button is clicked,</p>
                    <p class="fs-15 fw-medium mb-4 pb-2">one credit will be deducted.</p>
                    <div class="hstack mt-auto gap-3 flex-wrap justify-content-center">
                        <a [routerLink]="['/user/user-dashboard']"
                            class="mw-110 bg-white btn btn-light text-body flex-sm-grow-0 flex-grow-1 mx-xl-1">Cancel</a>
                        <a (click)="onSubmit()"
                            class="mw-110 btn btn-primary flex-sm-grow-0 flex-grow-1 mx-xl-1">Continue</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>