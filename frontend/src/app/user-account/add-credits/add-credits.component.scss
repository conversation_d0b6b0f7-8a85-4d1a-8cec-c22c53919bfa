@import "../../../assets/css/variables";

.max-w-170 {
    max-width: 170px;
}

:host ::ng-deep {
    .number-counter-cover {
        &.btn-disabled-min {
            .p-inputnumber-button-down {
                pointer-events: none;

                .pi-minus {
                    opacity: .3;
                }
            }
        }

        &.btn-disabled-max {
            .p-inputnumber-button-up {
                pointer-events: none;

                .pi-plus {
                    opacity: .3;
                }
            }
        }

        .p-inputnumber-button {
            &.p-button {
                &:enabled {
                    &:active {
                        border-color: $primary;
                    }

                    &:hover {
                        border-color: $primary;
                    }
                }

                &:focus {
                    box-shadow: $theme-focus-shadow;
                }
            }

            width: 40px;

            .pi-minus,
            .pi-plus {
                width: 15px;
                height: 3px;
                background-color: $body-color;
                border-radius: 1px;
            }

            .pi-plus {
                &::before {
                    content: "";
                    height: 15px;
                    width: 3px;
                    background-color: $body-color;
                    border-radius: 1px;
                    top: 50%;
                    position: absolute;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
            }
        }
    }
}