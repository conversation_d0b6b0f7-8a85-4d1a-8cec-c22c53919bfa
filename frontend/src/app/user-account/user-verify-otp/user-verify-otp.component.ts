import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { MessageService } from 'primeng/api';
import { CommonService } from 'src/app/services/common.service';
import { ErrorMessages } from 'src/app/validators/error-messages';
import { environment } from 'src/environments/environment';
import { Subscription, interval } from 'rxjs';
import jwt_decode from 'jwt-decode';
import { CryptoService } from 'src/app/services/crypto.service';
import { UserServiceService } from 'src/app/services/user-service.service';

@Component({
  selector: 'app-user-verify-otp',
  templateUrl: './user-verify-otp.component.html',
  styleUrls: ['./user-verify-otp.component.scss']
})
export class UserVerifyOtpComponent implements OnInit, OnD<PERSON>roy {

  errorMessages = ErrorMessages.loginForm;
  siteKey: any = environment.siteKey;
  otpForm: FormGroup;
  signature : any = localStorage.getItem('userSignature');
  decodedSignature: any
  redirectCounter: number = 7;
  private countdownSubscription: Subscription = new Subscription;
  tokenData: any;


  constructor(
    private formBuilder: FormBuilder,
    private loginService: UserServiceService,
    private route: Router,
    private router: ActivatedRoute,
    private _commonService: CommonService,
    private titleService: Title,
    private messageService: MessageService,
    private cryptoService: CryptoService
  ){
    this.titleService.setTitle(this.router.snapshot.data['title'])
    this.otpForm = this.formBuilder.group({
      otp: ['', [Validators.required]],
      recaptcha: ['', Validators.required]
    });
  }
  ngOnInit(): void {
    this.startCountdown();
    this.decodedSignature = jwt_decode(this.signature);
  }
  ngOnDestroy(): void {
    this.stopCountdown(); 
  }
  isFieldInvalid(fieldName: string): any {
    const field = this.otpForm.get(fieldName);
    return field?.invalid && (field?.dirty || field?.touched);
  }

  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
  postOtp(){
    if(this.otpForm.valid) {
     let data = {
      email: this.decodedSignature?.data?.email,
      signature: this.signature,
      otp: this.otpForm.value.otp
      }
      // const encryptedData = this.cryptoService.encrypt(data);
      // let request = {
      //   encrypet: encryptedData
      // }
      this.loginService.postLoginOtp(data).subscribe((Response => {
        if(Response.statusCode === 200) {
          const decryptedData = this.cryptoService.decrypt(Response.data);
          let currentUser = decryptedData.token;
          localStorage.setItem('currentUser', currentUser)
          this.tokenData = jwt_decode(currentUser);
          let clinicId = this.tokenData.data.clinic_id;
          let roleName = this.tokenData.data.role_name;
          localStorage.setItem('clinicId', clinicId)
          localStorage.setItem('roleName', roleName)
          if (roleName === 'Clinician') {
            this.route.navigate(['user/doctor-dashboard']);
          } else {
            this.route.navigate(['user/user-dashboard']);
          }
          // Set session timeout
          setTimeout(() => {
            this.autoLogout();
          }, 120 * 60 * 1000);
        }
      }),
      (error) => {
        console.log('Error occurred:', error);
          if(error?.error?.msgCode === 50029 || error?.error?.msgCode === 50028 || error?.error?.msgCode === "50020"){
            this.messageService.add({ severity: 'error', summary: 'Error', detail: "Code you have entered is either invalid or expired" })
          } else {
            let errorMsg = error?.error?.msg || 'An error occurred';
            this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
          }
      })
    } else {
      this.markFormGroupTouched(this.otpForm);
    }
  }
  autoLogout() {
    localStorage.removeItem('userSignature');
    sessionStorage.clear();
    localStorage.removeItem('currentUser')
    localStorage.removeItem('_grecaptcha')
    localStorage.removeItem('clinicId')
    localStorage.removeItem('userProfile')
    localStorage.removeItem('userType')
    localStorage.removeItem('roleName')
    localStorage.removeItem('email')
    localStorage.removeItem('userName')
    localStorage.removeItem('note_id');
    localStorage.removeItem('userId');
    localStorage.removeItem('cardDetails');
    localStorage.removeItem('cardHolderName');
    localStorage.removeItem('planId')
    localStorage.removeItem('dicMinutes')
    localStorage.removeItem('customerId')
    localStorage.removeItem('selectedPlanIdForNewUser');
    localStorage.removeItem('addDocPrice');
    localStorage.removeItem('paymentMethodId');
    localStorage.removeItem('userSubminutes');
    localStorage.removeItem('balanceCredits');
    this.route.navigate(['/login']);
  }
  startCountdown(): void {
    this.countdownSubscription = interval(1000).subscribe(() => {
      if (this.redirectCounter > 0) {
        this.redirectCounter--;
      } else {
        this.stopCountdown();
      }
    });
  }

  stopCountdown(): void {
    if (this.countdownSubscription) {
      this.countdownSubscription.unsubscribe();
    }
  }

  resendOtp(){
    let data = {
      email: this.decodedSignature?.data?.email,
      signature: this.signature
    }
    const encryptedData = this.cryptoService.encrypt(data);
      let request = {
        encrypet: encryptedData
      }
    this.loginService.postResendOtp(request).subscribe((Response:any) => {
      if(Response.statusCode === 200) {
        this.messageService.add({ severity: 'success', summary: 'Sucess', detail: 'Otp resent sucessfully' });
      }
    },
    (error) => {
      console.log('Error occurred:', error);
        let errorMsg = error?.error?.msg || 'An error occurred';
        this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
    })
  }
}


