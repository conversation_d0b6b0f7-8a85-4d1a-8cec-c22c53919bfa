<div class="g-0 vh-100 position-relative row">
    <div
        class="align-items-center bg-body-tertiary col-md d-flex h-100 justify-content-center mobileBannerActive p-4 z-n1 pe-none user-select-none">
        <img src="assets/images/auth-banner.png" alt="Auth Banner" width="505" height="585" class="img-fluid w-auto" />
    </div>
    <div class="h-100 col-md mw-1">
        <div class="d-flex flex-column h-100 overflow-y-scroll pt-3 text-center w-100">
            <div class="auth-header pt-4">
                <a [routerLink]="['']" class="mb-4 d-inline-block">
                    <img src="assets/images/logo.png" title="Clinical Pad Logo" alt="Clinical Pad Logo" width="200"
                        height="30" class="img-fluid" />
                    <span class="visually-hidden">Clinical Pad Logo</span>
                </a>
                <h1 class="fw-normal h3 mt-3 mb-4 mb-md-5">Welcome to Clinical Pad</h1>
            </div>
            <div class="auth-body d-flex flex-column flex-grow-1 px-4 text-start auth-form-cover">
                <form class="px-xl-3 d-flex flex-column h-100" [formGroup]="otpForm"
                    (ngSubmit)="postOtp()">
                    <div class="row justify-content-center">
                        <div class="col-sm-9 col-lg-7 col-xxl-5">
                            <div class="form-group mb-4">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="otp">Enter Otp<span class="text-danger">*</span></label>
                                <input pInputText id="otp" formControlName="otp" />
                                    <div *ngIf="isFieldInvalid('otp')" class="fs-14 lh-sm my-1 text-danger">
                                        <div *ngIf="otpForm.get('otp')?.errors?.['required']">
                                            {{errorMessages.otp.required}}</div>
                                    </div>
                            </div>
                            <div class="fw-medium">
                                <ng-container *ngIf="redirectCounter > 0; else resendLink">
                                    Resend the otp in {{ redirectCounter }} seconds
                                    <p-progressSpinner styleClass="spinner-small"></p-progressSpinner>
                                </ng-container>
                                <ng-template #resendLink>
                                    <a href="javascript:void(0);" (click)="resendOtp()">Resend OTP</a>
                                </ng-template>
                            </div>
                            <div class="form-group mb-4 pb-1">
                                <ngx-recaptcha2 #captchaElem [siteKey]="siteKey" formControlName="recaptcha">
                                </ngx-recaptcha2>
                                <div *ngIf="isFieldInvalid('recaptcha')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.captcha.required}}</div>
                            </div>
                            <div class="form-group mb-4">
                                <p-toast></p-toast>
                                <button type="submit" class="btn btn-primary w-100">Login</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>