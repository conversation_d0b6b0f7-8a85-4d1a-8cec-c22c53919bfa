<div class="g-0 vh-100 position-relative row">
    <div class="h-100 col-md mw-1">
        <div class="d-flex flex-column h-100 overflow-y-scroll pt-3 text-center w-100">
            <div class="auth-header pt-4">
                <a [routerLink]="['']" class="mb-4 d-inline-block">
                    <img src="assets/images/logo.png" title="Clinical Pad Logo" alt="Clinical Pad Logo" width="200"
                        height="30" class="img-fluid" />
                        <span class="visually-hidden">Clinical Pad Logo</span>
                </a>
                <h1 class="fw-normal h3 mt-3 mb-4 mb-md-5" *ngIf="isSuccessResponse">Email Verification</h1>
                <div class="mb-3 text-success"  *ngIf="isSuccessResponse">                    
                    <span [innerHTML]="svgIcons.successIcon | safeHtml"></span>
                    <p class="mt-3"><span class="fw-bold">Email verification successful!</span><br>Thank you {{account_owner_name}}. Your email has been verified.</p>
                </div>
                <div class="mb-3 text-danger" *ngIf="isErrorResponse">
                    <span [innerHTML]="svgIcons.errorIcon | safeHtml"></span>
                    <p class="mt-3"><span class="fw-bold">Access Denied!</span><br>You do not have access to this page</p>                   
                </div>
                <div class="fw-medium pt-3" *ngIf="isSuccessResponse">
                    <p *ngIf="redirectCounter >= 0">Redirecting you to Login Page in... {{redirectCounter}} seconds</p>
                    <p-progressSpinner styleClass="spinner-small"></p-progressSpinner>
                </div>
            </div>

        </div>
    </div>
</div>