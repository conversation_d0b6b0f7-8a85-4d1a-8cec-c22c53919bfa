import { Component } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { CryptoService } from 'src/app/services/crypto.service';
import { UserServiceService } from 'src/app/services/user-service.service';
import { letterTypes2 } from 'src/app/validators/constant';
import { SvgIcons } from 'src/app/validators/svg-icons';
@Component({
  selector: 'app-user-dashboard',
  templateUrl: './user-dashboard.component.html',
  styleUrls: ['./user-dashboard.component.scss']
})
export class UserDashboardComponent {
  svgIcons = SvgIcons;
  data: any;
  options: any;
  dashboardData: any;
  userType: any;
  showClicnianCount: boolean = true;
  getToken: any = localStorage.getItem('currentUser');
  currentPage: number = 1;
  patientNotes: any;
  totalRecords: number = 0;
  selectedStartDate: Date | null = null;
  selectedEndDate: Date | null = null;
  searchInput: any = '';
  selectedDateRange: Date[] | null = null;
  specialisationList: { id: number, name: string }[] = [];
  letterTypes = letterTypes2;
  selectedLetterType: any;
  selectedSpecialisation: { id: number, name: string } | null = null;
  clinicList: { clinic_id: string, name: string, user_type: string }[] = [];
  selectedClinicId: any ;
  selecteduserType: any = localStorage.getItem('userType');
  creditDocuments: any;
  subDocuments: any;
  roleName: any;

  constructor(
    private titleService: Title,
    private route: ActivatedRoute,
    private services: UserServiceService,
    private cryptoService: CryptoService,
    private router: Router
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
  }
  ngOnInit() {
    this.selectedClinicId = localStorage.getItem('clinicId');
    this.selectedLetterType = "All Letters";
    this.selectedSpecialisation = { id: 0, name: 'All Specialisations' }
    this.getUserProfile();
    this.loadData();
    if(this.selecteduserType  === 'Individual') {
      this.showClicnianCount = false;
    } else {
      this.showClicnianCount = true;
    }
  }

  async loadData() {
    // await this.getUserType();
    await this.getDashboardData();
    this.getClinicList();
    this.getListData();
    this.getSpecList();


    this.data = {
      labels: ['Daily', 'Weekly', 'Monthly'],
      datasets: [
        {
          data: [this.dashboardData?.letters?.countsDaily, this.dashboardData?.letters?.countsWeekly, this.dashboardData?.letters?.countsMonthly],
          backgroundColor: [
            '#5875F7',
            '#47C4F2',
            '#5223FC',
          ],
        },
      ],
    };

    this.options = {
      plugins: {
        legend: {
          display: false,
        },
      },
      borderWidth: 0,
      responsive: false,
      maintainAspectRatio: true,
    };
  }

  getClinicList() {
    this.services.getClinicList(this.getToken).subscribe((Response: any) => {
      if (Response.statusCode === 200 && Response.status === 'success') {
        const decryptedData = this.cryptoService.decrypt(Response.data);
        let type = decryptedData.map((item:any) => {
          this.userType = item.user_type
        })
        this.clinicList = decryptedData.map((clinicItem: any) => {
          return {
            clinic_id: clinicItem.clinic.clinic_id,
            name: clinicItem.clinic.name,
            user_type: clinicItem.user_type
          };
        });
        this.selectedClinicId = localStorage.getItem('clinicId');
      }
    });
  }

  updateSelectedClinic(clinicId: string) {
    localStorage.setItem('clinicId', clinicId);
  
    const selectedClinicData = this.clinicList.find((clinicData) => clinicData.clinic_id === clinicId);
  
    if (selectedClinicData) {
      const userType = selectedClinicData.user_type; 
      localStorage.setItem('userType', userType);
      if (userType === 'Individual') {
        this.showClicnianCount = false;
      } else {
        this.showClicnianCount = true;
      }
    }
  
    this.loadData();
  }
  
  
  

  async getUserType() {
    try {
      const response = await this.services.getUserProfile(this.getToken).toPromise();
      if (response.statusCode === 200) {
        const decryptedData = this.cryptoService.decrypt(response.data);
        this.userType = decryptedData.user_type;
        if (this.userType === 'Individual') {
          this.showClicnianCount = false;
        }
      }
    } catch (error) {
      // Handle errors
    }
  }

  onLetterDropDownChange() {
    this.loadData();
  }
  onDropdownChange() {
    this.loadData();
  }

  async getDashboardData() {
    try {
      let data: any = {
        clinic_id: this.selectedClinicId
      };
      if (this.selectedSpecialisation) {
        data.clinical_specializations_id = this.selectedSpecialisation.id;
      } else {
        data.clinical_specializations_id = 0;
      }
      if(this.selectedLetterType !== 'All Letters'){
          data.letter_type = this.selectedLetterType
      } else {
        data.letter_type = 'ALL'
      }
      const encryptedData = this.cryptoService.encrypt(data);
      let request = {
        encrypet: encryptedData
      }
      const response = await this.services.getOwnerDashboard(request).toPromise();
      const decryptedData = this.cryptoService.decrypt(response.data);
      this.dashboardData = decryptedData;
    } catch (error) {
      // Handle errors
    }
  }

  getListData() {
    if (this.searchInput === '') {
      this.data = {
        clinic_id: this.selectedClinicId
      }
    } else {
      this.data = {
        searchStr: this.searchInput,
        clinic_id: this.selectedClinicId
      }
    }
    const encryptedData = this.cryptoService.encrypt(this.data);
    let request = {
      encrypet: encryptedData
    }
    this.services.getOwnerDashboardTable(this.currentPage, request).subscribe((Response: any) => {
      const decryptedData = this.cryptoService.decrypt(Response.data);
      this.patientNotes = decryptedData;
      this.totalRecords = decryptedData.totalRecords;
    });
  }
  onSearch() {
    if (this.selectedDateRange && this.selectedDateRange.length === 2) {
      const startDate = this.selectedDateRange[0]
      const endDate = this.selectedDateRange[1]
      startDate.setMinutes(startDate.getMinutes() - startDate.getTimezoneOffset());
      endDate.setMinutes(endDate.getMinutes() - endDate.getTimezoneOffset());
      this.searchInput = {
        "startDate": startDate.toISOString().split('T')[0],
        "endDate": endDate.toISOString().split('T')[0]
      };
    } else {
      this.searchInput = '';
    }
    this.loadData();
  }
  getSpecList() {
    this.services.getSpecList().subscribe((response) => {
      if (response.statusCode == 200) {
        const sortedSpecialisationList = [];
        sortedSpecialisationList.push({ id: 0, name: 'All Specialisations' });
        for (const item of response.data) {
          if (item.name !== 'Other') {
            sortedSpecialisationList.push({ id: item.id, name: item.name });
          }
        }
        sortedSpecialisationList.push({ id: '83', name: 'Other' });

        this.specialisationList = sortedSpecialisationList;
      }
    });
  }
  clearDateRange() {
    this.selectedDateRange = null; 
    this.searchInput = ''; 
    this.loadData();
  }
  getSharedStatus(element: any): string {
    if (element.is_letter_printed && element.is_letter_emailed && element.is_letter_downloaded) {
      return 'Printed, Emailed, Downloaded';
    } else if (element.is_letter_printed && element.is_letter_emailed) {
      return 'Printed, Emailed';
    } else if (element.is_letter_printed && element.is_letter_downloaded) {
      return 'Printed, Downloaded';
    } else if (element.is_letter_emailed && element.is_letter_downloaded) {
      return 'Emailed, Downloaded';
    } else if (element.is_letter_printed) {
      return 'Printed';
    } else if (element.is_letter_emailed) {
      return 'Emailed';
    } else if (element.is_letter_downloaded) {
      return 'Downloaded';
    } else {
      return '-';
    }
  }

  onPageChange(event: any) {
    this.currentPage = event.page + 1;
    this.getListData();
  }
  getUserProfile(){
    this.services.getUserProfile(this.getToken).subscribe((Response) =>{
      const decryptedData = this.cryptoService.decrypt(Response.data);
      this.roleName = decryptedData?.role_name;
      this.creditDocuments = decryptedData?.credit_documents;
      this.subDocuments = decryptedData?.subscription_documents;
      if(decryptedData?.owner_info){
        this.creditDocuments = decryptedData?.owner_info?.credit_documents
        this.subDocuments = decryptedData?.owner_info?.subscription_documents
      }
      const roledetailcount = localStorage.getItem('roledetailcount');
        if(roledetailcount) {
          if(decryptedData.roledetailcount < parseInt(roledetailcount)) {
            this.onLogout();
          } else {
            localStorage.setItem('roledetailcount', decryptedData.roledetailcount);
          }
        } else {
          localStorage.setItem('roledetailcount', decryptedData.roledetailcount);
        }
    })
  }
  onLogout() {
    localStorage.removeItem('currentUser')
    localStorage.removeItem('_grecaptcha')
    localStorage.removeItem('clinicId')
    localStorage.removeItem('userProfile')
    localStorage.removeItem('userType')
    localStorage.removeItem('roleName')
    localStorage.removeItem('email')
    localStorage.removeItem('phone')
    localStorage.removeItem('clinic_name')
    localStorage.removeItem('logoImage')
    localStorage.removeItem('signImage')
    localStorage.removeItem('userName')
    localStorage.removeItem('note_id');
    localStorage.removeItem('roledetailcount');
    localStorage.removeItem('userId');
    localStorage.removeItem('cardDetails');
    localStorage.removeItem('cardHolderName');
    localStorage.removeItem('planId')
    localStorage.removeItem('dicMinutes')
    localStorage.removeItem('customerId')
    localStorage.removeItem('selectedPlanIdForNewUser');
    localStorage.removeItem('addDocPrice');
    localStorage.removeItem('paymentMethodId');
    localStorage.removeItem('userSubminutes');
    localStorage.removeItem('balanceCredits');
    this.router.navigate(['/login']);
  }
}
