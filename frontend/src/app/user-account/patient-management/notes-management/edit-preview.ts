import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { CryptoService } from 'src/app/services/crypto.service';
import { UserServiceService } from 'src/app/services/user-service.service';

@Component({
    template: `
    <div class="accordion" id="themeTips">
        <div class="accordion-item mb-3">
            <h2 class="accordion-header font-family-base">
                <button class="accordion-button fw-semibold" type="button" data-bs-toggle="collapse"
                    data-bs-target="#faqCollapse1" aria-expanded="true" aria-controls="faqCollapse1">
                    Useful Tips<span class="text-danger sup">*</span>
                </button>
            </h2>
            <div id="faqCollapse1" class="accordion-collapse collapse show" data-bs-parent="#themeTips">
                <div class="accordion-body d-flex flex-column gap-4">
                    <ol>
                        <li>Customise the header, footer and signature.</li>
                        <li>Review and edit the AI generated text on the letter.</li>
                        <li>Patient Information & Letter Summary can be updated before you click on 'Save & Confirm'.</li>
                        <li><span class="fw-bold">Disclaimer:</span> Please do not enter patient identifiable information into the ‘Body’ (Between the highlighted sections) of this generated letter. Patient Identifiable information (PII) should only be entered within the highlighted areas of this generated letter. <a (click)="visibleDisclaimerDialog = true" role="button" class="fw-bold text-decoration-none">Read more.</a></li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    

    <div *ngIf="letterType === 'Clinical Summary'">
        <app-detailed-summary-template></app-detailed-summary-template>
    </div>
    <div *ngIf="letterType === 'Clinical Letter'">
        <app-clinical-letter-template></app-clinical-letter-template>
    </div>
   

        <!-- Disclaimer Dialog -->
        <p-dialog 
            styleClass="theme-modal-main theme-modal-lg theme-modal-center" 
            header="Header" 
            [modal]="true" 
            [draggable]="false"
            [resizable]="false" 
            [dismissableMask]="true" 
            [(visible)]="visibleDisclaimerDialog" 
            [closable]="false"
            >
            <ng-template pTemplate="header">
                <h3 class="mb-0 fw-normal">Disclaimer</h3>
            </ng-template>
            <div class="theme-modal-body">
                <p class="fw-medium mb-4 pt-3">The ‘Body’ of this generated letter is used to generate clinical letters using Machine Learning and Artificial Intelligence. To ensure a consistently enhanced user experience and continual application improvement, we store the information entered within this field in our database for system training and generating letters.</p>
                <p class="fw-medium mb-4">Please do not enter PII into the ‘body’ of this generated letter. Patient information should only be entered within the designated areas of the application as highlighted.</p> 
                <p class="fw-medium mb-2">Patient identifiable information include,</p>
                <p class="fw-medium mb-3">
                    Names<br />
                    Date of birth<br />
                    Addresses<br />
                    Contact Details<br />
                    NHS/Insurance details
                </p>
                <p class="fw-medium mb-4">More information on the safety and privacy of data, can be found on our <a [routerLink]="['/privacy-policy']" target="_blank" class="fw-bold text-decoration-none">Privacy Policy</a>.</p>                
            </div>
            <ng-template pTemplate="footer">
                <div class="gap-4 hstack justify-content-center">
                    <button (click)="visibleDisclaimerDialog = false" class="btn btn-primary m-0">Ok</button>
                </div>
            </ng-template>
        </p-dialog>
    `
})

export class EditPreview implements OnInit {

    visibleDisclaimerDialog: boolean = false;
    note_id = localStorage.getItem('note_id');
    phone = localStorage.getItem('phone')
    email = localStorage.getItem('email');
    clinic_name: any;
    letterType: string = '';

    constructor(
        private titleService: Title,
        private route: ActivatedRoute,
        private services: UserServiceService,
        private cryptoService: CryptoService
    ) {
        this.titleService.setTitle(this.route.snapshot.data['title']);
    }

    ngOnInit(): void {
        this.getNoteDetails();
    }
    getNoteDetails() {
        this.services.getGeneratedLetter(this.note_id).subscribe((Response) => {
            const decryptedData = this.cryptoService.decrypt(Response.data);
            this.letterType = decryptedData?.letter_type;
        })
    }
}