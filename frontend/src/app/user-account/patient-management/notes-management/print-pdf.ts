import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { MessageService } from 'primeng/api';
import { CryptoService } from 'src/app/services/crypto.service';
import { UserServiceService } from 'src/app/services/user-service.service';
import { ErrorMessages } from 'src/app/validators/error-messages';
import { SvgIcons } from 'src/app/validators/svg-icons';

@Component({
    template: `
    <div class="card border-0 w-100 h-100">
        <div class="card-body w-100 h-100">
            <div class="align-items-center d-flex h-100 justify-content-center p-md-1 w-100">
                <div class="align-content-center flex-wrap gap-3 gap-xl-5 hstack justify-content-center">
                    <button type="button" (click)="onPrint()" class="align-items-center border-2 btn btn-light text-body d-flex flex-column justify-content-center p-2 rounded-4 theme-share-btns">
                        <span class="mb-2" [innerHTML]="svgIcons.printIcon | safeHtml"></span>
                        <span class="fw-medium fs-15">Print Letter</span>
                    </button>
                    <button type="button" (click)="onDownload()" class="align-items-center border-2 btn btn-light text-body d-flex flex-column justify-content-center p-2 rounded-4 theme-share-btns">
                        <span class="mb-2" [innerHTML]="svgIcons.downloadIcon | safeHtml"></span>
                        <span class="fw-medium fs-15">Download Letter</span>
                    </button>
                    <button type="button" (click)="onEmailTrigger()" class="align-items-center border-2 btn btn-light text-body d-flex flex-column justify-content-center p-2 rounded-4 theme-share-btns">
                        <span class="mb-2" [innerHTML]="svgIcons.emailIcon | safeHtml"></span>
                        <span class="fw-medium fs-15">Email Letter</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- Disclaimer Dialog -->
    <p-dialog 
            styleClass="theme-modal-main theme-modal-lg theme-modal-center" 
            header="Header" 
            [modal]="true" 
            [draggable]="false"
            [resizable]="false" 
            [dismissableMask]="true" 
            [(visible)]="visibleDisclaimerDialog" 
            [closable]="false"
            >
            <ng-template pTemplate="header">
                <h3 class="mb-0 fw-normal">Send Email</h3>
            </ng-template>
                <div class="theme-modal-body">
                    <form [formGroup]="emailForm">
                        <div class="form-group mb-4">
                            <label class="fs-14 fw-semibold mb-1 lh-sm" for="email">To<span class="text-danger">*</span></label>
                            <input pInputText id="email" formControlName="email" />
                            <div *ngIf="isFieldInvalid('email')" class="fs-14 lh-sm my-1 text-danger">
                                <div *ngIf="emailForm.get('email')?.errors?.['required']">
                                    {{errorMessages.email.required}}</div>
                                    <div *ngIf="emailForm.get('email')?.errors?.['invalidEmail']">Invalid email</div>
                            </div>
                        </div>
                        <div class="form-group mb-4">
                            <label class="fs-14 fw-semibold mb-1 lh-sm" for="email">CC</label>
                            <input pInputText id="cc_email" formControlName="cc_emails" />
                            <div *ngIf="isFieldInvalid('cc_emails')" class="fs-14 lh-sm my-1 text-danger">
                                <div *ngIf="emailForm.get('cc_emails')?.errors?.['required']">
                                    {{errorMessages.email.required}}</div>
                                <div *ngIf="emailForm.get('cc_emails')?.errors?.['invalidEmail']">Invalid CC email(s).</div>
                            </div>
                        </div>
                        <div class="form-group mb-4">
                            <label class="fs-14 fw-semibold mb-1 lh-sm" for="subject">Subject<span class="text-danger">*</span></label>
                            <input pInputText id="subject" formControlName="subject" />
                            <div *ngIf="isFieldInvalid('subject')" class="fs-14 lh-sm my-1 text-danger">
                                <div *ngIf="emailForm.get('subject')?.errors?.['required']">
                                    {{errorMessages.subject.required}}</div>
                                <div *ngIf="emailForm.get('subject')?.errors?.['maxlength']">
                                    {{errorMessages.subject.pattern}}</div>
                            </div>
                        </div>
                        <div class="form-group mb-4">
                            <label class="fs-14 fw-semibold mb-1 lh-sm" for="body">Body<span class="text-danger">*</span></label>
                            <textarea id="body" rows="5" pInputTextarea formControlName="body"></textarea>
                            <div *ngIf="isFieldInvalid('body')" class="fs-14 lh-sm my-1 text-danger">
                                <div *ngIf="emailForm.get('body')?.errors?.['required']">
                                    {{errorMessages.body.required}}</div>
                                <div *ngIf="emailForm.get('body')?.errors?.['maxlength']">
                                    {{errorMessages.body.pattern}}</div>
                            </div>
                        </div>
                        <div class="form-group mb-4 d-inline-flex bg-light px-2 py-1 rounded align-items-center">
                            <span class="me-2" [innerHTML]="svgIcons.attachmentIcon | safeHtml"></span>
                            Letter Attached
                        </div>
                    </form>            
                </div>  
                <ng-template pTemplate="footer">
                            <div class="gap-4 hstack justify-content-center">
                                <button (click)="onCancel()" class="btn btn-light bg-white text-body">Cancel</button>
                                <button (click)="onEmailSent()" class="btn btn-primary">Submit</button>
                            </div>
                        </ng-template>  
    </p-dialog>
    `
})

export class PrintPDF implements OnInit {
    svgIcons = SvgIcons;
    note_id = localStorage.getItem('note_id');
    is_printed: boolean = false;
    is_emailed: boolean = false;
    is_downloaded: boolean = false;
    documentUrl: string = '';
    userProfile: any = localStorage.getItem('userProfile');
    userProfileParse: any = JSON.parse(this.userProfile)
    doctorEmail = this.userProfileParse?.email;
    patientEmail: any;
    visibleDisclaimerDialog: boolean = false;
    emailForm: FormGroup;
    errorMessages = ErrorMessages.emailForm;

    constructor(
        private titleService: Title,
        private route: ActivatedRoute,
        private services: UserServiceService,
        private formBuilder: FormBuilder,
        private messageService: MessageService,
        private cryptoService: CryptoService
    ) {
        this.titleService.setTitle(this.route.snapshot.data['title']);
        this.emailForm = this.formBuilder.group({
            email: ['', [Validators.required, this.validateMultipleEmails.bind(this)]],
            cc_emails: ['', [this.validateMultipleEmails.bind(this)]],
            subject: ['', [Validators.required, Validators.maxLength(100)]],
            body: ['', [Validators.required, Validators.maxLength(300)]],
        });
    }



    ngOnInit(): void {
        this.getDocument();
    }

    markFormGroupTouched(formGroup: FormGroup) {
        Object.values(formGroup.controls).forEach((control) => {
            control.markAsTouched();
            if (control instanceof FormGroup) {
                this.markFormGroupTouched(control);
            }
        });
    }

    isFieldInvalid(fieldName: string): any {
        const field = this.emailForm.get(fieldName);
        return field?.invalid && (field?.dirty || field?.touched);
    }

    validateMultipleEmails(control: AbstractControl): { [key: string]: any } | null {
        const ccEmails = control.value ? control.value.split(',').map((email: string) => email.trim()) : [];

        for (const email of ccEmails) {
            if (email && !this.isValidEmail(email)) {
                return { invalidEmail: true };
            }
        }

        return null;
    }

    // Validate email using a regular expression
    isValidEmail(email: string): boolean {
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
        return emailRegex.test(email);
    }

    getDocument() {
        this.services.getGeneratedLetter(this.note_id).subscribe((Response) => {
            const decryptedData = this.cryptoService.decrypt(Response.data);
            this.documentUrl = decryptedData?.documentS3Url;
            this.patientEmail = decryptedData?.patient?.email;
            this.emailForm.patchValue({
                email: this.patientEmail
            })
        })
    }

    sharedStatus() {
        let data = {
            is_letter_downloaded: this.is_downloaded,
            is_letter_emailed: this.is_emailed,
            is_letter_printed: this.is_printed
        }
        const encryptedData = this.cryptoService.encrypt(data);
        let request = {
            encrypet: encryptedData
        }
        this.services.sharedStatus(this.note_id, request).subscribe((Response) => {
        })
    }

    onEmailTrigger() {
        this.visibleDisclaimerDialog = true;
    }

    onEmailSent() {
        if (this.emailForm.valid) {
            let data = {
                email: `${this.emailForm.value.email},${this.emailForm.value.cc_emails}`,
                subject: this.emailForm.value.subject,
                body: this.emailForm.value.body,
                id: this.note_id !== null ? parseInt(this.note_id) : null
            };
            const encryptedData = this.cryptoService.encrypt(data);
            let request = {
                encrypet: encryptedData
            }
            this.services.postEmailSent(request).subscribe((Response) => {
                if (Response.statusCode === 200) {
                    this.is_emailed = true;
                    this.sharedStatus();
                    this.visibleDisclaimerDialog = false;
                    this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Email Sent Successfully' });
                    this.emailForm.reset();
                    this.emailForm.patchValue({
                        email: this.patientEmail
                    })
                }
            }, (error) => {
                console.log('Error occurred:', error);
                let errorMsg = error?.error?.msg || 'An error occurred';
                this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Error Sending the email' });
            });
        }
        else {
            this.markFormGroupTouched(this.emailForm);
        }
    }

    onCancel() {
        this.visibleDisclaimerDialog = false;
        this.emailForm.reset();
        this.emailForm.patchValue({
            email: this.patientEmail
        })
    }

    onPrint() {
        const printWindow = window.open(this.documentUrl, '_blank');
        if (printWindow) {
            printWindow.onload = () => {
                printWindow.print();
            };
        }
        this.is_printed = true;
        this.sharedStatus();
    }


    onDownload() {
        const link = document.createElement('a');
        link.href = this.documentUrl;
        link.target = '_blank';
        link.download = 'generated_letter';
        link.click();
        this.is_downloaded = true; 
        this.sharedStatus(); 
    }
}