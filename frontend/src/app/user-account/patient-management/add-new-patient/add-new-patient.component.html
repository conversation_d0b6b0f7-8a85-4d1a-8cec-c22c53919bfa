<div class="d-flex flex-column pageContainer px-3 pb-3 h-100 overflow-y-scroll">
    <div class="contentHeader py-2 my-1">
        <div class="hstack gap-2 flex-wrap justify-content-between">
            <div class="theme-left-header">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb fw-medium">
                        <li class="breadcrumb-item">
                            <a class="text-decoration-none" [routerLink]="['/user/secure/patient-management']">Patient
                                Management</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Add Patient</li>
                    </ol>
                </nav>
                <h1 class="fw-normal h3 mb-0">Add Patient</h1>
            </div>
        </div>
    </div>
    <div class="flex-grow-1 pageContent position-relative">
        <div class="card border-0 h-100">
            <div class="card-body theme-data-table-cover w-100 h-100">
                <form class="p-xl-1 d-flex flex-column w-100 h-100" [formGroup]="newPatientForm"
                    (ngSubmit)="onSubmit()">
                    <div class="row">
                        <div class="col-12 mb-3">
                            <h5 class="fw-normal mb-0">Patient Information</h5>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="patient-name">First Name<span
                                        class="text-danger">*</span></label>
                                <input pInputText id="patient-first-name" formControlName="first_name" />
                                <div *ngIf="isFieldInvalid('first_name')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newPatientForm.get('first_name')?.errors?.['required']">
                                        {{errorMessages.first_name.required}}</div>
                                    <div *ngIf="newPatientForm.get('first_name')?.errors?.['pattern']">
                                        {{errorMessages.first_name.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="patient-name">Last Name<span
                                        class="text-danger">*</span></label>
                                <input pInputText id="patient-last-name" formControlName="last_name" />
                                <div *ngIf="isFieldInvalid('last_name')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newPatientForm.get('last_name')?.errors?.['required']">
                                        {{errorMessages.last_name.required}}</div>
                                    <div *ngIf="newPatientForm.get('last_name')?.errors?.['pattern']">
                                        {{errorMessages.last_name.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="dob">Date Of Birth<span
                                        class="text-danger">*</span></label>
                                <p-calendar appendTo="body" styleClass="w-100" formControlName="dob" [minDate]="minDate"
                                    [maxDate]="maxDate" placeholder="DD/MM/YYYY" dateFormat="dd/mm/yy"></p-calendar>
                                <div *ngIf="isFieldInvalid('dob')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.dob.required}}</div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm">Gender</label>
                                <p-dropdown placeholder="Select" [options]="gender"
                                    formControlName="gender"></p-dropdown>
                                <div *ngIf="isFieldInvalid('gender')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.gender.required}}</div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-8 col-lg-4 mb-4">
                            <label class="fs-14 fw-semibold mb-1 lh-sm" for="age">Age</label>
                            <div class="row">
                                <div class="col-4">
                                    <div class="form-group">
                                        <input pInputText id="age" formControlName="age" readonly />
                                    </div>
                                </div>
                                <div class="col-8">
                                    <div class="form-group">
                                        <p-dropdown placeholder="Select" [options]="period" formControlName="period"
                                            [disabled]="isAgeAutoPopulated()"></p-dropdown>
                                        <div *ngIf="isFieldInvalid('period')" class="fs-14 lh-sm my-1 text-danger">
                                            {{errorMessages.period.required}}</div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="col-sm-6 col-md-8 col-lg-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="insured-status">Insurance
                                    Status</label>
                                <p-dropdown placeholder="Select Insurance" [options]="insuranceList"
                                    formControlName="insuredStatus" optionLabel="name" optionValue="id"
                                    (onChange)="handleInsuranceChange()"></p-dropdown>
                                <div *ngIf="isFieldInvalid('insuredStatus')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.insuredBy.required}}</div>
                            </div>
                            <div class="form-group mt-3" *ngIf="showOtherTextbox">
                                <input pInputText placeholder="Enter Insurance Company" formControlName="others" />
                                <div *ngIf="isFieldInvalid('others')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newPatientForm.get('others')?.errors?.['required']">
                                        {{errorMessages.others.required}}</div>
                                    <div *ngIf="newPatientForm.get('others')?.errors?.['pattern']">
                                        {{errorMessages.others.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="last-checkup-date">Last Checkup Date
                                    Time</label>
                                <input pInputText id="last-checkup-date" formControlName="lastCheckupDate"
                                    [value]="currentDate" readonly />
                                <div *ngIf="isFieldInvalid('lastCheckupDate')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.lastCheckup.required}}</div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="reference-number">Reference
                                    Number</label>
                                <input pInputText id="reference-number" placeholder="Insurance Number"
                                    formControlName="refNumber" />
                                <div *ngIf="isFieldInvalid('refNumber')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newPatientForm.get('refNumber')?.errors?.['required']">
                                        {{errorMessages.refNumber.required}}</div>
                                    <div *ngIf="newPatientForm.get('refNumber')?.errors?.['pattern']">
                                        {{errorMessages.refNumber.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="reference-number">NHS
                                    Number</label>
                                <input pInputText id="nhs-number" formControlName="nhsNumber" />
                                <div *ngIf="isFieldInvalid('nhsNumber')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newPatientForm.get('nhsNumber')?.errors?.['required']">
                                        {{errorMessages.refNumber.required}}</div>
                                    <div *ngIf="newPatientForm.get('nhsNumber')?.errors?.['pattern']">
                                        {{errorMessages.nhsNumber.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 my-3">
                            <h5 class="fw-normal mb-0">Address Information</h5>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="address">Address</label>
                                <input pInputText id="address" formControlName="address"
                                    placeholder="Search by building name/number, street name or postcode"
                                    #addressInput />
                                <div *ngIf="isFieldInvalid('address')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newPatientForm.get('address')?.errors?.['required']">
                                        {{errorMessages.address.required}}</div>
                                    <div *ngIf="newPatientForm.get('address')?.errors?.['pattern']">
                                        {{errorMessages.address.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="country">Country</label>
                                <input pInputText id="country" formControlName="country" />
                                <div *ngIf="isFieldInvalid('country')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.country.required}}</div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="county">State</label>
                                <input pInputText id="county" formControlName="county" />
                                <div *ngIf="isFieldInvalid('county')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.county.required}}</div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="city">Town/City</label>
                                <input pInputText id="city" formControlName="city" />
                                <div *ngIf="isFieldInvalid('city')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.city.required}}</div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="zip-code">Post Code</label>
                                <input pInputText id="zip-code" formControlName="zip" />
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="email">Email</label>
                                <input pInputText id="email" formControlName="email" />
                                <div *ngIf="isFieldInvalid('email')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newPatientForm.get('email')?.errors?.['required']">
                                        {{errorMessages.email.required}}</div>
                                    <div *ngIf="newPatientForm.get('email')?.errors?.['email']">
                                        {{errorMessages.email.email}}</div>
                                    <div *ngIf="newPatientForm.get('email')?.errors?.['pattern']">
                                        {{errorMessages.email.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="phone">Phone</label>
                                <input pInputText id="phone" formControlName="phone" />
                                <div *ngIf="isFieldInvalid('phone')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newPatientForm.get('phone')?.errors?.['required']">
                                        {{errorMessages.phoneNumber.required}}</div>
                                    <div *ngIf="newPatientForm.get('phone')?.errors?.['pattern']">
                                        {{errorMessages.phoneNumber.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 my-3 d-flex align-items-center">
                            <p-checkbox value="check" inputId="check" formControlName="additional_fields"
                                styleClass="align-items-center fs-13 me-2 mt-1" [binary]="true"></p-checkbox>
                            <h5 class="fw-normal mb-0">Referee Details</h5><span class="fw-normal mb-0" style="padding-left: 5px;">(Please check this box to print Referee detail on letter header)</span>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="ref-name">Name</label>
                                <input pInputText id="ref-name" formControlName="refName" />
                                <div *ngIf="isFieldInvalid('refName')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newPatientForm.get('refName')?.errors?.['required']">
                                        {{errorMessages.first_name.required}}</div>
                                    <div *ngIf="newPatientForm.get('refName')?.errors?.['pattern']">
                                        {{errorMessages.first_name.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="refAddress">Address</label>
                                <input pInputText id="refAddress" formControlName="refAddress"
                                    placeholder="Search by building name/number, street name or postcode"
                                    #refAddressInput id="refAddressInput"/>
                                <div *ngIf="isFieldInvalid('refAddress')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newPatientForm.get('refAddress')?.errors?.['required']">
                                        {{errorMessages.address.required}}</div>
                                    <div *ngIf="newPatientForm.get('refAddress')?.errors?.['pattern']">
                                        {{errorMessages.address.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm"
                                    for="specialization">Specialisation</label>
                                <p-dropdown [options]="specialisationList" placeholder="Select"
                                    formControlName="specialisation" optionLabel="name"
                                    placeholder="Select Specialisation"
                                    (onChange)="handleSpecialisationChange()"></p-dropdown>
                                <div *ngIf="isFieldInvalid('specialisation')"
                                    class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.specialization.required}}</div>
                            </div>
                            <div class="form-group mt-3" *ngIf="showOtherTextbox">
                                <input pInputText placeholder="Enter Specialisation" formControlName="others" />
                                <div *ngIf="isFieldInvalid('others')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newPatientForm.get('others')?.errors?.['required']">
                                        {{errorMessages.others.required}}</div>
                                    <div *ngIf="newPatientForm.get('others')?.errors?.['pattern']">
                                        {{errorMessages.others.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="refEmail">Email</label>
                                <input pInputText id="refEmail" formControlName="refEmail" />
                                <div *ngIf="isFieldInvalid('refEmail')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newPatientForm.get('refEmail')?.errors?.['required']">
                                        {{errorMessages.email.required}}</div>
                                    <div *ngIf="newPatientForm.get('refEmail')?.errors?.['email']">
                                        {{errorMessages.email.email}}</div>
                                    <div *ngIf="newPatientForm.get('refEmail')?.errors?.['pattern']">
                                        {{errorMessages.email.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="refPhone">Phone</label>
                                <input pInputText id="refPhone" formControlName="refPhone" />
                                <div *ngIf="isFieldInvalid('refPhone')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newPatientForm.get('refPhone')?.errors?.['required']">
                                        {{errorMessages.phoneNumber.required}}</div>
                                    <div *ngIf="newPatientForm.get('refPhone')?.errors?.['pattern']">
                                        {{errorMessages.phoneNumber.pattern}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="hstack mt-auto justify-content-end gap-3">
                        <p-toast></p-toast>
                        <button type="submit" class="btn btn-primary mw-110">Submit</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>