import { Component } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { CryptoService } from 'src/app/services/crypto.service';
import { UserServiceService } from 'src/app/services/user-service.service';
import { ErrorMessages } from 'src/app/validators/error-messages';
import { SvgIcons } from 'src/app/validators/svg-icons';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-upload-patient',
  templateUrl: './upload-patient.component.html',
  styleUrls: ['./upload-patient.component.scss']
})
export class UploadPatientComponent {

  errorMessage: string = '';
  clinicId: any = localStorage.getItem('clinicId');
  documentUrl: string = ''
  filesPresent: boolean = false;
  discardedPatient: any[] = [];
  svgIcons = SvgIcons;
  public uploadPatientMessage = ErrorMessages.toolTips.uploadPatientMessage;
  public frontPath = environment.frontPath;
  visible: boolean = false;
  uploadedPatientLength: any;
  notUploadedPatientLength: any;
  formattedDob: any;

  constructor(private route: ActivatedRoute,
    private titleService: Title,
    private messageService: MessageService,
    private services: UserServiceService,
    private cryptoService: CryptoService,
    private router: Router
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
  }

  uploadPatientFile(fileInput: any) {
    this.errorMessage = '';
    if (fileInput.target.files && fileInput.target.files.length > 0) {
      const file = fileInput.target.files[0];

      // Check file size
      if (file.size > 1024 * 1024) {
        this.errorMessage = 'File size exceeds 1 MB limit.';
        return;
      }

      const formData = new FormData();
      formData.append('csv', file);
      formData.append('clinic_id', this.clinicId)

      this.services.postUploadPatientData(formData).subscribe({
        next: response => {
          const decryptedData = this.cryptoService.decrypt(response.data);
          if (response.statusCode === 200) {
            this.discardedPatient = decryptedData.notInserted || [];
            if (this.discardedPatient.length === 0) {
              this.filesPresent = false;
              this.router.navigate(['/user/secure/patient-management']);
              this.messageService.add({ severity: 'success', summary: 'Success', detail: response.msg });
            } else {
              this.filesPresent = true;
              this.notUploadedPatientLength = this.discardedPatient.length;
              if (decryptedData.inserted !== 0) {
                this.uploadedPatientLength = decryptedData.inserted;
                this.visible = true;
              }
            }
            fileInput.target.value = '';
          }
        },
        error: error => {
          this.messageService.add({ severity: 'error', summary: 'Error', detail: error?.error?.msg });
          fileInput.target.value = '';
        }
      });
    }
  }
  onSampleClick() {
    const link = document.createElement('a');
    link.href = `${this.frontPath}assets/files/patients.csv`;
    link.target = '_blank';
    link.download = 'patients.csv';
    link.click();
  }

  downloadCSV() {
    if (this.discardedPatient.length === 0) {
      console.log('No data to download.');
      return;
    }
    const csvData = this.discardedPatient.map(patient => {
    
      return {
        first_name: patient.first_name || '',
        last_name: patient.last_name || '',
        email: patient.email || '',
        dob: patient.dob || '',
        gender: patient.gender || '',
        phone: patient.phone || '',
        insurance: patient.insurance || '',
        nhs_number: patient.nhs_number || '',
        last_check_up_date: patient.last_check_up_date || '',
        reference_number: patient.reference_number || '',
        address: patient.address || '',
        country: patient.country_id || '',
        state: patient.county_id || '',
        town: patient.town_id || '',
        post_code: patient.post_code || '',
        others: patient.others || '',
      };
    });
    

    const csvString = this.convertToCSV(csvData);

    const blob = new Blob([csvString], { type: 'text/csv' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'patients.csv';
    link.click();
  }

  convertToCSV(data: any[]): string {
    const header = Object.keys(data[0]).join(',') + '\n';
    const body = data.map(row => Object.values(row).join(',') + '\n').join('');
    return header + body;
  }

  onClose() {
    this.visible = false;
  }

}
