<div class="d-flex flex-column pageContainer px-3 pb-3 h-100 overflow-y-scroll">
    <div class="contentHeader py-2 my-1">
        <div class="hstack gap-2 flex-wrap justify-content-between">
            <div class="theme-left-header">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb fw-medium">
                        <li class="breadcrumb-item">
                            <a class="text-decoration-none" [routerLink]="['/user/secure/patient-management']">Patient
                                Management</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Patient Details</li>
                    </ol>
                </nav>
                <h1 class="fw-normal h3 mb-0">Patient Details</h1>
            </div>
            <div class="hstack gap-2 flex-wrap">
            <a [routerLink]="['/user/secure/patient-management/notes/create-notes']" *ngIf="showCreateNotes" [queryParams]="{ patientId:patientId }" class="align-items-center btn btn-primary d-inline-flex">
                <span class="d-inline-flex me-2" [innerHTML]="svgIcons.createNoteSmallIcon | safeHtml"></span>
                Create
            </a>
            <a [routerLink]="['/user/secure/patient-management/edit-patient']" *ngIf="showEdit"  [queryParams]="{ patientId:patientId }" class="align-items-center btn btn-primary d-inline-flex">
                <span class="d-inline-flex me-2" [innerHTML]="svgIcons.editIcon | safeHtml"></span>
                Edit Patient
            </a>
            <a (click)="showDeleteDialog2(patientId)"  *ngIf="showDelete" class="align-items-center btn btn-primary d-inline-flex">
                <span class="d-inline-flex me-2" [innerHTML]="svgIcons.deleteIcon | safeHtml"></span>
                Delete Patient
            </a>
            </div>
        </div>
    </div>
    <div class="flex-grow-1 pageContent position-relative">
        <div class="d-flex flex-column h-100">
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card border-0 h-100">
                        <h5 class="border-bottom-0 card-header fw-normal pb-0">Patient Information</h5>
                        <div class="card-body pb-2">
                            <div class="row">
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.userCardIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Patient Name</div>
                                            <label class="fw-semibold">{{patientData?.name}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.ageGroupIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Age</div>
                                            <label class="fw-semibold">{{patientData?.age + ' ' +
                                                patientData?.period}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.genderIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Gender</div>
                                            <label class="fw-semibold">{{patientData?.gender}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.calendarIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Date of Birth</div>
                                            <label class="fw-semibold">{{patientData?.dob | date}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.umbrellaIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Insurance Status</div>
                                            <label class="fw-semibold">{{ patientData?.insurence?.name === 'Other' ?
                                                patientData?.others : patientData?.insurence?.name }}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.medicalBoardIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Last Checkup Date</div>
                                            <label class="fw-semibold">{{lastCheckUpDate}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.referralIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Reference Number</div>
                                            <label class="fw-semibold">{{patientData?.reference_number}}</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="card border-0 h-100">
                        <h5 class="border-bottom-0 card-header fw-normal pb-0">Address Information</h5>
                        <div class="card-body pb-2">
                            <div class="row">
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.locationIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Address</div>
                                            <label class="fw-semibold">{{patientData?.address}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.locationIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Country</div>
                                            <label class="fw-semibold">{{patientData?.country_id}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.locationIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>State</div>
                                            <label class="fw-semibold">{{patientData?.county_id}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.locationIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Town/City</div>
                                            <label class="fw-semibold">{{patientData?.town_id}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.postCodeIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Post Code</div>
                                            <label class="fw-semibold">{{patientData?.post_code}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.emailFilledIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Email</div>
                                            <a href="mailto:<EMAIL>"
                                                class="fw-semibold text-decoration-none text-body">{{patientData?.email}}</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary" [innerHTML]="svgIcons.phoneIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Phone</div>
                                            <a class="fw-semibold text-decoration-none text-body">{{ patientData?.phone
                                                }}</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4" *ngIf="doctorsData !== null">
                    <div class="card border-0 h-100">
                        <h5 class="border-bottom-0 card-header fw-normal pb-0 d-flex align-items-center"><span class="pe-2">Referee Details</span><span class="text-primary" [innerHTML]="svgIcons.checkIcon | safeHtml" *ngIf="hasReferral"></span></h5>
                        <div class="card-body pb-2">
                            <div class="row">
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.userCardIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Name</div>
                                            <label class="fw-semibold">{{doctorsData?.name}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.locationIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Address</div>
                                            <label class="fw-semibold">{{doctorsData?.address}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.medicalBoardIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Specialisation</div>
                                            <label class="fw-semibold">{{doctorsData?.specialization}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.emailFilledIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Email</div>
                                            <label class="fw-semibold">{{doctorsData?.email}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3">
                                    <div class="hstack gap-3">
                                        <span class="text-secondary"
                                            [innerHTML]="svgIcons.phoneIcon | safeHtml"></span>
                                        <div class="fs-14">
                                            <div>Phone</div>
                                            <a class="fw-semibold text-decoration-none text-body">{{doctorsData?.phone}}</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex-grow-1">
                <div class="card border-0 w-100 h-100">
                    <div class="card-body theme-data-table-cover theme-table-custom-header w-100 h-100">
                        <p-table styleClass="p-datatable-striped text-nowrap" [value]="patientNotes?.list"
                            [paginator]="false">
                            <ng-template pTemplate="caption">
                                <h5 class="fw-normal mb-0">
                                    Generated Letters
                                </h5>
                            </ng-template>
                            <ng-template pTemplate="header">
                                <tr>
                                    <th pSortableColumn="notes" scope="Notes">Notes
                                        <p-sortIcon field="notes"></p-sortIcon>
                                    </th>
                                    <th pSortableColumn="date" scope="Date">Date
                                        <p-sortIcon field="date"></p-sortIcon>
                                    </th>
                                    <th pSortableColumn="description" scope="Description">Description
                                        <p-sortIcon field="description"></p-sortIcon>
                                    </th>
                                    <th pSortableColumn="shared_status" scope="Shared_Status">Shared Status
                                        <p-sortIcon field="shared_status"></p-sortIcon>
                                    </th>
                                    <th style="width: 150px;" class="text-center" scope="Actions">Actions</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-element>
                                <tr>
                                    <td>{{ element.noteNumber }} </td>
                                    <td>{{ element?.createdAt ? (element.createdAt | date:'dd-MM-yyyy') : '-' }}</td>
                                    <td>{{ element?.clinical_note ? (element.clinical_note | slice:0:100) : '-' }}</td>
                                    <td>{{ getSharedStatus(element) }}</td>
                                    <td>
                                        <div class="hstack gap-3 justify-content-center">
                                            <a [routerLink]="['/user/view-note']" [queryParams]="{id: element.id}" appBsTooltip
                                                data-bs-toggle="tooltip" data-bs-placement="top" title="View"
                                                aria-label="View"
                                                class="btn btn-link h-20 p-1 rounded-1 w-20 d-flex align-items-center justify-content-center" *ngIf="showViewNote">
                                                <span [innerHTML]="svgIcons.eyeIcon | safeHtml"></span>
                                            </a>
                                            <button appBsTooltip data-bs-toggle="tooltip" data-bs-placement="top"
                                                title="Delete" aria-label="Delete"
                                                class="btn btn-link h-20 p-1 rounded-1 w-20 d-flex align-items-center justify-content-center"
                                                (click)="showDeleteDialog(element.id)" *ngIf="showNoteDelete">
                                                <span [innerHTML]="svgIcons.deleteIcon | safeHtml"></span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td class="bg-body-tertiary fw-medium text-center" colspan="5">No data found.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                        <p-paginator (onPageChange)="onPageChange($event)" [rows]="patientNotes?.recordsPerPage"
                            [totalRecords]="patientNotes?.totalRecords"></p-paginator>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--Confirmation Popup-->
<p-dialog styleClass="theme-modal-main theme-modal-sm theme-modal-center" header="Header" [modal]="true"
    [draggable]="false" [resizable]="false" [dismissableMask]="true" [(visible)]="visible" [closable]="false">
    <ng-template pTemplate="header">
        <h3 class="mb-0 fw-normal">Delete Note Record</h3>
    </ng-template>
    <div class="theme-modal-body">
        <p class="fw-medium mb-4 text-center">Are you sure you want to proceed?</p>
        <div class="gap-4 hstack justify-content-center">
            <button (click)="visible = false" class="btn btn-light bg-white text-body">Cancel</button>
            <button type="submit" class="btn btn-primary" (click)="deleteNote()">Delete</button>
        </div>
    </div>
</p-dialog>
<!-- Delete Dialog -->
<p-dialog styleClass="theme-modal-main theme-modal-sm theme-modal-center" header="Header" [modal]="true"
    [draggable]="false" [resizable]="false" [dismissableMask]="true" [(visible)]="visible2" [closable]="false">
    <ng-template pTemplate="header">
        <h3 class="mb-0 fw-normal">Delete Patient Record</h3>
    </ng-template>
    <div class="theme-modal-body">
        <p class="fw-medium mb-4 text-center">Are you sure you want to proceed?</p>
        <form [formGroup]="deleteReasonForm" (ngSubmit)="onSubmit()">
            <div class="form-group pb-1">
                <label class="fs-14 fw-semibold mb-1 lh-sm" for="message">Reason For Deleting Patient Record</label>
                <textarea id="deleted_reason" rows="5" pInputTextarea formControlName="deleted_reason"></textarea>
                <div *ngIf="isFieldInvalid('deleted_reason')" class="fs-14 lh-sm my-1 text-danger">
                    <div *ngIf="deleteReasonForm.get('deleted_reason')?.errors?.['required']">
                        {{errorMessages.message.required}}</div>
                    <div *ngIf="deleteReasonForm.get('deleted_reason')?.errors?.['maxlength']">
                        {{errorMessages.message.maxlength200}}</div>
                </div>
            </div>
            <p class="fw-medium fs-14 mb-4"><span class="fw-bold">Disclaimer<i
                        class="text-danger fst-normal">*</i>:</span> Once deleted, all patient details will be lost and
                cannot be retrieved.</p>
            <div class="gap-4 hstack justify-content-center">
                <button (click)="onCancel()" class="btn btn-light bg-white text-body">Cancel</button>
                <button type="submit" class="btn btn-primary">Delete</button>
            </div>
        </form>
    </div>
</p-dialog>