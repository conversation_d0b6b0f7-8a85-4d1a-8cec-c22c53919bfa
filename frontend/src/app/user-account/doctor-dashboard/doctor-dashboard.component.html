<div class="d-flex flex-column pageContainer px-3 pb-3 h-100 overflow-y-scroll">
    <div class="contentHeader py-3 my-1">
        <div class="hstack gap-2 flex-wrap justify-content-between">
            <div class="theme-left-header">
                <h1 class="fw-normal h3 mb-0">Doctor's Dashboard</h1>
            </div>
            <div class="flex-wrap gap-3 hstack">
                <div class="form-group theme-search-box">
                    <p-dropdown placeholder="Select" [options]="clinicList" optionLabel="name" optionValue="clinic_id" [(ngModel)]="selectedClinicId" [ngModelOptions]="
             {standalone: true}" (ngModelChange)="updateSelectedClinic($event)"></p-dropdown>
                </div>
            </div>
        </div>
    </div>
    <div class="flex-grow-1 pageContent position-relative">
        <div class="row">
            <div class="col-sm col-lg-4 mb-4">
                <div class="py-2 px-3 h-100 hstack gap-3 bg-secondary text-white rounded shadow-sm">
                    <div class="me-auto">
                        <h3 class="font-family-base fw-bold mb-1">{{ dashboardData?.totalPatientCount }}</h3>
                        <h5 class="font-family-base fw-normal mb-0">Patients</h5>
                    </div>
                    <span [innerHTML]="svgIcons.dahboardPatientsIcon | safeHtml" aria-hidden="true"
                        class="align-items-center d-flex justify-content-center my-1 rounded theme-dashboard-icon"></span>
                </div>
            </div>
            <div class="col-sm col-lg-4 mb-4">
                <div class="py-2 px-3 h-100 hstack gap-3 bg-secondary text-white rounded shadow-sm">
                    <div class="me-auto">
                        <h3 class="font-family-base fw-bold mb-1">{{ creditDocuments + subDocuments }}</h3>
                        <h5 class="font-family-base fs-6 fw-normal mb-0">Credits({{ creditDocuments }})+Subscribed({{ subDocuments }})</h5>
                    </div>
                    <span [innerHTML]="svgIcons.documentIcon | safeHtml" aria-hidden="true"
                        class="align-items-center d-flex justify-content-center my-1 rounded theme-dashboard-icon"></span>
                </div>
            </div>
        </div>
        <div class="card border-0">
            <div class="card-body">
                <div class="row align-items-center mb-2">
                    <div class="col mb-3">
                        <h4 class="mb-0 fw-normal">Number of Documents</h4>
                    </div>
                    <div class="col-md-7">
                        <div class="row">
                            <div class="col-sm mb-3">
                                <div class="form-group">
                                    <p-dropdown placeholder="Select" [options]="letterTypes" [(ngModel)]="selectedLetterType" (ngModelChange)="onLetterDropDownChange()"></p-dropdown>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="align-items-center row">
                    <div class="col-lg-auto col-xxl">
                        <div class="row gx-3 align-items-center">
                            <div class="col-sm-auto mb-3 text-center">
                                <div class="d-inline-flex position-relative theme-chart-container">
                                    <p-chart *ngIf="dashboardData?.letters?.countsDaily > 0" type="doughnut" width="213px" height="213px" [data]="data"
                                        [options]="options"></p-chart>
                                    <span *ngIf="dashboardData?.letters?.countsDaily > 0" aria-hidden="true" [innerHTML]="svgIcons.dashboardChartBg | safeHtml" class="position-absolute start-50 top-50 translate-middle theme-chart-bgImage text-body"></span>
                                </div>
                            </div>
                            <div class="col-sm mw-1">
                                <div class="input-group mb-3">
                                    <div class="border-end-0 form-control fs-6 lh-sm">Daily</div>
                                    <span
                                        class="font-family-base fs-5 fw-bold h5 input-group-text justify-content-center mb-0 px-2 theme-group-counter py-1 text-secondary">{{ dashboardData?.letters?.countsDaily }}</span>
                                </div>
                                <div class="input-group mb-3">
                                    <div class="border-end-0 form-control fs-6 lh-sm">Weekly</div>
                                    <span
                                        class="font-family-base fs-5 fw-bold h5 input-group-text justify-content-center mb-0 px-2 theme-group-counter py-1 text-info">{{ dashboardData?.letters?.countsWeekly }}</span>
                                </div>
                                <div class="input-group mb-3">
                                    <div class="border-end-0 form-control fs-6 lh-sm">Monthly</div>
                                    <span
                                        class="font-family-base fs-5 fw-bold h5 input-group-text justify-content-center mb-0 px-2 theme-group-counter py-1 text-primary">{{ dashboardData?.letters?.countsMonthly }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg col-xxl-7">
                        <div class="gx-3 row">
                            <div class="col mb-3">
                                <div
                                    class="align-items-center border border-3 d-flex flex-column justify-content-center p-1 rounded-4 theme-dashboard-letterbox">
                                    <span class="mb-2 pb-1 theme-dashboard-letter-icon1"
                                        [innerHTML]="svgIcons.printIcon | safeHtml"></span>
                                    <h3 class="font-family-base fw-bold mb-1 text-secondary">{{ dashboardData?.printDownloadEmailedCount?.printedCount }}</h3>
                                    <span class="fw-medium">Printed</span>
                                </div>
                            </div>
                            <div class="col mb-3">
                                <div
                                    class="align-items-center border border-3 d-flex flex-column justify-content-center p-1 rounded-4 theme-dashboard-letterbox">
                                    <span class="mb-2 pb-1 theme-dashboard-letter-icon2"
                                        [innerHTML]="svgIcons.downloadIcon | safeHtml"></span>
                                    <h3 class="font-family-base fw-bold mb-1 text-primary">{{ dashboardData?.printDownloadEmailedCount?.downloadedCount }}</h3>
                                    <span class="fw-medium">Downloaded</span>
                                </div>
                            </div>
                            <div class="col mb-3">
                                <div
                                    class="align-items-center border border-3 d-flex flex-column justify-content-center p-1 rounded-4 theme-dashboard-letterbox">
                                    <span class="mb-2 pb-1 theme-dashboard-letter-icon3"
                                        [innerHTML]="svgIcons.emailIcon | safeHtml"></span>
                                    <h3 class="font-family-base fw-bold mb-1 text-info">{{ dashboardData?.printDownloadEmailedCount?.emailedCount }}</h3>
                                    <span class="fw-medium">Emailed</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card border-0 mt-3">
            <div class="card-body theme-data-table-cover theme-table-custom-header">
                <p-table styleClass="p-datatable-striped text-nowrap" [value]="patientNotes?.list" [paginator]="false">
                    <ng-template pTemplate="caption">
                        <div class="row align-items-center">
                            <div class="col">
                                <h4 class="mb-md-0 fw-normal">Latest Clinical Notes</h4>
                            </div>
                            <div class="col-md-auto">
                                <div class="align-items-center gx-3 row">
                                    <div class="col-sm-auto">
                                        <div class="theme-search-box ms-md-auto position-relative">
                                            <p-calendar placeholder="Date Range" appendTo="body" styleClass="w-100"
                                                [showIcon]="true" selectionMode="range"
                                                [readonlyInput]="true" [(ngModel)]="selectedDateRange" (onSelect)="onSearch()"></p-calendar>
                                                <button class="align-items-center bg-white btn btn-clear-range btn-link d-inline-flex justify-content-center position-absolute px-3 z-1" [innerHTML]="svgIcons.closeIcon | safeHtml" aria-label="Close" *ngIf="selectedDateRange" (click)="clearDateRange()"></button>
                                        </div>
                                    </div>
                                    <!-- <div class="col-auto mt-sm-0 mt-2">
                                        <button class="btn btn-link fw-bold p-0 text-decoration-none">Show All</button>
                                    </div> -->
                                </div>
                            </div>
                        </div>
                    </ng-template>
                    <ng-template pTemplate="header">
                        <tr>
                            <th pSortableColumn="patientName" scope="Patient Name">Patient Name
                                <p-sortIcon field="patientName"></p-sortIcon>
                            </th>
                            <th pSortableColumn="email" scope="Email">Email
                                <p-sortIcon field="email"></p-sortIcon>
                            </th>
                            <th pSortableColumn="clinic" scope="Clinic">Clinic
                                <p-sortIcon field="clinic"></p-sortIcon>
                            </th>
                            <th pSortableColumn="generationDate" scope="Generation Date">Generation Date
                                <p-sortIcon field="generationDate"></p-sortIcon>
                            </th>
                            <th pSortableColumn="rlStatus" scope="RL Status">RL Status
                                <p-sortIcon field="rlStatus"></p-sortIcon>
                            </th>
                            <th pSortableColumn="sharedStatus" scope="Shared Status">Shared Status
                                <p-sortIcon field="sharedStatus"></p-sortIcon>
                            </th>
                            <th style="width: 90px;" class="text-center" scope="Actions">Actions</th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-element>
                        <tr>
                            <td>{{ element?.patient?.name || '-' }}</td>
                            <td>{{ element?.patient?.email || '-'}}</td>
                            <td>{{ (element.clinic?.name || '-').replace('Individual Clinic', '') }}</td>
                            <td>{{ element?.createdAt ? (element.createdAt | date:'dd-MM-yyyy') : '-' }}</td>
                            <td>Generated</td>
                            <td>{{ getSharedStatus(element) }}</td>
                            <td>
                                <div class="hstack gap-3 justify-content-center">
                                    <a [routerLink]="['/user/view-note']" [queryParams]="{id: element.id}" appBsTooltip data-bs-toggle="tooltip"
                                        data-bs-placement="top" title="View" aria-label="View"
                                        class="btn btn-link h-20 p-1 rounded-1 w-20 d-flex align-items-center justify-content-center">
                                        <span [innerHTML]="svgIcons.eyeIcon | safeHtml"></span>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="emptymessage" >
                        <tr>
                            <td class="bg-body-tertiary fw-medium text-center" colspan="7">No data found.</td>
                        </tr>
                    </ng-template>
                </p-table>
                <p-paginator (onPageChange)="onPageChange($event)" [rows]="patientNotes?.recordsPerPage"
                            [totalRecords]="patientNotes?.totalRecords"></p-paginator>
            </div>
        </div>
    </div>
</div>