@import "../../../assets/css/variables";
.pageContainer {
    .pageContent {
        .btn-clear-range {
            padding: 1px;
            right: 2px;
            top: 2px;
            bottom: 2px;
        }
        .theme-dashboard-icon {
            width: 60px;
            height: 60px;
            background-color: rgba($white, 0.10);
        }
        .theme-dashboard-letterbox{
            min-height: 154px;
            min-width: 125px;
        }
        .theme-group-counter {
            min-width: 70px;
        }
        ::ng-deep{
            .theme-dashboard-letter-icon1{
                svg{
                    width: 39px;
                    height: 39px;
                }
            }
            .theme-dashboard-letter-icon2{
                svg{
                    width: 36px;
                    height: 39px;
                }
            }
            .theme-dashboard-letter-icon3{
                svg{
                    width: 34px;
                    height: 34px;
                }
            }
        }
    }
}