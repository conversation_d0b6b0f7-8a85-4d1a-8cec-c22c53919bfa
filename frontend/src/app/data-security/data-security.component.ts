import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { UserServiceService } from '../services/user-service.service';

@Component({
  selector: 'app-data-security',
  templateUrl: './data-security.component.html',
  styleUrls: ['./data-security.component.scss']
})
export class DataSecurityComponent implements OnInit{
  securingDatas: any[] = [
    {
      securingDataImg: "securing-data-img-1.png",
      securingDataTitle: "Data Sharing",
      securingDataDetails: "We never sell or share your data.",
    },
    {
      securingDataImg: "securing-data-img-2.png",
      securingDataTitle: "Your Data",
      securingDataDetails: "We simply store and protect your data, but you remain the owner.",
    }, {
      securingDataImg: "securing-data-img-3.png",
      securingDataTitle: "Encryption",
      securingDataDetails: "We use end-to-end encryption and SSL/TLS protocols to transmit and store your data securely.",
    }, {
      securingDataImg: "securing-data-img-4.png",
      securingDataTitle: "Safe and Secure Partners",
      securingDataDetails: "We ensure our partners are safe and secure with robust security standards.",
    }, {
      securingDataImg: "securing-data-img-5.png",
      securingDataTitle: "Restricted",
      securingDataDetails: "All your data will only be visible to you and us. At no point will your data be accessible to third parties.",
    }, {
      securingDataImg: "securing-data-img-6.png",
      securingDataTitle: "Trained Staff",
      securingDataDetails: "All our employees are trained in Privacy and Data Security in accordance with GDPR standards.",
    }
  ];
  compliances: any[] = [
    // {
    //   complianceImg: "compliance-img-1.png",
    //   complianceTitle: "Australian Privacy Principles",
    // },
    {
      complianceImg: "compliance-img-2.png",
      complianceTitle: "GDPR",
    },
    // {
    //   complianceImg: "compliance-img-3.png",
    //   complianceTitle: "HIPAA",
    // }
  ];
  dataSecurityContent: string = '';
  constructor(
    private titleService: Title,
    private route: ActivatedRoute,
    private services: UserServiceService
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
  }
  ngOnInit(): void {
    this.getCmsContent();
  }
  getCmsContent() {
    let id = 2;
    this.services.getCMSContent(id).subscribe((Response:any) => {
      this.dataSecurityContent = Response.data.content;
    })
  }
}
