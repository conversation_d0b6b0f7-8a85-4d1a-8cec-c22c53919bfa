@import "../assets/css/variables";

::ng-deep {
    .webLoader {
        position: fixed;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        background-color: rgba($black, 0.4);
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        cursor: progress;
        z-index: 9999;

        .webLoaderinner {
            .p-progress-spinner {
                width: 50px;
                height: 50px;
                display: block;
            }

            >span {
                display: inline-block;
                width: 100%;
                text-transform: uppercase;
                font-weight: bold;
                letter-spacing: 4px;
                margin-top: 5px;
                color: $white;
                text-shadow: 1px 1px 6px rgba($black, 0.16);
            }
        }
    }
}