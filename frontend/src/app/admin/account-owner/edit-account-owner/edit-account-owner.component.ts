import { DatePipe } from '@angular/common';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { AdminService } from 'src/app/services/admin.service';
import { CryptoService } from 'src/app/services/crypto.service';
import { UserServiceService } from 'src/app/services/user-service.service';
import { addressValidation, clinicRegNumberValidator, emailValidation, nameValidation, phoneValidator, postCodeValidator } from 'src/app/validators/constant';
import { ErrorMessages } from 'src/app/validators/error-messages';
import { SvgIcons } from 'src/app/validators/svg-icons';
import { environment } from 'src/environments/environment';
declare var google: any;

@Component({
  selector: 'app-edit-account-owner',
  templateUrl: './edit-account-owner.component.html',
  styleUrls: ['./edit-account-owner.component.scss']
})
export class EditAccountOwnerComponent implements OnInit {

  @ViewChild('addressInput', { static: true })
  public addressInput!: ElementRef;
  editOwnerForm: FormGroup;
  ownerId: any;
  ownerData: any;
  getToken = localStorage.getItem('adminUser');
  onBoardingDate: any;
  showOtherTextbox: boolean = false;
  specialisationList: { id: number, name: string }[] = [];
  public gmcNumberInfo = ErrorMessages.toolTips.gmcNumberInfo;
  public clinicRegNumInfo = ErrorMessages.toolTips.clinicRegNumInfo;
  svgIcons = SvgIcons;
  errorMessages: any = ErrorMessages.registrationForm;
  selectedCountyId: number | null = null;
  successShown: boolean = false;
  private readonly googleMapsApiKey: string = environment.googleMapsApiKey;

  constructor(
    private messageService: MessageService,
    private services: AdminService,
    private userServices: UserServiceService,
    private titleService: Title,
    private route: ActivatedRoute,
    private datePipe: DatePipe,
    private formBuilder: FormBuilder,
    private router: Router,
    private cryptoService: CryptoService
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
    this.editOwnerForm = this.formBuilder.group({
      first_name: ['', [Validators.required, nameValidation]],
      last_name: ['', [Validators.required, nameValidation]],
      email: [{ value: '', disabled: true }],
      clinic_name: ['', [Validators.required, nameValidation]],
      address: ['', [Validators.required,addressValidation]],
      phone: ['', [Validators.required,phoneValidator]],
      country: ['', [Validators.required]],
      city: ['', Validators.required],
      county: ['', Validators.required],
      zip: ['', [Validators.required]],
      others: [''],
      onBoardingDate: [{ value: '', disabled: true }],
      regNumber: ['', [clinicRegNumberValidator]],
      specialisation: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.ownerId = params['ownerId'];
    });
    this.getSpecList();
    this.getOwnerData();
    this.initAutocomplete();
  }
  private initAutocomplete(): void {
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = `https://maps.googleapis.com/maps/api/js?key=${this.googleMapsApiKey}&libraries=places`;
    script.onload = () => {
      const autocomplete = new google.maps.places.Autocomplete(
        this.addressInput.nativeElement,
        {
          types: ['geocode']
        }
      );
      autocomplete.addListener('place_changed', () => {
        if (this.editOwnerForm) {
          const place = autocomplete.getPlace();

          if (place.geometry) {
            const addressComponents = place.address_components;
            let selectedAddress = '';
            addressComponents.forEach((component: any) => {
              const componentType = component.types[0];
              switch (componentType) {
                case 'street_number':
                  this.editOwnerForm!.get('address')!.setValue(component.long_name);
                  break;
                case 'route':
                  const existingRoute = this.editOwnerForm!.get('address')!.value || '';
                  this.editOwnerForm!.get('address')!.setValue(existingRoute + ' ' + component.long_name);
                  break;
                case 'postal_code':
                case 'country':
                case 'administrative_area_level_1':
                case 'locality':
                  break;
                default:
                  selectedAddress += component.long_name + ' ';
                  break;
              }
            });
            selectedAddress = selectedAddress.trim();
            this.editOwnerForm!.get('address')!.setValue(selectedAddress);
            this.editOwnerForm!.get('city')!.setValue(place.address_components.find((c: any) => c.types.includes('locality'))?.long_name || '');
            this.editOwnerForm!.get('county')!.setValue(place.address_components.find((c: any) => c.types.includes('administrative_area_level_1'))?.long_name || '');
            this.editOwnerForm!.get('country')!.setValue(place.address_components.find((c: any) => c.types.includes('country'))?.long_name || '');
            this.editOwnerForm!.get('zip')!.setValue(place.address_components.find((c: any) => c.types.includes('postal_code'))?.long_name || '');
          }
        }
      });
    };
    script.onerror = (error) => {
      console.error('Error loading Google Maps script:', error);
    };
    document.head.appendChild(script);
  }
  getSpecList() {
    this.userServices.getSpecList().subscribe((response) => {
      if (response.statusCode == 200) {
        const sortedSpecialisationList = [];
        for (const item of response.data) {
          if (item.name !== 'Other') {
            sortedSpecialisationList.push({ id: item.id, name: item.name });
          }
        }
        sortedSpecialisationList.push({ id: '83', name: 'Other' });

        this.specialisationList = sortedSpecialisationList;
      } else {
        this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Error Loading Specialisations' });
      }
    });
  }
  handleSpecialisationChange() {
    const selectedSpecialisation: any = this.specialisationList.find(spec => spec.name === this.editOwnerForm.value.specialisation.name);
    this.showOtherTextbox = selectedSpecialisation.name === "Other";
    const othersField = this.editOwnerForm.get('others');
    if (this.showOtherTextbox && othersField) {
      othersField.setValidators([Validators.required, Validators.pattern(/^[A-Za-z\s]{5,50}$/)]);
    } else if (othersField) {
      othersField.clearValidators();
    }
    if (othersField) {
      othersField.updateValueAndValidity();
    }
  }

  isFieldInvalid(fieldName: string): any {
    const field = this.editOwnerForm.get(fieldName);
    return field?.invalid && (field?.dirty || field?.touched);
  }

  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
  getOwnerData() {
    this.services.getAccountOwnerDetails(this.ownerId, this.getToken).subscribe((Response) => {
      const decryptedData = this.cryptoService.decrypt(Response.data);
      this.ownerData = decryptedData;
        const formattedDob = this.datePipe.transform(this.ownerData.created_at, 'yyyy-MM-dd');
        const onBoardingDate = formattedDob ? new Date(formattedDob) : null;
      this.editOwnerForm.patchValue({
        first_name: this.ownerData.first_name,
        last_name: this.ownerData.last_name,
        email: this.ownerData.email,
        clinic_name: this.ownerData.clinic_name.replace('Individual Clinic', ''),
        address: this.ownerData.address,
        zip: this.ownerData.pincode,
        phone: this.ownerData.phone,
        regNumber: this.ownerData.reg_gmc_no,
        onBoardingDate: onBoardingDate,
        country: this.ownerData.country,
        county: this.ownerData.county_id,
        city: this.ownerData.town_id
      })
      this.editOwnerForm.controls['specialisation'].setValue(this.ownerData.clinical_specializations_id);
    })
  }

  onSubmit() {
    if(this.editOwnerForm.valid) {
      let data: { [key: string]: any } = {
        clinic_id: this.ownerData?.clinic_id,
        first_name: this.editOwnerForm.value.first_name,
        last_name: this.editOwnerForm.value.last_name,
        phone: this.editOwnerForm.value.phone,
        pincode: this.editOwnerForm.value.zip,
        address: this.editOwnerForm.value.address,
        clinic_name: this.editOwnerForm.value.clinic_name,
        reg_gmc_no: this.editOwnerForm.value.regNumber,
        country: this.editOwnerForm.value.country,
        county_id: this.editOwnerForm.value.county,
        town_id: this.editOwnerForm.value.city,
        clinical_specializations_id: this.editOwnerForm.value.specialisation
      }
      if (this.editOwnerForm.value.others) {
        data['others'] = this.editOwnerForm.value.others;
      }
      const encryptedData = this.cryptoService.encrypt(data);
      let request = {
        encrypet: encryptedData
      }
      this.services.updateAccountOwner(this.ownerId,request,this.getToken).subscribe((Response) => {
        if (!this.successShown) {
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Account Owner Details Updated Successfully' });
          this.successShown = true;
        }
        setTimeout(() => {
          this.router.navigate(['/admin/secure/account-owner']);
        }, 1000);
      },
      (error) => {
        console.log('Error occurred:', error);
        let errorMsg = error?.error?.msg || 'An error occurred';
        this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
      });
    } else {
      this.markFormGroupTouched(this.editOwnerForm);
    }
  }
}
