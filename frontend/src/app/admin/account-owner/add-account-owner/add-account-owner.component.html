<div class="d-flex flex-column pageContainer px-3 pb-3 h-100 overflow-y-scroll">
    <div class="contentHeader py-2 my-1">
        <div class="hstack gap-2 flex-wrap justify-content-between">
            <div class="theme-left-header me-auto">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb fw-medium">
                        <li class="breadcrumb-item">
                            <a class="text-decoration-none" [routerLink]="['/admin/secure/account-owner']">Account Owners</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Add Account Owner</li>
                    </ol>
                </nav>
                <h1 class="fw-normal h3 mb-0">Add Account Owner</h1>
            </div>
        </div>
    </div>
    <div class="flex-grow-1 pageContent position-relative">
        <div class="card border-0 h-100">
            <div class="card-body theme-data-table-cover w-100 h-100">
                <form class="p-xl-1 d-flex flex-column w-100 h-100">
                    <div class="row">
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="account-owner">Account Owner</label>
                                <input pInputText id="account-owner" />
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="email">Email</label>
                                <input pInputText id="email" />
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="clinic">Clinic</label>
                                <input pInputText id="clinic" />
                            </div>
                        </div>
                        <div class="col-md-8 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="address">Address</label>
                                <input pInputText id="address" />
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm">Country</label>
                                <p-dropdown placeholder="Select"></p-dropdown>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm">State</label>
                                <p-dropdown placeholder="Select"></p-dropdown>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm">Town/City</label>
                                <p-dropdown placeholder="Select"></p-dropdown>
                            </div>
                        </div>                       
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="post-code">Post Code</label>
                                <input pInputText id="post-code" />
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="phone-number">Phone Number</label>
                                <input pInputText id="phone-number" />
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="registration-number">Registration Number</label>
                                <input pInputText id="registration-number" />
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm">Specialisation</label>
                                <p-dropdown placeholder="Select"></p-dropdown>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm">Onboarding Date</label>
                                <p-calendar appendTo="body" styleClass="w-100" [showIcon]="true" ></p-calendar>
                            </div>
                        </div>                  
                    </div>
                    <div class="hstack mt-auto justify-content-end gap-3">
                        <p-toast></p-toast>
                        <button type="submit" class="btn btn-primary mw-110">Submit</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>