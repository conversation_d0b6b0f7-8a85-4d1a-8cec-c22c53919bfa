<div class="d-flex flex-column pageContainer px-3 pb-3 h-100 overflow-y-scroll">
    <div class="contentHeader py-3 my-1">
        <div class="hstack gap-3 flex-wrap justify-content-between">
            <div class="theme-left-header me-auto">
                <h1 class="fw-normal h3 mb-0">Account Owners</h1>
            </div>
            <!-- <a [routerLink]="['add-account-owner']"
                class="align-items-center btn btn-primary d-inline-flex order-lg-last">
                Add Account Owner
            </a> -->
            <div class="flex-fill flex-wrap gap-3 hstack justify-content-end">
                <div class="col-12 col-lg-auto col-sm theme-header-field">
                    <span class="p-input-icon-left w-100">
                        <i class="text-body text-opacity-25" [innerHTML]="svgIcons.searchIcon | safeHtml"></i>
                        <input pInputText aria-label="Search" placeholder="Search" (input)="onSearch()" [(ngModel)]="searchInput"/>
                    </span>
                </div>
                <div class="col-12 col-lg-auto col-sm theme-header-field">
                    <p-dropdown placeholder="Select Clinic" [options]="clinicList" optionLabel="name" (onChange)="onClinicSelect($event.value)"></p-dropdown>
                </div>
            </div>
        </div>
    </div>
    <div class="flex-grow-1 pageContent position-relative">
        <div class="card border-0">
            <div class="card-body theme-data-table-cover w-100 h-100">
                <p-table styleClass="p-datatable-striped text-nowrap" [value]="ownersList?.list" [paginator]="false">
                    <ng-template pTemplate="header">
                        <tr>
                            <th pSortableColumn="accountOwner" scope="Account Owner">Account Owner
                                <p-sortIcon field="accountOwner"></p-sortIcon>
                            </th>
                            <th pSortableColumn="clinic" scope="Clinic">Clinic
                                <p-sortIcon field="clinic"></p-sortIcon>
                            </th>
                            <th class="text-center" pSortableColumn="noOfUsers" scope="No. of Users">No. of Users
                                <p-sortIcon field="noOfUsers"></p-sortIcon>
                            </th>
                            <th class="text-center" pSortableColumn="noOfLetters" scope="No. of Letters">No. of Letters
                                <p-sortIcon field="noOfLetters"></p-sortIcon>
                            </th>
                            <th class="text-center" pSortableColumn="onboardingDate" scope="Onboarding Date">Onboarding
                                Date
                                <p-sortIcon field="onboardingDate"></p-sortIcon>
                            </th>
                            <th class="text-center" pSortableColumn="status" scope="Status" *ngIf="showApprove">Status
                                <p-sortIcon field="status"></p-sortIcon>
                            </th>
                            <th class="text-center" style="width: 100px;" scope="Actions">Actions</th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-element>
                        <tr>
                            <td>{{ element?.first_name + ' ' + element?.last_name|| '-' }}</td>
                            <td>{{ (element?.name || '-').replace('Individual Clinic', '') }}</td>
                            <td class="text-center pe-4"><span class="pe-1">{{ element?.user_count }}</span></td>
                            <td class="text-center pe-4"><span class="pe-1">{{ element?.letter_count }}</span></td>
                            <td class="text-center pe-4"><span class="pe-1">{{ element?.created_at ? (element.created_at | date:'dd-MM-yyyy') : '-' }}</span></td>
                            <td class="text-center pe-4" *ngIf="showApprove">
                                <span class="pe-1">
                                    <p-inputSwitch [ngModel]="element.status === 1" (ngModelChange)="onStatusChange(element,element?.id, element?.clinic_id)"></p-inputSwitch>
                                </span>
                            </td>
                            <td>
                                <div class="hstack gap-3 justify-content-center">
                                    <a [routerLink]="['view-account-owner']" [queryParams]="{ ownerId:element?.id }"
                                        data-bs-toggle="tooltip" data-bs-placement="top" title="View" aria-label="View" appBsTooltip
                                        class="btn btn-link h-20 p-1 rounded-1 w-20 d-flex align-items-center justify-content-center" *ngIf="showView">
                                        <span [innerHTML]="svgIcons.eyeIcon | safeHtml"></span>
                                    </a>
                                    <a [routerLink]="['edit-account-owner']" [queryParams]="{ ownerId:element?.id }" appBsTooltip
                                        data-bs-toggle="tooltip" data-bs-placement="top" title="Edit" aria-label="Edit"
                                        class="btn btn-link h-20 p-1 rounded-1 w-20 d-flex align-items-center justify-content-center" *ngIf="showEdit">
                                        <span [innerHTML]="svgIcons.editIcon | safeHtml"></span>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="emptymessage" >
                        <tr>
                            <td class="bg-body-tertiary fw-medium text-center" colspan="7">No data found.</td>
                        </tr>
                    </ng-template>
                </p-table>
                <p-paginator (onPageChange)="onPageChange($event)" [rows]="ownersList?.recordsPerPage"
                            [totalRecords]="ownersList?.totalRecords"></p-paginator>
            </div>
        </div>
    </div>
</div>
<p-dialog styleClass="theme-modal-main theme-modal-sm theme-modal-center" header="Header" [modal]="true"
    [draggable]="false" [resizable]="false" [dismissableMask]="true" [(visible)]="visible" [closable]="false">
    <ng-template pTemplate="header">
        <h3 class="mb-0 fw-normal">Changing Account Owner</h3>
    </ng-template>
    <div class="theme-modal-body">
        <p class="fw-medium mb-4 text-center">Are you sure you want to proceed?</p>
        <form [formGroup]="deleteReasonForm" (ngSubmit)="onSubmit()">
            <div class="form-group pb-1">
                <label class="fs-14 fw-semibold mb-1 lh-sm" for="message">Reason For Changing Account Owner Status</label>
                <textarea id="deleted_reason" rows="5" pInputTextarea formControlName="deleted_reason"></textarea>
                <div *ngIf="isFieldInvalid('deleted_reason')" class="fs-14 lh-sm my-1 text-danger">
                    <div *ngIf="deleteReasonForm.get('deleted_reason')?.errors?.['required']">
                        {{errorMessages.message.required}}</div>
                    <div *ngIf="deleteReasonForm.get('deleted_reason')?.errors?.['maxlength']">
                        {{errorMessages.message.maxlength200}}</div>
                </div>
            </div>
            <div class="gap-4 hstack justify-content-center">
                <button (click)="onCancel()" class="btn btn-light bg-white text-body">Cancel</button>
                <button type="submit" class="btn btn-primary">Proceed</button>
            </div>
        </form>
    </div>
</p-dialog>
<p-toast></p-toast>