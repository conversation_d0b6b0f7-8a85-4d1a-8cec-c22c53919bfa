import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router} from '@angular/router';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LoggedInAuthGuard implements CanActivate {
  constructor(
    private router: Router,
) {}
  // Restricting Routes
  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    if (localStorage.getItem('adminUser')) {
      if(state.url.includes("/admin/admin-login/")){ 
        return true;
      }
      else{  
        this.router.navigate(['admin/dashboard'])
      }
      return false;
    }
    else {      
      return true;
    }
  }

}
