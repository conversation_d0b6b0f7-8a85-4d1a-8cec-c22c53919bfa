import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { AdminService } from 'src/app/services/admin.service';
import { CryptoService } from 'src/app/services/crypto.service';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-user-subscription-details',
  templateUrl: './user-subscription-details.component.html',
  styleUrls: ['./user-subscription-details.component.scss']
})
export class UserSubscriptionDetailsComponent implements OnInit {

  subId: any;
  getToken = localStorage.getItem('adminUser');
  subDetailsData: any[] = [];
  subData: any;
  startDate: any;
  nextDate: any;
  cancellationDate: any;

  constructor(
    private route: ActivatedRoute,
    private titleService: Title,
    private services: AdminService,
    private cryptoService: CryptoService,
    private datePipe: DatePipe,
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
  }
  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.subId = params['subId'];
    });
    this.getUserSubDetail();
  }
  getStatusDisplay(status: number, cancellationReason: string | null): string {
    if (cancellationReason !== null) {
      return 'Cancelled';
    }
    switch (status) {
      case 1:
        return 'Active';
      case 8:
        return 'Incomplete';
      case 4:
        return 'Past Due';
      case 5:
        return 'Trailing';
      case 9:
        return 'Cancelled';
      case 2:
        return 'Expired';
      default:
        return 'Cancelled';
    }
  }
  getUserSubDetail() {
    this.services.getUserSubsDetail(this.subId, this.getToken).subscribe((Response) => {
      const decryptedData = this.cryptoService.decrypt(Response.data);
      this.subData = decryptedData;
      this.startDate = this.datePipe.transform(this.subData?.start_date, 'dd/MM/yy') ?? '';
      this.nextDate = this.datePipe.transform(this.subData?.next_billing_date, 'dd/MM/yy') ?? '';
      this.cancellationDate = this.datePipe.transform(this.subData?.cancellation_date, 'dd/MM/yy') ?? '';
      this.subDetailsData = [
        {
          title: "Name",
          details: (this.subData?.user?.first_name + ' ' + this.subData?.user?.last_name) || '-',
        },
        {
          title: "Email",
          details: this.subData?.user?.email || '-',
        },
        {
          title: "Phone",
          details: this.subData?.user?.phone || '-',
        },
        {
          title: "Plan",
          details: this.subData?.subscription_features?.name || '-',
        },
        {
          title: "Plan Type",
          details: this.subData?.subscription_features?.plan_type || '-',
        },
        // {
        //   title: "Currency",
        //   details: this.subData?.subscription_features?.currency || '-',
        // },
        {
          title: "Price",
          details: '£'+this.subData?.subscription_features?.price || '-',
        },
        {
          title: "Start Date",
          details: this.startDate || '-',
        },
        {
          title: "Next Billing Date",
          details: this.nextDate || '-',
        },
        {
          title: "Plan Status",
          details: this.getStatusDisplay(this.subData?.status, this.subData?.cancellation_reason),
        },
        {
          title: "Cancellation Reason",
          details: this.subData?.cancellation_reason || '-',
        },
        {
          title: "Cancellation Date",
          details: this.cancellationDate || '-',
        }
      ];
    })
  }
}
