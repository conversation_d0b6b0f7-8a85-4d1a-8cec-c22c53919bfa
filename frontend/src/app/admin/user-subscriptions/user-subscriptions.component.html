<div class="d-flex flex-column pageContainer px-3 pb-3 h-100 overflow-y-scroll">
    <div class="contentHeader py-3 my-1">
        <div class="hstack gap-3 flex-wrap justify-content-between">
            <div class="theme-left-header me-auto">
                <h1 class="fw-normal h3 mb-0">User Subscriptions</h1>
            </div>
            <!-- <a [routerLink]="['add-subscription']"
                class="align-items-center btn btn-primary d-inline-flex order-lg-last">
                Add Subscription
            </a> -->
            <!-- <div class="flex-fill flex-wrap gap-3 hstack justify-content-end">
                <div class="col-12 col-lg-auto col-sm theme-header-field">
                    <span class="p-input-icon-left w-100">
                        <i class="text-body text-opacity-25" [innerHTML]="svgIcons.searchIcon | safeHtml"></i>
                        <input pInputText aria-label="Search" placeholder="Search" (input)="onSearch()"
                            [(ngModel)]="searchInput" />
                    </span>
                </div>
            </div> -->
        </div>
    </div>
    <div class="flex-grow-1 pageContent position-relative">
        <div class="card border-0">
            <div class="card-body theme-data-table-cover w-100 h-100">
                <p-table styleClass="p-datatable-striped text-nowrap" [value]="userSubList?.list" [paginator]="false">
                    <ng-template pTemplate="header">
                        <tr>
                            <th scope="Name">Name
                            </th>
                            <th scope="Email">Email
                            </th>
                            <th scope="Plan">Plan
                            </th>
                            <th scope="Plan Type">Plan Type
                            </th>
                            <th scope="Price">Price
                            </th>
                            <th scope="Plan Status">Plan Status
                            </th>
                            <th scope="Actions">Actions
                            </th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-element>
                        <tr>
                            <td>{{ element?.user?.first_name + ' ' + element?.user?.last_name }}</td>
                            <td>{{ element?.user?.email }}</td>
                            <td>{{ element?.subscription_features?.name }}</td>
                            <td>{{ element?.billing_frequency }}</td>
                            <td>£{{ element?.price }}
                            </td>
                            <td *ngIf="element.status === 1 && !element.cancellation_reason"><span
                                    class="badge fw-semibold bg-success px-3 py-2 fs-15">Active</span>
                            </td>
                            <td *ngIf="element.status === 8">
                                <span class="badge fw-semibold bg-danger px-3 py-2 fs-15">Incomplete</span>
                            </td>
                            <td *ngIf="element.status === 4">
                                <span class="badge fw-semibold bg-warning px-3 py-2 fs-15">Past Due</span>
                            </td>
                            <td *ngIf="element.status === 5">
                                <span class="badge fw-semibold bg-info px-3 py-2 fs-15">Trailing</span>
                            </td>
                            <td *ngIf="element.cancellation_reason || element.status === 9">
                                <span class="badge fw-semibold bg-danger px-3 py-2 fs-15">Cancelled</span>
                            </td>
                            <td *ngIf="element.status === 2">
                                <span class="badge fw-semibold bg-danger px-3 py-2 fs-15">Expired</span>
                            </td>
                            <td>
                                <!-- <div class="hstack gap-3 justify-content-center"> -->
                                <a [routerLink]="['view-user-subscription']" [queryParams]="{ subId:element?.id }"
                                    data-bs-toggle="tooltip" data-bs-placement="top" title="View" aria-label="View"
                                    appBsTooltip
                                    class="btn btn-link h-20 p-1 rounded-1 w-20 d-flex align-items-center justify-content-center">
                                    <span [innerHTML]="svgIcons.eyeIcon | safeHtml"></span>
                                </a>
                            </td>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="emptymessage">
                        <tr>
                            <td class="bg-body-tertiary fw-medium text-center" colspan="7">No data found.</td>
                        </tr>
                    </ng-template>
                </p-table>
                <p-paginator (onPageChange)="onPageChange($event)" [rows]="userSubList?.recordsPerPage"
                    [totalRecords]="userSubList?.totalRecords"></p-paginator>
            </div>
        </div>
    </div>
</div>
<!--Confirmation Popup-->
<!-- <p-dialog styleClass="theme-modal-main theme-modal-sm theme-modal-center" header="Header" [modal]="true"
    [draggable]="false" [resizable]="false" [dismissableMask]="true" [(visible)]="visible2" [closable]="false">
    <ng-template pTemplate="header">
        <h3 class="mb-0 fw-normal">Delete Note Record</h3>
    </ng-template>
    <div class="theme-modal-body">
        <p class="fw-medium mb-4 text-center">Are you sure you want to proceed?</p>
        <div class="gap-4 hstack justify-content-center">
            <button (click)="visible2 = false" class="btn btn-light bg-white text-body">Cancel</button>
            <button type="submit" class="btn btn-primary" (click)="deletePlan()">Delete</button>
        </div>
    </div>
</p-dialog> -->
<p-toast></p-toast>