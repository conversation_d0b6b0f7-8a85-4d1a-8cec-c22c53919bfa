import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { AdminService } from 'src/app/services/admin.service';
import { CryptoService } from 'src/app/services/crypto.service';
import { addressValidation, currencyOptions, nameValidation, planTypes, properNumValidation, rationalNumValidation } from 'src/app/validators/constant';
import { ErrorMessages } from 'src/app/validators/error-messages';

@Component({
  selector: 'app-add-subscription',
  templateUrl: './add-subscription.component.html',
  styleUrls: ['./add-subscription.component.scss']
})
export class AddSubscriptionComponent implements OnInit {

  newPlanForm: FormGroup;
  successShown: boolean = false;
  isSubmitting: boolean = false;
  currencyOptions = currencyOptions;
  planTypes = planTypes;
  errorMessages = ErrorMessages.planForm;
  getToken = localStorage.getItem('adminUser');

  constructor(
    private formBuilder: FormBuilder,
    private services: AdminService,
    private messageService: MessageService,
    private titleService: Title,
    private route: ActivatedRoute,
    private router: Router,
    private cryptoService: CryptoService
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
    this.newPlanForm = this.formBuilder.group({
      planName: ['', [Validators.required,nameValidation]],
      description: ['', [Validators.required,addressValidation]],
      // currency: ['', [Validators.required]],
      price: ['', [Validators.required,rationalNumValidation]],
      planType: ['', [Validators.required]],
      additionalDocPrice: ['', [Validators.required,rationalNumValidation]],
      trialDays: ['', [Validators.required,properNumValidation]],
      freeDocs: ['', [Validators.required,properNumValidation]],
      includedDocs: ['', [Validators.required,properNumValidation]],
      dictationLimit: ['', [Validators.required,properNumValidation]],
      maxTeam: ['', [Validators.required,properNumValidation]],
      freeSupport: [false],
      cancelAnytime: [false],
      pFA: [false],
    });
  }
  ngOnInit(): void {

  }
  
  isFieldInvalid(fieldName: string): any {
    const field = this.newPlanForm.get(fieldName);
    return field?.invalid && (field?.dirty || field?.touched);
  }

  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
  onSubmit() {
    if (this.isSubmitting) {
      return;
    }
    if (this.newPlanForm.valid) {
      this.isSubmitting = true;
      let data: { [key: string]: any } = {
        name: this.newPlanForm.value.planName,
        description: this.newPlanForm.value.description,
        price: this.newPlanForm.value.price,
        additional_document_price: this.newPlanForm.value.additionalDocPrice,
        currency: 'GBP',
        plan_type: this.newPlanForm.value.planType,
        trial_period_days: this.newPlanForm.value.trialDays,
        free_trial_docs: this.newPlanForm.value.freeDocs,
        included_docs: this.newPlanForm.value.includedDocs,
        dictation_limit_per_doc_min: this.newPlanForm.value.dictationLimit,
        max_team_members: this.newPlanForm.value.maxTeam,
        free_support: this.newPlanForm.value.freeSupport,
        cancel_anytime: this.newPlanForm.value.cancelAnytime,
        priority_feature_access: this.newPlanForm.value.pFA
      }
      const encryptedData = this.cryptoService.encrypt(data);
      let request = {
        encrypet: encryptedData
      }
      this.services.postAddPlan(request, this.getToken).subscribe((Response) => {
        this.isSubmitting = false;
        if (Response.statusCode == 200) {
          this.newPlanForm.reset();
          if (!this.successShown) {
            this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Plan Added Successfully' });
            this.successShown = true;
            const decryptedData = this.cryptoService.decrypt(Response.data);
            let planId = decryptedData?.id;
            setTimeout(() => {
              this.router.navigate(['/admin/secure/subscription-plans/view-subscription'], {
                queryParams: { planId: planId }
              });
            }, 1000);
          }
        }
      },
        (error) => {
          this.isSubmitting = false;
          console.log('Error occurred:', error);
          let errorMsg = error?.error?.msg || 'An error occurred';
          this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
        })
    } else {
      this.markFormGroupTouched(this.newPlanForm);
    }
  }
}
