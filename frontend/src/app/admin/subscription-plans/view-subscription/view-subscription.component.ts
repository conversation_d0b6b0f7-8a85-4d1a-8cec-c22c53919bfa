import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { AdminService } from 'src/app/services/admin.service';
import { CryptoService } from 'src/app/services/crypto.service';

@Component({
  selector: 'app-view-subscription',
  templateUrl: './view-subscription.component.html',
  styleUrls: ['./view-subscription.component.scss']
})
export class ViewSubscriptionComponent implements OnInit {

  planId: any;
  getToken = localStorage.getItem('adminUser');
  planDetailsData: any[] = [];
  planData: any;

  constructor(
    private titleService: Title,
    private route: ActivatedRoute,
    private services: AdminService,
    private cryptoService: CryptoService
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
  }

  getYesNo(value: boolean): string {
    return value ? 'Yes' : 'No';
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.planId = params['planId'];
    });
    this.getPlanDetails();
  }
  getPlanDetails() {
    this.services.getPlanDetails(this.planId, this.getToken).subscribe((Response) => {
      const decryptedData = this.cryptoService.decrypt(Response.data);
      this.planData = decryptedData;
      this.planDetailsData = [
        {
          title: "Plan Name",
          details: this.planData?.name || '-',
        },
        {
          title: "Description",
          details: this.planData?.description || '-',
        },
        // {
        //   title: "Currency",
        //   details: this.planData?.currency || '-',
        // },
        {
          title: "Price",
          details: '£'+this.planData?.price || '-',
        },
        {
          title: "Plan Type",
          details: this.planData?.plan_type || '-',
        },
        {
          title: "Addl Document Price",
          details: this.planData?.additional_document_price || '-',
        },
        {
          title: "Trial Period Days",
          details: this.planData?.trial_period_days || '-',
        },
        {
          title: "Free Trial Docs",
          details: this.planData?.free_trial_docs || '-',
        },
        {
          title: "Included Docs",
          details: this.planData?.included_docs || '-',
        },
        {
          title: "Dictation Limit(min)",
          details: this.planData?.dictation_limit_per_doc_min || '-',
        },
        {
          title: "Max Team Members",
          details: this.planData?.max_team_members || '-',
        },
        {
          title: "Free Support",
          details: this.getYesNo(this.planData?.free_support),
        },
        // {
        //   title: "Cancel Anytime",
        //   details: this.getYesNo(this.planData?.cancel_anytime),
        // },
        {
          title: "Priority Feature Access",
          details: this.getYesNo(this.planData?.priority_feature_access),
        },
      ];
    })
  }
}
