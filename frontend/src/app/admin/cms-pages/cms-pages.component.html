<div class="d-flex flex-column pageContainer px-3 pb-3 h-100 overflow-y-scroll">
    <div class="contentHeader py-3 my-1">
        <div class="hstack gap-2 flex-wrap justify-content-between">
            <div class="theme-left-header">
                <h1 class="fw-normal h3 mb-0">CMS Pages</h1>
            </div>
            <!-- <div class="theme-search-box">
                <span class="p-input-icon-left w-100">
                    <i class="text-body text-opacity-25" [innerHTML]="svgIcons.searchIcon | safeHtml"></i>
                    <input pInputText aria-label="Search" placeholder="Search" />
                </span>
            </div> -->
        </div>
    </div>
    <div class="flex-grow-1 pageContent position-relative">
        <div class="card border-0">
            <div class="card-body theme-data-table-cover w-100 h-100">
                <p-table styleClass="p-datatable-striped text-nowrap" [value]="cmsList" [paginator]="false" [rows]="10" [rowsPerPageOptions]="[10, 25, 50]"
                    [showCurrentPageReport]="true" currentPageReportTemplate="{first}-{last} of {totalRecords}">
                    <ng-template pTemplate="header">
                        <tr>
                            <th style="width: 25%;" pSortableColumn="page_name" scope="Page Name">Page Name
                                <p-sortIcon field="page_name"></p-sortIcon>
                            </th>
                            <th pSortableColumn="description" scope="Description">Description
                                <p-sortIcon field="description"></p-sortIcon>
                            </th>
                            <th class="text-center" style="width: 100px;" scope="Actions">Actions</th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-element>
                        <tr>
                            <td>{{ element?.title }}</td>
                            <td>{{ element?.content | slice:0:100 || '-'}}</td>
                            <td>
                                <div class="hstack gap-3 justify-content-center">
                                    <a [routerLink]="['edit-cms-pages']" [queryParams]="{ id:element.id }" appBsTooltip data-bs-toggle="tooltip"
                                        data-bs-placement="top" title="Edit" aria-label="Edit"
                                        class="btn btn-link h-20 p-1 rounded-1 w-20 d-flex align-items-center justify-content-center" *ngIf="showEdit">
                                        <span [innerHTML]="svgIcons.editIcon | safeHtml"></span>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="emptymessage" >
                        <tr>
                            <td class="bg-body-tertiary fw-medium text-center" colspan="3">No data found.</td>
                        </tr>
                    </ng-template>
                </p-table>
            </div>
        </div>
    </div>
</div>