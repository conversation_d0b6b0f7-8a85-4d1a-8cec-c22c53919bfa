import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { MessageService } from 'primeng/api';
import { AdminService } from 'src/app/services/admin.service';
import { CryptoService } from 'src/app/services/crypto.service';
import { SvgIcons } from 'src/app/validators/svg-icons';

@Component({
  selector: 'app-cms-pages',
  templateUrl: './cms-pages.component.html',
  styleUrls: ['./cms-pages.component.scss']
})
export class CmsPagesComponent implements OnInit{
  svgIcons = SvgIcons;
  getToken = localStorage.getItem('adminUser')
  cmsList: any;
  userId: any = localStorage.getItem('userId');
  showEdit: boolean = false;
  roleName: any = localStorage.getItem('roleName')

  constructor(
    private services: AdminService,
    private titleService: Title,
    private route: ActivatedRoute,
    private messageService: MessageService,
    private cryptoService: CryptoService
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
  }
   ngOnInit(): void {
     this.getCmsList();
     this.getUserDetail();
     if(this.roleName === 'SUPER_ADMIN') {
      this.showEdit = true
     }
   }

   getCmsList() {
    this.services.getCMSList(this.getToken).subscribe((Response) => {
      this.cmsList = Response.data;
    })
   }
   getUserDetail() {
    this.services.getAdminDetails(this.userId, this.getToken).subscribe((Response) => {
      const decryptedData = this.cryptoService.decrypt(Response.data);
      const selectedPermissions = decryptedData.permissions
        .filter((permission: any) => permission.isSelected === 1)
        .map((permission: any) => permission.id);
      if (selectedPermissions.includes(6)) {
        this.showEdit = true;
      }
    })
  }
}
