import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { AdminService } from 'src/app/services/admin.service';
import { CryptoService } from 'src/app/services/crypto.service';
import { UserServiceService } from 'src/app/services/user-service.service';
import { addressValidation, emailValidation, nameValidation, phoneValidator, postCodeValidator } from 'src/app/validators/constant';
import { ErrorMessages } from 'src/app/validators/error-messages';
import { SvgIcons } from 'src/app/validators/svg-icons';

interface CheckboxItem {
  id: number;
  section: string;
  name: string;
  description: string;
  label: string;
  status: number;
  isSelected: number;
}
@Component({
  selector: 'app-add-user',
  templateUrl: './add-user.component.html',
  styleUrls: ['./add-user.component.scss']
})
export class AddUserComponent implements OnInit {

  roleId: any = 2;
  addUserForm: FormGroup;
  errorMessages = ErrorMessages.registrationForm;
  rolesList: any;
  rolesListData: any;
  getToken = localStorage.getItem('adminUser');
  permissionsList: CheckboxItem[] = [];
  updatedPermissionList: CheckboxItem[] = [];
  groupedPermissions: { [section: string]: CheckboxItem[] } = {};
  countryList: { id: number, name: string }[] = [];
  countyList: { id: number, name: string }[] = [];
  cityList: { id: number, name: string }[] = [];
  selectedCountryId: number | null = null;
  selectedCountyId: number | null = null;
  isSubmitting: boolean = false;
  public postCodeInfo = ErrorMessages.toolTips.postCodeInfo;
  svgIcons = SvgIcons;

  constructor(
    private route: ActivatedRoute,
    private titleService: Title,
    private formBuilder: FormBuilder,
    private services: AdminService,
    private userServices: UserServiceService,
    private messageService: MessageService,
    private router:Router,
    private cryptoService: CryptoService
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
    this.addUserForm = this.formBuilder.group({
      first_name: ['', [Validators.required, nameValidation]],
      last_name: ['', [Validators.required, nameValidation]],
      email: ['', [Validators.required, Validators.email]],
      address: ['', [Validators.required,addressValidation]],
      phone: ['', [Validators.required,phoneValidator]],
      country: ['', Validators.required],
      city: ['', Validators.required],
      county: ['', Validators.required],
      zip: ['', [Validators.required, postCodeValidator]],
    });
  }

  ngOnInit(): void {
    this.initializePermissions();
    this.getCountryList();
  }

  initializePermissions() {
    this.services.getAdminPermssions(this.getToken).subscribe((response) => {
      if (response.statusCode === 200) {
      const decryptedData = this.cryptoService.decrypt(response.data);
        this.permissionsList = decryptedData.filter((permission: CheckboxItem) =>
          permission.name !== 'Setting-Delete' && permission.name !== 'Setting-Add' && permission.name !== 'ACCOUNT-OWNER-ADD' && permission.name !== 'ACCOUNT-OWNER-DELETE' && permission.name !== 'CMS-VIEW'
        );
        this.groupPermissions();
        this.getRolesPerimissions();
        this.createFormControls();
      } else {
        this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Failed to fetch permissions' });
      }
    });
  }

  groupPermissions() {
    this.groupedPermissions = {};

    for (const permission of this.permissionsList) {
      if (!this.groupedPermissions[permission.section]) {
        this.groupedPermissions[permission.section] = [];
      }
      this.groupedPermissions[permission.section].push(permission);
    }
  }

  createFormControls() {
    const permissionKeys = this.addUserForm.controls;
    for (const section of Object.keys(this.groupedPermissions)) {
      for (const permission of this.groupedPermissions[section]) {
        if (!permissionKeys[permission.name]) {
          permissionKeys[permission.name] = this.formBuilder.control(false);
        }
      }
    }
  }

  isFieldInvalid(fieldName: string): any {
    const field = this.addUserForm.get(fieldName);
    return field?.invalid && (field?.dirty || field?.touched);
  }



  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
  getRolesPerimissions() {
    this.services.getAdminRolePermssions(this.roleId,this.getToken).subscribe((Response) => {
      const decryptedData = this.cryptoService.decrypt(Response.data);
      this.rolesList = decryptedData;
      
      // Loop through the permissionsList and set form controls dynamically
      for (const permission of this.rolesList) {
        const controlName = permission.name;
        const isSelected = permission.isSelected === 1;
        if (this.addUserForm.get(controlName)) {
          this.addUserForm.get(controlName)?.setValue(isSelected);
        }
      }
    },
    (error) => {
      console.log('Error occurred:', error);
        let errorMsg = error?.error?.msg || 'An error occurred';
        this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
    });
  }
  getCountryList() {
    this.userServices.getCountryList().subscribe((response) => {
      if (response.statusCode == 200) {
        for (const item of response.data) {
          this.countryList.push({ id: item.id, name: item.name });
        }
      } else {
        this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Error Loading Countries' });
      }
    })
  }

  onCountryChange(event: any) {
    const selectedCountry: any = this.countryList.find(country => country.name === this.addUserForm.value.country.name);
    this.selectedCountryId = selectedCountry?.id || null; // Store selected country ID
    this.getCountyList();
  }

  getCountyList() {
    if (this.selectedCountryId) {
      this.countyList = [];
      this.userServices.getCountyList(this.selectedCountryId).subscribe((response) => {
        if (response.statusCode == 200) {
          for (const item of response.data) {
            this.countyList.push({ id: item.id, name: item.name });
          }
        } else {
          this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Error Loading States' });
        }
      });
    } else {
      this.countyList = [];
    }
  }

  onCountyChange(event: any) {
    const selectedCounty: any = this.countyList.find(county => county.name === this.addUserForm.value.county.name);
    this.selectedCountyId = selectedCounty?.id || null; // Store selected county ID
    this.getCityList();
  }


  getCityList() {
    if (this.selectedCountyId) {
      this.cityList = [];
      this.userServices.getCityList(this.selectedCountyId).subscribe((response) => {
        if (response.statusCode == 200) {
          for (const item of response.data) {
            this.cityList.push({ id: item.id, name: item.name });
          }
        } else {
          this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Error Loading Cities' });
        }
      });
    } else {
      this.cityList = [];
    }
  }

  onSubmit() {
    if (this.isSubmitting) {
      return;
    }
    if(this.addUserForm.valid) {
      this.isSubmitting = true;
      const selectedCounty: any = this.countyList.find(county => county.name === this.addUserForm.value.county?.name);
      const selectedCity: any = this.cityList.find(city => city.name === this.addUserForm.value.city?.name);
      const selectedCountry: any = this.countryList.find(country => country.name === this.addUserForm.value.country.name);
      const permissions: number[] = [];
        for (const permission of this.permissionsList) {
          if (this.addUserForm.value[permission.name]) {
            permissions.push(permission.id);
            permission.isSelected = 1;
          } else {
            permission.isSelected = 0;
          }
        }
      let data: { [key: string]: any } ={
        first_name: this.addUserForm.value.first_name,
        last_name: this.addUserForm.value.last_name,
        email: this.addUserForm.value.email,
        phone: this.addUserForm.value.phone,
        address: this.addUserForm.value.address,
        postcode: this.addUserForm.value.zip,
        country_id: selectedCountry?.id,
        role_id: 2,
        permissions: permissions,
      }
      if (selectedCounty && selectedCounty.id) {
        data['county_id'] = selectedCounty.id;
      }
      if (selectedCity && selectedCity.id) {
        data['town_id'] = selectedCity.id;
      }
      const encryptedData = this.cryptoService.encrypt(data);
      let request = {
        encrypet: encryptedData
      }
      this.services.postAddAdmin(request, this.getToken).subscribe((Response) => {
        this.isSubmitting = false;
        this.addUserForm.reset();
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'User Invited Successfully' });
      },
      (error) => {
        this.isSubmitting = false;
        console.log('Error occurred:', error);
          let errorMsg = error?.error?.msg || 'An error occurred';
          this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
      })
    } else {
      this.markFormGroupTouched(this.addUserForm);
    }
  }
}
