@import "../../../assets/css/variables";

.pageContainer {
    .pageContent {
        .theme-dashboard-bg {
            background-color: #EDEFFF;
            border-bottom-color: #DCD3FF;
        }

        .theme-dashboard-icon {
            width: 60px;
            height: 60px;
            background-color: rgba($white, 0.10);
        }        

        .bg-info-opacity-20 {
            background-color: rgba($info, 0.20);
        }

        .bg-secondary-opacity-20 {
            background-color: rgba($secondary, 0.20);
        }

        .theme-dashboard-letterbox {
            min-height: 154px;
            min-width: 125px;
        }

        .theme-group-counter {
            min-width: 70px;
        }

        @include above-1300(){
            .theme-col-xl-3 {
                flex: 0 0 auto;
                width: 25%;
            }
            .theme-col-xl-6 {
                flex: 0 0 auto;
                width: 50%;
            }
        }

        ::ng-deep {
            .theme-dashboard-letter-icon1 {
                svg {
                    width: 39px;
                    height: 39px;
                }
            }

            .theme-dashboard-letter-icon2 {
                svg {
                    width: 36px;
                    height: 39px;
                }
            }

            .theme-dashboard-letter-icon3 {
                svg {
                    width: 34px;
                    height: 34px;
                }
            }
            .theme-dashboard-star {
                svg {
                    width: 32px;
                    height: 29px;
                }
            }
        }
    }
}