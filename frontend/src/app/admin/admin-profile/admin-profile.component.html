<div class="d-flex flex-column pageContainer px-3 pb-3 h-100 overflow-y-scroll">
    <div class="contentHeader py-3 my-1">
        <div class="hstack gap-2 flex-wrap justify-content-between">
            <div class="theme-left-header">
                <h1 class="fw-normal h3 mb-0">Profile</h1>
            </div>
        </div>
    </div>
    <div class="flex-grow-1 pageContent position-relative">
        <div class="card border-0 h-100">
            <div class="card-body theme-data-table-cover w-100 h-100">
                <form class="p-xl-1 d-flex flex-column w-100" [formGroup]="adminProfileForm" (ngSubmit)="onSubmit()">

                    <div class="row">
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="first-name">First Name<span class="text-danger">*</span></label>
                                <input pInputText id="first-name" formControlName="first_name" />
                                <div *ngIf="isFieldInvalid('first_name')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="adminProfileForm.get('first_name')?.errors?.['required']">
                                        {{errorMessages.firstName.required}}</div>
                                    <div *ngIf="adminProfileForm.get('first_name')?.errors?.['pattern']">
                                        {{errorMessages.firstName.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="last-name">Last Name<span class="text-danger">*</span></label>
                                <input pInputText id="last-name" formControlName="last_name" />
                                <div *ngIf="isFieldInvalid('last_name')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="adminProfileForm.get('last_name')?.errors?.['required']">
                                        {{errorMessages.firstName.required}}</div>
                                    <div *ngIf="adminProfileForm.get('last_name')?.errors?.['pattern']">
                                        {{errorMessages.firstName.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="email">Email<span class="text-danger">*</span></label>
                                <input pInputText id="email" formControlName="email" />
                                <div *ngIf="isFieldInvalid('email')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="adminProfileForm.get('email')?.errors?.['required']">
                                        {{errorMessages.email.required}}</div>
                                    <div *ngIf="adminProfileForm.get('email')?.errors?.['email']">
                                        {{errorMessages.email.email}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 mt-4 mb-3">
                            <h6 class="font-family-base fw-bold mb-0">Enter Password Information</h6>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="password">Password<span class="text-danger">*</span>
                                    <a appBsTooltip data-bs-html="true" data-bs-toggle="tooltip" data-bs-placement="top"
                                        title={{passwordInfoMessage}}
                                        class="btn btn-link text-body align-bottom d-inline-block infoIcon ms-1 p-0">
                                        <span class="theme-info-icon" [innerHTML]="svgIcons.infoIcon | safeHtml"></span>
                                        <span class="visually-hidden">{{passwordInfoMessage}}</span>
                                    </a>
                                </label>
                                <p-password id="password" [feedback]="false" [toggleMask]="true"
                                    formControlName="password"></p-password>
                                <div *ngIf="isFieldInvalid('password')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="adminProfileForm.get('password')?.errors?.['pattern']">
                                        {{errorMessages.password.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-4 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="confirm-password">Confirm
                                    Password<span class="text-danger">*</span>
                                    <a appBsTooltip data-bs-html="true" data-bs-toggle="tooltip" data-bs-placement="top"
                                        title={{passwordInfoMessage}}
                                        class="btn btn-link text-body align-bottom d-inline-block infoIcon ms-1 p-0">
                                        <span class="theme-info-icon" [innerHTML]="svgIcons.infoIcon | safeHtml"></span>
                                        <span class="visually-hidden">{{passwordInfoMessage}}</span>
                                    </a>
                                </label>
                                <p-password id="confirm-password" [feedback]="false" [toggleMask]="true"
                                    formControlName="confirmPassword"></p-password>
                                <div *ngIf="isFieldInvalid('confirmPassword')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="adminProfileForm.get('confirmPassword')?.errors?.['passwordMismatch']">
                                        {{errorMessages.confirmPassword.equalTo}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 mb-4">
                            <div class="form-group">
                                <ngx-recaptcha2 #captchaElem [siteKey]="siteKey"
                                    formControlName="recaptcha"></ngx-recaptcha2>
                            </div>
                            <div *ngIf="isFieldInvalid('recaptcha')" class="fs-14 lh-sm my-1 text-danger">
                                {{errorMessages.captcha.required}}</div>
                        </div>
                    </div>
                    <div class="hstack mt-3 justify-content-end gap-3">
                        <p-toast></p-toast>
                        <button type="submit" class="btn btn-primary mw-110">Update</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>