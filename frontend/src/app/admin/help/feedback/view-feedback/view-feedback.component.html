<div class="d-flex flex-column pageContainer px-3 pb-3 h-100 overflow-y-scroll">
    <div class="contentHeader py-2 my-1">
        <div class="hstack gap-2 flex-wrap justify-content-between">
            <div class="theme-left-header me-auto">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb fw-medium">
                        <li class="breadcrumb-item">
                            <a class="text-decoration-none" [routerLink]="['/admin/secure/help/feedback']">Feedback</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Feedback Details</li>
                    </ol>
                </nav>
                <h1 class="fw-normal h3 mb-0">Feedback Details</h1>
            </div>          
        </div>
    </div>
    <div class="flex-grow-1 pageContent position-relative">
        <div class="card border-0 h-100">
            <div class="card-body theme-data-table-cover w-100 h-100">
                <form [formGroup]="replyForm" (ngSubmit)="onSubmit()" class="p-xl-1 d-flex flex-column w-100 h-100">
                    <div class="row">
                        <div class="col-sm-6 col-md-4 col-lg-3 mb-4" *ngFor="let feedback of feedbackViewData">
                            <div class="form-group">
                                <div class="fs-14">{{feedback.title}}</div>
                                <div class="fs-14 fw-semibold mt-1">{{feedback.details}}</div>
                            </div>
                        </div>                      
                    </div>
                    <ng-container *ngIf="feedbackData">
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <div class="fs-14">Feedback</div>
                                    <div class="fs-14 fw-semibold mt-1">{{feedbackData?.feedback_text}}</div>
                                </div>
                            </div>                      
                        </div>
                        <ng-container *ngIf="showReplyForm">
                            <hr>
                            <div class="row">
                                <div class="col-6">
                                    <h4>Reply to user</h4>
                                    <div class="form-group pb-1">
                                        <label class="fs-14 fw-semibold mb-1 lh-sm" for="replySubject">Subject</label>
                                        <input maxlength="50" type="text" id="replySubject" rows="5" pInputTextarea
                                            formControlName="subject" />
                                        <div *ngIf="isFieldInvalid('subject')" class="fs-14 lh-sm my-1 text-danger">
                                            <div *ngIf="replyForm.get('subject')?.errors?.['required']">
                                                {{errorMessages.subject.required}}</div>
                                            <div *ngIf="replyForm.get('subject')?.errors?.['maxlength']">
                                                {{errorMessages.subject.maxlength}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group pb-1">
                                        <label class="fs-14 fw-semibold mb-1 lh-sm" for="replyMessage">Message</label>
                                        <textarea id="replyMessage" rows="5" pInputTextarea
                                            formControlName="message"></textarea>
                                        <div *ngIf="isFieldInvalid('message')" class="fs-14 lh-sm my-1 text-danger">
                                            <div *ngIf="replyForm.get('message')?.errors?.['required']">
                                                {{errorMessages.message.required}}</div>
                                            <div *ngIf="replyForm.get('message')?.errors?.['maxlength']">
                                                {{errorMessages.message.maxlength}}</div>
                                        </div>
                                    </div>
                                    <div class="gap-4 hstack justify-content-end">
                                        <a class="bg-white btn btn-light text-body" [routerLink]="['/admin/secure/help/feedback']">Cancel</a>
                                        <button type="submit" class="btn btn-primary">Reply</button>
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                    </ng-container>
                </form>
            </div>
        </div>
    </div>
</div>
<p-toast></p-toast>