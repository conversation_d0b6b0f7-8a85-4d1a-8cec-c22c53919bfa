import { Component } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { MessageService } from 'primeng/api';
import { Subscription } from 'rxjs';
import { AdminService } from 'src/app/services/admin.service';
import { CryptoService } from 'src/app/services/crypto.service';
import { helpStatus } from 'src/app/validators/constant';
import { SvgIcons } from 'src/app/validators/svg-icons';

@Component({
  selector: 'app-feedback',
  templateUrl: './feedback.component.html',
  styleUrls: ['./feedback.component.scss']
})
export class FeedbackComponent {
  svgIcons = SvgIcons;  
  helpStatus:any = helpStatus;
  private searchSubscription: Subscription | undefined;
  private warningMessageShown: boolean = false;
  private noRecordsFoundShown: boolean = false;
  currentPage: number = 1;
  totalRecords: number = 0;
  searchInput: string = '';
  feedbackList: any;
  getToken = localStorage.getItem('adminUser');
  clinicList: any;
  selectedClinicId: string = "";
  selectedDateRange: Date[] | null = null;
  selectedClinic:any = null;
  statusList:any = [
    {name:'All', value:0},
    {name:helpStatus[1], value:1},
    {name:helpStatus[3], value:3},
  ];
  selectedStatus: number = 0;

  constructor(
    private messageService: MessageService,
    private services: AdminService,
    private titleService: Title,
    private route: ActivatedRoute,
    private cryptoService: CryptoService
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);    
  }

  ngOnInit(): void {
    this.getFeedbackList();
    this.getClinicList();
  }

  getFeedbackList() {
    if (this.searchSubscription) {
      this.searchSubscription.unsubscribe();
    }
    let requestParam:any = {};    
    if (this.selectedDateRange && this.selectedDateRange.length === 2) {
      const startDate = this.selectedDateRange[0]
      const endDate = this.selectedDateRange[1]
      if(startDate && endDate){
      startDate.setMinutes(startDate.getMinutes() - startDate.getTimezoneOffset());
      endDate.setMinutes(endDate.getMinutes() - endDate.getTimezoneOffset());
      requestParam = {
        "startDate": startDate.toISOString().split('T')[0],
        "endDate": endDate.toISOString().split('T')[0]
      };
    }
    } else {
      requestParam = {};
    }
    

    if(this.selectedClinicId){
      requestParam.clinic_id = this.selectedClinicId
    }

    if(this.searchInput && this.searchInput.length >= 3){
      requestParam.searchText = this.searchInput
    }
    
    requestParam.status = this.selectedStatus;
        
    this.searchSubscription = this.services.getFeedbackList(this.currentPage, this.getToken, requestParam).subscribe((Response) => {
      const decryptedData = this.cryptoService.decrypt(Response.data);
      this.feedbackList = decryptedData;
      this.totalRecords = decryptedData.totalRecords;
    })
  }

  getClinicList() {
    this.services.getClinicList(this.getToken).subscribe((Response) => {
      const decryptedData = this.cryptoService.decrypt(Response.data);
      this.clinicList = decryptedData;
    })
  }

  onPageChange(event: any) {
    this.currentPage = event.page + 1;
    this.getFeedbackList();
  }

  onSearch() {
    if (this.searchSubscription) {
      this.searchSubscription.unsubscribe();
    }
    if (!this.searchInput) {    
      this.warningMessageShown = false;
      this.noRecordsFoundShown = false;     
    } else if (this.searchInput.length >= 3) {
      return;
    } 
    else if (!this.warningMessageShown) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Please enter at least 3 characters for the search'
      });
      this.warningMessageShown = true;
    }
  }

  filterSubmit(){    
    if ((this.selectedDateRange && this.selectedDateRange.length === 2) || this.selectedClinicId || this.searchInput || this.selectedStatus != undefined) {
      this.getFeedbackList();
    }
  }
  onClinicSelect(clinic: any) {
    if (clinic && clinic.clinic_id) {
      this.selectedClinicId = clinic.clinic_id;
    }
  }

  validateDateRange(){
    if (!(this.selectedDateRange && this.selectedDateRange.length === 2 && this.selectedDateRange[0] && this.selectedDateRange[1])) {
      this.selectedDateRange = null;
    }
    
  }

  resetFilter(){
    this.selectedClinicId = "";
    this.selectedDateRange = null;
    this.selectedClinic = null;
    this.searchInput = "";
    this.selectedStatus = 0;
    this.getFeedbackList();

  }

}
