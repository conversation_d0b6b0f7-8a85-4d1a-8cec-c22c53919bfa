<div class="d-flex flex-column pageContainer px-3 pb-3 h-100 overflow-y-scroll">
    <div class="contentHeader py-3 my-1">
        <div class="hstack gap-3 flex-wrap justify-content-between">
            <div class="theme-left-header me-auto">
                <h1 class="fw-normal h3 mb-0">Contact Us List</h1>
            </div>          
            <div class="flex-fill flex-wrap gap-3 hstack justify-content-end">
                <div class="col-12 col-lg-auto col-md theme-header-field">
                    <span class="p-input-icon-left w-100">
                        <i class="text-body text-opacity-25" [innerHTML]="svgIcons.searchIcon | safeHtml"></i>
                        <input pInputText aria-label="Search" placeholder="Search" (input)="onSearch()" [(ngModel)]="searchInput"/>
                    </span>
                </div>
                <div class="col-12 col-lg-auto col-md theme-header-field">                    
                        <p-calendar placeholder="Date Range" appendTo="body" styleClass="w-100"
                            [showIcon]="true" selectionMode="range" 
                            [readonlyInput]="true" [(ngModel)]="selectedDateRange" (onClose)="validateDateRange()"></p-calendar>                                              
                </div>
                <div class="col-12 col-lg-auto col-md theme-header-field">
                    <p-dropdown placeholder="Select Status" [options]="statusList" optionLabel="name" optionValue="value" dataKey="value" [(ngModel)]="selectedStatus"></p-dropdown>
                </div>
                <div class="col-12 col-lg-auto col-md d-inline-flex gap-2">                    
                    <button class="align-items-center btn btn-primary d-inline-flex h-37 justify-content-center p-1 w-37" appBsTooltip
                    data-bs-toggle="tooltip" data-bs-placement="top" title="Search" aria-label="Search" [innerHTML]="svgIcons.searchIcon | safeHtml" (click)="filterSubmit()" type="reset"></button>                                               
                    <button class="align-items-center btn btn-primary d-inline-flex h-37 justify-content-center p-1 w-37" appBsTooltip
                    data-bs-toggle="tooltip" data-bs-placement="top" title="Reset" aria-label="Reset" [innerHTML]="svgIcons.reloadIcon | safeHtml" (click)="resetFilter()" type="reset"></button>                                               
                </div>
            </div>
        </div>
    </div>
    <div class="flex-grow-1 pageContent position-relative">
        <div class="card border-0">
            <div class="card-body theme-data-table-cover w-100 h-100">
                <p-table styleClass="p-datatable-striped text-nowrap" [value]="contactList?.list" [paginator]="false">
                    <ng-template pTemplate="header">
                        <tr>
                            <th pSortableColumn="accountOwner" scope="User">User
                                <p-sortIcon field="username"></p-sortIcon>
                            </th>
                            <th pSortableColumn="noOfUsers" scope="Email">Email
                                <p-sortIcon field="useremail"></p-sortIcon>
                            </th>
                            <th pSortableColumn="noOfLetters" scope="Subject">Subject
                                <p-sortIcon field="subject"></p-sortIcon>
                            </th>
                            <th pSortableColumn="status" scope="status">Status
                                <p-sortIcon field="status"></p-sortIcon>
                            </th>
                            <th class="text-center" pSortableColumn="onboardingDate" scope="Created at">Created at
                                <p-sortIcon field="created_at"></p-sortIcon>
                            </th>                            
                            <th class="text-center" style="width: 100px;" scope="Actions">Actions</th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-element>
                        <tr>
                            <td>{{ element?.name }}</td>
                            <td>{{ element?.email }}</td>
                            <td class="text-truncate max-w-300">{{ element?.subject }}</td>
                            <td>{{ helpStatus[element?.status] }}</td>
                            <td class="text-center pe-4"><span class="pe-1">{{ element?.createdAt ? (element.createdAt | date:'dd-MM-yyyy') : '-' }}</span></td>
                            <td>
                                <div class="hstack gap-3 justify-content-center">
                                    <a [routerLink]="['view']" [queryParams]="{ contactId:element?.id }"
                                        data-bs-toggle="tooltip" data-bs-placement="top" title="View" aria-label="View"
                                        class="btn btn-link h-20 p-1 rounded-1 w-20 d-flex align-items-center justify-content-center" >
                                        <span [innerHTML]="svgIcons.eyeIcon | safeHtml"></span>
                                    </a>                                    
                                </div>
                            </td>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="emptymessage" >
                        <tr>
                            <td class="bg-body-tertiary fw-medium text-center" colspan="6">No data found.</td>
                        </tr>
                    </ng-template>
                </p-table>
                <p-paginator (onPageChange)="onPageChange($event)" [rows]="contactList?.recordsPerPage"
                            [totalRecords]="contactList?.totalRecords"></p-paginator>
            </div>
        </div>
    </div>
</div>
<p-toast></p-toast>