<div class="g-0 vh-100 position-relative row">
    <div
        class="align-items-center bg-body-tertiary col-md d-flex h-100 justify-content-center mobileBannerActive p-4 z-n1 pe-none user-select-none">
        <img src="assets/images/auth-banner.png" alt="Auth Banner" width="505" height="585" class="img-fluid w-auto" />
    </div>
    <div class="h-100 col-md mw-1">
        <div class="d-flex flex-column h-100 overflow-y-scroll pt-3 text-center w-100">
            <div class="auth-header pt-4">
                <a [routerLink]="['']" class="mb-4 d-inline-block">
                    <img src="assets/images/logo.png" title="Clinical Pad Logo" alt="Clinical Pad Logo" width="200"
                        height="30" class="img-fluid" />
                    <span class="visually-hidden">Clinical Pad Logo</span>
                </a>
                <h1 class="fw-normal h3 mt-3 mb-4 mb-md-5">Welcome to Clinical Pad</h1>
            </div>
            <div class="auth-body d-flex flex-column flex-grow-1 px-4 text-start auth-form-cover">
                <form class="px-xl-3 d-flex flex-column h-100" [formGroup]="myLoginForm"
                    (ngSubmit)="postAccountLogin()">
                    <div class="row justify-content-center">
                        <div class="col-sm-9 col-lg-7 col-xxl-5">
                            <div class="form-group mb-4">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="email">Email<span class="text-danger">*</span></label>
                                <input pInputText id="email" formControlName="email" />
                                <div *ngIf="isFieldInvalid('email')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="myLoginForm.get('email')?.errors?.['required']">
                                        {{errorMessages.username.required}}</div>
                                    <div *ngIf="myLoginForm.get('email')?.errors?.['email']">
                                        {{errorMessages.username.email}}</div>
                                    <div *ngIf="myLoginForm.get('email')?.errors?.['pattern']">
                                        {{errorMessages.username.pattern}}</div>
                                </div>
                            </div>

                            <div class="form-group mb-4">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="password">Password<span class="text-danger">*</span></label>
                                <p-password id="password" [feedback]="false" [toggleMask]="true"
                                    formControlName="password" (keydown)="onKeyDown($event)"></p-password>
                                    <div *ngIf="isFieldInvalid('password')" class="fs-14 lh-sm my-1 text-danger">
                                        <div *ngIf="myLoginForm.get('password')?.errors?.['required']">
                                            {{errorMessages.password.required}}</div>
                                    </div>
                            </div>

                            <div class="gap-3 hstack justify-content-between mb-4">
                                <div class="form-group">
                                    <div class="align-items-start d-flex theme-checkbox fs-14">
                                        <p-checkbox value="remember-check" inputId="remember-check"
                                            formControlName="rememberMe" styleClass="align-items-center fs-11"
                                            [binary]="true"></p-checkbox>
                                        <label class="ps-2 fw-medium" for="remember-check">Remember Me</label>
                                    </div>
                                </div>
                                <a [routerLink]="['/admin/admin-forgot-password']"
                                    class="fw-medium fs-14 text-decoration-none">Forgot
                                    Password?</a>
                            </div>
                            <div class="form-group mb-4 pb-1">
                                <ngx-recaptcha2 #captchaElem [siteKey]="siteKey" formControlName="recaptcha">
                                </ngx-recaptcha2>
                                <div *ngIf="isFieldInvalid('recaptcha')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.captcha.required}}</div>
                            </div>
                            <div class="form-group mb-4">
                                <p-toast></p-toast>
                                <button type="submit" class="btn btn-primary w-100">Login</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>