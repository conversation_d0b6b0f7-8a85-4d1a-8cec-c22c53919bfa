@import "../../../assets/css/variables";

header {
    .navMenuBtn {
        .menuTrigger {
            width: 15px;
            height: 2px;

            &::after,
            &::before {
                content: "";
                position: absolute;
                width: 15px;
                height: 2px;
                background-color: $primary;
                right: 50%;
                transform: translateX(50%);
                transition: all .3s ease-in-out;
            }

            &::after {
                bottom: -6px;
            }

            &::before {
                top: -6px;
            }

            &.active {
                @include below-xl() {
                    visibility: hidden;
                }

                &::after {
                    width: 10px;
                    transform: rotate(-45deg);

                    @include above-xl() {
                        right: -2px;
                        bottom: -3px;

                    }

                    @include below-xl() {
                        visibility: visible;
                        width: 15px;
                        bottom: 0;
                        right: 0;
                    }
                }

                &::before {
                    width: 10px;
                    transform: rotate(45deg);

                    @include above-xl() {
                        right: -2px;
                        top: -3px;
                    }

                    @include below-xl() {
                        visibility: visible;
                        width: 15px;
                        top: 0;
                        right: 0;

                    }
                }

            }
        }

    }

    .theme-feedback-btn {
        @include below-sm() {
            font-size: $theme-size-12;
            padding: .25rem;
            width: 90px;
        }
    }

    .w-30 {
        width: 30px;
    }

    .h-30 {
        height: 30px;
    }    

    .mh-70 {
        min-height: 70px;

        @include below-xl() {
            min-height: $theme-mobile-header-height;
        }
    }

    .mw-150 {
        max-width: 150px;
    }

}

.sidebarMenu {
    width: $theme-sidebar-width;
    transition: width .3s ease-in-out;
    position: relative;
    height: 100%;

    @include below-xl() {
        top: calc($theme-mobile-header-height + 2px);
        position: fixed;
        z-index: 9;
        left: -$theme-sidebar-width;
        transition: left .3s ease-in-out;
        height: calc(100% - $theme-mobile-header-height) !important;
    }
    .min-h-44{
        min-height: 44px;
    }
    .sidebarList {
        >ul {
            li {
                margin-bottom: .25rem;

                a {
                    min-height: 2.75rem;

                    &.active,
                    &:hover {
                        background-color: rgba($primary, .10);
                    }

                    svg {
                        display: block;
                        margin: auto;
                    }

                    .min-w-36 {
                        min-width: 36px;
                    }
                }

                >ul {
                    li {
                        a {
                            padding-left: 36px !important;

                            .min-w-44 {
                                min-width: 0;
                                margin-right: .75rem;
                            }

                            &.active,
                            &:hover {
                                background-color: rgba($white, .3);
                            }
                        }
                    }
                }
            }
        }
    }

    &.sidebarAction {
        @include below-xl() {
            left: 0;
        }

    }
}

.theme-sidebar-overlay {
    top: calc($theme-mobile-header-height + 2px);

    @include above-xl() {
        display: none !important;
    }
}