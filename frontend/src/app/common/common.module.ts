import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BsTooltipDirective } from '../directives/bs-tooltip.directive';
import { SafeHtmlPipe } from '../pipes/safe-html.pipe';


@NgModule({
  declarations: [
    BsTooltipDirective,
    SafeHtmlPipe
  ],
  imports: [
    CommonModule,   
  ],
  exports: [
    BsTooltipDirective,
    SafeHtmlPipe
  ]
})
export class CustomCommonModule { }
