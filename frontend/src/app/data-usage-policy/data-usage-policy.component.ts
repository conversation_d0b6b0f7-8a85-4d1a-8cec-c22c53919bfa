import { Component } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { UserServiceService } from '../services/user-service.service';

@Component({
  selector: 'app-data-usage-policy',
  templateUrl: './data-usage-policy.component.html',
  styleUrls: ['./data-usage-policy.component.scss']
})
export class DataUsagePolicyComponent {

  dataUsageContent: string = '';
  constructor(
    private titleService: Title,
    private route: ActivatedRoute,
    private services: UserServiceService
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
  }
  ngOnInit(): void {
    this.getCmsContent();
  }
  getCmsContent() {
    let id = 3;
    this.services.getCMSContent(id).subscribe((Response:any) => {
      this.dataUsageContent = Response.data.content;
    })
  }
}
