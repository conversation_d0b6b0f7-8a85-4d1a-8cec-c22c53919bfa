<header>
    <nav id="theme-home-navbar" role="menu" class="border-2 border-bottom navbar navbar-expand-lg px-md-2 py-0">
        <div class="mh-70 container-fluid theme-container">
            <a class="navbar-brand py-0 me-0" [routerLink]="['']">
                <img src="assets/images/logo.png" title="Clinical Pad Logo" alt="Clinical Pad Logo" width="146"
                    height="22" class="d-block img-fluid" />
                <span class="visually-hidden">Clinical Pad Logo</span>
            </a>
            <div class="offcanvas offcanvas-start" tabindex="-1" id="themeHomeHeaderNavbar"
                aria-labelledby="themeHomeHeaderNavbarLabel">
                <div class="offcanvas-body">
                    <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
                        <li class="nav-item px-xl-2 px-lg-1" [ngClass]="{ 'active': isHomePage() }">
                            <a (click)="homeLink('home-content')" data-bs-dismiss="offcanvas"
                                data-bs-target="#themeHomeHeaderNavbar" class="d-flex align-items-center nav-link"
                                aria-current="page" href="#home-content">Home</a>
                        </li>
                        <li class="nav-item px-xl-2 px-lg-1" [ngClass]="{ 'active': isHomePage() }">
                            <a [routerLink]="['/pricing']" data-bs-dismiss="offcanvas"
                                data-bs-target="#themeHomeHeaderNavbar" class="d-flex align-items-center nav-link"
                                aria-current="page">Pricing</a>
                        </li>
                        <li class="nav-item px-xl-2 px-lg-1">
                            <a (click)="homeLink('features-content')" data-bs-dismiss="offcanvas"
                                data-bs-target="#themeHomeHeaderNavbar" class="d-flex align-items-center nav-link"
                                href="#features-content">Features</a>
                        </li>
                        <li class="nav-item px-xl-2 px-lg-1">
                            <a (click)="homeLink('security-content')" data-bs-dismiss="offcanvas"
                                data-bs-target="#themeHomeHeaderNavbar" class="d-flex align-items-center nav-link"
                                href="#security-content">Security</a>
                        </li>
                        <li class="nav-item px-xl-2 px-lg-1">
                            <a (click)="homeLink('benefits-content')" data-bs-dismiss="offcanvas"
                                data-bs-target="#themeHomeHeaderNavbar" class="d-flex align-items-center nav-link"
                                href="#benefits-content">Benefits</a>
                        </li>
                        <li class="nav-item px-xl-2 px-lg-1">
                            <a (click)="homeLink('about-content')" data-bs-dismiss="offcanvas"
                                data-bs-target="#themeHomeHeaderNavbar" class="d-flex align-items-center nav-link"
                                href="#about-content">About Us</a>
                        </li>
                        <li class="nav-item px-xl-2 px-lg-1">
                            <a (click)="showContactDialog()" data-bs-dismiss="offcanvas"
                                data-bs-target="#themeHomeHeaderNavbar" class="d-flex align-items-center nav-link"
                                href="#">Contact Us</a>
                        </li>
                    </ul>
                </div>
            </div>
            <button data-bs-toggle="offcanvas" data-bs-target="#themeHomeHeaderNavbar"
                aria-controls="themeHomeHeaderNavbar" aria-label="Toggle navigation"
                class="align-items-center btn btn-link d-flex d-lg-none justify-content-center me-2 navMenuBtn pe-1 ps-0 py-3">
                <span class="bg-primary d-inline-block menuTrigger position-relative text-center"></span>
            </button>
            <div class="gap-2 gap-sm-3 hstack ms-auto">
                <a [routerLink]="['/login']" type="button" class="bg-white btn btn-light text-body px-3 px-sm-4">
                    <span class="d-sm-none" [innerHTML]="svgIcons.signInIcon | safeHtml"></span>
                    <span class="d-sm-block d-none">Login</span>
                </a>
                <a [routerLink]="['/sign-up/clinic']" type="button" class="btn btn-primary px-3 px-sm-4">
                    <span class="d-sm-none" [innerHTML]="svgIcons.signUpIcon | safeHtml"></span>
                    <span class="d-sm-block d-none">Sign Up</span>
                </a>
            </div>
        </div>
    </nav>
</header>