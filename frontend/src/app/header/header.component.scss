@import "../../assets/css/variables";

header,
.offcanvas-header {
    .navbar {
        .mh-70 {
            min-height: 70px;

            @include below-xl() {
                min-height: $theme-mobile-header-height;
            }
        }

        .navMenuBtn {
            order: -1;
            .menuTrigger {
                width: 15px;
                height: 2px;

                &::after,
                &::before {
                    content: "";
                    position: absolute;
                    width: 15px;
                    height: 2px;
                    background-color: $primary;
                    right: 50%;
                    transform: translateX(50%);
                    transition: all .3s ease-in-out;
                }

                &::after {
                    bottom: -6px;
                }

                &::before {
                    top: -6px;
                }
            }

        }

        ul {
            li {
                a {
                    font-weight: 500;

                    @include above-lg() {
                        min-height: $theme-mobile-header-height;
                        border-bottom: 4px solid transparent;
                        border-top: 4px solid transparent;
                    }

                    @include above-xl() {
                        min-height: 70px;
                    }

                    @include below-lg() {                        
                        padding-left: 1rem;
                        padding-right: 1rem;
                        border-radius: $border-radius;
                        min-height: 2.75rem;
                        font-size: $theme-size-14;
                    }

                    &.active {
                        font-weight: 600;

                        @include above-lg() {
                            border-bottom-color: $primary;
                        }

                        @include below-lg() {
                            background-color: rgba($primary, .10);
                        }

                    }
                }
            }
        }

        .offcanvas {
            &.offcanvas-start {
                top: calc($theme-mobile-header-height + 2px);
            }
            &.show {
                ~ {
                    .navMenuBtn {
                        .menuTrigger {
                            width: 15px;
                            height: 2px;
            
                            &::after,
                            &::before {
                                content: "";
                                position: absolute;
                                width: 15px;
                                height: 2px;
                                background-color: $primary;
                                right: 50%;
                                transform: translateX(50%);
                                transition: all .3s ease-in-out;
                            }
            
                            &::after {
                                bottom: -6px;
                            }
            
                            &::before {
                                top: -6px;
                            }
            
                            &.menuTrigger {
                                @include below-xl() {
                                    visibility: hidden;
                                }
            
                                &::after,
                                &::before {
                                    width: 10px;
            
                                    @include below-xl() {
                                        visibility: visible;
                                        width: 15px;
                                    }
                                }
            
                                &::after {
                                    transform: rotate(-45deg);
            
                                    @include below-xl() {
                                        bottom: 0;
                                        right: 0;
                                    }
                                }
            
                                &::before {
                                    transform: rotate(45deg);
            
                                    @include below-xl() {
                                        top: 0;
                                        right: 0;
            
                                    }
                                }
            
                            }
                        }
            
                    }
                }
            }
        }
    }

    ::ng-deep{
        .offcanvas-backdrop {
            top: calc($theme-mobile-header-height + 2px);
        }
    }
}