import { AfterViewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { SvgIcons } from '../validators/svg-icons';
import { ErrorMessages } from '../validators/error-messages';
import { environment } from 'src/environments/environment';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
import jwt_decode from 'jwt-decode';
import { UserServiceService } from '../services/user-service.service';
import { MessageService } from 'primeng/api';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { addressValidation, passwordValidator, phoneValidator, regNumberValidator } from '../validators/constant';
import { CryptoService } from '../services/crypto.service';
declare var google: any;


@Component({
  selector: 'app-invite-user-registration',
  templateUrl: './invite-user-registration.component.html',
  styleUrls: ['./invite-user-registration.component.scss']
})
export class InviteUserRegistrationComponent implements OnInit, AfterViewInit {
  
  @ViewChild('addressInput', { static: false })
  public addressInput!: ElementRef;
  svgIcons = SvgIcons;
  public gmcNumberInfo = ErrorMessages.toolTips.gmcNumberInfo;
  public passwordInfoMessage = ErrorMessages.toolTips.passwordInfoMessage;
  siteKey = environment.siteKey;
  decodedToken: any;
  specialisationList: { id: number, name: string }[] = [];
  newInviteUserForm: FormGroup;
  errorMessages = ErrorMessages.registrationForm;
  invitedUserData: any;
  existedInvitedUserData: any;
  showErrorMessage: boolean = true;
  visible: boolean = false;
  visible2: boolean = false;
  showOtherTextbox: boolean = false;
  private readonly googleMapsApiKey: string = environment.googleMapsApiKey;

  constructor(
    private titleService: Title,
    private route: ActivatedRoute,
    private services: UserServiceService,
    private messageService: MessageService,
    private formBuilder: FormBuilder,
    private cryptoService: CryptoService
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
    this.newInviteUserForm = this.formBuilder.group({
      firstName: [{ value: '', disabled: true }, [Validators.required]],
      lastName: [{ value: '', disabled: true }, [Validators.required]],
      email: [{ value: '', disabled: true }, [Validators.required, Validators.email]],
      phone: ['', [Validators.required, phoneValidator]],
      specialisation: ['', Validators.required],
      others: [''],
      gmcNumber: [{ value: '' }, [regNumberValidator]],
      password: ['', [Validators.required, passwordValidator]],
      confirmPassword: ['', Validators.required],
      searchaddress: [''],
      address: ['', [Validators.required, addressValidation]],
      city: ['', Validators.required],
      post_code: ['', [Validators.required]],
      country: ['', [Validators.required]],
      county: ['', Validators.required],
      recaptcha: ['', Validators.required]
    },
      {
        validators: this.passwordMatchValidator
      }
    );
  }

  ngOnInit(): void {
    this.showErrorMessage = true;
    this.route.queryParams.subscribe(params => {
      let token = params['t'];
      if (token) {
        this.decodedToken = jwt_decode(token);
        const currentTime = Date.now() / 1000;

        if (this.decodedToken && this.decodedToken.exp) {
          if (this.decodedToken.exp < currentTime) {
            this.existedInvitedUserData = null;
            this.showErrorMessage = true;
          } else {
            this.invitedUserData = this.decodedToken.data;
            this.existedInvitedUserData = this.decodedToken.data.userData;
            this.showErrorMessage = false;
          }
        }
       
      }
    });
    this.getSpecList();
    this.getInvitedUserData();
   
  }
  ngAfterViewInit(): void {
    this.initAutocomplete();
  }
  private initAutocomplete(): void {
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = `https://maps.googleapis.com/maps/api/js?key=${this.googleMapsApiKey}&libraries=places`;
    script.onload = () => {
      const autocomplete = new google.maps.places.Autocomplete(
        this.addressInput.nativeElement,
        {
          types: ['geocode']
        }
      );
      autocomplete.addListener('place_changed', () => {
        if (this.newInviteUserForm) {
          const place = autocomplete.getPlace();

          if (place.geometry) {
            const addressComponents = place.address_components;
            let selectedAddress = '';
            addressComponents.forEach((component: any) => {
              const componentType = component.types[0];
              switch (componentType) {
                case 'street_number':
                  this.newInviteUserForm!.get('address')!.setValue(component.long_name);
                  break;
                case 'route':
                  const existingRoute = this.newInviteUserForm!.get('address')!.value || '';
                  this.newInviteUserForm!.get('address')!.setValue(existingRoute + ' ' + component.long_name);
                  break;
                case 'postal_code':
                case 'country':
                case 'administrative_area_level_1':
                case 'locality':
                  break;
                default:
                  selectedAddress += component.long_name + ' ';
                  break;
              }
            });
            selectedAddress = selectedAddress.trim();
            this.newInviteUserForm!.get('address')!.setValue(selectedAddress);
            this.newInviteUserForm!.get('city')!.setValue(place.address_components.find((c: any) => c.types.includes('locality'))?.long_name || '');
            this.newInviteUserForm!.get('county')!.setValue(place.address_components.find((c: any) => c.types.includes('administrative_area_level_1'))?.long_name || '');
            this.newInviteUserForm!.get('country')!.setValue(place.address_components.find((c: any) => c.types.includes('country'))?.long_name || '');
            this.newInviteUserForm!.get('post_code')!.setValue(place.address_components.find((c: any) => c.types.includes('postal_code'))?.long_name || '');
          }
        }
      });
    };
    script.onerror = (error) => {
      console.error('Error loading Google Maps script:', error);
    };
    document.head.appendChild(script);
  }

  getSpecList() {
    this.services.getSpecList().subscribe((response) => {
      if (response.statusCode == 200) {
        const sortedSpecialisationList = [];
        for (const item of response.data) {
          if (item.name !== 'Other') {
            sortedSpecialisationList.push({ id: item.id, name: item.name });
          }
        }
        sortedSpecialisationList.push({ id: '83', name: 'Other' });

        this.specialisationList = sortedSpecialisationList;
      } else {
        this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Error Loading Specialisations' });
      }
    });
  }
  handleSpecialisationChange() {
    const selectedSpecialisation: any = this.specialisationList.find(spec => spec.name === this.newInviteUserForm.value.specialisation.name);
    this.showOtherTextbox = selectedSpecialisation.name === "Other";
    const othersField = this.newInviteUserForm.get('others');
    if (this.showOtherTextbox && othersField) {
      othersField.setValidators([Validators.required, Validators.pattern(/^[A-Za-z\s]{5,50}$/)]);
    } else if (othersField) {
      othersField.clearValidators();
    }
    if (othersField) {
      othersField.updateValueAndValidity();
    }
  }

  isFieldInvalid(fieldName: string): any {
    const field = this.newInviteUserForm.get(fieldName);
    return field?.invalid && (field?.dirty || field?.touched);
  }

  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  passwordMatchValidator(control: FormGroup) {
    const password = control.get('password');
    const confirmPassword = control.get('confirmPassword');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
    }
  }
  onSubmit() {
    if (this.newInviteUserForm.valid) {
      let data: { [key: string]: any } = {
        first_name: this.invitedUserData?.first_name,
        last_name: this.invitedUserData?.last_name,
        invite_id: this.invitedUserData?.id,
        email: this.invitedUserData?.email,
        phone: this.newInviteUserForm.value.phone,
        password: this.newInviteUserForm.value.password,
        town_id: this.newInviteUserForm.value.city,
        county_id: this.newInviteUserForm.value.county,
        country: this.newInviteUserForm.value.country,
        address: this.newInviteUserForm.value.address,
        post_code: this.newInviteUserForm.value.post_code,
        clinical_specializations_id: this.newInviteUserForm.value.specialisation,
        permissions: this.invitedUserData?.permissions,
        clinic_id: this.invitedUserData?.client_id,
        role_id: this.invitedUserData?.role_id,
        user_type: this.invitedUserData?.user_type
      }
      if (this.newInviteUserForm.value.others) {
        data['others'] = this.newInviteUserForm.value.others;
      }
      if (this.newInviteUserForm.value.gmcNumber) {
        data['registration_number'] = this.newInviteUserForm.value.gmcNumber;
      }
      const encryptedData = this.cryptoService.encrypt(data);
      let request = {
        encrypet: encryptedData
      }
      this.services.postInviteUserReg(request).subscribe((Response) => {
        if (Response.statusCode == 200) {
          this.newInviteUserForm.reset();
          this.visible = true;
          localStorage.removeItem('selectedCategory');
        } else if (Response.statusCode == 400) {
          this.messageService.add({ severity: 'error', summary: 'Error', detail: 'User Already Exists' });
        }
      },
        (error) => {
          console.log('Error occurred:', error);
          let errorMsg = error?.error?.msg || 'An error occurred';
          this.messageService.add({ severity: 'error', summary: 'Error', detail: errorMsg });
        })
    } else {
      this.markFormGroupTouched(this.newInviteUserForm);
    }
  }
  getInvitedUserData() {
    this.newInviteUserForm.patchValue({
      firstName: this.invitedUserData?.first_name,
      lastName: this.invitedUserData?.last_name,
      email: this.invitedUserData?.email,
      gmcNumber: this.invitedUserData?.registration_number
    })
    if (this.existedInvitedUserData !== null) {
      this.visible2 = true;
      this.newInviteUserForm.patchValue({
        firstName: this.existedInvitedUserData?.first_name,
        lastName: this.existedInvitedUserData?.last_name,
        email: this.existedInvitedUserData?.email,
        gmcNumber: this.existedInvitedUserData?.reg_gmc_no,
        phone: this.existedInvitedUserData?.phone,
        post_code: this.existedInvitedUserData?.pincode,
        address: this.existedInvitedUserData?.address,
        county: this.existedInvitedUserData?.county_id,
        country: this.existedInvitedUserData?.country,
        city: this.existedInvitedUserData?.town_id
      })
      this.newInviteUserForm.controls['specialisation'].setValue(this.existedInvitedUserData.clinical_specializations_id);

    }
  }

}
