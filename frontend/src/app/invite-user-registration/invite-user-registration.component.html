<div class="g-0 vh-100 position-relative row">
    <div
        class="align-items-center bg-body-tertiary col-md d-flex h-100 justify-content-center mobileBannerActive p-4 z-n1 pe-none user-select-none">
        <img src="assets/images/auth-banner.png" alt="Auth Banner" width="505" height="585" class="img-fluid w-auto" />
    </div>
    <div class="h-100 col-md mw-1">
        <div class="d-flex flex-column h-100 overflow-y-scroll pt-3 text-center w-100">
            <div class="auth-header pt-4">
                <a [routerLink]="['']" class="mb-4 d-inline-block">
                    <img src="assets/images/logo.png" title="Clinical Pad Logo" alt="Clinical Pad Logo" width="200"
                        height="30" class="img-fluid" />
                    <span class="visually-hidden">Clinical Pad Logo</span>
                </a>
                <h1 *ngIf="!showErrorMessage" class="fw-normal h3 mt-3 pb-1">Invited User Sign Up</h1>
                <p *ngIf="!showErrorMessage" class="fs-18 fw-medium mb-4 pb-2">Welcome to Clinical Pad</p>
            </div>
            <div *ngIf="!showErrorMessage"
                class="auth-body d-flex flex-column flex-grow-1 px-4 text-start auth-form-cover">
                <form class="px-xl-3 d-flex flex-column h-100 pt-4" [formGroup]="newInviteUserForm"
                    (ngSubmit)="onSubmit()">
                    <div class="row gx-3 gx-xl-4">
                        <div class="col-sm-6 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="first-name">First Name<span class="text-danger">*</span></label>
                                <input pInputText id="first-name" formControlName="firstName" />
                                <div *ngIf="isFieldInvalid('firstName')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.firstName.required}}</div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="user-name">Last Name<span class="text-danger">*</span></label>
                                <input pInputText id="user-name" formControlName="lastName" />
                                <div *ngIf="isFieldInvalid('lastName')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.lastName.required}}</div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="user-email">Email<span class="text-danger">*</span></label>
                                <input pInputText id="user-email" formControlName="email" />
                                <div *ngIf="isFieldInvalid('email')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newInviteUserForm.get('email')?.errors?.['required']">
                                        {{errorMessages.email.required}}</div>
                                    <div *ngIf="newInviteUserForm.get('email')?.errors?.['email']">
                                        {{errorMessages.email.email}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-4">
                            <div class="form-group">
                                <label class="align-items-center d-inline-flex fs-14 fw-semibold lh-sm mb-1"
                                    for="registration-gmc-number">Registration Number</label>
                                <a appBsTooltip data-bs-html="true" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title={{gmcNumberInfo}}
                                    class="btn btn-link text-body d-inline-block infoIcon ms-1 p-0">
                                    <span class="theme-info-icon" [innerHTML]="svgIcons.infoIcon | safeHtml"></span>
                                    <span class="visually-hidden">{{gmcNumberInfo}}</span>
                                </a>
                                <input placeholder="GMC, NMC, PH etc." pInputText id="registration-gmc-number"
                                    formControlName="gmcNumber" />
                                <div *ngIf="isFieldInvalid('gmcNumber')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newInviteUserForm.get('gmcNumber')?.errors?.['pattern']">
                                        {{errorMessages.gmcNumber.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="specialization">Specialisation<span class="text-danger">*</span></label>
                                <p-dropdown placeholder="Select a specialisation" [options]="specialisationList"
                                    optionLabel="name" formControlName="specialisation"
                                    placeholder="Select Specialisation" optionValue="id"
                                    (onChange)="handleSpecialisationChange()"></p-dropdown>
                                <div *ngIf="isFieldInvalid('specialisation')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.specialization.required}}</div>
                            </div>
                            <div class="form-group mt-3" *ngIf="showOtherTextbox">
                                <input pInputText placeholder="Enter Specialisation" formControlName="others" />
                                <div *ngIf="isFieldInvalid('others')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newInviteUserForm.get('others')?.errors?.['required']">
                                        {{errorMessages.others.required}}</div>
                                    <div *ngIf="newInviteUserForm.get('others')?.errors?.['pattern']">
                                        {{errorMessages.others.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="user-address">Address<span class="text-danger">*</span></label>
                                <input pInputText id="user-address" formControlName="address" placeholder="Search by building name/number, street name or postcode" #addressInput />
                                <div *ngIf="isFieldInvalid('address')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newInviteUserForm.get('address')?.errors?.['required']">
                                        {{errorMessages.address.required}}</div>
                                    <div *ngIf="newInviteUserForm.get('address')?.errors?.['pattern']">
                                        {{errorMessages.address.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="country">Country<span class="text-danger">*</span></label>
                                <input pInputText id="country" formControlName="country" />
                                <div *ngIf="isFieldInvalid('country')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.country.required}}</div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="county">State</label>
                                <input pInputText id="county" formControlName="county" />
                                <div *ngIf="isFieldInvalid('county')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.state.required}}</div>
                            </div>
                        </div>

                        <div class="col-sm-6 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="town">Town/City<span class="text-danger">*</span></label>
                                <input pInputText id="town" formControlName="city" />
                                <div *ngIf="isFieldInvalid('city')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.city.required}}</div>
                            </div>
                        </div>

                        <div class="col-sm-6 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="post_code">Post Code<span class="text-danger">*</span></label>
                                <input pInputText id="post_code" formControlName="post_code" />
                                <div *ngIf="isFieldInvalid('post_code')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newInviteUserForm.get('post_code')?.errors?.['required']">
                                        {{errorMessages.zip.required}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-4">
                            <div class="form-group">
                                <label class="fs-14 fw-semibold mb-1 lh-sm" for="user-phone">Phone
                                    Number<span class="text-danger">*</span></label>
                                <input pInputText id="user-phone" formControlName="phone" />
                                <div *ngIf="isFieldInvalid('phone')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newInviteUserForm.get('phone')?.errors?.['required']">
                                        {{errorMessages.phoneNumber.required}}</div>
                                    <div *ngIf="newInviteUserForm.get('phone')?.errors?.['pattern']">
                                        {{errorMessages.phoneNumber.pattern}}</div>
                                </div>
                            </div>
                        </div>


                        <div class="col-12">
                            <div class="fw-bold my-3">Create Password</div>
                        </div>
                        <div class="col-sm-6 mb-4">
                            <div class="form-group">
                                <label class="align-items-center d-inline-flex fs-14 fw-semibold lh-sm mb-1"
                                    for="password">Password<span class="text-danger">*</span>
                                    <a appBsTooltip data-bs-html="true" data-bs-toggle="tooltip" data-bs-placement="top"
                                        title={{passwordInfoMessage}}
                                        class="btn btn-link text-body d-inline-block infoIcon ms-1 p-0">
                                        <span class="theme-info-icon" [innerHTML]="svgIcons.infoIcon | safeHtml"></span>
                                        <span class="visually-hidden">{{passwordInfoMessage}}</span>
                                    </a>
                                </label>
                                <p-password id="password" [feedback]="false" [toggleMask]="true"
                                    formControlName="password"></p-password>
                                <div *ngIf="isFieldInvalid('password')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newInviteUserForm.get('password')?.errors?.['required']">
                                        {{errorMessages.password.required}}</div>
                                    <div *ngIf="newInviteUserForm.get('password')?.errors?.['pattern']">
                                        {{errorMessages.password.pattern}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-4">
                            <div class="form-group">
                                <label class="align-items-center d-inline-flex fs-14 fw-semibold lh-sm mb-1"
                                    for="confirm-password">Confirm Password<span class="text-danger">*</span>
                                    <a appBsTooltip data-bs-html="true" data-bs-toggle="tooltip" data-bs-placement="top"
                                        title={{passwordInfoMessage}}
                                        class="btn btn-link text-body d-inline-block infoIcon ms-1 p-0">
                                        <span class="theme-info-icon" [innerHTML]="svgIcons.infoIcon | safeHtml"></span>
                                        <span class="visually-hidden">{{passwordInfoMessage}}</span>
                                    </a>
                                </label>
                                <p-password id="confirm-password" [feedback]="false" [toggleMask]="true"
                                    formControlName="confirmPassword"></p-password>
                                <div *ngIf="isFieldInvalid('confirmPassword')" class="fs-14 lh-sm my-1 text-danger">
                                    <div *ngIf="newInviteUserForm.get('confirmPassword')?.errors?.['required']">
                                        {{errorMessages.confirmPassword.required}}</div>
                                    <div *ngIf="newInviteUserForm.get('confirmPassword')?.errors?.['passwordMismatch']">
                                        {{errorMessages.confirmPassword.equalTo}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 mb-4 pt-3">
                            <div class="form-group">
                                <ngx-recaptcha2 #captchaElem [siteKey]="siteKey"
                                    formControlName="recaptcha"></ngx-recaptcha2>
                                <div *ngIf="isFieldInvalid('recaptcha')" class="fs-14 lh-sm my-1 text-danger">
                                    {{errorMessages.captcha.required}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="text-sm-end mt-auto pb-4 pt-2 mb-1">
                        <p-toast></p-toast>
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </div>
                </form>
            </div>
            <div *ngIf="showErrorMessage" class="mb-3 text-danger">
                <svg fill="currentColor" xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60">
                    <path
                        d="M27.18,14,22,19.18,16.82,14,14,16.82,19.18,22,14,27.18,16.82,30,22,24.82,27.18,30,30,27.18,24.82,22,30,16.82ZM22,2A20,20,0,1,0,42,22,19.982,19.982,0,0,0,22,2Zm0,36A16,16,0,1,1,38,22,16.021,16.021,0,0,1,22,38Z"
                        transform="translate(-2 -2)" />
                </svg>
                <p class="mt-3"><span class="fw-bold">Access Denied!</span><br>You do not have access to this page or
                    Link has been expired</p>
            </div>
            <div class="border-top mt-auto px-3 py-2 py-xl-3 text-center">Already have an account? <a
                    [routerLink]="['/login']" class="fw-bold text-decoration-none">Login</a></div>
        </div>
    </div>
</div>

<!-- Success Popup -->
<p-dialog styleClass="theme-modal-main theme-modal-lg theme-modal-center" header="Header" [modal]="true"
    [draggable]="false" [resizable]="false" [dismissableMask]="true" [(visible)]="visible" [closable]="false">
    <ng-template pTemplate="header">
        <h3 class="mb-0 fw-normal">Successful!</h3>
    </ng-template>
    <div class="theme-modal-body">
        <p class="fw-medium mb-4 pt-3">Your account has been successfully registered. Please login with your
            credintials.</p>
    </div>
    <ng-template pTemplate="footer">
        <div class="gap-4 hstack justify-content-center">
            <button (click)="visible = false" class="btn btn-primary">Ok</button>
        </div>
    </ng-template>
</p-dialog>
<!--Already User Popup-->
<p-dialog styleClass="theme-modal-main theme-modal-sm theme-modal-center" header="Header" [modal]="true"
    [draggable]="false" [resizable]="false" [dismissableMask]="true" [(visible)]="visible2" [closable]="false">
    <ng-template pTemplate="header">
        <h3 class="mb-0 fw-normal">Already registered</h3>
    </ng-template>
    <div class="theme-modal-body">
        <p class="fw-medium mb-4 text-center">You have already registered with us. Your details will be updated once you
            updated here
        <p>
        <div class="gap-4 hstack justify-content-center">
            <button (click)="visible2 = false" class="btn btn-light bg-white text-body">Okay</button>
        </div>
    </div>
</p-dialog>