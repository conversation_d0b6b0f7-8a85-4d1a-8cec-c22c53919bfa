import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { CookieService } from 'ngx-cookie-service';
import { environment } from '../../environments/environment';
import { UserServiceService } from './user-service.service';

@Injectable({
  providedIn: 'root'
})
export class CommonService {
  public frontPath = environment.frontPath;
  constructor(
    private cookieService:CookieService,
    private userService: UserServiceService,
    private router: Router
  ) { }
  // Set isLoggedIn cookie for 2 hours
  setUserCookie() {
    let userProfile  : any = localStorage.getItem('userProfile');
    let userProfileData = JSON.parse(userProfile);
  
    if (userProfileData) {
      const userDetail = {
        firstName: userProfileData.firstName,
        lastName: userProfileData.lastName,
        email: userProfileData.email,
        phone: userProfileData.phone,
        specialisation: userProfileData.specialisation,
        gmcNumber: userProfileData.gmcNumber
      };
  
      const userData = window.btoa(JSON.stringify(userDetail));
      this.cookieService.set('isLoggedIn', userData, {
        expires: new Date(new Date().getTime() + 2 * 60 * 2 * 1000),
        domain: this.frontPath,
        path: '/'
      });
    }
  }
  
  // remove isLoggedIn cookie for 2 hours
  removeUserCookie() {
    if (this.frontPath) {
      this.cookieService.delete('isLoggedIn', '/', this.frontPath);
    }
  }

  clearStorage() {
    localStorage.removeItem('currentUser');
    sessionStorage.clear();
    this.removeUserCookie();
    this.router.navigate(['/login']);
  }

  clearStorageOnLogout(){
    this.userService.getLogout().subscribe((resp: any) => {
      if(resp.response.code == 200 || resp.response.code == 602) {
        this.clearStorage();
      }
    })
              
  }
}
