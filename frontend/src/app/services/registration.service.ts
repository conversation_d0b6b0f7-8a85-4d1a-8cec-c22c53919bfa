import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class RegistrationService {
  private completedStepSubject: BehaviorSubject<number> = new BehaviorSubject<number>(-1);
  public completedStep$ = this.completedStepSubject.asObservable();

  public setCompletedStep(stepIndex: number): void {
    this.completedStepSubject.next(stepIndex);
  }
}
