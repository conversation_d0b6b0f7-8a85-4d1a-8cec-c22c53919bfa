import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class UserServiceService {

  private apiUrl: string = environment.apiUrl;
  constructor(private httpClient: HttpClient) { }

  // API to get specialisation values
  getSpecList(): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/genral/clinical-specialisations`);
  }

  // API to get clinical roles
  getGeneralClinicalRoles(): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/genral/clinical-roles`);
  }

  // API to get country values
  getCountryList(): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/genral/countries`);
  }

  // API to get county values
  getCountyList(id:any): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/genral/states/` + id);
  }

  // API to get county values
  getInsuranceList(): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/genral/clinical-insurence`);
  }

  // API to get city values
  getCityList(id: any): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/genral/cities/` + id);
  }


  // API to get clinical roles
  getClinicalRoles(id: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/users/role-list`, id);
  }

  //Api to get Roles Permissions
  getRolePermissions(id: any): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/users/role-permission-detail/` + id);
  }

  //Api to update Roles Permissions
  updateRolePermissions(id: any, key: any): Observable<any> {
    return this.httpClient.patch(`${this.apiUrl}v1/users/update-role-permission/` + id, key);
  }

  //Api to submit clinic form
  public postClinicForm(key: any): Observable<any> {
    return this.httpClient.post<any>(`${this.apiUrl}v1/auth/register-clinic`, key);
  }

  //Api to submit account owner form
  public postDocRegForm(key: any): Observable<any> {
    return this.httpClient.post<any>(`${this.apiUrl}v1/auth/register`, key);
  }

  //Api to activate link
  public postActivationLink(token: any): Observable<any> {
    return this.httpClient.post<any>(`${this.apiUrl}v1/auth/varify-user`, token)
  }

  // API to make user login
  postUserLogin(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/auth/login`, key);
  }

  //Api to forget-password
  postForgetPassword(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/auth/forgot-password`, key);
  }

  //Api to reset-password
  postResetPassword(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/auth/reset-password`, key);
  }

  //Api to activate account via email
  postActivate(token: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/auth/varify-user`, token);
  }

  //Api to submit new patient
  postNewPatient(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/patients/create`, key);
  }

  //Api to submit support form
  postSupportForm(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/genral/add-support`, key);
  }

  //Api to submit support form
  postContactForm(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/genral/contact-us`, key);
  }

  //Api to delete patient record
  postDeleteForm(id: string, key: any): Observable<any> {
    const params = new HttpParams().set('deleted_reason', key.deleted_reason);
    return this.httpClient.delete(`${this.apiUrl}v1/patients/delete/${id}`, { params });
  }

  //Api to submit feedback form
  postFeedbackForm(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/genral/send-feedback`, key);
  }

  // API to logout
  getLogout() {
    return this.httpClient.get(`${this.apiUrl}`);
  }

  // API to update User Profile
  postUpdateProfile(Key: any) {
    return this.httpClient.patch(`${this.apiUrl}v1/users/update-profile`, Key);
  }

  // API to upload the profile image
  postUploadProfileImage(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/users/upload-file`, key);
  }


  // API to upload the patient file
  postUploadPatientData(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/patients/upload-csv`, key);
  }

  // API to upload the profile image
  postUploadHeaderImage(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/users/upload-header-file`, key);
  }

  // API to upload the profile image
  postUploadFooterImage(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/users/upload-footer-file`, key);
  }

  // API to update letter header and footer
  postUserSettings(key: any): Observable<any> {
    return this.httpClient.patch(`${this.apiUrl}v1/users/settings/clinical-note`, key);
  }

  // API to upload the settings image
  postUploadSettingsImage(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/users/settings/save-image`, key);
  }

  // API to shared status
  sharedStatus(id: any, key: any): Observable<any> {
    return this.httpClient.patch(`${this.apiUrl}v1/clinical-note/edit-print-share-downlaod/` + id, key);
  }

  // API to send the email
  postEmailSent(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/clinical-note/share-note`, key);
  }

  // API to get User profile information
  getUserProfile(token: any): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/users/profile-details`, token);
  }

  // API to get pateints list
  getPatientList(page: number,key: any): Observable<any> {
    const Url = `${this.apiUrl}v1/patients/list/${page}`;
    return this.httpClient.post<any>(Url, key);

  }

  // API to patient's data
  getPatientData(id: any): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/patients/get-detail/` + id);
  }

  // API to delete note
  deleteNote(id: any, clinicId: any): Observable<any> {
    const url = `${this.apiUrl}v1/clinical-note/delete/${id}?clinic_id=${clinicId}`;
    return this.httpClient.delete(url);
  }

  


  // API to edit patient
  postEditPatient(id: string, Key: any) {
    return this.httpClient.patch(`${this.apiUrl}v1/patients/edit/` + id, Key);
  }

  // API to get pateint's notes
  getPatientNotes(id: any, page: number): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/clinical-note/list-for-patient/${page}`, id);
  }

  // API to get Invite Users List
  getInviteUsersList(page: number, key:any): Observable<any> {
    let Url = `${this.apiUrl}v1/users/list/${page}`;
    return this.httpClient.post<any>(Url,key);
  }

  // API to get doctor dashboard data
  getDoctorDashboard(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/users/doctor-dashboard`, key);
  }

  // API to get owner dashboard data
  getOwnerDashboard(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/users/owner-dashboard`, key);
  }

  // API to get Permissions List
  getPermissionsList(token: any): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/users/permission-list`, token);
  }

  // API to invite user
  postInviteUser(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/users/send-invitation`, key);
  }

  // API to delete user
  deleteUser(id: any, key:any): Observable<any> {
    const url = `${this.apiUrl}v1/users/delete/${id}?clinic_id=${key}`;
    return this.httpClient.delete(url);
  }

  // API to register invited user
  postInviteUserReg(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/users/register`, key);
  }

  // API to get invite user's data
  getInviteUsersData(id: any): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/users/detail/` + id);
  }

  // API to update User Profile
  postEditUser(id: any, key: any) {
    return this.httpClient.patch(`${this.apiUrl}v1/users/update/` + id, key);
  }

  // API to post create notes
  postCreateNotes(key: any) {
    return this.httpClient.post(`${this.apiUrl}v1/clinical-note/create`, key);
  }

  // Regenerate the created note
  regenrateCreateNotes(noteId: number, key: any) {
    return this.httpClient.patch(`${this.apiUrl}v1/clinical-note/re-create/` + noteId, key);
  }

  // API to get get generated letter
  getGeneratedLetter(id: any): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/clinical-note/get-detail/` + id);
  }

  // API to update User Profile
  updateGeneratedLetter(id: any, key: any) {
    return this.httpClient.patch(`${this.apiUrl}v1/clinical-note/edit/` + id, key);
  }

  // API to get owner dashboard table
  getOwnerDashboardTable(page: number,key:any) {
    const Url = `${this.apiUrl}v1/clinical-note/list-for-owner/${page}`;
    return this.httpClient.post<any>(Url, key);
  }

  // API to get doctor dashboard table
  getDoctorDashboardTable( page: number,key: any) {
    const Url = `${this.apiUrl}v1/clinical-note/list-for-clinician/${page}`;
    return this.httpClient.post<any>(Url, key);
  }
  // API to get cms content
  getCMSContent(id: any) {
    return this.httpClient.get(`${this.apiUrl}v1/genral/cms-detail/${id}`);
  }
  // API to get cms content
  getClinicList(token: any) {
    return this.httpClient.get(`${this.apiUrl}v1/users/get-clinic-list`);
  }
  getUserSettings(key: any) {
    return this.httpClient.post(`${this.apiUrl}v1/users/setting-details`, key);
  }
  checkEmail(key: any) {
    return this.httpClient.post(`${this.apiUrl}v1/auth/check-email`, key);
  }
  sendOtp(key: any) {
    return this.httpClient.post(`${this.apiUrl}v1/auth/send-otp`, key);
  }
  verifyOtp(key: any) {
    return this.httpClient.post(`${this.apiUrl}v1/auth/register-verify-otp`, key);
  }
  // API to verify otp
  postLoginOtp(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/auth/verify-otp`, key);
  }
  // API to resend otp
  postResendOtp(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/auth/resend-otp`, key);
  }

  updateHeaderFooter(key: any): Observable<any> {
    return this.httpClient.patch(`${this.apiUrl}v1/users/settings/clinical-note`, key);
  }
  // API to get pricing plans frontend
  postPrcingQuery(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/subscription/plan-list`, key);
  }
  // API to create customer
  createCustomer(key: any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/subscription/create-customer`, key);
  }
  // API to get stripe customer
  getStripeCustomer(): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/subscription/customer-detail`);
  }
  // API to create subscription
  createSubscription(key:any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/subscription/create`, key);
  }
  // API to get user subscription list
  getUserSubscriptionList(): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/subscription/user-subscription-list`);
  }
  // API to cancel subscription
  cancelUserSubscription(key:any): Observable<any> {
    return this.httpClient.patch(`${this.apiUrl}v1/subscription/cancell-user-subscription`, key);
  } 
   // API to get subscription plan detail
   getUserPlanDetail(id:any): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/subscription/plan-detail/${id}`);
  }
  // API to update user subscription
  updateUserSubscription(key:any): Observable<any> {
    return this.httpClient.patch(`${this.apiUrl}v1/subscription/update-user-subscription`, key);
  }
   // API to get subscription plan detail
   getUserSubscriptionDetail(id:any): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/subscription/detail-user-subscription/${id}`);
  }
  // API to get user invoice list
  getUserInvoiceList(page: number): Observable<any> {
    const Url = `${this.apiUrl}v1/subscription/list-user-invoice/${page}`;
    return this.httpClient.get<any>(Url);
  }
  // API to get user invoice  detail
  getUserInvoiceDetail(id:any): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/subscription/detail-user-invoice/${id}`);
  }
  // API to get user invoice  detail
  postBuyCredits(request:any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/credits/purchase`, request);
  }
  // API to confirm user subscription
  confirmSubscription(request:any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/subscription/confirm-user-subscription`, request);
  }
  // API to confirm credits bought
  confirmCreditsPurchase(request:any): Observable<any> {
    return this.httpClient.post(`${this.apiUrl}v1/credits/confirm-purchase`, request);
  }
   // API to credits purchase list
   creditsPurchaseList(page: number): Observable<any> {
    const Url = `${this.apiUrl}v1/credits/purchase-list/${page}`;
    return this.httpClient.get<any>(Url);
  }
  // API to get credit purchase  detail
  getCreditDetail(id:any): Observable<any> {
    return this.httpClient.get(`${this.apiUrl}v1/credits/purchase-detail/${id}`);
  }
}

