// stripe.service.ts

import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class StripeService {
  private stripePromise: Promise<any>;

  constructor() {
    this.stripePromise = this.loadStripe();
  }

  private loadStripe(): Promise<any> {
    return new Promise((resolve, reject) => {
      // Check if Stripe script is already loaded
      if ((window as any).Stripe) {
        resolve((window as any).Stripe(environment.stripePublishableKey));
      } else {
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = 'https://js.stripe.com/v3/';
        document.head.appendChild(script);
  
        script.onload = () => {
          resolve((window as any).Stripe(environment.stripePublishableKey));
        };
  
        script.onerror = (error: Event | string) => {
          reject(error);
        };
      }
    });
  }
  
  getStripe(): Promise<any> {
    return this.stripePromise;
  }

  async createPaymentMethod(card: any, countryShortName:any, billingDetails: any): Promise<any> {
    try {
      const stripe = await this.stripePromise;
  
      const result = await stripe.createPaymentMethod({
        type: 'card',
        card: card,
        "billing_details": {
          address: {
            city: billingDetails.town,
            country: countryShortName || null,
            line1: billingDetails.address,
            postal_code: billingDetails.post_code,
            state: billingDetails.county
          },
          email: billingDetails.email,
          name: billingDetails.first_name + ' ' + billingDetails.last_name,
          phone: billingDetails.phone
        },
      });
  
      return result;
    } catch (error) {
      console.error('Error creating payment method:', error);
      throw error; // Rethrow the error for the calling code to handle if needed
    }
  }

  async createPaymentMethodForCredits(card: any, countryShortName:any, billingDetails: any): Promise<any> {
    try {
      const stripe = await this.stripePromise;
  
      const result = await stripe.createPaymentMethod({
        type: 'card',
        card: card,
        "billing_details": {
          address: {
            city: billingDetails.town,
            country: countryShortName || null,
            line1: billingDetails.address,
            postal_code: billingDetails.post_code,
            state: billingDetails.county
          },
          email: billingDetails.email,
          name: billingDetails.first_name + ' ' + billingDetails.last_name,
          phone: billingDetails.phone
        },
      });
  
      return result;
    } catch (error) {
      console.error('Error creating payment method:', error);
      throw error; // Rethrow the error for the calling code to handle if needed
    }
  }

  confirmCardPayment(clientSecret: string, paymentMethodId: any): Promise<any> {
    return this.stripePromise.then(stripe => {
      return stripe.confirmCardPayment(clientSecret, {
        payment_method: paymentMethodId
      });
    });
  }

  retrieveSetupIntent(clientSecret:any): Promise<any> {
    return this.stripePromise.then(stripe => {
      return stripe.retrieveSetupIntent(clientSecret);
    });
  }

  orderComplete(clientSecret:any): Promise<any> {
    return this.stripePromise.then(stripe => {
      return stripe.retrieveSetupIntent(clientSecret);
    });
  }

  retrievePaymentMethod(paymentMethodId:any): Promise<any> {
    return this.stripePromise.then(stripe => {
      return stripe.payment_methods?.retrieve(paymentMethodId)
    })
  }
  
}
