import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class LoadingService {
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private loadingTextSubject = new BehaviorSubject<string>('Loading');

  loading$: Observable<boolean> = this.loadingSubject.asObservable();
  loadingText$: Observable<string> = this.loadingTextSubject.asObservable();

  showLoading(text: string = 'Loading') {
    this.loadingTextSubject.next(text);
    this.loadingSubject.next(true);
  }

  hideLoading() {
    this.loadingTextSubject.next('Loading');
    this.loadingSubject.next(false);
  }
}
