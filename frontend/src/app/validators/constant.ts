import { Validators } from "@angular/forms";

export const gender = [
  { label: 'Male', value: 'Male' },
  { label: 'Female', value: 'Female' },
  { label: 'Prefer not to say', value: 'Prefer not to say' },
];

export const currencyOptions = [
  { label: 'GBP', value: 'GBP' },
  { label: 'USD', value: 'USD' },
  { label: 'EUR', value: 'EUR' },
];

export const planTypes = [
  { label: 'Monthly', value: 'Monthly' },
  { label: 'Yearly', value: 'Yearly' },
];

export const period = [
  { label: 'Days', value: 'Days' },
  { label: 'Months', value: 'Months' },
  { label: 'Years', value: 'Years' }
];

export const accordionData = [
  {
    header: 'Who owns Clinical Pad?',
    content: 'Clinical Pad is owned by Neurosens Ltd, a UK based technology company.',
  },
  {
    header: 'Where is Clinical Pad based?',
    content: 'Clinical Pad is based in Berkshire, United Kingdom. Our team however is fully remote and based around the world.',
  },
  {
    header: 'Does Clinical Pad have an app for phones, tablets and laptops?',
    content: 'We do not have an app at this stage. However this is something we look to implement in the near future. Clinical Pad is predominantly designed to be used on a laptop or desktop. However, can be used on mobile phones and tablets as well.',
  },
  {
    header: 'Does Clinical Pad integrate with any EMS/EMR systems?',
    content: 'We do not offer integrations to any EMS/EMR providers at this stage. This is however a feature we are looking to implement in the near future.',
  },
  {
    header: 'How much does Clinical Pad cost?',
    content: 'At this point in time, Clinical Pad is completely free to use with no hidden costs. No credit/debit card details are required when signing up. Simply sign-up with your information and start using the application straight away.We hope to introduce monthly and yearly subscription options in the near future. We will be notifying all our users in advance of any changes to our pricing plan. For the time being, you get to enjoy Clinical Pad completely free.',
  },
  {
    header: 'Can I import patient records to the application?',
    content: 'We do not offer this feature at this point in time. Another feature we hope to introduce very soon.',
  },
  {
    header: 'What type of accounts do you offer?',
    content: 'Users have 2 options when signing-up with Clinical Pad. <ul><li><b>Option 1:</b> Individual Account. As the name suggests, this is for individual users who would like to create letters and manage all their patients’ letters in one place. Individual users get to add staff members free of charge to their account to help with editing and managing generated letters.</li> <li><b>Option 2:</b> Clinic/Hospital Account. This type of account is for clinics, Surgeries, hospital and any other organisation that would usually create clinical letters. This account allows the account owner to add and manage multiple clinicians and staff members to this account. Based on the permissions allowed by the account owner, Clinicians can create letters and much more, while staff members would have restricted access to either view, edit or share the generated letter.</li></ul>',
  },
  {
    header: 'Is Clinical Pad similar to EMS/EMR systems?',
    content: 'Not at all. Clinical Pad is not designed to replace any EMS/EMR system. This is simply a writing tool packed with features to help clinicians create and manage clinical letters.',
  },
  {
    header: 'I currently use medical transcription services to generate clinical letters. How does Clinical Pad differ?',
    content: 'Traditional transcription services can be costly, have long turnaround times and sometimes contain inaccurate information. In addition, clinicians have to spend additional time recording voice notes/ dictations and then uploading or sending them securely to their transcription service.<br>Clinical Pad takes away all this hassle from the clinician by allowing the user to simply type in the patient notes they would usually take down while with the patient and simply click on the ‘Generate Letter’ button to generate a professional clinical letter with their customised letterhead and signature all intact ready to be shared in only a matter of seconds.',
  },
  {
    header: 'How efficient and accurate are the letters generated by Clinical Pad?',
    content: 'Clinical Pad uses Artificial intelligence and Machine learning to generate clinical letters. The application was initially trained on thousands of anonymised patient notes and clinical letters. As a new application, Clinical Pad is continuously learning with every patient note typed and every letter it generates. At its early stages, the generated letters may have to be edited to meet certain expectations. But as time goes on and our users continue to use this application, the generated letters become smarter, accurate and efficient.',
  },
  {
    header: 'Can I edit the the generated letters?',
    content: 'Absolutely. All generated letters will be presented to you in an editable screen where you can customise and edit the letter as required. Once done, you can preview your letter and either print, download or share via email.',
  },
  {
    header: 'What type of medical documentation does Clinical Pad generate?',
    content: 'Currently the application generates clinical letters. We will be implementing other medical documentation in the near future.',
  },
  {
    header: 'Can I use Clinical Pad in my country?',
    content: 'Clinical Pad is currently only available to users within the UK. We are however currently in the process of making this application available to users across the world. We hope to make this available in the coming months.',
  },
  {
    header: 'I have questions around data usage and security of the system. Where can I find out more?',
    content: `You can find out more on data usage and security of Clinical Pad on our Security page on our website. In addition, you can always view our Privacy policy and Data usage policy for more information.`,
  },

];

export const accordionSecurityData = [
  {
    header: 'Where is my data stored?',
    content: `<p>We use Amazon Web Services (AWS) servers to store all your data. AWS is the industry leader when it comes to secure data storage and is used by major corporations to safely store data.</p><p>For the purpose of data storage, all data are stored securely in our AWS servers located in the United Kingdom. For the purpose of generating letters using AI, our AI partner Open AI is located in the United States. So, while non-identifiable information will not be saved with Open AI, this information will transit through US servers in order to generate a response.</p><p>If that sounds like a bunch of nonsensical jargon to you, here’s what it means: all data shared between you and Clinical Pad is transmitted and stored securely in our secure servers. No one can read or access the information except for you and us.</p>`,
  },
  {
    header: 'Who has access to my data?',
    content: `Your data is only accessible to yourself (The account owner) and us. Once the data is added to Clinical Pad, it is securely stored in our servers with no other points of access.`,
  },
  {
    header: 'How long is my data stored for?',
    content: `<p>As long as you continue to maintain an account with us, your data will be stored and made available to you. Should you however delete a record or cancel your subscription, then the data will be instantly deleted and cannot be retrieved.</p><p>We hope to implement a data saving feature in the near future.</p>`,
  },
  {
    header: 'How will my data be kept safe if I’m using AI to generate letters?',
    content: `<p>Many AI writing tools/applications in the market cannot be used for patient sensitive material. This is because these tools use the data you enter, to train and fine-tune their AI systems to increase its usability.</p><p>Clinical Pad does exactly this but with safeguards in place to ensure all our users can add all the required information to generate a clinical letter without compromising the safety and confidentiality of the entered data. In fact, we built Clinical Pad using thousands of anonymised clinical letters and notes to ensure the privacy and confidentiality of the used data. We have designated areas to add patient identifiable information which is stored in a dedicated safe AWS server in encrypted form and will <b><u>not</u></b> be used to train and fine-tune the system. All other non-identifiable information added to the system will be used to train and fine-tune the system to provide better quality letters every time you use Clinical Pad.</p>`,
  },
  {
    header: 'How will my data be managed?',
    content: `<p>In addition to being fully vetted, all our staff are trained in data security and privacy in accordance with GDPR standards. Which means your data is always handled with the utmost confidentiality.</p><p>We never sell or share identifiable or non-identifiable data. While you have complete access to your data, as a further security measure, we only have limited access to any identifiable data you enter. This means, while we have access to your data, it is only limited, always keeping you in full control of your data.</p><p>We do however use non-identifiable data entered into Clinical Pad to train and fine-tune the application to help the application constantly learn from your input and provide better quality letters as you continue to use it. Importantly, we also use non-identifiable data to derive important medical trends. More information on this can be found on the following FAQ.</p>`,
  },
  {
    header: 'How do you use non-identifiable data?',
    content: `<p>Non-identifiable data is never shared or sold. We use this data to train and fine-tune the application to help the application constantly learn from your input and provide better quality letters as you continue to use it.</p><p>We also use this data for a few important causes. We use this type of data to understand important medical trends in terms of diseases, causes, accuracy of diagnoses, develop preventative care and many more. This important information is gathered by collecting and analysing the data. Only the results from this analysis is then shared with medical bodies, research organisations and others to assist with developing better care for patients across the world.</p>`,
  },
  {
    header: 'What happens to my data when I delete a record?',
    content: `<p>When you delete a record, your data will not be saved and will be lost. While we hope to implement a feature to recover deleted records in the near future, this option is not currently available.</p><p>When deleting a record, it’s important to know that this record will no longer be available.</p>`,
  },
  {
    header: 'What happens to my data when I cancel my subscription?',
    content: `When you cancel your subscription, all your data will be lost. We hope to implement a feature to recover cancelled subscriptions in the near future.`,
  },
  {
    header: 'What can I do to keep my data safe?',
    content: `
      <p>While keeping your data safe is our topmost priority, data security starts with you. Following the below Safe Use points will ensure you are doing your part to keep your data safe and secure at all times.</p>
      <ul>
        <li><b>Keep your browser and device up to date:</b><br />This prevents any unwanted malware entering your browser or device and ultimately Clinical Pad as a result. In fact, this should be a standard practice for any application you may use.</li>
        <li><b>Create strong passwords:</b><br />Creating a strong password cannot be underestimated. Creating and maintaining a strong password will prevent criminals from guessing and entering your account.</li>
        <li><b>Understanding user roles:</b><br />Understanding and managing user roles will ensure you provide access to confidential information only to those who need it.</li>
        <li><b>Do not share your password:</b><br />Under no circumstance do we encourage sharing your password. Should you wish to provide a user access to your account, you can do this by inviting them and controlling what they have access to.</li>
      <ul>
    `,
  },
];

export const insuranceStatus = [
  { label: 'Active', value: 'Active' },
  { label: 'Inactive', value: 'Inactive' },
];

export const userRole = [
  { label: 'Clinician', value: 'Clinician' },
  { label: 'Staff Member', value: 'Staff Member' },
];

export const letterTypes = [
  { label: 'Clinical Letter', value: 'Clinical Letter' },
  { label: 'Clinical Summary(SOAP)', value: 'Clinical Summary' },
];
export const letterTypes2 = [
  { label: 'All Documents', value: 'All Letters' },
  { label: 'Clinical Letter', value: 'Clinical Letter' },
  { label: 'Clinical Summary(SOAP)', value: 'Clinical Summary' }
];

export function calculateAge(dob: Date): string | null {
  if (dob) {
    const today = new Date();
    const birthDate = new Date(dob);

    const timeDiff = Math.abs(today.getTime() - birthDate.getTime());
    const days = Math.floor(timeDiff / (1000 * 3600 * 24));
    const months = Math.floor(days / 30);
    const years = Math.floor(months / 12);

    if (days < 30) {
      return `${days} Days`;
    } else if (days < 365) {
      return `${months} Months`;
    } else {
      return `${years} Years`;
    }
  }

  return null;
}


export const passwordValidator = Validators.pattern(/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%&])[A-Za-z\d!@#$%&]{8,20}$/)
export const phoneValidator = Validators.pattern(/^(?:\+?\d{8,16})$/)
export const postCodeValidator = Validators.pattern(/^(?=.*[a-zA-Z])(?=.*[0-9])[a-zA-Z0-9\s]{6,8}$/);
export const clinicRegNumberValidator = Validators.pattern(/^(?!^0+$)[a-zA-Z0-9]{1,30}$/);
export const regNumberValidator = Validators.pattern(/^(?=.*[a-zA-Z])(?=.*[0-9])[a-zA-Z0-9\s]{8,15}$/);
export const nameValidation = Validators.pattern(/^[a-zA-Z0-9\s]{1,20}$/);
export const addressValidation = Validators.pattern(/^.{1,255}$/);
export const emailValidation = Validators.pattern(/^.{1,50}$/);
export const subjectValidation = Validators.pattern(/^.{1,250}$/);
export const helpStatus = {
  "1":"Open",
  "2":"In Progress",
  "3":"Closed",
}
export const properNumValidation = Validators.pattern(/^[0-9]+$/);
export const rationalNumValidation = Validators.pattern(/^[0-9]+(?:\.[0-9]{1,2})?$|^([0-9]+)\/([1-9][0-9]*)$/);
