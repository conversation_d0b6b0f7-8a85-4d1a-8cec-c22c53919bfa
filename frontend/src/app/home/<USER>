import { Component } from '@angular/core';
import { SvgIcons } from '../validators/svg-icons';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { delay, take, timer } from 'rxjs';
declare var bootstrap: any;
@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent {
  svgIcons = SvgIcons;
  benefits: any[] = [
    {
      benefitsImg: "benifit-img-1.png",
      benefitsTitle: "Instant Letters",
      benefitsDetails: "Letters generated in real-time with patient notes.",
    },
    {
      benefitsImg: "benifit-img-2.png",
      benefitsTitle: "Reduced Patient Wait Times",
      benefitsDetails: "Save on average 5-7 days, from patient consultation to sharing the completed clinical letter.",
    }, {
      benefitsImg: "benifit-img-3.png",
      benefitsTitle: "Significant Savings",
      benefitsDetails: "Save up to 85% on medical transaction costs.",
    }, {
      benefitsImg: "benifit-img-4.png",
      benefitsTitle: "Security & Confidentiality",
      benefitsDetails: "You control who creates, edits and views patient information and the contents of the generated letters.",
    }, {
      benefitsImg: "benifit-img-5.png",
      benefitsTitle: "No Limits",
      benefitsDetails: "Create as many letters as required with no restrictions.",
    }, {
      benefitsImg: "benifit-img-6.png",
      benefitsTitle: "Simple to Use",
      benefitsDetails: "A simple to use system with little to no training required. Simply sign-up and start creating letters.",
    }
  ];
  constructor(
    private titleService: Title,
    private route: ActivatedRoute,
  ) {
    this.titleService.setTitle(this.route.snapshot.data['title']);
    this.route.fragment.pipe(delay(200)).subscribe((f) => {
      if (f) {
        const anchors: any = document.querySelector(`[href="#${f}"]`);
        if (anchors) {
          timer(100).pipe(take(1)).subscribe(() => {
            anchors.click()
          })
        }
      }
    })
  }
  ngAfterViewInit() {
    timer(100).pipe(take(1)).subscribe(() => {
      this.setScrollSpy()
    })
  }
  private setScrollSpy() {
    const scrollSpyBody = document.getElementById('scrollSpyBody')
    const scrollSpy: any = new bootstrap.ScrollSpy(scrollSpyBody, {
      target: "#theme-home-navbar"
    })
    scrollSpy.refresh();
    return scrollSpy;
  }

}
