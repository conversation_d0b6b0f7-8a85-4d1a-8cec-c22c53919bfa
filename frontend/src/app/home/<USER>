@import "../../assets/css/variables";

// Banner Section
.theme-home-banner {
    background-image: url('../../assets/images/home-banner-bg.jpg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;

    .theme-home-banner-cover {
        margin-bottom: -9.75%;
    }
}

// Feature Section
.theme-features-content {
    padding-top: 100px;

    @include below-lg() {
        padding-top: 60px;
    }

    @include below-md() {
        padding-top: 40px;
    }

    .mw-920 {
        max-width: 920px;
    }

    .theme-features-bg-1,
    .theme-features-bg-2 {
        @include below-xl() {
            max-width: 20%;
        }

        @include below-md() {
            max-width: 25%;
        }
    }

    .theme-features-box-main {
        .theme-features-box {
            margin-bottom: 56px;

            @include below-md() {
                margin-bottom: 40px;
            }

            .theme-feature-icon {
                width: 42px;
                height: 42px;
                background-color: rgba($primary, .10);
                box-shadow: 0px 0px 0 8px rgba($primary, .05);

                &.theme-svg-size-23 {
                    ::ng-deep {
                        svg {
                            width: 23px;
                            height: 23px;
                        }
                    }
                }

                &.pulse {
                    animation: pulse-animation 3s infinite;
                }

                @keyframes pulse-animation {
                    0% {
                        box-shadow: 0 0 0 8px rgba($primary, .05);
                    }

                    50% {
                        box-shadow: 0 0 0 0 rgba($primary, .05);
                    }

                    100% {
                        box-shadow: 0 0 0 8px rgba($primary, .05);
                    }
                }
            }


        }

        .theme-features-image {
            &.ratio {
                --bs-aspect-ratio: 61.86%;
                border-radius: 1.5rem;

                @include below-md() {
                    border-radius: 1rem;
                }

                .theme-features-image-inner {
                    border-radius: 1.25rem;

                    @include below-md() {
                        border-radius: 0.75rem;
                    }

                    img {
                        min-height: 100%;
                    }
                }
            }
        }
    }
}

// Security Section
.theme-security-content {
    .theme-security-bg-1 {
        @include below-lg() {
            max-width: 30%;
        }
    }
}

// Benefits Section
::ng-deep {
    .theme-benefits-content {
        .theme-benefits-box-cover {
            .theme-benefits-item {
                .theme-benefits-item-img {
                    width: 240px;
                    height: 200px;

                    @include below-lg() {
                        width: 200px;
                        height: 160px;
                    }

                    img {
                        max-width: 85%;
                        max-height: 85%;
                    }
                }
            }
        }
    }
}

// Sign-Up Section
.theme-signup-content {
    .mw-172 {
        min-width: 172px;
    }

    .theme-signup-bg-1,
    .theme-signup-bg-2 {
        @include below-xl() {
            max-width: 25%;
            height: auto;
        }
    }
}