/* You can add global styles to this file, and also import other style files */
@import "./assets/css/variables";
@import "../node_modules/bootstrap/scss/bootstrap";

//Custom CSS
.btn-primary {
    color: $white !important;
}

.mw-1 {
    min-width: 1px;
}

.fs-8 {
    font-size: $theme-size-8 !important;
}

.fs-9 {
    font-size: $theme-size-9 !important;
}

.fs-10 {
    font-size: $theme-size-10 !important;
}

.fs-11 {
    font-size: $theme-size-11 !important;
}

.fs-12 {
    font-size: $theme-size-12 !important;
}

.fs-13 {
    font-size: $theme-size-13 !important;
}

.fs-14 {
    font-size: $theme-size-14 !important;
}

.fs-15 {
    font-size: $theme-size-15 !important;
}

.fs-18 {
    font-size: $theme-size-18 !important;
}

.w-20 {
    width: 1.25rem;
}

.h-20 {
    height: 1.25rem;
}

.w-37 {
    width: 2.3125rem;
}

.h-37 {
    height: 2.3125rem;
}

.w-40 {
    width: 2.5rem;
}

.h-40 {
    height: 2.5rem;
}

.min-w-50{
    min-width: 50px;
}

.mw-110 {
    min-width: 110px;
}

.font-family-base {
    font-family: $font-family-base !important;
}

.lh-xl {
    line-height: 1.75;
}

.pulse-wave {
    animation: pulse-wave-animation 2.5s infinite;
}

@keyframes pulse-wave-animation {
    0% {
        box-shadow: 0 0 0 8px rgba($danger, .25);
    }

    50% {
        box-shadow: 0 0 0 0 rgba($danger, .25);
    }

    100% {
        box-shadow: 0 0 0 8px rgba($danger, .25);
    }
}

// Custom Scrollbar
@include above-xl() {
    * {
        scrollbar-width: thin;
        scrollbar-color: $primary;

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
            background-color: $theme-body-bg;
        }

        &::-webkit-scrollbar-thumb {
            background-color: $dark;
            border-radius: 1.25rem;

            &:hover {
                background-color: $primary;
            }
        }
    }
}

// Placeholder CSS
::-webkit-input-placeholder {
    color: $input-placeholder-color;
}

::-moz-placeholder {
    color: $input-placeholder-color;
    opacity: 1;
}

::-ms-input-placeholder {
    color: $input-placeholder-color;
}

::placeholder {
    color: $input-placeholder-color;
}


//PrimeNg Override CSS
body {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;

    .theme-bg-light {
        background-color: $theme-body-bg !important;
    }

    .p-component {
        font-family: $input-font-family;

        &:disabled,
        &.p-disabled {

            &.p-inputtext,
            &.p-dropdown {
                opacity: 1;
                color: rgba($body-color, .5);
                background-color: $input-disabled-bg;
            }
        }

        &:not(.p-toast, .p-datepicker, .p-datepicker-trigger, .p-inputswitch, .p-overlay) {
            width: 100%;
        }

        &.p-toast {
            width: 20rem;
            font-size: $theme-size-14;
            opacity: 1;

            @include below-xsm() {
                width: auto;
                top: .5rem;
                right: .5rem;
                left: .5rem;
            }

            .p-toast-message {
                @include below-xsm() {
                    border-radius: 0;
                }

                .p-toast-message-content {
                    .p-toast-detail {
                        margin: 0.25rem 0 0 0;
                    }
                }

                .p-toast-icon-close {
                    &:focus {
                        box-shadow: $theme-focus-shadow;
                    }
                }

                &.p-toast-message-error {

                    .p-toast-message-icon,
                    .p-toast-icon-close {
                        color: $danger;
                    }

                    background: #ffc9c9;
                    border-color: $danger;
                    color: $danger;
                }
            }
        }

        &.p-input-icon-right {
            >.p-inputtext {
                padding-right: 3rem;
            }

            &.p-password {
                >[type="text"] {
                    ~ {
                        .p-icon-wrapper {
                            background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='16.271' height='13.948' viewBox='0 0 16.271 13.948'><path d='M17541.186,11300.285l-12.91-12.92a.513.513,0,0,1,0-.727.519.519,0,0,1,.732,0l12.908,12.921a.515.515,0,0,1,0,.726.487.487,0,0,1-.23.136.474.474,0,0,1-.139.018A.507.507,0,0,1,17541.186,11300.285Zm-14.078-6.439a.579.579,0,0,1,0-.774,25.321,25.321,0,0,1,3.092-2.953l1.939,1.942a3.27,3.27,0,0,0,4.355,4.352l1.584,1.58a7.039,7.039,0,0,1-2.994.7c-3.51,0-5.629-2.226-7.951-4.81Zm10.941,1.013a3.266,3.266,0,0,0-3.107-4.664,3.211,3.211,0,0,0-1.244.311l-1.588-1.587a6.8,6.8,0,0,1,2.979-.69c3.477,0,6.109,2.535,8.01,4.843a.564.564,0,0,1-.018.75,23.769,23.769,0,0,1-3.09,2.98Zm-3.131.92a2.341,2.341,0,0,1-2.141-2.14,2.422,2.422,0,0,1,.092-.854l2.9,2.9a2.212,2.212,0,0,1-.678.1c-.049,0-.1,0-.152,0Zm1.723-2.324a.921.921,0,0,0,.115,0,1.622,1.622,0,0,0,.66-.143c0,.048,0,.095,0,.143a2.242,2.242,0,0,1-.1.672Zm-2.215-2.223a2.274,2.274,0,0,1,.67-.1c.041,0,.078,0,.113,0a1.733,1.733,0,0,0-.113.614c0,.054,0,.109.008.164Z' transform='translate(-17526.961 -11286.485)' fill='%239B9BAA' /></svg>");
                        }
                    }
                }

                >[type="password"] {
                    ~ {
                        .p-icon-wrapper {
                            background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='16.271' height='10.46' viewBox='0 0 16.271 10.46'><path d='M17489.357,11292.139a.571.571,0,0,1,0-.773c2.756-3,5.053-4.841,7.973-4.841,3.48,0,6.107,2.536,8.014,4.841a.57.57,0,0,1-.016.753c-1.867,2.043-4.5,4.866-8,4.866C17493.8,11297,17491.684,11294.725,17489.357,11292.139Zm7.832-3.649a3.217,3.217,0,1,0,.145-.006,1.007,1.007,0,0,0-.127-.011Zm-.023,5.581a2.324,2.324,0,0,1,.178-4.639h.111a1.7,1.7,0,0,0-.111.614,1.682,1.682,0,0,0,1.656,1.705,1.616,1.616,0,0,0,.664-.142v.142a2.322,2.322,0,0,1-2.32,2.323.624.624,0,0,0-.189-.023Z' transform='translate(-17489.207 -11286.524)' fill='%239B9BAA' /></svg>");
                        }
                    }
                }

                .p-icon-wrapper {
                    cursor: pointer;
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: 1.15rem;
                    width: 3rem;
                    height: 2.5rem;
                    top: 0;
                    right: 0;
                    margin-top: 0;

                    svg {
                        opacity: 0;
                    }
                }

            }
        }

        .p-dropdown-panel {
            font-size: $theme-size-14;

            .p-dropdown-items {
                .p-dropdown-item {
                    padding: 0.5rem 1rem;
                    color: $body-color;
                    font-weight: 500;
                    white-space: normal;

                    &.p-highlight {
                        color: $primary;
                        background: $light;
                    }
                }

                .p-dropdown-empty-message {
                    padding: 0.5rem 1rem;
                    color: $body-color;
                }
            }
        }

        &.p-radiobutton {
            .p-radiobutton-box {
                border-color: rgba($body-color, .5);

                &:not(.p-disabled) {
                    &.p-focus {
                        box-shadow: $theme-focus-shadow;
                    }

                    &:not(.p-highlight) {
                        &:hover {
                            border-color: $primary;
                        }
                    }
                }

                &.p-highlight {
                    border-color: $primary;
                    background-color: $white;

                    .p-radiobutton-icon {
                        background-color: $primary;
                    }

                    &:not(.p-disabled) {
                        &:hover {
                            border-color: $primary;
                            background: $white;
                        }
                    }
                }
            }
        }

        &.p-checkbox {
            &:not(.p-checkbox-disabled) {
                .p-checkbox-box {
                    &:hover {
                        border-color: $primary;
                    }

                    &.p-highlight {
                        &:hover {
                            border-color: $primary;
                            background: $primary;
                        }
                    }

                    &.p-focus {
                        box-shadow: $theme-focus-shadow;
                        border-color: $primary;
                    }
                }
            }

            .p-checkbox-box {
                color: $body-color;
                border-color: $body-color;
                border-radius: .25rem;
                width: 1.375em;
                height: 1.375em;
                border-width: 1px;
                margin-bottom: 1px;

                .p-icon {
                    width: .94em;
                    height: .94em;
                    font-size: 1em;
                }

                &.p-highlight {
                    border-color: $primary;
                    background-color: $primary;
                }
            }
        }

        &.p-datepicker {
            color: $body-color;

            .p-datepicker-header {
                color: $body-color;

                .p-datepicker-prev,
                .p-datepicker-next {
                    color: $body-color;

                    &:enabled {
                        &:hover {
                            color: $body-color;
                            background-color: $light
                        }
                    }

                    &:focus {
                        box-shadow: $theme-focus-shadow;
                    }
                }

                .p-datepicker-title {
                    line-height: 1rem;

                    .p-datepicker-year,
                    .p-datepicker-month {
                        color: $body-color;

                        &:enabled {
                            &:hover {
                                color: $primary;
                            }
                        }
                    }
                }


            }

            table {
                font-size: $theme-size-14;

                th {
                    padding: .5em .25em;
                    text-align: center;
                }

                td {
                    padding: .25em;

                    &.p-datepicker-today {
                        >span {
                            background: $light;
                            color: $body-color;
                        }
                    }

                    >span {
                        width: 2.5em;
                        height: 2.5em;

                        &:focus {
                            box-shadow: $theme-focus-shadow;
                        }

                        &.p-highlight {
                            color: $white;
                            background: $primary;
                        }
                    }

                    span {
                        &:not(.p-highlight) {
                            &:not(.p-disabled) {
                                &:focus {
                                    box-shadow: $theme-focus-shadow;
                                }

                                &:hover {
                                    background-color: $light
                                }
                            }
                        }
                    }
                }
            }

            &:not(.p-disabled) {

                .p-monthpicker,
                .p-yearpicker {

                    .p-monthpicker-month,
                    .p-yearpicker-year {
                        &:not(.p-disabled) {
                            &:not(.p-highlight) {
                                &:hover {
                                    background: $light
                                }
                            }

                            &:focus {
                                box-shadow: $theme-focus-shadow;
                            }
                        }
                    }
                }
            }
        }

        .p-link {
            font-size: $theme-size-14;
            font-family: $font-family-base;

            &:focus {
                box-shadow: $theme-focus-shadow;
            }
        }

        // Switch Toggle
        .p-inputswitch {
            width: 37px;
            height: 15px;
            vertical-align: middle;

            .p-inputswitch-slider {
                background: rgba($primary, .12);

                &:before {
                    width: 21px;
                    height: 21px;
                    left: 0;
                    border: 1px solid $primary;
                    margin-top: -10.5px;
                }
            }

            &:not(.p-disabled) {
                &:hover {
                    .p-inputswitch-slider {
                        background: rgba($primary, .20);
                    }
                }
            }

            &.p-inputswitch-checked {
                .p-inputswitch-slider {
                    background: $primary;

                    &:before {
                        transform: translateX(17px);
                    }
                }

                &:not(.p-disabled) {
                    &:hover {
                        .p-inputswitch-slider {
                            background: $primary;
                        }
                    }
                }
            }

            &.p-focus {
                .p-inputswitch-slider {
                    box-shadow: none;
                }
            }
        }


    }

    .theme-checkbox {
        &.fs-14 {
            ~.text-danger {
                padding-left: 27px;
            }
        }

        ~.text-danger {
            padding-left: 30px;

        }
    }

    .p-inputtext {
        padding-top: $input-padding-y;
        padding-bottom: $input-padding-y;
        padding-left: $input-padding-x;
        padding-right: $input-padding-x;
        font-family: $input-font-family;
        font-size: $input-font-size;
        border-color: $light;
        resize: none;
        font-weight: 500;

        &:enabled {
            &:focus {
                box-shadow: $theme-focus-shadow;
                border-color: $primary;
            }

            &:hover {
                border-color: $primary;
            }
        }
    }

    .p-dropdown {
        border-color: $light;

        .p-dropdown-label {
            &.p-placeholder {
                color: rgba($body-color, .5);
            }
        }

        &:not(.p-disabled) {
            &:hover {
                border-color: $primary;
            }

            &.p-focus {
                box-shadow: $theme-focus-shadow;
                border-color: $primary;
            }
        }

        .p-dropdown-trigger {
            color: $body-color;

            .p-icon-wrapper {
                .p-dropdown-trigger-icon {
                    width: 0.75rem;
                    height: 0.75rem;
                }
            }
        }
    }

    .p-element {
        .p-editor-container {
            .p-editor-toolbar {
                background-color: $light;

                &.ql-snow {
                    padding: 0.6rem 0.75rem;
                    border-color: $light;
                    font-family: $font-family-base;
                }
            }

            .p-editor-content {
                &.ql-snow {
                    border-color: $light;
                    font-family: $font-family-base;
                    font-size: 1rem;

                    .ql-editor {
                        color: $body-color;
                        padding: 1.25rem 1rem;
                    }
                }
            }
        }

        // Modal Main
        .p-dialog-mask {
            .theme-modal-main {
                &:not(.p-dialog-maximized) {
                    &.theme-modal-xl {
                        max-width: 1000px;

                        @include below-xl() {
                            max-width: 90%;
                        }
                    }

                    &.theme-modal-lg {
                        max-width: 700px;

                        @include below-lg() {
                            max-width: 90%;
                        }
                    }

                    &.theme-modal-md {
                        max-width: 600px;

                        @include below-md() {
                            max-width: 90%;
                        }
                    }

                    &.theme-modal-sm {
                        max-width: 500px;

                        @include below-sm() {
                            max-width: 90%;
                        }
                    }
                }

                &.p-dialog-maximized {
                    border-radius: 0;

                    .p-dialog-header,
                    .p-dialog-footer,
                    .p-dialog-content {
                        border-radius: 0;
                    }
                }

                .p-dialog-header,
                .p-dialog-content,
                .p-dialog-footer {
                    color: $body-color;
                }

                .p-dialog-header {
                    border-bottom: 2px solid $light;
                    padding: 1.25rem 1.75rem;

                    .p-dialog-header-icons {

                        .p-dialog-header-icon {
                            color: rgba($body-color, .5);

                            .p-icon {
                                width: 1.25rem;
                                height: 1.25rem;
                            }

                            &.p-dialog-header-maximize {
                                @include above-xl() {
                                    display: none;
                                }
                            }
                        }

                    }
                }

                .p-dialog-content {
                    padding: 1rem 1.75rem 2rem;

                    &:last-of-type {
                        border-bottom: $theme-size-14 solid $light;
                    }
                }

                .p-dialog-footer {
                    border-top: 2px solid $light;
                    padding: 1rem 1.75rem;
                }

                &.theme-modal-center {
                    .p-dialog-header {
                        border: 0;
                        padding: 1.75rem 2.5rem 0.75rem;
                        justify-content: center;

                        @include below-sm() {
                            padding: 1.75rem 1.5rem 0.75rem;
                        }
                    }

                    .p-dialog-content {
                        padding: 0 2.5rem 0.25rem;

                        @include below-sm() {
                            padding: 0 1.5rem 0.25rem;
                        }

                        &:last-of-type {
                            border-bottom: 0;
                            padding-bottom: 1.5rem;
                        }
                    }

                    .p-dialog-footer {
                        border-top: 0;
                        padding: 1.5rem 2.5rem;

                        @include below-sm() {
                            padding: 1rem 1.5rem;
                        }
                    }
                }


                &.theme-modal-header-close {
                    overflow: auto;
                    &.p-dialog-maximized {
                        .p-dialog-header {
                            padding-right: 3.25rem;

                            .p-dialog-header-icons {
                                left: auto;
                                right: 0;
                                top: 6px;

                                .p-dialog-header-icon {
                                    border-radius: 6px 0 0 6px;

                                    &.p-dialog-header-maximize {
                                        @include above-xl() {
                                            display: flex;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    .p-dialog-header {
                        border-bottom-color: $info;
                        padding-right: 3.25rem;
                        // @include below-xl() {
                        //     padding-right: 3.25rem;
                        // }

                        .p-dialog-header-icons {
                            position: absolute;
                            // top: 28px;
                            // left: 100%;
                            left: auto;
                            right: 0;
                            top: 6px;
                            flex-direction: column-reverse;
                            gap: 0.25rem;

                            // @include below-xl() {
                            //     left: auto;
                            //     right: 0;
                            //     top: 6px;
                            // }

                            .p-dialog-header-icon {
                                width: 2.5rem;
                                height: 2.5rem;
                                margin: 0;
                                // border-radius: 0 6px 6px 0;
                                border-radius: 6px 0 0 6px;
                                color: $white;
                                background-color: $primary;

                                // @include below-xl() {
                                //     border-radius: 6px 0 0 6px;
                                // }

                                &:enabled {
                                    &:hover {
                                        background-color: rgba($primary, .9);
                                    }
                                }

                                &:focus {
                                    box-shadow: 0 0 0 0.2rem rgba($primary, .25);
                                }

                                &.p-dialog-header-close {
                                    background-color: $danger;

                                    &:enabled {
                                        &:hover {
                                            background-color: rgba($danger, .9);
                                        }
                                    }

                                    &:focus {
                                        box-shadow: 0 0 0 0.2rem rgba($danger, .25);
                                    }
                                }



                                .p-icon {
                                    width: 1.25rem;
                                    height: 1.25rem;
                                }
                            }
                        }
                    }

                    .p-dialog-content {
                        overflow: visible;
                        &:last-of-type {
                            border-bottom-color: $info;
                        }
                    }

                    &.theme-preview-modal {
                        >.p-dialog-header {
                            padding-bottom: 0;
                            padding-top: 0;
                            border: 0;
                            z-index: 1;
                        }

                        .p-dialog-content {
                            padding-top: 0;
                            .p-dialog-header {
                                margin-left: -1.75rem;
                                margin-right: -1.75rem;
                                // position: sticky;
                                // top: 0;
                                // left: 0;
                                margin-bottom: 1.5rem;
                            }
                        }
                    }
                }

                &.p-confirm-dialog {
                    overflow: hidden;

                    .p-dialog-header {
                        border-bottom: 0;
                        padding-top: 1.75rem;
                        justify-content: center;
                    }

                    .p-dialog-content {
                        padding: 1.5rem 1.75rem;
                        justify-content: center;

                        .p-confirm-dialog-message {
                            margin: 0;
                            font-weight: 500;
                        }
                    }

                    .p-dialog-footer {
                        border-top: 0;
                        padding-bottom: 2rem;
                        text-align: center;
                    }
                }

            }
        }

        //Date Picker 
        .p-calendar {
            &.p-calendar-w-btn {
                .p-inputtext {
                    padding-right: 4.25rem;
                    border-top-right-radius: $border-radius;
                    border-bottom-right-radius: $border-radius;
                }

                .p-button-icon-only {
                    background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='14.875' height='17' viewBox='0 0 14.875 17'><path d='M0,15.406A1.594,1.594,0,0,0,1.594,17H13.281a1.594,1.594,0,0,0,1.594-1.594V6.375H0ZM10.625,8.9a.4.4,0,0,1,.4-.4h1.328a.4.4,0,0,1,.4.4v1.328a.4.4,0,0,1-.4.4H11.023a.4.4,0,0,1-.4-.4Zm0,4.25a.4.4,0,0,1,.4-.4h1.328a.4.4,0,0,1,.4.4v1.328a.4.4,0,0,1-.4.4H11.023a.4.4,0,0,1-.4-.4ZM6.375,8.9a.4.4,0,0,1,.4-.4H8.1a.4.4,0,0,1,.4.4v1.328a.4.4,0,0,1-.4.4H6.773a.4.4,0,0,1-.4-.4Zm0,4.25a.4.4,0,0,1,.4-.4H8.1a.4.4,0,0,1,.4.4v1.328a.4.4,0,0,1-.4.4H6.773a.4.4,0,0,1-.4-.4ZM2.125,8.9a.4.4,0,0,1,.4-.4H3.852a.4.4,0,0,1,.4.4v1.328a.4.4,0,0,1-.4.4H2.523a.4.4,0,0,1-.4-.4Zm0,4.25a.4.4,0,0,1,.4-.4H3.852a.4.4,0,0,1,.4.4v1.328a.4.4,0,0,1-.4.4H2.523a.4.4,0,0,1-.4-.4ZM13.281,2.125H11.688V.531A.533.533,0,0,0,11.156,0H10.094a.533.533,0,0,0-.531.531V2.125H5.313V.531A.533.533,0,0,0,4.781,0H3.719a.533.533,0,0,0-.531.531V2.125H1.594A1.594,1.594,0,0,0,0,3.719V5.313H14.875V3.719A1.594,1.594,0,0,0,13.281,2.125Z' fill='%23cdcdd4'/></svg>");
                    background-repeat: no-repeat;
                    background-position: center;
                    width: 4rem;
                    padding: 0.25rem 0;
                    background-color: transparent;
                    border: 0;
                    color: $body-color;
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    z-index: 1;
                    box-shadow: none;
                    pointer-events: none;

                    .p-icon-wrapper {
                        opacity: 0;
                    }
                }
            }
        }

    }

    .form-group {
        >label {
            @include below-xl() {
                font-size: $theme-size-14;
            }
        }
    }

    //Data Table Styles
    .theme-data-table-cover {
        p-table {
            >.p-component {

                &.p-datatable {
                    &.p-datatable-striped {
                        font-size: $theme-size-14;
                        font-weight: 500;

                        .p-datatable-tbody {
                            >tr {
                                &:nth-child(even) {
                                    background: $theme-body-bg;
                                }
                            }
                        }
                    }

                    .p-datatable-table {
                        .p-datatable-thead {
                            >tr {
                                >th {
                                    background-color: $light;
                                    padding: 0.5rem;
                                    border-width: 0;
                                    color: $body-color;
                                    box-shadow: none;
                                    line-height: 1.23;
                                    height: 50px;

                                    &:first-child {
                                        border-top-left-radius: $border-radius;
                                        padding-left: 1rem;
                                    }

                                    &:last-child {
                                        border-top-right-radius: $border-radius;
                                    }

                                    &.p-sortable-column {

                                        &.p-highlight {
                                            .p-element {
                                                .p-icon-wrapper {
                                                    .p-sortable-column-icon {
                                                        color: $body-color;
                                                        opacity: 1;
                                                    }
                                                }
                                            }
                                        }

                                        &:hover {
                                            .p-element {
                                                .p-icon-wrapper {
                                                    .p-sortable-column-icon {
                                                        opacity: 1;
                                                    }
                                                }
                                            }
                                        }

                                        .p-element {
                                            .p-icon-wrapper {
                                                .p-sortable-column-icon {
                                                    opacity: 0;
                                                    width: .75rem;
                                                    height: .75rem;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        .p-datatable-tbody {
                            >tr {
                                color: $body-color;

                                >td {
                                    border-width: 0;
                                    padding: 0.5rem;
                                    height: 50px;

                                    &:first-child {
                                        padding-left: 1rem;
                                    }
                                }

                                &:last-child {
                                    >td {
                                        &:first-child {
                                            border-bottom-left-radius: $border-radius;
                                        }
                                        &:last-child {
                                            border-bottom-right-radius: $border-radius;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    .p-paginator {
                        padding: 0.875rem 1.25rem 0;
                        justify-content: flex-end;
                        border-width: 0;

                        @include below-sm() {
                            justify-content: center;
                        }

                        &::before {
                            // content: "Rows per page:";
                            font-size: $theme-size-14;
                            font-weight: 500;
                            color: $body-color;
                            order: -1;
                            margin-right: .25rem;

                            @include below-sm() {
                                display: none;
                            }
                        }

                        .p-paginator-current {
                            color: $body-color;
                            font-size: $theme-size-14;
                            font-weight: 500;
                            height: 2rem;
                            cursor: auto;
                        }

                        .p-paginator-first,
                        .p-paginator-prev,
                        .p-paginator-next,
                        .p-paginator-last {
                            min-width: 2rem;
                            height: 2rem;
                            color: $body-color;
                        }

                        .p-paginator-pages,
                        .p-paginator-first,
                        .p-paginator-last {
                            display: none;
                        }

                        p-dropdown {
                            order: -1;

                            .p-component {
                                &.p-dropdown-panel {
                                    font-size: $theme-size-14;
                                }
                            }

                            .p-dropdown {
                                margin: 0;
                                height: 2rem;
                                align-items: center;
                                border: 0;
                                padding-left: .5rem;

                                .p-dropdown-label {
                                    padding: 0;
                                    font-size: $theme-size-14;
                                    font-weight: 500;
                                }

                                .p-dropdown-trigger {
                                    color: $body-color;
                                    position: relative;
                                    width: 2rem;

                                    &::after {
                                        content: "";
                                        position: absolute;
                                        top: 50%;
                                        left: 50%;
                                        border-top: 5px solid $body-color;
                                        border-left: 5px solid transparent;
                                        border-right: 5px solid transparent;
                                        transform: translate(-50%, -50%);
                                    }

                                    .p-icon-wrapper {
                                        display: none;
                                    }
                                }

                            }
                        }

                        .p-disabled {
                            opacity: .5;
                        }
                    }
                }
            }
        }

        &.theme-table-custom-header {
            p-table {
                >.p-component {
                    &.p-datatable {
                        .p-datatable-header {
                            border-width: 0;
                            background-color: $white;
                            padding: .25rem 0 1.25rem;
                        }
                    }
                }
            }
        }
    }

    //Tooltip
    .tooltipDottedList {
        li {
            position: relative;
            padding-left: 1rem;
            margin-bottom: 0.25rem;
            line-height: normal;

            &::before {
                content: "";
                width: 6px;
                height: 6px;
                background-color: $white;
                border-radius: 50%;
                position: absolute;
                left: 0;
                top: 6px;
            }
        }
    }

    //Router CSS
    .theme-router-inner {
        >*:not(router-outlet) {
            height: 100%;
        }
    }

    .theme-search-box {
        width: 300px;

        @include below-sm() {
            width: 100%;
        }
    }

    // Flip CSS
    .theme-flip {
        -webkit-transform: scaleX(-1);
        transform: scaleX(-1);
    }

    //Gradient Secondary to Info
    .theme-gradient-secondary-info {
        background: $secondary;
        background: -moz-linear-gradient(-45deg, $secondary 0%, $info 100%);
        background: -webkit-linear-gradient(-45deg, $secondary 0%, $info 100%);
        background: linear-gradient(135deg, $secondary 0%, $info 100%);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='$secondary', endColorstr='$info', GradientType=1);
    }

    //CSM Pages
    .theme-cms-content {
        .theme-cms-bg-1 {
            top: 70px;
            max-height: 235px;
            @include below-xl() {
                top: $theme-mobile-header-height;
                max-width: 20%;
            }

            @include below-md() {
                max-width: 25%;
            }
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            font-weight: normal;
        }

        h2,
        h3,
        h4,
        h5,
        h6 {
            padding-top: 1.5rem;
        }

        p {
            margin-bottom: 1.25rem;
        }

        a {
            text-decoration: none;
            font-weight: 600;
        }

        b,
        strong {
            font-weight: bold;
        }

        ol,
        ul {
            padding-left: 1.25rem;
            margin-bottom: 0;

            li {
                margin-bottom: 1rem;

                &::marker {
                    font-weight: bold;
                }
            }
        }

        ul {
            list-style: none;
            padding-left: 0;

            li {
                position: relative;
                padding-left: 18px;

                &::before {
                    content: "";
                    position: absolute;
                    top: 10px;
                    left: 0;
                    width: .5rem;
                    height: .5rem;
                    background-color: $secondary;
                    border-radius: .5rem;
                }
            }
        }

        ol {

            counter-reset: section;
            list-style-type: none;

            li {

                &::before {
                    counter-increment: section;
                    content: counters(section, ".") ". ";
                }
            }
        }
    }

    .theme-container {
        @include above-1900() {
            max-width: 1900px;
        }
    }

    .theme-container-sm {
        @include above-1400() {
            max-width: 1400px;
        }
    }


}

html, body, body app-root, body app-root > *, body > * .mainContentBox, .vh-100 {
    height: 100% !important;
}
.about-sign {
    max-height: 367px;
}
.security-sign {
    max-height: 377px;
}
.secure-img {
    max-height: 175px;
}
.secure-banner {
    max-height: 62px;
}
.secure-compliance {
    max-height: 127px;
}
.hospital-sign {
    max-height: 223px;
}
.theme-svg-auto{
    svg{
        max-width: 100%;
        max-height: 100%;
    }
}
.theme-notes-dropdown{
    width: 260px;
    @include below-md(){
        width: 100%;
    }
}
.table-light{
    --bs-table-color: #{$body-color};
}
.table-layout-fixed{
    table-layout: fixed;
}
.table-flex-below-sm{
    @include below-sm(){
        tr {
            display: flex;
            flex-direction: column;
            &:not(:first-child) {
                border-top: 0;
                td {
                    border-top: 0 !important;
                    border-left: 0 !important;
                    border-right: 0 !important;
                }
            }
            &:not(:last-child) {
                border-bottom: 0;
                td {
                    border-bottom: 0 !important;
                    border-left: 0 !important;
                    border-right: 0 !important;
                }
            }
            .w-75, .w-250{
                width: 100% !important;
            }
            
        }
    }
}
.w-250{
    width: 250px;
}
.max-w-300{
    max-width: 300px;
}