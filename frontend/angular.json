{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"clinicalnotes-frontend": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/clinicalnotes-frontend", "index": "src/index.html", "main": "src/main.ts", "allowedCommonJsDependencies": ["jspdf", "quill", "crypto-js", "html2canvas", "dompurify", "rgbcolor", "core-js", "raf"], "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/primeng/resources/themes/lara-light-blue/theme.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/quill/dist/quill.core.css", "node_modules/quill/dist/quill.snow.css", "src/styles.scss"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js", "node_modules/quill/dist/quill.js", "node_modules/chart.js/dist/chart.umd.js"], "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "5mb"}]}, "stage": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stage.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "5mb"}]}, "qa": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.qa.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}]}, "development": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}]}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "clinicalnotes-frontend:build"}, "configurations": {"production": {"browserTarget": "clinicalnotes-frontend:build:production"}, "stage": {"browserTarget": "clinicalnotes-frontend:build:stage"}, "development": {"browserTarget": "clinicalnotes-frontend:build:development"}, "qa": {"browserTarget": "clinicalnotes-frontend:build:qa"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "clinicalnotes-frontend:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css"], "scripts": []}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "clinicalnotes-frontend:serve"}, "configurations": {"production": {"devServerTarget": "clinicalnotes-frontend:serve:production"}, "dev": {"devServerTarget": "clinicalnotes-frontend:serve:dev"}}}}}}, "cli": {"analytics": false}}