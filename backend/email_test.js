const nodemailer = require('nodemailer');


// ClinicalPad
// const email = '<EMAIL>';
// const password = 'xifzulcrmulrapou';

// Smartvision
// const email = '<EMAIL>';
// const password = 'yxpplbvqtwdjeoro';
const email = '<EMAIL>';
const password = 'kmllbnbpyegvyaoj';
// const password = 'dovjjhsnljitnkgc';
// const password = 'Folio3@2025*!*';

// Email ID: <EMAIL>
// App Pass: kmll bnbp yegv yaoj

// Converse Smartly
// const email = '<EMAIL>'
// const password = 'kH^#,[W17?1321'


console.log('email', email, 'password', password);
// Replace with your credentials
const transporter = nodemailer.createTransport({
  // service: 'gmail',
  host: 'smtp.gmail.com',
  port: 465,
  secure: true, // Use SSL
  auth: {
    user: email,
    pass: password, // NOT your Gmail password
  },
});

// Setup email data
const mailOptions = {
  from: email,
  to: '<EMAIL>',
  subject: 'Nodemailer Gmail Test',
  text: 'This is a test email sent using Nodemailer and Gmail SMTP!',
};

// Send email
transporter.sendMail(mailOptions, (error, info) => {
  if (error) {
    return console.error('Error:', error);
  }
  console.log('Email sent:', info.response);
});
