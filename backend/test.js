const axios = require('axios');

function redactText(originalText, redactionPositions) {
  console.log(redactionPositions)
  // let redactedText = '';
  let redactedText = originalText;

  // Sort redaction positions by start position in descending order
  redactionPositions.sort((a, b) => b.start - a.start);

  let currentPosition = originalText.length;

  // Iterate over each redaction position
  redactionPositions.forEach(({start, end, entity_type}) => {
    // Add non-redacted text between the current position and the redaction start position
    // redactedText = originalText.slice(position.start, currentPosition) + redactedText;
    // // Add redacted text (e.g., [REDACTED]) for the redaction range
    // // redactedText = '[REDACTED]' + redactedText.slice(position.end);
    // redactedText = `[${position.entity_type}]` + redactedText.slice(position.end);
    // // Update current position for the next iteration
    // currentPosition = position.start;
    redactedText = redactedText.slice(0, start) + `[${entity_type}]` + redactedText.slice(end);
  });

  // Add remaining non-redacted text
  redactedText = originalText.slice(0, currentPosition) + redactedText;

  return redactedText;
}

// const originalText = "Hello My name is Hassan Khalid. My phone number is 03205757964";
const originalText = `
Gender & Age: 
Male & 30 years

Presenting Complaint & Duration: 
Jane Stone has a Kidney issue    

Patient name: Jane Stone

Doing really with shoulder – can start doing light weights and build up fully over next 2 /12, no problems

Ongoing sx both knees right worse than left
Keen on surgery (had 1 injection in right)

Wants to do iron man type event in end of Nov - and have surgery after
? right first end of Dec and left end of jan [Hospital] - he will get back to me`;
// const originalText = `Patient name: Jane Stone. Doing really with shoulder, can start doing light weights and build up fully over next 2 /12, no problems Ongoing sx both knees right worse than left Keen on surgery (had 1 injection in right) Wants to do iron man type event in end of Nov - and have surgery after? right first end of Dec and left end of jan [Hospital] - he will get back to me`;
// const originalText = `Ashley Jonas
// 20/01/1994
//
// Feels dizzy ? vertigo mild like on a ship, possibly slighty light headed, worse last few days, denies any abnormal neurolohgy slurred speech no previous TIA
// ,gets worse on moving head lasts seconds+ hearing nad tinnitus nad, N+V nad
// No Abnormal neurology weakness or paralysis
// No Visual symptoms
// No Facial weakness, loc
// Ear symptoms
// No Chest pain
// O.a t= 37 p=74 sat98= bp, 126/78 no postural
// ENT -nad
// Chest clear hs normal carotids notmal
// CN/ PNS intact
// Imp BPV
// Management – trial of stermetil
// Advised take it easy
// If worsening any abnormal neurology or unwell or feels she may collapse to be reviwed in ED urgently `;
const REDACT_URL = "http://localhost:5500/analyze"

axios.post(REDACT_URL,{
  text: originalText,
  language: "en"
}).then(res => {
  const redactedText = redactText(originalText, res.data);
  console.log(redactedText);
})



// ---------------------------------------------------------------------------------------------------------------------------------------------------------------


// const winston = require('winston');
// const	WinstonCloudWatch = require('winston-cloudwatch');
// require('dotenv').config()
//
// const userActivityLogger = new winston.createLogger({
//   format: winston.format.json(),
//   defaultMeta: { service: 'user-activity-log' },
//   transports: [
//     new (winston.transports.Console)({
//       timestamp: true,
//       colorize: true,
//     })
//   ]
// });
//
//
// const userActivityLoggerConfig = {
//   logGroupName:"data-access-log",
//   logStreamName: `data-access-log-${process.env.NODE_ENV}`,
//   awsAccessKeyId: process.env.CLOUDWATCH_ACCESS_KEY,
//   awsSecretKey: process.env.CLOUDWATCH_SECRET_ACCESS_KEY,
//   awsRegion: process.env.CLOUDWATCH_REGION,
//   messageFormatter: ({message, additionalInfo }) => `[info] : ${message} \nAdditional Info: ${JSON.stringify(additionalInfo)}`
//
// }
// console.log(userActivityLoggerConfig)
// userActivityLogger.add(new WinstonCloudWatch(userActivityLoggerConfig));
//
// userActivityLogger.info('Clinic Register sucessfully');

// const winston = require('winston');
// const WinstonCloudWatch = require('winston-cloudwatch');
//
// // Configure Winston with CloudWatch transport
// const logger = winston.createLogger({
//   level: 'info',
//   transports: [
//     new WinstonCloudWatch({
//       errorHandler(e){
//         console.log(e)
//       },
//       level: 'info',
//       silent: false,
//       logGroupName: 'data-access-loga', // Specify the log group name
//       logStreamName: 'data-access-log-staging', // Specify the log stream name
//       awsRegion: 'us-west-2', // Specify the AWS region
//       // awsAccessKeyId: '********************', // Specify your AWS access key ID
//       // awsSecretKey: '1JaRmAiARC0ShNmPp/V3501PTYyHFSoaHpB5U/te', // Specify your AWS secret access key
//       awsOptions: {
//         credentials: {
//           // accessKeyId: "********************",
//           // secretAccessKey: "1JaRmAiARC0ShNmPp/V3501PTYyHFSoaHpB5U/te",
//           accessKeyId: "********************",
//           secretAccessKey: "dcSbXDvX+jiR0GtA7RgTkcFnd2DO8vLtQUHONkN0",
//         },
//         // region: "us-west-2",
//       }
//     }),
//     new winston.transports.Console(),
//     // new winston.transports.CloudWatch({
//     //   logGroupName: 'data-access-log', // Specify the log group name
//     //   logStreamName: 'data-access-log-staging', // Specify the log stream name
//     //   awsRegion: 'us-west-2', // Specify the AWS region
//     //   awsAccessKeyId: '********************', // Specify your AWS access key ID
//     //   awsSecretKey: '1JaRmAiARC0ShNmPp/V3501PTYyHFSoaHpB5U/te' // Specify your AWS secret access key
//     // })
//   ]
// });
//
// // Example usage
// logger.info('This is an informational message.');
// logger.info('This is an informational message.2222');
// winston.error(1)
// // logger.error('This is an error message.');

