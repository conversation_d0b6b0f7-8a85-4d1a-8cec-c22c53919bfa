#!/bin/bash

set -eo pipefail
COMMAND="$@"

### below command will replace the variables in env.php
sed -i \
-e 's/'DB-HOST-HERE'/'$DB_HOST_STRING'/' \
-e 's/'DB-USERNAME-HERE'/'$DB_USER_STRING'/' \
-e 's/'DB-PASSWORD-HERE'/'$DB_PASSWORD_STRING'/' \
-e 's/'DB-DATABASE-HERE'/'$DB_NAME_STRING'/' \
-e 's/'DB-PORT-HERE'/'$DB_PORT_STRING'/' \
-e 's/'DB-TYPE-HERE'/'$DB_TYPE_STRING'/' \
-e 's/'DB-URL-HERE'/'$DB_URL_STRING'/' \
-e 's/'API-URL-HERE'/'$API_URL_STRING'/' \
-e 's/'FRONTEND-URL-HERE'/'$FRONT_END_URL_STRING'/' \
-e 's/'SMTP-HOST-HERE'/'$SMTP_HOST_STRING'/' \
-e 's/'SMTP-PORT-HERE'/'$SMTP_PORT_STRING'/' \
-e 's/'SMTP-USER-HERE'/'$SMTP_USER_STRING'/' \
-e 's/'SMTP-PASS-HERE'/'$SMTP_PASS_STRING'/' \
-e 's/'MAIL-FROM-ADDRESS-HERE'/'$MAIL_FROM_ADDRESS_STRING'/' \
-e 's/'S3-BUCKET-NAME-HERE'/'$S3_BUCKET_NAME_STRING'/' \
-e 's/'S3-BUCKET-URL-HERE'/'$S3_BUCKET_URL_STRING'/' \
-e 's/'S3-REGION-HERE'/'$S3_REGION_STRING'/' \
-e 's/'JWT-TOKEN-SECRET-HERE'/'$JWT_TOKEN_SECRET_STRING'/' \
-e 's/'AES-KEY-HERE'/'$AES_KEY_STRING'/' \
-e 's/'NOTE-GENRATE-URL-HERE'/'$NOTE_GENRATE_URL_STRING'/' \
-e 's/'ADMIN-PANEL-URL-HERE'/'$ADMIN_PANEL_URL_STRING'/' \
-e 's/'ENC-CRYPTO-KEY-HERE'/'$ENC_CRYPTO_KEY_STRING'/' \
-e 's/'CLOUDWATCH-GROUP-NAME-HERE'/'$CLOUDWATCH_GROUP_NAME_STRING'/' \
-e 's/'CLOUDWATCH-DATA-ACCESS-GROUP-NAME-HERE'/'$CLOUDWATCH_DATA_ACCESS_GROUP_NAME_STRING'/' \
-e 's/'CLOUDWATCH-SECURITY-GROUP-NAME-HERE'/'$CLOUDWATCH_SECURITY_GROUP_NAME_STRING'/' \
-e 's/'CLOUDWATCH-AUDIT-GROUP-NAME-HERE'/'$CLOUDWATCH_AUDIT_GROUP_NAME_STRING'/' \
-e 's/'CLOUDWATCH-GENRAL-GROUP-NAME-HERE'/'$CLOUDWATCH_GENRAL_GROUP_NAME_STRING'/' \
-e 's/'MAX-GENERATE-LETTER-COUNT-HERE'/'$MAX_GENERATE_LETTER_COUNT_STRING'/' \
-e 's/'CLOUDWATCH-REGION-HERE'/'$CLOUDWATCH_REGION_STRING'/' \
-e 's/'S3-CMS-BUCKET-NAME-HERE'/'$AES_KEY_STRING'/' \
-e 's/'S3-CMS-REGION-HERE'/'$S3_CMS_REGION_STRING'/' \
-e 's/'CRON-ACCESS-KEY-HERE'/'$CRON_ACCESS_KEY_STRING'/' \
-e 's/'STRIPE-SECRET-KEY-HERE'/'$STRIPE_SECRET_KEY_STRING'/' \
-e 's/'STRIPE-WEBHOOK-SECRET-HERE'/'$STRIPE_WEBHOOK_SECRET_STRING'/' \
-e 's/'REDACT_URL_STRING'/'$REDACT_URL_STRING'/' \

