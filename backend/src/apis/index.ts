import * as express from 'express'
import _ from 'lodash'
import fs from 'fs'
import path from 'path'
import stripeClient from '../services/Stripe'
import { sendMail } from '../services/Mail'
import Models from "../database/models";

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;


const UtilsHelper = require('../helpers/utils.helper')

exports.mount = (app, options) => {
  let _isAppMounted = false
  let _isAdminMounted = false
  if (fs.existsSync(path.resolve(__dirname, './app'))) {
    const appApis = require('./app')

    // App apis
    appApis.mount(app, options)

    _isAppMounted = true

    console.info('App - APIS mounted successfully')
  }

  if (fs.existsSync(path.resolve(__dirname, './admin'))) {
		const adminApis = require("./admin");

		// Admin apis
		adminApis.mount(app, options);

		_isAdminMounted = true

		console.info("Admin - APIS mounted successfully")
	}

  let _msg = 'Nerosense APIs running...'

  if (_isAppMounted && _isAdminMounted) _msg = 'Nerosense APIs running...'
  else if (_isAppMounted) _msg = 'Nerosense App APIs running...'
  else if (_isAdminMounted) _msg = 'Nerosense Admin APIs running...'

  // app.post('/webhook', express.raw({ type: 'application/json' }), async(request, response) => {
  //   const sig = request.headers['stripe-signature'];
  //
  //
  //   let event;
  //
  //   try {
  //     event = stripeClient.webhooks.constructEvent(request.body, sig, endpointSecret);
  //   } catch (err) {
  //     response.status(400).send(`Webhook Error: ${err.message}`);
  //     return;
  //   }
  //
  //   // Handle the event
  //   switch (event.type) {
  //     case 'customer.subscription.created':
  //       //let customerSubscriptionCreated = event.data.object;
  //       //console.log('customerSubscriptionCreated', customerSubscriptionCreated);
  //       // Then define and call a function to handle the event customer.subscription.created
  //       break;
  //     case 'customer.subscription.deleted': {
  //       // const customerSubscriptionDeleted = event.data.object;
  //       // Then define and call a function to handle the event customer.subscription.deleted
  //       const stripeSubscription = event.data.object;
  //
  //       // Locate the local subscription record by matching the Stripe subscription ID.
  //       const localSub = await Models.ClinicalUserSubscriptions.findOne({
  //         where: {subscription_id: stripeSubscription.id}
  //       });
  //
  //       if (localSub) {
  //         // Update the local record to indicate cancellation and switch to the starter plan (id = 1).
  //         await localSub.update({
  //           plan_id: 1,
  //           // Set a new status to indicate the subscription has been cancelled or moved.
  //           status: 2,
  //         });
  //
  //         // Optionally update the user's main record.
  //         const user = await Models.ClinicalUsers.findByPk(localSub.user_id);
  //         if (user) {
  //           await user.update({
  //             subscription_plan_id: 1,
  //             // Update user limits according to the starter plan settings if necessary.
  //             // For example: remaining_custom_templates, remaining_audio_consultation_minutes, etc.
  //           });
  //         }
  //       }
  //       break;
  //     }
  //     case 'customer.subscription.paused':
  //      // const customerSubscriptionPaused = event.data.object;
  //       // Then define and call a function to handle the event customer.subscription.paused
  //       break;
  //     case 'customer.subscription.resumed':
  //       //const customerSubscriptionResumed = event.data.object;
  //       // Then define and call a function to handle the event customer.subscription.resumed
  //       break;
  //     case 'customer.subscription.updated':
  //       //const customerSubscriptionUpdated = event.data.object;
  //       // Then define and call a function to handle the event customer.subscription.updated
  //       break;
  //     case 'invoice.finalized':
  //      // const invoiceFinalized = event.data.object;
  //       // Then define and call a function to handle the event invoice.finalized
  //       break;
  //     case 'invoice.paid':
  //       //const invoicePaid = event.data.object;
  //       // Then define and call a function to handle the event invoice.paid
  //       break;
  //     case 'invoice.payment_failed':
  //       //const invoicePaymentFailed = event.data.object;
  //       // Then define and call a function to handle the event invoice.payment_failed
  //       break;
  //     case 'payment_intent.created':
  //       //const paymentIntentCreated = event.data.object;
  //       // Then define and call a function to handle the event payment_intent.created
  //       break;
  //     case 'payment_intent.payment_failed':
  //      // const paymentIntentPaymentFailed = event.data.object;
  //       // Then define and call a function to handle the event payment_intent.payment_failed
  //       break;
  //     case 'payment_intent.succeeded':
  //       //const paymentIntentSucceeded = event.data.object;
  //       // Then define and call a function to handle the event payment_intent.succeeded
  //       break;
  //     // ... handle other event types
  //     default:
  //       console.log(`Unhandled event type ${event.type}`);
  //
  //       await sendMail('<EMAIL>', 'stripe webhook email', event, event, '')
  //   }
  //
  //   // Return a 200 response to acknowledge receipt of the event
  //   response.send();
  // });

  // should be at the end
  app.all('/*', function (req, res, next) {
    const _responseObj = UtilsHelper.responseObject()

    if (req.url === '/' || req.url === '/health') {
      _.assign(_responseObj, { msg: _msg })
    } else {
      _.assign(_responseObj, { status: 'error', statusCode: 404, msg: 'Route not found.' })
    }

    return UtilsHelper.cRes(res, _responseObj)
  })
}
