import Joi, {string} from 'joi'
import express from 'express'
import _ from 'lodash'
import * as UtilsHelper from '../../../../helpers/utils.helper'

const Validators: any = {
	getMedicalHistoryValid: Joi.object({
		clinic_id: Joi.string().required(),
		patient_id: Joi.number().integer(),
		// TODO: add fields for sorting and pagination
	}),
	updateMedicalHistoryValid: Joi.object({
		clinic_id: Joi.string().required(),
		patient_id: Joi.number().integer(),
		id: Joi.number().integer(),
		content: Joi.object().required(),
		// TODO: add fields for sorting and pagination
	}),
	createMedicalHistoryValid: Joi.object({
		clinic_id: Joi.string().required(),
		patient_id: Joi.number().integer(),
		content: Joi.object().required(),
	}),
}

export default function Validator(func: string) {
	return async function Validator(
		req: express.Request,
		res: express.Response,
		next: express.NextFunction
	) {
		try {
			const _reqBody = (req.body?.encrypet) ? UtilsHelper.cryptoJsObjectDcrypt(req.body.encrypet) : req.body;
			const _reqData = _.assign(_reqBody, req.params, req.query);
			const validated = await Validators[func].validateAsync(_reqData, { abortEarly: false })
			req.body = validated
			next()
		} catch (err: any) {
			const _er: any = {}
			if (err.isJoi) {
				err.details.forEach((d: any) => {
					const _key: string = d.context.key
					_er[_key] = d.message
				})
			}

			const _retData = UtilsHelper.responseObject()
			_retData.status = 'error'
			_retData.statusCode = 400
			_retData.msg = UtilsHelper.getMessage('50020', 'en')
			_retData.msgCode = '50020'
			_retData.data = _er

			return UtilsHelper.cRes(res, _retData)
		}
	}
}
