import * as express from 'express'
import { S3Client, GetObjectCommand, S3ClientConfig } from '@aws-sdk/client-s3';
import _ from 'lodash'

import * as UtilsHelper from '../../../../helpers/utils.helper'
import { IResponseObject } from '../../../../helpers/utils.interface'
import BaseController from '../../../../helpers/BaseController'
import Models from '../../../../database/models'
import { userActivityLogger, securityLogger, dataAccessLogger, auditLogger } from '../../../../helpers/logger.helpers';

//import * as patientHelper from './helper'
const s3Configuration: S3ClientConfig = {
	credentials: {
		accessKeyId: process.env.S3_ACCESS_KEY,
		secretAccessKey: process.env.S3_ACCESS_SECRET
	},
	region: process.env.S3_REGION,
};
const s3 = new S3Client(s3Configuration);
class MedicalHistoryController extends BaseController {
	constructor() {
		super()
		this.getMedicalHistory = this.getMedicalHistory.bind(this);
		this.updateMedicalHistory = this.updateMedicalHistory.bind(this);
		this.createMedicalHistory = this.createMedicalHistory.bind(this);
	}

	public async createMedicalHistory(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const _reqBody = req.body;
			const loggedInUserId = req?.user?.id
			const patient_id = _reqBody.patient_id;
			const medical_history_content = _reqBody.content;

			const _userData = await Models.ClinicalUserRoleDetail.findOne({
				where: {
					user_id: `${loggedInUserId}`,
				},
			});

			if (!_userData || (_userData.clinic_id!== _reqBody.clinic_id)) {
				_.assign(_retData, {
					statusCode: 400,
					status: 'error',
					msg: UtilsHelper.getMessage(50047, 'en'),
				})
				return this.sendResponse(res, _retData)
			}
			// create medical history
			const medicalHistory = await Models.PatientMedicalHistory.create({
				patient_id: patient_id,
				content: medical_history_content,
				user_id: loggedInUserId,
				created_at: new Date(),
			},
				{ user: { id: loggedInUserId } });
			// logger
			userActivityLogger.info(UtilsHelper.getMessage(10116), { additionalInfo: { user_id: loggedInUserId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, medical_history_id: medicalHistory.id, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });
			_.assign(_retData, {
				msgCode: 10116,
				data: UtilsHelper.cryptoJsObjectEncrypt({
					list: [{...medicalHistory.toJSON()}],
					totalCount: 1,
				}),
				msg: UtilsHelper.getMessage(10116),
			})
			this.sendResponse(res, _retData)
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})
			this.logErrors(err, 'Error in ClinicalNoteController.createNote')
			this.sendResponse(res, _retData)
		}
	}

	public async updateMedicalHistory(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const _reqBody = req.body;
			const loggedInUserId = req?.user?.id
			const patient_id = _reqBody.patient_id;
			const medical_history_id = _reqBody.id;

			// Get user data to check if he is in this clinic
			const _userData = await Models.ClinicalUserRoleDetail.findOne({
				where: {
					user_id: `${loggedInUserId}`,
				},
			});

			if (!_userData || (_userData.clinic_id !== _reqBody.clinic_id)) {
				_.assign(_retData, {
					statusCode: 400,
					status: 'error',
					msg: UtilsHelper.getMessage(50047, 'en'),
				})

				return this.sendResponse(res, _retData)
			}

			// update medical history
			const medicalHistory = await Models.PatientMedicalHistory.update({
				content: _reqBody.content,
			},{
				where: {
					// patient_id: patient_id,
					id: medical_history_id,
				},
				individualHooks: true,
				user: { id: loggedInUserId }
			});

			console.log(medicalHistory)

			_.assign(_retData, {
				statusCode: 200,
				status: 'success',
				data: UtilsHelper.cryptoJsObjectEncrypt({
					list: medicalHistory,
				}),
			})
			this.sendResponse(res, _retData)
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})
			this.logErrors(err, 'Error in ClinicalNoteController.createNote')
			this.sendResponse(res, _retData)
		}
	}

	public async getMedicalHistory(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const _reqBody = req.body;
			const loggedInUserId = req?.user?.id
			const patient_id = _reqBody.patient_id;

			// Get user data to check if he is in this clinic
			const _userData = await Models.ClinicalUserRoleDetail.findOne({
				where: {
					user_id: `${loggedInUserId}`,
				},
			});

			if (!_userData || (_userData.clinic_id !== _reqBody.clinic_id)) {
				_.assign(_retData, {
					statusCode: 400,
					status: 'error',
					msg: UtilsHelper.getMessage(50047, 'en'),
				})

				return this.sendResponse(res, _retData)
			}

			// get medical history
			const { rows: medicalHistory, count: totalCount } = await Models.PatientMedicalHistory.findAndCountAll({
				where: {
					patient_id: patient_id,
				},
				order: [['created_at', 'DESC']], // Order by created_at column in descending order
				include: [
					{
						model: Models.ClinicalUsers,
						as: 'users',
						attributes: ['id', 'first_name', 'last_name', 'email', 'phone', 'country', ['county_id', 'state'], ['town_id', 'city'], 'address'],
					},
				]
			});

			if (medicalHistory) {
				//logger
				userActivityLogger.info(UtilsHelper.getMessage(10116), { additionalInfo: { user_id: loggedInUserId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, medical_history_id: medicalHistory.id, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

				_.assign(_retData, {
					msgCode: 10116,
					data: UtilsHelper.cryptoJsObjectEncrypt({
						list: medicalHistory,
						totalCount: totalCount
					}),
					msg: UtilsHelper.getMessage(10116),
				});

			}
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in ClinicalNoteController.createNote')
		}

		return this.sendResponse(res, _retData)
	}

}

const medicalHistoryController = new MedicalHistoryController()

export default medicalHistoryController
