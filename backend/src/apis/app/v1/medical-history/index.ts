const express = require('express')
const Router = express.Router()
import medicalHistoryController from './MedicalHistoryController'
import Validator from './validators'

const _router = function (options: any) {

	// get patient's medical history
	Router.post('/:patient_id', options.Authenticate, Validator('getMedicalHistoryValid'), medicalHistoryController.getMedicalHistory);

	// update patient's medical history
	Router.post('/edit/:patient_id/:id', options.Authenticate, <PERSON><PERSON><PERSON>('updateMedicalHistoryValid'), medicalHistoryController.updateMedicalHistory);

	// Create a new medical history entry
	Router.post('/create/:patient_id', options.Authenti<PERSON>, <PERSON><PERSON><PERSON>('createMedicalHistoryValid'), medicalHistoryController.createMedicalHistory);

	return Router
}

export default _router
