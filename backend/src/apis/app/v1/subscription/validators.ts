import Joi from 'joi'
import express from 'express'
import _ from 'lodash'
import * as UtilsHelper from '../../../../helpers/utils.helper'

const billingAddressInfoSchema = Joi.object({
  first_name: Joi.string().required(),
  last_name: Joi.string().required(),
  email: Joi.string().trim().email().required(),
  phone: Joi.number().optional(),
  address: Joi.string().optional(),
  country: Joi.string().optional(),
  state: Joi.string().optional(),
  town: Joi.string().optional(),
  zip_code: Joi.string().optional(),
});
const Validators: any = {
  createCheckoutSessionValid: Joi.object({
    plan_id: Joi.number().required(),
    plan_type: Joi.string().required().valid('Monthly', 'Yearly'),
    success_url: Joi.string().required(),
    cancel_url: Joi.string().required(),
    action: Joi.string().required().valid('subscribe', 'upgrade', 'downgrade', 'cancel'),
    billing_address_info: billingAddressInfoSchema.required(),
    stripe_price_id: Joi.string(),
    coupon: Joi.string().optional(),
  }),

  getPlanListValid: Joi.object({
    currency: Joi.string().required().valid('USD', 'GBP', 'EUR'),
    plan_type: Joi.string().required().valid('Monthly', 'Yearly'),
    clinic_id: Joi.string(),
  }),

  planDetailValid: Joi.object({
    subscriptionPlanId: Joi.number().positive().required(),
  }),

  createCustomerValid: Joi.object({
    payment_method_id: Joi.string().required(),
    billing_address_info: billingAddressInfoSchema.required(),
  }),

  createSubscriptionValid: Joi.object({
    customer_id: Joi.string().required(),
    payment_method_id: Joi.string().required(),
    plan_id: Joi.number().required(),
    billing_address_info: billingAddressInfoSchema.required(),
  }),

  cancellSubscriptionValid: Joi.object({
    // subscription_id: Joi.string().required(),
    cancellation_reason: Joi.string().required(),
  }),

  updateSubscriptionValid: Joi.object({
    plan_id: Joi.number().required(),
  }),

  detailUserSubscriptionValid: Joi.object({
    subscriptionId: Joi.number().required(),
  }),

  detailUserInvoiceValid: Joi.object({
    invoiceId: Joi.number().required(),
  }),

  confirmUserSubscriptionValid: Joi.object({
    // subscription_id: Joi.string().required(),
    session_id: Joi.string().required(),
  }),

}

export default function Validator(func: string) {
  return async function Validator(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const _reqData = _.assign(req.body, req.params, req.query)
      const validated = await Validators[func].validateAsync(_reqData, { abortEarly: false })
      req.body = validated
      next()
    } catch (err: any) {
      const _er: any = {}
      if (err.isJoi) {
        err.details.forEach((d: any) => {
          const _key: string = d.context.key
          _er[_key] = d.message
        })
      }

      const _retData = UtilsHelper.responseObject()
      _retData.status = 'error'
      _retData.statusCode = 400
      _retData.msg = UtilsHelper.getMessage('50020', 'en')
      _retData.msgCode = '50020'
      _retData.data = _er

      return UtilsHelper.cRes(res, _retData)
    }
  }
}
