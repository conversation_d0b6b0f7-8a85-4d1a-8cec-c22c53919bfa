import * as express from 'express'
import * as UtilsHelper from '../../../../helpers/utils.helper'
import { IResponseObject } from '../../../../helpers/utils.interface'
import BaseController from '../../../../helpers/BaseController'
import _ from 'lodash'
import Models from '../../../../database/models'
import { Op, Sequelize } from 'sequelize'
import * as TokenHelper from '../../../../helpers/token.helper'
import * as PasswordHelper from '../../../../helpers/password.helper'
import * as authHelper from './helper'
import { sendMail } from "../../../../services/Mail";
import { v4 as uuidv4 } from 'uuid'
import { userActivityLogger, securityLogger, dataAccessLogger, auditLogger } from '../../../../helpers/logger.helpers';
const Moment = require("moment");
const fs = require('fs');
const Ejs = require("ejs");
import dayjs from 'dayjs';


class AuthController extends BaseController {
  constructor() {
    super()

    this.registerClinic = this.registerClinic.bind(this)
    this.register = this.register.bind(this)
    this.varifyUser = this.varifyUser.bind(this)
    this.login = this.login.bind(this)
    this.logout = this.logout.bind(this)
    this.forgotPassword = this.forgotPassword.bind(this)
    this.resetPassword = this.resetPassword.bind(this)
    this.checkEmail = this.checkEmail.bind(this)
    this.sendOtp = this.sendOtp.bind(this)
    this.verifyOtp = this.verifyOtp.bind(this)
    this.verifyOTPfor2FA = this.verifyOTPfor2FA.bind(this)
    this.refreshToken = this.refreshToken.bind(this)
  }

  /*
   * Method to register Clinic
   */

  public async registerClinic(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {
      const reqBody: any = req.body

      reqBody.name = reqBody.name.toUpperCase()

      // check gorup already exist then group name 2
        const _filter: object = {
         name: reqBody.name,
         status: { [Op.ne]: 9 },
       }

      // check Clinic name  already exist or not in the Clinic collection
        const hasName = await Models.Clinics.findAll({ where: _filter })
       if (hasName.length > 0) {
         _.assign(_retData, {
           status: 'error',
           statusCode: 400,
           msgCode: '50004',
           msg: UtilsHelper.getMessage('50004', 'en'),
         })
         return this.sendResponse(res, _retData)
       }

      reqBody.clinic_id = uuidv4()
      reqBody.status = 0

      const clinicInfo: any = await Models.Clinics.create(reqBody)

      if (clinicInfo) {
        authHelper.addUserDefaultRolePermission(clinicInfo.clinic_id, null)
      }

      //logger
      userActivityLogger.info('Clinic Register sucessfully', { additionalInfo: { clinic_id: clinicInfo.clinic_id, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

      _.assign(_retData, {
        msgCode: '10004',
        msg: UtilsHelper.getMessage('10004', 'en'),
        data: UtilsHelper.cryptoJsObjectEncrypt(clinicInfo),
      })
    } catch (err: any) {

      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in AuthController.registerClinic')
    }

    return this.sendResponse(res, _retData)
  }

  /*
   * Method to register user
   */

  public async register(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {
      const reqBody = req.body;
      let clinicName = "";
      if (!reqBody.clinic_id.length) {
        reqBody.clinic_id = uuidv4()
        const individualClinic = await authHelper.addClinic(reqBody);
        reqBody.clinic_id = individualClinic.clinic_id;
      }

      if (reqBody.clinic_id) {
        const checkClinicExist = await Models.Clinics.findOne({
          where: { clinic_id: reqBody.clinic_id, status: { [Op.ne]: 9 } },
        });

        if (reqBody.user_type == 'Clinic') {
          reqBody.address = checkClinicExist?.address;
          reqBody.country = checkClinicExist?.country_id;
          reqBody.county_id = checkClinicExist?.county_id;
          reqBody.town_id = checkClinicExist?.town_id;
          reqBody.pincode = checkClinicExist?.post_code;
        }

        if (!checkClinicExist) {
          _.assign(_retData, {
            status: 'error',
            statusCode: 400,
            msgCode: '50005',
            msg: UtilsHelper.getMessage('50005', 'en'),
          })
          return this.sendResponse(res, _retData)
        } else {
          clinicName = checkClinicExist?.name;
        }
      }

      let header_deafult_data = "";
      let footer_default_data = "";

      const _globalSettingData: any = await Models.ClinicalGlobalSettings.findOne({ where: { status: 1 } })

      if (_globalSettingData && _globalSettingData?.setting_data) {
        const setting_data = _globalSettingData.setting_data;
        if (setting_data?.header_deafult_data) {
          header_deafult_data = setting_data.header_deafult_data;
        }
        if (setting_data?.footer_default_data) {
          footer_default_data = setting_data.footer_default_data;
        }
      }

      reqBody.email = reqBody.email.toLowerCase()
      // filter object for findOne query
      const _filter = {
        email: reqBody.email,
        is_email_verified: true,
        status: { [Op.ne]: 9 },
      }
      // check if user exist in database
      const _olddata: any = await Models.ClinicalUsers.findOne({ where: _filter })
      // if _data exist
      if (_olddata) {
        // send same email
        const ClinicalUserRoleDetail = await Models.ClinicalUserRoleDetail.findOne({
          where: {
            user_type: reqBody.user_type,
            user_id: _olddata.id,
            status: 1
          }
        });

        if (ClinicalUserRoleDetail) {
          _.assign(_retData, {
            status: 'error',
            statusCode: 400,
            msgCode: '50003',
            msg: UtilsHelper.getMessage('50003', 'en'),
          });
          return this.sendResponse(res, _retData)
        } else {
          const userId = _olddata.id;


          const _roleData: any = await Models.ClinicalRoles.findOne({
            where: {
              name: 'Account Owner',
            },
          })

          let role_id = 0
          if (_roleData) {
            role_id = _roleData.id
          }
          reqBody.role_id = role_id

          await Models.ClinicalUsers.update(reqBody, {
            where: {
              id: userId
            }
          });

          if (reqBody.user_type == 'Individual') {
            authHelper.addUserDefaultRolePermission(reqBody.clinic_id, userId);
          }

          reqBody.password = await PasswordHelper.hashPassword(reqBody.password);
          const expireDate = new Date()
          expireDate.setMonth(expireDate.getMonth() + 3) // need to  3 from setting
          const clinicUserCreData: object = {
            user_id: userId,
            password: reqBody.password,
            password_expire_on: expireDate,
            login_attempts: 0,
            last_lggedin_on: new Date(),
          }

          // added data in to the user credentails table
          if (clinicUserCreData) {
            await Models.clinicalUserCredentials.update(clinicUserCreData, {
              where: {
                user_id: userId
              }
            })
          }

          const clinicUserData: object = {
            clinic_id: reqBody.clinic_id,
            user_id: userId,
            status: 1,
            letter_header_template: header_deafult_data.replace('<EMAIL>', _olddata.email).replace('<PHONE>', _olddata.phone).replace('<CLINIC_NAME>', clinicName),
            letter_footer_template: footer_default_data
          }
          // added data in to the user setting  table and send verify email
          if (clinicUserData) {
            await Models.ClinicalUserSettings.create(clinicUserData)
            const clinicUserRoleData: object = {
              clinic_id: reqBody.clinic_id,
              user_id: userId,
              user_type: reqBody.user_type,
              role_id: reqBody.role_id,
              status: 1,
            }
            await Models.ClinicalUserRoleDetail.create(clinicUserRoleData);
          }

          if (reqBody?.clinic_id) {
            await Models.Clinics.update({ status: 1 }, { where: { clinic_id: reqBody?.clinic_id } });
          }

          userActivityLogger.info('User Register successfully', { additionalInfo: { user_id: userId, first_name: reqBody.first_name, last_name: reqBody.last_name, email: reqBody.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

        }
      } else {
        const _roleData: any = await Models.ClinicalRoles.findOne({
          where: {
            name: 'Account Owner',
          },
        })

        let role_id = 0
        if (_roleData) {
          role_id = _roleData.id
        }
        // get plan with id 0
        const planData = await Models.SubscriptionPlan.findOne({
          where: { id: 1 }
        })

        reqBody.role_id = role_id
        reqBody.status = 0
        reqBody.subscription_plan_id = 1
        reqBody.remaining_custom_templates = planData.custom_templates_limit
        reqBody.remaining_design_templates = planData.design_templates_limit
        reqBody.remaining_audio_consultation_minutes = planData.audio_consultation_minutes * 60
        // reqBody.user_type = 'Clinic'
        reqBody.password = await PasswordHelper.hashPassword(reqBody.password)
        const _data = await Models.ClinicalUsers.create(reqBody)

        if (_data) {
          if (reqBody.user_type == 'Individual') {
            authHelper.addUserDefaultRolePermission(reqBody.clinic_id, _data.id);
          }
          const userId = _data.id
          const expireDate = new Date()
          expireDate.setMonth(expireDate.getMonth() + 3) // need to  3 from setting
          const clinicUserCreData: object = {
            user_id: userId,
            password: reqBody.password,
            password_expire_on: expireDate,
            login_attempts: 0,
            last_lggedin_on: new Date(),
            status: 0,
          }
          // added data in to the user credentails table
          if (clinicUserCreData) {
            await Models.clinicalUserCredentials.create(clinicUserCreData)
          }

          const clinicUserData: object = {
            clinic_id: reqBody.clinic_id,
            user_id: userId,
            status: 0,
            letter_header_template: header_deafult_data.replace('<EMAIL>', _data.email).replace('<PHONE>', _data.phone).replace('<CLINIC_NAME>', clinicName),
            letter_footer_template: footer_default_data
          }
          // added data in to the user setting  table and send verify email
          if (clinicUserData) {
            await Models.ClinicalUserSettings.create(clinicUserData)
            const clinicUserRoleData: object = {
              clinic_id: reqBody.clinic_id,
              user_id: userId,
              user_type: reqBody.user_type,
              role_id: reqBody.role_id,
              status: 0,
            }
            await Models.ClinicalUserRoleDetail.create(clinicUserRoleData);
            if (reqBody?.clinic_id) {
              await Models.Clinics.update({ status: 1 }, { where: { clinic_id: reqBody?.clinic_id } });
            }
            await authHelper.sendWelcomeEmail(_data, reqBody.clinic_id)
          }

          //send email process
          userActivityLogger.info('User Register successfully', { additionalInfo: { first_name: reqBody.first_name, last_name: reqBody.last_name, email: reqBody.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

          _.assign(_retData, {
            data: UtilsHelper.cryptoJsObjectEncrypt(_data),
            msgCode: '10003',
            msg: UtilsHelper.getMessage('10003', 'en'),
          })
        }
      }
    } catch (err: any) {
      console.log(err)
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in AuthController.register')
    }

    return this.sendResponse(res, _retData)
  }

  /*
   * Method to register user
   */

  public async varifyUser(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {
      const _reqData = req.body
      let _tokenData: any
      let _msgCode: any

      try {
        // first check the token validity
        _tokenData = await TokenHelper.DecodeJWTToken(_reqData.token)

        if (_tokenData && _.isEmpty(_tokenData.data.type)) {
          throw new Error('INVALID_TOKEN')
        }

      } catch (err: any) {
        _msgCode = err.name && err.name == 'TokenExpiredError' ? 50006 : 50007
        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode),
        })
        return UtilsHelper.cRes(res, _retData)
      }

      if (_tokenData?.data?.clinic_id.trim() === '') {
        throw new Error('NOT_FOUND')
      }

      const clinic_id = _tokenData.data.clinic_id;

      // if request is for Activate  user account
      const _filter = { id: _tokenData.data.id, email: _tokenData.data.email }
      if (_tokenData.data.type === 'VERIFY_USER') {
        _.assign(_filter, {
          is_email_verified: false,
        })
      }

      // check user data
      const _userData: any = await Models.ClinicalUsers.findOne({
        where: _filter,
        include: {
          model: Models.ClinicalUserRoleDetail,
          as: 'user_role_detail',
          where: {
            status: {
              [Op.notIn]: [9],
            }
          },
          attributes: ['clinic_id', 'user_type', 'status'],
        },
      })
      if (_.isEmpty(_userData)) {
        throw new Error('NOT_FOUND')
      }

      _userData.is_email_verified = true

      await _userData.save()

      const user: any = {
        first_name: _userData.first_name,
        last_name: _userData.last_name
      }

      const updateData: object = {
        status: 1
      }
      //activate user
      await Models.ClinicalUsers.update(updateData, { where: { id: _userData.id } });
      await Models.clinicalUserCredentials.update(updateData, { where: { user_id: _userData.id } });
      await Models.ClinicalUserSettings.update({ status: 1, clinic_id: clinic_id }, { where: { user_id: _userData.id } });
      await Models.ClinicalUserRoleDetail.update({ status: 1, clinic_id: clinic_id }, { where: { user_id: _userData.id } });

      //added free subscrption
      await authHelper.provideFreeSubscrption(_userData.id);


      if (_userData?.user_role_detail && _userData?.user_role_detail.length > 0) {
        const clinic_id = _userData?.user_role_detail[0].clinic_id;
        await Models.Clinics.update(updateData, { where: { clinic_id: clinic_id } });
      }

      _msgCode = 10005
      //logger
      userActivityLogger.info(UtilsHelper.getMessage(_msgCode), { additionalInfo: { user_id: _userData.id, first_name: _userData.first_name, last_name: _userData.last_name, email: _userData.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });
      //logger

      _.assign(_retData, { msgCode: _msgCode, data: UtilsHelper.cryptoJsObjectEncrypt(user), msg: UtilsHelper.getMessage(_msgCode) })
    } catch (err: any) {

      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in AuthController.varifyUser')
    }

    return UtilsHelper.cRes(res, _retData)
  }

  /*
   * Method to login user
   */
  public async login(req: express.Request, res: express.Response): Promise<void | any> {
    let _retData: IResponseObject = UtilsHelper.responseObject();
    let _msgCode;
    try {
      const reqBody = req.body;
      const _email = reqBody.email.toLowerCase();
      const _filter = {
        email: _email,
        is_email_verified: true,
        status: {
          [Op.ne]: 9,
        },
      };
      const _data: any = await Models.ClinicalUsers.findOne({ where: _filter });

      if (!_data) {
        //logger
        const clientIP = req.ip;
        securityLogger.info('Failed login attempt from', { additionalInfo: { user_ip: clientIP, email: reqBody.email, tags: 'Security Log', request: { Method: req.method, originalUrl: req.originalUrl } } });

        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: '50010',
          msg: UtilsHelper.getMessage('50010', 'en'),
          data: {},
        });
        return this.sendResponse(res, _retData);
      } else {
        if (_data.is_email_verified === false) {
          _.assign(_retData, {
            status: 'error',
            statusCode: 400,
            msgCode: 50011,
            msg: UtilsHelper.getMessage(50011),
          });
          return UtilsHelper.cRes(res, _retData);
        }

        if (_data.status === 0) {
          _.assign(_retData, {
            status: 'error',
            statusCode: 400,
            msgCode: 50035,
            msg: UtilsHelper.getMessage(50035),
          });
          return UtilsHelper.cRes(res, _retData);
        }

        if (_data.status === 8) {
          _.assign(_retData, {
            status: 'error',
            statusCode: 400,
            msgCode: 50036,
            msg: UtilsHelper.getMessage(50036),
          });
          return UtilsHelper.cRes(res, _retData);
        }

        const user_id = _data.id;

        // check user status
        _retData = authHelper.checkAccountStatus(_data, _retData);

        if (_retData.status === 'success') {
          const _filter = {
            user_id: user_id,
            status: 1,
          };

          const _dataCreden = await Models.clinicalUserCredentials.findOne({ where: _filter });

          if (!_dataCreden) {
            throw new Error('INVALID_CREDENTIALS');
          }
          // check password if valid
          const passValid = await PasswordHelper.comparePassword(
            reqBody.password,
            _dataCreden.password
          );

          if (!passValid) {
            //login attempts more than 3  user will be blocked
            await authHelper.userLoginAttempts(_dataCreden, user_id);

            //login attempts more than 3  user will be blocked
            throw new Error('INVALID_CREDENTIALS');
          }

          // Check if user has any subscription plan
          // const existingSub = await Models.ClinicalUserSubscriptions.findOne({
          //   where: {
          //     user_id: _data.id,
          //     status: 1
          //   }
          // });

          if (!_data.subscription_plan_id) {
            // Get free plan details
            const freePlan = await Models.SubscriptionPlan.findOne({
              where: { id: 1 }  // Free plan
            });

            if (freePlan) {
              // Create new subscription entry for free plan
              const startDate = new Date();
              const endDate = dayjs().add(1, 'month').toDate();

              await Models.ClinicalUserSubscriptions.create({
                user_id: _data.id,
                plan_id: freePlan.id,
                start_date: startDate,
                end_date: endDate,
                next_billing_date: endDate,
                currency: freePlan.currency,
                price: 0,
                billing_frequency: 'Monthly',
                status: 1, // Active
                subscription_features: {
                  id: freePlan.id,
                  name: freePlan.name,
                  plan_type: 'Monthly',
                  currency: freePlan.currency
                }
              });

              // Update user with free plan limits
              await Models.ClinicalUsers.update({
                subscription_plan_id: freePlan.id,
                remaining_custom_templates: freePlan.custom_templates_limit,
                remaining_audio_consultation_minutes: freePlan.audio_consultation_minutes * 60,
                remaining_design_templates: freePlan.design_templates_limit,
                allows_team_members: freePlan.allows_team_members,
                has_tasks: freePlan.has_tasks,
                has_user_logs: freePlan.has_user_logs,
                has_medical_history: freePlan.has_medical_history,
                has_invite_user: freePlan.has_invite_user
              }, {
                where: { id: _data.id }
              });
            }
          }

          // send OTP for 2FA
          const _OTP = await authHelper.sendOTPEmail(_data);

          // generate Token
          // If we log in via credentials, and we take more than 130 mins to fill OTP, this should get invalid
          // const _token = await TokenHelper.GenJWTToken({ email: _data.email }, '5m')
          const _token = await TokenHelper.GenJWTToken({ email: _data.email }, '130m')

          _dataCreden.login_attempts = 0;
          _dataCreden.save();

          /*     _.assign(_retData, {
                msgCode: _msgCode,
                msg: UtilsHelper.getMessage(_msgCode),
                data: UtilsHelper.cryptoJsObjectEncrypt({ token: _token }),
              }) */


          _msgCode = 10045
          _.assign(_retData, {
            msgCode: _msgCode,
            msg: UtilsHelper.getMessage(_msgCode),
            data: UtilsHelper.cryptoJsObjectEncrypt({
              signature: _token
            })
          })
        }
      }
    } catch (err: any) {
      this.logErrors(err, 'Error in AuthController.login')
      if (err.message === 'INVALID_CREDENTIALS') {
        _msgCode = 50010
        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode),
        })
      } else {
        _.assign(_retData, { status: 'error', statusCode: 500 })
      }
    }
    return this.sendResponse(res, _retData)
  }

  public async verifyOTPfor2FA(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()

    try {
      const _reqData = req.body;

      // validate signature
      await authHelper.validateSignature(_reqData);

      // check for OTP validation
      const _data = await Models.Otps.findOne({
        where: {
          email: _reqData.email.toLowerCase(),
          otp: _reqData.otp,
          valid_upto: { [Op.gt]: Sequelize.fn('NOW') }
        },
        include: {
          model: Models.ClinicalUsers,
          as: 'user',
          where: { status: 1 }
        }
      });



      if (_.isEmpty(_data)) {
        securityLogger.info('Failed login attempt with invalid otp', { additionalInfo: { user_ip: req.ip, email: _reqData.email, tags: 'Security Log', request: { Method: req.method, originalUrl: req.originalUrl } } });
        throw new Error("INVALID_OTP")
      }

      // delete OTP
      await Models.Otps.destroy({ where: { id: _data.id } });

      // generate Token
      const expiresIn = req.body.keepMeLoggedIn ? '30d' : '48h';
      const _token = await authHelper.generateAuthToken(_data.user, expiresIn);
      // TODO: use different secret key for refresh token
      // Generate refresh token
      const refresh_token = await TokenHelper.GenJWTToken(
        { id: _data.user.id, email: _data.user.email, type: 'refresh' }, 
        '7d'
      );

      const _msgCode = 10006;
      const _filter = {
        user_id: _data?.user?.id,
        status: 1,
      }
      await Models.clinicalUserCredentials.update({ last_lggedin_on: new Date() }, { where: _filter });
      //logger
      userActivityLogger.info(UtilsHelper.getMessage(_msgCode), { additionalInfo: { user_id: _data?.user?.id, first_name: _data?.user?.first_name, last_name: _data?.user?.last_name, email: _data?.user?.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });
      //logger
      _.assign(_retData, {
        msgCode: _msgCode,
        msg: UtilsHelper.getMessage(_msgCode),
        data: UtilsHelper.cryptoJsObjectEncrypt({ 
          token: _token,
          refresh_token: refresh_token 
        })
      })

    } catch (error: any) {
      this.logErrors(error, 'AuthControllerr.verifyOTPfor2FA')
      if (error.message === "INVALID_OTP") {
        const _msgCode = 50029;
        _.assign(_retData, { status: "error", statusCode: 400, msgCode: _msgCode, msg: UtilsHelper.getMessage(_msgCode) })
      }
      else if (error.message === "INVALID_SIGN") {
        const _msgCode = 50028;
        _.assign(_retData, { status: "error", statusCode: 400, msgCode: _msgCode, msg: UtilsHelper.getMessage(_msgCode) })
      }
    }

    return this.sendResponse(res, _retData)
  }

  /**
   * This method is used to resend the OTP for 2FA for login
   */
  public async resendOTPfor2FA(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject();
    try {
      const _reqData = req.body;
      // validate signature
      await authHelper.validateSignature(_reqData);
      // find user data
      const _filter = {
        email: _reqData.email,
        status: 1
      }

      const _userData = await Models.ClinicalUsers.findOne({
        where: _filter,
        attributes: ['id', 'role_id', 'email', 'phone', 'first_name', 'last_name', 'status']
      });

      // if record not found
      if (_.isEmpty(_userData)) {
        throw new Error("INVALID_CREDENTIALS");
      }

      // delete any old OTP for the account
      await Models.Otps.destroy({ where: { email: _userData.email } })

      // send OTP for 2FA
      const _OTP = await authHelper.sendOTPEmail(_userData);

      const _msgCode = 10045;
      userActivityLogger.info('resend otp send to user email', { additionalInfo: { userIp: req.ip, email: _userData.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });


      _.assign(_retData, {
        msgCode: _msgCode,
        msg: UtilsHelper.getMessage(_msgCode)
      })

      // if the enviornment is "test", ie., Unit Testing then pass the OTP in response
      if (process.env.NODE_ENV === "test") {
        _.assign(_retData.data, { data: { OTP: _OTP } })
      }
    }
    catch (error) {
      UtilsHelper.logErrors(error, "AuthController.resendOTPfor2FA")

      _.assign(_retData, { status: "error", statusCode: 500, msg: error.message })

      if (error.message === "INVALID_SIGN") {
        const _msgCode = 50028;
        _.assign(_retData, {
          status: "error",
          statusCode: 400,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode)
        })
      }
      else if (error.message === "INVALID_CREDENTIALS") {
        const _msgCode = 50013;
        _.assign(_retData, {
          status: "error",
          statusCode: 400,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode)
        })
      }
    }
    return UtilsHelper.cRes(res, _retData);
  }

  /**
   * Method to logout
   */
  public async logout(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()

    try {
      const text = {
        "name": "my Clinic",
        "address": "shjshjsaj sa jbasjn sa",
        "country_id": "230",
        "county_id": "3805",
        "town_id": 3880,
        "post_code": "45658",
        "phone": "9875662152",
        "registartion_number": "sssasasaa89"

      }
      const ciphertext = UtilsHelper.cryptoJsObjectEncrypt(text);
      // Decrypt
      const originalText = UtilsHelper.cryptoJsObjectDcrypt("U2FsdGVkX1/nkUwqEaj+uIiuKDAskGwJZzzbpS7R6cHCWUqROFiZpWPXuskZaNJPJOuzJ8ruRww72Yg3DLikCp0syr1WTibvqsoNgA/prVNPZ96YRjzPRdKn8yc/cxa7hPOFeUUZyJuIMQq+Dwq4wUty87w9+w9rwPkRTMtLyd71fFEPa3eUaMlMloWGRVO5MYfbedSdnrI7vn5CRho/tDGqYXbaNOgse9OjpxfSmGl55FUWu0ahAZgZ7uA0avAn5J+AFIIRKWupCDCkav37g2JKT5ABHIXluKSoxS+tqj44AtsK/d3CST/uX+n8JlvnfvVAtqHaJr3g2WSQabkM/YonDRHfCxcxIBu9JwZcTmtnmHhWP0YKKhTeMI/wth7KiWGSvpgwY1Wq5xk0qbDxPLYlkEWeruKnQ3uK6s5h4xE=");
      const _msgCode = 10006
      _.assign(_retData, {
        msgCode: _msgCode,
        msg: UtilsHelper.getMessage(_msgCode),
        data: {
          encrypet: ciphertext,
          decrypt: originalText,
        },
      })
    } catch (err) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in AuthController.logout')
    }

    return this.sendResponse(res, _retData)
  }

  /*
   * Method to login user
   */
  public async forgotPassword(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    let _msgCode
    try {
      const reqBody = req.body
      const _filter: any = {
        email: reqBody.email.toLowerCase(),
        status: {
          [Op.notIn]: [9],
        }
      }
      const _data = await Models.ClinicalUsers.findOne({ where: _filter })
      if (_.isEmpty(_data)) {
        throw new Error('INVALID_EMAIL')
      }

      if (_data.status === 0) {
        throw new Error('DEACTIVATED')
      }

      // send email
      const _resetPasswordToken = await authHelper.sendForgotPasswordEmail(_data)
      _msgCode = 10012

      //logger
      userActivityLogger.info(UtilsHelper.getMessage(_msgCode), { additionalInfo: { user_id: _data.id, first_name: _data.first_name, last_name: _data.last_name, email: _data.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

      const clientIP = req.ip;
      auditLogger.info(`successful forgot password request from ${_data.first_name}`, { additionalInfo: { user_id: _data.id, ipAddress: clientIP, email: _data.email, tags: 'Audit log', status: "success", request: { Method: req.method, originalUrl: req.originalUrl } } });

      _.assign(_retData, {
        msgCode: _msgCode,
        msg: UtilsHelper.getMessage(_msgCode, 'en'),
      })
    } catch (error: any) {
      this.logErrors(error, 'Error in AuthController.forgotPassword')
      if (error.message === 'INVALID_EMAIL') {

        securityLogger.info('Failed forgot password request from', { additionalInfo: { user_ip: req.ip, tags: 'Security Log', request: { Method: req.method, originalUrl: req.originalUrl } } });

        _msgCode = 50013
        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode),
        })
      } else if (error.message === 'EMAIL_ERROR') {
        _msgCode = 50014
        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode),
        })
      } else if (error.message === 'DEACTIVATED') {
        _msgCode = 50008
        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode),
        })
      } else {
        _.assign(_retData, {
          status: 'error',
          statusCode: 500,
          msg: error.message,
        })
      }
    }
    return this.sendResponse(res, _retData)
  }

  /*
   * Method to login user
   */
  public async resetPassword(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    let _msgCode
    try {
      const _reqData = req.body
      let _tokenData, _msgCode

      try {
        // first check the token validity
        _tokenData = await TokenHelper.DecodeJWTToken(_reqData.token)

        if (_tokenData && _.isEmpty(_tokenData.data.type)) {
          throw new Error('INVALID_TOKEN')
        }
      } catch (err) {
        _msgCode = err.name && err.name == 'TokenExpiredError' ? 50015 : 50016
        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode),
        })
        return UtilsHelper.cRes(res, _retData)
      }

      await authHelper.createPassword(_tokenData, _reqData)

      _msgCode = 10013

      userActivityLogger.info(UtilsHelper.getMessage(_msgCode), { additionalInfo: { user_id: _tokenData.data.id, email: _tokenData.data.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

      const clientIP = req.ip;
      auditLogger.info(`successful reset password request from `, { additionalInfo: { user_ip: clientIP, user_id: _tokenData.data.id, email: _tokenData.data.email, tags: 'Audit log', status: "success", request: { Method: req.method, originalUrl: req.originalUrl } } });

      _.assign(_retData, { msgCode: _msgCode, msg: UtilsHelper.getMessage(_msgCode) })
    } catch (error: any) {
      this.logErrors(error, 'Error in AuthController.resetPassword')
      if (error.message === 'INVALID_EMAIL') {

        securityLogger.info('Failed reset password request from', { additionalInfo: { user_ip: req.ip, tags: 'Security Log', request: { Method: req.method, originalUrl: req.originalUrl } } });

        _msgCode = 50013
        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode),
        })
      } else if (error.message === 'EMAIL_ERROR') {
        _msgCode = 50014
        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode),
        })
      } else if (error.message === 'SAME_PASSWORD') {
        const _msgCode = 50031;
        _.assign(_retData, {
          status: "error",
          statusCode: 400,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode)
        })
      } else {
        _.assign(_retData, {
          status: 'error',
          statusCode: 500,
          msg: error.message,
        })
      }
    }
    return this.sendResponse(res, _retData)
  }

  /*
   * Method to check user
   */
  public async checkEmail(req: express.Request, res: express.Response): Promise<void | any> {

    const _retData: IResponseObject = UtilsHelper.responseObject()
    let _msgCode
    try {

      const reqBody = req.body
      const _filter: any = {
        email: reqBody.email.toLowerCase(),
        is_email_verified: true,
        status: {
          [Op.notIn]: [9],
        }
      }
      const _data = await Models.ClinicalUsers.findOne({
        where: _filter,
        include: {
          model: Models.ClinicalUserRoleDetail,
          as: 'user_role_detail',
          where: {
            status: {
              [Op.notIn]: [9],
            }
          },
          attributes: ['user_type', 'status'],
        },
      })

      if (_data) {
        _msgCode = 10014
        _.assign(_retData, {
          msgCode: _msgCode,
          data: UtilsHelper.cryptoJsObjectEncrypt(_data),
          msg: UtilsHelper.getMessage(_msgCode, 'en'),
        })
      } else {
        _msgCode = 50013
        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode),
        })
      }


    } catch (err: any) {
      console.log(err)
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in AuthController.checkEmail')
    }

    return UtilsHelper.cRes(res, _retData)

  }

  /*
   * Method to send otp
   */
  public async sendOtp(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    let _msgCode
    try {
      const reqBody = req.body;
      const otpExpDate = new Date();
      const otpExpDateNew = otpExpDate.setHours(otpExpDate.getHours() + 2);
      const _filter = {
        email: reqBody.email.toLowerCase(),
        status: {
          [Op.notIn]: [9],
        }
      }

      const _data = await Models.ClinicalUsers.findOne({ where: _filter })
      const otp = UtilsHelper.generateRandomNumber(5);
      const subject = `${otp} is your OTP for verification of your account with ClinicalPad. It is valid for 2 hours.`;

      if (_data) {
        const user_id = _data.id;
        const first_name = _data.first_name;
        await Models.Otps.destroy({ where: { email: reqBody.email.toLowerCase() } });
        const _Otpdata = await Models.Otps.create({
          user_id: user_id,
          email: reqBody.email.toLowerCase(),
          otp: otp,
          valid_upto: otpExpDateNew,
        });

        if (_Otpdata) {
          const template = process.cwd() + '/src/templates/sendotp.ejs';
          fs.readFile(template, "utf8", async function (error: any, file: any) {
            if (error) {
              return error
            }
            const compileTemplate: any = Ejs.compile(file, { filename: template })
            const context = {
              data: {
                name: first_name,
                otp: otp,
                frontEndUrl: process.env.FRONT_END_URL

              }
            }
            let htmls = compileTemplate(context)
            htmls = htmls.replace(/&lt;/g, "<");
            htmls = htmls.replace(/&gt;/g, ">");
            htmls = htmls.replace(/&#34;/g, '"');

            await sendMail(reqBody.email.toLowerCase(), subject, htmls, htmls, "").then((_data) => console.log(_data));

          })
          _msgCode = 10045;

          //logger
          userActivityLogger.info(UtilsHelper.getMessage(_msgCode), { additionalInfo: { user_id: _data.id, first_name: _data.first_name, last_name: _data.last_name, email: _data.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

          _.assign(_retData, {
            msgCode: _msgCode,
            msg: UtilsHelper.getMessage(_msgCode, 'en'),
          })
        }
      } else {
        _msgCode = 50013
        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode),
        })
      }


    } catch (err: any) {

      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in AuthController.sendOtp')
    }

    return UtilsHelper.cRes(res, _retData)
  }

  /*
 * Method to send otp
 */
  public async verifyOtp(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    let _msgCode
    try {
      const reqBody = req.body;
      const _data = await Models.Otps.findOne({ where: { email: reqBody.email.toLowerCase(), otp: reqBody.otp, valid_upto: { [Op.gte]: Moment().utc().toDate() } } });
      _msgCode = 10046

      if (_data) {
        const _filter: any = {
          email: reqBody.email.toLowerCase(),
          status: {
            [Op.notIn]: [9],
          }
        }
        const _userdata = await Models.ClinicalUsers.findOne({ where: _filter });

        //logger
        userActivityLogger.info(UtilsHelper.getMessage(_msgCode), { additionalInfo: { user_id: _userdata.id, first_name: _userdata.first_name, last_name: _userdata.last_name, email: _userdata.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

        _.assign(_retData, {
          msgCode: _msgCode,
          data: UtilsHelper.cryptoJsObjectEncrypt(_userdata),
          msg: UtilsHelper.getMessage(_msgCode, 'en'),
        })

      } else {

        securityLogger.info('Failed to attempt verify OTP from', { additionalInfo: { user_ip: req.ip, tags: 'Security Log', request: { Method: req.method, originalUrl: req.originalUrl } } });

        _msgCode = 50026
        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode),
        })
      }
    } catch (err: any) {
      console.log(err)
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in AuthController.verifyOtp')
    }

    return UtilsHelper.cRes(res, _retData)
  }

  /*
   * Method to refresh access token
   */
  public async refreshToken(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {
      const { refresh_token } = req.body
      
      if (!refresh_token) {
        throw new Error('INVALID_TOKEN')
      }
      
      // Verify refresh token
      let tokenData: any
      try {
        tokenData = await TokenHelper.DecodeJWTToken(refresh_token)
        
        if (!tokenData || !tokenData.data || !tokenData.data.id) {
          throw new Error('INVALID_TOKEN')
        }
      } catch (err: any) {
        const _msgCode = err.name && err.name == 'TokenExpiredError' ? 50006 : 50007
        _.assign(_retData, {
          status: 'error',
          statusCode: 401,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode),
        })
        return this.sendResponse(res, _retData)
      }
      
      // Get user data
      const userData = await Models.ClinicalUsers.findOne({
        where: { 
          id: tokenData.data.id,
          status: 1
        }
      })
      
      if (!userData) {
        throw new Error('USER_NOT_FOUND')
      }
      
      // Generate new tokens
      const access_token = await authHelper.generateAuthToken(userData, '2h')
      const new_refresh_token = await TokenHelper.GenJWTToken(
        { id: userData.id, email: userData.email, type: 'refresh' }, 
        '7d'
      )
      
      const _msgCode = 10006
      _.assign(_retData, {
        msgCode: _msgCode,
        msg: UtilsHelper.getMessage(_msgCode),
        data: UtilsHelper.cryptoJsObjectEncrypt({ 
          access_token, 
          refresh_token: new_refresh_token 
        })
      })
      
    } catch (err: any) {
      this.logErrors(err, 'Error in AuthController.refreshToken')
      
      if (err.message === 'USER_NOT_FOUND' || err.message === 'INVALID_TOKEN') {
        const _msgCode = 50007
        _.assign(_retData, {
          status: 'error',
          statusCode: 401,
          msgCode: _msgCode,
          msg: UtilsHelper.getMessage(_msgCode),
        })
      } else {
        _.assign(_retData, { 
          status: 'error', 
          statusCode: 500,
          msg: err.message
        })
      }
    }
    
    return this.sendResponse(res, _retData)
  }

}

const authController = new AuthController()

export default authController
