const express = require('express')
const Router = express.Router()
import authController from './AuthController'
import Validator from './validators'
//import Validator from "./validators";
//import { verifyToken } from "../../../../services/Auth;
const _router = function (options: any) {
  Router.post('/register-clinic', Validator('addClinicValid'), authController.registerClinic)
  Router.post('/register', <PERSON><PERSON><PERSON>('registerOwnerValid'), authController.register)
  Router.post('/varify-user', Validator('verifyUserValid'), authController.varifyUser)
  Router.post('/login', Val<PERSON><PERSON>('loginValid'), authController.login)
  // Verify OTP for 2FA
  Router.post("/verify-otp", Validator("verifyOTPValidate"), authController.verifyOTPfor2FA);
  Router.post("/resend-otp", <PERSON><PERSON><PERSON>("resendOTPValidate"), authController.resendOTPfor2FA);
  Router.post('/logout', authController.logout);
  // API endpoint to reset the password
  Router.post('/forgot-password', Validator('forgotPasswordValid'), authController.forgotPassword)
  Router.post('/reset-password', Validator('resetPasswordValid'), authController.resetPassword)
  Router.post('/check-email', Validator('checkEmailValid'), authController.checkEmail);
  Router.post('/send-otp', Validator('checkEmailValid'), authController.sendOtp)
  Router.post('/register-verify-otp', Validator('userVerifyOtp'), authController.verifyOtp)
  // Refresh token endpoint
  Router.post('/refresh-token', authController.refreshToken)

  return Router
}

export default _router
