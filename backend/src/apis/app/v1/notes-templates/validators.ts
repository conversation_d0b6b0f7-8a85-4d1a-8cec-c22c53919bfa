import Joi from 'joi'
import express from 'express'
import _ from 'lodash'

import * as UtilsHelper from '../../../../helpers/utils.helper'

const Validators: any = {
  addTemplateValid: Joi.object({
    note_type: Joi.string().required(),
    // file: Joi.binary().required(),
    // note_template: Joi.string().required(),
    header_html: Joi.string().required(),
    footer_html: Joi.string().required(),
    name: Joi.string().required(),
    clinic_id: Joi.string().required(),
  }),
  getTemplateListValid: Joi.object({
    clinic_id: Joi.string().required(),
    page: Joi.string().optional(),
  }),
  getTemplateDetailValid: Joi.object({
    templateId: Joi.number().integer().positive().required(),
    clinic_id: Joi.string().required(),
  }),
  deleteTemplateValid: Joi.object({
    templateId: Joi.number().integer().positive().required(),
  }),
  editTemplateValid: Joi.object({
    templateId: Joi.number().integer().positive().required(),
    note_type: Joi.string().required(),
    // note_template: Joi.string().required(),
    header_html: Joi.string().required(),
    footer_html: Joi.string().required(),
    name: Joi.string().required(),
    clinic_id: Joi.string().required(),
  }),
}

export default function Validator(func: string) {
  return async function Validator(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const _reqBody = (req.body?.encrypet) ? UtilsHelper.cryptoJsObjectDcrypt(req.body.encrypet) : req.body;
      const _reqData = _.assign(_reqBody, req.params, req.query);
      const validated = await Validators[func].validateAsync(_reqData, { abortEarly: false })
      req.body = validated
      next()
    } catch (err: any) {
      const _er: any = {}
      if (err.isJoi) {
        err.details.forEach((d: any) => {
          const _key: string = d.context.key
          _er[_key] = d.message
        })
      }

      const _retData = UtilsHelper.responseObject()
      _retData.status = 'error'
      _retData.statusCode = 400
      _retData.msg = UtilsHelper.getMessage('50020', 'en')
      _retData.msgCode = '50020'
      _retData.data = _er

      return UtilsHelper.cRes(res, _retData)
    }
  }
}