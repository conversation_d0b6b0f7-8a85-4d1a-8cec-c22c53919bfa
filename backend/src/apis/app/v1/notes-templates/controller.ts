import * as express from 'express'
import _ from 'lodash'
import { v4 as uuidv4 } from 'uuid';

import BaseController from '../../../../helpers/BaseController'
import { IResponseObject } from '../../../../helpers/utils.interface';
import * as UtilsHelper from '../../../../helpers/utils.helper'
import Models from '../../../../database/models'
import { userActivityLogger } from '../../../../helpers/logger.helpers';
import { saveDoc, getPresignedUrls } from '../../../../helpers/fileUpload.helper';

class NotesTemplatesController extends BaseController {
  constructor() {
    super()

    this.createTemplate = this.createTemplate.bind(this)
    this.listTemplates = this.listTemplates.bind(this)
    this.getTemplateDetail = this.getTemplateDetail.bind(this)
    this.deleteTemplate = this.deleteTemplate.bind(this)
    this.editTemplate = this.editTemplate.bind(this)
  }

  public async createTemplate(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject();
    try {
      const reqBody = req.body;
      const files = req.files;
      const uniqueId = uuidv4();
      const { filename: pdfFilename, path: pdfFilePath } = files[0];
      const { filename: imageFilename, path: imageFilePath } = files[1];
      const userId = req?.user?.id;
      const clinicId = reqBody.clinic_id;

      // Fetch Clinical User to check remaining_design_template
      const clinicalUser = await Models.ClinicalUsers.findOne({ where: {  id: userId } });

      if (!clinicalUser) {
        _.assign(_retData, {
          statusCode: 404,
          status: 'error',
          msg: 'Clinical user not found',
        });
        return this.sendResponse(res, _retData);
      }

      // Check if user has remaining templates available
      if (clinicalUser.remaining_design_templates <= 0) {
        _.assign(_retData, {
          statusCode: 400,
          status: 'error',
          msg: 'No remaining design templates available',
        });
        return this.sendResponse(res, _retData);
      }

      // Prepare template data
      const notesTemplates: any = {
        user_id: userId,
        clinic_id: clinicId,
        note_type: reqBody.note_type,
        letter_header: reqBody.header_html,
        letter_footer: reqBody.footer_html,
        thumbnail_uuid: uniqueId,
        name: reqBody.name,
      };

      // Upload files
      const uploadedPdf = await saveDoc(pdfFilePath, `backend/templates/${uniqueId}.pdf`);
      const uploadedImage = await saveDoc(imageFilePath, `backend/templates-images/${uniqueId}.png`);

      // Create template
      const _createdData = await Models.ClinicalUserNotesTemplates.create(notesTemplates);

      if (_createdData) {
        // Decrement remaining_design_template by 1
        await Models.ClinicalUsers.update(
          { remaining_design_templates: clinicalUser.remaining_design_templates - 1 },
          { where: { id: userId } }
        );

        // Log user activity
        userActivityLogger.info(UtilsHelper.getMessage(10100), {
          additionalInfo: {
            user_id: userId,
            first_name: req?.user?.first_name,
            last_name: req?.user?.last_name,
            email: req?.user?.email,
            templateId: _createdData.id,
            tags: 'User Activity',
            request: { Method: req.method, originalUrl: req.originalUrl },
          },
        });

        _.assign(_retData, {
          data: UtilsHelper.cryptoJsObjectEncrypt(_createdData),
          msgCode: 10100,
          msg: UtilsHelper.getMessage(10100),
        });
      }
    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      });

      this.logErrors(err, 'Error in TemplateController.createTemplate');
    }

    return this.sendResponse(res, _retData);
  }

  public async listTemplates(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {
      const userId = req?.user?.id
      const _templates = await Models.ClinicalUserNotesTemplates.findAll({
        where: {
          // user_id: userId,
          clinic_id: req?.body?.clinic_id
        },
      })
      if(_templates) {
        const thumbnaiIds = _templates.map((template: any) => `backend/templates-images/${template.thumbnail_uuid}.png`);
        const signedUrls = await getPresignedUrls(thumbnaiIds);
        const response = { templates: _templates, thumbnail_urls: signedUrls };
        _.assign(_retData, {
          data: UtilsHelper.cryptoJsObjectEncrypt(response),
          msgCode: 10101,
          msg: UtilsHelper.getMessage(10101),
        })
      }
    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in TemplateController.listTemplate')
    }
    return this.sendResponse(res, _retData)
  }

  public async getTemplateDetail(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {
      const userId = req?.user?.id
      const templateId = req.params.templateId
      const clinicId = req?.body?.clinic_id
      const _template = await Models.ClinicalUserNotesTemplates.findOne({
        where: {
          // user_id: userId,
          clinic_id: clinicId,
          id: templateId,
        },
      })

      if(_template) {
        _.assign(_retData, {
          data: UtilsHelper.cryptoJsObjectEncrypt(_template),
          msgCode: 10102,
          msg: UtilsHelper.getMessage(10102),
        })
      }
    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in TemplateController.getTemplateDetail')
    }
    return this.sendResponse(res, _retData)
  }

  public async deleteTemplate(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {
      const userId = req?.user?.id
      const templateId = req.params.templateId
      const _template = await Models.ClinicalUserNotesTemplates.findOne({
        where: {
          user_id: userId,
          id: templateId,
        },
      })

      if(_template) {
        await _template.destroy()
        _.assign(_retData, {
          msgCode: 10103,
          msg: UtilsHelper.getMessage(10103),
        })
      }
    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in TemplateController.deleteTemplate')
    }

    return this.sendResponse(res, _retData)
  }

  public async editTemplate(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {
      const reqBody = req.body
      const userId = req?.user?.id
      const files = req.files;
      const uniqueId = uuidv4();
      const {filename: pdfFilename, path: pdfFilePath } = files[0];
      const {filename: imageFilename, path: imageFilePath } = files[1];
      const notesTemplates: any = {};
      notesTemplates.thumbnail_uuid = uniqueId;
      const uploadedPdf = await saveDoc(pdfFilePath, `backend/templates/${uniqueId}.pdf`);
      const uploadedImage = await saveDoc(imageFilePath, `backend/templates-images/${uniqueId}.png`);

      const templateId = req.params.templateId
      const _template = await Models.ClinicalUserNotesTemplates.findOne({
        where: {
          // user_id: userId,
          clinic_id: reqBody.clinic_id,
          id: templateId,
        },
      })

      if(_template) {
        // _template.note_type = reqBody.note_type
        // _template.note_template = reqBody.note_template
        _template.letter_header = reqBody.header_html
        _template.letter_footer = reqBody.footer_html
        _template.name = reqBody.name
        _template.thumbnail_uuid = uniqueId
        await _template.save()
        _.assign(_retData, {
          data: UtilsHelper.cryptoJsObjectEncrypt(_template),
          msgCode: 10104,
          msg: UtilsHelper.getMessage(10104),
        })
      }
    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in TemplateController.editTemplate')
    }
    return this.sendResponse(res, _retData)
  }
}

const templateController = new NotesTemplatesController()
export default templateController
