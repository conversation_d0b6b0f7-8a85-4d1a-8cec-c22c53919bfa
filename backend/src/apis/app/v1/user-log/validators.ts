import Joi from 'joi'
import express from 'express'
import _ from 'lodash'
import * as UtilsHelper from '../../../../helpers/utils.helper'

const Validators: any = {
  getUserLogValid: Joi.object({
    searchStr: Joi.string().min(3), // Legacy search string (if still used)
    page: Joi.number().integer().min(1).required(), // Pagination page
    clinic_id: Joi.string().required(), // Clinic ID validation
    patient_id: Joi.number().required(), // Patient ID validation
    startDate: Joi.date().iso(), // Optional ISO date for filtering start date
    endDate: Joi.date().iso().when('startDate', {
      is: Joi.exist(), // When startDate exists
      then: Joi.date().min(Joi.ref('startDate')), // Apply min validation
      otherwise: Joi.date().iso(), // Just validate as ISO date if no startDate
    }),// Optional ISO date, must be >= startDate
  }),
}

export default function Validator(func: string) {
  return async function Validator(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const _reqBody = (req.body?.encrypet) ? UtilsHelper.cryptoJsObjectDcrypt(req.body.encrypet) : req.body;
      const _reqData = _.assign(_reqBody, req.params, req.query);
      const validated = await Validators[func].validateAsync(_reqData, { abortEarly: false })
      req.body = validated
      next()
    } catch (err: any) {
      const _er: any = {}
      if (err.isJoi) {
        err.details.forEach((d: any) => {
          const _key: string = d.context.key
          _er[_key] = d.message
        })
      }

      const _retData = UtilsHelper.responseObject()
      _retData.status = 'error'
      _retData.statusCode = 400
      _retData.msg = UtilsHelper.getMessage('50020', 'en')
      _retData.msgCode = '50020'
      _retData.data = _er

      return UtilsHelper.cRes(res, _retData)
    }
  }
}
