import Joi from 'joi'
import express from 'express'
import _ from 'lodash'
import * as UtilsHelper from '../../../../helpers/utils.helper'

// Define a schema for the referralDetails
const referralDetailsSchema = Joi.object({
  name: Joi.string().required(),
  email: Joi.string().email(),
  phone: Joi.string().required(),
  address: Joi.string(),
  specialization: Joi.string(),
});

const Validators: any = {
  addPatientValid: Joi.object({
    clinic_id: Joi.string().required(),
    period: Joi.string().required().valid('Years', 'Months','Days'),
    first_name: Joi.string().required(),
    last_name: Joi.string().required(),
    insurance_id: Joi.number().integer().positive(),
    nhs_number: Joi.string().allow(null).allow(''),
    others: Joi.string(),
    email: Joi.string().trim().email(),
    age: Joi.number().required(),
    phone: Joi.string().allow(null).allow(''),
    gender: Joi.string().allow(null).allow(''),
    dob: Joi.string().required(),
    insurence_status: Joi.string().allow(null).allow(''),
    last_check_up_date: Joi.string(),
    reference_number: Joi.string().allow(null).allow(''),
    address: Joi.string().allow(null).allow(''),
    country_id: Joi.string().allow(null).allow(''),
    county_id: Joi.string().allow(null).allow(''),
    town_id: Joi.string().allow(null).allow(''),
    post_code: Joi.string().allow(null).allow(''),
    //name: Joi.string().required(),
    additional_fields: Joi.boolean().required(),
    referral_details: Joi.when('additional_fields', {
      is: true,
      then: referralDetailsSchema.required(),
      otherwise: Joi.optional(),
    }),
  }),


  editPatientValid: Joi.object({
    patientId: Joi.number().integer().positive().required(),
    period: Joi.string().required().valid('Years', 'Months', 'Days'),
    clinic_id: Joi.string().required(),
    first_name: Joi.string(),
    insurance_id: Joi.number().integer().positive(),
    nhs_number: Joi.string().allow(null).allow(''),
    others: Joi.string(),
    last_name: Joi.string(),
    email: Joi.string().allow(null).allow(''),
    age: Joi.number().required(),
    phone: Joi.string().allow(null).allow(''),
    gender: Joi.string().allow(null).allow(''),
    dob: Joi.string().required(),
    insurence_status: Joi.string().allow(null).allow(''),
    last_check_up_date: Joi.string(),
    reference_number: Joi.string().allow(null).allow(''),
    address: Joi.string().allow(null).allow(''),
    country_id: Joi.string().allow(null).allow(''),
    county_id: Joi.string().allow(null).allow(''),
    town_id: Joi.string().allow(null).allow(''),
    post_code: Joi.string().allow(null).allow(''),
    additional_fields: Joi.boolean().required(),
    referral_details: Joi.when('additional_fields', {
      is: true,
      then: referralDetailsSchema.required(),
      otherwise: Joi.optional(),
    }),
  }),

  getPatientDetailValid: Joi.object({
    patientId: Joi.number().integer().positive().required(),
  }),
  deletePatientDetailValid: Joi.object({
    patientId: Joi.number().integer().positive().required(),
    deleted_reason: Joi.string().required(),
  }),

  patientListValid: Joi.object({
    searchStr: Joi.string().min(3),
    page: Joi.number().integer().min(1).required(),
    clinic_id: Joi.string().required(),
  }),

  uploadPatientCsvValid: Joi.object({
    clinic_id: Joi.string().required(),
  }),
}

export default function Validator(func: string) {
  return async function Validator(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const _reqBody = (req.body?.encrypet) ? UtilsHelper.cryptoJsObjectDcrypt(req.body.encrypet) : req.body;
      const _reqData = _.assign(_reqBody, req.params, req.query);
      const validated = await Validators[func].validateAsync(_reqData, { abortEarly: false })
      req.body = validated
      next()
    } catch (err: any) {
      const _er: any = {}
      if (err.isJoi) {
        err.details.forEach((d: any) => {
          const _key: string = d.context.key
          _er[_key] = d.message
        })
      }

      const _retData = UtilsHelper.responseObject()
      _retData.status = 'error'
      _retData.statusCode = 400
      _retData.msg = UtilsHelper.getMessage('50020', 'en')
      _retData.msgCode = '50020'
      _retData.data = _er

      return UtilsHelper.cRes(res, _retData)
    }
  }
}
