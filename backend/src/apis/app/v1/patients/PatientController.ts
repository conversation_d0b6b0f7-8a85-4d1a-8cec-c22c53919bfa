import * as express from 'express'
import * as UtilsHelper from '../../../../helpers/utils.helper'
import { IResponseObject } from '../../../../helpers/utils.interface'
import BaseController from '../../../../helpers/BaseController'
const { uploadFile } = require('../../../../helpers/fileUpload.helper')
import _ from 'lodash'
import multer from 'multer'
const moment = require('moment');
import fs from 'fs'
import { createReadStream } from 'fs';
import { parse } from 'fast-csv';
import Models from '../../../../database/models'
import { Op, Sequelize } from 'sequelize'
import * as clinicalNoteHelper from '../clinicalNote/helper';
import { userActivityLogger, securityLogger, dataAccessLogger, auditLogger } from '../../../../helpers/logger.helpers';

//import * as patientHelper from './helper'

const key = process.env.AES_KEY;
class PatientController extends BaseController {
  constructor() {
    super()

    this.createPatient = this.createPatient.bind(this)
    this.editPatient = this.editPatient.bind(this)
    this.gePatientDeatil = this.gePatientDeatil.bind(this)
    this.patientList = this.patientList.bind(this)
    this.uploadFiles = this.uploadFiles.bind(this)
    this.deletePatient = this.deletePatient.bind(this)
    this.uploadPatientCsv = this.uploadPatientCsv.bind(this);
  }

  public async createPatient(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {
      const reqBody = req.body
      //validate client Id
      const userId = req?.user?.id

      //const nameSearch: any = Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('name'), 'bytea'), process.env.AES_KEY)
      const whereCond: any = {
        [Op.and]: [
          Models.sequelize.where(
            Models.sequelize.literal(
              `PGP_SYM_DECRYPT(email::bytea, '${key}')::text`
            ),
            {
              [Op.iLike]: `%${reqBody.email}%`,
            }
          ),
          { status: 1, }

          // Additional conditions can be added here
        ],
      }


      if (reqBody?.clinic_id && reqBody.clinic_id != null) {

        const checkClient: any = await Models.Clinics.findOne({
          where: { status: 1, clinic_id: reqBody.clinic_id },
        })

        reqBody.clinic_id = checkClient?.clinic_id

      } else {
        whereCond.user_id = userId
      }

      /* if (reqBody?.email) {
        const patientData: any = await Models.Patients.findOne({
          where: whereCond,
        })

        if (patientData) {
          _.assign(_retData, {
            status: 'error',
            statusCode: 400,
            msgCode: 50021,
            msg: UtilsHelper.getMessage(50021),
          })
          return this.sendResponse(res, _retData)
        }

      } */

      reqBody.status = 1

      reqBody.user_id = userId;
      reqBody.name = UtilsHelper.systemEncrypt(`${reqBody.first_name} ${reqBody.last_name}`);
      reqBody.first_name = UtilsHelper.systemEncrypt(reqBody.first_name);
      reqBody.last_name = UtilsHelper.systemEncrypt(reqBody.last_name);
      reqBody.email = (_.has(reqBody, 'email')) ? UtilsHelper.systemEncrypt(reqBody.email) : null;
      reqBody.phone = (_.has(reqBody, 'phone')) ? UtilsHelper.systemEncrypt(reqBody.phone) : null;

      reqBody.nhs_number = (_.has(reqBody, 'nhs_number')) ? UtilsHelper.systemEncrypt(reqBody.nhs_number) : null;
      reqBody.others = (_.has(reqBody, 'others')) ? UtilsHelper.systemEncrypt(reqBody.others) : null;
      reqBody.gender = (_.has(reqBody, 'gender')) ? UtilsHelper.systemEncrypt(reqBody.gender) : null;
      reqBody.insurence_status = (_.has(reqBody, 'insurence_status')) ? UtilsHelper.systemEncrypt(reqBody.insurence_status) : null;
      reqBody.reference_number = (_.has(reqBody, 'reference_number')) ? UtilsHelper.systemEncrypt(reqBody.reference_number) : null;
      reqBody.address = (_.has(reqBody, 'address')) ? UtilsHelper.systemEncrypt(reqBody.address) : null;


      reqBody.dob = new Date(reqBody.dob)
      if (reqBody?.last_check_up_date) {
        reqBody.last_check_up_date = new Date(reqBody.last_check_up_date)
      }

      const _createdData = await Models.Patients.create(reqBody);

      //logger
      userActivityLogger.info(UtilsHelper.getMessage(10020), { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, patientId: _createdData.id, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

      const requestBodyKeys = Object.keys(req.body);

      dataAccessLogger.info(`${req?.user?.first_name} has created patient record for the Clinical note genration`, { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, patientId: _createdData.id, data_acess: requestBodyKeys, tags: 'Data Access', request: { Method: req.method, originalUrl: req.originalUrl } } });

      _.assign(_retData, {
        data: UtilsHelper.cryptoJsObjectEncrypt(_createdData),
        msgCode: 10020,
        msg: UtilsHelper.getMessage(10020),
      })

    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in PatientController.createPatient')
    }

    return this.sendResponse(res, _retData)
  }

  public async editPatient(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {
      const reqBody = req.body
      //validate client Id
      const _params = req.params
      const patientId = _params.patientId
      const userId = req?.user?.id
      reqBody.name = UtilsHelper.systemEncrypt(`${reqBody.first_name} ${reqBody.last_name}`);
      reqBody.first_name = UtilsHelper.systemEncrypt(reqBody.first_name);
      reqBody.last_name = UtilsHelper.systemEncrypt(reqBody.last_name);

      const _userRoleDeatilData: any = await Models.ClinicalUserRoleDetail.findOne({ where: { user_id: userId, status: 1 } })
      if (_.isEmpty(_userRoleDeatilData)) {
        throw new Error('USER_ROLE_DETAIL_NOT_FOUND')
      }

      const checkPatient: any = await Models.Patients.findOne({
        where: { status: 1, id: patientId },
      })

      if (checkPatient) {

        reqBody.status = 1
        reqBody.clinic_id = checkPatient.clinic_id
        reqBody.user_id = userId

        if (_.has(reqBody, "email") === true) {
          reqBody.email = UtilsHelper.systemEncrypt(reqBody.email);
        }
        if (_.has(reqBody, "phone") === true) {
          reqBody.phone = UtilsHelper.systemEncrypt(reqBody.phone);
        }
        if (_.has(reqBody, "nhs_number") === true) {
          reqBody.nhs_number = UtilsHelper.systemEncrypt(reqBody.nhs_number);
        }
        if (_.has(reqBody, "gender") === true) {
          reqBody.gender = UtilsHelper.systemEncrypt(reqBody.gender);
        }
        if (_.has(reqBody, "others") === true) {
          reqBody.others = UtilsHelper.systemEncrypt(reqBody.others);
        }
        if (_.has(reqBody, "insurence_status") === true) {
          reqBody.insurence_status = UtilsHelper.systemEncrypt(reqBody.insurence_status);
        }
        if (_.has(reqBody, "reference_number") === true) {
          reqBody.reference_number = UtilsHelper.systemEncrypt(reqBody.reference_number);
        }
        if (_.has(reqBody, "address") === true) {
          reqBody.address = UtilsHelper.systemEncrypt(reqBody.address);
        }

        reqBody.dob = new Date(reqBody.dob)
        if (reqBody?.last_check_up_date) {
          reqBody.last_check_up_date = new Date(reqBody.last_check_up_date)
        }

        await Models.Patients.update(reqBody, { where: { id: checkPatient.id } });


        //logger
        userActivityLogger.info(UtilsHelper.getMessage(10021), { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, patientId: patientId, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

        const requestBodyKeys = Object.keys(req.body);

        dataAccessLogger.info(`${req?.user?.first_name} has edited the patient record for the Clinical note genration`, { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, patientId: checkPatient.id, tags: 'Data Access', data_acess: requestBodyKeys, request: { Method: req.method, originalUrl: req.originalUrl } } });


        _.assign(_retData, {
          data: UtilsHelper.cryptoJsObjectEncrypt(checkPatient),
          msgCode: 10021,
          msg: UtilsHelper.getMessage(10021),
        })
      } else {
        //sssss
        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: 50012,
          msg: UtilsHelper.getMessage(50012),
        })
      }
    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in PatientController.editPatient')
    }

    return this.sendResponse(res, _retData)
  }

  public async gePatientDeatil(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {

      //validate client Id
      const _params = req.params
      const patientId = _params.patientId
      const userId = req?.user?.id
      const _userRoleDeatilData: any = await Models.ClinicalUserRoleDetail.findOne({ where: { user_id: userId, status: 1 } })
      if (_.isEmpty(_userRoleDeatilData)) {
        throw new Error('USER_ROLE_DETAIL_NOT_FOUND')
      }
      const getPatient: any = await Models.Patients.findOne({
        where: { status: 1, id: patientId },
        attributes: [
          'id', 'status', 'clinic_id', 'user_id', 'dob', 'age', 'last_check_up_date', 'post_code', 'period', 'county_id', 'country_id', 'town_id', "additional_fields","referral_details",
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.name'), 'bytea'), key), 'name'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.gender'), 'bytea'), key), 'gender'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.first_name'), 'bytea'), key), 'first_name'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.last_name'), 'bytea'), key), 'last_name'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.phone'), 'bytea'), key), 'phone'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.email'), 'bytea'), key), 'email'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.nhs_number'), 'bytea'), key), 'nhs_number'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.insurence_status'), 'bytea'), key), 'insurence_status'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.reference_number'), 'bytea'), key), 'reference_number'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.address'), 'bytea'), key), 'address'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.others'), 'bytea'), key), 'others'],

        ],
        include: [
          {
            model: Models.Clinics,
            as: 'clinic',
            attributes: ['id', 'name', 'status'],
          },
          {
            model: Models.ClinicalUsers,
            as: 'users',
            attributes: ['id', 'first_name', 'last_name', 'email', 'phone', 'country', ['county_id', 'state'], ['town_id', 'city'], 'address'],
            include: [
              {
                model: Models.ClinicalSpecialization,
                as: 'specializations',
                attributes: ['id', 'name', 'description'],
              },
            ],
          },
          {
            model: Models.ClinicalInsurences,
            as: 'insurence',
            attributes: ['id', 'name', 'status'],
          }
        ],
      })

      if (getPatient) {

        //logger
        userActivityLogger.info(UtilsHelper.getMessage(10022), { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, patientId: patientId, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

        const requestBodyKeys = Object.keys(getPatient?.dataValues);

        dataAccessLogger.info(`${req?.user?.first_name} get the patient record for the Clinical note genration`, { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, patientId: patientId, tags: 'Data Access', data_acess: requestBodyKeys, request: { Method: req.method, originalUrl: req.originalUrl } } });

        _.assign(_retData, {
          data: UtilsHelper.cryptoJsObjectEncrypt(getPatient),
          msgCode: 10022,
          msg: UtilsHelper.getMessage(10022),
        })
      } else {
        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: 50012,
          msg: UtilsHelper.getMessage(50012),
        })
      }
    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in PatientController.gePatientDeatil')
    }

    return this.sendResponse(res, _retData)
  }

  public async patientList(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {
      const userId = req?.user?.id
      const _reqData = req.body
      const _params = req.params
      // check user data
      /*   const _userRoleDeatilData: any = await Models.ClinicalUserRoleDetail.findOne({ where: { user_id: userId, status: 1 } })
        if (_.isEmpty(_userRoleDeatilData)) {
          throw new Error('USER_ROLE_DETAIL_NOT_FOUND')
        } */

      const searchStr: any = _reqData?.searchStr

      let _offset = 0
      const _recordsLimit = 10

      const _filter = {
        status: 1,
        clinic_id: _reqData.clinic_id,
        [Op.or]: [],
      }


      if (!_.isEmpty(searchStr)) {

        _filter[Op.or].push(Models.sequelize.where(
          Models.sequelize.literal(
            `PGP_SYM_DECRYPT("Patients"."name"::bytea, '${key}')::text`
          ),
          {
            [Op.iLike]: `%${searchStr}%`,
          }
        ));

        _filter[Op.or].push(Models.sequelize.where(
          Models.sequelize.literal(
            `PGP_SYM_DECRYPT(email::bytea, '${key}')::text`
          ),
          {
            [Op.iLike]: `%${searchStr}%`,
          }
        ));

        _filter[Op.or].push(Models.sequelize.where(
          Models.sequelize.literal(
            `PGP_SYM_DECRYPT("Patients"."phone"::bytea, '${key}')::text`
          ),
          {
            [Op.iLike]: `%${searchStr}%`,
          }
        ));

        _filter[Op.or].push({ '$clinic.name$': { [Op.iLike]: `${searchStr}%` } });
        _filter[Op.or].push({ '$insurence.name$': { [Op.iLike]: `${searchStr}%` } });

        _filter[Op.or].push(Models.sequelize.where(
          Models.sequelize.literal(
            `PGP_SYM_DECRYPT(nhs_number::bytea, '${key}')::text`
          ),
          {
            [Op.iLike]: `%${searchStr}%`,
          }
        ));

      } else {
        delete _filter[Op.or]
      }



      if (_params.page && _.isInteger(_.parseInt(_params.page)) && _.parseInt(_params.page) > 0) {
        _offset = _recordsLimit * (_.parseInt(_params.page) - 1)
      }
      const { count, rows }: any = await Models.Patients.findAndCountAll({
        where: _filter,
        order: [['createdAt', 'DESC']],
        attributes: [
          'id', 'status', 'clinic_id', 'age', 'dob', 'referral_details', 'post_code', 'country_id', 'county_id', 'town_id',
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.gender'), 'bytea'), key), 'gender'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.name'), 'bytea'), key), 'name'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('first_name'), 'bytea'), key), 'first_name'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('last_name'), 'bytea'), key), 'last_name'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.phone'), 'bytea'), key), 'phone'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('email'), 'bytea'), key), 'email'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('nhs_number'), 'bytea'), key), 'nhs_number'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('insurence_status'), 'bytea'), key), 'insurence_status'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.reference_number'), 'bytea'), key), 'reference_number'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('others'), 'bytea'), key), 'others'],
          [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.address'), 'bytea'), key), 'address']
        ],
        limit: _recordsLimit,
        offset: _offset,
        include: [{
          model: Models.Clinics,
          as: 'clinic',
          attributes: ['id', 'name', 'status'],
        }, {
          model: Models.ClinicalInsurences,
          as: 'insurence',
          attributes: ['id', 'name', 'status'],
        }
        ]

      })

      //logger
      userActivityLogger.info(UtilsHelper.getMessage(10023), { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

      //const requestBodyKeys = Object.keys(rows);

      dataAccessLogger.info(`${req?.user?.first_name} get all the the patient record for the Clinical note genration`, { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, tags: 'Data Access', data_acess: ['name', 'first_name', 'last_name', 'phone', 'email', 'nhs_number', 'others', 'insurence'], request: { Method: req.method, originalUrl: req.originalUrl } } });


      _.assign(_retData, {
        data: UtilsHelper.cryptoJsObjectEncrypt({
          totalRecords: count,
          recordsPerPage: _recordsLimit,
          list: rows,
        }),
        msgCode: 10023,
        msg: UtilsHelper.getMessage(10023),
      })
    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in PatientController.patientList')
    }

    return this.sendResponse(res, _retData)
  }

  public async uploadFiles(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {
      const _params = req.params
      const type = _params.type
      const uploadFileData = await uploadFile(req, res)

      const _msgCode = 10023
      _.assign(_retData, {
        msgCode: 10023,
        data: UtilsHelper.cryptoJsObjectEncrypt(uploadFileData),
        msg: UtilsHelper.getMessage(10023),
      })
    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in PatientController.uploadFiles')
    }

    return this.sendResponse(res, _retData)
  }

  public async deletePatient(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {
      const reqBody = req.body
      //validate client Id
      const _params = req.params
      const patientId = _params.patientId
      const userId = req?.user?.id
      const deleted_reason = reqBody.deleted_reason


      const checkPatient: any = await Models.Patients.findOne({
        where: { status: 1, id: patientId },
      })

      if (checkPatient) {
        checkPatient.status = 9
        checkPatient.deleted_reason = deleted_reason
        checkPatient.deleted_by = userId
          ; (checkPatient.deleted_at = new Date()), await checkPatient.save()

        //delete note after patient delete

        const getCliniclNote: any = await Models.PatientClinicalNotesLetters.findAll({
          where: { status: { [Op.ne]: 9 }, patient_id: checkPatient.id }
        });

        if (getCliniclNote.length > 0) {
          for (const note of getCliniclNote) {
            await clinicalNoteHelper.deleteDocumentFroms3(note.document_url);
            await Models.PatientClinicalNotesLetters.update({ status: 9 }, { where: { id: note.id } });
            await Models.Task.destroy({ where: { letter_id: note.id } });

            await Models.Clinics.decrement('letter_count', {
              where: {
                clinic_id: checkPatient?.clinic_id
              }
            });

            dataAccessLogger.info(`${req?.user?.first_name} has deleted the patient note becuase no longer use in clinical note genration`, { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, patientId: patientId, tags: 'Data Access', data_acess: [], request: { Method: req.method, originalUrl: req.originalUrl, noteUrl: note.document_url } } });

          }
        }

        //logger
        userActivityLogger.info(UtilsHelper.getMessage(10025), { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, patientId: patientId, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

        dataAccessLogger.info(`${req?.user?.first_name} has deleted the patient record becuase no longer use in clinical note genration`, { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, patientId: patientId, tags: 'Data Access', data_acess: [], request: { Method: req.method, originalUrl: req.originalUrl } } });

        _.assign(_retData, {
          msgCode: 10025,
          msg: UtilsHelper.getMessage(10025),
        })
      } else {
        //sssss
        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: 50012,
          msg: UtilsHelper.getMessage(50012),
        })
      }
    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in PatientController.deletePatient')
    }

    return this.sendResponse(res, _retData)
  }

  public async uploadPatientCsv(req: express.Request, res: express.Response): Promise<void | any> {

    const _retData: IResponseObject = UtilsHelper.responseObject();
    try {

      const userId = req?.user?.id
      const _reqData = req.body

      if (req.files == undefined) {
        _.assign(_retData, {
          status: 'error',
          statusCode: 400,
          msgCode: 50038,
          msg: UtilsHelper.getMessage(50038),
        });
        return this.sendResponse(res, _retData)
      }

      const reqFiles: any = req?.files ? req.files[0] : null;

      const insuranceNames: any = await Models.ClinicalInsurences.findAll({
        attributes: [
          'id',
          [Models.sequelize.fn('LOWER', Models.sequelize.col('name')), 'name']
        ],
        where: { status: 1 },
        raw: true,
        order: [['name', 'ASC']],
      });

      const csvPatients = [];
      const patientsData = [];
      let path = "build/src/uploads/" + reqFiles.filename;
      if (process.env.NODE_ENV === 'development') {
        path = "src/uploads/" + reqFiles.filename;
      }
      const encodeArrayField = ["name", "gender", "reference_number", "address", "email", "phone", "first_name", "last_name", "nhs_number"];
      const matchedObjects = [];
      const unmatchedObjects = [];
      const currentDate = new Date();
      const emailRegex: any = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\\.,;:\s@\"]+\.)+[^<>()[\]\\.,;:\s@\"]{2,})$/i;
      // Supported date formats
      const supportedDateFormats = ['DD/MM/YYYY', 'DD-MM-YYYY', 'DD.MM.YYYY', 'YYYY-MM-DD'];




      createReadStream(path)
        .pipe(parse({ headers: true }))
        .on("error", (error) => {
          throw error.message;
        })
        .on("data", (row) => {
          let formattedDate;
          // Try parsing the date with supported formats
          for (const format of supportedDateFormats) {
            const parsedDate = moment(row.dob, format, true);
            if (parsedDate.isValid()) {
              formattedDate = parsedDate.format('YYYY-MM-DD');
              break;
            }
          }

          if (!row.first_name || !row.last_name || !row.dob) {
            row.reason = 'Mandatory validation failed';
            matchedObjects.push(row);
          } else if (row.first_name.length > 20 || row.last_name.length > 20) {
            row.reason = 'First name and last name should not exceed 20 characters';
            matchedObjects.push(row);
          } else if (!/^[a-zA-Z0-9 ]+$/.test(row.first_name)) {
            row.reason = 'First name should not contain special characters';
            matchedObjects.push(row);
          } else if (!/^[a-zA-Z0-9 ]+$/.test(row.last_name)) {
            row.reason = 'Last name should not contain special characters';
            matchedObjects.push(row);
          } else if (row?.email && !emailRegex.test(row.email)) {
            row.reason = 'Invalid email format';
            matchedObjects.push(row);
          } else if (!formattedDate) {
            row.reason = 'Invalid date of birth format';
            matchedObjects.push(row);
          } else {
            row.status = 1;
            row.name = `${row.first_name} ${row.last_name}`;
            if (row.country) {
              row.country_id = row.country;
            }

            if (row.state) {
              row.county_id = row.state;
            }

            if (row.town) {
              row.town_id = row.town;
            }

            row.insurance_id = null;

            row.dob = formattedDate

            if (row.insurance) {

              const insuranceName = row.insurance.toLowerCase();

              const foundObject = insuranceNames.find(object => object.name === insuranceName);

              row.insurance_id = foundObject.id;
            }
            delete row.country;
            delete row.state;
            delete row.town;

            csvPatients.push(row);
          }

        })
        .on("end", async () => {

          const _filter = {
            status: 1,
            clinic_id: _reqData.clinic_id,
            email: { [Op.ne]: null },
          }

          const dbPatients = await Models.Patients.findAll({
            where: _filter,
            order: [['createdAt', 'DESC']],
            raw: true,
            attributes: [
              'id', 'status', 'clinic_id',
              [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('first_name'), 'bytea'), key), 'first_name'],
              [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('last_name'), 'bytea'), key), 'last_name'],
              [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('Patients.phone'), 'bytea'), key), 'phone'],
              [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('email'), 'bytea'), key), 'email'],

            ],
          });



          // checking duplicate email from csv
          const uniqueEmails = new Set();

          //checking duplicate value from db
          for (const object1 of csvPatients) {
            let matchingObject = null;
            for (const object2 of dbPatients) {
              if (object2.email && object1.email === object2.email) {
                matchingObject = object2;
                break;
              }
            }

            if (matchingObject) {
              object1.reason = "Patient email already exist";
              const mergedObject = { ...object1, ...matchingObject };
              matchedObjects.push(mergedObject);

            } else {

              object1.clinic_id = _reqData.clinic_id;
              object1.user_id = userId,

              object1.name = (_.has(object1, 'name')) ? UtilsHelper.systemEncrypt(object1.name) : null;
              object1.gender = (_.has(object1, 'gender')) ? UtilsHelper.systemEncrypt(object1.gender) : null;
              object1.email = (_.has(object1, 'email')) ? UtilsHelper.systemEncrypt(object1.email) : null;
              object1.reference_number = (_.has(object1, 'reference_number')) ? UtilsHelper.systemEncrypt(object1.reference_number) : null;
              object1.address = (_.has(object1, 'address')) ? UtilsHelper.systemEncrypt(object1.address) : null;
              object1.phone = (_.has(object1, 'phone')) ? UtilsHelper.systemEncrypt(object1.phone) : null;
              object1.first_name = (_.has(object1, 'first_name')) ? UtilsHelper.systemEncrypt(object1.first_name) : null;
              object1.last_name = (_.has(object1, 'last_name')) ? UtilsHelper.systemEncrypt(object1.last_name) : null;
              object1.nhs_number = (_.has(object1, 'nhs_number')) ? UtilsHelper.systemEncrypt(object1.nhs_number) : null;
              object1.others = (_.has(object1, 'others')) ? UtilsHelper.systemEncrypt(object1.others) : null;
              object1.insurence_status = (_.has(object1, 'insurence_status')) ? UtilsHelper.systemEncrypt(object1.insurence_status) : null;

              // Rearrange the date components to match the expected format

              object1.dob = new Date(object1.dob);
              const birthDate = object1.dob;
             // const birthDate = object1.dob; // Replace with the actual birth date
              const millisecondsDifference = currentDate?.getTime() - birthDate?.getTime();
              // Calculate age in years
              const ageInYears = Math.floor(millisecondsDifference / (1000 * 60 * 60 * 24 * 365));

              // Calculate age in months
              const ageInMonths = Math.floor((ageInYears * 12) + ((currentDate?.getMonth() - birthDate?.getMonth()) + (currentDate?.getDate() < birthDate?.getDate() ? -1 : 0)));

              // Calculate age in days
              const ageInDays = Math.floor((millisecondsDifference / (1000 * 60 * 60 * 24)) % 365);


              if (ageInYears != 0) {
                object1.period = 'Years'
              }

              if (ageInYears == 0 && ageInMonths != 0) {
                object1.period = 'Months'
              }
              if (ageInYears == 0 && ageInMonths == 0) {
                object1.period = 'Days'
              }
              object1.age = (ageInYears) ? ageInYears : ageInMonths ? ageInMonths : ageInDays;
              object1.last_check_up_date = new Date();


              unmatchedObjects.push(object1);
            }
          }
          const filteredArray = unmatchedObjects.filter(obj => obj.first_name);

          await Models.Patients.bulkCreate(filteredArray)
            .then(() => {

              //logger
              userActivityLogger.info(UtilsHelper.getMessage(10065), { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });


              dataAccessLogger.info(`${req?.user?.first_name} has created patient bulk record for the Clinical note genration`, { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, tags: 'Data Access', request: { Method: req.method, originalUrl: req.originalUrl } } });


              fs.unlink(path, (err) => {
                if (err) throw err;

                _.assign(_retData, {
                  data: UtilsHelper.cryptoJsObjectEncrypt({
                    notInserted: matchedObjects,
                    inserted: unmatchedObjects.length
                  }),
                  msgCode: 10065,
                  msg: UtilsHelper.getMessage(10065),
                });

                return this.sendResponse(res, _retData);

              });

            })
            .catch((error) => {

              fs.unlink(path, (err) => {
                if (err) throw err;
              });

              _.assign(_retData, {
                statusCode: 500,
                status: 'error',
                msg: "Couldn't import data into database!: " + error.message,
              });
              return this.sendResponse(res, _retData)
            });
        });

    } catch (err: any) {

      console.log(err);
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: "Failed to upload the file: " + err.message,
      })

      this.logErrors(err, 'Error in PatientController.uploadPatientCsv');
      return this.sendResponse(res, _retData)
    }

  }
}

const patientController = new PatientController()

export default patientController
