import * as express from 'express'
import * as UtilsHelper from '../../../../helpers/utils.helper'
import { IResponseObject } from '../../../../helpers/utils.interface'
import BaseController from '../../../../helpers/BaseController'
import { S3Client, GetObjectCommand, S3ClientConfig } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import _ from 'lodash'
import fs from 'fs'
const path = require('path');
import Models from '../../../../database/models'
import { Op, Sequelize } from 'sequelize'
import * as TokenHelper from '../../../../helpers/token.helper'
import * as PasswordHelper from '../../../../helpers/password.helper'
const { saveDoc, deleteFileFroms3 } = require('../../../../helpers/fileUpload.helper')
import * as userHelper from './helper'
import { userActivityLogger, securityLogger, dataAccessLogger, auditLogger } from '../../../../helpers/logger.helpers';
import {
	audioConsultationMinutesUsedForOwner,
	clinicUserCount, countLLMPromptsForClinic,
	countNotesTemplatesForClinic,
	totalLettersCountForClinic
} from "./helper";

const s3Configuration: S3ClientConfig = {
	credentials: {
		accessKeyId: process.env.S3_ACCESS_KEY,
		secretAccessKey: process.env.S3_ACCESS_SECRET
	},
	region: process.env.S3_REGION,
};
const s3 = new S3Client(s3Configuration);

class UserSettingController extends BaseController {
	constructor() {
		super()
		this.uploadProfilePic = this.uploadProfilePic.bind(this);
		this.uploadHeaderImage = this.uploadHeaderImage.bind(this);
		this.uploadFooterImage = this.uploadFooterImage.bind(this)
		this.updateUserClinicalNoteSetting = this.updateUserClinicalNoteSetting.bind(this);
		this.updateUserSettingImages = this.updateUserSettingImages.bind(this);
		this.doctorDashboard = this.doctorDashboard.bind(this);
		this.ownerDashboard = this.ownerDashboard.bind(this);
		this.getUserSettingData = this.getUserSettingData.bind(this);
		this.uploadImage = this.uploadImage.bind(this);
	}

	public async uploadProfilePic(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const logedInUserId = req?.user?.id
			const reqFiles: any = req.files;
			const file: any = reqFiles ? reqFiles[0] : null;
			const data: any = {
				location: null,
				fileName: null,
				originalName: null

			}
			const timestamp = Date.now();

			if (file) {
				const ext = path.extname(file.originalname);
				const image = "profile_" + timestamp + ext;
				const image_name = "backend/clinical-note/" + logedInUserId + "/" + image;
				const uploadResult = await saveDoc(file.path, image_name);

				if (uploadResult && uploadResult?.Key != "") {
					data.fileName = image_name;
					data.originalName = image;
					const command = new GetObjectCommand({ Bucket: process.env.S3_BUCKET_NAME, Key: image_name });
					const url = await getSignedUrl(s3, command, { expiresIn: 6 * 24 * 60 * 60 }); // expires in seconds
					data.location = url;

					const userInfo = await Models.ClinicalUsers.findOne({
						where: { id: logedInUserId }
					});

					const _update: any = await Models.ClinicalUsers.update({ profile_image_url: image_name }, { where: { id: logedInUserId } });

					if (userInfo && userInfo.profile_image_url != null) {
						await deleteFileFroms3(userInfo?.profile_image_url);
					}

					await userHelper.deleteTempImage();

					//logger
					userActivityLogger.info('User profile updated', { additionalInfo: { logedInUserId: req?.user?.id, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, image: image_name, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

					const clientIP = req.ip;
					auditLogger.info(`successful updated profile pic from `, { additionalInfo: { user_ip: clientIP, logedInUserId: req?.user?.id, email: req?.user?.email, tags: 'Audit log', status: "success", request: { Method: req.method, originalUrl: req.originalUrl } } });
				}
			} else {
				await Models.ClinicalUsers.update({ profile_image_url: null }, { where: { id: logedInUserId } });
			}

			_.assign(_retData, {
				msgCode: 10040,
				data: UtilsHelper.cryptoJsObjectEncrypt(data),
				msg: UtilsHelper.getMessage(10040),
			})

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in UserSettingController.uploadFiles')
		}

		return this.sendResponse(res, _retData)
	}
	public async uploadHeaderImage(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const logedInUserId = req?.user?.id
			const reqFiles: any = req.files;
			const _reqBody = req.body;


			const file: any = reqFiles ? reqFiles[0] : null;
			const data: any = {
				location: null,
				fileName: null,
				originalName: null
			}
			//type check
			const fileName = reqFiles[0].originalname;

			//type check
			const timestamp = Date.now();

			const clinicalUserSettingsData: any = await Models.ClinicalUserSettings.findOne({
				where: { status: { [Op.ne]: 9 }, clinic_id: _reqBody.clinic_id, user_id: logedInUserId },
			});

			if (file && clinicalUserSettingsData) {
				const ext = path.extname(file.originalname);
				const image = "img_" + timestamp + ext;
				const image_name = "backend/clinical-note/" + logedInUserId + "/" + image;
				const uploadResult = await saveDoc(file.path, image_name);



				if (uploadResult) {
					data.fileName = image_name;
					data.originalName = image

					const command = new GetObjectCommand({ Bucket: process.env.S3_BUCKET_NAME, Key: image_name });
					const url = await getSignedUrl(s3, command, { expiresIn: 6 * 24 * 60 * 60 }); // expires in seconds
					data.location = url;

					const _update: any = await Models.ClinicalUserSettings.update({ header_image: image_name }, { where: { id: clinicalUserSettingsData.id } });


					if (clinicalUserSettingsData && clinicalUserSettingsData?.header_image != null) {
						const resss = await deleteFileFroms3(clinicalUserSettingsData.header_image);

					}

					await userHelper.deleteTempImage();


					//logger
					userActivityLogger.info('User header image updated', { additionalInfo: { logedInUserId: req?.user?.id, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, image: image_name, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

					const clientIP = req.ip;
					auditLogger.info(`successful updated  header image from `, { additionalInfo: { user_ip: clientIP, logedInUserId: req?.user?.id, email: req?.user?.email, tags: 'Audit log', status: "success", request: { Method: req.method, originalUrl: req.originalUrl } } });

				}
			} else {
				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50012,
					msg: UtilsHelper.getMessage(50012),
				})
				return this.sendResponse(res, _retData)
			}


			_.assign(_retData, {
				msgCode: 10040,
				data: UtilsHelper.cryptoJsObjectEncrypt(data),
				msg: UtilsHelper.getMessage(10040),
			})

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in UserSettingController.uploadHeaderImage')
		}

		return this.sendResponse(res, _retData)
	}

	public async uploadFooterImage(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const logedInUserId = req?.user?.id
			const reqFiles: any = req.files;
			const _reqBody = req.body;
			const file: any = reqFiles ? reqFiles[0] : null;
			const data: any = {
				location: null,
				fileName: null,
				originalName: null,
			}
			//type check
			const fileName = reqFiles[0].originalname;
			const regex = /\.(jpeg)$/i;
			if (!regex.test(fileName)) {
				_.assign(_retData, {
					statusCode: 400,
					status: 'error',
					msg: UtilsHelper.getMessage(50027, 'en'),
				})
				return this.sendResponse(res, _retData)
			}
			//type check

			const timestamp = Date.now();

			const clinicalUserSettingsData: any = await Models.ClinicalUserSettings.findOne({
				where: { status: { [Op.ne]: 9 }, clinic_id: _reqBody.clinic_id, user_id: logedInUserId },
			});

			if (file && clinicalUserSettingsData) {
				const ext = path.extname(file.originalname);
				const image = "img_" + timestamp + ext;
				const image_name = "backend/clinical-note/" + logedInUserId + "/" + image;
				const uploadResult = await saveDoc(file.path, image_name);
				if (uploadResult) {
					data.fileName = image_name;
					data.originalName = image

					const command = new GetObjectCommand({ Bucket: process.env.S3_BUCKET_NAME, Key: image_name });
					const url = await getSignedUrl(s3, command, { expiresIn: 6 * 24 * 60 * 60 }); // expires in seconds
					data.location = url;



					if (clinicalUserSettingsData) {
						const _update: any = await Models.ClinicalUserSettings.update({ footer_image: image_name }, { where: { id: clinicalUserSettingsData.id } });
						if (clinicalUserSettingsData?.footer_image != null) {
							await deleteFileFroms3(clinicalUserSettingsData.footer_image);
						}
					} else {
						_.assign(_retData, {
							status: 'error',
							statusCode: 400,
							msgCode: 50012,
							msg: UtilsHelper.getMessage(50012),
						})
						return this.sendResponse(res, _retData)
					}

					await userHelper.deleteTempImage();


					//logger
					userActivityLogger.info('User footer image updated', { additionalInfo: { logedInUserId: req?.user?.id, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, image: image_name, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

					const clientIP = req.ip;
					auditLogger.info(`successful updated  footer image from `, { additionalInfo: { user_ip: clientIP, logedInUserId: req?.user?.id, email: req?.user?.email, tags: 'Audit log', status: "success", request: { Method: req.method, originalUrl: req.originalUrl } } });
				}
			} else {
				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50012,
					msg: UtilsHelper.getMessage(50012),
				})
				return this.sendResponse(res, _retData)
			}


			_.assign(_retData, {
				msgCode: 10040,
				data: UtilsHelper.cryptoJsObjectEncrypt(data),
				msg: UtilsHelper.getMessage(10040),
			})

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in UserSettingController.uploadFooterImage')
		}

		return this.sendResponse(res, _retData)
	}

	public async updateUserClinicalNoteSetting(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const logedInUserId = req?.user?.id
			const reqBody = req.body;
			const clinicalUserSettingsData: any = await Models.ClinicalUserSettings.findOne({
				where: { status: { [Op.ne]: 9 }, user_id: logedInUserId, clinic_id: reqBody.clinic_id },
			});

			if (clinicalUserSettingsData) {
				const _updateData: any = {
					letter_header_template: reqBody.letter_header_template,
					letter_footer_template: reqBody.letter_footer_template,
					update_count: clinicalUserSettingsData.update_count + 1
				}

				if (_.has(reqBody, "header_image") === true) {
					_updateData.header_image = reqBody.header_image;
				}
				if (_.has(reqBody, "footer_image") === true) {
					_updateData.footer_image = reqBody.footer_image;
				}


				const _update: any = await Models.ClinicalUserSettings.update(_updateData, { where: { id: clinicalUserSettingsData.id } });

				//logger
				userActivityLogger.info(UtilsHelper.getMessage(10041, 'en'), { additionalInfo: { logedInUserId: req?.user?.id, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

				const clientIP = req.ip;
				auditLogger.info(`successful updated note setting from `, { additionalInfo: { user_ip: clientIP, logedInUserId: req?.user?.id, email: req?.user?.email, tags: 'Audit log', status: "success", request: { Method: req.method, originalUrl: req.originalUrl } } });

				const _msgCode = 10041
				_.assign(_retData, { msgCode: _msgCode, msg: UtilsHelper.getMessage(_msgCode, 'en') })
			} else {
				_.assign(_retData, {
					statusCode: 400,
					status: 'error',
					msg: UtilsHelper.getMessage(50012, 'en'),
				})
			}

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in UserSettingController.updateUserClinicalNoteSetting')
		}
		return this.sendResponse(res, _retData)
	}

	public async updateUserSettingImages(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const reqFiles: any = req.files;
			const logedInUserId = req?.user?.id
			const reqBody = req.body;

			const clinicalUserSettingsData: any = await Models.ClinicalUserSettings.findOne({
				where: { status: { [Op.ne]: 9 }, user_id: logedInUserId, clinic_id: reqBody.clinic_id },
			});

			if (clinicalUserSettingsData) {
				const file: any = reqFiles ? reqFiles[0] : null;
				const path = file.path;
				const imageBuffer = fs.readFileSync(path);
				const _updateData: any = {};

				if (reqBody.type === 'header') {
					_updateData.header_image = imageBuffer
				} else {
					_updateData.footer_image = imageBuffer
				}

				const _update: any = await Models.ClinicalUserSettings.update(_updateData, { where: { id: clinicalUserSettingsData.id } });

				await userHelper.deleteTempImage();

				const _msgCode = 10043
				//logger
				userActivityLogger.info(UtilsHelper.getMessage(10043, 'en'), { additionalInfo: { logedInUserId: req?.user?.id, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

				const clientIP = req.ip;
				auditLogger.info(`successful updated settings image from `, { additionalInfo: { user_ip: clientIP, logedInUserId: req?.user?.id, email: req?.user?.email, tags: 'Audit log', status: "success", request: { Method: req.method, originalUrl: req.originalUrl } } });


				_.assign(_retData, {
					msgCode: _msgCode,
					data: UtilsHelper.cryptoJsObjectEncrypt({
						image: imageBuffer.toString('base64'),
						//data: clinicalUserSettingsData
					}), msg: UtilsHelper.getMessage(_msgCode, 'en')
				})

			} else {
				_.assign(_retData, {
					statusCode: 400,
					status: 'error',
					msg: UtilsHelper.getMessage(50012, 'en'),
				})
			}

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in UserSettingController.updateUserSettingImages')
		}
		return this.sendResponse(res, _retData)
	}

	public async doctorDashboard(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const logedInUserId = req?.user?.id
			const reqBody = req.body;
			const clinic_id = reqBody.clinic_id;
			const letter_type = (reqBody.letter_type) ? reqBody.letter_type : 'ALL'

			const totalPatients = await userHelper.totalPatients(logedInUserId, clinic_id);
			const countsData: any = await userHelper.printDownloadEmailedCount(logedInUserId, clinic_id);
			const countsDaily: any = await userHelper.dailyLettersCount(logedInUserId, clinic_id, letter_type);
			const countsWeekly: any = await userHelper.weeklyLettersCount(logedInUserId, clinic_id, letter_type);
			const countsMonthly: any = await userHelper.monthlyLettersCount(logedInUserId, clinic_id, letter_type);

			const _msgCode = 10048;

			//logger
			userActivityLogger.info(UtilsHelper.getMessage(10048, 'en'), { additionalInfo: { logedInUserId: req?.user?.id, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

			_.assign(_retData, {
				msgCode: _msgCode, data: UtilsHelper.cryptoJsObjectEncrypt({
					totalPatientCount: totalPatients,
					printDownloadEmailedCount: countsData,
					letters: { countsDaily, countsWeekly, countsMonthly }
				}), msg: UtilsHelper.getMessage(_msgCode, 'en')
			})

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in UserSettingController.doctorDashboard')
		}
		return this.sendResponse(res, _retData)
	}

	public async ownerDashboard(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const logedInUserId = req?.user?.id
			const reqBody = req.body;
			const clinic_id = reqBody.clinic_id;
			const letter_type = (reqBody.letter_type) ? reqBody.letter_type : 'ALL'

			const usersId = [];

			if (_.has(reqBody, "clinical_specializations_id") === true && reqBody.clinical_specializations_id != 0) {
				const _filter: any = {
					status: { [Op.ne]: 9 },
					owner_user_id: logedInUserId,
					clinical_specializations_id: reqBody.clinical_specializations_id
				}
				const usersData: any = await Models.ClinicalUsers.findAll({
					where: _filter,
				});

				if (usersData.length > 0) {
					for (const user of usersData) {
						usersId.push(user.id)
					}
				}

			}

			const totalPatients = await userHelper.totalPatientsForOwner(clinic_id);
			const countsUsers: any = await userHelper.clinicUserCount(logedInUserId, clinic_id);

			const countsData: any = await userHelper.printDownloadEmailedCountForOwner(clinic_id, letter_type, usersId);
			const countsDaily: any = await userHelper.dailyLettersCountForOwner(clinic_id, letter_type, usersId);
			const countsWeekly: any = await userHelper.weeklyLettersCountForOwner(clinic_id, letter_type, usersId);
			const countsMonthly: any = await userHelper.monthlyLettersCountForOwner(clinic_id, letter_type, usersId);

			const countDocuments: any = await userHelper.totalLettersCountForClinic(clinic_id, '');
			const countAudioMinutesInSeconds: any = await userHelper.audioConsultationMinutesUsedForOwner(clinic_id, logedInUserId);
			const countTemplates: any = await userHelper.countLLMPromptsForClinic(clinic_id);
			const countDesigns: any = await userHelper.countNotesTemplatesForClinic(clinic_id);

			const _msgCode = 10048;

			//logger
			userActivityLogger.info(UtilsHelper.getMessage(10048, 'en'), { additionalInfo: { logedInUserId: req?.user?.id, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, dashboard: 'ownerDashboard', tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

			_.assign(_retData, {
				msgCode: _msgCode, data: UtilsHelper.cryptoJsObjectEncrypt({
					// totalPatientCount: totalPatients,
					countsUsers: countsUsers?.userCount,
					// printDownloadEmailedCount: countsData,
					audioMinutesUsed: countAudioMinutesInSeconds,
					totalDesigns: countDesigns,
					totalTemplates: countTemplates,
					lettersCount: countDocuments,
					totalPatients
				}), msg: UtilsHelper.getMessage(_msgCode, 'en')
			})

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in UserSettingController.ownerDashboard')
		}
		return this.sendResponse(res, _retData)
	}

	public async getUserSettingData(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const logedInUserId = req?.user?.id;
			const reqBody = req.body;
			const clinic_id = reqBody.clinic_id;

			const clinicalUserSettingsData: any = await Models.ClinicalUserSettings.findOne({
				where: { status: { [Op.ne]: 9 }, user_id: logedInUserId, clinic_id: clinic_id },
			});

			if (clinicalUserSettingsData) {

				if (clinicalUserSettingsData?.letter_header_template && clinicalUserSettingsData.letter_header_template != null) {
					clinicalUserSettingsData.letter_header_template = await userHelper.updateImgSrcWithSignedUrls(clinicalUserSettingsData.letter_header_template, 'assets/images/dummy-logo.png');
				}

				if (clinicalUserSettingsData?.letter_footer_template && clinicalUserSettingsData.letter_footer_template != null) {
					clinicalUserSettingsData.letter_footer_template = await userHelper.updateImgSrcWithSignedUrls(clinicalUserSettingsData.letter_footer_template, 'assets/images/dummy-signature.png');
				}



				const _msgCode = 10063;

				//logger
				userActivityLogger.info('User setting data fetched', { additionalInfo: { logedInUserId: req?.user?.id, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

				_.assign(_retData, {
					msgCode: _msgCode,
					data: UtilsHelper.cryptoJsObjectEncrypt(clinicalUserSettingsData),
					msg: UtilsHelper.getMessage(_msgCode, 'en')
				});
			} else {
				_.assign(_retData, {
					statusCode: 400,
					status: 'error',
					msg: UtilsHelper.getMessage(50012, 'en'),
				})
			}

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in UserSettingController.getUserSettingData')
		}
		return this.sendResponse(res, _retData)
	}

	/**
	* Method to upload file and image
	*/
	public async uploadImage(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const logedInUserId = req?.user?.id;
			const reqFiles: any = req.files;


			const maxSize = 1 * 1024 * 1024; // 1 MB

			const file: any = reqFiles ? reqFiles[0] : null;
			const data: any = {
				url: null,
				uploaded: 0,
				fileName: null

			}

			//type check
			const fileName = reqFiles[0].originalname;
			const regex = /\.(jpe?g|png|jpg|jpeg|gif|webp)$/i;

			if (!regex.test(fileName)) {
				return res.status(200).json({
					uploaded: 0,
					error: {
						message: UtilsHelper.getMessage(50027, 'en')
					}
				})
			}
			//type check

			// Check file size
			if (file.size > maxSize) {
				return res.status(200).json({
					uploaded: 0,
					error: {
						message: UtilsHelper.getMessage(50037, 'en')
					}
				})
			}




			//type check
			const timestamp = Date.now();

			if (file) {

				const ext = path.extname(file.originalname);
				const image = "img_" + timestamp + ext;
				const image_name = "backend/clinical-note/" + logedInUserId + "/" + image;
				const uploaded = await saveDoc(file.path, image_name);

				if (uploaded) {
					data.uploaded = 1;
					data.fileName = image;

					const command = new GetObjectCommand({ Bucket: process.env.S3_BUCKET_NAME, Key: image_name });
					const url = await getSignedUrl(s3, command, { expiresIn: 6 * 24 * 60 * 60 }); // expires in seconds
					data.url = url;

					await userHelper.deleteTempImage();

					return res.status(200).json(data);
				} else {
					return res.status(200).json({
						uploaded: 0,
						error: {
							message: "Something went wrong in file upload"
						}
					})
				}

			} else {
				return res.status(200).json({
					uploaded: 0,
					error: {
						message: "file is missing"
					}
				})
			}


		} catch (err: any) {

			this.logErrors(err, 'Error in UserSettingController.uploadImage');
			return res.status(400).json({
				uploaded: 0,
				"error": err?.message
			})
		}

		//return this.sendResponse(res, _retData)
	}


}

const userSettingController = new UserSettingController()

export default userSettingController