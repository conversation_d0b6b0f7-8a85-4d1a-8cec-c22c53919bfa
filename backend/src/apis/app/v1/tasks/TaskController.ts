import Models from '../../../../database/models'
import { IResponseObject } from "../../../../helpers/utils.interface";
import * as UtilsHelper from '../../../../helpers/utils.helper'
import * as express from "express";
import { getClinicalUserType, getDoctorsTasks, getSecretaryTasks, getTaskCountByStatus } from "../../../../apis/app/v1/tasks/helper";
import BaseController from "../../../../helpers/BaseController";
import { Op } from "sequelize";
import { userActivityLogger } from "../../../../helpers/logger.helpers";
import _ from "lodash";

const key = process.env.AES_KEY;

class TaskController extends BaseController {
    constructor() {
        super();

        this.getAllTasks = this.getAllTasks.bind(this);
    }

    async getAllTasksCount(req: express.Request, res: express.Response) {
        const _retData: IResponseObject = UtilsHelper.responseObject();

        try {
            const tasks = await getTaskCountByStatus(req?.user?.id);
            
            if (tasks) {
                _.assign(_retData, {
                    msgCode: 10102,
                    data: UtilsHelper.cryptoJsObjectEncrypt(tasks),
                    msg: UtilsHelper.getMessage(10102),
                })
                res.status(200).json(_retData)
            } else {
                _.assign(_retData, {
                    msgCode: 50049,
                    data: UtilsHelper.cryptoJsObjectEncrypt(tasks),
                    msg: UtilsHelper.getMessage(50049),
                })
                res.status(404).json(_retData)
            }

        } catch (err) {
            console.error(err);
            res.status(500).json({ message: 'Server Error' });
        }
    }

    async getAllTasks(req: express.Request, res: express.Response) {
        const _retData: IResponseObject = UtilsHelper.responseObject()
        const page = req.query.page || 1;
        const pageSize = req.query.pageSize || 10;
        const searchStr = req.query.searchStr || '';
        const sortOrder = req.query.sortOrder || 'desc';
        const sortField = req.query.sortBy || 'id';
        const status = req.query.status || '';
        // const search = req.query.search || '';

        try {
            const userType = getClinicalUserType(req?.user?.role_id)
            let tasks;
            switch (userType) {
                case 1:
                case 2:
                    tasks = await getDoctorsTasks(req?.user?.id, page, pageSize, searchStr, sortOrder, sortField, status)
                    break;
                case 3:
                    tasks = await getSecretaryTasks(req?.user?.id, page, pageSize)
                    break;
                default:
                    console.error("Cannot get Tasks for this user type")
                    break;
            }

            if (tasks) {
                _.assign(_retData, {
                    msgCode: 10102,
                    data: UtilsHelper.cryptoJsObjectEncrypt(tasks),
                    msg: UtilsHelper.getMessage(10102),
                })
                res.status(200).json(_retData)
            } else {
                _.assign(_retData, {
                    msgCode: 50049,
                    data: UtilsHelper.cryptoJsObjectEncrypt(tasks),
                    msg: UtilsHelper.getMessage(50049),
                })
                res.status(404).json(_retData)
            }

        } catch (err) {
            console.error(err);
            res.status(500).json({ message: 'Server Error' });
        }
    }

    async getTaskById(req, res) {
        try {
            const userType = getClinicalUserType(req?.user?.role_id)
            let query;
            switch (userType) {
                case 1:
                case 2:
                    query = {
                        where: {
                            id: req?.params?.id,
                            [Op.or]: [
                                { assigned_to: req?.user?.id },
                                { task_owner: req?.user?.id }
                            ]
                        },
                        include: [
                            {
                                model: Models.ClinicalUsers,
                                as: 'assignedUser',
                                attributes: ['id', 'first_name', 'last_name'],
                            },
                            {
                                model: Models.ClinicalUsers,
                                as: 'ownerUser',
                                attributes: ['id', 'first_name', 'last_name'],
                            },
                            {
                                model: Models.ClinicalUsers,
                                as: 'lastModifiedUser',
                                attributes: ['id', 'first_name', 'last_name'],
                            },
                            {
                                model: Models.PatientClinicalNotesLetters,
                                as: 'letter',
                                attributes: ['id', 'clinical_note', 'letter_type'],
                                include: [
                                    {
                                        model: Models.Patients,
                                        as: 'patient',
                                        attributes: ['id', 'name',
                                            [Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('letter->patient.name'), 'bytea'), key), 'name'],
                                        ] // Include patient's name
                                    }
                                ]
                            },
                            {
                                model: Models.TaskAssignment,
                                as: 'assignments',
                                attributes: ['id', 'staff_id', 'date_assigned', 'notes'],
                                include: [
                                    {
                                        model: Models.ClinicalUsers,
                                        as: 'assignedStaff',
                                        attributes: ['id', 'first_name', 'last_name', 'email']
                                    }
                                ]
                            }
                        ]
                    }
                    break;
                case 3:
                    query = {
                        where: {
                            id: req?.params?.id,
                            assigned_to: req?.user?.id
                        }
                    }
                    break;
                default:
                    console.error("Cannot get Tasks for this user")
                    res.status(404).json({ message: 'No Tasks Found' });
                    return
            }
            const task = await Models.Task.findOne(query);
            if (!task) {
                return res.status(404).json({ message: 'Task not found' });
            }
            const _retData = {
                msgCode: 10102,
                data: UtilsHelper.cryptoJsObjectEncrypt(task),
                msg: UtilsHelper.getMessage(10102),
            };

            res.status(200).json(_retData);
        } catch (err) {
            console.error(err);
            res.status(500).json({ message: 'Server Error' });
        }
    }

    async createTask(req, res) {
        const _retData: IResponseObject = UtilsHelper.responseObject()
        const userId = req?.user?.id
        try {
            const userType = getClinicalUserType(req?.user?.role_id)
            if (![1, 2].includes(userType)) {
                console.error("Permission Denied");
                res.status(403).json({ message: 'Cannot Create Task' });
                return
            }
            const tasksBody = {
                task_name: req.body.task_name,
                description: req.body.description,
                letter_id: req.body.letter_id,
                priority: req.body.priority,
                status: req.body.status,
                assigned_to: req.body.assigned_to,
                task_owner: req.body.task_owner,
                last_modified_by: req.body.last_modified_by
            };
            console.log(req.user)
            const _createdData = await Models.Task.create(tasksBody);

            if (_createdData) {
                const taskAssignmentBody = {
                    task_id: _createdData.id,
                    staff_id: req.body.assigned_to,
                    date_assigned: new Date(),  // or use req.body.date_assigned if provided
                    notes: req.body.notes  // or any default value if notes is optional
                };

                const _taskAssignmentData = await Models.TaskAssignment.create(taskAssignmentBody);

                userActivityLogger.info(UtilsHelper.getMessage(10101), {
                    additionalInfo: {
                        user_id: userId,
                        first_name: req?.user?.first_name,
                        last_name: req?.user?.last_name,
                        email: req?.user?.email,
                        templateId: _createdData.id,
                        tags: 'User Activity',
                        request: { Method: req.method, originalUrl: req.originalUrl }
                    }
                });
                _.assign(_retData, {
                    data: UtilsHelper.cryptoJsObjectEncrypt(_createdData),
                    msgCode: 10101,
                    msg: UtilsHelper.getMessage(10101),
                })
            }


            res.status(201).json(_retData);
        } catch (err) {
            console.error(err);
            _.assign(_retData, {
                statusCode: 500,
                status: 'error',
                msg: err.message,
            })
            res.status(500).json({ message: 'Server Error' });
        }
    }

    async updateTask(req, res) {
        try {
            const task = await Models.Task.findByPk(req.params.id);
            if (!task) {
                return res.status(404).json({ message: 'Task not found' });
            }

            if (task.assigned_to !== req.body.assigned_to) {
                const taskAssignmentBody = {
                    // task_id: task.id,
                    staff_id: req.body.assigned_to,
                    date_assigned: new Date(),  // or use req.body.date_assigned if provided
                    notes: req.body.notes  // or any default value if notes is optional
                };

                const _taskAssignment = await Models.TaskAssignment.findOne({
                    where: {
                        task_id: task.id
                    }
                });

                await _taskAssignment.update(taskAssignmentBody);
                await _taskAssignment.save();
            } else {
                const taskAssignmentBody = {
                    notes: req.body.notes  // or any default value if notes is optional
                };

                const _taskAssignment = await Models.TaskAssignment.findOne({
                    where: {
                        task_id: task.id
                    }
                });

                await _taskAssignment.update(taskAssignmentBody);
                await _taskAssignment.save();
            }
            await task.update(req.body);
            await task.save();
            console.log('req.body')
            console.log(req.body)
            res.json(task);
        } catch (err) {
            console.error(err);
            res.status(500).json({ message: 'Server Error' });
        }
    }

    async deleteTask(req, res) {
        try {
            const task = await Models.Task.findByPk(req.params.id);
            if (!task) {
                return res.status(404).json({ message: 'Task not found' });
            }
            await task.destroy();
            res.json({ message: 'Task deleted successfully' });
        } catch (err) {
            console.error(err);
            res.status(500).json({ message: 'Server Error' });
        }
    }
}

const taskController = new TaskController()

export default taskController