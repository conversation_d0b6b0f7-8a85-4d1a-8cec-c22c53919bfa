import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../../../../helpers/token.helper'
import * as <PERSON>ailHelper from '../../../../helpers/email.helper'
import _ from 'lodash'
const { Op, Sequelize } = require('sequelize')
import Models from '../../../../database/models'
import * as UtilsHelper from '../../../../helpers/utils.helper'
import stripeClient from '../../../../services/Stripe'

export async function sendUserBuyCreditEmail(userData: any) {
	const _emailData = {
		toEmail: userData.email,
		data: userData,
	}
	// Send conatct us email from admin
	await EmailHelper.userBuyCreditEmail(_emailData).then(data => console.log(data))

	return true
}

export async function confirmUserCreatePurchase(userBuyCreditData: any, userId: any, userName: any, email: any) {


	const user = await Models.ClinicalUsers.findByPk(userId);

	const credit_documents = user.credit_documents + userBuyCreditData?.quantity;

	// Record the credit purchase in your database
	await Models.ClinicalUsers.update(
		{ credit_documents: credit_documents },
		{ where: { id: userId } }
	);

	//update user subscription table
	await Models.ClinicalUserBuyCredit.update({status: 1},{where: { user_id: userId, id: userBuyCreditData?.id },});

	// Log the transaction
	await Models.ClinicalUserTransactions.create({
		user_id: userId,
		transaction_type: 2,
		documents_created: userBuyCreditData?.quantity,
		transaction_date: new Date(),
		currency: userBuyCreditData?.currency.toLowerCase(),
		credit_amount: userBuyCreditData?.total_amount,
		action: 1,
	});

	await this.sendUserBuyCreditEmail({
		name: userName,
		email: email,
		credit: userBuyCreditData?.quantity,
		currency: userBuyCreditData?.currency,
		amount: userBuyCreditData?.total_amount,
	});


	return true
}