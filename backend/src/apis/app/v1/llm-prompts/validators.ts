import Joi from 'joi'
import express from 'express'
import _, { get } from 'lodash'

import * as UtilsHelper from '../../../../helpers/utils.helper'

const Validators: any = {
  addPromptValid: Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    prompt: Joi.string().required(),
    is_favourite: Joi.boolean(),
    clinic_id: Joi.string().required(),
  }),
  getPromptListValid: Joi.object({
    clinic_id: Joi.string().required(),
    page: Joi.string().optional(),
    pageSize: Joi.string().optional(),
    searchStr: Joi.string().min(3),
    sortBy: Joi.string().valid('id', 'name').required(),
    sortOrder: Joi.string().valid('asc', 'desc').required(),
    type: Joi.string().valid('system', 'custom'),
  }),
  getPromptDetailValid: Joi.object({
    promptId: Joi.number().integer().positive().required(),
  }),
  deletePromptValid: Joi.object({
    promptId: Joi.number().integer().positive().required(),
    clinicId: Joi.string().required(),
  }),
  editPromptValid: Joi.object({
    promptId: Joi.number().integer().positive().required(),
    name: Joi.string().required(),
    description: Joi.string().required(),
    prompt: Joi.string().required(),
    is_favourite: Joi.boolean(),
    clinic_id: Joi.string().required(),
  }),
}

export default function Validator(func: string) {
  return async function Validator(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const _reqBody = (req.body?.encrypet) ? UtilsHelper.cryptoJsObjectDcrypt(req.body.encrypet) : req.body;
      const _reqData = _.assign(_reqBody, req.params, req.query);
      const validated = await Validators[func].validateAsync(_reqData, { abortEarly: false })
      req.body = validated
      next()
    } catch (err: any) {
      const _er: any = {}
      if (err.isJoi) {
        err.details.forEach((d: any) => {
          const _key: string = d.context.key
          _er[_key] = d.message
        })
      }

      const _retData = UtilsHelper.responseObject()
      _retData.status = 'error'
      _retData.statusCode = 400
      _retData.msg = UtilsHelper.getMessage('50020', 'en')
      _retData.msgCode = '50020'
      _retData.data = _er

      return UtilsHelper.cRes(res, _retData)
    }
  }
}