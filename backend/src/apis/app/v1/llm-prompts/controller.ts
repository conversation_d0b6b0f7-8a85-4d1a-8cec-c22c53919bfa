import * as express from 'express'
import _ from 'lodash'

import BaseController from '../../../../helpers/BaseController'
import { IResponseObject } from '../../../../helpers/utils.interface';
import * as UtilsHelper from '../../../../helpers/utils.helper'
import Models from '../../../../database/models'
import { userActivityLogger } from '../../../../helpers/logger.helpers';
import { Op } from 'sequelize';

class LlmPromptsController extends BaseController {
  constructor() {
    super()

    this.createPrompt = this.createPrompt.bind(this)
    this.listPrompts = this.listPrompts.bind(this)
    this.getPromptDetail = this.getPromptDetail.bind(this)
    this.deletePrompt = this.deletePrompt.bind(this)
    this.editPrompt = this.editPrompt.bind(this)
  }

  public async createPrompt(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject();
    try {
      const reqBody = req.body;
      const userId = req?.user?.id;
      const clinicId = reqBody.clinic_id;

      // Fetch user object from Clinical Users
      const clinicalUser = await Models.ClinicalUsers.findOne({ where: {  id: userId } });

      if (!clinicalUser) {
        _.assign(_retData, {
          statusCode: 404,
          status: 'error',
          msg: 'Clinical user not found',
        });
        return this.sendResponse(res, _retData);
      }

      // Check if custom template limit is greater than 0
      if (clinicalUser.remaining_custom_templates <= 0) {
        _.assign(_retData, {
          statusCode: 400,
          status: 'error',
          msg: 'Custom template limit exceeded',
        });
        return this.sendResponse(res, _retData);
      }

      // Prepare LLM Prompt object
      const llmPrompt: any = {
        user_id: userId,
        clinic_id: clinicId,
        name: reqBody.name,
        description: reqBody.description,
        prompt: reqBody.prompt,
        is_favourite: reqBody.is_favourite || false,
      };

      // Create the LLM Prompt
      const _createdData = await Models.ClinicalUserLlmPrompts.create(llmPrompt);

      if (_createdData) {
        // Decrease the custom template limit by 1
        await Models.ClinicalUsers.update(
          { remaining_custom_templates: clinicalUser.remaining_custom_templates - 1 },
          { where: { id: userId} }
        );

        // Log user activity
        userActivityLogger.info(UtilsHelper.getMessage(10100), {
          additionalInfo: {
            user_id: userId,
            first_name: req?.user?.first_name,
            last_name: req?.user?.last_name,
            email: req?.user?.email,
            promptId: _createdData.id,
            tags: 'User Activity',
            request: { Method: req.method, originalUrl: req.originalUrl },
          },
        });

        _.assign(_retData, {
          data: UtilsHelper.cryptoJsObjectEncrypt(_createdData),
          msgCode: 10100,
          msg: UtilsHelper.getMessage(10110),
        });
      }
    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      });

      this.logErrors(err, 'Error in PromptController.createPrompt');
    }

    return this.sendResponse(res, _retData);
  }

  public async listPrompts(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject();
    try {
      const clinicId = req?.body?.clinic_id
      const page: any = req.query.page || 1;
      const pageSize: any = req.query.pageSize || 10;
      const searchStr: any = req?.query?.searchStr || '';
      const sortOrder: any = req?.query?.sortOrder || 'desc';
      const sortField: any = req?.query?.sortBy || 'id';
      const type: any = req.query?.type;

      const _filters: any = {
        [Op.and]: [
          {
            [Op.or]: [
              {clinic_id: clinicId},
              {clinic_id: null},
            ]
          },
          {
            [Op.or]: [
              {
                [Op.and]: [
                  {
                    [Op.or]: [
                      {name: {[Op.iLike]: `%${searchStr}%`}},
                      {description: {[Op.iLike]: `%${searchStr}%`}}
                    ]
                  },
                  {clinic_id: clinicId}
                ]
              },
              {
                [Op.and]: [
                  {
                    [Op.or]: [
                      {name: {[Op.iLike]: `%${searchStr}%`}},
                      {description: {[Op.iLike]: `%${searchStr}%`}}
                    ]
                  },
                  {clinic_id: null}
                ]
              }

            ]
          },
          { status: 1 }
        ]
      };
      if (type) {
        if (type == 'custom') {
          _filters[Op.and].push({clinic_id: {[Op.ne]: null}});
        } else {
          _filters[Op.and].push({clinic_id: {[Op.eq]: null}});

        }
      }

      const order: string[][] = [[sortField, sortOrder]];

      const { rows: _prompts, count: totalCount } = await Models.ClinicalUserLlmPrompts.findAndCountAll({
        attributes: ['id', 'name', 'description', 'is_favourite', 'clinic_id'],
        where: _filters,
        order: order,
        offset: (page - 1) * pageSize, // Calculate the offset based on the page number and page size
        limit: pageSize, // Limit the number of tasks per page
      });

      if (_prompts) {
        const response = { prompts: _prompts, totalCount };
        _.assign(_retData, {
          data: UtilsHelper.cryptoJsObjectEncrypt(response),
          msgCode: 10111,
          msg: UtilsHelper.getMessage(10111),
        });
      }
    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      });

      this.logErrors(err, 'Error in PromptController.listPrompts');
    }
    return this.sendResponse(res, _retData);
  }

  public async getPromptDetail(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {
      const promptId = req.params.promptId
      const _prompt = await Models.ClinicalUserLlmPrompts.findOne({
        where: {
          id: promptId,
        },
      })
      
      if(_prompt) {
        _.assign(_retData, {
          data: UtilsHelper.cryptoJsObjectEncrypt(_prompt),
          msgCode: 10112,
          msg: UtilsHelper.getMessage(10112),
        })
      }
    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in PromptController.getPromptDetail')
    }
    return this.sendResponse(res, _retData)
  }

  public async deletePrompt(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject();
    try {
      const userId = req?.user?.id;
      const promptId = req.params.promptId;
      const clinicId = req.params.clinicId;

      // Fetch the prompt
      const _prompt = await Models.ClinicalUserLlmPrompts.findOne({
        where: {
          id: promptId,
          clinic_id: clinicId,
        },
      });

      if (!_prompt) {
        _.assign(_retData, {
          msgCode: 50052,
          msg: UtilsHelper.getMessage(50052),
          statusCode: 404,
        });
        return this.sendResponse(res, _retData);
      }

      // Fetch the clinical user to update the template limit
      const clinicalUser = await Models.ClinicalUsers.findOne({ where: { id: userId} });

      if (!clinicalUser) {
        _.assign(_retData, {
          statusCode: 404,
          status: 'error',
          msg: 'Clinical user not found',
        });
        return this.sendResponse(res, _retData);
      }

      // Soft delete the prompt (status: 9)
      await _prompt.update({ status: 9 });

      // get current user plan details
      const userPlan = await Models.SubscriptionPlan.findByPk(clinicalUser.subscription_plan_id);

      // Increment the custom_template_limit by 1 if it doesn't exceed the current plan limit
      if (clinicalUser.remaining_custom_templates < userPlan.custom_templates_limit) {
        await Models.ClinicalUsers.update(
          { remaining_custom_templates: clinicalUser.remaining_custom_templates + 1 },
          { where: {  id: userId } }
        );
      }

      _.assign(_retData, {
        msgCode: 10113,
        msg: UtilsHelper.getMessage(10113),
      });

    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      });

      this.logErrors(err, 'Error in PromptController.deletePrompt');
    }

    return this.sendResponse(res, _retData);
  }

  public async editPrompt(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject()
    try {
      const reqBody = req.body
      const userId = req?.user?.id
      const promptId = req.params.promptId
      const _prompt = await Models.ClinicalUserLlmPrompts.findOne({
        where: {
          user_id: userId,
          id: promptId,
        },
      })

      if(_prompt) {
        _prompt.name = reqBody.name
        if(reqBody.description) {
          _prompt.description = reqBody.description;
        }
        _prompt.prompt = reqBody.prompt
        _prompt.is_favourite = reqBody.is_favourite
        await _prompt.save()
        _.assign(_retData, {
          data: UtilsHelper.cryptoJsObjectEncrypt(_prompt),
          msgCode: 10114,
          msg: UtilsHelper.getMessage(10114),
        })
      }
    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      })

      this.logErrors(err, 'Error in PromptController.editPrompt')
    }
    return this.sendResponse(res, _retData)
  }
}

const promptsController = new LlmPromptsController()
export default promptsController
