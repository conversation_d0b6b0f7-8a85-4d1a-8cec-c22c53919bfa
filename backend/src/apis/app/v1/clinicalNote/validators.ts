import Joi, {string} from 'joi'
import express from 'express'
import _ from 'lodash'
import * as UtilsHelper from '../../../../helpers/utils.helper'

const Validators: any = {
	createNoteValid: Joi.object({
		clinic_id: Joi.string().required(),
		patient_id: Joi.number().integer(),
		clinical_note: Joi.string().allow(''),
		letter_type: Joi.string().required().valid('Clinical Letter', 'Clinical Summary'),
		prompt_id: Joi.number().integer().required(),
		image_base64: Joi.string().optional(),
		is_patient_medical_history_checked: Joi.boolean().optional().default(false),
		//header_content: Joi.string().required(),
		//footer_content: Joi.string().required(),
		//approval_rating: Joi.number().integer().min(1).max(5).required(),
	}),

	updateRemainingSecondsValid: Joi.object({
		clinic_id: Joi.string().required(),
		remaining_seconds: Joi.number().integer().required(),
		userId: Joi.number().integer().positive().required(),
	}),

	reCreateNoteValid: Joi.object({
		clinicalNoteId: Joi.number().integer().positive().required(),
		clinical_note: Joi.string().allow(''),
		prompt_id: Joi.number().integer().required(),
		image_base64: Joi.string().optional(),
		is_patient_medical_history_checked: Joi.boolean().optional().default(false),
	}),

	EditNoteValid: Joi.object({
		clinicalNoteId: Joi.number().integer().positive().required(),
		patient_id: Joi.alternatives().try(
			Joi.number().integer().allow(null),
			Joi.string().valid('null').custom((value, helpers) => {
				// Convert the string "null" to actual null
				return null;
			}, 'null conversion')
		).allow(null),

		updated_body_content: Joi.string().required(),
		updated_body_content_text: Joi.string().required(),

		template_id: Joi.alternatives().try(
			Joi.number().integer().allow(null),
			Joi.string().valid('null').custom((value, helpers) => {
				// Convert the string "null" to actual null
				return null;
			}, 'null conversion')
		).allow(null),
		header_content: Joi.string().allow(''),
		footer_content: Joi.string().allow(''),
		feedback: Joi.number().valid(0, 1),
		approval_rating: Joi.number().integer().min(1).max(5),
		edit_view_letter: Joi.number().valid(0, 1).required(),
		system_genrated_body_content: Joi.string().optional(),
	}),
	PinNoteValid: Joi.object({
		clinic_id: Joi.string().required(),
		clinicalNoteId: Joi.number().integer().positive().required(),
		isPinned: Joi.boolean().required(),
	}),
	getNoteDetailValid: Joi.object({
		clinicalNoteId: Joi.number().integer().positive().required(),
	}),

	getNoteDataValid: Joi.object({
		userId: Joi.number().integer().positive().required(),
	}),

	noteListValid: Joi.object({
		searchStr: Joi.string().min(3),
		clinic_id: Joi.string().required(),
		page: Joi.number().integer().min(1).required(),
    sortBy: Joi.string().valid('id', 'createdAt', 'name', 'template', 'prompt').required(),
		sortOrder: Joi.string().valid('asc', 'desc').required(),
	}),

	printShareDownlaodValidation: Joi.object({
		clinicalNoteId: Joi.number().integer().required(),
		is_letter_printed: Joi.boolean().optional(),
		is_letter_emailed: Joi.boolean().optional(),
		is_letter_downloaded: Joi.boolean().optional(),
	}).or('is_letter_printed', 'is_letter_emailed', 'is_letter_downloaded') // At least one of these keys must be in the object to be valid.
		.required(),

	shareNoteValid: Joi.object({
		email: Joi.string().trim().required(),
		subject: Joi.string().required(),
		body: Joi.string().required(),
		id: Joi.number().integer().required(),
	}),
	patientNoteListValid: Joi.object({
		searchStr: Joi.string().min(3),
		startDate: Joi.string(),
		endDate: Joi.string(),
		sortBy: Joi.string().valid('id', 'createdAt', 'name', 'template', 'prompt').required(),
		sortOrder: Joi.string().valid('asc', 'desc').required(),
		clinic_id: Joi.string().required(),
		patient_id: Joi.number().integer().min(1).required(),
		page: Joi.number().integer().min(1).required(),
	}),
	noteDeleteValid: Joi.object({
		clinicalNoteId: Joi.number().integer().positive().required(),
		clinic_id: Joi.string().required(),
	}),
	bulkDeleteValid: Joi.object({
		clinicalNoteIds: Joi.array()
			.items(Joi.number().integer().positive().required())
			.min(1)
			.required()
			.messages({
				'array.min': 'At least one clinicalNoteId must be provided.',
			}),
		clinic_id: Joi.string().required(),
	}),
	sampleNoteValid: Joi.object({
		name: Joi.string().required(),
		description: Joi.string().required(),
		prompt: Joi.string().required(),
		clinical_note: Joi.string().required(),
	}),
	generatePDFPreviewValid: Joi.object({
		headerHtml: Joi.string().optional().allow(""),
		bodyHtml: Joi.string().optional().allow(""),
		footerHtml: Joi.string().optional().allow(""),
	}),
}

export default function Validator(func: string) {
	return async function Validator(
		req: express.Request,
		res: express.Response,
		next: express.NextFunction
	) {
		try {
			const _reqBody = (req.body?.encrypet) ? UtilsHelper.cryptoJsObjectDcrypt(req.body.encrypet) : req.body;
			const _reqData = _.assign(_reqBody, req.params, req.query);
			const validated = await Validators[func].validateAsync(_reqData, { abortEarly: false })
			req.body = validated
			next()
		} catch (err: any) {
			const _er: any = {}
			if (err.isJoi) {
				err.details.forEach((d: any) => {
					const _key: string = d.context.key
					_er[_key] = d.message
				})
			}

			const _retData = UtilsHelper.responseObject()
			_retData.status = 'error'
			_retData.statusCode = 400
			_retData.msg = UtilsHelper.getMessage('50020', 'en')
			_retData.msgCode = '50020'
			_retData.data = _er

			return UtilsHelper.cRes(res, _retData)
		}
	}
}
