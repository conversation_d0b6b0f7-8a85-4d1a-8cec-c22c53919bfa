import * as express from 'express'
import { S3<PERSON>lient, GetObjectCommand, S3ClientConfig } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import _ from 'lodash'
import { v4 as uuidv4 } from 'uuid';
import html_to_pdf from 'html-pdf-node';

import * as UtilsHelper from '../../../../helpers/utils.helper'
import { IResponseObject } from '../../../../helpers/utils.interface'
import BaseController from '../../../../helpers/BaseController'
import { saveDoc } from '../../../../helpers/fileUpload.helper';
import { sendMail } from "../../../../services/Mail";
import Models from '../../../../database/models'
import { Op, Sequelize } from 'sequelize'
import * as clinicalNoteHelper from './helper';
import { userActivity<PERSON>ogger, securityLogger, dataAccessLogger, auditLogger } from '../../../../helpers/logger.helpers';
import { systemGeneratedMedicalHistory } from "./helper";

import puppeteer from 'puppeteer';      // use puppeteer directly

//import * as patientHelper from './helper'
const s3Configuration: S3ClientConfig = {
	credentials: {
		accessKeyId: process.env.S3_ACCESS_KEY,
		secretAccessKey: process.env.S3_ACCESS_SECRET
	},
	region: process.env.S3_REGION,
};
const s3 = new S3Client(s3Configuration);
class ClinicalNoteController extends BaseController {
	constructor() {
		super()
		this.createNote = this.createNote.bind(this);
		this.reCreateNoteByNoteId = this.reCreateNoteByNoteId.bind(this);
		this.getSignedUrl = this.getSignedUrl.bind(this);
		this.noteDeatilFromClinicalNoteId = this.noteDeatilFromClinicalNoteId.bind(this);
		this.editNoteFromClinicalNoteId = this.editNoteFromClinicalNoteId.bind(this);
		this.noteListForDoctor = this.noteListForDoctor.bind(this);
		this.maxRatedNoteForAiml = this.maxRatedNoteForAiml.bind(this);
		this.updatePrintShareDownlaod = this.updatePrintShareDownlaod.bind(this);
		this.shareNoteViaEmail = this.shareNoteViaEmail.bind(this);
		this.noteListForPatient = this.noteListForPatient.bind(this);
		this.noteListForOwner = this.noteListForOwner.bind(this);
		//this.noteDeatilFromId = this.noteDeatilFromId.bind(this);
		this.noteDeleteFromId = this.noteDeleteFromId.bind(this);
		this.sampleNote = this.sampleNote.bind(this);
		this.bulkDeleteNotes = this.bulkDeleteNotes.bind(this);
		this.updateRemainingMinutes = this.updateRemainingMinutes.bind(this)
		this.downloadClinicalNotePdf = this.downloadClinicalNotePdf.bind(this);
	}
	public async updateRemainingMinutes(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject();
		try {
			const { clinic_id, remaining_seconds, userId } = req.body;

			const clinicalUser = await Models.ClinicalUsers.findOne({ where: {  id: userId } });

			if (!clinicalUser) {
				_.assign(_retData, {
					statusCode: 404,
					status: 'error',
					msg: 'Clinical user not found',
				});
				return this.sendResponse(res, _retData);
			}
			const outDatedMinutes = clinicalUser.remaining_audio_consultation_minutes - remaining_seconds;
			console.log(outDatedMinutes)
			await Models.ClinicalUsers.update(
				{ remaining_audio_consultation_minutes: remaining_seconds ,
								audio_minutes_used_in_seconds: clinicalUser.audio_minutes_used_in_seconds + outDatedMinutes
				},
				{ where: {  id: userId} }
			);

			_.assign(_retData, {
				statusCode: 200,
				status: 'success',
				msg: 'Remaining minutes updated successfully',
			});

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			});

			this.logErrors(err, 'Error in updateRemainingMinutes');
		}

		return this.sendResponse(res, _retData);
	}

	public async createNote(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const _reqBody = req.body;
			_reqBody.image_base64 = JSON.parse(_reqBody.image_base64)
			const logedInUserId = req?.user?.id
			_reqBody.is_letter_printed = 0;
			//_reqBody.clinic_id = null;
			const user_id = req?.user?.id
			const patient_id = _reqBody.patient_id;
			const is_patient_medical_history_checked = _reqBody.is_patient_medical_history_checked;

			const _prompt = await Models.ClinicalUserLlmPrompts.findOne({
				where: {
					id: _reqBody.prompt_id,
				},
      })

			//check document limit exceed or not
			// const isExceeded = await clinicalNoteHelper.hasExceededDocumentLimit(req?.user);
			// if (isExceeded == 0) {
			// 	//console.log('Document limit exceeded.');
			// 	_.assign(_retData, {
			// 		statusCode: 400,
			// 		status: 'error',
			// 		msg: UtilsHelper.getMessage(50045, 'en'),
			// 	});
			//
			// 	return this.sendResponse(res, _retData)
			// }

			// get medical history
			let medicalHistory = null;
			const medicalHistoryContent = [];
			let shouldUseMedicalHistory = false
			if (patient_id && is_patient_medical_history_checked) {
				shouldUseMedicalHistory = true;
				medicalHistory = await Models.PatientMedicalHistory.findAll({
					where: {
						patient_id: patient_id,
					},
				});

				if (_.isEmpty(medicalHistory) && !_reqBody.clinical_note) {
					_.assign(_retData, {
						statusCode: 400,
						status: 'error',
						msg: UtilsHelper.getMessage(50051, 'en'),
					});
					return this.sendResponse(res, _retData)
				}
				medicalHistory?.forEach((_mh: any) => {
					medicalHistoryContent.push(_mh.content);
				})
			}

			//end check document limit exceed or not

			_reqBody.user_id = logedInUserId;
			_reqBody.is_letter_emailed = 0;
			_reqBody.is_letter_downloaded = 0;
			_reqBody.status = 0;
			const generated_content = await clinicalNoteHelper.systemGenratedContnet(user_id, _prompt.name, _prompt.description, _prompt.prompt,  _reqBody.clinical_note, _reqBody.image_base64, medicalHistoryContent, shouldUseMedicalHistory);
			_reqBody.system_genrated_body_content = generated_content.letter;
			console.log(generated_content);
			_reqBody.system_genrated_body_content = _reqBody?.system_genrated_body_content?.replaceAll(/\n/g, '<br>');
			_reqBody.updated_body_content = _reqBody.system_genrated_body_content;
			_reqBody.attempt_generate_letter_count = 1;
			delete _reqBody.image_base64;
			delete _reqBody.is_patient_medical_history_checked;

			const _createdData = await Models.PatientClinicalNotesLetters.create(_reqBody);

      //decrement one document
			// const decrementDocuments = await clinicalNoteHelper.decrementDocuments(req?.user);

			// if (!decrementDocuments){
			// 	_.assign(_retData, {
			// 		statusCode: 400,
			// 		status: 'error',
			// 		msg: UtilsHelper.getMessage(50046, 'en'),
			// 	});
			// 	return this.sendResponse(res, _retData)
			// }
			//decrement one document

			if (_createdData) {
				//logger
				userActivityLogger.info(UtilsHelper.getMessage(10036), { additionalInfo: { user_id: logedInUserId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, clinicalNoteId: _createdData.id, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

				_.assign(_retData, {
					msgCode: 10036,
					data: UtilsHelper.cryptoJsObjectEncrypt(_createdData),
					msg: UtilsHelper.getMessage(10036),
				});

			}
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in ClinicalNoteController.createNote')
		}

		return this.sendResponse(res, _retData)
	}

	public async noteDeatilFromClinicalNoteId(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const logedInUserId = req?.user?.id
			const _params = req.params;
			const _reqBody = req.body;
			const clinicalNoteId = _params.clinicalNoteId;
			const key = process.env.AES_KEY;


			const getCliniclNote: any = await Models.PatientClinicalNotesLetters.findOne({
				where: { status: { [Op.ne]: 9 }, id: clinicalNoteId },
				include: [
					{
						model: Models.Patients,
						as: 'patient',
						attributes: ['id', 'period', 'age', 'dob', 'last_check_up_date', 'post_code', ['country_id', 'country'], ['county_id', 'state'], ['town_id', 'city'], "additional_fields", "referral_details",
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.name'), 'bytea'), key), 'name'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.first_name'), 'bytea'), key), 'first_name'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.last_name'), 'bytea'), key), 'last_name'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.gender'), 'bytea'), key), 'gender'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.email'), 'bytea'), key), 'email'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.address'), 'bytea'), key), 'address'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.phone'), 'bytea'), key), 'phone'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.nhs_number'), 'bytea'), key), 'nhs_number'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.reference_number'), 'bytea'), key), 'reference_number'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.insurence_status'), 'bytea'), key), 'insurence_status'],
						],
						include: [{
							model: Models.ClinicalUsers,
							as: 'users',
							attributes: ['id', 'first_name', 'last_name', 'email', 'phone', 'country', ['county_id', 'state'], ['town_id', 'city'], 'address'],
							include: [
								{
									model: Models.ClinicalSpecialization,
									as: 'specializations',
									attributes: ['id', 'name', 'description'],
								},
							],
						},
						{
							model: Models.ClinicalInsurences,
							as: 'insurence',
							attributes: ['name']
						}]
					},
					{
						model: Models.ClinicalUserSettings,
						as: 'userSetting',
						attributes: ['id', 'email_subject_template', 'email_body_template', 'letter_header_template', 'letter_footer_template', 'header_image', 'footer_image', 'status']
					}, {
						model: Models.Clinics,
						as: 'clinic',
						attributes: ['id', 'name', 'status']
					}
				],
			})

			if (getCliniclNote) {

				if (getCliniclNote?.userSetting?.header_image && getCliniclNote.userSetting.header_image != null) {
					const command = new GetObjectCommand({ Bucket: process.env.S3_BUCKET_NAME, Key: getCliniclNote.userSetting.header_image });
					const headerUrl = await getSignedUrl(s3, command, { expiresIn: 6 * 24 * 60 * 60 });
					getCliniclNote.userSetting.header_image = headerUrl
				}

				if (getCliniclNote?.userSetting?.footer_image && getCliniclNote.userSetting.footer_image != null) {

					const command = new GetObjectCommand({ Bucket: process.env.S3_BUCKET_NAME, Key: getCliniclNote.userSetting.footer_image });
					const footerUrl = await getSignedUrl(s3, command, { expiresIn: 6 * 24 * 60 * 60 });
					getCliniclNote.userSetting.footer_image = footerUrl
				}

				let url = "";

				if (getCliniclNote.document_url != null) {
					const command = new GetObjectCommand({ Bucket: process.env.S3_BUCKET_NAME, Key: getCliniclNote.document_url });
					url = await getSignedUrl(s3, command, { expiresIn: 6 * 24 * 60 * 60 }); // expires in seconds
				}

				getCliniclNote.setDataValue('documentS3Url', url);
				getCliniclNote.setDataValue('maxGenerateLetterCount', parseInt(process.env.MAX_GENERATE_LETTER_COUNT));

				getCliniclNote.maxGenerateLetterCount = parseInt(getCliniclNote.maxGenerateLetterCount)



				//logger
				userActivityLogger.info(UtilsHelper.getMessage(10037), { additionalInfo: { user_id: logedInUserId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, clinicalNoteId: clinicalNoteId, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

				_.assign(_retData, {
					data: UtilsHelper.cryptoJsObjectEncrypt(getCliniclNote),
					msgCode: 10037,
					msg: UtilsHelper.getMessage(10037),
				})
			} else {
				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50012,
					msg: UtilsHelper.getMessage(50012),
				})
			}

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in ClinicalNoteController.noteDeatilFromClinicalNoteId')
		}

		return this.sendResponse(res, _retData)
	}

	public async reCreateNoteByNoteId(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const user_id = req?.user?.id
			const _params = req.params;
			const _reqBody = req.body;
			let patient_id = _reqBody.patient_id;
			const clinicalNoteId = _params.clinicalNoteId;
			_reqBody.image_base64 = JSON.parse(_reqBody.image_base64);

			const getCliniclNote: any = await Models.PatientClinicalNotesLetters.findOne({
				where: { status: { [Op.ne]: 9 }, id: clinicalNoteId }
			});

			if (getCliniclNote) {
				const _prompt = await Models.ClinicalUserLlmPrompts.findOne({
					where: {
						id: _reqBody.prompt_id,
					},
				})

				if (!patient_id) {
					patient_id = getCliniclNote.patient_id
				}

				// get medical history
				let medicalHistory = null;
				const medicalHistoryContent = [];
				let shouldUseMedicalHistory = false
				if (patient_id && _reqBody.is_patient_medical_history_checked) {
					shouldUseMedicalHistory = true;
					medicalHistory = await Models.PatientMedicalHistory.findAll({
						where: {
							patient_id: patient_id,
						},
					});

					if (_.isEmpty(medicalHistory) && !_reqBody.clinical_note) {
						_.assign(_retData, {
							statusCode: 400,
							status: 'error',
							msg: UtilsHelper.getMessage(50051, 'en'),
						});
						return this.sendResponse(res, _retData)
					}

					medicalHistory?.forEach(_mh => {
						medicalHistoryContent.push(_mh.content);
					})
				}

				const generated_content = await clinicalNoteHelper.systemGenratedContnet(user_id, _prompt.name, _prompt.description, _prompt.prompt,  _reqBody.clinical_note, _reqBody.image_base64, medicalHistoryContent, shouldUseMedicalHistory);
				_reqBody.system_genrated_body_content = generated_content.letter;
				// _reqBody.system_genrated_body_content = await clinicalNoteHelper.systemGenratedContnet(user_id, _prompt.name, _prompt.description, _prompt.prompt, _reqBody.clinical_note, _reqBody.image_base64);
				_reqBody.updated_body_content = _reqBody.system_genrated_body_content;
				_reqBody.attempt_generate_letter_count = getCliniclNote.attempt_generate_letter_count + 1;
				delete _reqBody.image_base64;
				await Models.PatientClinicalNotesLetters.update(_reqBody, { where: { id: getCliniclNote.id } });

				getCliniclNote.attempt_generate_letter_count = _reqBody.attempt_generate_letter_count
				getCliniclNote.system_genrated_body_content = _reqBody.system_genrated_body_content
				//logger
				userActivityLogger.info(UtilsHelper.getMessage(10062), { additionalInfo: { user_id: user_id, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, clinicalNoteId: getCliniclNote.id, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

				_.assign(_retData, {
					msgCode: 10062,
					data: UtilsHelper.cryptoJsObjectEncrypt(getCliniclNote),
					msg: UtilsHelper.getMessage(10062),
				});

			} else {
				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50012,
					msg: UtilsHelper.getMessage(50012),
				})
			}

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in ClinicalNoteController.reCreateNoteByNoteId')
		}

		return this.sendResponse(res, _retData)
	}

	public async editNoteFromClinicalNoteId(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const _reqBody = req.body;
			const user_id = req?.user?.id
			const patient_id = _reqBody.patient_id;
			_reqBody.status = 1;
			if (_reqBody.patient_id === 'null') {
				_reqBody.patient_id = null;
			}
			if (_reqBody.template_id === 'null') {
				_reqBody.template_id = null;
			}
			const _params = req.params
			const clinicalNoteId = _params.clinicalNoteId;
			const edit_view_letter = _reqBody.edit_view_letter;
			const files = req.files;
			const uniqueId = uuidv4();
			const pdfUrl = `backend/clinical-letters/${uniqueId}.pdf`;
			// const {filename: pdfFilename, path: pdfFilePath } = files[0];

			// use helper function to generate pdf file and use that here
			const generatedPdfPath = await clinicalNoteHelper.generatePdfWithHtmlPdfNode(
				user_id,
				_reqBody.header_content || '',
				_reqBody.updated_body_content || '',
				_reqBody.footer_content || ''
			);

			const getCliniclNote: any = await Models.PatientClinicalNotesLetters.findOne({
				where: { status: { [Op.ne]: 9 }, id: clinicalNoteId }
			})
			if (getCliniclNote) {
				await Models.PatientClinicalNotesLetters.update({
					updated_body_content: _reqBody.updated_body_content,
					system_genrated_body_content: _reqBody.updated_body_content,
				}, { where: { id: getCliniclNote.id } });
				if(getCliniclNote.document_url != null) {
					await clinicalNoteHelper.deleteDocumentFroms3(getCliniclNote.document_url);
				}
				// const uploadRes: any = await clinicalNoteHelper.convertInPdf(user_id, _reqBody.updated_body_content, clinicalNoteId, edit_view_letter);
				const uploadedPdf = await saveDoc(generatedPdfPath, `backend/clinical-letters/${uniqueId}.pdf`);
				_reqBody.document_url = pdfUrl;
				await Models.PatientClinicalNotesLetters.update(_reqBody, { where: { id: getCliniclNote.id } });
				const _updatedData = await Models.PatientClinicalNotesLetters.findOne({
					where: { status: { [Op.ne]: 9 }, id: clinicalNoteId }
				});
				await clinicalNoteHelper.updateCountNote(getCliniclNote?.clinic_id);
				// Create Medical History
				if (_reqBody.patient_id) {
					const medicalHistory = await Models.PatientMedicalHistory.findOne({
						where: {
							patient_id: patient_id,
						},
					});
					const medicalHistoryContent = medicalHistory?.content
					const history = await systemGeneratedMedicalHistory(user_id,  _reqBody.updated_body_content, medicalHistoryContent)
					const _medicalHistory = await Models.PatientMedicalHistory.create({
						patient_id: patient_id,
						user_id: user_id,
						content: history,
						letter_id: getCliniclNote.id
					},
						{ user: { id: user_id } })
					console.log(_medicalHistory)
				}
				//logger
				userActivityLogger.info(UtilsHelper.getMessage(10038), { additionalInfo: { user_id: user_id, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, clinicalNoteId: clinicalNoteId, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

				_.assign(_retData, {
					msgCode: 10038,
					msg: UtilsHelper.getMessage(10038),
					data: UtilsHelper.cryptoJsObjectEncrypt(_updatedData),
				})
			} else {
				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50012,
					msg: UtilsHelper.getMessage(50012),
				})
			}
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in ClinicalNoteController.editNote')
		}
		return this.sendResponse(res, _retData)
	}

	public async pinUnpinClinicalNote(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject();
		try {
			const user_id = req?.user?.id;
			const { clinicalNoteId } = req.params; // Get clinical note ID from the request parameters
			const { isPinned } = req.body; // Get the pinning information from the request body

			// Check if the clinical note exists
			const getClinicalNote: any = await Models.PatientClinicalNotesLetters.findOne({
				where: { status: { [Op.ne]: 9 }, id: clinicalNoteId }
			});

			if (!getClinicalNote) {
				// If the clinical note is not found, return an error
				_.assign(_retData, {
					status: 'error',
					statusCode: 404,
					msgCode: 50012,
					msg: UtilsHelper.getMessage(50012), // Message for "Note not found"
				});
			} else {
				if (isPinned) {
					// Pin the clinical note for the user
					const existingPin = await Models.PatientPinnedNotes.findOne({
						where: { user_id: user_id, clinical_note_id: clinicalNoteId }
					});

					if (!existingPin) {
						// Create a new pin entry if the note is not already pinned
						await Models.PatientPinnedNotes.create({
							user_id: user_id,
							clinical_note_id: clinicalNoteId,
							pinned_at: new Date(),
						});

						_.assign(_retData, {
							status: 'success',
							msgCode: 10039,
							msg: UtilsHelper.getMessage(10039), // Message for "Note pinned successfully"
						});
					} else {
						// If already pinned, return a message indicating it
						_.assign(_retData, {
							status: 'success',
							msgCode: 10040,
							msg: UtilsHelper.getMessage(10040), // Message for "Note already pinned"
						});
					}
				} else {
					// Unpin the clinical note for the user
					const unpinned = await Models.PatientPinnedNotes.destroy({
						where: { user_id: user_id, clinical_note_id: clinicalNoteId }
					});

					if (unpinned) {
						_.assign(_retData, {
							status: 'success',
							msgCode: 10041,
							msg: UtilsHelper.getMessage(10041), // Message for "Note unpinned successfully"
						});
					} else {
						_.assign(_retData, {
							status: 'error',
							msgCode: 50013,
							msg: UtilsHelper.getMessage(50013), // Message for "Note was not pinned"
						});
					}
				}

				// Log user activity for pin/unpin actions
				userActivityLogger.info(UtilsHelper.getMessage(10042), {
					additionalInfo: {
						user_id: user_id,
						first_name: req?.user?.first_name,
						last_name: req?.user?.last_name,
						email: req?.user?.email,
						clinicalNoteId: clinicalNoteId,
						tags: 'User Activity',
						request: { Method: req.method, originalUrl: req.originalUrl },
					}
				});
			}
		} catch (err: any) {
			// Handle any errors that occur
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			});

			this.logErrors(err, 'Error in ClinicalNoteController.pinUnpinClinicalNote');
		}
		return res.status(_retData.statusCode).json(_retData)
		// return this.sendResponse(res, _retData);
	}


	public async getSignedUrl(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {

			const options = {
				expiresIn: 6 * 24 * 60 * 60, // expires in seconds
				responseContentDisposition: 'inline',
			};

			const command = new GetObjectCommand({ Bucket: process.env.S3_BUCKET_NAME, Key: 'backend/clinical_note/90/doc_1695882380902.pdf' });
			const url = await getSignedUrl(s3, command, options); // expires in seconds

			const _msgCode = 10023
			_.assign(_retData, {
				msgCode: 10023,
				data: url,
				msg: UtilsHelper.getMessage(10023),
			})
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in ClinicalNoteController.getSignedUrl')
		}

		return this.sendResponse(res, _retData)
	}

	public async noteListForDoctor(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		const key = process.env.AES_KEY;
		try {
			const userId = req?.user?.id
			const _reqData = req.body
			const _params = req.params

			let _offset = 0
			const _recordsLimit = 10

			const _filter: any = {
				status: { [Op.ne]: 9 },
				clinic_id: _reqData?.clinic_id,
				user_id: userId,
			}
			if (!_.isEmpty(_reqData.searchStr)) {
				_filter[Op.or] = [];
				_filter[Op.or].push(Models.sequelize.where(
					Models.sequelize.literal(
						`PGP_SYM_DECRYPT("patient"."name"::bytea, '${key}')::text`
					),
					{
						[Op.iLike]: `%${_reqData.searchStr}%`,
					}
				));

				_filter[Op.or].push({
					'$prompt.name$': {[Op.iLike]: `%${_reqData.searchStr}%`},
				})

				_filter[Op.or].push({
					'$template.name$': {[Op.iLike]: `%${_reqData.searchStr}%`},
				})

			}

			if (_params.page && _.isInteger(_.parseInt(_params.page)) && _.parseInt(_params.page) > 0) {
				_offset = _recordsLimit * (_.parseInt(_params.page) - 1)
			}
			// Parse sorting parameters
			const sortBy = req.query.sortBy || 'createdAt'; // default sort by 'createdAt'
			const sortOrder = req.query.sortOrder === 'asc' ? 'ASC' : 'DESC'; // default order 'DESC'

			let order = [[sortBy, sortOrder]];

			if (sortBy === 'name') {
				order = [[Models.Sequelize.literal(`PGP_SYM_DECRYPT(CAST("patient"."name" AS BYTEA), '${key}')`), sortOrder]]
			}
			if (sortBy === 'template') {
				order = [[{model: Models.ClinicalUserNotesTemplates, as: 'template'}, 'name', sortOrder]]
			}
			if (sortBy === 'prompt') {
				order = [[{model: Models.ClinicalUserLlmPrompts, as: 'prompt'}, 'name', sortOrder]]
			}


			const { count, rows }: any = await Models.PatientClinicalNotesLetters.findAndCountAll({
				where: _filter,
				order: order,
				limit: _recordsLimit,
				offset: _offset,
				include: [
					{
						model: Models.Clinics,
						as: 'clinic',
						attributes: ['id', 'name', 'status'],
					},
					{
						model: Models.Patients,
						as: 'patient',
						attributes: ['id', 'period', 'age', 'dob', 'last_check_up_date',
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.name'), 'bytea'), key), 'name'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.first_name'), 'bytea'), key), 'first_name'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.last_name'), 'bytea'), key), 'last_name'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.gender'), 'bytea'), key), 'gender'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.email'), 'bytea'), key), 'email'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.nhs_number'), 'bytea'), key), 'nhs_number'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.reference_number'), 'bytea'), key), 'reference_number']],
					},
					{
						model: Models.ClinicalUserLlmPrompts,
						as: 'prompt',
						attributes: ['id', 'name'],
					},
					{
						model: Models.ClinicalUserNotesTemplates,
						as: 'template',
						attributes: ['id', 'name'],
					},
				]
			});


			//logger
			userActivityLogger.info(UtilsHelper.getMessage(10039), { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });


			_.assign(_retData, {
				data: UtilsHelper.cryptoJsObjectEncrypt({
					totalRecords: count,
					recordsPerPage: _recordsLimit,
					list: rows,
				}),
				msgCode: 10039,
				msg: UtilsHelper.getMessage(10039),
			})

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in ClinicalNoteController.noteListForDoctor')
		}

		return this.sendResponse(res, _retData)
	}

	public async maxRatedNoteForAiml(req: express.Request, res: express.Response): Promise<void | any> {

		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {

			const _params = req.params
			const userId = req?.user?.id

			const getCliniclNote: any = await Models.PatientClinicalNotesLetters.findOne({
				where: { status: { [Op.ne]: 9 }, user_id: userId, feedback: 1 },
				attributes: ['system_genrated_body_content', 'updated_body_content_text', 'approval_rating', 'status'],
				order: [['id', 'desc']],
			})

			if (getCliniclNote) {
				_.assign(_retData, {
					data: getCliniclNote,
					msgCode: 10037,
					msg: UtilsHelper.getMessage(10037),
				})
			} else {
				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50012,
					msg: UtilsHelper.getMessage(50012),
				})
			}

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in ClinicalNoteController.maxRatedNoteForAiml')
		}

		return this.sendResponse(res, _retData)

	}

	public async updatePrintShareDownlaod(req: express.Request, res: express.Response): Promise<void | any> {

		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const reqBody = req.body;
			const user_id = req?.user?.id
			const _params = req.params
			const clinicalNoteId = _params.clinicalNoteId;

			const getCliniclNote: any = await Models.PatientClinicalNotesLetters.findOne({
				where: { status: { [Op.ne]: 9 }, id: clinicalNoteId }
			})

			if (getCliniclNote) {

				const _updateData: any = {};

				if (_.has(reqBody, "is_letter_printed") === true) {
					_updateData.is_letter_printed = reqBody.is_letter_printed;

					//logger
					userActivityLogger.info('User has printed clinical note', { additionalInfo: { user_id: user_id, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, clinicalNoteId: clinicalNoteId, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

				}

				if (_.has(reqBody, "is_letter_emailed") === true) {
					_updateData.is_letter_emailed = reqBody.is_letter_emailed;

					//logger
					userActivityLogger.info('User has emailed clinical note', { additionalInfo: { user_id: user_id, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, clinicalNoteId: clinicalNoteId, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

				}

				if (_.has(reqBody, "is_letter_downloaded") === true) {
					_updateData.is_letter_downloaded = reqBody.is_letter_downloaded;

					//logger
					userActivityLogger.info('User has downloded clinical note', { additionalInfo: { user_id: user_id, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, clinicalNoteId: clinicalNoteId, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

				}
				await Models.PatientClinicalNotesLetters.update(_updateData, { where: { id: getCliniclNote.id } });


				_.assign(_retData, {
					msgCode: 10038,
					msg: UtilsHelper.getMessage(10038),
				})
			} else {
				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50012,
					msg: UtilsHelper.getMessage(50012),
				})
			}
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in ClinicalNoteController.noteDeatilFromClinicalNoteId')
		}

		return this.sendResponse(res, _retData)

	}

	public async shareNoteViaEmail(req: express.Request, res: express.Response): Promise<void | any> {

		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const reqBody = req.body;
			const user_id = req?.user?.id;
			const email = reqBody.email;
			const subject = reqBody.subject;
			const body = reqBody.body;
			const clinicalNoteId = reqBody.id;

			const getCliniclNote: any = await Models.PatientClinicalNotesLetters.findOne({
				where: { status: { [Op.ne]: 9 }, id: clinicalNoteId }
			})

			if (getCliniclNote) {

				/* const s3Configuration: S3ClientConfig = {
					credentials: {
						accessKeyId: process.env.S3_ACCESS_KEY,
						secretAccessKey: process.env.S3_ACCESS_SECRET
					},
					region: process.env.S3_REGION,
				};
				const s3 = new S3Client(s3Configuration); */

				const command = new GetObjectCommand({ Bucket: process.env.S3_BUCKET_NAME, Key: getCliniclNote.document_url });
				const url = await getSignedUrl(s3, command, { expiresIn: 6 * 24 * 60 * 60 }); // expires in seconds


				if (email != "") {

					const attachments: any = [
						{
							filename: getCliniclNote.document_url,
							path: url
						}
					]
					await sendMail(email, subject, "", body, attachments).then((data: any) => console.log(data));
				}

				//logger
				userActivityLogger.info(UtilsHelper.getMessage(10044), { additionalInfo: { user_id: user_id, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, clinicalNoteId: clinicalNoteId, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });



				_.assign(_retData, {
					msgCode: 10044,
					msg: UtilsHelper.getMessage(10044),
				})
			} else {
				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50012,
					msg: UtilsHelper.getMessage(50012),
				})
			}
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in ClinicalNoteController.shareNoteViaEmail')
		}

		return this.sendResponse(res, _retData)

	}

	public async noteListForPatient(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		const key = process.env.AES_KEY;
		try {
			const userId = req?.user?.id
			const _reqData = req.body
			const _params = req.params;

			const clinic_id = _reqData.clinic_id;

			const patient_id = _reqData.patient_id
			const _userRoleDeatilData: any = await Models.ClinicalUserRoleDetail.findOne({ where: { user_id: userId, status: 1 } });
			if (_.isEmpty(_userRoleDeatilData)) {
				throw new Error('USER_ROLE_DETAIL_NOT_FOUND')
			}

			let _offset = 0
			const _recordsLimit = 10

			const _filter: any = {
				status: { [Op.ne]: 9 },
				patient_id: patient_id,
				clinic_id: clinic_id,
			}

			if (!_.isEmpty(_reqData.startDate) && !_.isEmpty(_reqData.endDate)) {
				const startedDate: any = new Date(_reqData.startDate);
				const endDate: any = new Date(_reqData.endDate);
				_filter.createdAt = { [Op.between]: [startedDate, endDate] }
			}

			// Parse sorting parameters
			const sortBy = req.query.sortBy || 'createdAt'; // default sort by 'createdAt'
			const sortOrder = req.query.sortOrder === 'asc' ? 'ASC' : 'DESC'; // default order 'DESC'

			let order = [[sortBy, sortOrder]];

			if (sortBy === 'name') {
				order = [[Models.Sequelize.literal(`PGP_SYM_DECRYPT(CAST("patient"."name" AS BYTEA), '${key}')`), sortOrder]]
			}
			if (sortBy === 'template') {
				order = [[{model: Models.ClinicalUserNotesTemplates, as: 'template'}, 'name', sortOrder]]
			}
			if (sortBy === 'prompt') {
				order = [[{model: Models.ClinicalUserLlmPrompts, as: 'prompt'}, 'name', sortOrder]]
			}

			if (_params.page && _.isInteger(_.parseInt(_params.page)) && _.parseInt(_params.page) > 0) {
				_offset = _recordsLimit * (_.parseInt(_params.page) - 1)
			}
			const { count, rows }: any = await Models.PatientClinicalNotesLetters.findAndCountAll({
				where: _filter,
				include: [
					{
						model: Models.Patients,
						as: 'patient',
						attributes: ['id', 'period', 'age', 'dob', 'last_check_up_date',
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.name'), 'bytea'), key), 'name'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.first_name'), 'bytea'), key), 'first_name'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.last_name'), 'bytea'), key), 'last_name'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.gender'), 'bytea'), key), 'gender'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.email'), 'bytea'), key), 'email'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.nhs_number'), 'bytea'), key), 'nhs_number'],
							[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.reference_number'), 'bytea'), key), 'reference_number']],
					},
					{
						model: Models.ClinicalUserLlmPrompts,
						as: 'prompt',
						attributes: ['id', 'name'],
					},
					{
						model: Models.ClinicalUserNotesTemplates,
						as: 'template',
						attributes: ['id', 'name'],
					},
					{
						model: Models.PatientPinnedNotes, // Include the pinned status
						as: 'pinned',
						where: { user_id: userId }, // Filter based on the current user
						required: false, // Use left join, so we include notes even if they are not pinned
						attributes: ['id', 'pinned_at'] // Get the pin ID and the pinned date
					}
				],
				order: order,
				limit: _recordsLimit,
				offset: _offset,
			});

			//logger
			userActivityLogger.info(UtilsHelper.getMessage(10039), { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, funcction: 'noteListForPatient', tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });


			_.assign(_retData, {
				data: UtilsHelper.cryptoJsObjectEncrypt({
					totalRecords: count,
					recordsPerPage: _recordsLimit,
					list: rows,
				}),
				msgCode: 10039,
				msg: UtilsHelper.getMessage(10039),
			})

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in ClinicalNoteController.noteListForDoctor')
		}

		return this.sendResponse(res, _retData)
	}

	public async noteListForOwner(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		const key = process.env.AES_KEY;
		try {
			const userId = req?.user?.id
			const _reqData = req.body
			const _params = req.params
			const clinicIdArray: any = [];
			const checkRole = await clinicalNoteHelper.checkRole(req?.user?.role_id)

			if (checkRole === true) {
				const roleData = await Models.ClinicalUserRoleDetail.findAll({
					where: { user_id: userId, status: 1 }
				})

				if (roleData.length > 0) {
					for (const role of roleData) {
						clinicIdArray.push(role.clinic_id)
					}
				}

				let _offset = 0
				const _recordsLimit = 10

				const _filter: any = {
					status: { [Op.ne]: 9 },
					clinic_id: _reqData?.clinic_id,

				}

				if (!_.isEmpty(_reqData.searchStr)) {
					_filter[Op.or] = [];
					_filter[Op.or].push(Models.sequelize.where(
						Models.sequelize.literal(
							`PGP_SYM_DECRYPT("patient"."name"::bytea, '${key}')::text`
						),
						{
							[Op.iLike]: `%${_reqData.searchStr}%`,
						}
					));

					_filter[Op.or].push({
						'$prompt.name$': {[Op.iLike]: `%${_reqData.searchStr}%`},
					})

					_filter[Op.or].push({
						'$template.name$': {[Op.iLike]: `%${_reqData.searchStr}%`},
					})

				}

				if (_params.page && _.isInteger(_.parseInt(_params.page)) && _.parseInt(_params.page) > 0) {
					_offset = _recordsLimit * (_.parseInt(_params.page) - 1)
				}
				// Parse sorting parameters
				const sortBy = req.query.sortBy || 'createdAt'; // default sort by 'createdAt'
				const sortOrder = req.query.sortOrder === 'asc' ? 'ASC' : 'DESC'; // default order 'DESC'

        let order = [[sortBy, sortOrder]];

        if (sortBy === 'name') {
          order = [[Models.Sequelize.literal(`PGP_SYM_DECRYPT(CAST("patient"."name" AS BYTEA), '${key}')`), sortOrder]]
        }
				if (sortBy === 'template') {
					order = [[{model: Models.ClinicalUserNotesTemplates, as: 'template'}, 'name', sortOrder]]
				}
				if (sortBy === 'prompt') {
					order = [[{model: Models.ClinicalUserLlmPrompts, as: 'prompt'}, 'name', sortOrder]]
				}

				const { count, rows }: any = await Models.PatientClinicalNotesLetters.findAndCountAll({
					where: _filter,
					limit: _recordsLimit,
          order: order,
					offset: _offset,
					include: [
						{
							model: Models.Clinics,
							as: 'clinic',
							attributes: ['id', 'name', 'status'],
						},
						{
							model: Models.Patients,
							as: 'patient',
							attributes: ['id', 'period', 'age', 'dob', 'last_check_up_date',
								[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.name'), 'bytea'), key), 'name'],
								[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.first_name'), 'bytea'), key), 'first_name'],
								[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.last_name'), 'bytea'), key), 'last_name'],
								[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.gender'), 'bytea'), key), 'gender'],
								[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.email'), 'bytea'), key), 'email'],
								[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.nhs_number'), 'bytea'), key), 'nhs_number'],
								[Models.sequelize.fn('PGP_SYM_DECRYPT', Models.sequelize.cast(Models.sequelize.col('patient.reference_number'), 'bytea'), key), 'reference_number']],
						},
						{
							model: Models.ClinicalUserLlmPrompts,
							as: 'prompt',
							attributes: ['id', 'name'],
						},
						{
							model: Models.ClinicalUserNotesTemplates,
							as: 'template',
							attributes: ['id', 'name'],
						},
					]
				});

				//logger
				userActivityLogger.info(UtilsHelper.getMessage(10039), { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, funcction: 'noteListForOwner', tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

				dataAccessLogger.info(`${req?.user?.first_name} view the patient record for genrated clinical note `, { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, tags: 'Data Access', data_acess: ['name', 'first_name', 'last_name', 'gender', 'email', 'nhs_number', 'others', 'reference_number'], request: { Method: req.method, originalUrl: req.originalUrl } } });


				_.assign(_retData, {
					data: UtilsHelper.cryptoJsObjectEncrypt({
						totalRecords: count,
						recordsPerPage: _recordsLimit,
						list: rows,
					}),
					msgCode: 10039,
					msg: UtilsHelper.getMessage(10039),
				})

			} else {
				const clientIP = req.ip;
				securityLogger.info('Unauthorized request access from', { additionalInfo: { user_ip: clientIP, user_id: req?.user?.id, tags: 'Security Log', request: { Method: req.method, originalUrl: req.originalUrl } } });

				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50022,
					msg: UtilsHelper.getMessage(50022),
				})
			}

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in ClinicalNoteController.noteListForOwner')
		}

		return this.sendResponse(res, _retData)
	}

	/* public async noteDeatilFromId(req: express.Request, res: express.Response): Promise<void | any> {

		const _retData: IResponseObject = UtilsHelper.responseObject()
		const key = process.env.AES_KEY;
		try {
			const _params = req.params
			const clinicalNoteId = _params.clinicalNoteId;
			const getCliniclNote: any = await Models.PatientClinicalNotesLetters.findOne({
				where: { status: { [Op.ne]: 9 }, id: clinicalNoteId },
			});

			if (getCliniclNote){
				_.assign(_retData, {
					data: getCliniclNote,
					msgCode: 10037,
					msg: UtilsHelper.getMessage(10037),
				})
			} else {
				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50012,
					msg: UtilsHelper.getMessage(50012),
				})
			}

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in ClinicalNoteController.noteDeatilFromId')
		}

		return this.sendResponse(res, _retData)

	} */

	public async bulkDeleteNotes(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject();

		try {
			const userId = req?.user?.id;
			const { clinic_id, clinicalNoteIds } = req.body;

			if (!clinicalNoteIds || !Array.isArray(clinicalNoteIds) || clinicalNoteIds.length === 0) {
				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50013,
					msg: UtilsHelper.getMessage(50013), // "No notes to delete"
				});
				return this.sendResponse(res, _retData);
			}

			// Check user permissions for deleting notes
			// const checkPermission = await clinicalNoteHelper.checkPermissions('Clinical-Note-Delete', userId, clinic_id);
			// if (!checkPermission) {
			// 	const clientIP = req.ip;
			// 	securityLogger.info('Unauthorized request access from', {
			// 		additionalInfo: {
			// 			user_ip: clientIP,
			// 			user_id: req?.user?.id,
			// 			tags: 'Security Log',
			// 			request: { Method: req.method, originalUrl: req.originalUrl },
			// 		},
			// 	});
			//
			// 	_.assign(_retData, {
			// 		status: 'error',
			// 		statusCode: 400,
			// 		msgCode: 50022,
			// 		msg: UtilsHelper.getMessage(50022),
			// 	});
			//
			// 	return this.sendResponse(res, _retData);
			// }

			// Fetch all clinical notes that are valid and not already deleted
			const validNotes = await Models.PatientClinicalNotesLetters.findAll({
				where: {
					id: { [Op.in]: clinicalNoteIds },
					status: { [Op.ne]: 9 },
				},
			});

			if (validNotes.length === 0) {
				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50014,
					msg: UtilsHelper.getMessage(50014), // "No valid clinical notes found"
				});
				return this.sendResponse(res, _retData);
			}

			const validNoteIds = validNotes.map(note => note.id);

			// Delete associated tasks for each clinical note
			await Models.Task.destroy({
				where: { letter_id: { [Op.in]: validNoteIds } },
			});

			// Update the status of each clinical note to deleted (status: 9)
			await Models.PatientClinicalNotesLetters.update(
				{ status: 9 },
				{ where: { id: { [Op.in]: validNoteIds } } }
			);

			// Decrement letter_count for the clinic
			await Models.Clinics.decrement('letter_count', {
				where: { clinic_id },
				by: validNoteIds.length,
			});

			// Log activity for bulk delete
			userActivityLogger.info(UtilsHelper.getMessage(10051), {
				additionalInfo: {
					user_id: userId,
					first_name: req?.user?.first_name,
					last_name: req?.user?.last_name,
					email: req?.user?.email,
					clinicalNoteIds: validNoteIds.join(','),
					tags: 'User Activity',
					request: { Method: req.method, originalUrl: req.originalUrl },
				},
			});

			// Return success response
			_.assign(_retData, {
				msgCode: 10051,
				msg: UtilsHelper.getMessage(10051), // "Clinical notes deleted successfully"
				data: { deletedNoteIds: validNoteIds },
			});
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			});

			this.logErrors(err, 'Error in ClinicalNoteController.bulkDeleteNotes');
		}

		return this.sendResponse(res, _retData);
	}


	public async noteDeleteFromId(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()

		try {
			const userId = req?.user?.id
			const _reqData = req.body
			const clinicId = _reqData.clinic_id;
			const _params = req.params
			const clinicalNoteId = _params.clinicalNoteId;

			//check user has permission to delete note
			const checkPermission = clinicalNoteHelper.checkPermissions('Clinical-Note-Delete', userId, clinicId);
			if (checkPermission) {
				const getCliniclNote: any = await Models.PatientClinicalNotesLetters.findOne({
					where: { status: { [Op.ne]: 9 }, id: clinicalNoteId },
				});

				if (getCliniclNote) {
					// Delete associated tasks
					await Models.Task.destroy(
						{ where: { letter_id: clinicalNoteId } } // Find tasks associated with the clinical note
					);

					await Models.PatientClinicalNotesLetters.update({ status: 9 }, { where: { id: getCliniclNote.id } });

					await Models.Clinics.decrement('letter_count', {
						where: {
							clinic_id: clinicId
						}
					});

					//logger
					userActivityLogger.info(UtilsHelper.getMessage(10050), { additionalInfo: { user_id: userId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, clinicalNoteId: clinicalNoteId, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });

					_.assign(_retData, {
						msgCode: 10050,
						msg: UtilsHelper.getMessage(10050),
					})
				} else {
					_.assign(_retData, {
						status: 'error',
						statusCode: 400,
						msgCode: 50012,
						msg: UtilsHelper.getMessage(50012),
					})
				}

			} else {

				const clientIP = req.ip;
				securityLogger.info('Unauthorized request access from', { additionalInfo: { user_ip: clientIP, user_id: req?.user?.id, tags: 'Security Log', request: { Method: req.method, originalUrl: req.originalUrl } } });

				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50022,
					msg: UtilsHelper.getMessage(50022),
				})
			}
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in ClinicalNoteController.noteDeleteFromId')
		}

		return this.sendResponse(res, _retData)
	}

	public async sampleNote(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const _reqBody = req.body;
			const logedInUserId = req?.user?.id
			const user_id = req?.user?.id
			const { name, description, prompt, clinical_note } = _reqBody;
			
			// const isExceeded = await clinicalNoteHelper.hasExceededDocumentLimit(req?.user);
			// if (isExceeded == 0) {
			// 	_.assign(_retData, {
			// 		statusCode: 400,
			// 		status: 'error',
			// 		msg: UtilsHelper.getMessage(50045, 'en'),
			// 	});
			//
			// 	return this.sendResponse(res, _retData)
			// }

			const system_genrated_body_content = await clinicalNoteHelper.systemGenratedContnet(user_id, name, description, prompt,  clinical_note);

			// const decrementDocuments = await clinicalNoteHelper.decrementDocuments(req?.user);
			// if (!decrementDocuments){
			// 	_.assign(_retData, {
			// 		statusCode: 400,
			// 		status: 'error',
			// 		msg: UtilsHelper.getMessage(50046, 'en'),
			// 	});
			// 	return this.sendResponse(res, _retData)
			// }

			if (system_genrated_body_content) {
				userActivityLogger.info(UtilsHelper.getMessage(10036), { additionalInfo: { user_id: logedInUserId, first_name: req?.user?.first_name, last_name: req?.user?.last_name, email: req?.user?.email, tags: 'User Activity', request: { Method: req.method, originalUrl: req.originalUrl } } });
				_.assign(_retData, {
					msgCode: 10036,
					data: UtilsHelper.cryptoJsObjectEncrypt({
						letter: system_genrated_body_content,
					}),
					msg: UtilsHelper.getMessage(10036),
				});
			}
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in ClinicalNoteController.sampleNote')
		}

		return this.sendResponse(res, _retData)
	}

	public async downloadClinicalNotePdf(req: express.Request, res: express.Response): Promise<void | any> {
		let browser;
		const clinicalNoteId = req.params.clinicalNoteId;

		try {
			// … your note & template fetch, S3 URL logic …
			// 1) FETCH YOUR NOTE & RELATED SETTINGS
			const note = await Models.PatientClinicalNotesLetters.findOne({
				where: { id: clinicalNoteId, status: { [Op.ne]: 9 } },
				include: [
					{
						model: Models.ClinicalUserSettings,
						as: 'userSetting',
						attributes: [
							'letter_header_template',
							'letter_footer_template',
							'header_image',
							'footer_image'
						]
					},
					// template for this note
					{
						model: Models.ClinicalUserNotesTemplates,
						as: 'template',
					}
				]
			});

			if (!note) {
				return res.status(404).json({ error: 'Clinical note not found' });
			}

			// Launch Puppeteer
			const headerHtml = note?.template?.letter_header;
			const footerHtml = note?.template?.letter_footer;

			// 2) Launch & measure
			const browser = await puppeteer.launch({ args: ['--no-sandbox'] });
			let headerCm = 0, footerCm = 0;

			if (headerHtml?.trim()) {
				const hPage = await browser.newPage();
				await hPage.setContent(headerHtml, { waitUntil: 'networkidle0' });
				const headerPx = await hPage.evaluate(() => document.body.scrollHeight);
				headerCm = pxToCm(headerPx) + 0.5;
				await hPage.close();
			}

			if (footerHtml?.trim()) {
				const fPage = await browser.newPage();
				await fPage.setContent(footerHtml, { waitUntil: 'networkidle0' });
				const footerPx = await fPage.evaluate(() => document.body.scrollHeight);
				footerCm = pxToCm(footerPx) + 0.5;
				await fPage.close();
			}

// 3) Enforce sensible minimums
			headerCm = Math.max(headerCm, 1);  // always at least 3 cm
			footerCm = Math.max(footerCm, 1);  // always at least 2 cm

// 4) Clamp so you don’t overflow the page
			const LETTER_HEIGHT_CM = 27.94;
			if (headerCm + footerCm >= LETTER_HEIGHT_CM - 2) {
				console.warn(`Header: ${headerCm} and Footer: ${footerCm} exceed letter height`)
				headerCm = 10; footerCm = 10;
				console.warn(`New Header: ${headerCm} and New Footer: ${footerCm}`)
			}

// 5) Build your body‐only HTML (no huge inline margins!)
			const contentHtml = `
<!DOCTYPE html>
<html>
<head><meta charset="utf-8">
  <style>
    body {
      margin: 0; padding: 0;
      font-family: Arial, sans-serif;
    }
    .page-break { page-break-after: always; }
  </style>
</head>
<body>
  ${ note.updated_body_content
				.split('<!-- pagebreak -->')
				.map(f => `<div class="page-break">${f}</div>`).join('') }
</body>
</html>`;

			const page = await browser.newPage();
			await page.setContent(contentHtml, { waitUntil: 'networkidle0' });

// 6) Finally generate the PDF with correct margins
			const pdfBuffer = await page.pdf({
				format: 'Letter',
				printBackground: true,
				displayHeaderFooter: !!note?.template?.letter_header,
				margin: {
					top:    `${headerCm.toFixed(2)}cm`,
					bottom: `${footerCm.toFixed(2)}cm`,
					left:   '1cm',
					right:  '1cm',
				},
				headerTemplate: `<div style="margin:0 1cm; text-align:left; font-size:12px; height: ${headerCm}">
      ${headerHtml}
    </div>`,
				footerTemplate: `<div style="margin:0 1cm; text-align:left; font-size:10px;">
      ${footerHtml}
    </div>`,
			});

			await browser.close();
			res.set({
				'Content-Type':        'application/pdf',
				'Content-Disposition': `attachment; filename="note-${note.id}.pdf"`,
				'Content-Length':      pdfBuffer.length
			});
			res.send(pdfBuffer);

		} catch (err) {
			if (browser) await browser.close();
			console.error(err);
			return res.status(400).send(err.message || err);
		}

	}
	public async generatePdf(req: express.Request, res: express.Response): Promise<void | any> {
		let browser;
		try {
			const { headerHtml, bodyHtml, footerHtml, letterId, patientId } = req.body;
			
			// Function to convert pixels to centimeters
			const pxToCm = (px: number) => px / 37.8;
			
			// Launch Puppeteer
			browser = await puppeteer.launch({ args: ['--no-sandbox'] });
			let headerCm = 0, footerCm = 0;

			// Measure header height
			if (headerHtml?.trim()) {
				const hPage = await browser.newPage();
				await hPage.setContent(headerHtml, { waitUntil: 'networkidle0' });
				const headerPx = await hPage.evaluate(() => document.body.scrollHeight);
				headerCm = pxToCm(headerPx) + 0.5;
				await hPage.close();
			}

			// Measure footer height
			if (footerHtml?.trim()) {
				const fPage = await browser.newPage();
				await fPage.setContent(footerHtml, { waitUntil: 'networkidle0' });
				const footerPx = await fPage.evaluate(() => document.body.scrollHeight);
				footerCm = pxToCm(footerPx) + 0.5;
				await fPage.close();
			}

			// Build content HTML
			const contentHtml = `
<!DOCTYPE html>
<html>
<head><meta charset="utf-8">
  <style>
    body {
      margin: 0; padding: 0;
      font-family: Arial, sans-serif;
    }
    .page-break { page-break-after: always; }
  </style>
</head>
<body>
  ${bodyHtml
    .split('<!-- pagebreak -->')
    .map(f => `<div class="page-break">${f}</div>`).join('')}
</body>
</html>`;

			const page = await browser.newPage();
			await page.setContent(contentHtml, { waitUntil: 'networkidle0' });

			// Generate PDF with correct margins
			const pdfBuffer = await page.pdf({
				format: 'Letter',
				printBackground: true,
				displayHeaderFooter: !!headerHtml,
				margin: {
					top: `${headerCm.toFixed(2)}cm`,
					bottom: `${footerCm.toFixed(2)}cm`,
					left: '1cm',
					right: '1cm',
				},
				headerTemplate: `<div style="margin:0 1cm; text-align:left; font-size:12px; height: ${headerCm}cm">
					${headerHtml || ''}
				</div>`,
				footerTemplate: `<div style="margin:0 1cm; text-align:left; font-size:10px;">
					${footerHtml || ''}
				</div>`,
			});

			// Save to database if needed
			if (letterId) {
				// Update the document URL in the database if needed
				// This is optional - you can implement based on your requirements
			}

			// Send PDF as response
			res.set({
				'Content-Type': 'application/pdf',
				'Content-Disposition': `attachment; filename="clinical-letter-${Date.now()}.pdf"`,
				'Content-Length': pdfBuffer.length
			});
			res.send(pdfBuffer);

		} catch (err) {
			console.error('❌ PDF generation error', err);
			res.status(500).send({ error: 'Failed to generate PDF' });
		} finally {
			if (browser) {
				await browser.close();
			}
		}
	}
	public async generatePdfPreview(req: express.Request, res: express.Response): Promise<void> {
		let browser = null;

		try {
			const { headerHtml = '', bodyHtml = '', footerHtml = '', patientId } = req.body;

			// ─── Adjusted for measured page height ───
			const PAGE_WIDTH_CM   = 21.59;  // Letter width
			const PAGE_HEIGHT_CM  = 27.5;   // measured height
			const SIDE_CM         = 1;      // left/right margin
			const CONTENT_WIDTH_CM = PAGE_WIDTH_CM - 2*SIDE_CM;
			const CONTENT_PX      = Math.round(CONTENT_WIDTH_CM * 96 / 2.54);
			const pxToCm = (px: number) => px / (96/2.54);

			browser = await puppeteer.launch({
				args: ['--no-sandbox','--disable-setuid-sandbox']
			});

			// ─── measure helper ───
			async function measureFragment(html: string): Promise<number> {
				if (!html.trim()) return 0;
				const page = await browser!.newPage();
				await page.setViewport({ width: CONTENT_PX, height: 800 });

				const wrapped = `
          <!DOCTYPE html><html><head><meta charset="utf-8">
            <style>
              html, body { margin:0; padding:0; }
              #w { width:${CONTENT_PX}px; display:inline-block; }
              #w img { max-width:100%; height:auto; display:block; }
            </style>
          </head>
          <body><div id="w">${html}</div></body></html>`;
				await page.setContent(wrapped, { waitUntil: 'domcontentloaded' });
				await page.waitForNetworkIdle({ idleTime: 300 });
				await page.evaluate(() =>
					Promise.all(Array.from(document.images).map(img =>
						img.complete ? null : new Promise(r => { img.onload=img.onerror=r; })
					))
				);
				const px = await page.$eval('#w', el =>
					Math.ceil(el.getBoundingClientRect().height)
				);
				await page.close();
				return pxToCm(px) + 1.5;  // 0.5 cm safety buffer
			}

			// ─── measure header & footer ───
			let headerCm = await measureFragment(headerHtml);
			let footerCm = await measureFragment(footerHtml);

			// ─── clamp minima & ensure content space ───
			headerCm = Math.max(headerCm, 2);
			footerCm = Math.max(footerCm, 1.5);
			// leave at least 2 cm for body
			if (headerCm + footerCm > PAGE_HEIGHT_CM - 2) {
				headerCm = 2;
				footerCm = 2;
			}

			// ─── build body-only HTML ───
			const contentHtml = `
        <!DOCTYPE html><html><head><meta charset="utf-8">
          <style>
            body { margin:0; padding:0; font-family:Arial,sans-serif; }
            .page-break { page-break-after: always; }
          </style>
        </head>
        <body>
          ${bodyHtml.split('<!-- pagebreak -->')
				.map(f => `<div class="page-break">${f}</div>`).join('')}
        </body></html>`;

			const page = await browser.newPage();
			await page.setContent(contentHtml, { waitUntil: 'networkidle0' });

			// ─── generate PDF ───
			const pdfBuffer = await page.pdf({
				// explicitly pin to measured size
				width:  `${PAGE_WIDTH_CM}cm`,
				height: `${PAGE_HEIGHT_CM}cm`,
				printBackground: true,
				displayHeaderFooter: !!headerHtml.trim(),
				margin: {
					top:    `${(headerCm + 0.5).toFixed(2)}cm`,
					bottom: `${(footerCm + 0.5).toFixed(2)}cm`,
					left:   `${SIDE_CM}cm`,
					right:  `${SIDE_CM}cm`,
				},
				headerTemplate: `
          <div style="
            margin:0 ${SIDE_CM}cm;
            font-size:12px;
            width: 100%;
            max-width:${CONTENT_WIDTH_CM}cm;
          ">
            ${headerHtml}
          </div>`,
				footerTemplate: `
          <div style="
            margin:0 ${SIDE_CM}cm;
            font-size:10px;
            width: 100%;
            max-width:${CONTENT_WIDTH_CM}cm;
          ">
            ${footerHtml}
          </div>`
			});

			await browser.close();
			browser = null;

			res.set({
				'Content-Type':        'application/pdf',
				'Content-Disposition': 'inline; filename="preview.pdf"',
				'Content-Length':      pdfBuffer.length
			});
			res.send(pdfBuffer);

		} catch (err) {
			console.error('❌ PDF preview error', err);
			if (browser) await browser.close();
			res.status(500).json({ error: 'Failed to generate PDF preview' });
		}
	}

	public async generatePdfPreview_old1(req: express.Request, res: express.Response): Promise<void | any> {
		let browser;
		try {
			const { headerHtml, bodyHtml, footerHtml } = req.body;

			// Convert px → cm at 96 DPI: 96px = 2.54cm, so px/37.8 ≈ cm
			const pxToCm = (px: number) => px / 37.8;
			const CONTENT_PX = Math.floor((21.59 - 2)   /* cm */ * (96/2.54));

			// Helper to measure any arbitrary HTML fragment in cm
			const measureHtml = async (htmlFragment: string) => {
				const page = await browser.newPage();
				// match PDF content width:
				await page.setViewport({ width: CONTENT_PX, height: 800 });

				const wrapped = `
    <!DOCTYPE html><html><head><meta charset="utf-8">
      <style>
        html, body { margin:0; padding:0; }
        #wrapper {
          display: block;
          width: ${CONTENT_PX}px;
        }
      </style>
    </head>
    <body>
      <div id="wrapper">${htmlFragment}</div>
    </body>
  </html>`;

				// load & wait
				await page.setContent(wrapped, { waitUntil: 'domcontentloaded' });
				console.log({wrapped})
				await page.waitForNetworkIdle({ idleTime: 500 });
				await page.evaluate(() =>
					Promise.all(
						Array.from(document.images)
							.map(img => img.complete
								? Promise.resolve()
								: new Promise(res => { img.onload = img.onerror = res; })
							)
					)
				);

				// measure
				const px = await page.$eval('#wrapper', el => Math.ceil(el.getBoundingClientRect().height));
				await page.close();
				const cms = pxToCm(px) + 0.5
				return cms;

			};

			// 1) Launch Puppeteer
			browser = await puppeteer.launch({ args: ['--no-sandbox','--disable-setuid-sandbox'] });
			let headerCm = 0, footerCm = 0;

			// 2) Measure header if present
			if (headerHtml?.trim()) {
				headerCm = await measureHtml(headerHtml);
			}

			// 3) Measure footer if present
			if (footerHtml?.trim()) {
				footerCm = await measureHtml(footerHtml);
			}

			// 4) Clamp sensible minimums & ensure we don’t overflow a Letter page
			const LETTER_HEIGHT_CM = 27.94;
			headerCm = Math.max(headerCm, 2);  // at least 2cm
			footerCm = Math.max(footerCm, 1.5); // at least 1.5cm

			console.warn(
				`Current Computed header (${headerCm}cm) + footer (${footerCm}cm)`
			);

			if (headerCm + footerCm >= LETTER_HEIGHT_CM - 2) {
				console.warn(
					`Computed header (${headerCm}cm) + footer (${footerCm}cm) too tall; falling back to 2cm/2cm`
				);
				headerCm = 10;
				footerCm = 10;
			}

			// 5) Build the body-only HTML (with page breaks)
			const contentHtml = `
      <!DOCTYPE html>
      <html>
        <head><meta charset="utf-8">
          <style>
            body { margin:0; padding:0; font-family:Arial,sans-serif; }
            .page-break { page-break-after: always; }
          </style>
        </head>
        <body>
          ${bodyHtml
				.split('<!-- pagebreak -->')
				.map(f => `<div class="page-break">${f}</div>`)
				.join('')}
        </body>
      </html>`;

			const page = await browser.newPage();
			await page.setContent(contentHtml, { waitUntil: 'networkidle0' });
			console.log({top:    `${Number(headerCm.toFixed(2)) + 3}cm`,
				bottom: `${Number(footerCm.toFixed(2))}cm`,})
			// 6) Generate PDF
			const pdfBuffer = await page.pdf({
				format: 'A4',
				printBackground: true,
				displayHeaderFooter: !!headerHtml?.trim(),
				margin: {
					top:    `10cm`,
					bottom: `10cm`,
					// top:    `${headerCm.toFixed(2) + 5}cm`,
					// bottom: `${footerCm.toFixed(2)}cm`,
					left:   '1cm',
					right:  '1cm',
				},
				headerTemplate: `
        <div style="
          margin:0 1cm;
          text-align:left;
          font-size:12px;
          height: ${headerCm}cm;
        ">
          ${headerHtml || ''}
        </div>`,
				footerTemplate: `
        <div style="
          margin:0 1cm;
          text-align:left;
          font-size:10px;
        ">
          ${footerHtml || ''}
        </div>`,
			});

			const fullHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          html, body { margin:0; padding:0; font-family: Arial, sans-serif; }
          .page-break { page-break-after: always; }
          .header { 
            position: fixed; top: 0; left: 0; right: 0; 
            /* optional styling for clarity: */
            border-bottom: 1px solid #ccc;
          }
          .footer {
            position: fixed; bottom: 0; left: 0; right: 0;
            border-top: 1px solid #ccc;
          }
          .content {
            /* you’ll adjust these margins dynamically in PDF */
            margin-top: ${headerCm.toFixed(2) + 3}cm;  
            margin-bottom: ${headerCm.toFixed(2)}cm;
            padding: 1cm;
          }
        </style>
      </head>
      <body>
        ${ headerHtml ? `<div class="header">${headerHtml}</div>` : '' }
        <div class="content">
          ${bodyHtml
				.split('<!-- pagebreak -->')
				.map(f => `<div class="page-break">${f}</div>`)}
        </div>
        ${ footerHtml ? `<div class="footer">${footerHtml}</div>` : '' }
      </body>
      </html>
    `;
			console.log({fullHtml});

			// 7) Cleanup & respond
			await browser.close();
			res.set({
				'Content-Type':        'application/pdf',
				'Content-Length':      pdfBuffer.length,
				'Content-Disposition': 'inline; filename="preview.pdf"'
			});
			res.send(pdfBuffer);

		} catch (err) {
			console.error('❌ PDF preview generation error', err);
			if (browser) await browser.close();
			res.status(500).send({ error: 'Failed to generate PDF preview' });
		}
	}

	public async generatePdfPreview_old(req: express.Request, res: express.Response): Promise<void | any> {
		let browser;
		try {
			const { headerHtml, bodyHtml, footerHtml } = req.body;
			
			// Function to convert pixels to centimeters
			const pxToCm = (px: number) => px / 37.8;
			
			// Launch Puppeteer
			browser = await puppeteer.launch({ args: ['--no-sandbox'] });
			let headerCm = 0, footerCm = 0;

			// Measure header height
			if (headerHtml?.trim()) {
				const hPage = await browser.newPage();
				await hPage.setContent(headerHtml, { waitUntil: 'networkidle0' });
				const headerPx = await hPage.evaluate(() => document.body.scrollHeight);
				headerCm = pxToCm(headerPx) + 0.5;
				await hPage.close();
			}

			// Measure footer height
			if (footerHtml?.trim()) {
				const fPage = await browser.newPage();
				await fPage.setContent(footerHtml, { waitUntil: 'networkidle0' });
				const footerPx = await fPage.evaluate(() => document.body.scrollHeight);
				footerCm = pxToCm(footerPx) + 0.5;
				await fPage.close();
			}
			const LETTER_HEIGHT_CM = 27.94;
			if (headerCm + footerCm >= LETTER_HEIGHT_CM - 2) {
				console.warn(`Header: ${headerCm} and Footer: ${footerCm} exceed letter height`)
				headerCm = 10; footerCm = 10;
				console.warn(`New Header: ${headerCm} and New Footer: ${footerCm}`)
			}

			// Build content HTML
			const contentHtml = `
<!DOCTYPE html>
<html>
<head><meta charset="utf-8">
  <style>
    body {
      margin: 0; padding: 0;
      font-family: Arial, sans-serif;
    }
    .page-break { page-break-after: always; }
  </style>
</head>
<body>
  ${bodyHtml
    .split('<!-- pagebreak -->')
    .map(f => `<div class="page-break">${f}</div>`).join('')}
</body>
</html>`;

			const page = await browser.newPage();
			await page.setContent(contentHtml, { waitUntil: 'networkidle0' });

			// Generate PDF with correct margins
			const pdfBuffer = await page.pdf({
				format: 'Letter',
				printBackground: true,
				displayHeaderFooter: !!headerHtml,
				margin: {
					top: `${headerCm.toFixed(2)}cm`,
					bottom: `${footerCm.toFixed(2)}cm`,
					left: '1cm',
					right: '1cm',
				},
				headerTemplate: `<div style="margin:0 1cm; text-align:left; font-size:12px; height: ${headerCm}cm">
					${headerHtml || ''}
				</div>`,
				footerTemplate: `<div style="margin:0 1cm; text-align:left; font-size:10px;">
					${footerHtml || ''}
				</div>`,
			});

			// Send PDF as response
			res.set({
				'Content-Type': 'application/pdf',
				'Content-Length': pdfBuffer.length
			});
			res.send(pdfBuffer);

		} catch (err) {
			console.error('❌ PDF preview generation error', err);
			res.status(500).send({ error: 'Failed to generate PDF preview' });
		} finally {
			if (browser) {
				await browser.close();
			}
		}
	}
	// public async downloadClinicalNotePdf(req: express.Request, res: express.Response): Promise<void | any> {
	// 	const _retData: IResponseObject = UtilsHelper.responseObject();
	// 	try {
	// 		const userId = req?.user?.id;
	// 		const { clinicalNoteId } = req.params;
	//
	// 		// Get the clinical note details
	// 		const clinicalNote: any = await Models.PatientClinicalNotesLetters.findOne({
	// 			where: { status: { [Op.ne]: 9 }, id: clinicalNoteId },
	// 			include: [
	// 				{
	// 					model: Models.Users,
	// 					as: 'user',
	// 					attributes: ['id', 'first_name', 'last_name', 'email']
	// 				}
	// 			]
	// 		});
	//
	// 		if (!clinicalNote) {
	// 			_.assign(_retData, {
	// 				status: 'error',
	// 				statusCode: 400,
	// 				msgCode: 50012,
	// 				msg: UtilsHelper.getMessage(50012), // Note not found
	// 			});
	// 			return this.sendResponse(res, _retData);
	// 		}
	//
	// 		// Generate PDF
	// 		const pdfPath = await clinicalNoteHelper.generatePdfWithHtmlPdfNode(
	// 			userId,
	// 			clinicalNote.header_content || '',
	// 			clinicalNote.updated_body_content || clinicalNote.system_genrated_body_content,
	// 			clinicalNote.footer_content || ''
	// 		);
	//
	// 		// Update the document URL in the database
	// 		await Models.PatientClinicalNotesLetters.update(
	// 			{ document_url: pdfPath, is_letter_downloaded: 1 },
	// 			{ where: { id: clinicalNoteId } }
	// 		);
	//
	// 		// Generate a signed URL for the PDF
	// 		const command = new GetObjectCommand({
	// 			Bucket: process.env.S3_BUCKET_NAME,
	// 			Key: pdfPath
	// 		});
	// 		const url = await getSignedUrl(s3, command, { expiresIn: 6 * 24 * 60 * 60 });
	//
	// 		// Log user activity
	// 		userActivityLogger.info(UtilsHelper.getMessage(10041), {
	// 			additionalInfo: {
	// 				user_id: userId,
	// 				first_name: req?.user?.first_name,
	// 				last_name: req?.user?.last_name,
	// 				email: req?.user?.email,
	// 				clinicalNoteId: clinicalNoteId,
	// 				tags: 'User Activity',
	// 				request: { Method: req.method, originalUrl: req.originalUrl }
	// 			}
	// 		});
	//
	// 		_.assign(_retData, {
	// 			msgCode: 10041,
	// 			data: UtilsHelper.cryptoJsObjectEncrypt({ documentUrl: url }),
	// 			msg: UtilsHelper.getMessage(10041), // PDF generated successfully
	// 		});
	// 	} catch (err: any) {
	// 		_.assign(_retData, {
	// 			statusCode: 500,
	// 			status: 'error',
	// 			msg: err.message,
	// 		});
	//
	// 		this.logErrors(err, 'Error in ClinicalNoteController.downloadClinicalNotePdf');
	// 	}
	//
	// 	return this.sendResponse(res, _retData);
	// }

}

function pxToCm(px) {
	// assume 96dpi: inches = px/96; cm = inches*2.54
	return (px / 96) * 2.54;
}

const clinicalNoteController = new ClinicalNoteController()

export default clinicalNoteController
