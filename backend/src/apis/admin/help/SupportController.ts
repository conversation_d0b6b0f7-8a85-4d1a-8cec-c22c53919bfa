import * as express from 'express'
import * as UtilsHelper from '../../../helpers/utils.helper'
import { IResponseObject } from '../../../helpers/utils.interface'
import BaseController from '../../../helpers/BaseController'
import _ from 'lodash'
import Models from '../../../database/models'
import { Op, Sequelize } from 'sequelize'
const { savePublicImage } = require('../../../helpers/fileUpload.helper')
const path = require('path');
import * as supportHelper from './helpers'

class SupportController extends BaseController {
	constructor() {
		super()
		this.getSupportList = this.getSupportList.bind(this);
		this.supportDeatilFromSupportId = this.supportDeatilFromSupportId.bind(this);
		this.updateStatusFromSupportId = this.updateStatusFromSupportId.bind(this);
		this.adminReplyFromSupportId = this.adminReplyFromSupportId.bind(this);
		this.uploadImage = this.uploadImage.bind(this);

	}

	/**
	 * Method to get all support list
	 */
	public async getSupportList(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const userId = req?.user?.id
			const _reqData = req.body
			const _params = req.params

			let _offset = 0
			const _recordsLimit = 25

			const _filter: any = {
				status: { [Op.ne]: 9 },
			}

			if (_.has(_reqData, "clinic_id") === true) {
				_filter.clinic_id = _reqData?.clinic_id
			}

			if (_.has(_reqData, "status") === true && (_reqData.status == 1 || _reqData?.status == 3)) {
				_filter.status = _reqData?.status
			}

			if (!_.isEmpty(_reqData.searchText)) {
				_filter[Op.or] = [
					{ name: { [Op.iLike]: `%${_reqData.searchText}%` } },
					{ email: { [Op.iLike]: `%${_reqData.searchText}%` } },
					{ subject: { [Op.iLike]: `%${_reqData.searchText}%` } },
					{ message: { [Op.iLike]: `%${_reqData.searchText}%` } },
				];
			}

			if (!_.isEmpty(_reqData.startDate) && !_.isEmpty(_reqData.endDate)) {
				const startedDate: any = new Date(_reqData?.startDate);
				const endDate: any = new Date(_reqData?.endDate);
				// Set the end date to the end of the day
				endDate.setHours(23, 59, 59, 999);
				_filter.createdAt = { [Op.between]: [startedDate, endDate] }
			}

			if (_params.page && _.isInteger(_.parseInt(_params.page)) && _.parseInt(_params.page) > 0) {
				_offset = _recordsLimit * (_.parseInt(_params.page) - 1)
			}
			const { count, rows }: any = await Models.Support.findAndCountAll({
				where: _filter,
				order: [['createdAt', 'DESC'], ['status', 'ASC']],
				limit: _recordsLimit,
				offset: _offset,
				include: [
					{
						model: Models.Clinics,
						as: 'clinic',
						attributes: ['id', 'name', 'status'],
					},
				]
			});


			_.assign(_retData, {
				data: UtilsHelper.cryptoJsObjectEncrypt({
					totalRecords: count,
					recordsPerPage: _recordsLimit,
					list: rows,
				}),
				msgCode: 10066,
				msg: UtilsHelper.getMessage(10066),
			})

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in SupportController.getSupportList')
		}

		return UtilsHelper.cRes(res, _retData)
	}



	/**
	 * Method to fetch detail support data from support Id
	 */
	public async supportDeatilFromSupportId(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const _params = req.params
			const supportId = _params.supportId
			const ckSupportData: any = await Models.Support.findOne({
				where: { status: { [Op.ne]: 9 }, id: supportId },
				include: [
					{
						model: Models.Clinics,
						as: 'clinic',
						attributes: ['id', 'name', 'status'],
					},
				]
			})

			if (ckSupportData) {

				_.assign(_retData, {
					msgCode: 10067,
					data: UtilsHelper.cryptoJsObjectEncrypt(ckSupportData),
					msg: UtilsHelper.getMessage('10067', 'en'),
				})
			} else {
				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50012,
					msg: UtilsHelper.getMessage('50012', 'en'),
				})
			}
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in SupportController.supportDeatilFromSupportId')
		}

		return UtilsHelper.cRes(res, _retData)
	}

	/**
	 * Method to update status support data from support Id
	 */
	public async updateStatusFromSupportId(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const _params = req.params
			const reqBody = req.body;
			const supportId = _params.supportId
			const ckSupportData: any = await Models.Support.findOne({
				where: { status: { [Op.ne]: 9 }, id: supportId },
				include: [
					{
						model: Models.Clinics,
						as: 'clinic',
						attributes: ['id', 'name', 'status'],
					},
				]
			})

			if (ckSupportData) {

				await Models.Support.update({ status: reqBody.status }, {
					where: {
						id: supportId
					}
				});

				_.assign(_retData, {
					msgCode: 10072,
					data: UtilsHelper.cryptoJsObjectEncrypt(ckSupportData),
					msg: UtilsHelper.getMessage('10072', 'en'),
				})
			} else {
				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50012,
					msg: UtilsHelper.getMessage('50012', 'en'),
				})
			}
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in SupportController.updateStatusFromSupportId')
		}

		return UtilsHelper.cRes(res, _retData)
	}

	/**
	* Method to update status support data from support Id
	*/
	public async adminReplyFromSupportId(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const _params = req.params
			const reqBody = req.body;
			const supportId = _params.supportId
			const supportData: any = {}
			const ckSupportData: any = await Models.Support.findOne({
				where: { status: { [Op.ne]: 9 }, id: supportId },
				include: [
					{
						model: Models.Clinics,
						as: 'clinic',
						attributes: ['id', 'name', 'status'],
					},
				]
			})

			if (ckSupportData) {
				supportData.name = ckSupportData.name;
				supportData.email = ckSupportData.email;
				supportData.subject = reqBody.subject;
				supportData.message = reqBody.message;

				if (supportData) {
					await Models.Support.update({ status: 3 }, {
						where: {
							id: supportId
						}
					});
					//send email
					await supportHelper.supportEmail(supportData);

				}

				_.assign(_retData, {
					msgCode: 10075,
					//data: UtilsHelper.cryptoJsObjectEncrypt(ckSupportData),
					msg: UtilsHelper.getMessage('10075', 'en'),
				})
			} else {
				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50012,
					msg: UtilsHelper.getMessage('50012', 'en'),
				})
			}
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in SupportController.adminReplyFromSupportId')
		}

		return UtilsHelper.cRes(res, _retData)
	}


	/**
	 * Method to upload file and image
	 */
	public async uploadImage(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const reqFiles: any = req.files;
			const _reqBody = req.body;

			const file: any = reqFiles ? reqFiles[0] : null;
			const data: any = {
				url: null,
				uploaded: 0,
				fileName: null

			}

			//type check
			const timestamp = Date.now();

			if (file) {
				const ext = path.extname(file.originalname);
				const image = "img_" + timestamp + ext;
				const image_name = "backend/cms/" + image;
				const uploaded = await savePublicImage(file.path, image_name);

				if (uploaded) {
					data.uploaded = 1;
					data.fileName = image
					data.url = process.env.S3_BUCKET_URL + "/" + image_name;
					const directory = 'src/uploads';
					return res.status(200).json(data)
				} else {
					return res.status(200).json({
						uploaded: 0,
						"error": "Something went wrong in file upload"
					})
				}

			} else {
				return res.status(200).json({
					uploaded: 0,
					"error": "file is missing"
				})
			}


		} catch (err: any) {

			this.logErrors(err, 'Error in CmsController.uploadImage');
			return res.status(400).json({
				uploaded: 0,
				"error": err?.message
			})
		}

		//return this.sendResponse(res, _retData)
	}

}

const supportController = new SupportController()

export default supportController