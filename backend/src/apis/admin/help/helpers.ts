import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../../../helpers/token.helper'
import * as <PERSON>ailHel<PERSON> from '../../../helpers/email.helper'
import _ from 'lodash'
const { Op, Sequelize } = require('sequelize')
import Models from '../../../database/models'
import * as UtilsHelper from '../../../helpers/utils.helper'

export async function sendContactUsEmail(userData: any) {
	// prepare token and email data
	userData.message = userData.message.replace(/\n/g, '<br>');
	const _emailData = {
		toEmail: userData.email,
		data: userData,
	}

	// Send conatct us email from admin
	await EmailHelper.userContactUsEmail(_emailData).then(data => console.log(data))

	return true
}

export async function sendFeedbackEmail(userData: any) {
	// prepare token and email data
	userData.message = userData.message.replace(/\n/g, '<br>');
	const _emailData = {
		toEmail: userData.email,
		data: userData,
	}
	// Send feedback email from admin
	await EmailHelper.userFeedbackEmail(_emailData).then(data => console.log(data))

	return true
}

export async function supportEmail(userData: any) {
	// prepare token and email data
	userData.message = userData.message.replace(/\n/g, '<br>');
	const _emailData = {
		toEmail: userData.email,
		data: userData,
	}
	// Send support  email from admin
	await EmailHelper.userSupportEmail(_emailData).then(data => console.log(data))

	return true
}
