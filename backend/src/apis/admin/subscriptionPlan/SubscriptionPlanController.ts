import * as express from 'express'
import * as UtilsHelper from '../../../helpers/utils.helper'
import { IResponseObject } from '../../../helpers/utils.interface'
import BaseController from '../../../helpers/BaseController'
import _ from 'lodash'
import Strip<PERSON> from 'stripe';
import Models from '../../../database/models'
//import * as Helper from './helper'
import { Op, Sequelize } from 'sequelize'
import stripeClient from "../../../services/Stripe";
import * as <PERSON><PERSON> from "joi";
import moment from "moment/moment";

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY, {
	apiVersion: '2023-10-16',
	appInfo: { // For sample support and debugging, not required for production:
		name: "ClinicalPad Subscription",
		version: "0.0.1",
		url: "https://clinicalpad.com"
	}
});
function mapSubscriptionPlanInput(input: any) {
	return {
		name: input.name,
		price: input.price,
		currency: input.currency,
		description: input.description,
		audio_consultation_minutes: input.audio_consultation_mins ?? 0, // Default 0 if not provided
		custom_templates_limit: input.custom_templates ?? 5, // Default 5
		design_templates_limit: input.designs ?? 0, // Default 0
		allows_team_members: input.team_members ?? false, // Default false
		trial_period_days: input.trial_period_days ?? 0, // Default 0
		has_user_logs: input.user_log ?? false,
		has_medical_history: input.medical_history ?? false,
		has_tasks: input.tasks ?? false,
		plan_type: input.plan_type, // Ensure it is 'Monthly' or 'Yearly'
		custom_max_team_members: input.max_team_members ?? 0, // Default 0
		free_support: input.free_support ?? false, // Default false
		status: 1, // Default 1 (Active)
		stripe_product_id: input.stripe_product_id,
		user_id: input.user_id, // Ensure this user exists in clinical_users
		is_custom: input.is_custom,
		plan_details: input.plan_details,
		subscription_features : input.subscription_features,
	};
}

/**
 * Fetches the current USD to GBP conversion rate from an external API.
 *
 * @returns {Promise<number>} - A promise that resolves to the conversion rate.
 */
async function getUsdToGbpConversionRate() {
	try {
		// Replace the URL with your chosen API endpoint if needed
		const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
		const data = await response.json();

		if (!data.rates || !data.rates.GBP) {
			throw new Error('GBP rate not found in API response');
		}

		return data.rates.GBP;
	} catch (error) {
		console.error('Error fetching conversion rate:', error);
		throw error;
	}
}

/**
 * Converts a given USD amount to GBP using a dynamic conversion rate.
 *
 * @param {number} usdAmount - The amount in USD.
 * @returns {Promise<number>} - A promise that resolves to the equivalent amount in GBP.
 */
async function convertUsdToGbp(usdAmount) {
	const rate = await getUsdToGbpConversionRate();
	return usdAmount * rate;
}

class SubscriptionPlanController extends BaseController {
	constructor() {
		super()
		this.addPlan = this.addPlan.bind(this);
		this.editPlan = this.editPlan.bind(this);
		this.getPlanList = this.getPlanList.bind(this);
		this.detailPlan = this.detailPlan.bind(this);
		this.deletePlan = this.deletePlan.bind(this);
		this.changePlanStatus = this.changePlanStatus.bind(this);
		this.addOrUpdateSubscriptionPlanClinicalUser = this.addOrUpdateSubscriptionPlanClinicalUser.bind(this);
		this.createCoupon = this.createCoupon.bind(this);
		this.listCouponsForAdmin = this.listCouponsForAdmin.bind(this);
		this.deleteCoupon = this.deleteCoupon.bind(this);
		this.applyCouponToUsers = this.applyCouponToUsers.bind(this);
		this.getCoupon = this.getCoupon.bind(this);
		this.getSubscriptionsStatsWithChurnRenewal = this.getSubscriptionsStatsWithChurnRenewal.bind(this);
	}

	public async getSubscriptionsStatsWithChurnRenewal(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData = UtilsHelper.responseObject();
		try {
			// Parse period parameters.
			const startDateStr = (req.query.startDate as string) || "2025-02-13T00:00:00Z";
			const endDateStr = (req.query.endDate as string) || "2025-04-28T23:59:59Z";
			const startDateUnix = Math.floor(new Date(startDateStr).getTime() / 1000);
			const endDateUnix = Math.floor(new Date(endDateStr).getTime() / 1000);

			// Fetch subscriptions from Stripe (limit to 100 for demonstration).
			const subsResponse = await stripeClient.subscriptions.list({
				limit: 100,
				status: 'all',
			});
			const subscriptions = subsResponse.data;

			// Helper functions to filter subscriptions based on the period.
			const filterActiveAtStart = (subs: any[]) => {
				console.log(subs)
				return subs.filter(sub => {
					const created = sub.created; // Unix timestamp
					const canceledAt = sub.canceled_at; // might be null
					return created < startDateUnix && (!canceledAt || canceledAt >= startDateUnix);
				});
			};

			const filterCancellations = (subs: any[]) => {
				return subs.filter(sub => {
					const canceledAt = sub.canceled_at;
					return canceledAt && canceledAt >= startDateUnix && canceledAt <= endDateUnix;
				});
			};

			const calculateRates = (subs: any[]) => {
				const activeAtStart = filterActiveAtStart(subs);
				const cancellations = filterCancellations(activeAtStart);
				const startCount = activeAtStart.length;
				const cancellationCount = cancellations.length;
				const churnRate = startCount ? ((cancellationCount / startCount) * 100).toFixed(2) + "%" : "N/A";
				const renewalRate = startCount ? (((startCount - cancellationCount) / startCount) * 100).toFixed(2) + "%" : "N/A";
				return { activeAtStart, cancellationCount, churnRate, renewalRate };
			};

			// 1. Retrieve standard (non-custom) plans along with their prices.
			const standardPlans = await Models.SubscriptionPlan.findAll({
				where: {
					name: { [Models.Sequelize.Op.in]: ['Starter', 'Core', 'Pro'] },
					is_custom: false,
				},
				include: [{
					model: Models.SubscriptionPlanPrices,
					as: 'prices',
				}],
			});

			// 2. Retrieve custom plans (Enterprise).
			const enterprisePlans = await Models.SubscriptionPlan.findAll({
				where: { is_custom: true },
				include: [{
					model: Models.SubscriptionPlanPrices,
					as: 'prices',
				}],
			});

			// 3. Build stats for standard plans.
			const stats: any[] = [];
			for (const plan of standardPlans) {
				if (plan.name === 'Starter') {
					// For Starter, count active users from ClinicalUsers, as subscriptions are not in ClinicalUserSubscriptions.
					const starterCount = await Models.ClinicalUsers.count({
						where: {
							subscription_plan_id: plan.id,
							status: { [Models.Sequelize.Op.ne]: 9 }
						},
					});
					stats.push({
						package: plan.name,
						subscribed: starterCount,
						churnRate: "N/A",
						renewalRate: "N/A",
					});
				} else {
					// For Core and Pro, filter subscriptions from Stripe by comparing any subscription item price against plan prices.
					const planSubs = subscriptions.filter(sub => {
						const items = sub.items.data;
						return items && items.length > 0 && plan.prices && plan.prices.length > 0 &&
							items.some((item: any) =>
								plan.prices.some((price: any) => price.stripe_price_id === item.price.id)
							);
					});
					const rates = calculateRates(planSubs);
					stats.push({
						package: plan.name,
						subscribed: rates.activeAtStart.length,
						churnRate: rates.churnRate,
						renewalRate: rates.renewalRate,
					});
				}
			}

			// 4. For Enterprise, combine all subscriptions belonging to any custom plan.
			const enterprisePriceIds = enterprisePlans.flatMap((plan: any) =>
				plan.prices.map((price: any) => price.stripe_price_id)
			);
			const enterpriseSubs = subscriptions.filter(sub => {
				const items = sub.items.data;
				return items && items.length > 0 &&
					items.some((item: any) => enterprisePriceIds.includes(item.price.id));
			});
			const enterpriseRates = calculateRates(enterpriseSubs);
			stats.push({
				package: 'Enterprise',
				subscribed: enterpriseRates.activeAtStart.length,
				churnRate: enterpriseRates.churnRate,
				renewalRate: enterpriseRates.renewalRate,
			});

			_.assign(_retData, {
				data: {
					period: { startDate: startDateStr, endDate: endDateStr },
					stats,
				},
				msgCode: 10120,
				msg: 'Subscription stats with churn and renewal rates retrieved successfully',
			});
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			});
		}
		return UtilsHelper.cRes(res, _retData);
	}

	public async getBillingStats	(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData = UtilsHelper.responseObject();
		try {
			const filter = ((req.query.filter as string) || "ALL").toUpperCase();
			let startDate: string, endDate: string;

			// Determine date range based on filter
			switch (filter) {
				case "TODAY":
					startDate = moment().startOf("day").toISOString();
					endDate = moment().endOf("day").toISOString();
					break;
				case "THIS_MONTH":
					startDate = moment().startOf("month").toISOString();
					endDate = moment().endOf("month").toISOString();
					break;
				case "THIS_YEAR":
					startDate = moment().startOf("year").toISOString();
					endDate = moment().endOf("year").toISOString();
					break;
				case "ALL":
				default:
					startDate = "2025-02-01T00:00:00Z";
					endDate = moment().endOf("year").toISOString();
					break;
			}

			const startDateUnix = Math.floor(new Date(startDate).getTime() / 1000);
			const endDateUnix = Math.floor(new Date(endDate).getTime() / 1000);

			// Fetch subscriptions
			const subsResponse = await stripeClient.subscriptions.list({
				limit: 100,
				status: "all",
			});
			const subscriptions = subsResponse.data;

			// Fetch invoices
			const invoicesResponse = await stripeClient.invoices.list({
				limit: 100,
				created: { gte: startDateUnix, lte: endDateUnix },
			});
			const invoices = invoicesResponse.data;

			// Fetch outstanding invoices (unpaid or past due)
			const outstandingInvoices = invoices.filter(
				(invoice) => invoice.status === "open" || invoice.status === "past_due"
			);

			// Calculate Total Revenue
			const totalRevenue = invoices
			.filter((invoice) => invoice.status === "paid")
			.reduce((sum, invoice) => sum + invoice.amount_paid / 100, 0); // Convert cents to dollars

			// Calculate Recurring Revenue
			const recurringRevenue = subscriptions
			.filter((sub) => ["active", "trialing"].includes(sub.status))
			.reduce((sum, sub) => {
				return sum + sub.items.data.reduce((itemSum, item) => itemSum + item.price.unit_amount / 100, 0);
			}, 0);

			// Active Subscriptions at Start
			const activeAtStart = subscriptions.filter((sub) => {
				console.log(sub)
				const created = sub.created;
				const canceledAt = sub.canceled_at;
				return created < startDateUnix && (!canceledAt || canceledAt >= startDateUnix);
			});

			// Cancellations within the period
			const canceledWithinPeriod = activeAtStart.filter((sub) => {
				return sub.canceled_at && sub.canceled_at >= startDateUnix && sub.canceled_at <= endDateUnix;
			});

			// Calculate Rates
			const startCount = activeAtStart.length;
			const cancellationCount = canceledWithinPeriod.length;
			const churnRate = startCount ? ((cancellationCount / startCount) * 100).toFixed(2) + "%" : "N/A";
			const renewalRate = startCount ? (((startCount - cancellationCount) / startCount) * 100).toFixed(2) + "%" : "N/A";

			// Outstanding Invoices %
			const totalInvoices = invoices.length;
			const outstandingInvoicePercentage =
				totalInvoices > 0 ? ((outstandingInvoices.length / totalInvoices) * 100).toFixed(2) + "%" : "0%";

			// Response
			_.assign(_retData, {
				data: {
					startDate,
					endDate,
					totalRevenue: `$${totalRevenue.toFixed(2)}`,
					recurringRevenue: `$${recurringRevenue.toFixed(2)}`,
					renewalRate,
					outstandingInvoices: outstandingInvoicePercentage,
					churnRate,
					subscribed: startCount, // Total active subscriptions at start
				},
				msgCode: 10120,
				msg: "Subscription stats retrieved successfully",
			});
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: "error",
				msg: err.message,
			});
		}
		return UtilsHelper.cRes(res, _retData);
	}


	// Helper method to get date range based on filter
	private getDateRange(filter: string) {
		const now = new Date();
		let startDateStr, endDateStr;

		switch (filter) {
			case 'TODAY':
				startDateStr = new Date(now.setHours(0, 0, 0, 0)).toISOString();
				endDateStr = new Date(now.setHours(23, 59, 59, 999)).toISOString();
				break;
			case 'THIS_MONTH':
				startDateStr = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
				endDateStr = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999).toISOString();
				break;
			case 'THIS_YEAR':
				startDateStr = new Date(now.getFullYear(), 0, 1).toISOString();
				endDateStr = new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999).toISOString();
				break;
			default:
				startDateStr = "2025-01-01T00:00:00Z"; // Example default for ALL
				endDateStr = now.toISOString();
		}

		return { startDateStr, endDateStr };
	}

	// public async getSubscriptionsStatsWithChurnRenewal(req: express.Request, res: express.Response): Promise<void | any> {
	// 	const _retData = UtilsHelper.responseObject();
	// 	try {
	// 		// Parse period parameters.
	// 		const startDateStr = (req.query.startDate as string) || "2025-03-13T00:00:00Z";
	// 		const endDateStr = (req.query.endDate as string) || "2025-04-28T23:59:59Z";
	// 		const startDateUnix = Math.floor(new Date(startDateStr).getTime() / 1000);
	// 		const endDateUnix = Math.floor(new Date(endDateStr).getTime() / 1000);
	//
	// 		// Fetch subscriptions from Stripe (limit to 100 for demonstration).
	// 		const subsResponse = await stripeClient.subscriptions.list({
	// 			limit: 100,
	// 			status: 'all',
	// 		});
	// 		const subscriptions = subsResponse.data;
	//
	// 		// Helper functions to filter subscriptions based on the period.
	// 		const filterActiveAtStart = (subs: any[]) => {
	// 			return subs.filter(sub => {
	// 				const created = sub.created; // Unix timestamp
	// 				const canceledAt = sub.canceled_at; // might be null
	// 				return created < startDateUnix && (!canceledAt || canceledAt >= startDateUnix);
	// 			});
	// 		};
	//
	// 		const filterCancellations = (subs: any[]) => {
	// 			return subs.filter(sub => {
	// 				const canceledAt = sub.canceled_at;
	// 				return canceledAt && canceledAt >= startDateUnix && canceledAt <= endDateUnix;
	// 			});
	// 		};
	//
	// 		const calculateRates = (subs: any[]) => {
	// 			const activeAtStart = filterActiveAtStart(subs);
	// 			const cancellations = filterCancellations(activeAtStart);
	// 			const startCount = activeAtStart.length;
	// 			const cancellationCount = cancellations.length;
	// 			const churnRate = startCount ? ((cancellationCount / startCount) * 100).toFixed(2) + "%" : "N/A";
	// 			const renewalRate = startCount ? (((startCount - cancellationCount) / startCount) * 100).toFixed(2) + "%" : "N/A";
	// 			return { activeAtStart, cancellationCount, churnRate, renewalRate };
	// 		};
	//
	// 		// 1. Retrieve standard (non-custom) plans along with their prices.
	// 		const standardPlans = await Models.SubscriptionPlan.findAll({
	// 			where: {
	// 				name: { [Models.Sequelize.Op.in]: ['Starter', 'Core', 'Pro'] },
	// 				is_custom: false,
	// 			},
	// 			include: [{
	// 				model: Models.SubscriptionPlanPrices,
	// 				as: 'prices',
	// 			}],
	// 		});
	//
	// 		// 2. Retrieve custom plans (Enterprise).
	// 		const enterprisePlans = await Models.SubscriptionPlan.findAll({
	// 			where: { is_custom: true },
	// 			include: [{
	// 				model: Models.SubscriptionPlanPrices,
	// 				as: 'prices',
	// 			}],
	// 		});
	//
	// 		// 3. For each standard plan, filter subscriptions whose first item's price
	// 		// matches any of the plan's prices.
	// 		const stats: any[] = [];
	// 		for (const plan of standardPlans) {
	// 			const planSubs = subscriptions.filter(sub => {
	// 				const items = sub.items.data;
	// 				// Ensure both the subscription and the plan have at least one price.
	// 				return items && items.length > 0 && plan.prices && plan.prices.length > 0 &&
	// 					// Check if any subscription item matches any price from the plan.
	// 					items.some((item: any) =>
	// 						plan.prices.some((price: any) => price.stripe_price_id === item.price.id)
	// 					);
	// 			});
	// 			const rates = calculateRates(planSubs);
	// 			stats.push({
	// 				package: plan.name,
	// 				subscribed: rates.activeAtStart.length,
	// 				churnRate: rates.churnRate,
	// 				renewalRate: rates.renewalRate,
	// 			});
	// 		}
	//
	// 		// 4. For Enterprise, combine all subscriptions belonging to any custom plan.
	// 		const enterprisePriceIds = enterprisePlans.flatMap((plan: any) =>
	// 			plan.prices.map((price: any) => price.stripe_price_id)
	// 		);
	// 		const enterpriseSubs = subscriptions.filter(sub => {
	// 			const items = sub.items.data;
	// 			return items && items.length > 0 &&
	// 				items.some((item: any) => enterprisePriceIds.includes(item.price.id));
	// 		});
	// 		const enterpriseRates = calculateRates(enterpriseSubs);
	// 		stats.push({
	// 			package: 'Enterprise',
	// 			subscribed: enterpriseRates.activeAtStart.length,
	// 			churnRate: enterpriseRates.churnRate,
	// 			renewalRate: enterpriseRates.renewalRate,
	// 		});
	//
	// 		_.assign(_retData, {
	// 			data: {
	// 				period: { startDate: startDateStr, endDate: endDateStr },
	// 				stats,
	// 			},
	// 			msgCode: 10120,
	// 			msg: 'Subscription stats with churn and renewal rates retrieved successfully',
	// 		});
	// 	} catch (err: any) {
	// 		_.assign(_retData, {
	// 			statusCode: 500,
	// 			status: 'error',
	// 			msg: err.message,
	// 		});
	// 	}
	// 	return UtilsHelper.cRes(res, _retData);
	// }

	// public async getSubscriptionsStatsWithChurnRenewal(req: express.Request, res: express.Response): Promise<void | any> {
	// 	const _retData = UtilsHelper.responseObject();
	// 	try {
	// 		// 1. Parse period parameters.
	// 		const startDateStr = (req.query.startDate as string) || "2023-02-01T00:00:00Z";
	// 		const endDateStr = (req.query.endDate as string) || "2023-02-28T23:59:59Z";
	// 		const startDateUnix = Math.floor(new Date(startDateStr).getTime() / 1000);
	// 		const endDateUnix = Math.floor(new Date(endDateStr).getTime() / 1000);
	//
	// 		// 2. Fetch subscriptions from Stripe (limit to 100 for demo purposes).
	// 		const subsResponse = await stripeClient.subscriptions.list({
	// 			limit: 100,
	// 			status: 'all', // include all statuses for churn analysis
	// 		});
	// 		const subscriptions = subsResponse.data;
	//
	// 		// Helper functions to filter subscriptions based on period.
	// 		const filterActiveAtStart = (subs: any[]) => {
	// 			return subs.filter(sub => {
	// 				const created = sub.created; // Unix timestamp
	// 				const canceledAt = sub.canceled_at; // might be null
	// 				return created < startDateUnix && (!canceledAt || canceledAt >= startDateUnix);
	// 			});
	// 		};
	//
	// 		const filterCancellations = (subs: any[]) => {
	// 			return subs.filter(sub => {
	// 				const canceledAt = sub.canceled_at;
	// 				return canceledAt && canceledAt >= startDateUnix && canceledAt <= endDateUnix;
	// 			});
	// 		};
	//
	// 		// Given a list of subscriptions, calculate churn and renewal rates.
	// 		const calculateRates = (subs: any[]) => {
	// 			const activeAtStart = filterActiveAtStart(subs);
	// 			const cancellations = filterCancellations(activeAtStart);
	// 			const startCount = activeAtStart.length;
	// 			const cancellationCount = cancellations.length;
	// 			const churnRate = startCount ? ((cancellationCount / startCount) * 100).toFixed(2) + "%" : "N/A";
	// 			const renewalRate = startCount ? (((startCount - cancellationCount) / startCount) * 100).toFixed(2) + "%" : "N/A";
	// 			return { activeAtStart, cancellationCount, churnRate, renewalRate };
	// 		};
	//
	// 		// 3. Retrieve plan data from your local DB.
	// 		// Standard plans (non-custom): Starter, Core, Pro.
	// 		const standardPlans = await Models.SubscriptionPlan.findAll({
	// 			where: {
	// 				name: { [Models.Sequelize.Op.in]: ['Starter', 'Core', 'Pro'] },
	// 				is_custom: false,
	// 			},
	// 			include: [
	// 				{
	// 					model: Models.SubscriptionPlanPrices,
	// 					as: 'prices',
	// 					required: false,
	// 				},
	// 			],
	// 		});
	//
	// 		// Enterprise: all custom plans.
	// 		const enterprisePlans = await Models.SubscriptionPlan.findAll({
	// 			where: { is_custom: true },
	// 			include: [
	// 				{
	// 					model: Models.SubscriptionPlanPrices,
	// 					as: 'prices',
	// 					required: false,
	// 				},
	// 			],
	// 		});
	//
	// 		// 4. Build statistics for standard plans.
	// 		const stats: any[] = [];
	// 		for (const plan of standardPlans) {
	// 			// Filter subscriptions for which the first item's price ID matches the plan's stripe_price_id.
	// 			const planSubs = subscriptions.filter(sub => {
	// 				const items = sub.items.data;
	// 				return items && items.length > 0 && items[0].price.id === plan.prices[0].stripe_price_id;
	// 			});
	// 			const rates = calculateRates(planSubs);
	// 			stats.push({
	// 				package: plan.name,
	// 				subscribed: rates.activeAtStart,
	// 				churnRate: rates.churnRate,
	// 				renewalRate: rates.renewalRate,
	// 			});
	// 		}
	//
	// 		// 5. For Enterprise, combine all subscriptions that belong to any custom plan.
	// 		const enterprisePriceIds = enterprisePlans.map(plan => plan.stripe_price_id);
	// 		const enterpriseSubs = subscriptions.filter(sub => {
	// 			const items = sub.items.data;
	// 			return items && items.length > 0 && enterprisePriceIds.includes(items[0].price.id);
	// 		});
	// 		const enterpriseRates = calculateRates(enterpriseSubs);
	// 		stats.push({
	// 			package: 'Enterprise',
	// 			subscribed: enterpriseRates.activeAtStart,
	// 			churnRate: enterpriseRates.churnRate,
	// 			renewalRate: enterpriseRates.renewalRate,
	// 		});
	//
	// 		_.assign(_retData, {
	// 			data: {
	// 				period: { startDate: startDateStr, endDate: endDateStr },
	// 				stats,
	// 			},
	// 			msgCode: 10120,
	// 			msg: 'Subscription stats with churn and renewal rates retrieved successfully',
	// 		});
	// 	} catch (err: any) {
	// 		_.assign(_retData, {
	// 			statusCode: 500,
	// 			status: 'error',
	// 			msg: err.message,
	// 		});
	// 	}
	// 	return UtilsHelper.cRes(res, _retData);
	// }

	public async applyCouponToUsers(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject();
		try {
			// Expect the coupon ID, a scope ("all" or "selected") and, if selected, an array of user IDs.
			const { coupon_id, scope, user_ids } = req.body;

			// Validate that the coupon exists on Stripe.
			const coupon = await stripeClient.coupons.retrieve(coupon_id);
			if (!coupon) {
				throw new Error("Coupon not found in Stripe.");
			}

			let results = [];

			if (scope === "all") {
				// Apply coupon to all active customers.
				const customers = await Models.ClinicalUserStripeCustomer.findAll({
					where: { status: { [Op.ne]: 9 } }
				});
				results = await Promise.all(customers.map(async (customerRecord) => {
					try {
						const discount = await stripeClient.customers.createDiscount(
							customerRecord.customer_id,
							{ coupon: coupon_id }
						);
						return { customer_id: customerRecord.customer_id, discount };
					} catch (error: any) {
						return { customer_id: customerRecord.customer_id, error: error.message };
					}
				}));

				_.assign(_retData, {
					data: results,
					msgCode: 10116,
					msg: "Coupon applied successfully to all customers.",
				});
			} else if (scope === "selected") {
				// Validate that an array of user_ids is provided.
				if (!user_ids || !Array.isArray(user_ids) || user_ids.length === 0) {
					throw new Error("For scope 'selected', an array of user_ids must be provided.");
				}

				// Retrieve the stripe customer records for these user IDs.
				const customers = await Models.ClinicalUserStripeCustomer.findAll({
					where: { user_id: user_ids, status: { [Op.ne]: 9 } }
				});
				results = await Promise.all(
					customers.map(async (customerRecord) => {
						try {
							// Apply a discount by updating the customer with a coupon
							const customer = await stripeClient.customers.update(
								customerRecord.customer_id,
								{ coupon: coupon_id }
							);
							// The updated customer object now contains the discount information
							return { customer_id: customerRecord.customer_id, discount: customer.discount };
						} catch (error) {
							return { customer_id: customerRecord.customer_id, error: error.message };
						}
					})
				);


				_.assign(_retData, {
					data: results,
					msgCode: 10116,
					msg: "Coupon applied successfully to selected customers.",
				});
			} else {
				throw new Error("Invalid scope provided. Use 'all' or 'selected'.");
			}
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			});
		}
		return UtilsHelper.cRes(res, _retData);
	}

	public async deleteCoupon(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject();
		try {
			const couponId = req.params.id;

			// Get the current Unix timestamp in seconds.
			const now = Math.floor(Date.now() / 1000);

			// Update the coupon in Stripe: set redeem_by to a past timestamp to disable it.
			const expiredCoupon = await stripeClient.coupons.update(couponId, {
				redeem_by: now - 1,
			});

			// Update the local coupon record to mark it as inactive.
			// This assumes your Coupons model has a "status" field (e.g., "active" or "inactive").
			await Models.Coupons.update(
				{ status: 0 },
				{ where: { stripe_coupon_id: couponId } }
			);

			_.assign(_retData, {
				data: expiredCoupon,
				msgCode: 10114,
				msg: "Coupon expired successfully (simulated deletion)",
			});
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			});
		}
		return UtilsHelper.cRes(res, _retData);
	}

	public async listCouponsForAdmin(req: express.Request, res: express.Response): Promise<void | any>  {
		const _retData: any = UtilsHelper.responseObject();
		try {
			// Get all local coupon records.
			const localCoupons = await Models.Coupons.findAll({
				where: {
					status: {
						[Op.in]: [0, 1]
					}
				}
			});

			// For each coupon, retrieve the latest data from Stripe.
			const updatedCoupons = await Promise.all(localCoupons.map(async (coupon) => {
				const stripeCoupon = await stripeClient.coupons.retrieve(coupon.stripe_coupon_id);

				// Check custom metadata "start_date" to ensure coupon is redeemable only when valid.
				let isActive = true;
				if (stripeCoupon.metadata && stripeCoupon.metadata.start_date) {
					const startDate = parseInt(stripeCoupon.metadata.start_date, 10);
					const now = Math.floor(Date.now() / 1000);
					isActive = now >= startDate;
				}

				return {
					local: coupon,
					stripe: stripeCoupon,
					active: isActive,
					times_redeemed: stripeCoupon.times_redeemed,
				};
			}));

			_.assign(_retData, {
				data: updatedCoupons,
				msgCode: 10120,
				msg: "Coupons retrieved successfully with updated stats",
			});
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			});
		}
		return UtilsHelper.cRes(res, _retData);
	}

	public async getCoupon(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject();
		try {
			// Get the coupon ID from the URL parameters
			const couponId = req.params.id;

			// Retrieve the coupon from Stripe using the coupon ID
			const coupon = await stripeClient.coupons.retrieve(couponId);

			// Return the coupon details in the response
			_.assign(_retData, {
				data: coupon,
				msgCode: 10111,
				msg: "Coupon retrieved successfully",
			});
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			});
		}
		return UtilsHelper.cRes(res, _retData);
	}

	/**
	 * Create a new coupon in Stripe.
	 * Expects in req.body:
	 *  - percent_off: number (percentage discount) [optional]
	 *  - amount_off: number (flat discount in the smallest currency unit) [optional]
	 *  - duration: string ("once", "repeating", or "forever")
	 *  - duration_in_months: number (if duration is "repeating")
	 *  - redeem_by: timestamp (optional; Unix timestamp in seconds)
	 *  - max_redemptions: number (optional)
	 *  - name: string (optional, for your reference)
	 *  - metadata: object (optional, can include custom start_date field)
	 */
	public async createCoupon(req: express.Request, res: express.Response): Promise<void | any>  {
		const _retData: any = UtilsHelper.responseObject();
		try {
			const {
				percent_off,
				amount_off,
				currency_options, // Optional custom multi-currency options can be provided instead
				duration,
				duration_in_months,
				redeem_by,
				max_redemptions,
				name,
				metadata,
				active,
			} = req.body;

			// Build the coupon payload
			const couponPayload: any = {
				duration,
				duration_in_months: duration === 'repeating' ? duration_in_months : undefined,
				redeem_by,
				max_redemptions,
				name,
				metadata,
			};

			if (percent_off) {
				// Percentage-based discount – cannot combine with fixed amounts.
				couponPayload.percent_off = percent_off;
			} else if (amount_off) {
				// Fixed-amount discount provided in USD.
				// Convert to GBP using a conversion rate from the environment or default to 0.75.
				const usdAmount = amount_off;
				const conversionRate = Number(getUsdToGbpConversionRate()) || 0.75;
				const gbpAmount = Math.round(usdAmount * conversionRate);
				couponPayload.currency_options = {
					// usd: { amount_off: usdAmount },
					gbp: { amount_off: gbpAmount },
				};
				couponPayload.amount_off = usdAmount;
				couponPayload.currency = 'usd';
			} else if (currency_options) {
				// Custom multi-currency options provided directly.
				couponPayload.currency_options = currency_options;
			} else {
				throw new Error("Must provide either 'percent_off', 'amount_off', or 'currency_options' for a valid coupon.");
			}

			// Create the coupon in Stripe using the constructed payload.
			const coupon = await stripeClient.coupons.create(couponPayload);

			// Save the coupon locally for display in the admin dashboard.
			await Models.Coupons.create({
				stripe_coupon_id: coupon.id,
				name: coupon.name,
				percent_off: coupon.percent_off,
				amount_off: coupon.amount_off,
				duration: coupon.duration,
				duration_in_months: coupon.duration_in_months,
				redeem_by: coupon.redeem_by,
				max_redemptions: coupon.max_redemptions,
				metadata: coupon.metadata,
				status: active ? 1 : 0,
				currency_options: coupon.currency_options || null,
			});

			_.assign(_retData, {
				data: coupon,
				msgCode: 10110,
				msg: "Coupon created successfully",
			});
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			});
		}
		return UtilsHelper.cRes(res, _retData);
	}


	/**
	 * Method to create plan from super admin with Stripe integration
	 */
	public async addPlan(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject();
		try {
			const reqBody = req.body;
			// Extract email and fetch the user from ClinicalUsers
			const userEmail = reqBody.email;
			const user = await Models.ClinicalUsers.findOne({ where: { email: userEmail } });
			if (!user) {
				_.assign(_retData, {
					status: 'error',
					statusCode: 404,
					msgCode: 50053,
					msg: UtilsHelper.getMessage(50053),
				});
				return UtilsHelper.cRes(res, _retData);
			}
			const user_id = user.id;

			// Check for an existing plan
			const existingPlan = await Models.SubscriptionPlan.findOne({
				where: {
					name: reqBody.name,
					currency: reqBody.currency,
					plan_type: reqBody.plan_type,
					user_id: user_id, // Ensure the plan belongs to this user
					status: { [Op.notIn]: [9] },
				},
			});
			if (existingPlan) {
				_.assign(_retData, {
					status: 'error',
					statusCode: 400,
					msgCode: 50040,
					msg: UtilsHelper.getMessage(50040),
				});
				return UtilsHelper.cRes(res, _retData);
			}

			// Create Stripe product
			const product = await stripe.products.create({
				name: reqBody.name,
				description: reqBody.description,
			});

			// Prepare pricing details
			const unit_amount = Math.round(parseFloat(reqBody.price) * 100); // convert to smallest unit
			const billing_interval = reqBody.plan_type.toLowerCase() === 'monthly' ? 'month' : 'year';

			// Create Stripe price
			const stripePrice = await stripe.prices.create({
				unit_amount,
				currency: reqBody.currency.toLowerCase(),
				recurring: { interval: billing_interval },
				product: product.id,
			});

			// Set additional fields on the request body for creating the plan
			reqBody.status = 0;
			reqBody.stripe_product_id = product.id;
			reqBody.user_id = user_id;

			const mappedInput = mapSubscriptionPlanInput(reqBody);

			// Create the SubscriptionPlan record
			const _createdData = await Models.SubscriptionPlan.create(mappedInput);
			if (_createdData) {
				// Create the SubscriptionPlanPrices record
				const priceData = {
					subscription_plan_id: _createdData.id,
					stripe_price_id: stripePrice.id,
					billing_interval,
					unit_amount,
					currency: reqBody.currency.toLowerCase(),
				};
				const _createdPriceData = await Models.SubscriptionPlanPrices.create(priceData);

				const responseData = {
					plan: _createdData,
					planPrice: _createdPriceData,
				};

				_.assign(_retData, {
					data: UtilsHelper.cryptoJsObjectEncrypt(responseData),
					msgCode: 10080,
					msg: UtilsHelper.getMessage(10080),
				});
			}
		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			});
			this.logErrors(err, 'Error in SubscriptionPlanController.addPlan');
		}

		return UtilsHelper.cRes(res, _retData);
	}


	/**
	 * Method to update plan from super admin with Stripe integration
	 */
	public async editPlan(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject();
		const transaction = await Models.sequelize.transaction();
		try {
			// Extract required fields from the request body using validated field names
			const {
				subscriptionPlanId,
				name,
				description,
				price: reqPrice,
				plan_type,
				currency,
				trial_period_days,
				email,
				is_custom,
				pre_made_templates,
				...otherFields
			} = req.body;

			const price = reqPrice ? parseFloat(reqPrice) : 0;

			if (!subscriptionPlanId || !name || !description || !plan_type || !currency) {
				return res.status(400).json({ error: 'Missing required fields.' });
			}

			// Look up the existing plan using the validated subscriptionPlanId
			const plan = await Models.SubscriptionPlan.findByPk(subscriptionPlanId);
			if (!plan) {
				return res.status(404).json({ error: 'Plan not found.' });
			}

			let user_id = plan.user_id; // Keep existing user_id by default

			// Store old plan state for potential rollback
			const oldPlanState = {
				...plan.toJSON(),
				custom_templates_limit: plan.custom_templates_limit,
				audio_consultation_minutes: plan.audio_consultation_minutes,
				design_templates_limit: plan.design_templates_limit,
				price: Number(plan.price),
				stripe_price_id: plan.stripe_price_id
			};

			// Only process user email if it's a custom plan
			if (is_custom) {
				if (!email) {
					return res.status(400).json({ error: 'Email is required for custom plans.' });
				}
				
				const user = await Models.ClinicalUsers.findOne({ where: { email } });
				if (!user) {
					_.assign(_retData, {
						status: 'error',
						statusCode: 404,
						msgCode: 50041, // Code for "User not found"
						msg: UtilsHelper.getMessage(50041),
					});
					return UtilsHelper.cRes(res, _retData);
				}
				user_id = user.id;
			}

			// Keep track of the old price to determine if a new Stripe price is needed
			const oldPrice = Number(plan.price);

			// Update the plan details in the database
			plan.name = name;
			plan.description = description;
			plan.price = price;
			plan.plan_type = plan_type;
			plan.currency = currency;
			plan.trial_period_days = trial_period_days;
			plan.user_id = user_id; // Will be null for non-custom plans
			plan.is_custom = is_custom;

			if (otherFields.plan_details) {
				plan.plan_details = otherFields.plan_details;
			}
			plan.audio_consultation_minutes = otherFields.audio_consultation_mins ?? 0;
			plan.custom_templates_limit = otherFields.custom_templates ?? 0;
			plan.design_templates_limit = otherFields.designs ?? 0;
			plan.allows_team_members = otherFields.team_members ?? false;
			plan.has_tasks = otherFields.tasks ?? false;
			plan.has_user_logs = otherFields.user_log ?? false;
			plan.has_pre_made_templates = otherFields.pre_made_templates;
			plan.has_medical_history = otherFields.medical_history ?? false;
			plan.has_invite_user = otherFields.team_members ?? false;
			plan.subscription_features = otherFields.subscription_features;
			plan.status = otherFields.status;

			// Store Stripe-related changes for rollback
			const stripeChanges = {
				oldProductId: plan.stripe_product_id,
				oldPriceId: plan.stripe_price_id,
				newPriceId: null,
				updatedProduct: null
			};

			// Compute billing interval based on plan_type
			const billing_interval = plan_type.toLowerCase() === 'monthly' ? 'month' : 'year';

			try {
				// Only update Stripe product if stripe_product_id exists
				if (plan.stripe_product_id) {
					// Update the corresponding Stripe product using the stored stripe_product_id
					stripeChanges.updatedProduct = await stripe.products.update(plan.stripe_product_id, {
						name,
						description,
						metadata: { plan_type, trial_period_days: trial_period_days.toString() }
					});

					// If the price has changed, create a new Stripe price object
					if (oldPlanState.oldPrice !== price) {
						// Convert price to the smallest currency unit (e.g., cents)
						const unit_amount = Math.round(price * 100);
						const newStripePrice = await stripe.prices.create({
							unit_amount,
							currency: 'USD',
							recurring: { interval: billing_interval },
							product: stripeChanges.updatedProduct.id,
						});
						stripeChanges.newPriceId = newStripePrice.id;
						plan.stripe_price_id = newStripePrice.id;

						// Update or create the corresponding SubscriptionPlanPrices record
						const priceData = {
							subscription_plan_id: plan.id,
							stripe_price_id: newStripePrice.id,
							billing_interval,
							unit_amount,
							currency: 'USD',
						};

						const planPrice = await Models.SubscriptionPlanPrices.findOne({
							where: { subscription_plan_id: plan.id },
						});
						if (planPrice) {
							await planPrice.update(priceData);
						} else {
							await Models.SubscriptionPlanPrices.create(priceData);
						}
					}
				}

				// Save the updated plan
				await plan.save({ transaction });

				// Get all users subscribed to this plan
				const subscribedUsers = await Models.ClinicalUsers.findAll({
					where: { subscription_plan_id: subscriptionPlanId },
					transaction
				});

				// Update each user's limits based on the difference
				await Promise.all(subscribedUsers.map(async (user) => {
					// Calculate differences
					const customTemplatesDiff = plan.custom_templates_limit - oldPlanState.custom_templates_limit;
					const audioDiff = plan.audio_consultation_minutes - oldPlanState.audio_consultation_minutes;
					const designTemplatesDiff = plan.design_templates_limit - oldPlanState.design_templates_limit;

					// Update user with adjusted limits
					await user.update({
						// Feature flags update directly
						allows_team_members: plan.allows_team_members,
						has_tasks: plan.has_tasks,
						has_user_logs: plan.has_user_logs,
						has_medical_history: plan.has_medical_history,
						has_invite_user: plan.has_invite_user,

						// Adjust remaining limits by adding/subtracting the difference
						remaining_custom_templates: Sequelize.literal(`GREATEST(0, remaining_custom_templates + ${customTemplatesDiff})`),
						remaining_audio_consultation_minutes: Sequelize.literal(`GREATEST(0, remaining_audio_consultation_minutes + ${audioDiff * 60})`), // Convert to seconds
						remaining_design_templates: Sequelize.literal(`GREATEST(0, remaining_design_templates + ${designTemplatesDiff})`),
					}, { transaction });
				}));

				// If price changed, update all active subscriptions
				if (oldPrice !== price && plan.stripe_product_id) {
					// Get all active subscriptions for this plan
					const activeSubscriptions = await Models.ClinicalUserSubscriptions.findAll({
						where: {
							plan_id: subscriptionPlanId,
							status: { [Op.ne]: 9 } // Not cancelled/deleted
						},
						transaction
					});

					// Update each subscription in Stripe
					await Promise.all(activeSubscriptions.map(async (subscription) => {
						try {
							// Retrieve the Stripe subscription
							const stripeSubscription = await stripeClient.subscriptions.retrieve(
								subscription.subscription_id
							);

							// Update the subscription with the new price
							await stripeClient.subscriptions.update(subscription.subscription_id, {
								items: [{
									id: stripeSubscription.items.data[0].id,
									price: plan.stripe_price_id, // New price ID
								}],
								proration_behavior: 'always_invoice'
							});

							// Update local subscription record
							await subscription.update({
								price: price,
								subscription_features: plan
							}, { transaction });
						} catch (error) {
							console.error(`Failed to update subscription ${subscription.subscription_id}:`, error);
						}
					}));
				}

				// If everything succeeded, commit the transaction
				await transaction.commit();

				_.assign(_retData, {
					data: UtilsHelper.cryptoJsObjectEncrypt(plan),
					msgCode: 10081, // Adjust message code as needed for "edit successful"
					msg: UtilsHelper.getMessage(10081),
				});
			} catch (error) {
				// Rollback database changes
				await transaction.rollback();

				// Rollback Stripe changes if any
				if (stripeChanges.updatedProduct) {
					await stripe.products.update(stripeChanges.oldProductId, {
						name: oldPlanState.name,
						description: oldPlanState.description,
						metadata: oldPlanState.metadata
					});
				}
				if (stripeChanges.newPriceId) {
					await stripe.prices.update(stripeChanges.newPriceId, { active: false });
				}

				throw error;
			}

			return UtilsHelper.cRes(res, _retData);
		} catch (error: any) {
			console.error('Error updating plan:', error);
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: error.message,
			});
			return UtilsHelper.cRes(res, _retData);
		}
	}



	/**
	 * Method to get all plan list
	 */
	public async getPlanList(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject()
		try {
			const userId = req?.user?.id
			const _reqData = req.body
			const isCustom = _reqData.custom
			const _params = req.params

			let _offset = 0
			const _recordsLimit = 25

			const _filter: any = {
				is_custom: isCustom,
				status: { [Op.ne]: 9 },
			}


			if (_.has(_reqData, "status") === true) {
				_filter.status = _reqData?.status
			}

			if (!_.isEmpty(_reqData.searchText)) {
				_filter[Op.or] = [
					{ name: { [Op.iLike]: `%${_reqData.searchText}%` } },
					{ description: { [Op.iLike]: `%${_reqData.searchText}%` } },

				];

				const searchTextAsInteger = parseInt(_reqData.searchText);
				if (!isNaN(searchTextAsInteger)) {
					_filter[Op.or].push(
						{ price: searchTextAsInteger },
						{ max_team_members: searchTextAsInteger }
					);
				}
			}
		/* 	if (typeof _reqData.currency === 'string') {
				_filter[Op.or].push({ currency: { [Op.iLike]: `%${_reqData.searchText}%` } });
			} */

			if (_params.page && _.isInteger(_.parseInt(_params.page)) && _.parseInt(_params.page) > 0) {
				_offset = _recordsLimit * (_.parseInt(_params.page) - 1)
			}
			const { count, rows }: any = await Models.SubscriptionPlan.findAndCountAll({
				where: _filter,
				order: [['createdAt', 'DESC'], ['status', 'ASC']],
				limit: _recordsLimit,
				offset: _offset,

			});


			_.assign(_retData, {
				data: UtilsHelper.cryptoJsObjectEncrypt({
					totalRecords: count,
					recordsPerPage: _recordsLimit,
					list: rows,
				}),
				msgCode: 10082,
				msg: UtilsHelper.getMessage(10082),
			})

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in SubscriptionPlanController.getPlanList')
		}

		return UtilsHelper.cRes(res, _retData)
	}

	/**
	* Method to Get plan daetil from subscriptionPlanId in super admin
	*/
	public async detailPlan(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject();
		try {
			const reqBody = req.body;
			const _params = req.params;
			const subscriptionPlanId = _params.subscriptionPlanId;

			const _filter: any = {
				status: {
					[Op.notIn]: [9],
				},
				id: subscriptionPlanId
			}

			const planData = await Models.SubscriptionPlan.findOne({
				where: _filter,
				include: [
					{
						model: Models.ClinicalUsers,
						as: 'user',
						required: false
					}
				]
			});

			if (planData) {
				_.assign(_retData, {
					msgCode: 10083,
					data: UtilsHelper.cryptoJsObjectEncrypt(planData),
					msg: UtilsHelper.getMessage(10083),
				});

			} else {
				const _msgCode = 50012;
				_.assign(_retData, {
					status: "error",
					statusCode: 400,
					msgCode: _msgCode,
					msg: UtilsHelper.getMessage(_msgCode)
				})
			}

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in SubscriptionPlanController.detailPlan')
		}

		return UtilsHelper.cRes(res, _retData)
	}


	/**
	* Method to delete plan from subscriptionPlanId in super admin
	*/
	public async deletePlan(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject();
		try {
			const reqBody = req.body;
			const _params = req.params;
			const subscriptionPlanId = _params.subscriptionPlanId;

			const _filter: any = {
				status: {
					[Op.notIn]: [9],
				},
				id: subscriptionPlanId
			}

			const planData = await Models.SubscriptionPlan.findOne({
				where: _filter,
			});

			if (planData) {

				await Models.SubscriptionPlan.update({ status: 9 }, {
					where: {
						id: planData.id
					}
				});

				_.assign(_retData, {
					msgCode: 10084,
					msg: UtilsHelper.getMessage(10084),
				});

			} else {
				const _msgCode = 50012;
				_.assign(_retData, {
					status: "error",
					statusCode: 400,
					msgCode: _msgCode,
					msg: UtilsHelper.getMessage(_msgCode)
				})
			}

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in SubscriptionPlanController.deletePlan')
		}

		return UtilsHelper.cRes(res, _retData)
	}

	/**
	* Method to change plan status from subscriptionPlanId in super admin
	*/
	public async changePlanStatus(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject();
		try {
			const reqBody = req.body;
			const _params = req.params;
			const reqStatus = reqBody.status;
			const subscriptionPlanId = _params.subscriptionPlanId;

			const _filter: any = {
				status: {
					[Op.notIn]: [9],
				},
				id: subscriptionPlanId
			}

			const planData = await Models.SubscriptionPlan.findOne({
				where: _filter,
			});

			if (planData) {

				// Create a product in Stripe
				const product = await stripe.products.create({
					name: `${planData.name}-${planData.currency}-${planData.plan_type.toLowerCase()}`,
					description: planData.description,
				});

				// Create a price associated with the product
				const stripePrice = await stripe.prices.create({
					product_data: {
						name: planData.name,
					},
					unit_amount: planData.price * 100, // Amount in cents
					currency: planData.currency,
					recurring: {
						interval: (planData.plan_type.toLowerCase() == 'monthly') ? 'month' : 'year', // 'monthly' or 'yearly'
					},
					// Add other necessary parameters based on your plan model
				});


				const stripePriceId = stripePrice.id;

				await Models.SubscriptionPlan.update({ status: reqStatus, stripe_price_id: stripePriceId }, {
					where: {
						id: planData.id
					}
				});

				_.assign(_retData, {
					msgCode: 10085,
					msg: UtilsHelper.getMessage(10085),
				});

			} else {
				const _msgCode = 50012;
				_.assign(_retData, {
					status: "error",
					statusCode: 400,
					msgCode: _msgCode,
					msg: UtilsHelper.getMessage(_msgCode)
				})
			}

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			})

			this.logErrors(err, 'Error in SubscriptionPlanController.changePlanStatus')
		}

		return UtilsHelper.cRes(res, _retData)
	}

	/**
		* Method to add Or Update Subscription Plan Clinic in super admin
		*/
	/**
	* Method to add or update SubscriptionPlanClinicalUser entry
	*/
	public async addOrUpdateSubscriptionPlanClinicalUser(req: express.Request, res: express.Response): Promise<void | any> {
		const _retData: IResponseObject = UtilsHelper.responseObject();
		try {
			const reqBody = req.body;

			// Check if the subscription plan exists
			const subscriptionPlan = await Models.SubscriptionPlan.findOne({
				where: {
					id: reqBody.subscription_plan_id,
					status: {
						[Op.not]: 9,
					},
				},
			});

			if (!subscriptionPlan) {
				_.assign(_retData, {
					statusCode: 400,
					status: 'error',
					msgCode: 50048, // Adjust the error code as needed
					msg: UtilsHelper.getMessage(50048),
				});

				return UtilsHelper.cRes(res, _retData);
			}

			// Check if the clinic exists
			const clinic = await Models.Clinics.findOne({
				where: {
					clinic_id: reqBody.clinic_id,
					status: {
						[Op.not]: 9,
					},
				},
			});

			if (!clinic) {
				_.assign(_retData, {
					statusCode: 400,
					status: 'error',
					msgCode: 50047, // Adjust the error code as needed
					msg: UtilsHelper.getMessage(50047),
				});

				return UtilsHelper.cRes(res, _retData);
			}

			// Check if the entry already exists
			const existingEntry = await Models.SubscriptionPlanClinicalUser.findOne({
				where: {
					subscription_plan_id: reqBody.subscription_plan_id,
					clinic_id: reqBody.clinic_id,
					status: {
						[Op.not]: 9,
					},
				},
			});

			console.log('existingEntry', existingEntry);

			if (existingEntry) {
				// Update the existing entry
				await Models.SubscriptionPlanClinicalUser.update(reqBody, {
					where: {
						subscription_plan_id: reqBody.subscription_plan_id,
						clinic_id: reqBody.clinic_id,
					},
				});
			} else {
				// Create a new entry
				await Models.SubscriptionPlanClinicalUser.create(reqBody);
			}

			_.assign(_retData, {
				msgCode: 10097,
				msg: UtilsHelper.getMessage(10097),
			});

		} catch (err: any) {
			_.assign(_retData, {
				statusCode: 500,
				status: 'error',
				msg: err.message,
			});

			this.logErrors(err, 'Error in SubscriptionPlanController.addOrUpdateSubscriptionPlanClinicalUser');
		}

		return UtilsHelper.cRes(res, _retData);
	}


}


const subscriptionPlanController = new SubscriptionPlanController()

export default subscriptionPlanController
