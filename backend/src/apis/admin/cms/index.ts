const Router = require("express").Router();
const Validator = require("./validator");
import cmsController from './CmsController'
//const Authorize = require("../../../middlewares/adminAuthenticate")

const _loadRoutes = (options) => {
	// define routes here

	// system-roles
	Router.get("/list", options.AdminAuthenticate,  cmsController.getCmsList);
	// system-roles
	Router.patch("/edit/:cmsId", options.AdminAuthenticate, Validator("cmsEditValidate"), cmsController.editCmsFromCmsId);

	Router.get("/detail/:cmsId", options.AdminAuthenticate, cmsController.cmsDeatilFromCmsId);

	Router.post("/upload-image", cmsController.uploadImage);

	return Router;
}

module.exports = _loadRoutes
