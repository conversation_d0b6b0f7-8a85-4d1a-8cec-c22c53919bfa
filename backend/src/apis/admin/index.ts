const Users = require("./users");
const Genral = require("./genral");
const Cms = require("./cms");
const Dashboard = require("./dashboard");
const Help = require("./help");
const Plan = require("./subscriptionPlan");
const Mailchimp = require("./mailchimp");
const Notes = require("./notes");

exports.mount = (app, options) => {
	const _endpointPrefix = '/admin-apis';

	app.use(`${_endpointPrefix}/users`, Users(options));
	app.use(`${_endpointPrefix}/genral`, Genral(options));
	app.use(`${_endpointPrefix}/cms`, Cms(options));
	app.use(`${_endpointPrefix}/dashboard`, Dashboard(options));
	app.use(`${_endpointPrefix}/help`, Help(options));
	app.use(`${_endpointPrefix}/subscription-plan`, Plan(options));
	app.use(`${_endpointPrefix}/mailchimp`, Mailchimp(options));
	app.use(`${_endpointPrefix}/notes`, Notes(options));
}


