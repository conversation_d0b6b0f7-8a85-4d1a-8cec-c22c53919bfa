// controllers/NotesController.ts
import express from 'express';
import * as _ from 'lodash';
import Models from '../../../database/models'
import * as UtilsHelper from '../../../helpers/utils.helper'
import { IResponseObject } from '../../../helpers/utils.interface'
import BaseController from '../../../helpers/BaseController'
import { Op } from 'sequelize';

class NotesController extends BaseController{
  constructor() {
    super();
    this.getNotesForUser = this.getNotesForUser.bind(this);
    this.getSingleNoteForUser = this.getSingleNoteForUser.bind(this);
    this.createNoteForUser = this.createNoteForUser.bind(this);
    this.updateNoteForUser = this.updateNoteForUser.bind(this);
    this.deleteNoteForUser = this.deleteNoteForUser.bind(this);
  }

  // 1) GET /api/v1/users/:userId/notes
  public async getNotesForUser(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject();
    try {
      const userId = parseInt(req.params.userId, 10);

      // 1. Parse pagination parameters from query
      const page = parseInt(req.query.page as string, 10) || 1;  // current page, default 1
      const limit = parseInt(req.query.limit as string, 10) || 10; // items per page, default 10
      const offset = (page - 1) * limit;

      // 2. Build the where clause for the query
      const searchStr = req.query.searchStr as string;
      const _filter: any = { user_id: userId };

      if (searchStr) {
        _filter[Op.or] = [
          { title: { [Op.iLike]: `%${searchStr}%` } },
          { note_text: { [Op.iLike]: `%${searchStr}%` } }
        ];
      }

      // 3. Retrieve notes for this user with pagination and search filtering
      const { rows, count } = await Models.ClinicalUserNotes.findAndCountAll({
        where: _filter,
        order: [['created_at', 'DESC']],
        limit,
        offset
      });

      // 4. Build pagination info
      const totalPages = Math.ceil(count / limit);

      // 5. Assign data to _retData with encryption
      _.assign(_retData, {
        data: UtilsHelper.cryptoJsObjectEncrypt({
          totalRecords: count,
          recordsPerPage: limit,
          list: rows,
          page,
          totalPages
        }),
        msgCode: 10150,
        msg: 'Notes fetched successfully'
      });
    } catch (error: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: error.message
      });
    }
    return UtilsHelper.cRes(res, _retData);
  }

  public async getSingleNoteForUser(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject();
    try {
      const userId = parseInt(req.params.userId, 10);
      const noteId = parseInt(req.params.noteId, 10);

      // 1. Retrieve the single note
      const note = await Models.ClinicalUserNotes.findOne({
        where: {
          id: noteId,
          user_id: userId, // Ensure the note belongs to this user
        },
      });

      if (!note) {
        throw new Error('Note not found');
      }

      // 2. Assign data to _retData
      _.assign(_retData, {
        data: UtilsHelper.cryptoJsObjectEncrypt(note),
        msgCode: 10154,
        msg: 'Note retrieved successfully',
      });
    } catch (error: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: error.message,
      });
    }
    return UtilsHelper.cRes(res, _retData);
  }


  // 2) POST /api/v1/users/:userId/notes
  public async createNoteForUser(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject();
    try {
      const userId = parseInt(req.params.userId, 10);
      const { title, note_text } = req.body;

      // Create note
      const newNote = await Models.ClinicalUserNotes.create({
        user_id: userId,
        title,
        note_text
      });

      _.assign(_retData, {
        msgCode: 10151,
        msg: 'Note created successfully'
      });
    } catch (error: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: error.message
      });
    }
    return UtilsHelper.cRes(res, _retData);
  }

  // 3) PUT /api/v1/users/:userId/notes/:noteId
  public async updateNoteForUser(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject();
    try {
      const userId = parseInt(req.params.userId, 10);
      const noteId = parseInt(req.params.noteId, 10);
      const { title, note_text } = req.body;

      // Find the note
      const note = await Models.ClinicalUserNotes.findOne({
        where: {
          id: noteId,
          user_id: userId
        }
      });
      if (!note) {
        throw new Error('Note not found');
      }

      // Update the note
      await note.update({ title, note_text });

      _.assign(_retData, {
        data: note,
        msgCode: 10152,
        msg: 'Note updated successfully'
      });
    } catch (error: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: error.message
      });
    }
    return UtilsHelper.cRes(res, _retData);
  }

  // 4) DELETE /api/v1/users/:userId/notes/:noteId
  public async deleteNoteForUser(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject();
    try {
      const userId = parseInt(req.params.userId, 10);
      const noteId = parseInt(req.params.noteId, 10);

      // Find the note
      const note = await Models.ClinicalUserNotes.findOne({
        where: {
          id: noteId,
          user_id: userId
        }
      });
      if (!note) {
        throw new Error('Note not found');
      }

      await note.destroy();

      _.assign(_retData, {
        msgCode: 10153,
        msg: 'Note deleted successfully'
      });
    } catch (error: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: error.message
      });
    }
    return UtilsHelper.cRes(res, _retData);
  }
}

const notesController = new NotesController()

export default notesController