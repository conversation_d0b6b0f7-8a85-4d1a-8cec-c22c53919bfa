import * as express from 'express'
import mailchimp from '@mailchimp/mailchimp_marketing';
import _ from 'lodash';
import * as UtilsHelper from '../../../helpers/utils.helper'
import BaseController from '../../../helpers/BaseController'
import {IResponseObject} from "@/helpers/utils.interface";

mailchimp.setConfig({
  apiKey: process.env.MAILCHIMP_API_KEY || '',
  server: process.env.MAILCHIMP_SERVER_PREFIX || '', // e.g., 'us1'
});

/**
 * Fetch stats for all campaigns:
 * recipients, open rate, click rate, bounce rate
 */
async function getAllCampaignsStats() {
  let allReports: any[] = [];
  let offset = 0;
  const count = 100; // # of items per request
  let response;

  // 2. Paginate through /reports until we have them all
  do {
    response = await mailchimp.request({
      method: 'GET',
      path: '/reports',
      query: {
        offset,
        count,
      },
    });
    const reportsBatch = response.reports || [];
    allReports = allReports.concat(reportsBatch);
    offset += count;
  } while (response.reports && response.reports.length === count);

  // 3. Map each report to the stats you need
  return allReports.map((report: any) => {
    const recipients = report.emails_sent || 0;
    const openRate = report.opens?.open_rate || 0;     // e.g., 0.12 => 12%
    const clickRate = report.clicks?.click_rate || 0;  // e.g., 0.05 => 5%

    const hardBounces = report.bounces?.hard_bounces || 0;
    const softBounces = report.bounces?.soft_bounces || 0;
    const syntaxErrors = report.bounces?.syntax_errors || 0;
    const totalBounces = hardBounces + softBounces + syntaxErrors;

    // compute bounce rate as a percentage
    const bounceRate = recipients
      ? ((totalBounces / recipients) * 100).toFixed(2)
      : '0.00';

    return {
      campaign_id: report.id,
      recipients,
      openRate,
      clickRate,
      bounceRate,
    };
  });
}

class MailchimpController extends BaseController {
  constructor() {
    super();
    this.getCampaignAnalytics = this.getCampaignAnalytics.bind(this);
  }
  /**
   * Fetches a list of campaigns from Mailchimp along with key metrics:
   * - Email Campaign (subject line)
   * - Number of Recipients (emails_sent or recipients.recipient_count)
   * - Status (sent, draft, etc.)
   * - Open Rate (report_summary.open_rate)
   * - Click Rate (report_summary.click_rate)
   *
   * Supports pagination via query parameters: page & pageSize.
   */
  public async getCampaignAnalytics(req: express.Request, res: express.Response): Promise<void | any> {
    const _retData: any = UtilsHelper.responseObject();
    try {
      // Parse query parameters for pagination.
      const page = parseInt(req.query.page as string, 10) || 1;
      const pageSize = parseInt(req.query.pageSize as string, 10) || 10;
      const offset = (page - 1) * pageSize;

      // List campaigns with pagination.
      const response = await mailchimp.campaigns.list({
        offset,
        count: pageSize,
        status: 'sent,draft,save,sending,paused,schedule',
      });

      // Mailchimp returns the total number of campaigns in response.total_items.
      const campaigns = response.campaigns || [];
      const totalItems = response.total_items || 0;
      const totalPages = Math.ceil(totalItems / pageSize);

      // Build the result array.
      const result = campaigns.map((campaign: any) => {
        const campaignName = campaign.settings?.subject_line || 'Untitled';
        const status = campaign.status;
        let recipientsCount = 0;

        if (status === 'sent') {
          recipientsCount = campaign.emails_sent || 0;
        } else {
          recipientsCount = campaign.recipients?.recipient_count || 0;
        }

        let openRate = 'N/A';
        let clickRate = 'N/A';
        if (status === 'sent' && campaign.report_summary) {
          openRate = (campaign.report_summary.open_rate * 100).toFixed(0) + '%';
          clickRate = (campaign.report_summary.click_rate * 100).toFixed(0) + '%';
        }

        return {
          emailCampaign: campaignName,
          recipients: recipientsCount,
          status,
          openRate,
          clickRate,
        };
      });

      _.assign(_retData, {
        data: UtilsHelper.cryptoJsObjectEncrypt({
          totalRecords: totalItems,
          recordsPerPage: pageSize,
          list: result,
          totalPages,
        }),
        msgCode: 10110,
        msg: UtilsHelper.getMessage(10110),
      });
    } catch (error: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: error.message,
      });
      this.logErrors(error, 'Error in MailchimpController.getCampaignAnalytics');
    }
    return this.sendResponse(res, _retData);
  }

  /**
   * GET /api/v1/mailchimp/overview?campaign_id=<CAMPAIGN_ID>
   *
   * Returns overview stats: recipients, open rate, click rate, bounce rate, etc.
   */
  public async getMailchimpOverviewStats(
    req: express.Request,
    res: express.Response
  ): Promise<void | any> {
    const _retData: IResponseObject = UtilsHelper.responseObject();
    try {
      // 1. Fetch all campaign reports
      const response = await mailchimp.reports.getAllCampaignReports();
      const reports = response.reports || [];

      // 2. Aggregate relevant stats
      let totalRecipients = 0;
      let totalUnsubscribes = 0;
      let totalUniqueOpens = 0;
      let totalUniqueClicks = 0;
      let totalBounces = 0;

      reports.forEach((report: any) => {
        // Recipients
        const recipients = report.emails_sent || 0;
        totalRecipients += recipients;

        // Unsubscribed
        // (If you want a second recipients stat;
        //  you can rename or remove this as needed.)
        totalUnsubscribes += report.unsubscribed || 0;

        // Unique Opens
        const uniqueOpens = report.opens?.unique_opens || 0;
        totalUniqueOpens += uniqueOpens;

        // Unique Clicks
        const uniqueClicks = report.clicks?.unique_clicks || 0;
        totalUniqueClicks += uniqueClicks;

        // Bounces (hard, soft, syntax)
        const hardBounces = report.bounces?.hard_bounces || 0;
        const softBounces = report.bounces?.soft_bounces || 0;
        const syntaxErrors = report.bounces?.syntax_errors || 0;
        totalBounces += (hardBounces + softBounces + syntaxErrors);
      });

      // 3. Compute aggregated rates
      //    For example, openRate = totalUniqueOpens / totalRecipients
      const openRate = totalRecipients
        ? (totalUniqueOpens / totalRecipients)
        : 0;
      const clickRate = totalRecipients
        ? (totalUniqueClicks / totalRecipients)
        : 0;
      const bounceRate = totalRecipients
        ? ((totalBounces / totalRecipients) * 100).toFixed(2)
        : '0.00';

      // 4. Build a single "overview" object
      const overview = {
        recipients: totalRecipients,        // e.g., 5123
        unsubscribed: totalUnsubscribes,     // e.g., 3126, or rename as you wish
        openRate,                           // e.g., 0.24 => 24%
        clickRate,                          // e.g., 0.05 => 5%
        bounceRate,                         // e.g., '1.23'
      };

      // 5. Assign to _retData
      _.assign(_retData, {
        data: overview,
        msgCode: 10117,
        msg: 'Mailchimp overview stats for all campaigns retrieved successfully',
      });
    } catch (err: any) {
      _.assign(_retData, {
        statusCode: 500,
        status: 'error',
        msg: err.message,
      });
    }
    return UtilsHelper.cRes(res, _retData);
  }
}

const mailchimpController = new MailchimpController();
export default mailchimpController;
