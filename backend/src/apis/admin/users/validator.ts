import Joi from 'joi'
import express from 'express'
import _ from 'lodash'
import * as UtilsHelper from '../../../helpers/utils.helper'


const Validators = {
	adminLoginValidate: Joi.object({
		email: Joi.string().trim().email().required(),
		password: Joi.string().trim().min(8).required(),
	}),

	getTotalTasksStatsValid: Joi.object({
		filter: Joi.string()
			.valid('TODAY', 'THIS_MONTH', 'THIS_YEAR', 'ALL')
			.optional()
			.label('Date Filter'),
	}),

	forgotPasswordValidate: Joi.object({
		email: Joi.string().trim().email().required(),
	}),

	resetPasswordValidate: Joi.object({
		token: Joi.string().trim().required(),
		password: Joi.string().trim().min(8).required(),
	}),

	changePasswordValidate: Joi.object({
		old_password: Joi.string().trim().required(),
		password: Joi.string().trim().min(8).invalid(Joi.ref('old_password')).required().messages({
			'any.invalid': "Current password and new password cannot be same."
		}),
	}),

	updateProfileValidate: Joi.object({
		first_name: Joi.string().trim().required(),
		last_name: Joi.string().trim().required(),
		email: Joi.string().trim().email().required(),
		password: Joi.string(),
	}),

	addSupportValidate: Joi.object({
		first_name: Joi.string().trim().required(),
		last_name: Joi.string().trim().required(),
		email: Joi.string().trim().email().required(),
		mobile: Joi.string().trim().required(),
		country_id: Joi.number().integer().positive().required(),
	}),

	verifyOTPValidate: Joi.object({
		signature: Joi.string().trim().required(),
		email: Joi.string().trim().email().required(),
		otp: Joi.number().integer().positive().max(999999).required(),
		keepMeLoggedIn: Joi.boolean(),
	}),

	resendOTPValidate: Joi.object({
		signature: Joi.string().trim().required(),
		email: Joi.string().trim().email().required(),
	}),

	userListValid: Joi.object({
		clinic_id: Joi.string(),
		searchStr: Joi.string().min(3),
		page: Joi.number().integer().min(1).required(),
	}),
	ownerDeatilValid: Joi.object({
		ownerId: Joi.number().integer().min(1).required(),
	}),
	ownerInvoiceValid: Joi.object({
		ownerId: Joi.number().integer().min(1).required(),
		page: Joi.number().optional(),
	}),
	editOwnerValid: Joi.object({
		ownerId: Joi.number().integer().min(1).required(),
		first_name: Joi.string().required(),
		last_name: Joi.string().required(),
		clinic_id: Joi.string().required(),
		clinic_name: Joi.string().required(),
		address: Joi.string().required(),
		clinical_specializations_id: Joi.number().integer().positive().required(),
		town_id: Joi.string(),
		pincode: Joi.string().required(),
		phone: Joi.string().required(),
		county_id: Joi.string(),
		country: Joi.string(),
		reg_gmc_no: Joi.string().allow(null).allow(''),
		others: Joi.string(),

	}),

	addAdminValid: Joi.object({
		first_name: Joi.string().required(),
		last_name: Joi.string().required(),
		email: Joi.string().trim().email().required(),
		phone: Joi.string().trim().optional(),
		address: Joi.string().optional(),
		country_id: Joi.number().integer().positive().optional(),
		county_id: Joi.number().integer().positive().optional(),
		town_id: Joi.number().integer().positive().optional(),
		postcode: Joi.string().trim().optional(),
		role_id: Joi.number().integer().positive().required(),
		permissions: Joi.array()
			.min(1)
			.messages({
				'array.min': 'Please select at least one permission',
			})
			.required(),
	}),

	adminUserListValid: Joi.object({
		page: Joi.number().integer().min(1).required(),
	}),

	billingStatsValid: Joi.object({
		filter: Joi.string()
		.valid('TODAY', 'THIS_MONTH', 'THIS_YEAR', 'ALL')
		.optional()
		.label('Date Filter'),
	}),

	adminDeatilValid: Joi.object({
		adminId: Joi.number().integer().min(1).required(),
	}),

	updateAdminValid: Joi.object({
		adminId: Joi.number().integer().min(1).required(),
		first_name: Joi.string().required(),
		last_name: Joi.string().required(),
		email: Joi.string().trim().email().required(),
		phone: Joi.string().trim().optional(),
		address: Joi.string().optional(),
		country_id: Joi.number().integer().positive().optional(),
		county_id: Joi.number().integer().positive().optional(),
		town_id: Joi.number().integer().positive().optional(),
		postcode: Joi.string().trim().optional(),
		role_id: Joi.number().integer().positive().required(),
		permissions: Joi.array()
			.min(1)
			.messages({
				'array.min': 'Please select at least one permission',
			})
			.required(),
	}),

	updateAdminStatusValid: Joi.object({
		adminId: Joi.number().integer().min(1).required(),
		status: Joi.number().integer().valid(0,1).required()
	}),

	updateOwnerStatusValid: Joi.object({
		ownerId: Joi.number().integer().min(1).required(),
		clinic_id: Joi.string().required(),
		status: Joi.number().integer().valid(0, 1).required(),
		reason: Joi.string().required(),
	}),

	deleteAdminValid: Joi.object({
		adminId: Joi.number().integer().min(1).required()
	}),

	userSubscriptionListValid: Joi.object({
		//searchStr: Joi.string().min(3),
		page: Joi.number().integer().min(1).required(),
	}),
	userSubscriptionDetailValid: Joi.object({
		subscriptionId: Joi.number().integer().min(1).required()
	}),

	cancellSubscriptionValid: Joi.object({
		subscription_id: Joi.string().required(),
		cancellation_reason: Joi.string().required(),
	}),

}

function Validator(func) {
	return async function ValidatorFunc(req, res, next) {
		try {
			const _reqBody = (req.body?.encrypet) ? UtilsHelper.cryptoJsObjectDcrypt(req.body.encrypet) : req.body;
			const _reqData = _.assign(_reqBody, req.params, req.query);
			const validated = await Validators[func].validateAsync(_reqData, { abortEarly: false })
			req.body = validated
			next()
		}
		catch (err) {
			const _er = {};
			if (err.isJoi) {
				err.details.forEach((d) => {
					const _key = d.context.key;
					_er[_key] = d.message
				})
			}

			const _retData = UtilsHelper.responseObject()
			_retData.status = 'error'
			_retData.statusCode = 400
			_retData.msg = UtilsHelper.getMessage('50020', 'en')
			_retData.msgCode = '50020'
			_retData.data = _er

			return UtilsHelper.cRes(res, _retData);
		}
	}
}

module.exports = Validator;
