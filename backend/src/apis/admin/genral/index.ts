const Router = require("express").Router();
const Validator = require("./validator");
import genralController from './GenralController'
//const Authorize = require("../../../middlewares/adminAuthenticate")

const _loadRoutes = (options) => {
	// define routes here

	// system-roles
	Router.get("/system-roles", options.AdminAuthenticate, genralController.getSystemRoles);
	// system-roles
	Router.get("/system-permissions", options.AdminAuthenticate, genralController.permissionList);
	Router.patch("/update-system-role-right/:permissionId", options.AdminAuthenticate, Validator('updateRoleRightValid'), genralController.updateDefaultRolesRights);
	Router.get("/system-role-right/:permissionId", options.AdminAuthenticate, genralController.getDefaultRolesRights);

	// clinic-list
	Router.get("/clinic-list", options.AdminAuthenticate, genralController.clinicList);


	return Router;
}

module.exports = _loadRoutes
