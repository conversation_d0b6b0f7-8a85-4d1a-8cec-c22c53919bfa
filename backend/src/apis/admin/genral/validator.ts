import Joi from 'joi'
import express from 'express'
import _ from 'lodash'
import * as UtilsHelper from '../../../helpers/utils.helper'


const Validators = {

	adminLoginValidate: Joi.object({
		email: Joi.string().trim().email().required(),
		password: Joi.string().trim().min(8).required(),
	}),

	updateRoleRightValid: Joi.object({
		roleId: Joi.number().integer().min(1).required(),
		permissionId: Joi.number().integer().min(1).required(),
		role_descriptions: Joi.string().required(),
		permissions: Joi.array()
			.min(1)
			.messages({
				'array.min': 'Please select at least one permission',
			})
			.required(),
	}),

	getMostUsedDocumentsValid: Joi.object({
		// Example: optional date range
		// startDate: Joi.string().isoDate().optional().label('Start Date'),
		// endDate: Joi.string().isoDate().optional().label('End Date'),
	})


}

function Validator(func) {
	return async function ValidatorFunc(req, res, next) {
		try {
			const _reqBody = (req.body?.encrypet) ? UtilsHelper.cryptoJsObjectDcrypt(req.body.encrypet) : req.body;
			const _reqData = _.assign(_reqBody, req.params, req.query);
			const validated = await Validators[func].validateAsync(_reqData, { abortEarly: false })
			req.body = validated
			next()
		}
		catch (err) {
			const _er = {};
			if (err.isJoi) {
				err.details.forEach((d) => {
					const _key = d.context.key;
					_er[_key] = d.message
				})
			}

			const _retData = UtilsHelper.responseObject()
			_retData.status = 'error'
			_retData.statusCode = 400
			_retData.msg = UtilsHelper.getMessage('50020', 'en')
			_retData.msgCode = '50020'
			_retData.data = _er

			return UtilsHelper.cRes(res, _retData);
		}
	}
}

module.exports = Validator;
