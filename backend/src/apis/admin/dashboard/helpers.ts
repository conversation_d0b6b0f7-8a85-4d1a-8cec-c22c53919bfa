import _ from 'lodash'
const moment = require('moment');
const { Op, Sequelize } = require('sequelize')
import Models from '../../../database/models'
import * as UtilsHelper from '../../../helpers/utils.helper'


const Services = {
	/**
 * Send OTP email for 2FA to login in the backend panel.
 * @param {object} userData
 * @returns OTP
 */

	getTotalAvgRating: async (specialisation_id: number, clinic_id: string) => {

		const _filter: any = {
			status: 1,
			approval_rating: { [Op.ne]: null },
		}


		if (specialisation_id != 0) {
			_filter['$users.clinical_specializations_id$'] = specialisation_id;
		}

		if (clinic_id != "ALL") {
			_filter.clinic_id = clinic_id;
		}

		const data: any = await Models.PatientClinicalNotesLetters.findAll({
			where: _filter,
			attributes: [
				'approval_rating'
			],
			include: {
				model: Models.ClinicalUsers,
				as: 'users',
				attributes: []
			}
		});


		let rating = 0;

		if (data.length > 0) {
			data.forEach((obj, index) => {
				rating = rating + obj.approval_rating
			})

			return Math.round(rating / data.length)
		} else {
			return 0
		}

	},


	getTotalUsers: async (specialisation_id: number, clinic_id: string) => {
		const _filter: any = {
			status: 1
		}

		if (specialisation_id != 0) {
			_filter.clinical_specializations_id = specialisation_id;
		}

		if (clinic_id != 'ALL') {
			_filter['$user_role_detail.clinic_id$'] = clinic_id;
		}

		const { count, rows }: any = await Models.ClinicalUsers.findAndCountAll({
			where: _filter,
			attributes: ['id'],
			include: {
				model: Models.ClinicalUserRoleDetail,
				as: 'user_role_detail',
				attributes: []
			},
		});


		return count;

	},


	clinicanStaffCount: async () => {

		const clinicCount = await Models.Clinics.count({
			where: {
				status: 1,
				clinic_id: {
					[Op.in]: Models.Sequelize.literal(
						`(SELECT clinic_id FROM clinical_user_role_details WHERE "Clinics"."clinic_id" = "clinical_user_role_details"."clinic_id")`
					),
				},
			}
		});

		const rolesData = await Models.ClinicalUserRoleDetail.findAll({
			where: {
				status: 1,
			}
		})
		const users: any = [];
		if (rolesData.length > 0) {
			for (const role of rolesData) {
				users.push(role.user_id)
			}
		}
		const _filter: any = {
			status: { [Op.ne]: 9 },
			id: { [Op.in]: users },
			owner_user_id: { [Op.ne]: null },

		}
		const countObject = {
			clinicianCount: 0,
			staffCount: 0,
			clinicCount: clinicCount,
			SubscriptionsCount:0
		}
		const rows: any = await Models.ClinicalUsers.findAll({
			where: _filter,
		});
		let staffId: any = 0;
		let clinicanId: any = 0;
		const roleData = await Models.ClinicalRoles.findAll({});

		if (roleData.length > 0) {
			for (const role of roleData) {
				if (role.name == 'Clinician') {
					clinicanId = role.id
				}
				if (role.name == 'Staff Member') {
					staffId = role.id
				}
			}
		}

		// ClinicalUserSubscriptions count
		const SubscriptionsCount = await Models.ClinicalUserSubscriptions.count({
			where: {
				price: {
					[Op.gt]: 0
				},
				status: {
					[Op.ne]: 9
				}
			}
		});
	// Return the count of user subscriptions
		countObject.SubscriptionsCount = SubscriptionsCount;



		if (rows.length > 0) {
			countObject.clinicianCount = rows.filter((x, i) => { return x.role_id == clinicanId; }).length;
			countObject.staffCount = rows.filter((x, i) => { return x.role_id == staffId; }).length;
		}

		return countObject;

	},

	printDownloadEmailedCount: async (letter_type) => {
		const _filter: any = {
			status: { [Op.ne]: 9 },
		}
		if (letter_type != null) {
			_filter.letter_type = letter_type;
		}
		const countObject = {
			printedCount: 0,
			downloadedCount: 0,
			emailedCount: 0,
			creatededCount: 0,
			ratedCount: 0,
		}
		const rows: any = await Models.PatientClinicalNotesLetters.findAll({
			where: _filter,
		});

		if (rows.length > 0) {
			countObject.creatededCount = rows.length;
			countObject.ratedCount = rows.filter((x, i) => { return x.approval_rating; }).length;
			countObject.printedCount = rows.filter((x, i) => { return x.is_letter_printed; }).length;
			countObject.downloadedCount = rows.filter((x, i) => { return x.is_letter_downloaded; }).length;
			countObject.emailedCount = rows.filter((x, i) => { return x.is_letter_emailed; }).length;

		}

		return countObject;
	},

	dailyLettersCountForOwner: async (type = null) => {
		let query = `SELECT DATE_TRUNC('day', created_at) AS day, COUNT(*) AS count
     FROM patient_clinical_notes_letters WHERE status = 1  `

		if (type != null) {
			query += ` AND  letter_type= :letter_type `
		}

		query += ` GROUP BY day ORDER BY day`

		const result = await Models.sequelize.query(query,
			{
				type: Models.sequelize.QueryTypes.SELECT,
				replacements: { letter_type: type },
			}
		);

		if (result.length > 0) {
			const total = result.reduce((accumulator, object) => {
				return parseInt(accumulator) + parseInt(object.count);
			}, 0);
			return Math.round(total / result.length)
		} else {
			return 0
		}
	},

	weeklyLettersCountForOwner: async (type = null) => {
		let query = `SELECT DATE_TRUNC('week', created_at) AS week, COUNT(*) AS count
    FROM patient_clinical_notes_letters WHERE status = 1  `

		if (type != null) {
			query += ` AND  letter_type= :letter_type `
		}

		query += ` GROUP BY week ORDER BY week`

		const result = await Models.sequelize.query(query,
			{
				type: Models.sequelize.QueryTypes.SELECT,
				replacements: { letter_type: type },
			}
		);

		if (result.length > 0) {
			const total = result.reduce((accumulator, object) => {
				return parseInt(accumulator) + parseInt(object.count);
			}, 0);

			return Math.round(total / result.length)
		} else {
			return 0
		}
	},

	monthlyLettersCountForOwner: async (type = null) => {
		let query = `SELECT DATE_TRUNC('month', created_at) AS month, COUNT(*) AS count
    FROM patient_clinical_notes_letters WHERE status = 1  `

		if (type != null) {
			query += ` AND  letter_type= :letter_type `
		}

		query += ` GROUP BY month ORDER BY month`

		const result = await Models.sequelize.query(query,
			{
				type: Models.sequelize.QueryTypes.SELECT,
				replacements: { letter_type: type },
			}
		);

		if (result.length > 0) {
			const total = result.reduce((accumulator, object) => {
				return parseInt(accumulator) + parseInt(object.count);
			}, 0);

			return Math.round(total / result.length)
		} else {
			return 0
		}
	},





}

module.exports = Services;