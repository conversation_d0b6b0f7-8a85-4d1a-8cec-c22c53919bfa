import Joi from 'joi'
import express from 'express'
import _ from 'lodash'
import * as UtilsHelper from '../../../helpers/utils.helper'


const Validators = {

	noteListValid: Joi.object({
		searchStr: Joi.string().min(3),
		page: Joi.number().integer().min(1).required(),
	}),

	printdownloademailValid: Joi.object({
		letter_type: Joi.string().valid('Clinical Letter', 'Clinical Summary'),
	}),

	totalAvgRatingValid: Joi.object({
		clinical_specializations_id: Joi.number().integer(),
		clinic_id: Joi.string(),
	}),




}

function Validator(func) {
	return async function ValidatorFunc(req, res, next) {
		try {
			const _reqBody = (req.body?.encrypet) ? UtilsHelper.cryptoJsObjectDcrypt(req.body.encrypet) : req.body;
			const _reqData = _.assign(_reqBody, req.params, req.query);
			const validated = await Validators[func].validateAsync(_reqData, { abortEarly: false })
			req.body = validated
			next()
		}
		catch (err) {
			const _er = {};
			if (err.isJoi) {
				err.details.forEach((d) => {
					const _key = d.context.key;
					_er[_key] = d.message
				})
			}

			const _retData = UtilsHelper.responseObject()
			_retData.status = 'error'
			_retData.statusCode = 400
			_retData.msg = UtilsHelper.getMessage('50020', 'en')
			_retData.msgCode = '50020'
			_retData.data = _er

			return UtilsHelper.cRes(res, _retData);
		}
	}
}

module.exports = Validator;
