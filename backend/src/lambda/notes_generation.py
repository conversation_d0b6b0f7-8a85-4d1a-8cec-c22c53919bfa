# Import the os module for accessing environment variables
import os  
# Import Json module 
import json
# Import the OpenAI library for API access
import openai  
# Import logging module
import logging

medical_history_prompt = '''
This medical history template is designed to dynamically update with each new patient note, ensuring the most accurate and up-to-date record is maintained in the
patient's profile. The system automatically updates the relevant sections without losing crucial historical information. Resolved conditions, discontinued medications, or
irrelevant data are moved to their respective past categories while the most recent information is highlighted. Presenting Problem (Tab/Section Title) Problem List (New & Active medical conditions): Automatically list any new or currently active medical conditions identified in the latest patient note. Move resolved
or inactive conditions to the appropriate "Past medical history" section. Past Medical & Surgical History (Tab/Section Title) Past medical history (PMH): Automatically update with all relevant past medical conditions, including resolved or ongoing chronic conditions (e.g., asthma, resolved
allergies). Surgical history: Record any past surgical interventions mentioned in patient notes. Family & Social History (Tab/Section Title) Family History: Update with new family medical history data while keeping older relevant information. If no changes occur, retain previously recorded details. Social History: Reflect any updates in lifestyle, habits, or environmental factors, including smoking status, pet exposure, or other relevant social history. Medications & Allergies (Tab/Section Title) Current medications: Display all active medications from the latest consultation. Automatically update with new prescriptions and move discontinued medications to
"Past medications." Past medications: List medications that are no longer in use or prescribed, ensuring a record of previous treatments. Allergies: Track all active allergies. If an allergy is resolved or no longer relevant (e.g., as a result of new treatments), move it to the "Past medical history." Vitals and Observations (Tab/Section Title) Vitals: Record the latest vitals (e.g., temperature, oxygen saturation, blood pressure, heart rate) from the patient note, overwriting older vitals with the most up-to- date information. Observations: Reflect clinical observations (e.g., no wheezing, improved breathing) based on the most recent consultation, replacing old observations as necessary. Health Management (Tab/Section Title) Care Plan: Automatically update with the latest care plan, including treatment recommendations or adjustments. Past care plans will be archived but remain
accessible for reference. Recommendations: Display new recommendations or instructions provided during the consultation, replacing outdated advice while keeping critical safety netting
details. Lab results & diagnostic imaging (Tab/Section Title) Lab results (summarized): Summarize the most recent lab results from the consultation, while older results are archived for reference. Diagnostic imaging (summarized): Reflect the latest diagnostic imaging or results, with older imaging data stored for historical comparison. Other (Tab/Section Title) Other information: Capture any additional notes or information that does not fit into other categories (e.g., general advice, patient precautions, etc.). Automatic Update Logic: Seamless Updates: Every time a new patient note is added, the system will intelligently parse and categorize the information, updating relevant sections while
archiving historical data in the appropriate "Past" categories (e.g., resolved conditions, discontinued medications). No Data Loss: Ensure that no crucial information is lost. If a condition or medication becomes inactive, it moves to the relevant "Past" section. This keeps the
history intact while ensuring the most up-to-date data is always displayed. Dynamic Record Keeping: The template maintains a complete and accurate patient history by dynamically adjusting to each new consultation. The most recent data is always reflected, while past information is stored for comprehensive tracking.
'''

def generate_prompt(prompt, old_medical_history=''):
    # Old medical history in formatted string
    if old_medical_history:
        old_medical_history_str = "Old Medical History:\n" + "\n".join(f"- {entry}" for entry in old_medical_history)
    else:
        old_medical_history_str = ''
    # Additional instructions for the prompt
    additional_instructions = (
        "Please provide the output in JSON format with two keys:\n"
        "- 'letter': containing the generated content from the letter template, all in string\n"
        "- 'medical_history': containing the updated medical history based on both the latest patient notes and the old medical history.\n"
        "- Ensure that placeholders in the document structure (such as [???]) are preserved and take precedence over placeholders in the patient notes.\n"
        "- Remove any bracketed text (e.g., [PERSON]) in the patient notes from the final output.\n"
        "- Avoid markdown formatting or adding any extra information that is not in the structure or patient notes."
    )

    # Medical history template with placeholders for automatic updates based on the old medical history
    medical_history_template = {
        "Presenting Problem": {
            "Problem List": "List any newly identified or currently active medical conditions. "
                            "Move resolved or inactive conditions to 'Past Medical History.'"
        },
        "Past Medical & Surgical History": {
            "Past Medical History (PMH)": "Update with all past medical conditions, including resolved or ongoing chronic issues (e.g., asthma, resolved allergies).",
            "Surgical History": "Include any surgical interventions mentioned."
        },
        "Family & Social History": {
            "Family History": "Reflect any new family medical history, retaining previous details if unchanged.",
            "Social History": "Reflect lifestyle or environmental updates, like smoking status, pet exposure, or other relevant social factors."
        },
        "Medications & Allergies": {
            "Current Medications": "List all active medications from the latest consultation. Automatically update new prescriptions, moving discontinued medications to 'Past Medications.'",
            "Past Medications": "List any medications no longer in use.",
            "Allergies": "Track all active allergies, moving resolved or irrelevant allergies to 'Past Medical History.'"
        },
        "Vitals and Observations": {
            "Vitals": "Include latest vitals (e.g., temperature, oxygen saturation, blood pressure, heart rate), replacing older entries as needed.",
            "Observations": "Include clinical observations, updating with the most recent notes."
        },
        "Health Management": {
            "Care Plan": "Update with the latest care plan, treatment recommendations, or adjustments.",
            "Recommendations": "List any new instructions, replacing outdated advice while retaining safety-critical notes."
        },
        "Lab Results & Diagnostic Imaging": {
            "Lab Results": "Summarize the most recent lab results, archiving older data.",
            "Diagnostic Imaging": "Summarize latest diagnostic imaging, archiving past results for comparison."
        },
        "Other Information": "Include any additional notes or details that don’t fit into other categories, like general advice or patient precautions."
    }

    # Construct the JSON with the letter and history keys
    json_output = {
        "letter": f"{prompt}\n\nn{old_medical_history_str}\n\n{additional_instructions}",
        "history": {
            "template": medical_history_template,
            "old_medical_history": old_medical_history  # Incorporating old medical history here
        }
    }

    # Return the JSON structure
    return json_output

Temperature = float(os.getenv("TEMPERATURE"))
Max_tokens = int(os.getenv("MAX_TOKENS"))

from logger1 import get_logger
log_group_name = os.getenv("LogGroupName")
log_stream_name = os.getenv("LogStreamName")
logger = get_logger(log_group_name, log_stream_name)

def medical_history_completion(prompt, old_medical_history=''):
    """
    Generate completion using OpenAI API for medical history.
    Args:
        prompt (str): The prompt for generating completion.
        old_medical_history (str): The old medical history.

    Returns:
        tuple: A tuple containing the generated completion text and the generation status flag.

    Raises:
        Exception: If there is an error during the API call.
    """
    # Initialize generation_status flag to 0 (in-process)
    generation_status = 0

    # Base message
    messages = [
        {"role": "system", "content": "You are a good healthcare assistant"},
    ]
    updated_prompt = prompt
    if old_medical_history:
        updated_prompt = prompt + "\n Old Medical History: " + json.dumps(old_medical_history, indent=2)
    logger.info(f"Updated prompt: {updated_prompt}")
    messages.append({"role": "user", "content": updated_prompt})
    # Determine which model to use based on the number of images
    model = 'gpt-4o-mini'


    try:
        # Call OpenAI API with the selected model and constructed messages
        response = openai.ChatCompletion.create(
            model=model,
            messages=messages,
            temperature=0.1,
            max_tokens=1000,
            n=1,
            stop=['Sincerely', 'Kind regards']
        )
        # Set generation_status flag to 1 (complete) if API call is successful
        generation_status = 1
    except Exception as exception:
        logger.error(f"Error generating completion with OpenAI API: {exception}")
        return {
                'statusCode': 401,
                'body': json.dumps({'statusCode': 401,'status': 'error', 'message': f" Unable to access, Please wait for a while"})
                },generation_status

    # Get the completion text from the API response
    completion_text = response.choices[0].message["content"]
    return completion_text, generation_status

def get_completion(prompt, model, temperature=Temperature, max_tokens=Max_tokens, images_base64=None):
    """
    Generate completion using OpenAI API and handle multiple images.

    Args:
        prompt (str): The prompt for generating completion.
        model (str): The model to use for generating completion (default: selected_model).
        images_base64 (list): A list of base64-encoded images (optional).

    Returns:
        tuple: A tuple containing the generated completion text and the generation status flag.

    Raises:
        Exception: If there is an error during the API call.
    """
    # Initialize generation_status flag to 0 (in-process)
    generation_status = 0

    # Base message
    messages = [
        {"role": "system", "content": "You are a good healthcare assistant"},
    ]

    # Check if there are images and append them to the prompt message
    if images_base64 and len(images_base64) > 0:
        # Start with the prompt content
        user_content = [{"type": "text", "text": prompt}]

        # Add each image as a separate entry in the message content
        for image_base64 in images_base64:
            user_content.append({
                "type": "image_url",
                "image_url": {
                    "url": image_base64,
                }
            })

        messages.append({
            "role": "user",
            "content": user_content
        })
        logger.info(f"Multiple images added to the prompt")
    else:
        # If no images, just add the prompt as usual
        messages.append({"role": "user", "content": prompt})

    # Determine which model to use based on the number of images
    if images_base64 and len(images_base64) > 2:
        model = 'gpt-4o'
        logger.info("More than 2 images, using model gpt-4o")
    else:
        model = 'gpt-4o-mini'
        logger.info("2 or fewer images, using model gpt-4o-mini")

    try:
        # Call OpenAI API with the selected model and constructed messages
        response = openai.ChatCompletion.create(
            model=model,
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens,
            n=1,
            stop=['Sincerely', 'Kind regards']
        )
        # Set generation_status flag to 1 (complete) if API call is successful
        generation_status = 1
    except Exception as exception:
        logger.error(f"Error generating completion with OpenAI API: {exception}")
        return {
                'statusCode': 401,
                'body': json.dumps({'statusCode': 401,'status': 'error', 'message': f" Unable to access, Please wait for a while"})
                },generation_status

    # Get the completion text from the API response
    completion_text = response.choices[0].message["content"]
    return completion_text, generation_status

schema4 = '''
interface MedicalHistoryTemplate {
  presentingProblem: {
    problemList: string; // HTML content with <ul> and <li> tags
  };
  pastMedicalAndSurgicalHistory: {
    pastMedicalHistory: string; // HTML content with <ul> and <li> tags
    surgicalHistory: string; // HTML content with <ul> and <li> tags
  };
  familyAndSocialHistory: {
    familyHistory: string; // HTML content with <ul> and <li> tags
    socialHistory: string; // HTML content with <ul> and <li> tags
  };
  medicationsAndAllergies: {
    currentMedications: string; // HTML content with <ul> and <li> tags
    pastMedications: string; // HTML content with <ul> and <li> tags
    allergies: string; // HTML content with <ul> and <li> tags
  };
  vitalsAndObservations: {
    vitals: string; // HTML content with <ul> and <li> tags
    observations: string; // HTML content with <ul> and <li> tags
  };
  healthManagement: {
    carePlan: string; // HTML content with <ul> and <li> tags
    recommendations: string; // HTML content with <ul> and <li> tags
  };
  labResultsAndDiagnosticImaging: {
    labResults: string; // HTML content with <ul> and <li> tags
    diagnosticImaging: string; // HTML content with <ul> and <li> tags
  };
  otherInformation: string; // HTML content with <ul> and <li> tags
}
'''

def generate_letter_body(retrieve_latest_letter, input_note, template, template_name, template_description, openai_api_key, selected_model, images_base64, medical_history_content, generate_medical_history, use_medical_history):
    """
    Generate the letter body based on the input clinical notes and handle multiple images.

    Args:
        retrieve_latest_letter (str): The latest retrieved letter.
        input_note (str): The input clinical notes.
        template (str): The letter template.
        template_name (str): The name of the template.
        template_description (str): The description of the template.
        openai_api_key (str): The OpenAI API key.
        selected_model (str): The model selected for OpenAI API.
        images_base64 (list): A list of base64 encoded images.
        medical_history_content (json): The medical history content.
        generate_medical_history (bool): A flag to indicate whether to generate medical history or not.
        use_medical_history (bool): A flag to indicate whether to use the medical history or not.

    Returns:
        str: The generated letter body.

    Raises:
        None
    """
    openai.api_key = openai_api_key
    prompt = os.getenv('PROMPT')
    logger.info(f'prompt from env {prompt}')
    updated_medical_history_prompt = "Generate updated medical history json populated in history key without any ```json markdown using this patient note:" + input_note + "\n Use the following template for generating the history: " + medical_history_prompt + "\n Ensure output has the following schema: " + schema4

    if not generate_medical_history:

        if use_medical_history and medical_history_content:
            input_note += "\nUse the following Old Medical History along with this note to generate the content \n History:" + json.dumps(medical_history_content)

        # Check if there are any images to process
        if images_base64:
            # Update the template to include information about multiple images
            template += "\nImage Descriptions:\n"
            image_description_notes = "Provide descriptions for the following images:\n"

            # Append descriptions for each image in the prompt
            for idx, image in enumerate(images_base64, start=1):
                image_description_notes += f"Image {idx}: Description.\n"

            input_note += " " + image_description_notes

        # Formatting the prompt with the provided values
        prompt = prompt.format(
            retrieve_latest_letter=f'"""{retrieve_latest_letter}"""',
            input_note=input_note,
            template=template,
            template_name=template_name,
            template_description=template_description,
        )

        # Pass the list of images to the `get_completion` function
        response, status = get_completion(prompt, model=selected_model, images_base64=images_base64)
        logger.info(f"medical_history_prompt: {medical_history_prompt}")
        logger.info(f"medical_history_content: {medical_history_content}")

        if response == "":
            logger.error("Error generating letter body with OpenAI API")
            return {
                    'statusCode': 401,
                    'body': json.dumps({'statusCode': 401, 'status': 'error', 'message': "Unable to access, please wait for a while"})
                    }, status

        return response, status, None

    response2 = {}
    if generate_medical_history:
        logger.info(f"generate_medical_history: {generate_medical_history}")
        response2 = medical_history_completion(updated_medical_history_prompt, medical_history_content)

        return None, None, response2



    return response, status, response2
