  # This AWS SAM template has been generated from your function's configuration. If
  # your function has one or more triggers, note that the AWS resources associated
  # with these triggers aren't fully specified in this template and include
  # placeholder values. Open this template in AWS Application Composer or your
  # favorite IDE and modify it to specify a serverless application with other AWS
  # resources.
  AWSTemplateFormatVersion: '2010-09-09'
  Transform: AWS::Serverless-2016-10-31
  Description: An AWS Serverless Application Model template describing your function.
  Resources:
    #  clinicalnotesstagelambdatemplatesprod:
    clinicalpadstagelambda:
    clinicalnotesstagelambdatclinicalnotesstagelambdadrn4hWn4HuDB:
      Type: AWS::Serverless::Function
      Properties:
        FunctionName: clinicalpad_notes_stage_lambda
#        FunctionName: clinicalpad_notes_prod_lambda
        CodeUri: .
        Description: ''
        MemorySize: 256
        Timeout: 300
        Handler: lambda_function.lambda_handler
        Runtime: python3.11
        Architectures:
          - x86_64
        EphemeralStorage:
          Size: 512
        Environment:
          Variables:
            BUCKET: clinical-notes-config
            DEFAULT_: >-
              Diagnosis – Post-operative recovery\n\nPlan – Continue with
              non-impact exercises, physiotherapy, and gradual increase in
              activity\n\nI had the opportunity to review this male patient who is
              currently 7 weeks post-surgery. He is doing well and has been off
              crutches for the past 3 weeks. He is mobilizing well with no
              significant problems and no neurovascular deficit. His surgical
              wounds have healed nicely.\n\nHowever, his muscle tone and bulk are
              still weak. He has been prescribed non-impact exercises to help with
              his recovery. Unfortunately, due to the current virus situation,
              gyms are closed. Despite this, he has shown improvement with
              exercises using ankle weights.\n\nHe is currently undergoing
              physiotherapy with a specialist. He has been advised to focus on
              non-impact exercises and gradually build up his activity levels.
              \n\nA follow-up is scheduled for 5 weeks from now, which will be 12
              weeks post-surgery. By then, he is expected to have built up to full
              activity levels. At that point, we will assess the osteochondral
              area. If all goes well, we hope to discharge him from our care at
              that time.
            ENV: stage
            LogGroupName: AIML-Logs
            LogStreamName: aimlapplogs_stage
            MAX_TOKENS: '800'
            MAX_TOKENS: '1800'
            PROMPT: >-
              Instructions: write a well brief explaination put diagnosis and plan
              as heading at first if it is in example letter or other heading that
              is in example letter and then explanin in brief and dont write the
              heading explanation in suitable number of words for the given
              clinical short note using the info given in note do not miss any
              information from note. Pattern should be like as given in example
              body of letter below. Write full form of short important acronyms
              like HPC and PMH after understanding the context. M in short
              clinical note is for male and F is for female. check if it refferd
              to another doctor or specilist if then write the explanation to that
              doctor. Make sure the answer is correct and don't output false or
              extra content. And dont put header and footer only body. If the note
              doen't relate to medical field, answer "out of context". Stop
              generation before Sincerely. if any value divide by 7 then its days
              for example 3/7 then its in 3 days. if any value divide by 52 then
              its weeks for example 3/52 then its in 3 weeks. if any value divide
              by 12 then its months for example 3/12 then its in 3 months. Dont
              write that it is from clinical short note. it should be like if
              doctor wrote it. For example you can check below body of
              letter-\nexample:\n{retrieve_latest_letter}\n. \nQuery:
              {input_note}\nbody of letter:
              The {template_name} template should be used when generating the corresponding document.
              {template_description}. Ensure that the document captures all necessary details as per the provided structure.
              The content should be professional, complete, and reflective of the clinical nature of the document. 
              Please refer to the attached template for the structure and required sections. 
              Based on the patient's notes provided, generate a comprehensive and professional document following the attached structure and ensure that no critical information is omitted. 
              patient's notes: {input_note} \n 
              document structure: {template} \n 
              Important Note: Don't add any markdown formatting and don't add any fake information like name, medication, etc. 
              Placeholder, of the shape [???] in "document structure" needs to remain intact and have higher precedence than any corresponding [???] found inside "patient note". 
              Remove any text in [] inside patient note from the final output like [PERSON] in patient note should be removed.
              Please provide the output in JSON format with two keys: (don't add ```json markup, just output plain json)\n
              - 'letter': containing the generated content from the letter template, all in string\n
              - 'medical_history': containing the updated medical history based on both the latest patient notes and the old medical history.\n
              - Placeholder, of the shape [UPPERCASE_TEXT] in "document structure" needs to remain intact keeping them in uppercase and separated by underscore and Dont add any placeholders [???] by yourself.\n
              - Remove any bracketed text (e.g., [PERSON]) in the patient notes from the final output.\n
              - Avoid markdown formatting or adding any extra information that is not in the structure or patient notes.
            TEMPERATURE: '0.1'
        EventInvokeConfig:
          MaximumEventAgeInSeconds: 21600
          MaximumRetryAttempts: 2
          DestinationConfig: {}
        FunctionUrlConfig:
          AuthType: NONE
          InvokeMode: BUFFERED
        PackageType: Zip
        Policies:
          - Statement:
              - Effect: Allow
                Action:
                  - autoscaling:Describe*
                  - cloudwatch:*
                  - logs:*
                  - sns:*
                  - iam:GetPolicy
                  - iam:GetPolicyVersion
                  - iam:GetRole
                  - oam:ListSinks
                Resource: '*'
              - Effect: Allow
                Action:
                  - iam:CreateServiceLinkedRole
                Resource: >-
                  arn:aws:iam::*:role/aws-service-role/events.amazonaws.com/AWSServiceRoleForCloudWatchEvents*
                Condition:
                  StringLike:
                    iam:AWSServiceName: events.amazonaws.com
              - Effect: Allow
                Action:
                  - oam:ListAttachedLinks
                Resource: arn:aws:oam:*:*:sink/*
              - Effect: Allow
                Action:
                  - s3:*
                  - s3-object-lambda:*
                Resource: '*'
        SnapStart:
          ApplyOn: None
        Events:
          Api1:
            Type: Api
            Properties:
              Path: /clinicalnotespost
              Method: POST
        RuntimeManagementConfig:
          UpdateRuntimeOn: Auto


  # sam package --template-file template.yml --output-template-file sam-template.yml --s3-bucket clinicalnotes-aiml
  # sam deploy --template-file sam-template.yml --stack-name clinicalnotesstagelambdatemplatestesting --capabilities CAPABILITY_IAM
  # sam deploy --template-file /home/<USER>/old_projects/clinicalpad/backend/src/lambda/sam-template.yml --stack-name clinicalpadstagelambdatemplatestesting --capabilities CAPABILITY_IAM --region us-east-2
