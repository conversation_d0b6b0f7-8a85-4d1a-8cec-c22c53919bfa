import { Response } from 'express'
import { IResponseObject } from './utils.interface'
import * as utils from 'node:util'
import { CustomValidator } from 'joi'
import Models from '../database/models'
import en from '../i18n/en.json'
const AesKey = process.env.AES_KEY;
const CryptoJS = require("crypto-js");
const cryptoKey = process.env.ENC_CRYPTO_KEY;
const responseObject = (): IResponseObject => {
  return {
    statusCode: 200,
    status: 'success',
    msgCode: '', // default empty msgCode
    msg: '',
    data: undefined,
  }
}

/**
 * Send response
 */
const cRes = (res: Response, data: any) => {
  return res.status(data.statusCode).json(data)
}



function securityKey() {
  return process.env.AES_KEY
}


/**
 * Generate random number
 */
const generateRandomNumber = (length: number) => {
  return Math.floor(10 ** (length - 1) + Math.random() * (9 * 10 ** (length - 1)))
}

function getLanguage(lan) {
  if (lan === 'en') {
    return en
  }
}

export default function getMessage(code, lan = 'en') {
  const language = getLanguage(lan)
  const message = language[code]
  return message
}

//encrypet text
 function systemEncrypt(text:any) {
   return Models.sequelize.fn('PGP_SYM_ENCRYPT', text, AesKey)
}

//encrypet text
function systemDcrypt(text: any) {

  return Models.sequelize.fn('PGP_SYM_ENCRYPT', text, AesKey)
}

/**
 * This function can be used to log error or any information
 * The purose to use this so in future if we need to log in file/db can be done from here only
 * @param {object} error
 * @param {string} msg
 */
const logErrors = function (error, msg: any = '') {
  console.log('--------------------')
  if (msg) {
    console.log('=== ', msg, ' ===')
  }
  console.log(error)
  console.log('--------------------')
}

const logObj = function (data: any, depth = 5) {
  console.log(utils.inspect(data, false, depth))
}

function cryptoJsEncrypt(text: any) {
  return CryptoJS.AES.encrypt(text, cryptoKey).toString();
}

function cryptoJsObjectEncrypt(data: any) {
  return CryptoJS.AES.encrypt(JSON.stringify(data), cryptoKey).toString();
}

function cryptoJsDcrypt(encryptedText: any) {
  const bytes = CryptoJS.AES.decrypt(encryptedText, cryptoKey);
  return bytes.toString(CryptoJS.enc.Utf8);
}

function cryptoJsObjectDcrypt(data: any) {
  const bytes = CryptoJS.AES.decrypt(data, cryptoKey);
  return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
}





export { responseObject, cRes, securityKey, generateRandomNumber, getMessage, systemEncrypt, logErrors, logObj, cryptoJsEncrypt, cryptoJsDcrypt, cryptoJsObjectEncrypt, cryptoJsObjectDcrypt }
