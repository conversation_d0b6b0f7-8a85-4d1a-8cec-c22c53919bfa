import * as express from 'express'
import { IResponseObject } from './utils.interface'
import * as UtilsHelper from './utils.helper'
import { genralLogger } from './logger.helpers'

export default class BaseController {
  /**
   * Method to send response
   */
  public sendResponse(res: express.Response, data: IResponseObject) {
    return UtilsHelper.cRes(res, data)
  }

  public logErrors(err: Error, msg: string) {
    console.log('--------------------')
    console.log('=== ', msg, ' ===')
    console.log(err)
    console.log('--------------------');
    //genralLogger.error('error', msg, { additionalInfo: { error: err, tags: 'Error Log', } });
    genralLogger.error(msg, { additionalInfo: { error: err?.message, tags: 'Error Log' } });
  }
}
