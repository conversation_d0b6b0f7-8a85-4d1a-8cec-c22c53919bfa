const bcrypt = require('bcrypt')

/**
 * Function to hash password
 * @param  String pwd Plain password to hash
 * @return String
 */
export async function hashPassword(pwd: string) {
  const saltRounds = 10
  try {
    const salt = await bcrypt.genSalt(saltRounds)
    return await bcrypt.hash(pwd, salt)
  } catch (err: any) {

    throw new Error(err)
  }
}

/**
 * Function to compare password
 * @param  String pwd 		Plain password
 * @param  String hashPwd Hashed password from db
 * @return bool
 */
export async function comparePassword(pwd: string, hashPwd: string) {
  try {
    return await bcrypt.compare(pwd, hashPwd)
  } catch (err: any) {

    throw new Error(err)
  }
}

/**
 * Function to generate Password
 * @param  number pwd 		default 8
 *
 * @return string
 */
export async function generatePassword(length = 8) {
  let stringInclude = ''
  stringInclude += '@$!%*?&#^'
  stringInclude += '0123456789'
  stringInclude += 'abcdefghijklmnopqrstuvwxyz'
  stringInclude += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  let password = ''
  for (let i = 0; i < length; i++) {
    password += stringInclude.charAt(Math.floor(Math.random() * stringInclude.length))
  }
  return password
}
