import path from 'path'
import {sendMail} from '../services/Mail'
import * as UtilsHelper from '../helpers/utils.helper'

const ejs = require('ejs')

/**
 * Send response
 */

const frontEndUrl = 'https://staging.clinicalpad.com' // process.env.FRONT_END_URL


// Contact Support Email
const sendContactUsEmail = async (data: any) => {
  try {
    const { type, subject, details, toEmail, enquiryType, userEmail, userName } = data;
    // Dynamically load template based on type (e.g., "info", "alert", etc.)
    const _filePath = path.resolve(__dirname, `../templates/${type}.ejs`);

    const templateData = {
      subject: `Support Email - ${subject}`,
      details,
      enquiryType,
      userEmail,
      userName,
      frontEndUrl, // optional if you want to include it
    };

    const _html = await ejs.renderFile(_filePath, { data: templateData });
    return await sendMail(toEmail, subject, _html, _html, '');
  } catch (error) {
    console.log(error);
    UtilsHelper.logErrors(error, "Error in EmailHelper.sendContactUsEmail");
    throw new Error('EMAIL_ERROR');
  }
};


const userVarifyEmail = async (data: any) => {
  try {
    const _sub = 'Neurosense - Verify your email'
    const _filePath: string = path.resolve(__dirname, '../templates/user-verify-email.ejs');
    data.data.frontEndUrl = frontEndUrl;
    const _html = await ejs.renderFile(_filePath, data)
    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {

    //UtilsHelper.logErrors(error, "Error in EmailHelper.userVarifyEmail");

    throw new Error('EMAIL_ERROR')
  }
}

// TODO: add email template for userTrialEndIn2DaysEmail
const userTrialEndIn2DaysEmail = async (data: any) => {
  try {
    const _sub = 'Neurosense - Verify your email'
    const _filePath = path.resolve(__dirname, '../templates/user-verify-email.ejs');
    data.data.frontEndUrl = frontEndUrl;
    const _html = await ejs.renderFile(_filePath, data)
    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {
    throw new Error('EMAIL_ERROR')
  }
}

const userContactUsEmail = async (data: any) => {
  try {
    const _sub = data.data.subject
    const _filePath = path.resolve(__dirname, '../templates/conatct-us-email.ejs');
    data.data.frontEndUrl = frontEndUrl;
    const _html = await ejs.renderFile(_filePath, data)

    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {

    //UtilsHelper.logErrors(error, "Error in EmailHelper.userVarifyEmail");

    throw new Error('EMAIL_ERROR')
  }
}

const userFeedbackEmail = async (data: any) => {
  console.log('data', data)
  try {
    const _sub = data.data.subject
    const _filePath = path.resolve(__dirname, '../templates/feedback-email.ejs');
    data.data.frontEndUrl = frontEndUrl;
    const _html = await ejs.renderFile(_filePath, data)
    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {


    throw new Error('EMAIL_ERROR')
  }
}
const userSupportEmail = async (data: any) => {

  try {
    const _sub = data.data.subject
    const _filePath = path.resolve(__dirname, '../templates/support-email.ejs');
    data.data.frontEndUrl = frontEndUrl;
    const _html = await ejs.renderFile(_filePath, data)
    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {

    throw new Error('EMAIL_ERROR')
  }
}

const userForgotPassword = async (data: any) => {
  try {
    const _sub = 'ClinicalPad - Reset your password'
    const _filePath = path.resolve(__dirname, '../templates/user-forgot-password-email.ejs');
    data.data.frontEndUrl = frontEndUrl;
    const _html = await ejs.renderFile(_filePath, data)

    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {


    throw new Error('EMAIL_ERROR')
  }
}

const userInvite = async (data: any) => {
  try {
    const _sub = 'ClinicalPad - User Invitation'
    const _filePath = path.resolve(__dirname, '../templates/user-invitation-email.ejs');
    data.data.frontEndUrl = frontEndUrl;
    const _html = await ejs.renderFile(_filePath, data)
    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {

    throw new Error('EMAIL_ERROR')
  }
}

const adminOTP2FA = async (userData) => {
  try {
    const _sub = "ClinicalPad Admin - OTP to Login";
    const _filePath = path.resolve(__dirname, "../templates/admin--OTP-2FA-login-email.ejs");
    userData.data.frontEndUrl = frontEndUrl;
    const _html = await ejs.renderFile(_filePath, userData);
    console.log(userData)

    return await sendMail(userData.toEmail, _sub, _html, _html, '');
  }
  catch (error) {
    UtilsHelper.logErrors(error, "Error in EmailHelper.adminOTP2FA");

    throw new Error("EMAIL_ERROR");
  }
}

const adminForgotPassword = async (data) => {
  try {
    const _sub = "ClinicalPad Admin - Reset your password";
    const _filePath = path.resolve(__dirname, "../templates/admin-forgot-password-email.ejs");
    data.data.frontEndUrl = frontEndUrl;
    const _html = await ejs.renderFile(_filePath, data);

    return await sendMail(data.toEmail, _sub, _html, _html,'');
  }
  catch (error) {
    UtilsHelper.logErrors(error, "Error in EmailHelper.adminForgotPassword");

    throw new Error("EMAIL_ERROR");
  }
}

const adminUserInvite = async (data: any) => {
  try {
    const _sub = 'ClinicalPad - Admin User Invitation'
    const _filePath = path.resolve(__dirname, '../templates/admin-user-invitation-email.ejs');
    data.data.frontEndUrl = frontEndUrl;
    const _html = await ejs.renderFile(_filePath, data)

    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {

    throw new Error('EMAIL_ERROR')
  }
}

const userCreateSubscription = async (data: any) => {
  try {
    const _sub = 'ClinicalPad - Subscription Success!'
    const _filePath = path.resolve(__dirname, '../templates/user-create-subscription.ejs');
    data.data.frontEndUrl = frontEndUrl;
    const _html = await ejs.renderFile(_filePath, data);
    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {
    throw new Error('EMAIL_ERROR')
  }
}

const userUpdateSubscription = async (data: any) => {
  try {
    const _sub = 'ClinicalPad - Subscription Update Confirmation'
    const _filePath = path.resolve(__dirname, '../templates/user-update-subscription.ejs');
    data.data.frontEndUrl = frontEndUrl;

    const _html = await ejs.renderFile(_filePath, data);
    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {
    throw new Error('EMAIL_ERROR')
  }
}

const userCancelSubscription = async (data: any) => {
  try {
    const _sub = 'ClinicalPad - Subscription Cancellation Confirmation'
    const _filePath = path.resolve(__dirname, '../templates/user-cancel-subscription.ejs');
    data.data.frontEndUrl = frontEndUrl;

    const _html = await ejs.renderFile(_filePath, data);
    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {
    throw new Error('EMAIL_ERROR')
  }
}

const userBuyCreditEmail = async (data: any) => {
  try {
    const _sub = 'ClinicalPad - Credit Purchase Confirmation'
    const _filePath = path.resolve(__dirname, '../templates/user-credit-purchase.ejs');
    data.data.frontEndUrl = frontEndUrl;

    const _html = await ejs.renderFile(_filePath, data);
    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {
    throw new Error('EMAIL_ERROR')
  }
}

const userPaymentFailedWarning = async (data: any) => {
  try {
    const _sub = 'ClinicalPad - Important: Payment Failed Warning'
    const _filePath = path.resolve(__dirname, '../templates/user-second-failed-payment-notification.ejs');
    data.data.frontEndUrl = frontEndUrl;
    const _html = await ejs.renderFile(_filePath, data)
    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {
    throw new Error('EMAIL_ERROR')
  }
}

const userPaymentFailedCancellation = async (data: any) => {
  try {
    const _sub = 'ClinicalPad - Subscription Cancelled Due to Payment Failure'
    const _filePath = path.resolve(__dirname, '../templates/user-third-failed-payment-notification.ejs');
    data.data.frontEndUrl = frontEndUrl;
    const _html = await ejs.renderFile(_filePath, data)
    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {
    throw new Error('EMAIL_ERROR')
  }
}

const  sendSecurityNotificationEmail = async (data: any) => {
  try {
    const _sub = 'ClinicalPad - Updated Settings'

    // Append frontEndUrl inside data.data
    data.data.frontEndUrl = frontEndUrl;
    const _filePath = path.resolve(__dirname, '../templates/user-profile-password-update-notification.ejs');

    const _html = await ejs.renderFile(_filePath, data);
    console.log(_html);
    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {
    console.log(error)
    throw new Error('EMAIL_ERROR')
  }
}
const userSubscriptionFailedFirstWarning = async (data: any) => {
  try {
    const _sub = 'ClinicalPad - Updated Settings'
    // Append frontEndUrl inside data.data
    data.data.frontEndUrl = frontEndUrl;
    const _filePath = path.resolve(__dirname, '../templates/user-first-failed-payment-notification.ejs');

    console.log(data)
    // Now render with only data.data passed to EJS
    const _html = await ejs.renderFile(_filePath, data);
    console.log(_html);
    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {
    console.log(error)
    throw new Error('EMAIL_ERROR')
  }
}

const userSubscriptionRenewEmail = async (data: any) => {
  try {
    const _sub = 'ClinicalPad - Subscription Renewal Reminder'
    const _filePath = path.resolve(__dirname, '../templates/user-subscription-renew.ejs');
    data.data.frontEndUrl = frontEndUrl;
    const _html = await ejs.renderFile(_filePath, data)
    console.log({_html});
    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {
    UtilsHelper.logErrors(error, "Error in EmailHelper.userSubscriptionRenewEmail");
    throw new Error('EMAIL_ERROR')
  }
}

const userCancelSubscriptionTrial = async (data: any) => {
  try {
    const _sub = 'ClinicalPad - Trial Subscription Cancellation'
    const _filePath = path.resolve(__dirname, '../templates/user-cancel-subscription-trial.ejs');
    data.data.frontEndUrl = frontEndUrl;
    
    const _html = await ejs.renderFile(_filePath, data);
    return await sendMail(data.toEmail, _sub, _html, _html, '')
  } catch (error) {
    throw new Error('EMAIL_ERROR')
  }
}

export {
  userVarifyEmail,
  userForgotPassword,
  userInvite,
  userContactUsEmail,
  sendContactUsEmail,
  userFeedbackEmail,
  userSupportEmail,
  sendSecurityNotificationEmail,
  adminOTP2FA,
  adminForgotPassword,
  adminUserInvite,
  userCreateSubscription,
  userUpdateSubscription,
  userCancelSubscription,
  userBuyCreditEmail,
  userTrialEndIn2DaysEmail,
  userPaymentFailedWarning,
  userPaymentFailedCancellation,
  userSubscriptionRenewEmail,
  userCancelSubscriptionTrial,
  userSubscriptionFailedFirstWarning
}
