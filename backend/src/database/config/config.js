const dotenv = require('dotenv')

dotenv.config()

/* interface ConfigTs {
    development: Options;
    test: Options;
    production: Options;
} */

const config = {
  development: {
    url: process.env.DB_URL,
    dialect: 'postgres',
    // logging: logObj,
    // logQueryParameters: true,
    define: {
      underscored: true,
      underscoredAll: true,
      timestamps: true,
      // createdAt: "created_at",
      // updatedAt: "updated_at"
    },
  },
  staging: {
    url: process.env.DB_URL,
    dialect: 'postgres',
    logging: false,
    define: {
      underscored: true,
      underscoredAll: true,
      timestamps: true,
      // createdAt: "created_at",
      // updatedAt: "updated_at"
    },
  },
  production: {
    url: process.env.DB_URL,
    dialect: 'postgres',
    logging: false,
    define: {
      underscored: true,
      underscoredAll: true,
      timestamps: true,
      // createdAt: "created_at",
      // updatedAt: "updated_at"
    },
  },
}
//export default config;

module.exports = config
