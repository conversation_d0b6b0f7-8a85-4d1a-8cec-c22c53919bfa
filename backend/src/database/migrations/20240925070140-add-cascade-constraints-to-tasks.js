'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // First, drop existing constraints
    await queryInterface.removeConstraint('tasks', 'tasks_assigned_to_fkey');
    await queryInterface.removeConstraint('tasks', 'tasks_last_modified_by_fkey');
    await queryInterface.removeConstraint('tasks', 'tasks_letter_id_fkey');
    await queryInterface.removeConstraint('tasks', 'tasks_task_owner_fkey');

    // Recreate constraints with ON DELETE CASCADE where required
    await queryInterface.addConstraint('tasks', {
      fields: ['assigned_to'],
      type: 'foreign key',
      name: 'tasks_assigned_to_fkey',
      references: {
        table: 'clinical_users',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });

    await queryInterface.addConstraint('tasks', {
      fields: ['last_modified_by'],
      type: 'foreign key',
      name: 'tasks_last_modified_by_fkey',
      references: {
        table: 'clinical_users',
        field: 'id',
      },
      onDelete: 'CASCADE', // Updated to CASCADE
      onUpdate: 'NO ACTION',
    });

    await queryInterface.addConstraint('tasks', {
      fields: ['letter_id'],
      type: 'foreign key',
      name: 'tasks_letter_id_fkey',
      references: {
        table: 'patient_clinical_notes_letters',
        field: 'id',
      },
      onDelete: 'CASCADE', // Updated to CASCADE
      onUpdate: 'NO ACTION',
    });

    await queryInterface.addConstraint('tasks', {
      fields: ['task_owner'],
      type: 'foreign key',
      name: 'tasks_task_owner_fkey',
      references: {
        table: 'clinical_users',
        field: 'id',
      },
      onDelete: 'NO ACTION',
      onUpdate: 'NO ACTION',
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Rollback the changes by dropping and re-creating the original constraints

    await queryInterface.removeConstraint('tasks', 'tasks_assigned_to_fkey');
    await queryInterface.removeConstraint('tasks', 'tasks_last_modified_by_fkey');
    await queryInterface.removeConstraint('tasks', 'tasks_letter_id_fkey');
    await queryInterface.removeConstraint('tasks', 'tasks_task_owner_fkey');

    await queryInterface.addConstraint('tasks', {
      fields: ['assigned_to'],
      type: 'foreign key',
      name: 'tasks_assigned_to_fkey',
      references: {
        table: 'clinical_users',
        field: 'id',
      },
      onDelete: 'NO ACTION', // Original value
      onUpdate: 'CASCADE',
    });

    await queryInterface.addConstraint('tasks', {
      fields: ['last_modified_by'],
      type: 'foreign key',
      name: 'tasks_last_modified_by_fkey',
      references: {
        table: 'clinical_users',
        field: 'id',
      },
      onDelete: 'NO ACTION',
      onUpdate: 'NO ACTION',
    });

    await queryInterface.addConstraint('tasks', {
      fields: ['letter_id'],
      type: 'foreign key',
      name: 'tasks_letter_id_fkey',
      references: {
        table: 'patient_clinical_notes_letters',
        field: 'id',
      },
      onDelete: 'NO ACTION',
      onUpdate: 'NO ACTION',
    });

    await queryInterface.addConstraint('tasks', {
      fields: ['task_owner'],
      type: 'foreign key',

      name: 'tasks_task_owner_fkey',
      references: {
        table: 'clinical_users',
        field: 'id',
      },
      onDelete: 'NO ACTION',
      onUpdate: 'NO ACTION',
    });
  },
};
