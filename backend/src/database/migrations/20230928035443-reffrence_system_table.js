'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
     await queryInterface.sequelize.query(`
      ALTER TABLE "system_user_credentials"
      DROP CONSTRAINT IF EXISTS "system_user_credentials_foreign_key_user_id";
      ALTER TABLE "system_user_credentials"
      ADD CONSTRAINT "system_user_credentials_foreign_key_user_id"
      FOREIGN KEY ("user_id")
      REFERENCES "system_users" ("id")
      ON DELETE CASCADE
      ON UPDATE CASCADE;
    `);

     await queryInterface.sequelize.query(`
      ALTER TABLE "system_user_permissions"
      DROP CONSTRAINT IF EXISTS "system_user_permissions_foreign_key_user_id";
      ALTER TABLE "system_user_permissions"
      ADD CONSTRAINT "system_user_permissions_foreign_key_user_id"
      FOREIGN KEY ("user_id")
      REFERENCES "system_users" ("id")
      ON DELETE CASCADE
      ON UPDATE CASCADE;
    `);


  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
