'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Change `status` column from INTEGER to STRING
    await queryInterface.changeColumn('feedbacks', 'status', {
      type: Sequelize.STRING,
      allowNull: true, // Adjust if required
    });

    // Add `rating` column with INTEGER type
    await queryInterface.addColumn('feedbacks', 'rating', {
      type: Sequelize.INTEGER,
      allowNull: true, // Adjust if required
      validate: { min: 1, max: 5 },
    });
  },

  async down(queryInterface, Sequelize) {
    // Revert `status` column back to INTEGER
    await queryInterface.changeColumn('feedbacks', 'status', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });

    // Remove `rating` column
    await queryInterface.removeColumn('feedbacks', 'rating');
  },
};
