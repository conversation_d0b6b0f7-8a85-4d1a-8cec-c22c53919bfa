'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */

    await queryInterface.sequelize.query(`
      ALTER TABLE "clinical_default_user_role_permissions"
      DROP CONSTRAINT IF EXISTS "clinical_default_user_role_permissions_foreign_key_role_id";

      ALTER TABLE "clinical_default_user_role_permissions"
      ADD CONSTRAINT "clinical_default_user_role_permissions_foreign_key_role_id"
      FOREIGN KEY ("role_id")
      REFERENCES "clinical_roles" ("id")
      ON DELETE CASCADE
      ON UPDATE CASCADE;
    `);

     /* await queryInterface.sequelize.query(`
      ALTER TABLE "clinical_user_deactive_reasons"
      DROP CONSTRAINT IF EXISTS "clinical_user_deactive_reasons_foreign_key_user_id";
      ALTER TABLE "clinical_user_deactive_reasons"
      ADD CONSTRAINT "clinical_user_deactive_reasons_foreign_key_user_id"
      FOREIGN KEY ("user_id")
      REFERENCES "clinical_users" ("id")
      ON DELETE CASCADE
      ON UPDATE CASCADE;
    `); */

     await queryInterface.sequelize.query(`
      ALTER TABLE "patient_clinical_notes_letters"
      DROP CONSTRAINT IF EXISTS "patient_clinical_notes_letters_key_user_id";
      ALTER TABLE "patient_clinical_notes_letters"
      ADD CONSTRAINT "patient_clinical_notes_letters_key_user_id"
      FOREIGN KEY ("user_id")
      REFERENCES "clinical_users" ("id")
      ON DELETE CASCADE
      ON UPDATE CASCADE;
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE "patient_clinical_notes_letters"
      DROP CONSTRAINT IF EXISTS "patient_clinical_notes_letters_key_patient_id";
      ALTER TABLE "patient_clinical_notes_letters"
      ADD CONSTRAINT "patient_clinical_notes_letters_key_patient_id"
      FOREIGN KEY ("patient_id")
      REFERENCES "patients" ("id")
      ON DELETE CASCADE
      ON UPDATE CASCADE;
    `);

     await queryInterface.sequelize.query(`
      ALTER TABLE "patients"
      DROP CONSTRAINT IF EXISTS "patients_key_user_id";
      ALTER TABLE "patients"
      ADD CONSTRAINT "patients_key_user_id"
      FOREIGN KEY ("user_id")
      REFERENCES "clinical_users" ("id")
      ON DELETE CASCADE
      ON UPDATE CASCADE;
    `);

     await queryInterface.sequelize.query(`
      ALTER TABLE "clinical_user_invitations"
      DROP CONSTRAINT IF EXISTS "clinical_user_invitations_foreign_key_owneruser_id";
      ALTER TABLE "clinical_user_invitations"
      ADD CONSTRAINT "clinical_user_invitations_foreign_key_owneruser_id"
      FOREIGN KEY ("owner_user_id")
      REFERENCES "clinical_users" ("id")
      ON DELETE CASCADE
      ON UPDATE CASCADE;
    `);



  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
