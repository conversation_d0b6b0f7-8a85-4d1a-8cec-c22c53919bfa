'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('patient_clinical_notes_letters', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      clinic_id: {
        type: Sequelize.UUID
      },
      user_id: {
        type: Sequelize.INTEGER
      },
      patient_id: {
        type: Sequelize.INTEGER
      },
      clinical_note: {
        type: Sequelize.TEXT
      },
      system_genrated_body_content: {
        type: Sequelize.TEXT
      },
      updated_body_content: {
        type: Sequelize.TEXT
      },
      header_content: {
        type: Sequelize.TEXT
      },
      footer_content: {
        type: Sequelize.TEXT
      },
      approval_rating: {
        type: Sequelize.INTEGER
      },
      is_letter_printed: {
        type: Sequelize.BOOLEAN
      },
      is_letter_emailed: {
        type: Sequelize.BOOLEAN
      },
      is_letter_downloaded: {
        type: Sequelize.BOOLEAN
      },
      status: {
        type: Sequelize.INTEGER
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('patient_clinical_notes_letters');
  }
};