'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('patient_clinical_notes_letters', 'prompt_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'clinical_user_llm_prompts',
        key: 'id'
      },
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('patient_clinical_notes_letters', 'prompt_id');
  }
};
