'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('clinical_user_transactions', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      user_id: {
        type: Sequelize.INTEGER
      },
      transaction_date: {
        type: Sequelize.DATE
      },
      transaction_type: {
        type: Sequelize.INTEGER
      },
      documents_created: {
        type: Sequelize.INTEGER
      },
      team_members_used: {
        type: Sequelize.INTEGER
      },
      dictation_minutes_used: {
        type: Sequelize.INTEGER
      },
      currency:{
        type: Sequelize.STRING,
        allowNull: true,
      },
      credit_amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
      },
      action: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      status: {
        type: Sequelize.INTEGER
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('clinical_user_transactions');
  }
};