'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
     await queryInterface.sequelize.query(`
      ALTER TABLE "clinical_users"
      DROP CONSTRAINT IF EXISTS "clinical_users_foreign_key_town_id";
      ALTER TABLE "clinical_users"
      ADD CONSTRAINT "clinical_users_foreign_key_town_id"
      FOREIGN KEY ("town_id")
      REFERENCES "towns" ("id")
      ON DELETE CASCADE
      ON UPDATE CASCADE;
    `);

     await queryInterface.sequelize.query(`
      ALTER TABLE "clinical_users"
      DROP CONSTRAINT IF EXISTS "clinical_users_foreign_key_county_id";
      ALTER TABLE "clinical_users"
      ADD CONSTRAINT "clinical_users_foreign_key_county_id"
      FOREIGN KEY ("county_id")
      REFERENCES "county_states" ("id")
      ON DELETE CASCADE
      ON UPDATE CASCADE;
    `);

     await queryInterface.sequelize.query(`
      ALTER TABLE "clinical_users"
      DROP CONSTRAINT IF EXISTS "FK_clinical_specializations_id_clinical_users";

      ALTER TABLE "clinical_users"
      ADD CONSTRAINT "FK_clinical_specializations_id_clinical_users"
      FOREIGN KEY ("clinical_specializations_id")
      REFERENCES "clinical_specializations" ("id")
      ON DELETE CASCADE
      ON UPDATE CASCADE;
    `);


  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
