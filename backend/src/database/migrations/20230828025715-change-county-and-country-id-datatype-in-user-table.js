'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
     return queryInterface.sequelize.transaction(t => {
      return Promise.all([
        //queryInterface.sequelize.query('ALTER TABLE clinical_users ALTER COLUMN countrty_id TYPE integer USING (countrty_id::integer)'),
        queryInterface.sequelize.query('ALTER TABLE clinical_users ALTER COLUMN county_id TYPE integer USING (county_id::integer)'),

      ])
    })
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    /* return queryInterface.sequelize.transaction(t => {
      return Promise.all([
        queryInterface.changeColumn('clinical_users', 'countrty_id', {
          type: Sequelize.STRING,
          allowNull: true,
        },{ transaction: t }),
        queryInterface.changeColumn('clinical_users', 'county_id', {
          type: Sequelize.STRING,
          allowNull: true,
        },{ transaction: t })
      ])
    }) */
  }
};
