'use strict'
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('clinics', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      clinic_id: {
        type: Sequelize.UUID,
      },
      name: {
        type: Sequelize.STRING,
      },
      address: {
        type: Sequelize.STRING,
      },
      registartion_number: {
        type: Sequelize.STRING,
      },
      country_id: {
        type: Sequelize.STRING,
      },
      county_id: {
        type: Sequelize.STRING,
      },
      town_id: {
        type: Sequelize.INTEGER,
      },
      phone: {
        type: Sequelize.BIGINT,
      },
      post_code: {
        type: Sequelize.STRING,
      },
      status: {
        type: Sequelize.SMALLINT,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    })
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('clinics')
  },
}
