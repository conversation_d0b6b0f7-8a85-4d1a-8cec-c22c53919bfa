'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('clinical_user_subscriptions', 'customer_id', {
      type: Sequelize.STRING,
      allowNull: true, // Adjust as needed
    })

    await queryInterface.addColumn('clinical_user_subscriptions', 'billing_address_info', {
      type: Sequelize.JSON,
      allowNull: true, // Adjust as needed
    })
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn('clinical_user_subscriptions', 'customer_id')
    await queryInterface.removeColumn('clinical_user_subscriptions', 'billing_address_info')
  },
}
