'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query('CREATE TYPE enum_subscription_plans_currency AS ENUM (\'USD\', \'GBP\', \'EUR\');');
    await queryInterface.createTable('subscription_plans', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.STRING
      },
      description: {
        type: Sequelize.TEXT
      },
      price: {
        type: Sequelize.DECIMAL(10, 2),
        defaultValue: 0.00,
      },
      additional_document_price: {
        type: Sequelize.DECIMAL(10, 2),
        defaultValue: 0.00,
      },
      plan_type: {
        type: Sequelize.ENUM('Monthly', 'Yearly'),
        defaultValue: 'Monthly',
      },
      currency: {
        type: Sequelize.ENUM('USD', 'GBP', 'EUR'),
         defaultValue: 'USD',
      },
      trial_period_days: {
        type: Sequelize.INTEGER,
         defaultValue: 0,
      },
      free_trial_docs: {
        type: Sequelize.INTEGER,
        defaultValue: 20,
      },
      custom_documents_price: {
        type: Sequelize.DECIMAL(10, 2),
        defaultValue: 0.00,
      },
      custom_dictation_duration_minutes: {
        type: Sequelize.INTEGER,
         defaultValue: 0,
      },
      custom_max_team_members: {
        type: Sequelize.INTEGER,
         defaultValue: 0,
      },
      included_docs: {
        type: Sequelize.INTEGER,
         defaultValue: 100,
      },
      dictation_limit_per_doc_min: {
        type: Sequelize.INTEGER,
         defaultValue: 2,
      },
      max_team_members: {
        type: Sequelize.INTEGER,
         defaultValue: 6,
      },
      free_support: {
        type: Sequelize.BOOLEAN,
         defaultValue: true,
      },
      cancel_anytime: {
        type: Sequelize.BOOLEAN,
         defaultValue: true,
      },
       priority_feature_access: {
        type: Sequelize.BOOLEAN,
         defaultValue: false,
      },
      stripe_price_id: {
        type: Sequelize.STRING
      },
      status: {
        type: Sequelize.INTEGER,
         defaultValue: 0,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('plans');
  }
};
