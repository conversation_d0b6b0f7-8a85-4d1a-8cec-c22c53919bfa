'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('clinical_user_notes_templates', 'thumbnail_uuid', {
      type: Sequelize.UUID,
      allowNull: false,
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('clinical_user_notes_templates', 'thumbnail_uuid');
  }
};
