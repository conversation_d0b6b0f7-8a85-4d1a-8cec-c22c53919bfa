'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Change the data type of columns
    await queryInterface.changeColumn('clinics', 'country_id', {
      type: Sequelize.STRING,
    });
    await queryInterface.changeColumn('clinics', 'county_id', {
      type: Sequelize.STRING,
    });
    await queryInterface.changeColumn('clinics', 'town_id', {
      type: Sequelize.STRING,
    });

    // Perform the data conversion
    const users = await queryInterface.sequelize.query('SELECT * FROM "clinics" WHERE country_id IS NOT NULL AND county_id IS NOT NULL AND town_id IS NOT NULL', {
      type: queryInterface.sequelize.QueryTypes.SELECT,
    });

    for (const user of users) {
      let country="";
      let state="";
      let city="";

      const countryData = await queryInterface.sequelize.query('SELECT name FROM "country" WHERE id = ' + user.country_id, {
        type: queryInterface.sequelize.QueryTypes.SELECT,
      });

      if(countryData.length > 0){
        country = countryData[0].name;
      }

      const stateData = await queryInterface.sequelize.query('SELECT name FROM "county_states" WHERE id = ' + user.county_id, {
        type: queryInterface.sequelize.QueryTypes.SELECT,
      });

       if(stateData.length > 0){
        state = stateData[0].name;
      }


      const cityData = await queryInterface.sequelize.query('SELECT name FROM "towns" WHERE id = ' + user.town_id, {
        type: queryInterface.sequelize.QueryTypes.SELECT,
      });

      if(cityData.length > 0){
        city = cityData[0].name;
      }

      await queryInterface.sequelize.query(`UPDATE "clinics" SET
        "country_id" = '${country}',
        "county_id" = '${state}',
        "town_id" = '${city}'
        WHERE "id" = ${user.id}`);
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Define the down migration if needed
    await queryInterface.changeColumn('clinics', 'country_id', {
      type: Sequelize.INTEGER,
    });
    await queryInterface.changeColumn('clinics', 'county_id', {
      type: Sequelize.INTEGER,
    });
    await queryInterface.changeColumn('clinics', 'town_id', {
      type: Sequelize.INTEGER,
    });
  },
};
