'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.createTable('medical_history_changes', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      medical_history_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'patient_medical_history', // Referenced table name
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      clinical_user_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'clinical_users', // Referenced table name
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      patient_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'patients',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      operation: {
        type: Sequelize.ENUM('CREATE', 'UPDATE', 'DELETE'),
        allowNull: false,
      },
      changes: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
      }
    });

    // Add indexes for optimized queries
    await queryInterface.addIndex('medical_history_changes', ['medical_history_id']);
    await queryInterface.addIndex('medical_history_changes', ['clinical_user_id']);
    await queryInterface.addIndex('medical_history_changes', ['patient_id']);
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    // Remove indexes
    await queryInterface.removeIndex('medical_history_changes', ['medical_history_id']);
    await queryInterface.removeIndex('medical_history_changes', ['clinical_user_id']);
    await queryInterface.removeIndex('medical_history_changes', ['patient_id']);

    // Drop the table
    await queryInterface.dropTable('medical_history_changes');
  }
};
