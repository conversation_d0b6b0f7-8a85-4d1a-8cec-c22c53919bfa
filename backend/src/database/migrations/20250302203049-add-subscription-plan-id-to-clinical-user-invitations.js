'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('clinical_user_invitations', 'subscription_plan_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      comment: 'Subscription plan id associated with the invitation'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('clinical_user_invitations', 'subscription_plan_id');
  }
};
