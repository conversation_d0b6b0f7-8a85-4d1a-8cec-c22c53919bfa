'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Change the data type of columns
    await queryInterface.renameColumn('clinical_users', 'countrty_id', 'country');

    await queryInterface.removeConstraint('clinical_users', 'clinical_users_foreign_key_county_id');

    await queryInterface.removeConstraint('clinical_users', 'clinical_users_foreign_key_town_id');

    await queryInterface.changeColumn('clinical_users', 'country', {
      type: Sequelize.STRING,
    });
    await queryInterface.changeColumn('clinical_users', 'county_id', {
      type: Sequelize.STRING,
    });
    await queryInterface.changeColumn('clinical_users', 'town_id', {
      type: Sequelize.STRING,
    });

    // Perform the data conversion
    const users = await queryInterface.sequelize.query('SELECT * FROM "clinical_users" WHERE county_id IS NOT NULL AND town_id IS NOT NULL', {
      type: queryInterface.sequelize.QueryTypes.SELECT,
    });

    for (const user of users) {

       let country = "";
      let state = "";
      let city = "";

       if (user?.country) {
        const countryData = await queryInterface.sequelize.query('SELECT name FROM "country" WHERE id = ' + user.country, {
          type: queryInterface.sequelize.QueryTypes.SELECT,
        });

        if (countryData.length > 0) {
          country = countryData[0].name;
        }
      }

      if (user?.county_id) {
        const stateData = await queryInterface.sequelize.query('SELECT name FROM "county_states" WHERE id = ' + user.county_id, {
          type: queryInterface.sequelize.QueryTypes.SELECT,
        });

        if (stateData.length > 0) {
          state = stateData[0].name;
        }

      }
      if (user?.town_id) {
        const cityData = await queryInterface.sequelize.query('SELECT name FROM "towns" WHERE id = ' + user.town_id, {
          type: queryInterface.sequelize.QueryTypes.SELECT,
        });

        if (cityData.length > 0) {
          city = cityData[0].name;
        }
      }

      /* let country = (user?.country) ? user.country.toString() : null;
      let county_id= (user?.county_id) ? user.county_id.toString() : null;
      let town_id= (user?.town_id) ? user.town_id.toString() : null; */

      await queryInterface.sequelize.query(`UPDATE "clinical_users" SET
        "country" = '${country}',
        "county_id" = '${state}',
        "town_id" = '${city}'
        WHERE "id" = ${user.id}`);
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Define the down migration if needed
    await queryInterface.changeColumn('clinical_users', 'country', {
      type: Sequelize.INTEGER,
    });
    await queryInterface.changeColumn('clinical_users', 'county_id', {
      type: Sequelize.INTEGER,
    });
    await queryInterface.changeColumn('clinical_users', 'town_id', {
      type: Sequelize.INTEGER,
    });
  },
};
