'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Remove the existing foreign key constraint
    await queryInterface.removeConstraint('tasks', 'tasks_assigned_to_fkey');
    // Add a new foreign key constraint with desired options
    await queryInterface.addConstraint('tasks', {
      fields: ['assigned_to'],
      type: 'foreign key',
      name: 'tasks_assigned_to_fkey', // Name of the constraint
      references: {
        table: 'clinical_users',
        field: 'id',
      },
      onDelete: 'CASCADE',  // Use 'CASCADE' or 'SET NULL' based on your requirement
      onUpdate: 'CASCADE',
    });

    // Drop the existing foreign key constraint
    await queryInterface.removeConstraint('task_assignments', 'task_assignments_staff_id_fkey');

    // Recreate the foreign key with CASCADE options
    await queryInterface.addConstraint('task_assignments', {
      fields: ['staff_id'],
      type: 'foreign key',
      name: 'task_assignments_staff_id_fkey',
      references: {
        table: 'clinical_users',
        field: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    // Drop the existing foreign key constraint
    await queryInterface.removeConstraint('task_assignments', 'task_assignments_task_id_fkey');

    // Recreate the foreign key with CASCADE options
    await queryInterface.addConstraint('task_assignments', {
      fields: ['staff_id'],
      type: 'foreign key',
      name: 'task_assignments_task_id_fkey',
      references: {
        table: 'clinical_users',
        field: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  },

  async down (queryInterface, Sequelize) {
    // Remove the new foreign key constraint
    await queryInterface.removeConstraint('tasks', 'tasks_assigned_to_fkey');

    // Optionally, you can add the original foreign key constraint back if needed
    await queryInterface.addConstraint('tasks', {
      fields: ['assigned_to'],
      type: 'foreign key',
      name: 'tasks_assigned_to_fkey', // Name of the constraint
      references: {
        table: 'clinical_users',
        field: 'id',
      },
      onDelete: 'RESTRICT', // Original behavior
      onUpdate: 'RESTRICT',
    });

    // Optionally, revert the changes by dropping the new constraint and re-adding the old one without CASCADE
    await queryInterface.removeConstraint('task_assignments', 'task_assignments_staff_id_fkey');
    await queryInterface.addConstraint('task_assignments', {
      fields: ['staff_id'],
      type: 'foreign key',
      name: 'task_assignments_staff_id_fkey',
      references: {
        table: 'clinical_users',
        field: 'id',
      },
      // Without CASCADE
      onDelete: 'RESTRICT', // Original behavior
      onUpdate: 'RESTRICT',
    });

    // Optionally, revert the changes by dropping the new constraint and re-adding the old one without CASCADE
    await queryInterface.removeConstraint('task_assignments', 'task_assignments_task_id_fkey');
    await queryInterface.addConstraint('task_assignments', {
      fields: ['staff_id'],
      type: 'foreign key',
      name: 'task_assignments_task_id_fkey',
      references: {
        table: 'clinical_users',
        field: 'id',
      },
      // Without CASCADE
      onDelete: 'RESTRICT', // Original behavior
      onUpdate: 'RESTRICT',
    });
  }
};
