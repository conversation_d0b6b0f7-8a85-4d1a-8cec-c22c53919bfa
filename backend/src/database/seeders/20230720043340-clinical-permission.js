'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
     */
    return queryInterface.sequelize.transaction(async t => {
      let data = await queryInterface.sequelize.query(
        'SELECT count(*)::int as count FROM clinical_permissions',
        {
          type: queryInterface.sequelize.QueryTypes.SELECT,
          transaction: t,
        }
      )
      console.log(` ~~~ ${data}  ~~~`)
      if (data && data[0].count === 0) {
        let _clinical_permissionsData = [
          {
            section: 'User Management',
            name: 'User-Add',
            label: 'Create',
            description: 'User-Add',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'User Management',
            name: 'User-<PERSON>',
            label: 'Edit',
            description: 'User-Edit',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'User Management',
            name: 'User-View',
            description: 'User-View',
            label: 'View',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'User Management',
            name: 'User-Delete',
            label: 'Delete',
            description: 'User-Delete',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Settings',
            name: 'Setting-Add',
            label: 'Create',
            description: 'Setting-Add',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Settings',
            name: 'Setting-Edit',
            label: 'Edit',
            description: 'Setting-Edit',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Settings',
            name: 'Setting-View',
            label: 'View',
            description: 'Setting-View',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Settings',
            name: 'Setting-Delete',
            label: 'Delete',
            description: 'Setting-Delete',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Dashboard',
            name: 'Dashboard-Add',
            label: 'Create',
            description: 'Dashboard-Add',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Dashboard',
            name: 'Dashboard-Edit',
            label: 'Edit',
            description: 'Dashboard-Edit',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Dashboard',
            name: 'Dashboard-View',
            label: 'View',
            description: 'Dashboard-View',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Dashboard',
            name: 'Dashboard-Delete',
            label: 'Delete',
            description: 'Dashboard-Delete',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Patient Management',
            name: 'Patient-Add',
            description: 'Patient-Add',
            label: 'Create',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Patient Management',
            name: 'Patient-Edit',
            label: 'Edit',
            description: 'Patient-Edit',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Patient Management',
            name: 'Patient-View',
            label: 'View',
            description: 'Patient-View',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Patient Management',
            name: 'Patient-Delete',
            label: 'Delete',
            description: 'Patient-Delete',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Clinical Note',
            name: 'Clinical-Note-Add',
            description: 'Clinical Note-Add',
            label: 'Create',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Clinical Note',
            name: 'Clinical-Note-Edit',
            label: 'Edit',
            description: 'Clinical Note-Edit',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Clinical Note',
            name: 'Clinical-Note-View',
            label: 'View',
            description: 'Clinical Note-View',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Clinical Note',
            name: 'Clinical-Note-Delete',
            label: 'Delete',
            description: 'Clinical Note-Delete',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Letters',
            name: 'Referral-Letter-Add',
            label: 'Create',
            description: 'Referral Letter-Add',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Letters',
            name: 'Referral-Letter-Edit',
            label: 'Edit',
            description: 'Referral Letter-Edit',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Letters',
            name: 'Referral-Letter-View',
            label: 'View',
            description: 'Referral Letter-View',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Letters',
            name: 'Referral-Letter-Delete',
            label: 'Delete',
            description: 'Referral Letter-Delete',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Adhoc Letter',
            name: 'Adhoc-Letter-Add',
            label: 'Create',
            description: 'Adhoc Letter-Add',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Adhoc Letter',
            name: 'Adhoc-Letter-Edit',
            label: 'Edit',
            description: 'Adhoc Letter-Edit',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Adhoc Letter',
            name: 'Adhoc-Letter-View',
            label: 'View',
            description: 'Adhoc Letter-View',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Adhoc Letter',
            name: 'Adhoc-Letter-Delete',
            label: 'Delete',
            description: 'Adhoc Letter-Delete',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
        ]
        await queryInterface.bulkInsert('clinical_permissions', _clinical_permissionsData, {
          transaction: t,
        })
        console.log(
          ` ~~~ ${_clinical_permissionsData.length} records inserted for _clinical_permissionsData ~~~`
        )
      }
    })
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    return queryInterface.bulkDelete('clinical_permissions', null, {})
  },
}
