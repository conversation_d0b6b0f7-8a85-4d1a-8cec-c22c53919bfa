'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
    */
    // Check if permission with section 'User Management' exists
    const userManagementPermission = await queryInterface.rawSelect('clinical_permissions', {
          where: {
            section: 'Tasks'
          },
        }, ['id']);
    console.log(userManagementPermission);
    // If the permission exists, add the new 'Task' permission
    if (!userManagementPermission) {
      let _clinical_permissionsData = [
        {
          section: 'Tasks',
          name: 'Task-Add',
          label: 'Create',
          description: 'Task-Add',
          status: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          section: 'Tasks',
          name: 'Task-Edit',
          label: 'Edit',
          description: 'Task-Edit',
          status: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          section: 'Tasks',
          name: 'Task-View',
          description: 'Task-View',
          label: 'View',
          status: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          section: 'Tasks',
          name: 'Task-Delete',
          label: 'Delete',
          description: 'Task-Delete',
          status: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          section: 'Designs',
          name: 'Design-Add',
          label: 'Create',
          description: 'Design-Add',
          status: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          section: 'Designs',
          name: 'Design-Edit',
          label: 'Edit',
          description: 'Design-Edit',
          status: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          section: 'Designs',
          name: 'Design-View',
          description: 'Design-View',
          label: 'View',
          status: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          section: 'Designs',
          name: 'Design-Delete',
          label: 'Delete',
          description: 'Design-Delete',
          status: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ]
      await queryInterface.bulkInsert('clinical_permissions', _clinical_permissionsData, {});
    }
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    // Remove the 'Tasks' permission in the down function
    await queryInterface.bulkDelete('clinical_permissions', {
      section: 'Tasks'
    }, {});
    // Remove the 'Designs' permission in the down function
    await queryInterface.bulkDelete('clinical_permissions', {
      section: 'Designs'
    }, {});
  }
};
