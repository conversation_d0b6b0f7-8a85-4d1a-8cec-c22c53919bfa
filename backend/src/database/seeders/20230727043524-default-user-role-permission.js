'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
     */
    return queryInterface.sequelize.transaction(async t => {
      let data = await queryInterface.sequelize.query(
        "SELECT count(*)::int as count FROM clinical_default_permissions where name='DEFAULT_USER-ROLE_PERMISSION'",
        {
          type: queryInterface.sequelize.QueryTypes.SELECT,
          transaction: t,
        }
      )
      console.log(` ~~~ ${data}  ~~~`)
      if (data && data[0].count === 0) {
        let permissinData = await queryInterface.sequelize.query(
          "SELECT * FROM clinical_permissions where name IN ('Patient-Add','Patient-Edit','Patient-View','Patient-Delete','Setting-Add','Setting-Edit','Setting-View','Setting-Delete','Dashboard-View','Clinical-Note-Add','Clinical-Note-Edit','Clinical-Note-View','Clinical-Note-Delete','Referral-Letter-Add','Referral-Letter-Edit','Referral-Letter-View','Referral-Letter-Delete','Adhoc-Letter-Add','Adhoc-Letter-Edit','Adhoc-Letter-View','Adhoc-Letter-Delete')",
          {
            type: queryInterface.sequelize.QueryTypes.SELECT,
            transaction: t,
          }
        )
        if (permissinData.length > 0) {
          const permissionArray = []
          for (const permission of permissinData) {
            permissionArray.push(permission.id)
          }
          let insertData = [
            {
              name: 'DEFAULT_USER-ROLE_PERMISSION',
              permissions: permissionArray,
              status: 1,
              created_at: new Date(),
              updated_at: new Date(),
            },
          ]
          await queryInterface.bulkInsert('clinical_default_permissions', insertData, {
            transaction: t,
          })
          console.log(
            ` ~~~ ${insertData.length} records inserted for clinical_default_permissions ~~~`
          )
        }
      }
    })
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    await queryInterface.dropTable('clinical_default_permissions')
  },
}
