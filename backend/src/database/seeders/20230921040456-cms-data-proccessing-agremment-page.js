'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
     */
    return queryInterface.sequelize.transaction(async t => {
      let [data] = await queryInterface.sequelize.query(
        'SELECT * FROM system_cms_pages where label IN(:label)',
        {
          replacements: {
            label: ['DATA_PROCESSING_AGREMENT'],
          },
          transaction: t,
        }
      )

      console.log(` ~~~ ${data}  ~~~`)
      if (data.length === 0) {
        let _clinical_cms = [
          {
            title: 'Data Processing Agreement',
            label: 'DATA_PROCESSING_AGREMENT',
            content:
              "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
        ]
        await queryInterface.bulkInsert('system_cms_pages', _clinical_cms, {
          transaction: t,
        })
        console.log(` ~~~ ${_clinical_cms.length} records inserted for _clinical_cms ~~~`)
      }
    })
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    return queryInterface.bulkDelete('system_cms_pages', null, {})
  },
}
