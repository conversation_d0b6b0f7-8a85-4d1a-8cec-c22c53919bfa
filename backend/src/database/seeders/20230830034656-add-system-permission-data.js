'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
     */
    return queryInterface.sequelize.transaction(async t => {
      let data = await queryInterface.sequelize.query(
        'SELECT count(*)::int as count FROM system_permissions',
        {
          type: queryInterface.sequelize.QueryTypes.SELECT,
          transaction: t,
        }
      )
      console.log(` ~~~ ${data}  ~~~`)
      if (data && data[0].count === 0) {
        let _system_permissionsData = [
          {
            section: 'User Management',
            name: 'USER-ADD',
            label: 'Create',
            description: 'User Add',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'User Management',
            name: 'USER-EDIT',
            label: 'Edit',
            description: 'User Edit',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'User Management',
            name: 'USER-VIEW',
            description: 'User-View',
            label: 'View',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Settings',
            name: 'SETTINGS-EDIT',
            label: 'Edit',
            description: 'Setting Edit',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Settings',
            name: 'SETTINGS-VIEW',
            label: 'View',
            description: 'Setting-View',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'CMS Pages',
            name: 'CMS-EDIT',
            label: 'Edit',
            description: 'CMS-Edit',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'CMS Pages',
            name: 'CMS-VIEW',
            label: 'View',
            description: 'CMS View',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Account Owner',
            name: 'ACCOUNT-OWNER-ADD',
            description: 'Account Owner Add',
            label: 'Create',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Account Owner',
            name: 'ACCOUNT-OWNER-EDIT',
            label: 'Edit',
            description: 'Account Owner Edit',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Account Owner',
            name: 'ACCOUNT-OWNER-VIEW',
            label: 'View',
            description: 'Account Owner View',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Account Owner',
            name: 'ACCOUNT-OWNER-DELETE',
            label: 'Delete',
            description: 'Account Owner Delete',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            section: 'Account Owner',
            name: 'ACCOUNT-OWNER-APPROVE',
            label: 'Approve',
            description: 'Account Owner Approve',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },

        ]
        await queryInterface.bulkInsert('system_permissions', _system_permissionsData, {
          transaction: t,
        })
        console.log(
          ` ~~~ ${_system_permissionsData.length} records inserted for system_permissionsdata ~~~`
        )
      }
    })
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    return queryInterface.bulkDelete('system_permissions', null, {})
  },
}
