'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
    */
    return queryInterface.sequelize.transaction(async (t) => {
      let data = await queryInterface.sequelize.query('SELECT count(*)::int as count FROM clinical_global_settings', {
        type: queryInterface.sequelize.QueryTypes.SELECT,
        transaction: t
      })
      if (data && data[0].count === 0) {
        let _insertData = [{
          "setting_data": {
            "header_deafult_data": '<table align="left" border="0" cellpadding="0" cellspacing="0" style="width:100%"><tbody><tr><td align="left"><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td style="width:152px"><img alt="Logo" src="assets/images/dummy-logo.png" style="height:100px; width:150px" /></td><td><CLINIC_NAME></td></tr></tbody></table></td><td align="middle"><table cellpadding="0" cellspacing="0" style="text-align:right; width: 100%;"><tbody><tr><td>Mobile: <PHONE></td></tr><tr><td>Email: <EMAIL></td></tr></tbody></table></td></tr></tbody></table>',
            "footer_default_data": '<p>Yours sincerely,</p><p><strong>Dictated but not signed to avoid delay</strong></p><p><img alt="Logo" src="assets/images/dummy-signature.png" style="height:50px; width:100px" /></p><p><strong>Doctor Signature</strong></p>'
          },
          "status": 1,
          "created_at": new Date(),
          "updated_at": new Date()
        }]
        await queryInterface.bulkInsert('clinical_global_settings', _insertData, {
          transaction: t
        }, {
          setting_data: { type: new Sequelize.JSON() }
        });
        console.log(` ~~~ ${_insertData.length} records inserted for clinical_global_settings ~~~`)
      }
    })
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    return queryInterface.bulkDelete('clinical_global_settings', null, {});
  }
};
