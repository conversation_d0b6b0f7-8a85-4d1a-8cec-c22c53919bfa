'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const permissionsToAdd = [
      {
        section: 'Monitor Analytic Dashboard',
        name: 'MONITOR-ANALYTICS-VIEW',
        label: 'View',
        description: 'Monitor Analytic Dashboard view',
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        section: 'Customer Support Tickets',
        name: 'CUSTOMER-SUPPORT-VIEW',
        label: 'View',
        description: 'Handle Customer Support Tickets view',
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        section: 'Subscription Plan Edit',
        name: 'SUBSCRIPTION-PLAN-EDIT',
        label: 'Edit',
        description: 'Edit Subscription Plan',
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        section: 'Subscription Plan View',
        name: 'SUBSCRIPTION-PLAN-VIEW',
        label: 'View',
        description: 'View Subscription Plan',
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        section: 'Billing',
        name: 'BILLING-DETAIL-VIEW',
        label: 'View',
        description: 'Access Billing Detail view',
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        section: 'Team Management Edit',
        name: 'TEAM-ACCESS-EDIT',
        label: 'Edit',
        description: 'Manage Team Access edit',
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        section: 'Team Management View',
        name: 'TEAM-ACCESS-VIEW',
        label: 'View',
        description: 'Manage Team Access view ',
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        section: 'System Configurations Edit',
        name: 'SYSTEM-CONFIG-EDIT',
        label: 'Edit',
        description: 'Manage System-Level Configurations Edit',
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        section: 'System Configurations View',
        name: 'SYSTEM-CONFIG-VIEW',
        label: 'View',
        description: 'Manage System-Level Configurations View',
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        section: 'Security & Financial',
        name: 'SENSITIVE-SETTINGS-VIEW',
        label: 'View',
        description: 'View Sensitive Security and Financial Settings',
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];

    // Check existing permissions
    const existingPermissions = await queryInterface.sequelize.query(
      'SELECT name FROM system_permissions WHERE name IN (:permissionNames)',
      {
        replacements: { 
          permissionNames: permissionsToAdd.map(p => p.name) 
        },
        type: queryInterface.sequelize.QueryTypes.SELECT
      }
    );

    const existingPermissionNames = existingPermissions.map(p => p.name);
    
    // Filter out permissions that already exist
    const newPermissions = permissionsToAdd.filter(
      permission => !existingPermissionNames.includes(permission.name)
    );

    if (newPermissions.length > 0) {
      return queryInterface.bulkInsert('system_permissions', newPermissions);
    }
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete('system_permissions', {
      name: [
        'MONITOR-ANALYTICS-VIEW',
        'CUSTOMER-SUPPORT-VIEW',
        'SUBSCRIPTION-PLAN-VIEW',
        'SUBSCRIPTION-PLAN-EDIT',
        'BILLING-DETAIL-VIEW',
        'TEAM-ACCESS-VIEW',
        'TEAM-ACCESS-EDIT',
        'SYSTEM-CONFIG-EDIT',
        'SYSTEM-CONFIG-VIEW',
        'SENSITIVE-SETTINGS-VIEW',
      ],
    });
  },
};