'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
     */
    return queryInterface.sequelize.transaction(async t => {
      let data = await queryInterface.sequelize.query(
        'SELECT count(*)::int as count FROM clinical_specializations',
        {
          type: queryInterface.sequelize.QueryTypes.SELECT,
          transaction: t,
        }
      )
      console.log(` ~~~ ${data}  ~~~`)
      if (data && data[0].count === 0) {
        let _clinicalSpecializationsData = [
          {
            name: 'Acupuncture',
            description: 'Acupuncture',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Adult and paediatric services',
            description: 'Adult and paediatric services',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Aesthetic medicine',
            description: 'Aesthetic medicine',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Allergy & immunology',
            description: 'Allergy & immunology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Anaesthesiology',
            description: 'Anaesthesiology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Andrology',
            description: 'Andrology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Anti-ageing',
            description: 'Anti-ageing',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Audiovestibular medicine',
            description: 'Audiovestibular medicine',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Cardiology',
            description: 'Cardiology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Cardiothoracic surgery',
            description: 'Cardiothoracic surgery',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Child & adolescent psychiatry',
            description: 'Child & adolescent psychiatry',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Clinical genetics',
            description: 'Clinical genetics',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Clinical oncology',
            description: 'Clinical oncology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Clinical pharmacology and therapeutics',
            description: 'Clinical pharmacology and therapeutics',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Colorectal surgery',
            description: 'Colorectal surgery',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Dentistry',
            description: 'Dentistry',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Dermatology',
            description: 'Dermatology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Diagnostic imaging',
            description: 'Diagnostic imaging',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Endocrinology, diabetes & metabolism',
            description: 'Endocrinology, diabetes & metabolism',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Endodontics',
            description: 'Endodontics',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Fertility specialist',
            description: 'Fertility specialist',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Gastroenterology',
            description: 'Gastroenterology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Genitourinary Medicine',
            description: 'Genitourinary Medicine',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Geriatrics',
            description: 'Geriatrics',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'GP (General Practitioner)',
            description: 'GP (General Practitioner)',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Haematology',
            description: 'Haematology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Healthcare professionals',
            description: 'Healthcare professionals',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Hepatology (liver specialist)',
            description: 'Hepatology (liver specialist)',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Hyperbaric oxygen therapy',
            description: 'Hyperbaric oxygen therapy',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Immunology',
            description: 'Immunology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Intensive care medicine',
            description: 'Intensive care medicine',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Internal medicine',
            description: 'Internal medicine',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Interventional radiology',
            description: 'Interventional radiology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Mastology',
            description: 'Mastology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Medical oncology',
            description: 'Medical oncology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Nephrology',
            description: 'Nephrology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Neurology',
            description: 'Neurology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Neurophysiology',
            description: 'Neurophysiology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Neurosurgery',
            description: 'Neurosurgery',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Nuclear medicine',
            description: 'Nuclear medicine',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Nutrition & dietetics',
            description: 'Nutrition & dietetics',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Obstetrics & gynaecology',
            description: 'Obstetrics & gynaecology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Ophthalmology',
            description: 'Ophthalmology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Optometry',
            description: 'Optometry',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Oral & maxillofacial surgery',
            description: 'Oral & maxillofacial surgery',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Orthodontics',
            description: 'Orthodontics',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Orthopaedic surgery',
            description: 'Orthopaedic surgery',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Osteopathy',
            description: 'Osteopathy',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Otolaryngology / ENT',
            description: 'Otolaryngology / ENT',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Ozone therapy',
            description: 'Ozone therapy',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Paediatric allergy & immunology',
            description: 'Paediatric allergy & immunology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Paediatric cardiology',
            description: 'Paediatric cardiology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Paediatric cardiothoracic surgery',
            description: 'Paediatric cardiothoracic surgery',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Paediatric dermatology',
            description: 'Paediatric dermatology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Paediatric endocrinology, diabetes & metabolism',
            description: 'Paediatric endocrinology, diabetes & metabolism',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Paediatric gastroenterology',
            description: 'Paediatric gastroenterology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Paediatric nephrology',
            description: 'Paediatric nephrology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Paediatric neurology',
            description: 'Paediatric neurology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Paediatric ophthalmology',
            description: 'Paediatric ophthalmology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Paediatric orthopaedics',
            description: 'Paediatric orthopaedics',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Paediatric otolaryngology',
            description: 'Paediatric otolaryngology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Paediatric respiratory medicine',
            description: 'Paediatric respiratory medicine',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Paediatric surgery',
            description: 'Paediatric surgery',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Paediatric urology',
            description: 'Paediatric urology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Paediatrics',
            description: 'Paediatrics',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Pain medicine',
            description: 'Pain medicine',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Pathology',
            description: 'Pathology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Periodontology',
            description: 'Periodontology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Physical medicine & rehabilitation',
            description: 'Physical medicine & rehabilitation',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Physiotherapy',
            description: 'Physiotherapy',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Plastic surgery',
            description: 'Plastic surgery',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Podiatry',
            description: 'Podiatry',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Psychiatry',
            description: 'Psychiatry',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Psychology',
            description: 'Psychology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Pulmonology & respiratory medicine',
            description: 'Pulmonology & respiratory medicine',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Radiology',
            description: 'Radiology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Rheumatology',
            description: 'Rheumatology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Speech therapy',
            description: 'Speech therapy',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Sports medicine',
            description: 'Sports medicine',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Surgery',
            description: 'Surgery',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Urology',
            description: 'Urology',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            name: 'Vascular surgery',
            description: 'Vascular surgery',
            status: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
        ]
        await queryInterface.bulkInsert('clinical_specializations', _clinicalSpecializationsData, {
          transaction: t,
        })
        console.log(
          ` ~~~ ${_clinicalSpecializationsData.length} records inserted for clinical_roles ~~~`
        )
      }
    })
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    return queryInterface.bulkDelete('clinical_specializations', null, {})
  },
}
