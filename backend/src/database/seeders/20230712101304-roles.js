'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
     */
    return queryInterface.sequelize.transaction(async t => {
      let data = await queryInterface.sequelize.query(
        'SELECT count(*)::int as count FROM clinical_roles',
        {
          type: queryInterface.sequelize.QueryTypes.SELECT,
          transaction: t,
        }
      )
      console.log(` ~~~ ${data}  ~~~`)
      if (data && data[0].count === 0) {
        let _clinicalRolesData = [
          {
            status: 1,
            name: 'Account Owner',
            description: 'Account Owner',
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            status: 1,
            name: 'Clinician',
            description: 'clinician / Doctor',
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            status: 1,
            name: 'Staff Member',
            description: 'Staff Member',
            created_at: new Date(),
            updated_at: new Date(),
          },
        ]
        await queryInterface.bulkInsert('clinical_roles', _clinicalRolesData, { transaction: t })
        console.log(` ~~~ ${_clinicalRolesData.length} records inserted for clinical_roles ~~~`)
      }
    })
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    return queryInterface.bulkDelete('clinical_roles', null, {})
  },
}
