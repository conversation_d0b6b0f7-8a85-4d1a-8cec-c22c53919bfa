'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Check if permission with section 'Templates' exists
    const templatePermissionExists = await queryInterface.rawSelect('clinical_permissions', {
      where: {
        section: 'Templates'
      },
    }, ['id']);

    // If the 'Templates' permission does not exist, add the new permissions
    if (!templatePermissionExists) {
      let templatePermissions = [
        {
          section: 'Templates',
          name: 'Template-Add',
          label: 'Create',
          description: 'Permission to add templates',
          status: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          section: 'Templates',
          name: 'Template-Edit',
          label: 'Edit',
          description: 'Permission to edit templates',
          status: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          section: 'Templates',
          name: 'Template-View',
          label: 'View',
          description: 'Permission to view templates',
          status: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          section: 'Templates',
          name: 'Template-Delete',
          label: 'Delete',
          description: 'Permission to delete templates',
          status: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert('clinical_permissions', templatePermissions, {});
    }
  },

  async down (queryInterface, Sequelize) {
    // Remove only the 'Templates' permissions in the down function
    await queryInterface.bulkDelete('clinical_permissions', {
      section: 'Templates'
    }, {});
  }
};
