DO $$
DECLARE
    starter_plan RECORD;
BEGIN
    -- First, get the starter plan details
    SELECT * INTO starter_plan 
    FROM subscription_plans 
    WHERE id = 1;  -- Starter plan ID

    -- Update all clinical users with starter plan and its limits
    UPDATE clinical_users 
    SET 
        subscription_plan_id = 1,  -- Starter plan ID
        remaining_custom_templates = starter_plan.custom_templates_limit,
        remaining_audio_consultation_minutes = starter_plan.audio_consultation_minutes * 60, -- Convert to seconds
        remaining_design_templates = starter_plan.design_templates_limit,
        allows_team_members = starter_plan.allows_team_members,
        has_tasks = starter_plan.has_tasks,
        has_user_logs = starter_plan.has_user_logs,
        has_medical_history = starter_plan.has_medical_history,
        has_invite_user = starter_plan.has_invite_user,
        subscription_status = 1  -- Set as active
    WHERE 
        status = 1  -- Only update active users
        AND deleted_at IS NULL;  -- Exclude deleted users

    -- Log the number of users updated
    RAISE NOTICE 'Updated subscription plan for % users', (
        SELECT COUNT(*) 
        FROM clinical_users 
        WHERE subscription_plan_id = 1 
        AND status = 1 
        AND deleted_at IS NULL
    );
END $$;