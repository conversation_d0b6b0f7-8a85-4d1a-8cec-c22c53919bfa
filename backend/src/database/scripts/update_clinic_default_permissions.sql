DO $$
DECLARE
    users RECORD;
    permissionIdsToAdd INTEGER[] := ARRAY[37,38,39,40];
    newPermissionId INTEGER;
	existingPermission INTEGER;
    alreadyPresent BOOLEAN;
BEGIN
    -- Loop through each row in the clinical_user_permissions table
    FOR users IN
        SELECT id, permissions
        FROM clinical_default_user_role_permissions
    LOOP
        -- Loop through each permissionId to add
        FOREACH newPermissionId IN ARRAY permissionIdsToAdd
        LOOP
            -- Check if the newPermissionId is already present in the user's permission_id array
            alreadyPresent := FALSE;
            IF users.permissions IS NOT NULL THEN
                FOREACH existingPermission IN ARRAY users.permissions
                LOOP
                    IF existingPermission = newPermissionId THEN
                        alreadyPresent := TRUE;
                        EXIT;
                    END IF;
                END LOOP;
            END IF;

            -- If the newPermissionId is not already present, append it to the array
            IF NOT alreadyPresent THEN
                users.permissions := COALESCE(users.permissions, ARRAY[]::INTEGER[]) || newPermissionId;
            END IF;
        END LOOP;

        -- Update the row with the new permission_id array
        UPDATE clinical_default_user_role_permissions
        SET permissions = users.permissions
        WHERE id = users.id;
    END LOOP;
END $$;
