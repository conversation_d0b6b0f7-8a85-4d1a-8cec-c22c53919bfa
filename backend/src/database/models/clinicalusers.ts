'use strict'
import { Model } from 'sequelize'
module.exports = (sequelize: any, DataTypes: any) => {
  class ClinicalUsers extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      const roleType: any = {
        name: 'role_id',
        type: DataTypes.INTEGER,
      }

      const permissionType: any = {
        name: 'id',
        type: DataTypes.INTEGER,
      }

      const clinicalUserRoleDetailType: any = {
        name: 'id',
        type: DataTypes.INTEGER,
      }

      const specializationsType: any = {
        name: 'clinical_specializations_id',
        type: DataTypes.INTEGER,
      }

      // Define associations here
      ClinicalUsers.hasMany(models.Task, {
        foreignKey: 'assigned_to',
        as: 'assignedTasks'
      });
      ClinicalUsers.hasMany(models.Task, {
        foreignKey: 'task_owner',
        as: 'ownedTasks'
      });
      ClinicalUsers.hasMany(models.Task, {
        foreignKey: 'last_modified_by',
        as: 'lastModifiedTasks'
      });

      //ClinicalUsers.belongsTo(models.ClinicalUserRoleDetail, { foreignKey: clinicalUserRoleDetailType, targetKey: 'user_id', as: 'user_role_detail' })
      ClinicalUsers.hasMany(models.ClinicalUserRoleDetail, { foreignKey: 'user_id', as: 'user_role_detail' })

      ClinicalUsers.belongsTo(models.ClinicalRoles, {
        foreignKey: roleType,
        targetKey: 'id',
        as: 'role',
        onDelete: 'CASCADE',
      })

      ClinicalUsers.belongsTo(models.ClinicalUserPermissions, {
        foreignKey: permissionType,
        targetKey: 'user_id',
        as: 'permissions',
        onDelete: 'CASCADE',
      })

      ClinicalUsers.belongsTo(models.ClinicalSpecialization, {
        foreignKey: specializationsType,
        targetKey: 'id',
        as: 'specializations',
      })

      ClinicalUsers.belongsTo(models.SubscriptionPlan, {
        foreignKey: 'subscription_plan_id',
        as: 'subscriptionPlan'
      });
    }
  }
  ClinicalUsers.init(
    {
      role_id: DataTypes.INTEGER,
      user_type: DataTypes.ENUM('Individual', 'Clinic'),
      reg_gmc_no: DataTypes.STRING,
      clinical_specializations_id: DataTypes.INTEGER,
      first_name: DataTypes.STRING,
      last_name: DataTypes.STRING,
      country: DataTypes.STRING,
      county_id: DataTypes.STRING,
      town_id: DataTypes.STRING,
      address: DataTypes.STRING,
      pincode: DataTypes.STRING,
      phone: DataTypes.STRING,
      email: DataTypes.STRING,
      profile_image_url: DataTypes.STRING,
      is_email_verified: DataTypes.BOOLEAN,
      status: DataTypes.INTEGER,
      owner_user_id: DataTypes.INTEGER,
      others: DataTypes.STRING,
      credit_documents: DataTypes.INTEGER,
      subscription_documents: DataTypes.INTEGER,
      stripe_customer_id: DataTypes.STRING,
      subscription_dictation_minutes: DataTypes.INTEGER,
      subscription_team_members: DataTypes.INTEGER,
      subscription_additional_document_price: DataTypes.DECIMAL(10, 2),
      subscription_currency: DataTypes.STRING, // Fixed capitalization
      subscription_status: DataTypes.INTEGER, // Fixed capitalization
      audio_minutes_used_in_seconds:DataTypes.INTEGER,
      subscription_plan_id: {
        type: DataTypes.INTEGER,
        references: {
          model: 'SubscriptionPlans',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      remaining_custom_templates: DataTypes.INTEGER,
      remaining_audio_consultation_minutes: DataTypes.INTEGER,
      remaining_design_templates: DataTypes.INTEGER,
    },
    {
      sequelize,
      modelName: 'ClinicalUsers',
      underscored: true,
      timestamps: true // Ensures Sequelize manages `created_at` and `updated_at`
    }
  );

  return ClinicalUsers;
};
