'use strict'
import { Model } from 'sequelize'
module.exports = (sequelize: any, DataTypes: any) => {
  class ClinicalUserBuyCredit extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  ClinicalUserBuyCredit.init({
    user_id: DataTypes.INTEGER,
    name: DataTypes.STRING,
    unit_price: DataTypes.DECIMAL,
    quantity: DataTypes.INTEGER,
    currency: DataTypes.STRING,
    total_amount: DataTypes.DECIMAL,
    payment_method_id: DataTypes.STRING,
    stripe_customer_id: DataTypes.STRING,
    billing_address_info: DataTypes.JSON,
    status: DataTypes.INTEGER
  }, {
    sequelize,
    modelName: 'ClinicalUserBuyCredit',
    underscored: true,
  });
  return ClinicalUserBuyCredit;
};