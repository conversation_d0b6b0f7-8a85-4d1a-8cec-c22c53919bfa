'use strict'
import { Model } from 'sequelize'
module.exports = (sequelize: any, DataTypes: any) => {
  class ClinicalUserLlmPrompt extends Model {
    static associate(models) {
      ClinicalUserLlmPrompt.belongsTo(models.ClinicalUsers, {
        foreignKey: 'user_id',
        as: 'user'
      });
      ClinicalUserLlmPrompt.belongsTo(models.Clinics, {
        foreignKey: 'clinic_id',
        as: 'clinic'
      });
    }
  }

  ClinicalUserLlmPrompt.init(
    {
      user_id: DataTypes.INTEGER,
      clinic_id: DataTypes.UUID,
      name: DataTypes.STRING,
      description: DataTypes.STRING,
      prompt: DataTypes.TEXT,
      is_favourite: DataTypes.SMALLINT,
      status: DataTypes.SMALLINT,
    },
    {
      sequelize,
      modelName: 'ClinicalUserLlmPrompts',
      underscored: true,
    }
  )
  return ClinicalUserLlmPrompt
}
