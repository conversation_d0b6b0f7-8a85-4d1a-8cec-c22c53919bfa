'use strict'
import { Model } from 'sequelize'
module.exports = (sequelize: any, DataTypes: any) => {
  class Otps extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      Otps.belongsTo(models.SystemUsers, { foreignKey: 'user_id', as: 'adminuser' });

      Otps.belongsTo(models.ClinicalUsers, { foreignKey: 'user_id', as: 'user' });
    }
  }
  Otps.init({
    user_id: DataTypes.INTEGER,
    email: DataTypes.STRING,
    otp: DataTypes.INTEGER,
    valid_upto: DataTypes.DATE,
    status: DataTypes.INTEGER
  }, {
    sequelize,
    modelName: 'Otps',
    underscored: true,
  });
  return Otps;
};