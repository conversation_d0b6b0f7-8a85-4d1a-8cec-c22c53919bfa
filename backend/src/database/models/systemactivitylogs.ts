'use strict'
import { Model } from 'sequelize'
module.exports = (sequelize: any, DataTypes: any) => {
  class SystemActivityLogs extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  SystemActivityLogs.init(
    {
      user_id: DataTypes.INTEGER,
      activity_name: DataTypes.STRING,
      action_name: DataTypes.STRING,
      action_on: DataTypes.STRING,
      status: DataTypes.INTEGER,
    },
    {
      sequelize,
      modelName: 'SystemActivityLogs',
      underscored: true,
    }
  )
  return SystemActivityLogs
}
