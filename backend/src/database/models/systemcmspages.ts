'use strict'
import { Model } from 'sequelize'
module.exports = (sequelize: any, DataTypes: any) => {
  class SystemCmsPages extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  SystemCmsPages.init(
    {
      title: DataTypes.STRING,
      label: DataTypes.STRING,
      content: DataTypes.TEXT,
      status: DataTypes.INTEGER,
    },
    {
      sequelize,
      modelName: 'SystemCmsPages',
      underscored: true,
    }
  )
  return SystemCmsPages
}
