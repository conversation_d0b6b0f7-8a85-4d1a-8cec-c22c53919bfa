'use strict'
import { Model } from 'sequelize'
module.exports = (sequelize: any, DataTypes: any) => {
  class ClinicalDefaultUserRolePermissions extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  ClinicalDefaultUserRolePermissions.init(
    {
      clinic_id: DataTypes.UUID,
      role_id: DataTypes.INTEGER,
      user_id: DataTypes.INTEGER,
      role_descriptions: DataTypes.STRING,
      permissions: DataTypes.ARRAY(DataTypes.INTEGER),
      status: DataTypes.INTEGER,
    },
    {
      sequelize,
      modelName: 'ClinicalDefaultUserRolePermissions',
      underscored: true,
    }
  )
  return ClinicalDefaultUserRolePermissions
}
