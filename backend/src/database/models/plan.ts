'use strict'
import { Model } from 'sequelize'
module.exports = (sequelize: any, DataTypes: any) => {
  class SubscriptionPlan extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Association to prices table
      SubscriptionPlan.hasMany(models.SubscriptionPlanPrices, {
        foreignKey: 'subscription_plan_id',
        as: 'prices'
      });
      SubscriptionPlan.belongsTo(models.ClinicalUsers, {
        foreignKey: 'user_id',
        as: 'user'
      });
    }
  }
  SubscriptionPlan.init({
    name: DataTypes.STRING,
    description: DataTypes.STRING,
    price: DataTypes.DECIMAL(10, 2),
    additional_document_price: DataTypes.DECIMAL(10, 2),
    plan_type: DataTypes.ENUM('Monthly', 'Yearly'), // VARCHAR(10) instead of ENUM
    currency: DataTypes.ENUM('USD', 'GBP', 'EUR'),
    trial_period_days: DataTypes.INTEGER,
    free_trial_docs: DataTypes.INTEGER,
    custom_documents_price: DataTypes.DECIMAL(10, 2),
    custom_dictation_duration_minutes: DataTypes.INTEGER,
    custom_max_team_members: DataTypes.INTEGER,
    included_docs: DataTypes.INTEGER,
    dictation_limit_per_doc_min: DataTypes.INTEGER,
    max_team_members: DataTypes.INTEGER,
    free_support: DataTypes.BOOLEAN,
    cancel_anytime: DataTypes.BOOLEAN,
    priority_feature_access: DataTypes.BOOLEAN,
    stripe_price_id: {
      type: DataTypes.STRING,
      comment: 'Legacy field: now using SubscriptionPlanPrices for multiple price IDs'
    },
    status: DataTypes.INTEGER,
    // New fields for plan features:
    requires_card: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Indicates if card details are required during signup'
    },
    custom_templates_limit: {
      type: DataTypes.INTEGER,
      defaultValue: 5,
      comment: 'Maximum number of custom templates allowed'
    },
    audio_consultation_minutes: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: 'Included audio dictation/consultation minutes per month'
    },
    design_templates_limit: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: 'Number of design templates available'
    },
    has_invite_user: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Indicates if inviting users is allowed'
    },
    has_medical_history: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Indicates if access to medical history is enabled'
    },
    has_user_logs: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Indicates if detailed user logs are provided'
    },
    has_pre_made_templates: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Indicates if pre made templates are included'
    },
    has_tasks: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Indicates if task management features are included'
    },
    subscription_features: {
      type: DataTypes.JSON,
      defaultValue: {},
      comment: 'Indicates if subscription features are included'
    },
    allows_team_members: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Indicates if team members can be added (for Pro/Enterprise plans)'
    },
    is_custom: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Indicates if plan is custom (for Enterprise users)'
    },
    stripe_product_id: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Stripe product ID for this subscription plan'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'ID of the user associated with this subscription plan',
      references: {
        model: 'clinical_users', // ensure this matches your users table name
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    // New column for storing plan details
    plan_details: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Textual details of what is offered in this plan (e.g., features, benefits, etc.)'
    }
  }, {
    sequelize,
    modelName: 'SubscriptionPlan',
    underscored: true,
  });
  return SubscriptionPlan;
};
