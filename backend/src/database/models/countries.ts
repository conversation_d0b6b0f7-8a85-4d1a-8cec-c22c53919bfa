'use strict'
import { Model } from 'sequelize'
module.exports = (sequelize: any, DataTypes: any) => {
  class Countries extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  Countries.init({
    name: DataTypes.STRING,
    country_code: DataTypes.STRING,
    dial_code: DataTypes.STRING,
    currency_name: DataTypes.STRING,
    currency_symbol: DataTypes.STRING,
    currency_code: DataTypes.STRING,
    status: DataTypes.INTEGER
  }, {
    sequelize,
    modelName: 'Countries',
    tableName: 'country',
    underscored: true,
  });
  return Countries;
};