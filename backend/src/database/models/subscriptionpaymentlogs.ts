'use strict';
import { Model } from 'sequelize'

module.exports = (sequelize, DataTypes) => {
  class SubscriptionPaymentLogs extends Model {
    static associate(models) {
      SubscriptionPaymentLogs.belongsTo(models.ClinicalUserSubscriptions, {
        foreignKey: 'subscription_id',
        as: 'subscription',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      });
    }
  }

  SubscriptionPaymentLogs.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    subscription_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    stripe_invoice_id: {
      type: DataTypes.STRING,
    },
    payment_intent: {
      type: DataTypes.STRING,
    },
    failure_date: {
      type: DataTypes.DATE,
    },
    status: {
      type: DataTypes.STRING,
    },
    processed: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    warning_sent: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  }, {
    sequelize,
    modelName: 'SubscriptionPaymentLogs',
    tableName: 'subscription_payment_logs',
    underscored: true,
    timestamps: true,
  });

  return SubscriptionPaymentLogs;
};
