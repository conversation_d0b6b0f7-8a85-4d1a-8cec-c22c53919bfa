'use strict'
import { Model } from 'sequelize'
module.exports = (sequelize: any, DataTypes: any) => {
  class ClinicalUserRoleDetail extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      const type: any = {
        name: 'clinic_id',
        type: DataTypes.UUID,
      }

      ClinicalUserRoleDetail.belongsTo(models.Clinics, {
        foreignKey: type,
        targetKey: 'clinic_id',
        as: 'clinic',
      })

      const roleType: any = {
        name: 'role_id',
        type: DataTypes.INTEGER,
      }

      ClinicalUserRoleDetail.belongsTo(models.ClinicalRoles, {
        foreignKey: roleType,
        targetKey: 'id',
        as: 'roles',
      })
    }
  }
  ClinicalUserRoleDetail.init(
    {
      user_id: DataTypes.INTEGER,
      role_id: DataTypes.INTEGER,
      clinic_id: DataTypes.UUID,
      owner_user_id: DataTypes.INTEGER,
      user_type: DataTypes.ENUM('Individual', 'Clinic'),
      status: DataTypes.INTEGER,

    },
    {
      sequelize,
      modelName: 'ClinicalUserRoleDetail',
      underscored: true,
    }
  )
  return ClinicalUserRoleDetail
}
