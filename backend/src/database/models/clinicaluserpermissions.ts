'use strict'
import { Model } from 'sequelize'
module.exports = (sequelize: any, DataTypes: any) => {
  class ClinicalUserPermissions extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  ClinicalUserPermissions.init(
    {
      user_id: DataTypes.INTEGER,
      clinic_id: DataTypes.UUID,
      permission_id: DataTypes.ARRAY(DataTypes.INTEGER),
      status: DataTypes.INTEGER,
    },
    {
      sequelize,
      modelName: 'ClinicalUserPermissions',
      underscored: true,
    }
  )
  return ClinicalUserPermissions
}
