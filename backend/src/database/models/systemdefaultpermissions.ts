'use strict'
import { Model } from 'sequelize'
module.exports = (sequelize: any, DataTypes: any) => {
  class SystemDefaultPermissions extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  SystemDefaultPermissions.init({
    name: DataTypes.STRING,
    role_id: DataTypes.INTEGER,
    permissions: DataTypes.ARRAY(DataTypes.INTEGER),
    status: DataTypes.INTEGER
  }, {
    sequelize,
    modelName: 'SystemDefaultPermissions',
    underscored: true,
  });
  return SystemDefaultPermissions;
};