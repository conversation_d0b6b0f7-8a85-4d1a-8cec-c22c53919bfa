'use strict'
import { Model } from 'sequelize'
module.exports = (sequelize: any, DataTypes: any) => {
  class ClinicalUserSubscriptionsTemp extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here

    }
  }
  ClinicalUserSubscriptionsTemp.init({
    user_id: DataTypes.INTEGER,
    plan_id: DataTypes.INTEGER,
    start_date: DataTypes.DATE,
    subscription_id: DataTypes.STRING,
    end_date: DataTypes.DATE,
    currency: DataTypes.STRING,
    price: DataTypes.DECIMAL,  //Price of the subscription plan
    billing_frequency: DataTypes.ENUM('Monthly', 'Yearly'),  // Monthly, Annually, etc.
    next_billing_date: DataTypes.DATE,  // Date of the next scheduled billing
    payment_method: DataTypes.STRING,  // Payment method used for the subscription (e.g., credit card, PayPal)
    cancellation_reason: DataTypes.STRING, //  Reason for cancellation, if applicable
    cancellation_date: DataTypes.DATE,  // Date of the cancellation
    status: DataTypes.INTEGER,   //1 Active, 2 Expired , 0 Pending,5 trialing, 4 past_due, 8 incomplete_expired, 9 Canceled or deleted, etc.
    coupon_code: DataTypes.STRING,// -- Coupon code applied to the subscription
    payment_gateway: DataTypes.STRING, //-- Payment gateway used for the transaction
    subscription_features: DataTypes.JSONB, //-- JSONB field for storing additional subscription features
    usage_tracking: DataTypes.JSONB, // -- JSONB field for storing usage tracking information
    customer_id: DataTypes.STRING,
    billing_address_info: DataTypes.JSONB,
    payment_inten_id: DataTypes.STRING,


  }, {
    sequelize,
    modelName: 'ClinicalUserSubscriptionsTemp',
    underscored: true,
  });
  return ClinicalUserSubscriptionsTemp;
};