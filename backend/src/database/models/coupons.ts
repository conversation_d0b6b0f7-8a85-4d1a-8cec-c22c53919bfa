'use strict';
import { Model } from 'sequelize';

module.exports = (sequelize: any, DataTypes: any) => {
  class Coupons extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models: any) {
      // define association here
    }
  }

  Coupons.init(
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      stripe_coupon_id: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        comment: 'ID of the coupon in Stripe',
      },
      name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      percent_off: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      amount_off: {
        type: DataTypes.DECIMAL,
        allowNull: true,
        comment: 'Flat amount discount in the smallest currency unit (e.g., cents)',
      },
      duration: {
        type: DataTypes.ENUM('once', 'repeating', 'forever'),
        allowNull: false,
      },
      duration_in_months: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      redeem_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'Latest time the coupon can be redeemed (Unix timestamp in seconds)',
      },
      max_redemptions: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      metadata: {
        type: DataTypes.JSONB,
        allowNull: true,
        comment: 'Custom metadata (e.g., { start_date: "1681084800" } where start_date is Unix timestamp)',
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: 'Local status to mark coupon as active (1) or inactive (other values)',
      },
    },
    {
      sequelize,
      modelName: 'Coupons',
      tableName: 'coupons',
      underscored: true,
      timestamps: true, // Sequelize will manage created_at and updated_at
    }
  );

  return Coupons;
};
