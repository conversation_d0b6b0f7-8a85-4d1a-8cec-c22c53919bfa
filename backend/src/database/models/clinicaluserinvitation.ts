'use strict'
import { Model } from 'sequelize'
module.exports = (sequelize: any, DataTypes: any) => {
  class ClinicalUserInvitation extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  ClinicalUserInvitation.init(
    {
      client_id: DataTypes.UUID,
      user_id: DataTypes.INTEGER,
      owner_user_id: DataTypes.INTEGER,
      owner_email: DataTypes.STRING,
      first_name: DataTypes.STRING,
      last_name: DataTypes.STRING,
      registration_number: DataTypes.STRING,
      role_id: DataTypes.INTEGER,
      email: DataTypes.STRING,
      permissions: DataTypes.ARRAY(DataTypes.INTEGER),
      subscription_plan_id: DataTypes.INTEGER,
      status: DataTypes.INTEGER, // 0 Deactive , 1 Active , 2 expired , 3 Accepted , 4 Rejected
    },
    {
      sequelize,
      modelName: 'ClinicalUserInvitation',
      underscored: true,
    }
  )
  return ClinicalUserInvitation
}
