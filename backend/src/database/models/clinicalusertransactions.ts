'use strict'
import { Model } from 'sequelize'
module.exports = (sequelize: any, DataTypes: any) => {
  class ClinicalUserTransactions extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  ClinicalUserTransactions.init({
    user_id: DataTypes.INTEGER,
    transaction_date: DataTypes.DATE,
    transaction_type: DataTypes.INTEGER, // 1 = spend, 2 = credit_purchase , 3 = subscription_added
    documents_created: DataTypes.INTEGER, // purchased document
    team_members_used: DataTypes.INTEGER,
    currency: DataTypes.STRING,
    credit_amount: DataTypes.DECIMAL(10, 2),
    action: DataTypes.INTEGER, // 1 for add , 2 for minus or spend
    dictation_minutes_used: DataTypes.INTEGER,
    status: DataTypes.INTEGER
  }, {
    sequelize,
    modelName: 'ClinicalUserTransactions',
    underscored: true,
  });
  return ClinicalUserTransactions;
};