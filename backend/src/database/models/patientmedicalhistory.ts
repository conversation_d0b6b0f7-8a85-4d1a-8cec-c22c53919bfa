'use strict'
import {CreationOptional, InstanceDestroyOptions, Model} from 'sequelize'

interface HookOptions extends InstanceDestroyOptions {
  user: { id: number }; // Add a type for options.user
}

module.exports = (sequelize: any, DataTypes: any) => {
  class PatientMedicalHistory extends Model {
    declare id: CreationOptional<number>;
    declare patient_id: number;
    declare letter_id: number;
    declare user_id: number;
    declare content: any; // Adjust type if the content structure is known
    declare created_at: CreationOptional<Date>;
    declare updated_at: CreationOptional<Date>;
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define associations
      PatientMedicalHistory.belongsTo(models.Patients, {
        foreignKey: 'patient_id',
        targetKey: 'id',
        as: 'patient',
      });
      PatientMedicalHistory.belongsTo(models.PatientClinicalNotesLetters, {
        foreignKey: 'letter_id',
        targetKey: 'id',
        as: 'letter',
      });
      PatientMedicalHistory.belongsTo(models.ClinicalUsers, {
        foreignKey: 'user_id',
        targetKey: 'id',
        as: 'users',
      });
    }
  }

  PatientMedicalHistory.init(
    {
      id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
      },
      patient_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'patients',
          key: 'id',
        },
      },
      letter_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'patient_clinical_notes_letters',
          key: 'id',
        },
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'clinical_users',
          key: 'id',
        },
      },
      content: {
        type: DataTypes.JSONB,
        allowNull: false,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      sequelize,
      modelName: 'PatientMedicalHistory',
      tableName: 'patient_medical_history',
      underscored: true,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      hooks: {
        afterCreate: async (instance: PatientMedicalHistory, options: HookOptions) => {
          console.log('inside after create hook');
          console.log('inside after create hook', instance);
          await sequelize.models.MedicalHistoryChanges.create({
            medical_history_id: instance.id,
            patient_id: instance.patient_id,
            clinical_user_id: options.user.id, // Pass user ID via options
            operation: 'CREATE',
            changes: instance.content, // Full content for creation
          });
        },
        afterUpdate: async (instance, options: HookOptions) => {
          console.log('inside after update hook');
          console.log('inside after update hook', instance);
          // @ts-ignore
          const originalData = instance._previousDataValues;
          const updatedData = instance.dataValues;

          const changes = {};

          // Recursive comparison for nested objects
          const compareObjects = (original, updated, prefix = '') => {
            const keys = new Set([...Object.keys(original || {}), ...Object.keys(updated || {})]);

            for (const key of keys) {
              const fullPath = prefix ? `${prefix}.${key}` : key;

              if (
                typeof original[key] === 'object' &&
                typeof updated[key] === 'object' &&
                original[key] !== null &&
                updated[key] !== null &&
                !Array.isArray(original[key]) &&
                !Array.isArray(updated[key])
              ) {
                // Recursively compare nested objects
                compareObjects(original[key], updated[key], fullPath);
              } else if (original[key] !== updated[key]) {
                // Record changes for leaf nodes or differing properties
                changes[fullPath] = {
                  old: original[key],
                  new: updated[key],
                };
              }
            }
          };

          // Compare the content object specifically
          compareObjects(originalData.content, updatedData.content);

          await sequelize.models.MedicalHistoryChanges.create({
            medical_history_id: instance.id,
            patient_id: instance.patient_id,
            clinical_user_id: options.user.id,
            operation: 'UPDATE',
            changes,
          });
        },
        afterDestroy: async (instance, options: HookOptions) => {
          await sequelize.models.MedicalHistoryChanges.create({
            medical_history_id: instance.id,
            patient_id: instance.patient_id,
            clinical_user_id: options.user.id,
            operation: 'DELETE',
            changes: null, // No changes for deletion
          });
        },
      }
    }
  );

  return PatientMedicalHistory;
}
