import express, { Application } from 'express'
import path from 'path'
const cookieParser = require('cookie-parser')
const logger = require('morgan')
const helmet = require('helmet')
const cors = require('cors')
const compression = require('compression')
import multer from "multer";
import stripeClient from '../services/Stripe'
import { sendMail } from '../services/Mail'
import Models from "../database/models";
import cronController from "../apis/app/v1/subscription/CronController";
import * as EmailHelper from '../helpers/email.helper'

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;
// Create Express App
const app = express()

// use the Helmet to protect app from some well-known web vulnerabilities by setting HTTP headers appropriately.
app.use(helmet({ crossOriginEmbedderPolicy: false, originAgentCluster: true }))
app.use(helmet.contentSecurityPolicy({
  useDefaults: true,
  directives: {
    "img-src": ["'self'", "https: data:", '*.clinical-pad-staging.s3.eu-west-2.amazonaws.com', '*.clinical-pad-dev.s3.us-west-2.amazonaws.com']
  }
}));
app.disable('x-powered-by')

// application HTTP logging
app.use(logger('dev'))

// CORS settings
const corsOptions: any = {
  origin: '*',
  credentials: true,
  allowedHeaders: [
    'Content-Type',
    'Accept',
    'User-Agent',
    'Cache-Control',
    'Authorization',
    'Postman-Token',
  ],
}
app.use(cors(corsOptions))

// Compress all HTTP responses
app.use(compression())
console.log({endpointSecret})
// defining stripe webhook here before express.json() middleware
app.post('/webhook', express.raw({ type: 'application/json' }), async(request, response) => {
  const sig = request.headers['stripe-signature'];



  let event;

  try {
    event = stripeClient.webhooks.constructEvent(request.body, sig, endpointSecret);
  } catch (err) {
    response.status(400).send(`Webhook Error: ${err.message}`);
    return;
  }

  // Handle the event
  switch (event.type) {
    case 'customer.subscription.created':
      //let customerSubscriptionCreated = event.data.object;
      //console.log('customerSubscriptionCreated', customerSubscriptionCreated);
      // Then define and call a function to handle the event customer.subscription.created
      break;
    case 'customer.subscription.deleted': {
      // const customerSubscriptionDeleted = event.data.object;
      // Then define and call a function to handle the event customer.subscription.deleted
      const stripeSubscription = event.data.object;

      // Locate the local subscription record by matching the Stripe subscription ID.
      const localSub = await Models.ClinicalUserSubscriptions.findOne({
        where: {subscription_id: stripeSubscription.id}
      });

      if (localSub) {
        // Update the local record to indicate cancellation and switch to the starter plan (id = 1).
        await localSub.update({
          plan_id: 1,
          // Set a new status to indicate the subscription has been cancelled or moved.
          status: 2,
        });

        // Optionally update the user's main record.
        const user = await Models.ClinicalUsers.findByPk(localSub.user_id);
        if (user) {
          await user.update({
            subscription_plan_id: 1,
            // Update user limits according to the starter plan settings if necessary.
            // For example: remaining_custom_templates, remaining_audio_consultation_minutes, etc.
          });
        }
      }
      break;
    }
    case 'customer.subscription.paused':
      // const customerSubscriptionPaused = event.data.object;
      // Then define and call a function to handle the event customer.subscription.paused
      break;
    case 'customer.subscription.resumed':
      //const customerSubscriptionResumed = event.data.object;
      // Then define and call a function to handle the event customer.subscription.resumed
      break;
    case 'customer.subscription.updated':
      //const customerSubscriptionUpdated = event.data.object;
      // Then define and call a function to handle the event customer.subscription.updated
      break;
    case 'invoice.finalized':
      // const invoiceFinalized = event.data.object;
      // Then define and call a function to handle the event invoice.finalized
      break;
    case 'invoice.paid': {
      const invoice = event.data.object;

      // Only process if invoice is already paid
      if (invoice.status === 'paid') {
        try {
          // Get the line items from the invoice
          const lineItem = invoice.lines.data[0]; // Getting first line item

          // Get product details from Stripe
          const product = await stripeClient.products.retrieve(lineItem.price.product);

          // Get formatted amount
          const amount = invoice.amount_paid / 100;
          // const formattedAmount = `${invoice.currency.toUpperCase()} ${amount.toFixed(2)}`;
          const formattedAmount = `USD ${amount.toFixed(2)}`;

          // Get billing interval
          const interval = lineItem.price.recurring ? lineItem.price.recurring.interval : 'one-time';

          // Get payment method details
          let cardDetails = 'Card';
          if (invoice.payment_intent) {
            const paymentIntent = await stripeClient.paymentIntents.retrieve(invoice.payment_intent);
            if (paymentIntent.payment_method) {
              const paymentMethod = await stripeClient.paymentMethods.retrieve(paymentIntent.payment_method);
              if (paymentMethod.card) {
                cardDetails = `**** ${paymentMethod.card.last4}`;
              }
            }
          }

          // Get customer details
          const customer = await stripeClient.customers.retrieve(invoice.customer);

          // Format dates
          const startDate = new Date(invoice.period_start * 1000);
          const endDate = new Date(invoice.period_end * 1000);

          // Send email
          await EmailHelper.userUpdateSubscription({
            toEmail: customer.email,
            data: {
              name: customer.name,
              price: formattedAmount,
              planName: product.name,
              link: invoice.hosted_invoice_url,
              frontEndUrl: process.env.FRONTEND_URL,
              invoice_number: invoice.number,
              next_billing_date: endDate.toLocaleDateString(),
              amount: formattedAmount,
              last_four_digits_card: cardDetails,
              billing_interval: interval,
              period_start: startDate.toLocaleDateString(),
              period_end: endDate.toLocaleDateString()
            }
          });

          console.log(`Subscription invoice email sent to ${customer.email}`);

        } catch (error) {
          console.error('Error processing invoice.created webhook:', error);
          // Don't throw error to prevent webhook failure response
        }
      }
      break;
    }
    case 'invoice.payment_failed': {
      const invoice = event.data.object;
      const subscription = invoice.subscription;

      const localSub = await Models.ClinicalUserSubscriptions.findOne({
        where: { subscription_id: subscription }
      });

      if (localSub) {
        // Check if the failure has already been logged
        const existingLog = await Models.SubscriptionPaymentLogs.findOne({
          where: {
            subscription_id: localSub.id,
            stripe_invoice_id: invoice.id,
            status: 'FAILED'
          }
        });

        // If not already logged, log it and send email
        if (!existingLog) {
          await Models.SubscriptionPaymentLogs.create({
            subscription_id: localSub.id,
            stripe_invoice_id: invoice.id,
            payment_intent: invoice.payment_intent,
            failure_date: new Date(invoice.created * 1000),
            status: 'FAILED',
            processed: false
          });

          const customer = await stripeClient.customers.retrieve(invoice.customer);
          await EmailHelper.userSubscriptionFailedFirstWarning({
            name: customer.customer?.name || 'User',
            reason: invoice.invoice?.failure_message || 'Your recent payment could not be processed.',

          });
          console.log(`Payment failure email sent to ${customer.email}`);
        }
      }

      break;
    }

    case 'payment_intent.created':
      //const paymentIntentCreated = event.data.object;
      // Then define and call a function to handle the event payment_intent.created
      break;
    case 'payment_intent.payment_failed':
      // const paymentIntentPaymentFailed = event.data.object;
      // Then define and call a function to handle the event payment_intent.payment_failed
      break;
    case 'payment_intent.succeeded':
      //const paymentIntentSucceeded = event.data.object;
      // Then define and call a function to handle the event payment_intent.succeeded
      break;
    // ... handle other event types
    default:
      console.log(`Unhandled event type ${event.type}`);

      await sendMail('<EMAIL>', `ENV: ${process.env.NODE_ENV} | Stripe Webhook Email`, event.type, '', '')
  }

  // Return a 200 response to acknowledge receipt of the event
  return response.json({ received: true });
});

// for parsing application/json
app.use(express.json({ limit: '50mb' })); // Increase limit as needed

// for parsing application/x-www-form-urlencoded
app.use(express.urlencoded({ extended: true }))

app.use(cookieParser())

// pubic path for static content
app.use(express.static(path.join(__dirname, '../../public')))

app.use(express.static(__dirname + '/../uploads'))
app.use(multer({ dest: __dirname + "/../uploads/" }).any())

export default app
