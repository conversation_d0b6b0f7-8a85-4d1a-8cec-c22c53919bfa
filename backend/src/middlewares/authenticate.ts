import _ from 'lodash'
import JWT from 'jsonwebtoken'
import express from 'express'
import Models from '../database/models'
import * as UtilsHelper from '../helpers/utils.helper'
import * as TokenHelper from '../helpers/token.helper'
import { userActivityLogger, securityLogger, dataAccessLogger, auditLogger } from '../helpers/logger.helpers';

const Authenticate = async (req: any, res: express.Response, next: express.NextFunction) => {
  const retData = UtilsHelper.responseObject()
  try {
    // get the JWT token from the request header
    const authHeader = req.headers['authorization']

    if (_.isEmpty(authHeader)) {
      throw new Error('INVALID_TOKEN')
    }

    const authorizationToken = authHeader && authHeader.split(' ')
    const tokenPrefix = authorizationToken[0]
    const token = authorizationToken[1]

    // if no token
    if (tokenPrefix !== 'Bearer' || token == null) {
      throw new Error('INVALID_TOKEN')
    }

    const decoded: any = await TokenHelper.DecodeJWTToken(token)


    if (decoded && decoded.data && decoded.data.id) {
      let _user = await Models.ClinicalUsers.findOne({
        where: { id: decoded.data.id, status: 1 },
        attributes: ['id', 'role_id', 'user_type', 'first_name', 'last_name', 'email', 'phone', 'owner_user_id', 'subscription_team_members', 'credit_documents','subscription_documents','status'],
      })


      if (_user) {
        _user = _user.toJSON()

        req.user = _user
        next()
      } else throw new Error('INVALID_TOKEN')
    } else throw new Error('INVALID_TOKEN')
  } catch (err) {
    UtilsHelper.logErrors(err, 'Error in User Authentication.')

    let _msgCode
    if (err.name && err.name == 'TokenExpiredError') {
      _msgCode = 50017
    } else _msgCode = 50018


    if (_msgCode === 50018){
      const clientIP = req.ip;
      securityLogger.info('Unauthorized Request from', { additionalInfo: { user_ip: clientIP, tags: 'Security Log', request: { Method: req.method, originalUrl: req.originalUrl } } });
    }

    _.assign(retData, {
      status: 'error',
      statusCode: 401,
      msgCode: _msgCode,
      msg: UtilsHelper.getMessage(_msgCode),
    })

    return UtilsHelper.cRes(res, retData)
  }
}

module.exports = Authenticate
