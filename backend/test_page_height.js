// testPageHeight.js
const puppeteer = require('puppeteer');

(async () => {
  // 1) Launch headless Chrome
  const browser = await puppeteer.launch({ args: ['--no-sandbox'] });
  const page = await browser.newPage();

  // 2) Build HTML: 1..29, each in a div with 1cm line-height
  const lines = Array.from({ length: 29 }, (_, i) => `<div>${i + 1}</div>`).join('');
  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <style>
          html, body { margin:0; padding:0; }
          body {
            font-size: 12px;
            line-height: 1cm;      /* exactly 1 cm per line */
          }
          div { /* optional: center the number */
            text-align: left;
            padding-left: 0.5cm;
          }
        </style>
      </head>
      <body>
        ${lines}
      </body>
    </html>`;

  // 3) Load the HTML
  await page.setContent(html, { waitUntil: 'networkidle0' });

  // 4) Generate a PDF pegged to U.S. Letter exactly
  await page.pdf({
    path: 'page-height-test.pdf',
    width:  '8.5in',   // exactly 21.59 cm
    height: '11in',    // exactly 27.94 cm
    printBackground: false,
    margin: { top: 0, bottom: 0, left: 0, right: 0 },
    preferCSSPageSize: true
  });

  await browser.close();
  console.log('✅ page-height-test.pdf generated—open it and count the lines!');
})();
