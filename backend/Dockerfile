# Use the Node.js 18.14 slim runtime as a parent image
#FROM node:18.14-slim
FROM 190187601652.dkr.ecr.eu-west-2.amazonaws.com/node-base-image:latest

# Setting up the work directory
WORKDIR /backend

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
#RUN npm install

# Copying all the files in our project
COPY . .

# Exposing server port
EXPOSE 3000

# Starting our application
CMD [ "npm", "run", "dev" ]
