import React from "react";
import { Stepper, Step, Box } from "@mui/material";

const Steppers = ({ activeStep, handleStepChange }) => {

  return (
    <Stepper
      activeStep={activeStep}
      connector={<div style={{ display: "none" }} />}
      style={{
        padding: 0,
        marginBottom: "60px",
      }}
    >
      {[0, 1, 2].map((step) => (
        <Step key={step} onClick={() => handleStepChange(step)}>
          <Box
            bgcolor={activeStep === step ? "#FF9900" : "#F5DEB7"}
            color={activeStep === step ? "white" : "#FF9900"}
            onClick={() => handleStepChange(step)}
            width={30}
            height={3}
            display="flex"
            alignItems="center"
            justifyContent="center"
            border="1px solid"
            borderColor={activeStep === step ? "#FF9900" : "#F5DEB7"}
            borderRadius={8}
            style={{
              // cursor: "pointer",
              "& .MuiStep-horizontal": { paddingLeft: 0 },
            }}
          >
            {/* <StepLabel>{`Step ${step + 1}`}</StepLabel> */}
          </Box>
        </Step>
      ))}
    </Stepper>
  );
};

export default Steppers;
