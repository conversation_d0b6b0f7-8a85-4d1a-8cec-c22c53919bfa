import React from "react";
import {
  <PERSON>,
  CardContent,
  Grid,
  <PERSON>po<PERSON>,
  Avatar,
  Box,
  Button,
} from "@mui/material";
import CustomRating from "./CustomRating";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import { Link } from "react-router-dom";
const firstColumnData = [
  {
    title: "<PERSON>",
    position: "Strategic Advisor",
    company: " Oprah Winfrey Network",
    review:
      "<PERSON><PERSON>s porttitor, neque feugiat scelerisque consequat , metus dui vehicula tellus, ac tincidunt turpis ligula ut massa. Cras sed rhoncus turpis. Vestibulum sodales est non leo placerat bibendum at ultricies risus. Nulla iaculis lacinia finibus. Sed sit amet metus dignissim, feugiat metus a, pulvinar lectus. Description Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    rating: 4.5,
  },
  {
    title: "<PERSON>",
    position: "Strategic Advisor",
    company: " Oprah Winfrey Network",
    review:
      "Mauris porttitor, neque feugiat scelerisque consequat , metus dui vehicula tellus, ac tincidunt turpis ligula ut massa. Cras sed rhoncus turpis. Vestibulum sodales est non leo placerat bibendum at ultricies risus. Nulla iaculis lacinia finibus. Sed sit amet metus dignissim, feugiat metus a, pulvinar lectus. Description Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    rating: 4.5,
  },
];

const secondColumnData = [
  {
    title: "Kathryn Murphy",
    position: "Strategic Advisor",
    company: " Oprah Winfrey Network",
    review:
      "Mauris porttitor, neque feugiat scelerisque consequat , metus dui vehicula tellus, ac tincidunt turpis ligula ut massa. Cras sed rhoncus turpis. Vestibulum sodales est non leo placerat bibendum at ultricies risus. Nulla iaculis lacinia finibus. Sed sit amet metus dignissim, feugiat metus a, pulvinar lectus. Description Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    rating: 4.5,
  },
  {
    title: "Kathryn Murphy",
    position: "Strategic Advisor",
    company: " Oprah Winfrey Network",
    review:
      "Mauris porttitor, neque feugiat scelerisque consequat , metus dui vehicula tellus, ac tincidunt turpis ligula ut massa. Cras sed rhoncus turpis. Vestibulum sodales est non leo placerat bibendum at ultricies risus. Nulla iaculis lacinia finibus. Sed sit amet metus dignissim, feugiat metus a, pulvinar lectus. Description Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    rating: 4.5,
  },
];

const thirdColumnData = [
  {
    title: "Kathryn Murphy",
    position: "Strategic Advisor",
    company: " Oprah Winfrey Network",
    review:
      "Mauris porttitor, neque feugiat scelerisque consequat , metus dui vehicula tellus, ac tincidunt turpis ligula ut massa. Cras sed rhoncus turpis. Vestibulum sodales est non leo placerat bibendum at ultricies risus. Nulla iaculis lacinia finibus. Sed sit amet metus dignissim, feugiat metus a, pulvinar lectus. Description Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    rating: 4.5,
  },
  {
    title: "Kathryn Murphy",
    position: "Strategic Advisor",
    company: " Oprah Winfrey Network",
    review:
      "Mauris porttitor, neque feugiat scelerisque consequat , metus dui vehicula tellus, ac tincidunt turpis ligula ut massa. Cras sed rhoncus turpis. Vestibulum sodales est non leo placerat bibendum at ultricies risus. Nulla iaculis lacinia finibus. Sed sit amet metus dignissim, feugiat metus a, pulvinar lectus. Description Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    rating: 4.5,
  },
];

function CustomerReviews() {
  const renderCard = (item, index) => {
    return (
      <Grid key={item.title + index} item xs={12} sm={4}>
        <Card
          style={{
            boxShadow: "0px 15px 15px 0px #F3EDE2",
            border: "1px solid #E6E6E6",
            borderRadius: 16,
            padding: "32px",
          }}
        >
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Grid container spacing={2}>
                  <Grid item xs={3}>
                    <Avatar
                      style={{
                        maxWidth: "100%",
                        // height: "-webkit-fill-available",
                      }}
                      src="images/profileImage.png"
                    />
                  </Grid>
                  <Grid item xs={9}>
                    <Typography
                      variant="h5"
                      style={{
                        fontFamily: "GilroyMedium",
                        fontWeight: 700,
                        fontSize: 20,
                        lineHeight: "24.76px",
                      }}
                    >
                      {item.title}
                    </Typography>
                    <Typography
                      variant="subtitle1"
                      style={{
                        fontFamily: "GilroyMedium",
                        fontWeight: 500,
                        fontSize: 18,
                        lineHeight: "21.83px",
                      }}
                    >
                      {item.position}
                    </Typography>
                    <Typography
                      variant="subtitle1"
                      style={{
                        fontFamily: "GilroyMedium",
                        fontWeight: 500,
                        fontSize: 18,
                        color: "#7E7E86",
                        lineHeight: "21.83px",
                      }}
                    >
                      {item.company}
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>

              {/* Description */}
              <Grid item xs={12}>
                <Typography
                  variant="body1"
                  sx={{
                    overflow: "hidden",
                    display: "-webkit-box",
                    WebkitBoxOrient: "vertical",
                    WebkitLineClamp: 5,
                    fontFamily: "GilroyMedium",
                    fontWeight: 500,
                    fontSize: 16,
                  }}
                >
                  {item.review}
                </Typography>
              </Grid>

              {/* 5 Star Rating */}
              <Grid item xs={12}>
                <CustomRating value={item.rating} />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    );
  };

  const gradientCard = (degree) => {
    return (
      <Grid item xs={12} sm={4} sx={{ display: { xs: "none", md: "block" } }}>
        <Card
          style={{
            borderRadius: "16px",
            padding: "54px",
              background: `linear-gradient(${degree}deg, #dce5e5 0%, #F4EDE2 100%)`,
            boxShadow: "none",
          }}
        ></Card>
      </Grid>
    );
  };
  return (
    <Box
      sx={{
        paddingLeft: { xs: "24px", md: "143px" },
        paddingRight: { xs: "24px", md: "143px" },
        paddingTop: { xs: "60px" },
      }}
    >
      <Typography
        style={{
          fontFamily: "GilroyMedium",
          fontWeight: 700,
          fontSize: 45,
          lineHeight: "50px",
          marginBottom: "24px",
          textAlign: "center",
        }}
      >
        Join the ranks of our happy customers!
      </Typography>
      <Typography
        style={{
          fontFamily: "GilroyMedium",
          fontWeight: 500,
          fontSize: 22,
          lineHeight: "32px",
          marginBottom: "40px",
          textAlign: "center",
        }}
      >
        Embark on a global journey of quality improvement with Dovetail,
        alongside thousands of teams worldwide!
      </Typography>

      <Grid container spacing={4}>
        {/* First grid */}
        <Grid item xs={12} sm={4}>
          <Grid container direction="column" spacing={3}>
            {firstColumnData.map((item, index) => {
              return renderCard(item, index);
            })}
            {gradientCard(0)}
          </Grid>
        </Grid>

        <Grid item xs={12} sm={4}>
          <Grid container direction="column" spacing={3}>
            {gradientCard(180)}
            {secondColumnData.map((item, index) => {
              return renderCard(item, index);
            })}
          </Grid>
        </Grid>

        <Grid item xs={12} sm={4}>
          <Grid container direction="column" spacing={3}>
            {thirdColumnData.map((item, index) => {
              return renderCard(item, index);
            })}
            {gradientCard(0)}
          </Grid>
        </Grid>
      </Grid>

      <Box sx={{ display: "flex", justifyContent: "center" }}>
        <Button
          onClick={() => {}}
          sx={{
            color: "#1C1C1C",
            // display: "block",
            textTransform: "none",
            fontFamily: "GilroyMedium",
            fontWeight: 700,
            fontSize: 18,
            lineHeight: "22px",
            marginTop: "100px",
            alignItems: "center",
            display: "flex",
            justifyContent: "center",
          }}
          component={Link}
          to="/testimonials"
        >
          Explore customers
          <ArrowForwardIcon
            sx={{ fontSize: "18px", color: "#0F0F10", marginLeft: 1 }}
          />
        </Button>
      </Box>
    </Box>
  );
}

export default CustomerReviews;
