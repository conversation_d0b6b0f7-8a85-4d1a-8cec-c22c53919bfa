
@font-face {
  font-family: "GilroyRegular";
  src: local("GilroyRegular"),
    url("./config/fonts/Gilroy-Regular.ttf") format("truetype");
}
@font-face {
  font-family: "GilroyBold";
  src: local("GilroyBold"),
    url("./config/fonts/Gilroy-Bold.ttf") format("truetype");
}
@font-face {
  font-family: "GilroyMedium";
  src: local("GilroyMedium"),
    url("./config/fonts/Gilroy-Medium.ttf") format("truetype");
}

@font-face {
  font-family: "GilroyBlack";
  src: local("GilroyBlack"),
    url("./config/fonts/Gilroy-Black.ttf") format("truetype");
}

@font-face {
  font-family: "PlusJakartaSans";
  src: local("PlusJakartaSans"),
    url("./config/fonts/PlusJakartaSans-Medium.ttf") format("truetype");
}

body {
  margin: 0;
  font-family: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>roy<PERSON><PERSON>, PlusJakartaSans;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

 /* Common container styles */
 .container {
  margin: 0 auto;
  padding: 0 10px;
  box-sizing: border-box;
}

/* Large screens (desktops) */
@media only screen and (min-width: 1200px) {
  .container {
    max-width: 1140px; /* Adjust as needed */
  }
  .subscribeModal {
    clear:left; 
    font:14px Helvetica,Arial,sans-serif; 
    width: 600px;
  }
}

/* Medium screens (tablets) */
@media only screen and (max-width: 1199px) {
  .container {
    max-width: 960px; /* Adjust as needed */
  }
  .subscribeModal {
    clear:left; 
    font:14px Helvetica,Arial,sans-serif; 
    width: 400px;
  }
}

/* Small screens (phones) */
@media only screen and (max-width: 769px) {
  .container {
    max-width: 720px; /* Adjust as needed */
  }

  .subscribeModal {
    clear:left; 
    font:14px Helvetica,Arial,sans-serif; 
    width: 270px;
  }

  .centerAlign {
    display: flex;
    justify-content: center;
  }
}

/* Extra small screens (phones) */

