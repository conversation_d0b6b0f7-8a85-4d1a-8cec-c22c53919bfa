# Stage 1: Base Nginx image
#FROM nginx:latest AS base
FROM 190187601652.dkr.ecr.eu-west-2.amazonaws.com/nginx-latest:latest AS base

WORKDIR /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Stage 2: Build the React app
#FROM node:18.14-slim AS build
FROM 190187601652.dkr.ecr.eu-west-2.amazonaws.com/node-base-image:latest AS build
# Set the working directory inside the container
WORKDIR /usr/src/app

# Copy content
COPY . ./
RUN npm install

RUN npm run build

FROM base AS final
ARG APP_NAME
WORKDIR /usr/share/nginx/html

# Copy the built assets from the build stage to the final image
COPY --from=build /usr/src/app/build/ .
